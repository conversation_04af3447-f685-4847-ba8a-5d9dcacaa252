using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService
{
    /// <summary>
    /// 文件上传下载服务
    /// </summary>
    [InjectService]
    public class FileInfoService : IFileInfoService
    {
        public List<Dictionary<string, string>> PostFile(List<string> filePaths)
        {
            var results = new List<Dictionary<string, string>>();
            if (filePaths == null || filePaths.Count <= 0)
            {
                return results;
            }
            filePaths = filePaths.Distinct().ToList();
            foreach (var filePath in filePaths)
            {
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    continue;
                }
                var result = PostFile(filePath);
                result["filePathInfo"] = filePath;
                results.Add(result);
            }
            return results;
        }

        public Dictionary<string, string> PostFile(string filePath, string fileName = null)
        {
            if (string.IsNullOrWhiteSpace(filePath))
            {
                return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg", $"文件路径不能为空!"}
                };
            }

            if (filePath.Contains("/"))
            {
                filePath = filePath.Replace('/', '\\');
            }

            if (filePath.StartsWith("\\"))
            {
                filePath = filePath.Substring(1);
            }

            var startupPath = PathUtils.GetStartupPath();
            filePath = Path.Combine(startupPath, filePath);

            if (File.Exists(filePath) == false)
            {
                return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg", $"不存在文件路径:{filePath}"}
                };
            }
            var fileServerInfo = this.GetFirstWorkFileServer();
            if (fileServerInfo == null)
            {
                return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg", "尚未配置文件服务器信息"}
                };
            }
            string baseUrl = fileServerInfo.Url;
            if (string.IsNullOrWhiteSpace(baseUrl))
            {
                return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg", "尚未配置文件服务器的地址信息"}
                };
            }

            if (baseUrl.EndsWith("/"))
            {
                baseUrl = baseUrl.Substring(0, baseUrl.Length - 1);
            }

            string url = $"{baseUrl}/FileInfo/AjaxUpload";

            Dictionary<string, string> headers = new Dictionary<string, string>();
            headers.Add("sysCode", fileServerInfo.SysCode);
            headers.Add("authCode", fileServerInfo.AuthCode);
            headers.Add("isThumbnail", "true");
            headers.Add("isPrintText", "false");

            string imageName = fileName.IsNullOrEmptyOrWhiteSpace() ? Path.GetFileName(filePath) : fileName;
            Dictionary<string, string> keyValues = new Dictionary<string, string>();
            keyValues.Add("id", "WU_FILE_0");
            keyValues.Add("name", imageName);
            keyValues.Add("type", System.Web.MimeMapping.GetMimeMapping(imageName));


            Dictionary<string, string> fileList = new Dictionary<string, string>();
            fileList.Add("file", filePath);

            try
            {
                var result = SendHttpRequestPost(url, keyValues, fileList, Encoding.UTF8, headers);
                if (string.IsNullOrWhiteSpace(result))
                {
                    return new Dictionary<string, string>
                    {
                        { "isSuccess", "false"},
                        { "errMsg", "文件服务无应答消息"}
                    };
                }
                Dictionary<string, string> json = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, string>>(result);
                var kvp = json?.FirstOrDefault(x => string.Equals("fileId", x.Key, StringComparison.OrdinalIgnoreCase));
                if (kvp == null || string.IsNullOrWhiteSpace(kvp.Value.Value))
                {
                    return new Dictionary<string, string>
                    {
                        { "isSuccess", "false"},
                        { "errMsg", "文件服务没有正确返回文件id"}
                    };
                }
                json["isSuccess"] = "true";
                return json;
            }
            catch (WebException ex)
            {
                var resp = (HttpWebResponse)ex.Response;
                Stream stream = resp.GetResponseStream();

                JObject result = null;

                using (StreamReader reader = new StreamReader(stream))
                {
                    result = JObject.Parse(reader.ReadToEnd());
                }

                return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg",  result.GetJsonValue<string>("message")}
                };
            }
            catch (Exception ex)
            {
                return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg", ex.Message }
                };
            }
        }

        public string SendHttpRequestPost(string url, Dictionary<string, string> keyValues, Dictionary<string, string> fileList, Encoding encoding, Dictionary<string, string> headers)
        {
            if (string.IsNullOrEmpty(url))
                throw new ArgumentNullException("url");

            if (encoding == null)
                encoding = Encoding.UTF8;


            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = "POST";        // 要上传文件，一定要是POST方法
            request.Accept = "*/*";

            // 数据块的分隔标记，用于设置请求头，注意：这个地方最好不要使用汉字。
            string boundary = "---------------------------" + Guid.NewGuid().ToString("N");
            // 数据块的分隔标记，用于写入请求体。
            //   注意：前面多了一段： "--" ，而且它们将独占一行。
            byte[] boundaryBytes = Encoding.ASCII.GetBytes("\r\n--" + boundary + "\r\n");

            // 设置请求头。指示是一个上传表单，以及各数据块的分隔标记。
            request.ContentType = "multipart/form-data; boundary=" + boundary;

            if (headers != null && headers.Count > 0)
            {
                foreach (var header in headers)
                {
                    request.Headers.Add(header.Key, header.Value);
                }
            }

            // 先得到请求流，准备写入数据。
            Stream stream = request.GetRequestStream();




            if (keyValues != null && keyValues.Count > 0)
            {
                // 写入非文件的keyvalues部分
                foreach (KeyValuePair<string, string> kvp in keyValues)
                {
                    // 写入数据块的分隔标记
                    stream.Write(boundaryBytes, 0, boundaryBytes.Length);

                    // 写入数据项描述，这里的Value部分可以不用URL编码
                    string str = string.Format(
                            "Content-Disposition: form-data; name=\"{0}\"\r\n\r\n{1}",
                            kvp.Key, kvp.Value);

                    byte[] data = encoding.GetBytes(str);
                    stream.Write(data, 0, data.Length);
                }
            }


            // 写入要上传的文件
            foreach (KeyValuePair<string, string> kvp in fileList)
            {
                // 写入数据块的分隔标记
                stream.Write(boundaryBytes, 0, boundaryBytes.Length);

                // 写入文件描述，这里设置一个通用的类型描述：application/octet-stream，具体的描述在注册表里有。
                string description = string.Format(
                        "Content-Disposition: form-data; name=\"{0}\"; filename=\"{1}\"\r\n" +
                        "Content-Type: application/octet-stream\r\n\r\n",
                        kvp.Key, Path.GetFileName(kvp.Value));

                // 注意：这里如果不使用UTF-8，对于汉字会有乱码。
                byte[] header = Encoding.UTF8.GetBytes(description);
                stream.Write(header, 0, header.Length);

                // 写入文件内容
                byte[] body = File.ReadAllBytes(kvp.Value);
                stream.Write(body, 0, body.Length);
            }


            // 写入结束标记
            boundaryBytes = Encoding.ASCII.GetBytes("\r\n--" + boundary + "--\r\n");
            stream.Write(boundaryBytes, 0, boundaryBytes.Length);

            stream.Close();

            // 开始发起请求，并获取服务器返回的结果。
            using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
            {
                using (StreamReader reader = new StreamReader(response.GetResponseStream(), encoding))
                {
                    return reader.ReadToEnd();
                }
            }
        }
    }
}
