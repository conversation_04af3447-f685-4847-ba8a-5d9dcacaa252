using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;

namespace JieNor.Framework.Interface
{

    /// <summary>
    /// 数据读取
    /// </summary>
    public interface IDBService
    {
        /// <summary>
        /// 申请物理临时表表名
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        string ApplyPhysicalTempTableName(UserContext userCtx);

        /// <summary>
        /// 申请临时表名
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="tempTableName"></param>
        /// <returns></returns>
        string ApplyPhysicalTempTableName(UserContext userCtx, string tempTableName);

        /// <summary>
        /// 返回一个数据读取器对象
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="strSQL"></param>
        /// <returns></returns>
        IDataReader ExecuteReader(UserContext ctx, String strSQL);
        /// <summary>
        /// 返回一个数据读取器对象
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="strSQL"></param>
        /// <param name="cmdtype"></param>
        /// <returns></returns>
        IDataReader ExecuteReader(UserContext ctx, String strSQL, CommandType cmdtype);
        /// <summary>
        /// 返回一个数据读取器对象
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="strSQL"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        IDataReader ExecuteReader(UserContext ctx, String strSQL, SqlParam param);
        /// <summary>
        /// 返回一个数据读取器对象
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="strSQL"></param>
        /// <param name="param"></param>
        /// <param name="cmdtype"></param>
        /// <returns></returns>
        IDataReader ExecuteReader(UserContext ctx, String strSQL, SqlParam param, CommandType cmdtype);
        /// <summary>
        /// 返回一个数据读取器对象
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="strSQL"></param>
        /// <param name="paramList"></param>
        /// <returns></returns>
        IDataReader ExecuteReader(UserContext ctx, String strSQL, List<SqlParam> paramList);
        /// <summary>
        /// 返回一个数据读取器对象
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="strSQL"></param>
        /// <param name="paramList"></param>
        /// <param name="cmdtype"></param>
        /// <returns></returns>
        IDataReader ExecuteReader(UserContext ctx, String strSQL, IEnumerable<SqlParam> paramList, CommandType cmdtype);
        /// <summary>
        /// 返回一个数据读取器对象
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="strSQL"></param>
        /// <param name="paramList"></param>
        /// <param name="cmdtype"></param>
        /// <param name="cmdBehavior"></param>
        /// <returns></returns>
        IDataReader ExecuteReader(UserContext ctx, String strSQL, IEnumerable<SqlParam> paramList, CommandType cmdtype, CommandBehavior cmdBehavior);

        /// <summary>
        /// 返回一个数据表实体
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="strSQL"></param>
        /// <returns></returns>
        DataTable ExecuteDataTable(UserContext ctx, String strSQL);
        /// <summary>
        /// 返回一个数据集实体
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="strSQL"></param>
        /// <returns></returns>
        DataSet ExecuteDataSet(UserContext ctx, String strSQL);

        /// <summary>
        /// 执行sql实体
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        DynamicObjectCollection ExecuteDynamicObject(UserContext ctx, string strSql);

        /// <summary>
        /// 返回一批动态实体
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="strSql"></param>
        /// <param name="paramList"></param>
        /// <returns></returns>
        DynamicObjectCollection ExecuteDynamicObject(UserContext ctx, string strSql, IEnumerable<SqlParam> paramList);

        /// <summary>
        /// 返回指定结构的一批实体对象
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="dt"></param>
        /// <param name="strSql"></param>
        /// <param name="paramList"></param>
        /// <returns></returns>
        DynamicObjectCollection ExecuteDynamicObject(UserContext ctx, DynamicObjectType dt, string strSql, IEnumerable<SqlParam> paramList);

        /// <summary>
        /// 执行Union后的Sql，返回指定结构的一批实体对象
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="strSqls"></param>
        /// <param name="batchCount">分批执行数量，默认300次</param>
        /// <returns></returns>
        DynamicObjectCollection ExecuteDynamicObjectByUnion(UserContext ctx, List<string> strSqls, int batchCount = 100);

        /// <summary>
        /// 执行Union后的Sql，返回指定结构的一批实体对象
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="strSqls"></param>
        /// <param name="batchCount">分批执行数量，默认300次</param>
        /// <returns></returns>
        DynamicObjectCollection ExecuteDynamicObjectByUnion(UserContext ctx, List<string> strSqls, List<SqlParam> sqlParams, int batchCount = 100);

        /// <summary>
        /// 创建临时表名称
        /// </summary>
        /// <param name="ctx"></param> 
        /// <param name="isPhysics">是否物理临时表：true---物理临时表；false----临时表</param>
        /// <returns></returns>
        string CreateTemporaryTableName(UserContext ctx, bool isPhysics = true);

        /// <summary>
        /// 创建临时表名称
        /// </summary>
        /// <param name="ctx"></param> 
        /// <param name="startWith">以此为表名开头</param> 
        /// <param name="isPhysics">是否物理临时表：true---物理临时表；false----临时表</param>
        /// <returns></returns>
        string CreateTemporaryTableName(UserContext ctx, string startWith, bool isPhysics = true);

        /// <summary>
        /// 将数组值创建进一个临时表里,成功返回表名，表的列名默认为FID
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="fIds"></param>
        /// <param name="isPhysics">是否物理临时表：true---物理临时表；false----临时表</param>
        /// <returns></returns>
        string CreateTempTableWithDataList(UserContext ctx, IEnumerable<object> fIds, bool isPhysics = true);


        /// <summary>
        /// 使用一组数据创建临时表，成功返回表名，表的列名默认为FID,FENTRYID,FENTITYKEY
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="lstSelectedRows"></param>
        /// <param name="isPhysics">是否物理临时表：true---物理临时表；false----临时表</param>
        /// <returns></returns>
        string CreateTempTableWithSelectedRows(UserContext ctx, IEnumerable<SelectedRow> lstSelectedRows, bool isPhysics = true);

        /// <summary>
        /// 使用一组数据创建临时表，成功返回表名，表的列名默认为FID,FENTRYID,FENTITYKEY，同时支持创建辅助列
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="lstSelectedRows"></param>
        /// <param name="auxCols"></param>
        /// <param name="isPhysics">是否物理临时表：true---物理临时表；false----临时表</param>
        /// <returns></returns>
        string CreateTempTableWithSelectedRows(UserContext ctx, IEnumerable<SelectedRow> lstSelectedRows, IEnumerable<DataColumn> auxCols, bool isPhysics = true);


        /// <summary>
        /// 根据dt创建临时表并将数据初始化进临时表
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="dt">DataTable</param>
        /// <param name="size">所有字段的默认大小</param>
        /// <param name="idxFieldKeys">需要创建索引的字段标识</param>
        /// <param name="columnSize">具体字段的默认大小，如果提供了字段的具体大小，则将忽略 size 参数值。</param>
        /// <param name="isPhysics">是否物理临时表：true---物理临时表；false----临时表</param>
        /// <returns>临时表名称</returns>
        string CreateTempTableWithDataTable(UserContext ctx, DataTable dt, int size = 0, List<string> idxFieldKeys = null, 
                                            Dictionary<string, int> columnSize = null,bool  isPhysics =true);

         
        /// <summary>
        /// 删除临时表
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="tempTable">临时表名称</param>
        /// <param name="immediately">是否立即删除</param>
        void DeleteTempTableByName(UserContext ctx, string tempTable, bool immediately = false);

        /// <summary>
        /// 删除临时表
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="tempTable"></param>
        /// <param name="immediately">是否立即删除</param>
        void DeleteTempTableByName(UserContext ctx, IEnumerable<string> tempTable, bool immediately = false);


        /// <summary>
        /// 延时删除临时表
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="tempTable">临时表名称</param>
        /// <param name="delayMin">N分钟后删除临时表，默认十分钟</param>
        void DeleteTempTableByName(UserContext ctx, string tempTable, int delayMin = 10);


        /// <summary>
        /// 使用一组数据创建指定表名的表，表的列名默认为fid。
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="fIds"></param>
        /// <param name="tableName"></param>
        /// <param name="isPhysics">是否物理临时表：true---物理临时表；false----临时表</param>
        void CreateTableWithDataList(UserContext ctx, IEnumerable<object> fIds, string tableName,
            bool isPhysics = true);
    }

    /// <summary>
    /// 数据库访问扩展接口，非申请备案的功能点不准使用此接口
    /// </summary>
    public interface IDBServiceEx
    {

        /// <summary>
        /// 执行sql语句（注意不要通过该接口更新业务数据，更新业务数据请通过orm的相关接口进行更新）
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="strSQL"></param>
        /// <returns></returns>
        int Execute(UserContext ctx, String strSQL);

        /// <summary>
        /// 执行sql语句（注意不要通过该接口更新业务数据，更新业务数据请通过orm的相关接口进行更新）
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="strSQL"></param>
        /// <returns></returns>
        int Execute(UserContext ctx, String strSQL, int timeOut);

        /// <summary>
        /// 执行sql语句（注意不要通过该接口更新业务数据，更新业务数据请通过orm的相关接口进行更新）
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="strSQL"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        int Execute(UserContext ctx, String strSQL, SqlParam param);

        /// <summary>
        /// 执行sql语句（注意不要通过该接口更新业务数据，更新业务数据请通过orm的相关接口进行更新）
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="strSQL"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        int Execute(UserContext ctx, String strSQL, SqlParam param, int timeOut);

        /// <summary>
        /// 执行sql语句（注意不要通过该接口更新业务数据，更新业务数据请通过orm的相关接口进行更新）
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="strSQL"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        int Execute(UserContext ctx, String strSQL, IEnumerable<SqlParam> param);

        /// <summary>
        /// 执行sql语句（注意不要通过该接口更新业务数据，更新业务数据请通过orm的相关接口进行更新）
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="strSQL"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        int Execute(UserContext ctx, String strSQL, IEnumerable<SqlParam> param, int timeOut);

        /// <summary>
        /// 批量执行sql的接口
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="lstSqls"></param>
        /// <returns></returns>
        int ExecuteBatch(UserContext ctx, List<string> lstSqls);

        /// <summary>
        /// 批量执行sql的接口
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="lstSqls"></param>
        /// <returns></returns>
        int ExecuteBatch(UserContext ctx, List<string> lstSqls, int timeOut);

        /// <summary>
        /// 批量执行sql的接口
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="lstSqls"></param>
        /// <returns></returns>
        int ExecuteBatch(UserContext ctx, List<string> lstSqls, int batchSize, int timeOut);

        /// <summary>
        /// 批量执行参数化的sql接口
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="lstParameterizedSqls"></param>
        /// <returns></returns>
        int ExecuteBatch(UserContext ctx, IEnumerable<KeyValuePair<string, IEnumerable<SqlParam>>> lstParameterizedSqls);

        /// <summary>
        /// 批量执行参数化的sql接口
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="lstParameterizedSqls"></param>
        /// <returns></returns>
        int ExecuteBatch(UserContext ctx, IEnumerable<KeyValuePair<string, IEnumerable<SqlParam>>> lstParameterizedSqls, int timeOut);

    }
}
