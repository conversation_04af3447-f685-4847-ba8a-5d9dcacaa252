using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Reflection;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.Consts;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 动态dto扩展方法
    /// </summary>
    public static class DynamicDTOWrapperExtension
    {
        ///// <summary>
        ///// 将正常请求对象封装成微服务对象
        ///// </summary>
        ///// <typeparam name="TReq"></typeparam>
        ///// <param name="reqDto"></param>
        ///// <param name="localAliasServiceName"></param>
        ///// <returns></returns>
        //public static MicroServiceDTO<TReq> ToMicroServiceDTO<TReq>(this TReq reqDto, string localAliasServiceName)
        //    where TReq : DynamicDTOWrapper
        //{
        //    var msDto = new MicroServiceDTO<TReq>();
        //    msDto.Task = reqDto;
        //    msDto.Target.LocalAlias = localAliasServiceName;
        //    return msDto;
        //}

        ///// <summary>
        ///// 将正常请求对象封装成微服务对象
        ///// </summary>
        ///// <typeparam name="TReq"></typeparam>
        ///// <param name="reqDto"></param>
        ///// <param name="companyId"></param>
        ///// <param name="serviceId"></param>
        ///// <returns></returns>
        //public static MicroServiceDTO<TReq> ToMicroServiceDTO<TReq>(this TReq reqDto, string companyId, string serviceId)
        //    where TReq : DynamicDTOWrapper
        //{
        //    var msDto = new MicroServiceDTO<TReq>();
        //    msDto.Task = reqDto;
        //    msDto.Target.CompanyId = companyId;
        //    msDto.Target.ServiceId = serviceId;            
        //    return msDto;
        //}

        /// <summary>
        /// 将请求对的转换成服务上下文
        /// </summary>
        /// <typeparam name="TOperCtx"></typeparam>
        /// <typeparam name="TReqDto"></typeparam>
        /// <param name="reqDto"></param>
        /// <param name="container"></param>
        /// <returns></returns>
        public static TOperCtx PopulateTo<TOperCtx, TReqDto>(this TReqDto reqDto, IServiceContainer container)
            where TOperCtx : OperationContext
            where TReqDto : DynamicDTOWrapper
        {
            TOperCtx ctx = default(TOperCtx);            
            if (container != null)
            {
                ctx = container.GetService<TOperCtx>();
            }
            else
            {
                ctx = Activator.CreateInstance<TOperCtx>();
            }
            return PopulateTo(reqDto, ctx);
        }

        /// <summary>
        /// 将请求上下文信息传递给操作上下文
        /// </summary>
        /// <typeparam name="TOperCtx"></typeparam>
        /// <typeparam name="TReqDto"></typeparam>
        /// <param name="reqDto"></param>
        /// <param name="existContext"></param>
        /// <returns></returns>
        public static TOperCtx PopulateTo<TOperCtx, TReqDto>(this TReqDto reqDto, TOperCtx existContext)
        {
            var propItems = existContext.GetType().GetProperties(BindingFlags.Instance | BindingFlags.Public);
            foreach (var propInfo in propItems)
            {
                //仅处理原生类型及字符串类型,反射时处理所有类型
                var srcPropInfo = reqDto.GetType().GetPropertyInfo(propInfo.Name);
                if (srcPropInfo != null
                    && propInfo.PropertyType.IsAssignableFrom(srcPropInfo.PropertyType))
                {
                    propInfo.SetValue(existContext, srcPropInfo.GetValue(reqDto));
                }
            }
            return existContext;
        }




        ///// <summary>
        ///// 将后台任务转换成动态实体对象
        ///// </summary>
        ///// <param name="taskInfo"></param>
        ///// <param name="ctx"></param>
        //public static DynamicObject ToDynamicObject(this ScheduleTaskObject taskInfo, UserContext ctx, DynamicObject exist)
        //{
        //    var meta = HtmlParser.LoadFormMetaFromCache("bas_task", ctx);
        //    if (exist == null)
        //    {
        //        exist = meta.GetDynamicObjectType(ctx).CreateInstance() as DynamicObject;
        //        exist["fexecutecount"] = taskInfo.FailTime;
        //        exist["ffinallydate"] = taskInfo.LastEndRuntime;
        //    }
            


        //    return exist;
        //}

        ///// <summary>
        ///// 将后台任务转换成动态实体对象
        ///// </summary>
        ///// <param name="logInfo"></param>
        ///// <param name="ctx"></param>
        //public static DynamicObject ToDynamicObject(this TaskRuntimeLogInfo logInfo, UserContext ctx)
        //{
        //    var meta = HtmlParser.LoadFormMetaFromCache(TaskConst.HtmlForm_TaskLogInfo, ctx);
        //    DynamicObject log = meta.GetDynamicObjectType(ctx).CreateInstance() as DynamicObject;
        //    log["fmsg"] = logInfo.Content;
        //    log["fbegindate"] = logInfo.BeginTime;
        //    log["fenddate"] = logInfo.EndTime;
        //    log["fcreatedate"] = DateTime.Now;
        //    log["flogname"] = logInfo.Caption;
        //    log["ftaskid"] = logInfo.TaskId;
        //    DynamicObjectCollection entity = log["fentity"] as DynamicObjectCollection;
        //    foreach (var item in logInfo.Details)
        //    {
        //        DynamicObject dy = entity.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
        //        dy["fdescribe_e"] = item.Content;
        //        dy["ftasktime"] = item.BeginTime;
        //        entity.Add(dy);
        //    }
        //    return log;
        //}

    }
}
