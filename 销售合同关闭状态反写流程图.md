# 销售合同关闭状态反写流程图

```mermaid
flowchart TD
    subgraph 触发事件["触发事件"]
        A1["销售出库单审核"] --> B
        A2["销售出库单反审核"] --> C
        A3["销售退货单审核"] --> D
        A4["销售退货单反审核"] --> E
        A5["合同变更完成"] --> F
        A6["手动关闭/反关闭操作"] --> G
    end

    subgraph 数据计算["数据计算"]
        B["更新销售已出库数量"]
        C["回滚销售已出库数量"]
        D["更新销售退换数量"]
        E["回滚销售退换数量"]
        F["更新销售数量"]
        G["手动设置关闭状态"]
    end

    subgraph 行关闭状态计算["行关闭状态计算"]
        B --> H
        C --> H
        D --> H
        E --> H
        F --> H
        G --> I
        
        H["自动计算行关闭状态"]
        I["手动设置行关闭状态"]
        
        H --> H1
        H --> H2
        H --> H3
        
        H1["正常: 剩余数量>0 且 等于销售数量"]
        H2["部分关闭: 剩余数量>0 且 小于销售数量"]
        H3["自动关闭: 剩余数量=0"]
        
        I --> I1
        I --> I2
        
        I1["手动关闭"]
        I2["手动反关闭"]
    end

    subgraph 单据头关闭状态计算["单据头关闭状态计算"]
        H1 --> J
        H2 --> J
        H3 --> J
        I1 --> J
        I2 --> J
        
        J["计算单据头关闭状态"]
        
        J --> J1
        J --> J2
        J --> J3
        
        J1["正常: 所有行都是正常状态"]
        J2["部分关闭: 存在部分关闭/自动关闭/手动关闭行 且 存在正常行"]
        J3["整单关闭: 所有行都是自动关闭或手动关闭"]
    end

    subgraph 关联业务处理["关联业务处理"]
        J --> K
        
        K["关联业务处理"]
        
        K --> K1
        K --> K2
        K --> K3
        
        K1["预留释放: 行关闭时自动释放预留"]
        K2["二级分销商采购订单状态更新"]
        K3["二级分销商销售合同状态更新"]
    end

    subgraph 剩余数量计算公式["剩余数量计算公式"]
        L["剩余数量 = 销售数量 - 销售已出库数 + 销售已退换数量"]
    end
```

## 关闭状态值说明

### 单据体关闭状态 (fclosestate)
- "0"：正常（未关闭）
- "1"：已关闭（整单关闭）
- "2"：部分关闭

### 商品行关闭状态 (fclosestatus_e)
- "0"：正常（未关闭）
- "1"：手动关闭
- "2"：部分关闭
- "3"：自动关闭

## 关键业务逻辑

1. **销售出库单审核时**：
   - 更新二级分销商的《销售合同》商品明细的【销售已出库数】和【基本单位已出库数】
   - 如果全部发货完毕，则更新【行关闭状态】= "自动关闭"，【流程状态】="已出库"
   - 更新二级分销商的《采购订单》商品明细的【采购已入库数】和【基本单位已采购数量】

2. **销售出库单反审核时**：
   - 反写合同关联的二级经销商采购订单【一级合同状态】为"订单已审核"
   - 自动重新计算二级销售合同和采购订单关闭状态

3. **合同变更完成时**：
   - 重新计算单据头的【关闭状态】与明细行的所有【行关闭状态】

4. **二级分销商特殊处理**：
   - 如果勾选了"不管理库存"，则一级经销商销售出库单审核后自动计算二级分销采购订单和销售合同行关闭状态

5. **预留释放**：
   - 当销售合同的行关闭时，系统会自动释放预留
