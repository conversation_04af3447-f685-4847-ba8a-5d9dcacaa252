using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;

namespace JieNor.Framework.Interface
{

    /// <summary>
    /// 数据查询服务：excel的列表查询、web端列表数据导出等场景
    /// </summary>
    public interface IQueryDataService
    {


        QueryDataInfo QueryData(UserContext ctx, FilterSchemeObject  filter,bool isOffice);


    }


}
