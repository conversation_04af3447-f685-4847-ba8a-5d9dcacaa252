
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.Print;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.Office;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 打印服务接口
    /// </summary>
    public interface IPrintService
    {


        /// <summary>
        /// 构建打印数据包
        /// </summary>
        /// <param name="ctx">上下文信息</param>
        /// <param name="formMeta">模型信息</param>
        /// <param name="billData">要打印的数据</param>
        /// <param name="option">打印相关参数</param>
        /// <returns></returns>
        PrintContext BuildPrintContext(UserContext ctx, HtmlForm formMeta,
                                             IEnumerable<PrintData> billData, PrintOption option);

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="ctx"></param>  
        /// <returns></returns>
        string PrintWithTemplete(PrintContext ctx);


        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="ctx"></param>  
        /// <returns></returns>
        string PrintWithTempleteEx(PrintContext ctx);


        /// <summary>
        /// 获取打印模板信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="tmplId">为空则返回默认模板</param>
        /// <returns></returns>
        PrintOption GetPrintTmpl(UserContext ctx, string formId, string tmplId = "");


        /// <summary>
        /// 多个excel文件合并后转换成pdf文件
        /// </summary>
        /// <param name="xlsFile"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        string ConverterToPdf(IEnumerable<Tuple<string, Stream>> xlsFile, PrintOption option);

        /// <summary>
        /// 多个excel文件合并后转换成pdf文件
        /// </summary>
        /// <param name="xlsFile"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        string ConverterToPdf(IEnumerable<string> xlsFile, PrintOption option);

        void ClearCache(UserContext ctx, string tmplId);

    }


}
