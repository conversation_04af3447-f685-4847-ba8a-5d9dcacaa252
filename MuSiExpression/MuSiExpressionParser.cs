using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.Framework.AppService.MuSiExpression
{
    /// <summary>
    /// 慕思表达式计算实现
    /// </summary>
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public class MuSiExpressionParser : IMuSiExpressionParser
    {
        /// <summary>
        /// 根据指定数据上下文计算表达式的值
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="bizData"></param>
        /// <param name="expression"></param>
        /// <returns></returns>
        public object Evaluate(UserContext userCtx, HtmlForm htmlForm, DynamicObject bizData, string expression)
        {
            var cctx = new ConvertContext(userCtx, htmlForm, new Dictionary<string, IEnumerable<string>>());

            var textPara = ParseTextKey(cctx, htmlForm, expression);

            cctx.SetRowData(bizData);

            return Evaluate(cctx, textPara);
        }

        private TextKeyParameter ParseTextKey(ConvertContext msCtx, HtmlForm htmlForm, string text)
        {
            TextKeyParameter textPara = new TextKeyParameter();

            textPara.SrcText = text;
            textPara.KeyParas = text.GetKeyParameter(msCtx.Context, htmlForm, false);

            return textPara;
        }

        private object Evaluate(ConvertContext cctx, TextKeyParameter textPara)
        {
            object result = null;

            if (textPara == null)
            {
                return result;
            }

            string cellText = textPara.IsHtml ? textPara.HtmlText : textPara.SrcText;
            if (cellText.IsNullOrEmptyOrWhiteSpace())
            {
                return result;
            }

            foreach (var para in textPara.KeyParas)
            {
                cctx.SetKeyPara(para);

                if (para.Key.EqualsIgnoreCase("[当前用户]") || para.Key.EqualsIgnoreCase("[CurrentUser]"))
                {
                    cellText = cellText.Replace(para.SrcKey, cctx.Context.DisplayName);
                }
                else if (para.Key.EqualsIgnoreCase("[当前日期]"))
                {
                    cellText = cellText.Replace(para.SrcKey, DateTime.Now.ToString("yyyy-MM-dd"));
                }
                else if (para.Key.EqualsIgnoreCase("[当前时间]"))
                {
                    cellText = cellText.Replace(para.SrcKey, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                }
                else if (para.Key.EqualsIgnoreCase("[当前企业]"))
                {
                    var org = cctx.Context.Companys.FirstOrDefault(f => f.CompanyId.EqualsIgnoreCase(cctx.Context.Company));
                    cellText = cellText.Replace(para.SrcKey, org?.CompanyName);
                }
                else if (para.Key.EqualsIgnoreCase("[换行]"))
                {
                    cellText = cellText.Replace(para.SrcKey, Environment.NewLine);
                }
                else if (cctx.HtmlForm.Id.EqualsIgnoreCase(para.FormId))
                {
                    string value = cctx.SetKeyPara(para).GetValue();
                    cellText = cellText.Replace(para.SrcKey, value.ToString());
                }
            }

            result = cellText;

            return result;
        }
    }
}
