using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.TaskProgress;
using ServiceStack.Redis;

namespace JieNor.Framework.AppService
{
    /// <summary>
    /// 任务进度管理服务
    /// </summary>
    [InjectService]
    public class TaskProgressService : ITaskProgressService
    {

        [InjectProperty]
        protected IRedisCache CacheClient { get; set; }

        [InjectProperty]
        protected ILogServiceEx LogService { get; set; }

        /// <summary>
        /// 移除进度任务
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="taskId"></param>
        public void DeregisterTaskProgress(UserContext ctx, string taskId)
        {
            if (taskId.IsNullOrEmptyOrWhiteSpace()) return;
            CacheClient.Remove(taskId);
        }

        /// <summary>
        /// 获取任务进度
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="taskId"></param>
        /// <returns></returns>
        public virtual TaskProgressItem GetTaskProgress(UserContext ctx, string taskId)
        {
            TaskProgressItem item = GetFromCache(taskId);
            if (item?.ProgressValue >= 100)
            {
                this.DeregisterTaskProgress(ctx, taskId);
            }

            return item?.Clone();
        }

        /// <summary>
        /// 注册一个任务进度
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="taskId"></param>
        /// <returns></returns>
        public virtual TaskProgressItem RegisterTaskProgress(UserContext ctx, string taskId)
        {
            TaskProgressItem taskProgItem = new TaskProgressItem
            {
                ProgressValue = 0,
                Message = "正在启动任务，请稍候……"
            };

            var item = GetFromCache(taskId);
            if (item == null)
            {
                CacheClient.Set<TaskProgressItem>(taskId, taskProgItem, TimeSpan.FromHours(10));
            }
            else
            {
                taskProgItem = item;
            }
            return taskProgItem;
        }

        /// <summary>
        /// 设置任务进度消息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="taskId"></param>
        /// <param name="progressMessage"></param>
        public void SetTaskProgressMessage(UserContext ctx, string taskId, string progressMessage)
        {
            if (taskId.IsNullOrEmptyOrWhiteSpace()) return;

            TaskProgressItem item = GetFromCache(taskId);
            if (item != null && progressMessage != null)
            {
                item.Message = progressMessage;
                CacheClient.Set<TaskProgressItem>(taskId, item, TimeSpan.FromHours(10));
                if (item.ProgressValue >= 100)
                {
                    CacheClient.SetTimeToLive(taskId, TimeSpan.FromMinutes(30));
                }
            }
        }

        /// <summary>
        /// 设置任务处理进度
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="taskId"></param>
        /// <param name="progressValue"></param>
        public void SetTaskProgressValue(UserContext ctx, string taskId, decimal progressValue)
        {
            if (taskId.IsNullOrEmptyOrWhiteSpace()) return;

            TaskProgressItem item = GetFromCache(taskId);
            if (item != null)
            {
                item.ProgressValue = Math.Round(progressValue, 2);
                CacheClient.Set<TaskProgressItem>(taskId, item, TimeSpan.FromHours(10));

                if (item.ProgressValue >= 100)
                {
                    CacheClient.SetTimeToLive(taskId, TimeSpan.FromMinutes(3));
                }
            }
        }

        /// <summary>
        /// 设置任务进度的错误状态：（一般在任务执行结束后设置）
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="taskId"></param>
        /// <param name="haveError"></param>
        public void SetTaskExeResultErrStatus(UserContext ctx, string taskId, bool haveError)
        {
            if (taskId.IsNullOrEmptyOrWhiteSpace()) return;

            TaskProgressItem item = GetFromCache(taskId);
            if (item != null)
            {
                item.HaveError = haveError;
                CacheClient.Set<TaskProgressItem>(taskId, item, TimeSpan.FromHours(1));
            }
        }


        /// <summary>
        /// 设置任务处理进度及消息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="taskId"></param>
        /// <param name="progressValue"></param>
        /// <param name="progressMessage"></param>
        public void SetTaskProgressValueAndMessage(UserContext ctx, string taskId, decimal progressValue, string progressMessage)
        {
            if (taskId.IsNullOrEmptyOrWhiteSpace()) return;

            TaskProgressItem item = GetFromCache(taskId);
            if (item != null)
            {
                item.ProgressValue = Math.Round(progressValue, 2);
                if (progressMessage != null)
                {
                    item.Message = progressMessage;
                }
                CacheClient.Set<TaskProgressItem>(taskId, item, TimeSpan.FromHours(10));

                if (progressValue >= 100)
                {
                    CacheClient.SetTimeToLive(taskId, TimeSpan.FromMinutes(3));
                }
            }
        }

        /// <summary>
        /// 设置任务返回值数据
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="taskId"></param>
        /// <param name="result"></param>
        public void SetTaskReturnData(UserContext ctx, string taskId, IOperationResult result)
        {
            if (taskId.IsNullOrEmptyOrWhiteSpace()) return;

            TaskProgressItem item = GetFromCache(taskId);
            if (item != null)
            {
                item.Result = result;
                CacheClient.Set<TaskProgressItem>(taskId, item, TimeSpan.FromHours(5));
                if (item.ProgressValue >= 100)
                {
                    CacheClient.SetTimeToLive(taskId, TimeSpan.FromMinutes(3));
                }
            }
        }

        /// <summary>
        /// 从缓存获取任务进度对象
        /// </summary>
        /// <param name="taskId"></param>
        /// <returns></returns>
        private TaskProgressItem GetFromCache(string taskId)
        {
            TaskProgressItem item = null;

            try
            {
                item = CacheClient.Get<TaskProgressItem>(taskId);
            }
            catch (Exception e)
            {
                this.LogService?.Error("从缓存获取任务进度对象失败", e);
            }

            if (item == null)
            {
                item = new TaskProgressItem
                {
                    ProgressValue = 0,
                    Message = "正在启动任务，请稍候……"
                };
            }

            return item;
        }
    }



}