using ServiceStack.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 服务拦截器接口
    /// </summary>
    public interface IServiceInterceptor
    {
        /// <summary>
        /// 执行服务
        /// </summary>
        /// <param name="req"></param>
        /// <param name="res"></param>
        void Execute(IRequest req, IResponse res);

        /// <summary>
        /// 顺序
        /// </summary>
        int Order { get; }
    }
}
