using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ServiceStack.Redis;

namespace JieNor.Framework.Interface.Cache
{
    /// <summary>
    /// redis缓存接口定义
    /// </summary>
    public interface IRedisCache
    {
        /// <summary>
        /// 设置某个对象至缓存
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="obj"></param>
        /// <returns></returns>
        bool Set<T>(string key, T obj);

        /// <summary>
        /// 设置某个对象至缓存，并应用有效期
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="expiryIn"></param>
        /// <returns></returns>
        bool Set<T>(string key, T value, TimeSpan expiryIn);

        /// <summary>
        /// 从缓存获取一个对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <returns></returns>
        T Get<T>(string key);

        /// <summary>
        /// 从缓存获取一组对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="keys"></param>
        /// <returns></returns>
        IDictionary<string, T> GetAll<T>(IEnumerable<string> keys);

        /// <summary>
        /// 设置一组数据至缓存
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="allValues"></param>
        void SetAll<T>(IDictionary<string, T> allValues);

        /// <summary>
        /// 清空指定标识的缓存
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        bool Remove(string key);

        /// <summary>
        /// 移除多个键关联的缓存
        /// </summary>
        /// <param name="allKeys"></param>
        void RemoveAll(IEnumerable<string> allKeys);

        /// <summary>
        /// 移除所有缓存信息
        /// </summary>
        void RemoveAll();

        /// <summary>
        /// 移除符合指定样式的所有key
        /// </summary>
        /// <param name="keyPattern"></param>
        /// <param name="type"></param>
        void RemoveAllByKeyPattern(string keyPattern, Enu_SearchType type);

        /// <summary>
        /// 获取符合指定键规则的所有值
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="keyPattern"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        IDictionary<string, T> GetAllByKeyPattern<T>(string keyPattern, Enu_SearchType type);

        /// <summary>
        /// 计数接口
        /// </summary>
        /// <param name="key"></param>
        /// <param name="amount"></param>
        /// <returns></returns>
        long Increment(string key, uint amount);

        /// <summary>
        /// 获取某个缓存内容存活期还有多久
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        TimeSpan? GetTimeToLive(string key);

        /// <summary>
        /// 设置键过期时间
        /// </summary>
        /// <param name="key"></param>
        /// <param name="tsExpireIn"></param>
        void SetTimeToLive(string key, TimeSpan tsExpireIn);
        /// <summary>
        /// 设置键过期时间
        /// </summary>
        /// <param name="key"></param>
        /// <param name="dtExpireAt"></param>
        void SetTimeToLive(string key, DateTime dtExpireAt);

        /// <summary>
        /// 获取缓存状态描述信息
        /// </summary>
        /// <returns></returns>
        Dictionary<string, object> GetStatusDescription();

        /// <summary>
        /// 向某个队列中加入某个对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="listId"></param>
        /// <param name="value"></param>
        void EnqueueItemToList<T>(string listId, T value);

        /// <summary>
        /// 从某个队列中弹出某个对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="listId"></param>
        /// <returns></returns>
        T DequeueItemFromList<T>(string listId);

        /// <summary>
        /// 推送一个对象到某个堆栈
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="listId"></param>
        /// <param name="value"></param>
        void PushItemToList<T>(string listId, T value);

        /// <summary>
        /// 从某个堆栈弹出一个对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="listId"></param>
        /// <returns></returns>
        T PopItemFromList<T>(string listId);

        /// <summary>
        /// 获取某集合里的所有对象
        /// </summary>
        /// <param name="listId"></param>
        /// <returns></returns>
        IEnumerable<string> GetAllItemsFromSet(string listId);

        /// <summary>
        /// 对指定集合做运算（差集）
        /// </summary>
        /// <param name="fromListId"></param>
        /// <param name="withListIds"></param>
        /// <returns></returns>
        IEnumerable<string> GetDifferencesFromSet(string fromListId, params string[] withListIds);

        /// <summary>
        /// 从集合里移除指定项
        /// </summary>
        /// <param name="listId"></param>
        /// <param name="key"></param>
        void RemoveItemFromSet(string listId, string key);

        /// <summary>
        /// 向集合里添加指定项
        /// </summary>
        /// <param name="listId"></param>
        /// <param name="key"></param>
        void AddItemToSet(string listId, string key);

        /// <summary>
        /// 批量向集合中添加元素
        /// </summary>
        /// <param name="listId"></param>
        /// <param name="items"></param>
        void AddRangeToSet(string listId, List<string> items);

        /// <summary>
        /// Lua
        /// </summary>
        /// <returns></returns>
        string ExecLua(string body, params string[] args);


        long IncrementWithLock(string lockKey, int lockTimeout, string key, uint amount);

        /// <summary>
        /// 获取分布式锁
        /// </summary>
        /// <param name="lockKey"></param>
        /// <param name="lockTimeout"></param>
        /// <returns></returns>
        IDisposable GetLock(string lockKey, int lockTimeout);

    }


}
