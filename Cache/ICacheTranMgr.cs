using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.Cache
{
    /// <summary>
    /// 缓存事务管理
    /// </summary>
    public interface ICacheTranMgr
    {
        /// <summary>
        /// 启动缓存事务
        /// </summary>
        /// <returns></returns>
        void Begin();

        /// <summary>
        /// 提交缓存事务
        /// </summary>
        bool Commit();

        /// <summary>
        /// 回滚缓存事务
        /// </summary>
        void Rollback();
    }
}
