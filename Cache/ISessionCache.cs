using ServiceStack.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks; 
namespace JieNor.Framework.Interface.Cache
{
    /// <summary>
    /// 用于会话的缓存接口定义
    /// </summary>
    public interface ISessionCache
    {
        /// <summary>
        /// 设置认证缓存数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="expiresIn"></param>
        /// <returns></returns>
        bool SetTicket(IRequest req, string key, UserAuthTicket value, TimeSpan expiresIn);

        /// <summary>
        /// 获取缓存的认证信息数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <returns></returns>
        UserAuthTicket GetTicket(string key);


        /// <summary>
        /// 清空指定标识的缓存
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        bool RemoveTicket(string key);


        /// <summary>
        /// 设置缓存数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="expiresIn"></param>
        /// <returns></returns>
        bool Set<T>(string key, T value, TimeSpan expiresIn);
        /// <summary>
        /// 设置缓存数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="expiresAt"></param>
        /// <returns></returns>
        bool Set<T>(string key, T value, DateTime expiresAt);
        /// <summary>
        /// 设置缓存数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        bool Set<T>(string key, T value);
        /// <summary>
        /// 获取缓存数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <returns></returns>
        T Get<T>(string key);

        /// <summary>
        /// 清空指定标识的缓存
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        bool Remove(string key);

        /// <summary>
        /// 获取某个缓存内容存活期还有多久
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        TimeSpan? GetTimeToLive(string key);

        /// <summary>
        /// 设置键过期时间
        /// </summary>
        /// <param name="key"></param>
        /// <param name="tsExpireIn"></param>
        void SetTimeToLive(string key, TimeSpan tsExpireIn);
        /// <summary>
        /// 设置键过期时间
        /// </summary>
        /// <param name="key"></param>
        /// <param name="dtExpireAt"></param>
        void SetTimeToLive(string key, DateTime dtExpireAt);

        /// <summary>
        /// 获取一批缓存数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="setId"></param>
        /// <returns></returns>
        HashSet<T> GetAllItemsFromSet<T>(string setId);
        /// <summary>
        /// 添加数据至队列
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="setId"></param>
        /// <param name="item"></param>
        void AddItemToSet<T>(string setId, T item);
        /// <summary>
        /// 从队列移除数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="setId"></param>
        /// <param name="item"></param>
        void RemoveItemFromSet<T>(string setId, T item);


        /// <summary>
        /// 获取分布式锁
        /// </summary>
        /// <param name="lockKey"></param>
        /// <param name="lockTimeout"></param>
        /// <returns></returns>
        IDisposable GetLock(string lockKey, int lockTimeout);

    }
}
