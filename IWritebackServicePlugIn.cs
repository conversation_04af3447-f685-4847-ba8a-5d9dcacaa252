using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.FormOp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.DesignMeta;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 反写服务插件接口定义
    /// </summary>
    public interface IWritebackServicePlugIn
    {
        /// <summary>
        /// 当前业务对象模型
        /// </summary>
        HtmlForm HtmlForm { get; }

        /// <summary>
        /// 目标单类型（从谁那发起的反写）
        /// </summary>
        HtmlForm TargetHtmlForm { get; }

        /// <summary>
        /// 上下文
        /// </summary>
        UserContext Context { get; }

        /// <summary>
        /// 转换规则
        /// </summary>
        WritebackRuleDesc WritebackRule { get; }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="wbCtx"></param>
        /// <param name="rule"></param>
        void InitializeContext(UserContext userCtx, WritebackContext wbCtx, WritebackRuleDesc rule);

        /// <summary>
        /// 关联反写前事件
        /// </summary>
        /// <param name="e"></param>
        void BeforeWriteback(BeforeWritebackEventArgs e);

        /// <summary>
        /// 关联反写后事件
        /// </summary>
        /// <param name="e"></param>
        void AfterWriteback(AfterWritebackEventArgs e);

        /// <summary>
        /// 超额检查时点事件
        /// </summary>
        /// <param name="e"></param>
        void OnCheckExceedance(OnCheckExceedanceEventArgs e);
    }
}
