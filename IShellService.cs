using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 外壳服务
    /// </summary>
    public interface IShellService
    {
        /// <summary>
        /// 创建短网址参数标识
        /// </summary>
        /// <param name="container"></param>
        /// <param name="paraData"></param>
        /// <param name="expiryIn"></param>
        /// <returns></returns>
        string CreateShortUrlParam(IServiceContainer container, object paraData,TimeSpan expiryIn);

        /// <summary>
        /// 创建短网址参数标识
        /// </summary>
        /// <param name="container"></param>
        /// <param name="paraData"></param>
        /// <param name="expiryAt"></param>
        /// <returns></returns>
        string CreateShortUrlParam(IServiceContainer container, object paraData, DateTime expiryAt);

        /// <summary>
        /// 创建短url
        /// </summary>
        /// <param name="container"></param>
        /// <param name="longUrl">长url</param>
        /// <param name="shortDomain">目标短域名</param>
        /// <returns>目标短网址</returns>
        string CreateShortUrl(IServiceContainer container, string longUrl, string shortDomain);

        /// <summary>
        /// 根据短网址查询长网址
        /// </summary>
        /// <param name="container"></param>
        /// <param name="shortUrl"></param>
        /// <returns></returns>
        string TranslateShortUrl(IServiceContainer container, string shortUrl);

        /// <summary>
        /// 根据短网址参数标识获取完整参数对象
        /// </summary>
        /// <param name="container"></param>
        /// <param name="paraId"></param>
        /// <returns></returns>
        T GetShortUrlParam<T>(IServiceContainer container, string paraId);
    }
}
