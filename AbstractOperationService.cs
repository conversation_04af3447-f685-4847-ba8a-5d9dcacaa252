using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.Interface.Converter;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface.Perfmon;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.FormOp.OpData;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;
using JieNor.Framework.DataTransferObject.Report;
using ServiceStack;
using JieNor.Framework.Interface.Validation;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 标准操作服务抽象基类
    /// </summary>
    public abstract class AbstractOperationService : IOperationService
    {
        /// <summary>
        /// 标准操作上下文
        /// </summary>
        [InjectProperty]
        public OperationContext OperationContext
        {
            get;
            private set;
        }

        /// <summary>
        /// 辅助属性相关服务
        /// </summary>
        [InjectProperty]
        protected IAuxPropService AuxPropService { get; set; }

        /// <summary>
        /// 当前登录信息
        /// </summary>
        public virtual UserContext UserCtx
        {
            get
            {
                if (this.OperationContext == null)
                {
                    return null;
                }
                return this.OperationContext.UserContext;
            }
        }

        /// <summary>
        ///  
        /// </summary>
        public IServiceContainer Container
        {
            get
            {
                if (this.OperationContext == null)
                {
                    return null;
                }
                return this.OperationContext.Container;
            }
        }

        /// <summary>
        /// 当前登录的用户id
        /// </summary>
        public string UserId
        {
            get
            {
                if (this.OperationContext == null || this.OperationContext.UserContext == null)
                {
                    return "";
                }
                return this.OperationContext.UserContext.UserId;
            }
        }

        private UserContext _slaveUserContent = null;
        /// <summary>
        /// 从库上下文
        /// </summary>
        protected UserContext SlaveUserContent
        {
            get
            {
                if (_slaveUserContent == null)
                {
                    _slaveUserContent = this.UserCtx.CreateSlaveDBContext();
                }

                return _slaveUserContent;
            }
        }

        /// <summary>
        /// 性能实体视图
        /// </summary>
        protected PerfmonDataEntryView PerfDataView { get; private set; }

        /// <summary>
        /// 服务控制选项
        /// </summary>
        protected ServiceControlOption ServiceControlOption { get; private set; }

        /// <summary>
        /// 插件代理
        /// </summary>
        protected PlugInProxy<IOperationServicePlugIn> PlugInProxy { get; set; }

        /// <summary>
        /// 系统缓存服务
        /// </summary>
        protected IRedisCache Cache { get; private set; }

        /// <summary>
        /// 内存缓存
        /// </summary>
        protected IMemoryCache CacheMem { get; private set; }

        /// <summary>
        /// 服务网关
        /// </summary>
        protected IHttpServiceInvoker Gateway { get; private set; }

        /// <summary>
        /// 当前页面数据共享区
        /// </summary>
        private dynamic PageSession
        {
            get
            {
                return this.OperationContext?.GetPageSession();
            }
        }

        /// <summary>
        /// 父级页面共享区
        /// </summary>
        private dynamic ParentPageSession
        {
            get
            {
                return this.OperationContext?.GetParentPageSession();
            }
        }

        /// <summary>
        /// 返回当前页面或者指定页面的父页面Id 
        /// </summary>
        /// <param name="pageId"></param>
        /// <returns></returns>
        protected string GetParentPageId(string pageId = null)
        {
            return this.OperationContext?.GetParentPageId(pageId);
        }

        /// <summary>
        /// 返回当前页面或者指定页面的父页面表单标识 
        /// </summary>
        /// <param name="pageId"></param>
        /// <returns></returns>
        protected string GetParentPageFormId(string pageId = null)
        {
            return this.OperationContext?.GetParentPageFormId(pageId);
        }

        /// <summary>
        /// 设置页面缓存值
        /// </summary>
        /// <param name="sessionKey">缓存键</param>
        /// <param name="sessionValue">缓存值</param>
        /// <param name="targetFormId">目标表单Id，不传则默认为当前表单</param>
        /// <param name="pageId">页面Id，不传则默认为当前页面</param>
        protected void SetPageSessionValue(string sessionKey, object sessionValue, string targetFormId = null, string pageId = null)
        {
            this.OperationContext?.SetPageSessionValue(sessionKey, sessionValue, targetFormId, pageId);
        }

        /// <summary>
        /// 获取页面缓存值
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="sessionKey">缓存键</param>
        /// <param name="defValue">未取到缓存值时的默认值</param>
        /// <param name="targetFormId">目标表单Id，不传则默认为当前表单</param>
        /// <param name="pageId">页面Id，不传则默认为当前页面</param>
        /// <returns>缓存值</returns>
        protected T GetPageSessionValue<T>(string sessionKey, T defValue = default(T), string targetFormId = null, string pageId = null)
        {
            return this.OperationContext.GetPageSessionValue(sessionKey, defValue, targetFormId, pageId);
        }

        /// <summary>
        /// orm数据读写引擎
        /// </summary>
        protected IDataManager GetDataManager()
        {
            if (this.UserCtx == null) return null;
            var dm = this.Container?.GetService<IDataManager>();
            dm.Option = this.OperationContext.Option;
            return dm;
        }

        /// <summary>
        /// 模型服务引擎
        /// </summary>
        protected IMetaModelService MetaModelService { get; private set; }

        /// <summary>
        /// 任务处理服务
        /// </summary>
        protected ITaskProgressService TaskProgressService { get; private set; }

        /// <summary>
        /// 日志服务
        /// </summary>
        protected ILogService LogService { get; set; }

        /// <summary>
        /// 本地文本日志器
        /// </summary>
        private ILogServiceEx LocalLogger { get; set; }

        /// <summary>
        /// 数据库服务引擎
        /// </summary>
        protected IDBService DBService { get; private set; }

        /// <summary>
        /// 单据锁定服务
        /// </summary>
        protected IBillLockService BillLockService { get; private set; }

        /// <summary>
        /// 需要保存的数据包
        /// </summary>
        protected List<DynamicObject> NeedSaveDataEntities { get; private set; } = new List<DynamicObject>();

        /// <summary>
        /// 当前操作对应的异步任务id
        /// </summary>
        protected string TaskId
        {
            get
            {
                return this.GetQueryOrSimpleParam<string>("__taskId__");
            }
        }

        /// <summary>
        /// 自动保存数据包
        /// </summary>
        protected virtual bool AutoSaveData
        {
            get { return false; }
        }

        /// <summary>
        /// 页面的打开方式：默认为页签方式打开
        /// </summary>
        protected virtual Enu_OpenStyle OpenStyle { get; private set; }

        /// <summary>
        /// 领域类型
        /// </summary>
        protected virtual Enu_DomainType DomainType { get; private set; }

        /// <summary>
        /// 是否忽略基类自动追加的操作消息
        /// </summary>
        protected virtual bool IgnoreOpMessage
        {
            get
            {
                return false;
            }
        }

        /// <summary>
        /// 当前操作是否需要执行反写逻辑
        /// </summary>
        protected virtual bool CanDoWriteback
        {
            get
            {
                return false;
            }
        }

        /// <summary>
        /// 操作配置信息
        /// </summary>
        protected OperationConfig OpConfig { get; private set; }

        /// <summary>
        /// 操作控制参数
        /// </summary>
        protected OperationControlParam OpCtlParam { get; private set; }


        /// <summary>
        /// 操作执行过程
        /// </summary>
        //[PerfMonitor]
        public void Execute()
        {
            var watch = this.StartMonitor();
            try
            {
                this.CheckPermission();
            }
            catch (Exception)
            {
                if (this.OperationContext.DataEntities != null)
                {
                    this.RollbackOperationData(this.OperationContext.DataEntities.ToArray());
                }
                throw;
            }
            //this.Container.GetService<ILogServiceEx>().Info($"操作{this.OperationContext.OperationNo}/{this.OperationContext.OperationName}验权耗时：{watch.Elapsed}");

            //watch.Restart();
            this.OperationContext.Result.IsShowMessage = true;
            this.OperationContext.Result.IsSuccess = true;

            //初始化表单打开方式及领域类型
            this.SetOpenStyle();
            this.SetDomaiinType();

            this.NeedSaveDataEntities = new List<DynamicObject>();

            //初始化操作关联的操作配置信息
            if (this.OperationContext.HtmlOperation?.Parameter.IsNullOrEmptyOrWhiteSpace() == false)
            {
                this.OpConfig = JsonConvert.DeserializeObject<OperationConfig>(this.OperationContext.HtmlOperation.Parameter);
            }
            else
            {
                this.OpConfig = new OperationConfig();
            }

            this.OpCtlParam = new OperationControlParam()
            {
                DisableTransaction = this.GetQueryOrSimpleParam<bool>("disableTransaction", false),
                IgnoreOpMessage = this.IgnoreOpMessage,
            };
            var ope = new OnPrepareOperationOptionEventArgs(this.OpCtlParam, this.OpConfig, this.OperationContext.Option);
            this.PlugInProxy.InvokePlugInMethod("OnPrepareOperationOption", ope);

            DynamicObject[] dataEntities = null;
            var dm = this.OperationContext.Container.GetService<IDataManager>();
            if (this.OperationContext.SelectedRows != null
                && this.OperationContext.SelectedRows.Any()
                && this.OperationContext.HtmlForm != null
                && this.OperationContext.HtmlForm.ElementType != HtmlElementType.HtmlForm_DynamicForm
                && this.OperationContext.HtmlForm.ElementType != HtmlElementType.HtmlForm_ParameterForm
                && this.OperationContext.HtmlForm.ElementType != HtmlElementType.HtmlForm_ReportForm)
            {
                var dt = this.OperationContext.HtmlForm.GetDynamicObjectType(UserCtx);
                if (this.OperationContext.HtmlForm.ElementType == HtmlElementType.HtmlForm_ListReportForm)
                {
                    var ctx = this.OperationContext as ListReportOperationContext;
                    if (ctx != null && this.OperationContext.HtmlForm.BillHeadTableName.IsNullOrEmptyOrWhiteSpace() && ctx.RptDt != null)
                    {
                        dt = ctx.RptDt;
                    }
                }

                dm.InitDbContext(this.UserCtx, dt);
                dataEntities = dm.Select(this.OperationContext.SelectedRows.Select(o => o.PkValue).Distinct().ToArray(), this.OperationContext.Option, false)
                    .OfType<DynamicObject>()
                    .Where(o => o != null)
                    .ToArray();
            }
            //this.Container.GetService<ILogServiceEx>().Info($"操作{this.OperationContext.OperationNo}/{this.OperationContext.OperationName}加载操作数据包耗时：{watch.Elapsed}");
            //watch.Restart();

            var dataList = this.OperationContext.DataEntities?.ToArray() ?? dataEntities;

            var initEventArgs = new InitializeDataEntityEventArgs(dataList);
            this.PlugInProxy.InvokePlugInMethod("InitializeOperationDataEntity", initEventArgs);
            dataList = initEventArgs.DataEntitys;

            if (!initEventArgs.Cancel)
            {
                //数据包准备阶段（例如保存时，对一些默认字段进行赋值，然后才进行条件校验）
                this.InitializeOperationDataEntities(ref dataList);
            }
            //this.Container.GetService<ILogServiceEx>().Info($"操作{this.OperationContext.OperationNo}/{this.OperationContext.OperationName}预处理数据耗时：{watch.Elapsed}");
            //watch.Restart();

            // 校验单据锁定
            this.CheckBillLock(ref dataList);

            var idemotencyIds = new List<string>();
            var distributedLocks = InitDistributedLocks(dataList);

            var distributedLockService = this.Container.GetService<IDistributedLockService>();

            try
            {
                BeginDistributedLock(distributedLocks, distributedLockService);

                // 新增数据对象级别的幂等性验证 
                var ce = new CreateObjectIdemotencyEventArgs(dataList);
                this.PlugInProxy.InvokePlugInMethod("CreateObjectIdemotency", ce);

                if (!ce.IdemotencyIds.IsNullOrEmpty())
                {
                    foreach (var idemotencyId in ce.IdemotencyIds)
                    {
                        idemotencyIds.Add(this.UserCtx.CreateObjectIdempotency(idemotencyId));
                    }
                }

                //操作前执行逻辑
                this.BeforeExecute(ref dataList);

                watch.EndMonitor((duration) =>
                {
                    this.PerfDataView.BeforeOpExecTime = duration;
                });

                //系统校验关联数据包
                watch.Restart();

                // 允许尝试三次单据编号错误
                int retryBillNoTimes = 3;
                // 不刷新编码 
                this.OperationContext.Option.TryGetVariableValue<bool>("NotRefreshNumber", out var notRefreshNumber);
                // 忽略校验 
                this.OperationContext.Option.TryGetVariableValue<bool>("IgnoreValidateDataEntities", out var ignoreValidateDataEntities);
                if (!ignoreValidateDataEntities)
                {
                    // 校验tag
                    validate:
                    try
                    {
                        //watch.Restart();
                        using (var scope = this.UserCtx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
                        {
                            this.ValidateDataEntities(ref dataList, this.OperationContext.SelectedRows);
                            scope.Complete();
                        }

                    }
                    catch (BillNoDuplicateException)
                    {
                        // 不刷新编码直接抛出异常
                        if (notRefreshNumber) throw;

                        //修正编码规则
                        var billNoService = this.Container.GetService<IBillNoService>();
                        billNoService?.RevisionBillNo(this.UserCtx, this.OperationContext.HtmlForm.Id, dataList);

                        if (dataList != null && dataList.Length > 0)
                        {
                            // 尝试减一
                            retryBillNoTimes--;
                            if (retryBillNoTimes >= 0)
                            {
                                // 重新获取新编码
                                billNoService?.SetBillNo(this.UserCtx, this.OperationContext.HtmlForm.Id, dataList);

                                goto validate;
                            }
                        }

                        //回滚数据
                        RollbackOperationData(dataList);

                        throw;
                    }
                    watch.EndMonitor((duration) =>
                    {
                        this.PerfDataView.OpValidateExecTime = duration;
                    });
                }

                //激活操作前插件事件
                watch.Restart();
                var be1 = new BeforeExecuteOperationTransaction(dataList);
                this.PlugInProxy.InvokePlugInMethod("BeforeExecuteOperationTransaction", be1);
                dataList = be1.DataEntitys;
                watch.EndMonitor((duration) =>
                {
                    this.PerfDataView.BeforeOpPlugExecTime = duration;
                });

                if (be1.Cancel) return;

                if (this.OpCtlParam.DisableTransaction)
                {
                    DoOperationCore(ref dataList);
                }
                else
                {
                    using (var scope = this.UserCtx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
                    {
                        DoOperationCore(ref dataList);
                        scope.Complete();
                    }
                }

                watch.Restart();
                this.AfterExecute(ref dataList);
                watch.EndMonitor((duration) =>
                {
                    this.PerfDataView.AfterOpExecTime = duration;
                });

                //激活操作后插件事件
                watch.Restart();
                var ae2 = new AfterExecuteOperationTransaction(dataList);
                this.PlugInProxy.InvokePlugInMethod("AfterExecuteOperationTransaction", ae2);
                watch.EndMonitor((duration) =>
                {
                    this.PerfDataView.AfterOpPlugExecTime = duration;
                });
            }
            catch (Exception ex)
            {
                //操作日志在AfterExecute中记录，出现错误时，不一定执行了AfterExecute方法，导致没有操作日志信息，这里捕获错误并记录操作日志
                Task task = new Task(() =>
                {
                    this.WriteErrorOperationLog(dataList, ex);
                });
                ThreadWorker.QuequeTask(task, result =>
                {
                    if (result?.Exception != null)
                    {
                    }
                });

                // 友好提示数据库层面的编码重复问题
                if (ex is SqlException)
                {
                    var sqlEx = (SqlException)ex;
                    switch (sqlEx.Number)
                    {
                        // 具体说明查看：https://learn.microsoft.com/zh-cn/sql/relational-databases/errors-events/database-engine-events-and-errors-2000-to-2999?view=sql-server-ver16
                        case 2601://不能在具有唯一索引 '%.*ls' 的对象 '%.*ls' 中插入重复键的行。 重复的键值为 %ls。
                            throw new BillNoDuplicateException($"{this.OperationContext.HtmlForm.GetNumberField()?.Caption ?? "编码"}出现重复，请尝试重新保存！");
                        case 2627://违反了 %ls 约束 '%.*ls'。 不能在对象 '%.*ls' 中插入重复键。 重复的键值为 %ls。
                            if (sqlEx.Message.Contains("UNIQUE KEY"))
                            {
                                throw new BillNoDuplicateException($"{this.OperationContext.HtmlForm.GetNumberField()?.Caption ?? "编码"}出现重复，请尝试重新保存！");
                            }
                            break;
                    }
                }

                throw;
            }
            finally
            {
                if (!idemotencyIds.IsNullOrEmpty())
                {
                    foreach (var idemotencyId in idemotencyIds)
                    {
                        this.UserCtx.RemoveObjectIdempotency(idemotencyId);
                    }
                }

                EndDistributedLock(distributedLocks, distributedLockService);
            }
        }

        #region 并发控制

        /// <summary>
        /// 必须开启并发控制的操作码
        /// </summary>
        private static HashSet<string> MustEnableDistributedLockOpCodes = new HashSet<string>(StringComparer.OrdinalIgnoreCase) { "Save", "Audit", "BizClose", "Cancel", "Change", "Delete", "DeleteRow", "Forbid", "Submit", "SubmitChange", "UnAudit", "UnCancel", "UnChange", "UnClose", "UnForbid", "UnSubmit", "RejectFlow", "AuditFlow", "SubmitFlow", "TerminateFlow", "Pull" };

        /// <summary>
        /// 初始化并发控制锁
        /// </summary>
        /// <param name="dataList"></param>
        /// <returns></returns>
        private Dictionary<string, string> InitDistributedLocks(DynamicObject[] dataList)
        {
            var distributedLocks = new Dictionary<string, string>();
            if (!this.ServiceControlOption.EnableDistributedLock)
            {
                return distributedLocks;
            }

            var enableDistributedLock = this.OperationContext.HtmlOperation?.EnableDistributedLock ?? false;
            if (!enableDistributedLock)
            {
                enableDistributedLock =
                    MustEnableDistributedLockOpCodes.Contains(this.OperationContext.HtmlOperation?.OperationNo);
            }

            // 新增并发控制
            if (enableDistributedLock)
            {
                if (dataList != null && dataList.Any())
                {
                    var numberFld = this.OperationContext.HtmlForm?.GetNumberField();

                    distributedLocks = dataList
                        .Where(s => !Convert.ToString(s["id"]).IsNullOrEmptyOrWhiteSpace())
                        .ToDictionary(s => $"DistributedLock:{this.OperationContext.HtmlForm?.Id}:{s["id"]}",
                            s =>
                            {
                                var billno = (numberFld?.DynamicProperty?.GetValue<string>(s) ?? "N/A");

                                return
                                    $"{this.OperationContext.HtmlForm?.Caption} {numberFld?.Caption} 为 【{billno}】的数据正在锁定中，请稍后再操作！";
                            });
                }
            }

            var be2 = new InitDistributedLocksEventArgs(dataList, distributedLocks);
            this.PlugInProxy.InvokePlugInMethod("InitDistributedLocks", be2);
            distributedLocks = be2.DistributedLocks;

            return distributedLocks;
        }

        /// <summary>
        /// 开启并发锁定
        /// </summary>
        /// <param name="distributedLocks"></param>
        /// <param name="distributedLockService"></param>
        private void BeginDistributedLock(Dictionary<string, string> distributedLocks, IDistributedLockService distributedLockService)
        {
            if (distributedLocks == null || distributedLocks.Count == 0)
            {
                return;
            }

            // 新增并发控制 

            int timeOutSec = "".GetAppConfig<int>(HostConfigKeys.FW.DistributedLock_ExpiredSec, 300);
            var timeOut = TimeSpan.FromSeconds(timeOutSec);

            List<string> errorMsgs = new List<string>();

            foreach (var distributedLock in distributedLocks)
            {
                try
                {
                    if (!distributedLockService.GetDistributedLock(this.UserCtx, distributedLock.Key, this.UserCtx.RequestId, timeOut))
                    {
                        errorMsgs.Add(distributedLock.Value);
                    }
                }
                catch (Exception ex)
                {
                    //忽略错误
                }
            }

            if (errorMsgs.Any())
            {
                this.OperationContext.Result.ComplexMessage.ErrorMessages.AddRange(errorMsgs);

                throw new BusinessException("");
            }

            // 只有第一层才有
            this.ServiceControlOption.EnableDistributedLock = false;
        }

        /// <summary>
        /// 结束并发锁定
        /// </summary>
        /// <param name="distributedLocks"></param>
        /// <param name="distributedLockService"></param>
        private void EndDistributedLock(Dictionary<string, string> distributedLocks, IDistributedLockService distributedLockService)
        {
            //var enableDistributedLock = this.OperationContext.HtmlOperation?.EnableDistributedLock ?? false;
            //if (!enableDistributedLock)
            //{
            //    enableDistributedLock =
            //        MustEnableDistributedLockOpCodes.Contains(this.OperationContext.HtmlOperation?.OperationNo);
            //}

            if (distributedLocks == null || distributedLocks.Count == 0)
            {
                return;
            }

            // 释放并发控制 
            if (distributedLocks.Any())
            {
                foreach (var distributedLock in distributedLocks)
                {
                    try
                    {
                        distributedLockService.ReleaseDistributedLock(this.UserCtx, distributedLock.Key,
                        this.UserCtx.RequestId);
                    }
                    catch (Exception)
                    {
                        //忽略错误
                    }                    
                }
            }
        }

        #endregion

        private void DoOperationCore(ref DynamicObject[] dataList)
        {
            //Stopwatch watch = Stopwatch.StartNew();
            try
            {
                var watch = this.StartMonitor();
                //激活操作前插件事件
                var be2 = new BeginOperationTransactionArgs(dataList);
                this.PlugInProxy.InvokePlugInMethod("BeginOperationTransaction", be2);
                dataList = be2.DataEntitys;
                watch.EndMonitor((duration) =>
                {
                    this.PerfDataView.BeginOpPlugExecTime = duration;
                });

                watch.Restart();
                if (!be2.CancelOperation)
                {
                    this.DoExecute(ref dataList);
                }

                if (this.AutoSaveData)
                    this.SaveDataEntities(ref dataList);

                watch.EndMonitor((duration) =>
                {
                    this.PerfDataView.OpExecTime = duration;
                });

                //执行操作关联的服务
                watch.Restart();
                if (!be2.CancelFormService)
                {
                    this.ExecuteOperationService(ref dataList);
                }
                watch.EndMonitor((duration) =>
                {
                    this.PerfDataView.OpServiceExecTime = duration;
                });

                //激活操作前插件事件
                watch.Restart();
                var ae1 = new EndOperationTransactionArgs(dataList);
                this.PlugInProxy.InvokePlugInMethod("EndOperationTransaction", ae1);
                dataList = ae1.DataEntitys;
                watch.EndMonitor((duration) =>
                {
                    this.PerfDataView.EndOpPlugExecTime = duration;
                });
                var rows = this.OperationContext.SimpleData.GetValue("rows", null);
                if (!rows.IsNullOrEmptyOrWhiteSpace() && !rows.EqualsIgnoreCase("0")) 
                {
                    this.PerfDataView.Rows = rows;
                }
                var filters = this.OperationContext.SimpleData.GetValue("filters", null);
                if (!filters.IsNullOrEmptyOrWhiteSpace())
                {
                    this.PerfDataView.Filters = filters;
                }
            }
            catch (SqlException)
            {
                RollbackOperationData(dataList);
                throw;
            }
            catch (Exception)
            {
                RollbackOperationData(dataList);
                throw;
            }
        }

        /// <summary>
        /// 执行操作运行时配置关联的服务
        /// </summary>
        /// <param name="dataList"></param>
        protected virtual void ExecuteOperationService(ref DynamicObject[] dataList)
        {
            if (dataList == null || !dataList.Any()) return;

            var opId = this.OperationContext.OperationNo;
            if (this.OperationContext.HtmlOperation != null
                && !this.OperationContext.HtmlOperation.Id.IsNullOrEmptyOrWhiteSpace())
            {
                opId = this.OperationContext.HtmlOperation.Id;
            };
            if (opId.IsNullOrEmptyOrWhiteSpace()) return;

            //todo 根据当前的 formid 与 opId 去配置表里查到配置的服务别名，然后通过服务容器解析到服务实例，完成初始化

            //根据 业务对象 和 业务操作 获取服务配置信息
            var htmlForm = this.MetaModelService.LoadFormModel(this.UserCtx, "sys_opserconfig");
            if (htmlForm == null) return;

            //读取操作服务配置信息
            var _opserconfigdm = this.UserCtx.GetDefaultDataManager(htmlForm);

            var pkIdSql = string.Format("select {0} from {1} where fbizobject='{2}' and fbizop='{3}' and fforbidstatus='0' ",
                htmlForm.HeadEntity.PkFieldName,
                htmlForm.HeadEntity.TableName,
                this.OperationContext.HtmlForm.Id,
                opId);
            var pkIds = this.DBService.ExecuteDynamicObject(this.UserCtx, $"{pkIdSql} and fmainorgid='{this.UserCtx.Company}'", null)?.Select(o => o[0])?.ToArray();
            if (pkIds == null || !pkIds.Any())
            {
                pkIds = this.DBService.ExecuteDynamicObject(this.UserCtx, $"{pkIdSql} and fmainorgid='0'", null)?.Select(o => o[0])?.ToArray();
            }
            List<DynamicObject> lstConfigObjs = new List<DynamicObject>();
            if (pkIds != null && pkIds.Any())
            {
                //最多有且只有一个配置项（人为在保存配置的时候做了唯一性校验）
                var configObj = _opserconfigdm.Select(pkIds).OfType<DynamicObject>().FirstOrDefault();
                if (configObj != null)
                {
                    //是否有配置操作服务
                    var entrys = configObj["fentity"] as DynamicObjectCollection;
                    lstConfigObjs.AddRange(entrys);
                }
            }

            var lstOpServices = new List<FormServiceDesc>();
            lstOpServices.AddRange(lstConfigObjs.Select(o => new FormServiceDesc()
            {
                ServiceAlias = o["fopser"] as string,
                Condition = o["fcondition"] as string,
                ParamString = o["fserconfig"] as string
            }));

            //允许操作添加自行添加待执行的服务
            this.PrepareBusinessService(lstOpServices);

            //通知插件是否需要动态添加待执行的服务
            var e = new PrepareBusinessServiceEventArgs(lstOpServices);
            this.PlugInProxy.InvokePlugInMethod("PrepareBusinessServices", e);

            string strLifetimeScopeId = Guid.NewGuid().ToString();
            foreach (var entry in lstOpServices)
            {
                var aliasName = entry.ServiceAlias;
                if (aliasName.IsNullOrEmptyOrWhiteSpace()
                    && entry.ServiceId.IsNullOrEmptyOrWhiteSpace()) continue;

                JObject joServicePara = new JObject();
                joServicePara["serConfig"] = entry.ParamString?.HtmlDecode();   //服务配置参数
                joServicePara["condition"] = entry.Condition?.HtmlDecode();     //服务前置条件
                string strServicePara = joServicePara.ToJson();

                IBaseService serviceInst = null;
                if (!entry.ServiceId.IsNullOrEmptyOrWhiteSpace())
                {
                    serviceInst = this.Container.GetOperationService(entry.ServiceId);
                }
                else
                {
                    serviceInst = this.Container.BeginLifetimeScope(strLifetimeScopeId).GetServiceByName<IBaseService>(aliasName);
                }

                serviceInst?.InitializeService(this.OperationContext, strServicePara);

                if (serviceInst?.TransactionMode == 1)
                {
                    using (var tranService = this.UserCtx.CreateTransaction((int)TransactionScopeOption.Suppress))
                    {
                        serviceInst?.ExecuteService(ref dataList);

                        //检查是否有错误，如果有错误发生，直接爆出错误，避免提交产生错误数据
                        this.OperationContext.Result.ThrowIfHasError(true, "执行操作服务出现意外错误");

                        tranService.Complete();
                    }
                }
                else
                {
                    serviceInst?.ExecuteService(ref dataList);

                    //检查是否有错误，如果有错误发生，直接爆出错误，避免提交产生错误数据
                    this.OperationContext.Result.ThrowIfHasError(true, "执行操作服务出现意外错误");
                }
            }
        }

        private void RollbackOperationData(DynamicObject[] dataList)
        {
            UpdateMetaData(false);
        }

        /// <summary>
        /// 执行业务规则校验
        /// </summary>
        /// <param name="dataList"></param>
        /// <param name="selectedRows"></param>
        protected void ValidateDataEntities(ref DynamicObject[] dataList, IEnumerable<SelectedRow> selectedRows)
        {
            if (dataList == null || dataList.Length == 0)
            {
                return;
            }
            var lstRules = this.PrepareValidationRules() ?? new List<IDataValidRule>();

            //增加操作前置条件解析判断
            if (!this.OpConfig.Condition.Expr.IsNullOrEmptyOrWhiteSpace())
            {
                var conditionValidator = this.Container.GetValidRuleService(HtmlElementType.HtmlValidator_OpCondition);
                if (conditionValidator != null)
                {
                    conditionValidator.EntityKey = this.OperationContext.HtmlOperation?.EntityKey ?? "fbillhead";
                    conditionValidator.Initialize(this.OperationContext.UserContext, this.OpConfig.Condition.ToJson());
                    lstRules.Add(conditionValidator);
                }
            }

            var e = new PrepareValidationRulesEventArgs(lstRules);
            this.PlugInProxy.InvokePlugInMethod("PrepareValidationRules", e);
            if (e.Rules == null || e.Rules.Count == 0)
            {
                return;
            }

            var validResult = ValidationManager.Validate(this.OperationContext, this.OperationContext.HtmlForm, ref dataList, selectedRows, e.Rules);
            //TODO:将校验结果转换成当前操作上下文中的消息
            if (!validResult.IsSuccess)
            {
                var strMsg = string.Join("\r\n", validResult.Errors.Take(50).Select(o => o.ErrorMessage));
                var isImportNumber = this.GetQueryOrSimpleParam<string>("ImportNumber");
                if (e.RollbackWhenError || string.Equals(isImportNumber, "true", StringComparison.OrdinalIgnoreCase))
                {
                    throw new BusinessException(strMsg);
                }
                else
                {
                    this.OperationContext.Result.IsSuccess = false;
                    var errors = validResult.Errors.Take(50);
                    this.OperationContext.Result.ComplexMessage?.ErrorMessages.AddRange(validResult.Errors.Select(o => o.ErrorMessage));

                    var validDataEntities = validResult.Errors.Select(o => o.RootDataEntity)
                                                                    .Union(validResult.Errors.SelectMany(o => o.LinkDataEntities).Select(o => o.GetRoot()));
                    foreach (var dataEntity in validDataEntities)
                    {
                        if (dataEntity == null)
                        {
                            continue;
                        }
                        //rollback 
                        var pkId = dataEntity.DynamicObjectType.PrimaryKey.GetValue<string>(dataEntity);
                    }

                    // 不刷新编码 
                    this.OperationContext.Option.TryGetVariableValue<bool>("NotRefreshNumber", out var notRefreshNumber);
                    if (!notRefreshNumber)
                    {
                        //修正编码规则
                        var billNoService = this.Container.GetService<IBillNoService>();
                        billNoService?.RevisionBillNo(this.UserCtx, this.OperationContext.HtmlForm.Id, dataList);
                    }
                    dataList = dataList.Except(validDataEntities).ToArray();
                }
            }
        }

        /// <summary>
        /// 添加校验器
        /// </summary>
        /// <returns></returns>
        protected virtual List<IDataValidRule> PrepareValidationRules()
        {
            var lstRules = new List<IDataValidRule>();

            #region 反审核/删除 操作是否有配置检查下游单据是否存在的校验规则，如果没有配置则系统根据单据转换规则自动生成一个校验器
            var operationNo = this.OperationContext?.HtmlOperation?.OperationNo;
            if (operationNo.EqualsIgnoreCase("unaudit") || operationNo.EqualsIgnoreCase("delete"))
            {
                var serviceValidations = this.OperationContext?.HtmlOperation?.ServiceValidations;
                var billLinkCheckRule = serviceValidations?.FirstOrDefault(o => o.ValidationId.EqualsIgnoreCase(HtmlElementType.HtmlValidator_BillLinkCheck));
                if (billLinkCheckRule == null)
                {
                    var jaLink = new JArray();
                    var metaService = this.Container.GetService<IMetaModelService>();
                    var convertRules = metaService?.LoadConvertRuleByFormId(this.UserCtx, this.OperationContext?.HtmlForm?.Id);
                    if (convertRules != null)
                    {
                        foreach (var convertRule in convertRules)
                        {
                            if (convertRule.TargetFormId.IsNullOrEmptyOrWhiteSpace()) continue;

                            var relationFieldKey = getFieldMapObject(convertRule, new[] { convertRule.RelationFieldKey, "fsourcenumber", "fsourceinterid", "fsourcebillno", "fsourceentryid" });
                            if (relationFieldKey == null) continue;

                            //排除源单字段在表体的情况
                            //var srcField = this.OperationContext?.HtmlForm?.GetField(sourceNumberField.SrcFieldId);
                            //if (srcField == null || srcField.Entity is HtmlSubEntryEntity || srcField.Entity is HtmlEntryEntity) continue;

                            var realtionFormIdFieldKey = getFieldMapObject(convertRule, new[] { convertRule.RealtionFormIdFieldKey, "fsourcetype", "fsourceformid" });
                            if (realtionFormIdFieldKey == null) continue;

                            JObject joLink = new JObject();
                            joLink["linkFormId"] = convertRule.TargetFormId;
                            joLink["linkFieldKey"] = relationFieldKey.Id;
                            joLink["linkFormFieldKey"] = realtionFormIdFieldKey.Id;
                            joLink["sourceLinkFieldKey"] = relationFieldKey.SrcFieldId;
                            joLink["sourceLinkFilter"] = convertRule.BillLinkCheckFitler;

                            jaLink.Add(joLink);
                        }
                    }
                    if (jaLink.Count > 0)
                    {
                        var joParam = new JObject();
                        joParam["expr"] = jaLink;
                        joParam["message"] = $"已经生成了下游单据，不允许{this.OperationContext?.HtmlOperation?.OperationName}！";

                        serviceValidations.Add(new HtmlValidation()
                        {
                            Id = Guid.NewGuid().ToString().ToLower(),
                            ValidationId = HtmlElementType.HtmlValidator_BillLinkCheck,
                            Parameter = joParam.ToJson()
                        });
                    }
                }
            }
            #endregion

            if (this.OperationContext.HtmlOperation != null
                && this.OperationContext.HtmlOperation.ServiceValidations.Any())
            {
                foreach (var validation in this.OperationContext.HtmlOperation.ServiceValidations)
                {
                    var validationRule = this.Container.GetValidRuleService(validation.ValidationId);
                    if (validationRule != null)
                    {
                        validationRule.EntityKey = validation.EntityKey ?? this.OperationContext.HtmlOperation?.EntityKey ?? "fbillhead";
                        validationRule.Initialize(this.OperationContext.UserContext, validation);
                        if (validationRule is IBusinessValidRule)
                        {
                            validationRule.InitializeWithOperation(this.OperationContext.HtmlOperation);
                        }

                        lstRules.Add(validationRule);
                    }
                }
            }

            lstRules.Add(new PresetDataOpValidation());

            return lstRules;
        }

        private FieldMapObject getFieldMapObject(ConvertRule convertRule, string[] fieldKeys)
        {
            if (convertRule == null || convertRule.FieldMappings == null || convertRule.FieldMappings.Count <= 0 || fieldKeys == null || fieldKeys.Length <= 0)
            {
                return null;
            }

            foreach (var fieldKey in fieldKeys)
            {
                if (string.IsNullOrWhiteSpace(fieldKey))
                {
                    continue;
                }
                var fieldMapObject = convertRule.FieldMappings.Where(o => o.Id.EqualsIgnoreCase(fieldKey)).FirstOrDefault();
                if (fieldMapObject != null && false == string.IsNullOrWhiteSpace(fieldMapObject.Id) && false == string.IsNullOrWhiteSpace(fieldMapObject.SrcFieldId))
                {
                    return fieldMapObject;
                }
            }

            return null;
        }

        /// <summary>
        /// 添加操作关联的基础服务
        /// </summary>
        /// <returns></returns>
        protected virtual void PrepareBusinessService(List<FormServiceDesc> lstOpServices)
        {
            //执行操作模型中配置的关联服务实例
            if (this.OperationContext.HtmlOperation != null)
            {
                foreach (var opService in this.OperationContext.HtmlOperation.OperationServices)
                {
                    lstOpServices.Add(new FormServiceDesc()
                    {
                        ServiceId = opService.ServiceId,
                        ParamString = opService.Parameter,
                        Condition = opService.Precondition,
                    });
                }
            }
        }

        /// <summary>
        /// 操作关联的权限项
        /// </summary>
        protected virtual string PermItem
        {
            get
            {
                return this.OperationContext?.HtmlOperation?.PermItemId ?? "";
            }
        }

        /// <summary>
        /// 校验权限的表单标识
        /// </summary>
        protected virtual string PermFormId
        {
            get
            {
                return this.OperationContext?.HtmlForm?.Id;
            }
        }

        /// <summary>
        /// 操作名称
        /// </summary>
        protected virtual string OperationName
        {
            get
            {
                return this.OperationContext.HtmlOperation?.OperationName ?? this.OperationContext.OperationName;
            }
        }

        /// <summary>
        /// 实际操作服务执行过程
        /// </summary>
        //[PerfMonitor]
        protected abstract void DoExecute(ref DynamicObject[] dataEntities);

        /// <summary>
        /// 执行操作前的相关处理
        /// </summary>
        //[PerfMonitor]
        protected virtual void BeforeExecute(ref DynamicObject[] dataEntities)
        {
            //记录操作开始时日志
            this.WriteStartOperationLog(dataEntities);
        }

        /// <summary>
        /// 执行操作后的相关动作（如  更新缓存？？？）
        /// </summary>
        [PerfMonitor]
        protected virtual void AfterExecute(ref DynamicObject[] dataEntities)
        {
            //生成操作消息明细
            this.BuildOperationMessage(dataEntities);

            //记录操作结束后日志
            this.WriteEndOperationLog(dataEntities);

            if (this.OperationContext.Result != null && dataEntities != null && dataEntities.Length == 1
                && this.OperationContext.HtmlForm != null && this.OperationContext.HtmlForm.SealSetting != null)
            {
                var svc = this.Container.GetService<IUiDataConverter>();
                var sealImg = svc.GetSealImge(this.UserCtx, this.OperationContext.HtmlForm, dataEntities[0]);
                var action = new { ShowSealImg = true, SealImg = sealImg };
                this.OperationContext.Result.HtmlActions.Add(action);
            }

            this.ReleaseBillLock(dataEntities);
        }

        /// <summary>
        /// 生成操作消息
        /// </summary>
        /// <param name="dataEntities"></param>
        protected void BuildOperationMessage(DynamicObject[] dataEntities)
        {
            //当产生明细消息时，平台不再自动设置操作结果消息
            if (this.OperationContext.Result.ComplexMessage.HasMessage) return;

            if (!this.OperationName.IsNullOrEmptyOrWhiteSpace()
                && !this.OpCtlParam.IgnoreOpMessage
                && this.OperationContext.Result.IsSuccess)
            {
                var billNoField = this.OperationContext.HtmlForm.GetNumberField();
                if (billNoField != null && dataEntities != null
                    && this.OperationContext.HtmlForm.ElementType != HtmlElementType.HtmlForm_DynamicForm
                    && this.OperationContext.HtmlForm.ElementType != HtmlElementType.HtmlForm_ParameterForm
                    && billNoField.DynamicProperty != null)
                {
                    foreach (var dataObj in dataEntities)
                    {
                        var billNo = billNoField.DynamicProperty.GetValue<string>(dataObj);
                        string opMsg = $"{this.OperationContext.HtmlForm.Caption}{billNo}{this.OperationName}操作成功！";
                        //移动端不加单号
                        if ((this.OperationContext.UserContext.ClientType & (long)Enu_ClientType.Mobile) == (long)Enu_ClientType.Mobile)
                        {
                            opMsg = $"{this.OperationContext.HtmlForm.Caption}{this.OperationName}操作成功！";
                        }
                        this.OperationContext.Result.ComplexMessage.SuccessMessages.Add(opMsg);
                    }
                }
                else if (this.OperationContext.Result.SimpleMessage.IsNullOrEmptyOrWhiteSpace())
                {
                    this.OperationContext.Result.SimpleMessage = string.Format("{0}{1}操作成功！", this.OperationContext.HtmlForm.Caption, this.OperationName ?? "N/A");
                }
            }
        }

        /// <summary>
        /// 当前操作中需要保存的数据包
        /// </summary>
        protected virtual void SaveDataEntities(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null || !dataEntities.Any()) return;
            var dataGroups = dataEntities.GroupBy(o => o.DynamicObjectType);
            var dm = this.OperationContext.Container.GetService<IDataManager>();
            var seqService = this.OperationContext.Container.GetService<IDataEntityPkService>();
            foreach (var group in dataGroups)
            {
                dm.InitDbContext(this.UserCtx, group.Key);
                seqService.AutoSetPrimaryKey(this.OperationContext.UserContext, group, dm.DataEntityType);
                dm.Save(group, null, this.OperationContext.Option);
            }

        }

        /// <summary>
        /// 初始化服务操作
        /// </summary>
        /// <param name="services"></param>
        public void Initialize(params object[] services)
        {
            if (services == null) return;
            var watch = Stopwatch.StartNew();
            foreach (var service in services)
            {
                if (service == null) continue;

                if (typeof(PlugInProxy<IOperationServicePlugIn>).IsAssignableFrom(service.GetType()))
                {
                    this.PlugInProxy = service as PlugInProxy<IOperationServicePlugIn>;
                }
                if (typeof(OperationContext).IsAssignableFrom(service.GetType()))
                {
                    this.OperationContext = service as OperationContext;

                    this.Gateway = this.OperationContext?.Container?.GetService<IHttpServiceInvoker>();
                    this.Cache = this.OperationContext?.Container?.GetService<IRedisCache>();
                    (this.Cache as IPreInitCache)?.Init(this.OperationContext.UserContext);

                    this.CacheMem = this.OperationContext?.Container?.GetService<IMemoryCache>();
                    (this.CacheMem as IPreInitCache)?.Init(this.OperationContext.UserContext);

                    this.DBService = this.OperationContext?.Container?.GetService<IDBService>();
                    this.MetaModelService = this.OperationContext?.Container?.GetService<IMetaModelService>();

                    this.TaskProgressService = this.OperationContext?.Container?.GetService<ITaskProgressService>();
                    this.LogService = this.OperationContext?.Container?.GetService<ILogService>();

                    this.LocalLogger = this.OperationContext?.Container.GetService<ILogServiceEx>();
                    //IScheduleTaskLogger taskLogger = null;
                    //if (this.OperationContext?.Option?.TryGetVariableValue("__taskLogger__", out taskLogger) == true)
                    //{
                    //    this.TaskLogger = taskLogger;
                    //}

                    this.BillLockService = this.OperationContext?.Container.GetService<IBillLockService>();
                }

                if (service is PerfmonDataEntryView)
                {
                    this.PerfDataView = service as PerfmonDataEntryView;
                }

                if (service is ServiceControlOption)
                {
                    this.ServiceControlOption = service as ServiceControlOption;
                }
            }

            this.OperationContext.Option.SetVariableValue("_plugInProxy_", this.PlugInProxy);

            if (this.OperationContext.PageId.IsNullOrEmptyOrWhiteSpace())
            {
                this.OperationContext.PageId = this.GetQueryOrSimpleParam<string>("pageId");
            }

            if (this.OperationContext.HtmlOperation != null && this.OperationContext.HtmlOperation.UseSlave)
            {
                this.OperationContext.UserContext = this.SlaveUserContent;
            }

            //给服务初始化干预时机
            this.InitializeService();
        }


        private void UpdateMetaData(bool rebuild)
        {
            if (this.OperationContext == null || this.OperationContext.HtmlForm == null)
            {
                return;
            }
            if (!this.OperationContext.Option.GetAutoUpdateScheme()) return;

            //excel导入的，不需要更新
            var excelImport = this.GetQueryOrSimpleParam<string>("TopOrperationNo", "");//是否从excel导入  
            if (excelImport.EqualsIgnoreCase("ExcelImport"))
            {
                return;
            }

            var dm = this.GetDataManager();
            var currFormDt = this.OperationContext.HtmlForm.GetDynamicObjectType(UserCtx);
            dm?.InitDbContext(this.UserCtx, currFormDt);
            var refFlds = this.OperationContext.HtmlForm?.GetFieldList().Where(x => x is HtmlBaseDataField);
            foreach (HtmlBaseDataField fld in refFlds)
            {
                var refForm = fld.RefHtmlForm(this.OperationContext.UserContext);
                if (refForm != null)
                {
                    //触发引用对象自动重建表结构
                    var refDt = refForm.GetDynamicObjectType(UserCtx);
                    dm?.InitDbContext(this.UserCtx, refDt);
                }

            }
        }

        /// <summary>
        /// 写操作开始日志
        /// </summary>
        /// <param name="dataEntities"></param>
        protected void WriteStartOperationLog(DynamicObject[] dataEntities)
        {
            if (this.OperationContext.Option.GetIgnoreOpLogFlag())
            {
                //操作上下文选项里设置了忽略日志的行为，系统不进行记录
                return;
            }

            var excelImport = this.GetQueryOrSimpleParam<string>("TopOrperationNo", "");
            if (excelImport.EqualsIgnoreCase("ExcelImport")) return;

            //创建日志对象
            var lstLogEntryObjs = this.CreateOperationLogEntry(dataEntities, (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Start));

            if (lstLogEntryObjs?.Any() == true)
            {
                this.LogService?.BatchWriteLog(this.OperationContext.UserContext, lstLogEntryObjs);
            }
        }

        /// <summary>
        /// 写操作错误日志
        /// </summary>
        /// <param name="dataEntities"></param>
        protected void WriteErrorOperationLog(DynamicObject[] dataEntities, Exception ex)
        {
            if (this.OperationContext.Option.GetIgnoreOpLogFlag())
            {
                //操作上下文选项里设置了忽略日志的行为，系统不进行记录
                return;
            }

            var excelImport = this.GetQueryOrSimpleParam<string>("TopOrperationNo", "");
            if (excelImport.EqualsIgnoreCase("ExcelImport")) return;


            //创建日志对象
            var lstLogEntryObjs = this.CreateErrOperationLogEntry(dataEntities, (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete), ex);

            if (lstLogEntryObjs?.Any() == true)
            {
                this.LogService?.BatchWriteLog(this.OperationContext.UserContext, lstLogEntryObjs);
            }

        }


        /// <summary>
        /// 写操作结束日志
        /// </summary>
        /// <param name="dataEntities"></param>
        protected void WriteEndOperationLog(DynamicObject[] dataEntities)
        {
            if (this.OperationContext.Option.GetIgnoreOpLogFlag())
            {
                //操作上下文选项里设置了忽略日志的行为，系统不进行记录
                return;
            }

            var excelImport = this.GetQueryOrSimpleParam<string>("TopOrperationNo", "");
            if (excelImport.EqualsIgnoreCase("ExcelImport")) return;

            //联查、查看操作日志不记录日志
            if (this.OperationContext.OperationNo.EqualsIgnoreCase("Linkformsearch") || this.OperationContext.OperationNo.EqualsIgnoreCase("operatelog"))
            {
                return;
            }
            //创建日志对象
            var lstLogEntryObjs = this.CreateOperationLogEntry(dataEntities, (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete));

            if (lstLogEntryObjs?.Any() == true)
            {
                this.LogService?.BatchWriteLog(this.OperationContext.UserContext, lstLogEntryObjs);
            }

        }


        /// <summary>
        /// 创建错误日志记录对象
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="logCategory"></param>
        protected virtual IEnumerable<LogEntry> CreateErrOperationLogEntry(IEnumerable<DynamicObject> dataEntities, long logCategory, Exception ex)
        {
            List<LogEntry> lstLogEntryObjs = new List<LogEntry>();

            if (dataEntities.IsNullOrEmpty()) return lstLogEntryObjs;

            var numberField = this.OperationContext.HtmlForm.GetNumberField();

            foreach (var dataEntity in dataEntities)
            {
                var opName = this.GetActualOperationName(this.OperationContext.HtmlForm, dataEntity);
                if (opName.IsNullOrEmptyOrWhiteSpace()) continue;

                var pkId = dataEntity.GetPrimaryKeyValue(false);
                if (pkId.IsEmptyPrimaryKey()) continue;

                string content = $"执行【{opName}】失败，错误信息：{ex.Message}";

                lstLogEntryObjs.Add(new LogEntry()
                {
                    BillFormId = this.OperationContext.HtmlForm.Id,
                    OpCode = this.OperationContext.OperationNo,
                    Level = Enu_LogLevel.Info.ToString(),
                    Category = logCategory,
                    Content = content,
                    BillIds = Convert.ToString(pkId),
                    BillNos = Convert.ToString(numberField?.DynamicProperty?.GetValue(dataEntity)),
                    Detail = ex.StackTrace,
                    DebugData = this.OperationContext.Result?.ToJson() ?? "",
                    OpName = opName,
                });
            }
            return lstLogEntryObjs;
        }

        /// <summary>
        /// 创建日志记录对象
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="logCategory"></param>
        protected virtual IEnumerable<LogEntry> CreateOperationLogEntry(IEnumerable<DynamicObject> dataEntities, long logCategory)
        {
            List<LogEntry> lstLogEntryObjs = new List<LogEntry>();

            if (dataEntities.IsNullOrEmpty()) return lstLogEntryObjs;

            var numberField = this.OperationContext.HtmlForm.GetNumberField();

            foreach (var dataEntity in dataEntities)
            {
                var opName = this.GetActualOperationName(this.OperationContext.HtmlForm, dataEntity);
                if (opName.IsNullOrEmptyOrWhiteSpace()) continue;

                var pkId = dataEntity.GetPrimaryKeyValue(false);
                if (pkId.IsEmptyPrimaryKey()) continue;

                string content = "";
                if ((logCategory & (long)Enu_LogCategory.BizOp_Start) == (long)Enu_LogCategory.BizOp_Start)
                {
                    content = $"正在执行【{opName}】操作……";
                }

                if ((logCategory & (long)Enu_LogCategory.BizOp_Complete) == (long)Enu_LogCategory.BizOp_Complete)
                {
                    var opdesc = this.GetQueryOrSimpleParam<string>("execOpinion");
                    if (!opdesc.IsNullOrEmptyOrWhiteSpace())
                    {
                        content = $"执行了【{opName}】操作：{opdesc}";
                    }
                    else
                    {
                        content = $"执行了【{opName}】操作！";
                    }
                }

                //获得详细审计日志
                var auditDetail = this.CreateBillAuditLog(this.OperationContext.HtmlForm, dataEntity, logCategory);
                if (auditDetail != null && (auditDetail.Items == null || auditDetail.Items.Count == 0))
                {
                    auditDetail = null;
                }
                //Task52991【运维优化】删除操作增加审计日志
                var detail = auditDetail?.ToJson() ?? "";
                detail = RepalceValueDisPlay(detail);
                if (auditDetail == null && opName == "删除")
                {
                    detail = dataEntity.ToJson();
                }

                lstLogEntryObjs.Add(new LogEntry()
                {
                    BillFormId = this.OperationContext.HtmlForm.Id,
                    OpCode = this.OperationContext.OperationNo,
                    Level = Enu_LogLevel.Info.ToString(),
                    Category = logCategory,
                    Content = content,
                    BillIds = Convert.ToString(pkId),
                    BillNos = Convert.ToString(numberField?.DynamicProperty?.GetValue(dataEntity)),
                    Detail = detail,
                    DebugData = this.OperationContext.Result?.ToJson() ?? "",
                    OpName = opName,
                });
            }
            return lstLogEntryObjs;
        }

        private string RepalceValueDisPlay(string detailValue)
        {
            return detailValue.Replace("oldValue", "更新前").Replace("oldDisplayValue", "更新前展示").Replace("newValue", "更新后").Replace("newDisplayValue", "更新后展示");
        }

        /// <summary>
        /// 根据数据包获取实体操作名称
        /// </summary>
        /// <param name="htmlForm"></param>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        protected virtual string GetActualOperationName(HtmlForm htmlForm, DynamicObject dataEntity)
        {
            var opName = "";
            if (this.OperationContext.UserContext.CurrentRequestObject is DynamicDTOWrapper)
            {
                opName = (this.OperationContext.UserContext.CurrentRequestObject as DynamicDTOWrapper).OperationName;
            }
            if (opName.IsNullOrEmptyOrWhiteSpace())
            {
                opName = this.OperationName;
            }
            return opName;
        }

        /// <summary>
        /// 生成表单审计日志
        /// </summary>
        /// <param name="htmlForm"></param>
        /// <param name="dataEntity"></param>
        /// <param name="logCategory"></param>
        /// <returns></returns>
        protected virtual BillAuditDetailObject CreateBillAuditLog(HtmlForm htmlForm, DynamicObject dataEntity, long logCategory)
        {
            return null;
        }
        /// <summary>
        /// 权限检测
        /// </summary>
        protected virtual void CheckPermission()
        {
            var ignoreCheck = this.GetQueryOrSimpleParam<string>("IgnoreCheckPermssion", "false").EqualsIgnoreCase("true")
               || this.GetQueryOrSimpleParam<string>("IgnoreCheckPermssion", "0").EqualsIgnoreCase("1");
            if (ignoreCheck)
            {
                return;
            }
            var e = new OnCheckPermssionArgs();
            e.FormId = this.PermFormId;
            e.PermItem = this.PermItem;
            this.PlugInProxy.InvokePlugInMethod("OnCheckPermssion", e);
            if (e.IgnoreCheck || e.FormId.IsNullOrEmptyOrWhiteSpace()
                || e.PermItem.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }

            //没开启权限的表单，不验权
            if (this.OperationContext.HtmlForm.EnableRAC == false)
            {
                return;
            }

            var permSrv = this.OperationContext.Container.GetService<IPermissionService>();
            permSrv?.CheckPermission(this.OperationContext.UserContext, new MetaCore.PermData.PermAuth(this.OperationContext.UserContext)
            {
                FormId = e.FormId,
                OperationName = this.OperationContext.HtmlOperation?.OperationName ?? this.OperationName,
                PermId = e.PermItem,
            });
        }

        /// <summary>
        /// 初始化操作关联的数据包信息
        /// </summary>
        /// <param name="dataEntities"></param>
        protected virtual void InitializeOperationDataEntities(ref DynamicObject[] dataEntities)
        {

        }
        /// <summary>
        /// 服务初始化事件
        /// </summary>
        protected virtual void InitializeService()
        {

        }

        /// <summary>
        /// 当前上下文中是否有指定的参数
        /// </summary>
        /// <param name="key">指定的参数名</param>
        /// <returns></returns>
        protected bool HasParamKey(string key)
        {
            var has = this.OperationContext?.QueryStringParam?.Keys?.Contains(key, StringComparer.OrdinalIgnoreCase) ?? false;
            if (!has)
            {
                has = this.OperationContext?.SimpleData?.Keys?.Contains(key, StringComparer.OrdinalIgnoreCase) ?? false;
            }
            return has;
        }

        /// <summary>
        /// 从当前上下文里获取url参数或简单数据参数
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="def"></param>
        /// <returns></returns>
        protected T GetQueryOrSimpleParam<T>(string key, T def = default(T))
        {
            object objValue = null;
            objValue = this.OperationContext.QueryStringParam.GetValue(key, null);

            if (objValue.IsNullOrEmpty() && this.OperationContext.SimpleData != null)
            {
                objValue = this.OperationContext.SimpleData.GetValue(key, null);
            }

            if (objValue.IsNullOrEmpty())
            {
                T value = def;
                if ((bool)this.OperationContext.Option?.TryGetVariableValue(key, out value))
                {
                    objValue = value;
                }
            }

            if (objValue.IsNullOrEmpty() && this.OpConfig != null && this.OpConfig.Parameter != null)
            {
                objValue = this.OpConfig.Parameter.Value<T>(key);
            }

            //防止死循环
            if (objValue.IsNullOrEmpty()
                && !key.EqualsIgnoreCase("__gp__"))
            {
                objValue = this.GlobalParameter?.GetValue(key, null);
            }

            if (objValue.IsNullOrEmpty())
            {
                var htppRequest = this.OperationContext?.GetCurrentRequest();
                if (htppRequest != null && htppRequest.Headers["X-MsRoute"].IsNullOrEmptyOrWhiteSpace())
                {
                    // 如果不是页面关闭操作，才获取页面会话级参数，因为这里获取参数时又会将当前关闭的页面重新初始化到缓存中，
                    // 导致关闭页面后无法重新打开页面的问题（提示：当前业务对象不允许打开多次）
                    if (!this.OperationContext.OperationNo.EqualsIgnoreCase("close") && !this.OperationContext.OperationNo.EqualsIgnoreCase("querydatadesc") && !key.EqualsIgnoreCase("formcaption"))
                    {
                        dynamic pageSession = this.OperationContext.GetPageSession();
                        var formPara = pageSession?.FormParameter;
                        if (formPara is FormParameter)
                        {
                            objValue = (formPara as FormParameter).CustomParameter?.GetValue(key, null);
                        }
                    }
                }
            }

            if (objValue.IsNullOrEmpty())
            {
                return def;
            }

            if (typeof(T).IsEnum)
            {
                return objValue.ToEnum<T>();
            }

            return (T)Convert.ChangeType(objValue, typeof(T));
        }

        /// <summary>
        /// 获取当前页面会话区变量
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="defValue"></param>
        /// <returns></returns>
        protected virtual T GetSessionValue<T>(string key, T defValue = default(T))
        {
            dynamic pageSession = this.OperationContext.GetPageSession();
            return this.GetSessionValue(pageSession, key, defValue);
        }

        /// <summary>
        /// 获取当前页面的父页面会话区变量
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="defValue"></param>
        /// <returns></returns>
        protected virtual T GetParentSessionValue<T>(string key, T defValue = default(T))
        {
            dynamic parentPageSession = this.OperationContext.GetParentPageSession();
            return this.GetSessionValue(parentPageSession, key, defValue);
        }

        /// <summary>
        /// 获取指定页面会话区变量
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="pageSession"></param>
        /// <param name="key"></param>
        /// <param name="defValue"></param>
        /// <returns></returns>
        private T GetSessionValue<T>(dynamic pageSession, string key, T defValue = default(T))
        {
            object objValue = null;
            var formPara = pageSession?.FormParameter;
            if (formPara is FormParameter)
            {
                objValue = (formPara as FormParameter).CustomParameter?.GetValue(key, null);
            }
            if (objValue == null) return defValue;

            return (T)Convert.ChangeType(objValue, typeof(T));
        }

        /// <summary>
        /// 操作关联的全局参数
        /// </summary>
        protected virtual Dictionary<string, object> GlobalParameter
        {
            get
            {
                var para = this.GetQueryOrSimpleParam<string>("__gp__", "");
                if (para.IsNullOrEmptyOrWhiteSpace()) para = "{}";
                return para.FromJson<Dictionary<string, object>>() ?? new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
            }
        }

        /// <summary>
        /// 设置页面打开方式（如果前端有传递，则用传递的方式打开，否则按默认的方式打开）
        /// </summary>
        private void SetOpenStyle()
        {
            var openStyle = GetQueryOrSimpleParam<string>("openStyle");
            if (!openStyle.IsNullOrEmptyOrWhiteSpace())
            {
                switch (openStyle.Trim().ToLower())
                {
                    case "modal":
                        this.OpenStyle = Enu_OpenStyle.Modal;
                        break;
                    case "nonmodal":
                        this.OpenStyle = Enu_OpenStyle.NonModal;
                        break;
                    case "incontainer":
                        this.OpenStyle = Enu_OpenStyle.InContainer;
                        break;
                    case "slidefromright":
                        this.OpenStyle = Enu_OpenStyle.SlideFromRight;
                        break;
                    default:
                        this.OpenStyle = Enu_OpenStyle.Default;
                        break;
                }
            }
        }

        private void SetDomaiinType()
        {
            if (this.OperationContext is ListTreeOperationContext)
            {
                this.DomainType = Enu_DomainType.ListTree;
            }
            else if (this.OperationContext is ListReportOperationContext)
            {
                this.DomainType = Enu_DomainType.ListReport;
            }
            else if (this.OperationContext is ListOperationContext)
            {
                this.DomainType = Enu_DomainType.List;
            }
            else if (this.OperationContext is BillOperationContext)
            {
                this.DomainType = Enu_DomainType.Bill;
            }
            else if (this.OperationContext is ReportOperationContext)
            {
                this.DomainType = Enu_DomainType.Report;
            }
            else if (this.OperationContext is DynamicOperationContext)
            {
                this.DomainType = Enu_DomainType.Dynamic;
            }
            else if (this.OperationContext is ParameterOperationContext)
            {
                this.DomainType = Enu_DomainType.Parameter;
            }
            else
            {
                throw new BusinessException("未知的业务领域！");
            }

            var domainType = GetQueryOrSimpleParam<string>("domainType");
            if (!domainType.IsNullOrEmptyOrWhiteSpace())
            {
                switch (domainType.Trim().ToLower())
                {
                    case "listtree":
                        this.DomainType = Enu_DomainType.ListTree;
                        break;
                    case "listreport":
                        this.DomainType = Enu_DomainType.ListReport;
                        break;
                    case "list":
                        this.DomainType = Enu_DomainType.List;
                        break;
                    case "bill":
                        this.DomainType = Enu_DomainType.Bill;
                        break;
                    case "report":
                        this.DomainType = Enu_DomainType.Report;
                        break;
                    case "dynamic":
                        this.DomainType = Enu_DomainType.Dynamic;
                        break;
                    case "parameter":
                        this.DomainType = Enu_DomainType.Parameter;
                        break;
                    default:
                        break;
                }
            }
        }

        /// <summary>
        /// 设置当前表单的标题名称
        /// </summary>
        /// <param name="formShowPara"></param>
        protected virtual void SetFormCaption(FormShowParameter formShowPara)
        {
            var formCaption = this.GetQueryOrSimpleParam<string>("formCaption");
            if (!formCaption.IsNullOrEmptyOrWhiteSpace())
            {
                formShowPara.FormCaption = formCaption;
            }
            var containerId = this.GetQueryOrSimpleParam<string>("containerId");
            if (formShowPara.OpenStyle == Enu_OpenStyle.InContainer)
            {
                formShowPara.ContainerId = containerId;
            }
            //允许业务插件自行设置表单的标题名称
            var ae = new OnCustomServiceEventArgs()
            {
                EventName = "onsetformcaption",
                EventData = formShowPara,
            };
            this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", ae);
        }

        /// <summary>
        /// 提供写日志接口，如果是在计划任务上下文里，提供写入日志对象的可能
        /// </summary>
        /// <param name="message"></param>
        protected void WriteLog(string message)
        {
            this.LocalLogger?.Info(message);
        }

        /// <summary>
        /// //允许业务插件自行设置基础资料弹窗列
        /// </summary>
        /// <param name="lstColumns"></param>
        protected virtual void SetDatabaseListColumns(IEnumerable<ColumnObject> lstColumns)
        {

        }

        /// <summary>
        /// 校验单据锁定
        /// </summary>
        /// <param name="dataEntities"></param>
        protected virtual void CheckBillLock(ref DynamicObject[] dataEntities)
        {
            if (dataEntities.IsNullOrEmpty())
            {
                return;
            }

            if (!EnableBillLock())
            {
                return;
            }

            List<DynamicObject> filterDataEntities = new List<DynamicObject>();

            var lockToken = this.GetQueryOrSimpleParam<string>("__LockToken__");
            var numberFld = this.OperationContext.HtmlForm.GetNumberField();

            var operationName = this.OperationName;
            if (operationName.IsNullOrEmptyOrWhiteSpace())
            {
                switch (this.OperationContext.OperationNo.ToLower())
                {
                    case "submitflow":
                        operationName = "提交";
                        break;
                    case "auditflow":
                        operationName = "审核";
                        break;
                    case "rejectflow":
                        operationName = "反审核";
                        break;
                    case "terminateflow":
                        operationName = "撤销";
                        break;
                    default:
                        operationName = "当前操作";
                        break;
                }
            }

            var throwError = dataEntities.Length == 1;

            foreach (var dataEntity in dataEntities)
            {
                string pkValue = Convert.ToString(dataEntity["id"]);

                if (this.BillLockService.IsLocked(this.UserCtx, this.OperationContext.HtmlForm.Id, pkValue, lockToken, out var lockTokenInfo))
                {
                    string warningMessage = null;

                    if (lockToken.IsNullOrEmptyOrWhiteSpace())
                    {
                        warningMessage =
                            $"{this.OperationContext.HtmlForm.Caption} {numberFld.DynamicProperty.GetValue(dataEntity)} 正在被 {lockTokenInfo.DisplayName} 编辑中，无法执行【{operationName}】";
                    }
                    else
                    {
                        warningMessage =
                            $"{this.OperationContext.HtmlForm.Caption} {numberFld.DynamicProperty.GetValue(dataEntity)} 拥有的锁过期，请重新进入编辑";
                    }

                    if (throwError)
                    {
                        throw new BusinessException(warningMessage);
                    }

                    this.OperationContext.Result.ComplexMessage.WarningMessages.Add(warningMessage);
                }

                //this.BillLockService.ExpiredLock(this.UserCtx, this.OperationContext.HtmlForm, pkValue, lockToken);

                filterDataEntities.Add(dataEntity);
            }

            dataEntities = filterDataEntities.ToArray();
        }

        /// <summary>
        /// 释放单据锁定
        /// </summary>
        /// <param name="dataEntities"></param>
        protected virtual void ReleaseBillLock(IEnumerable<DynamicObject> dataEntities)
        {
            if (dataEntities.IsNullOrEmpty())
            {
                return;
            }

            if (!EnableBillLock())
            {
                return;
            }

            foreach (var dataEntity in dataEntities)
            {
                string pkValue = Convert.ToString(dataEntity["id"]);

                this.BillLockService.ReleaseLock(this.UserCtx, this.OperationContext.HtmlForm.Id, pkValue);
            }
        }

        /// <summary>
        /// 开启单据锁定
        /// </summary>
        /// <returns></returns>
        protected bool EnableBillLock(bool ignoreOp = false)
        {
            // 是否人为操作：仅判断请求的接口，忽略后续的内部调用
            bool isUseBillLock = this.GetQueryOrSimpleParam<bool>("__UseBillLock__");
            // 系统管理员或集成账号不认为是人为操作
            if (this.UserCtx.UserId.EqualsIgnoreCase("sysadmin")        // 系统管理员
                || this.UserCtx.UserName.EqualsIgnoreCase("swjadmin")   // 三维家
                || this.UserCtx.UserName.EqualsIgnoreCase("musisync")   // 慕思中台
                )
            {
                isUseBillLock = false;
            }

            if (!isUseBillLock)
            {
                return false;
            }

            return this.BillLockService.EnableBillLock(this.UserCtx, this.OperationContext.HtmlForm,
                this.OperationContext.OperationNo, ignoreOp);
        }
    }
}