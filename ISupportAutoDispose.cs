using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 自动销毁对象接口定义
    /// </summary>
    public interface ISupportAutoDispose : IDisposable
    {
        /// <summary>
        /// 最后访问时间
        /// </summary>
        DateTime LastActiveTime { get; set; }

        /// <summary>
        /// 过期时间：必须>http过期时间
        /// </summary>
        TimeSpan? ExpiryIn { get; set; }

        /// <summary>
        /// 对象标识
        /// </summary>
        string Id { get; set; }

        /// <summary>
        /// 对象支持持久化
        /// </summary>
        bool SupportPersist { get; }
    }

    /// <summary>
    /// 全局对象管理器
    /// </summary>
    public interface IGlobalObjectManager<T>
        where T : class, ISupportAutoDispose
    {
        /// <summary>
        /// 申请一个全局对象，并增加有效期管理，过期释放
        /// </summary>
        /// <param name="objectId"></param>
        /// <param name="tag"></param>
        /// <param name="bAutoCreateWhenNotExists"></param>
        /// <param name="newInstance"></param>
        /// <returns></returns>
        T ApplyGlobalObject(string objectId, string tag, bool bAutoCreateWhenNotExists = false, Func<T> newInstance = null);

        /// <summary>
        /// 查找一个全局对象
        /// </summary>
        /// <param name="predicate"></param>
        /// <param name="dispObj"></param>
        /// <returns></returns>
        bool TryFindGlobalObject(Predicate<T> predicate, out T dispObj);

        /// <summary>
        /// 查找全局对象
        /// </summary>
        /// <param name="predicate"></param>
        /// <returns></returns>
        List<T> FindGlobalObject(Predicate<T> predicate);

        /// <summary>
        /// 人工摧毁某个全局对象
        /// </summary>
        /// <param name="objectId"></param>
        /// <param name="tag"></param>
        /// <param name="dispObject"></param>
        /// <returns></returns>
        bool DisposeGlobalObject(UserContext userContext, string objectId, string tag, out T dispObject);

        /// <summary>
        /// 人工摧毁某个全局对象
        /// </summary>
        /// <param name="predicate"></param>
        /// <returns></returns>
        void DisposeGlobalObject(Predicate<T> predicate);
    }
}
