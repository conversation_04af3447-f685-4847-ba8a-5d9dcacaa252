using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.BizTask;
using JieNor.Framework.Enums;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.BizTask
{
    /// <summary>
    /// 后台任务示例：员工生日提醒任务
    /// 后台任务开发要点：
    ///     1、TaskSvrIdAttribute 对应后台任务id，执行时根据这个获取对应的后台任务服务插件
    ///     2、所有后台服务插件继承自 AbstractTaskPlugin
    /// </summary>
    [InjectService(AliasName ="Task")]
    [TaskSvrIdAttribute("scrm.emp.birthday")]
    public   class EmpBirthdayTaskPlugin : AbstractTaskPlugin
    {

        public override IOperationResult DoExecute()
        {
            //
            IOperationResult result = new OperationResult();
            
            return result;
        }



    }


}
