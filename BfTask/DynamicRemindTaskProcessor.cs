using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.BfTask;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.FormMeta;
using Autofac.Features.Metadata;
using System.Text.RegularExpressions;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.Framework.AppService.BfTask
{
    /// <summary>
    /// 动态提醒任务处理器
    /// </summary>
    [InjectService]
    [Caption("动态提醒任务处理器")]
    [TaskProcessor("dynamicremind")]
    public class DynamicRemindTaskProcessor : AbstractTaskProcessor<DynamicRemindTaskParam>
    {
        /// <summary>
        /// 任务操作集合
        /// </summary>
        public override TaskOperation[] TaskOperations => new TaskOperation[]
        {
            //new TaskOperation(){ Id = "notremind", Caption = "不再提醒" },
            new TaskOperation(){ Id = "viewbill", Caption = "查看单据" }
        };

        /// <summary>
        /// 处理任务逻辑
        /// </summary>
        /// <param name="taskOpCode"></param>
        /// <param name="taskParam"></param>
        protected override void OnProcessTask(string taskOpCode, DynamicRemindTaskParam taskParam)
        {
            if (taskOpCode.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"任务操作码 taskOpCode 为空，请检查！");
            }
            if (taskParam == null
                || taskParam.FormId.IsNullOrEmptyOrWhiteSpace()
                || taskParam.DynRecordId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"动态提醒任务参数 formId 或 dynRecordId 为空，请检查！");
            }

            var metaModelService = UserContext.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.UserContext, taskParam.FormId);
            var args = new OnDynamicRemindProcessArgs
            {
                DynRecordId = taskParam.DynRecordId,
                OpCode = taskOpCode,
            };
            var plugIn = getPlugIn(UserContext, htmlForm);

            OnProcessTask(plugIn, args);

            if (args.IsLoadedAction && args.Actions != null && args.Actions.Count>0)
            {
                args.Actions.ForEach(x => this.Result.HtmlActions.Add(x));
            }

            if (args.Cancel)
            {
                return;
            }

            var logServiceEx = this.UserContext.Container.GetService<ILogServiceEx>();
            var bizLogDt = logServiceEx.LoadFormLogType(this.UserContext, taskParam.FormId);
            var dm = this.UserContext.Container?.TryGetService<IDataManager>();
            dm.InitDbContext(this.UserContext, bizLogDt);
            var dynObj = dm.Select(taskParam.DynRecordId) as DynamicObject;
            bizLogDt = null;

            if (dynObj == null)
            {
                throw new BusinessException($"动态记录不存在或者已被删除！");
            }

            switch (taskOpCode.ToLower())
            {
                case "notremind":
                    dynObj["fnotremind"] = true;
                    dm.Save(dynObj);
                    this.Result.ComplexMessage.SuccessMessages.Add("设置成功，系统将不再给您发送关于该动态的提醒消息！");
                    this.Result.IsShowMessage = true;
                    this.Result.IsSuccess = true;
                    break;

                case "viewbill":
                    var metaService = this.UserContext.Container.GetService<IMetaModelService>();
                    var bizForm = metaService.LoadFormModel(this.UserContext, taskParam.FormId);
                    var bizObjId = dynObj["fbizobjid"] as string;
                    if (bizObjId.IsNullOrEmptyOrWhiteSpace())
                    {
                        throw new BusinessException($"动态记录关联【{bizForm.Caption}】不存在或者已被删除！");
                    }
                    dm.InitDbContext(this.UserContext, bizForm.GetDynamicObjectType(this.UserContext));
                    var bizObj = dm.Select(bizObjId) as DynamicObject;
                    if (bizObj == null)
                    {
                        throw new BusinessException($"动态记录关联【{bizForm.Caption}】{bizObjId}不存在或者已被删除！");
                    }
                    //打开动态对应业务对象编辑页面
                    var action = this.UserContext.ShowSpecialForm(bizForm,
                        bizObj,
                        false,
                        Guid.NewGuid().ToString(),
                        Enu_OpenStyle.Default,
                        Enu_DomainType.Bill);
                    this.Result.HtmlActions.Add(action);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 调用插件OnProcessTask事件
        /// </summary>
        /// <param name="plugIn"></param>
        /// <param name="args"></param>
        private void OnProcessTask(List<IDynamicRemindPlugIn> plugIn, OnDynamicRemindProcessArgs args)
        {
            if (plugIn == null || plugIn.Count <= 0)
            {
                return;
            }
            foreach(var p in plugIn)
            {
                p.OnProcessTask(args);
            }
        }

        /// <summary>
        /// 获取插件并初始化插件
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="htmlForm"></param>
        /// <returns></returns>
        private List<IDynamicRemindPlugIn> getPlugIn(UserContext userContext, HtmlForm htmlForm)
        {
            var plugIn = userContext.Container.GetService<IEnumerable<Meta<Lazy<IDynamicRemindPlugIn>>>>()
                            .Where(o => (o.Metadata.GetString("FormId").EqualsIgnoreCase(htmlForm.Id) ||
                                        o.Metadata.GetString("FormId").EqualsIgnoreCase("*") || 
                                        o.Metadata.GetString("FormId").StartsWith(htmlForm.Id + "|") ||
                                        o.Metadata.GetString("FormId").EndsWith("|" + htmlForm.Id) ||
                                        o.Metadata.GetString("FormId").IndexOf("|" + htmlForm.Id + "|") > 0))
                            .Select(o => o.Value.Value)
                            .ToList();

            if (plugIn != null && plugIn.Count > 0)
            {
                foreach (var p in plugIn)
                {
                    p.InitPlugIn(userContext, htmlForm);
                }
            }

            return plugIn;
        }
    }

    /// <summary>
    /// 动态提醒任务参数对象
    /// </summary>
    public class DynamicRemindTaskParam
    {
        /// <summary>
        /// 业务表单Id
        /// </summary>
        public string FormId { get; set; }

        /// <summary>
        /// 业务对象动态记录Id
        /// </summary>
        public string DynRecordId { get; set; }
    }
}