using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.BizTask;
using JieNor.Framework.Enums;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using JieNor.Framework.DataEntity.Tmpl;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore;
using JieNor.Framework.Interface.QueryBuilder;
using Newtonsoft.Json.Linq;
using Autofac.Features.Metadata;

namespace JieNor.Framework.Interface.BizTask
{
    /// <summary>
    /// 任务基类：所有其他后台任务，从这个基类继承实现
    /// </summary>
    public abstract class AbstractTaskPlugin : MarshalByRefObject, ITaskPlugin
    {

        /// <summary>
        /// 是否中断执行
        /// </summary>
        public bool Interrupted
        {
            get;
            set;
        }

        /// <summary>
        /// 标准操作上下文
        /// </summary> 
        public UserContext Ctx
        {
            get;
            set;
        }


        /// <summary>
        /// 任务信息 
        /// </summary>
        public BackgroundTaskInfo TaskInfo
        {
            get;
            set;
        }

        public string LogFileUrl
        {
            get;
            set;
        }

        public string LocalFileUrl
        {
            get;
            set;
        }


        /// <summary>
        /// 记录任务执行情况(执行步骤，进度，日志等等)
        /// </summary>
        public TaskRuntimeLogInfo LogInfo
        {
            get;
            set;
        } = new TaskRuntimeLogInfo();


        static string _cacheKey = "TaskExeProcess:{0}";

        public AbstractTaskPlugin()
        { 
        }


        /// <summary>
        /// 添加任务执行的明细日志
        /// </summary>
        /// <param name="message">日志信息</param>
        /// <param name="isSuccess">是否成功</param>
        ///  <param name="saveNow">是否立即保存</param>
        public void AddLogDetail(string message,bool isSuccess=true,bool saveNow=false)
        {
            if(LogInfo ==null )
            {
                return;
            }

            if (!message.IsNullOrEmptyOrWhiteSpace())
            {
                LogInfo.AddDetail(message, isSuccess);
            }
            if (saveNow)
            {
                SaveTaskRuntimeLog();
            }
        }


        /// <summary>
        /// 线上环境运行入口
        /// </summary>
        public   IOperationResult Execute(bool imp)
        {
            IOperationResult result = new OperationResult();

            LogInfo.BeginTime = DateTime.Now; 
            LogInfo.Caption = TaskInfo.Caption;
            LogInfo.TaskId = TaskInfo.Id;

            if (!CheckStatus(result, imp))
            {
                SaveTaskRuntimeLog();
                return result;
            }
           
            Interrupted = false;

            TaskInfo.RunStatus = (int)EnumTaskRunStatus.Running;
            TaskInfo.LastStartRuntime = DateTime.Now;
            TaskInfo.RunningTime += 1;
            //UpdateTaskStatus();

            UpdateTaskExeProcess(100, 0, "开始执行......");

            LogInfo.AddDetail("开始执行任务",true);
            SaveTaskRuntimeLog(); 
            try
            {

                var jobCommon = this.Ctx.Container.GetService<IEnumerable<Meta<ITaskPlugin>>>()
                        .FirstOrDefault(o => o.Metadata.GetString("AliasName").EqualsIgnoreCase("Task")
                                && o.Metadata.GetString("TaskSvrId").EqualsIgnoreCase("fw.common.task"));


                //if (jobCommon != null && jobCommon.Value != null)
                //{
                //    jobCommon.Value.TaskInfo = TaskInfo;
                //}


                result = DoExecute();

                SendMesg(result);

                TaskInfo.LastConten = "任务执行完毕";
                TaskInfo.LastSuccess = true;
                TaskInfo.SuccessTime += 1;

                LogInfo.Content = "任务执行完毕";
                LogInfo.IsSuccess = true;
                LogInfo.AddDetail("任务执行完毕",true);                 
            }
            catch (Exception ex)
            {
                TaskInfo.LastConten = "任务执行失败：{0}".Fmt(ex.Message);
                TaskInfo.TraceConten = ex.StackTrace;
                TaskInfo.LastSuccess = false;

                LogInfo.IsSuccess = false;
                LogInfo.Content = "任务执行失败：{0}".Fmt(ex.Message);                
                LogInfo.TraceContent = ex.StackTrace;

                result.IsSuccess = false;
                result.SimpleMessage = "任务执行失败：{0} {1}".Fmt(ex.Message,ex.StackTrace);
            }
            finally
            {
                Interrupted = false;
                

                //更新任务状态
                TaskInfo.RunStatus = (int)EnumTaskRunStatus.Ready;
                TaskInfo.LastEndRuntime = DateTime.Now;                 
                UpdateTaskStatus();
                 
                //记录日志
                LogInfo.EndTime = DateTime.Now;
                SaveTaskRuntimeLog();

                //更新任务执行进度
                UpdateTaskExeProcessWhenFinish(); 
            }

            return result;
        }


        private bool CheckStatus(IOperationResult result,bool imp)
        {
            if (!imp)
            {
                if (TaskInfo == null || TaskInfo.Status == (int)EnumTaskStatus.Stop)
                {
                    result.IsSuccess = false;
                    result.SimpleMessage = "任务未启动，无法执行";

                    LogInfo.IsSuccess = false;
                    LogInfo.Content = "任务未启动，无法执行"; 
                    return false ;
                }
            }


            if (TaskInfo.Sigletion && TaskInfo.RunStatus == (int)EnumTaskRunStatus.Running)
            {
                result.IsSuccess = false;
                result.SimpleMessage = "任务已经在执行中";

                LogInfo.IsSuccess = false;
                LogInfo.Content = "任务未启动，无法执行"; 
                return false ;
            }

            return true;
        }

        public virtual void SendMesg(IOperationResult result)
        {
            if (result.SrvData.IsNullOrEmptyOrWhiteSpace() || !result.SimpleData.ContainsKey("taskId") || result.SimpleData["taskId"].IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            List<DynamicObject> data = result.SrvData as List<DynamicObject>;
            var meta = this.Ctx.Container.GetService<IMetaModelService>().LoadFormModel(this.Ctx, TaskConst.HtmlForm_TaskInfo);
            var dm = this.Ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Ctx, meta.GetDynamicObjectType(this.Ctx));
            Ctx.UpdateMdlSchema(meta.Id);
            var dy = dm.Select(result.SimpleData["taskId"]) as DynamicObject;
            if (dy.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            //List<DynamicObject> data= GetTaskCondition(this.Ctx, oid);
            
            var TmplService = this.Ctx.Container.GetService<IRichTxtTmplService>();
            List<RichTxtTmplInfo> ParseInfo = TmplService.GetRichTxtTmplList(this.Ctx, TaskConst.HtmlForm_TaskInfo, data, dy["fworkobject"].ToString());
            if (Convert.ToBoolean(dy["femail"]))   //发送邮件
            {
                var metaStaff = Ctx.Container.GetService<IMetaModelService>().LoadFormModel(Ctx, "sec_user");
                var dmStaff = Ctx.Container.GetService<IDataManager>();
                dmStaff.InitDbContext(Ctx, metaStaff.GetDynamicObjectType(Ctx));
                Ctx.UpdateMdlSchema(metaStaff.Id);
                foreach (var item in ParseInfo)
                {
                    var OpRichTxts = item.OpRichTxts.Where(s => s.OperationNo == TaskInfo.Id).FirstOrDefault();
                    if (OpRichTxts.IsNullOrEmptyOrWhiteSpace())
                    {
                        continue;
                    }
                    string ids = string.Join(",", OpRichTxts.Users.ToArray());
                    List<DynamicObject> staffData = dmStaff.Select(OpRichTxts.Users).OfType<DynamicObject>().ToList();
                    List<string> mail = new List<string>();
                    foreach (var staff in staffData)
                    {
                        if (!staff["femail"].IsNullOrEmptyOrWhiteSpace())
                        {
                            mail.Add(staff["femail"].ToString());
                        }

                    }
                    if (mail.Count <= 0)
                    {
                        continue;
                    }
                    var smtpSrv = this.Ctx.Container.GetService<ISmtpService>();
                    smtpSrv
                        .Reset()
                        .To(string.Join(",", mail.ToArray()))
                        .SubjectEncoding(Encoding.UTF8)
                        .Subject(OpRichTxts.Title)
                        .IsBodyHtml(true)
                        .BodyEncoding(Encoding.UTF8)
                        .Body(OpRichTxts.Body)
                        .Send();
                }

            }
            if (Convert.ToBoolean(dy["fsysmessage"]))//发送系统消息
            {
                var svc = this.Ctx.Container.GetService<ITaskMessageService>();
                svc.send(this.Ctx, data.Select(f => f["Id"]?.ToString()), this.TaskInfo.WorkObject, new string[] { "query" }, "2", this.TaskInfo.Id, "", "2", "bas_task");

            }
        }


        /// <summary>
        /// 执行任务
        /// </summary>
        public abstract IOperationResult DoExecute();




        /// <summary>
        /// 更新任务的最后一次执行情况及相关状态
        /// </summary>
        public virtual void UpdateTaskStatus()
        {
            if (TaskInfo == null)
            {
                return;
            }

            Task task = new Task(() =>
            {
                var meta = HtmlParser.LoadFormMetaFromCache(TaskConst.HtmlForm_TaskInfo, this.Ctx);
                var dm = this.Ctx.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Ctx, meta.GetDynamicObjectType(Ctx));
                Ctx.UpdateMdlSchema(meta.Id);
                //DynamicObject data = null;
                //由于预警模型在菜单点击打开的时候就已经把所有的模型都写进数据库了，所以不存在从模型读取的情况
                //if (TaskInfo.TaskSource == 0)
                //{
                //    data = this.TaskInfo.ToDynamicObject(Ctx,null);
                //}
                //else 
                if (TaskInfo.TaskSource == 1)
                {
                    var exist = dm.Select(TaskInfo.Id) as DynamicObject;
                    if (exist.IsNullOrEmptyOrWhiteSpace())
                    {
                        return;
                    }
                    //data = this.TaskInfo.ToDynamicObject(Ctx, exist);
                    exist["fexecutecount"] = Convert.ToInt32(exist["fexecutecount"]) + TaskInfo.SuccessTime;
                    exist["ffinallydate"] = TaskInfo.LastEndRuntime;
                    exist["ferrorcount"] = TaskInfo.RunningTime - TaskInfo.SuccessTime;
                    dm.Save(exist, null, OperateOption.InstanceBulkCopyAndNoCache);
                }
            });
            task.Start();
        }




        /// <summary>
        /// 保存任务执行日志
        /// </summary>
        public virtual void  SaveTaskRuntimeLog()
        {
            try
            {
                var meta = HtmlParser.LoadFormMetaFromCache(TaskConst.HtmlForm_TaskLogInfo, this.Ctx);
                var dm = this.Ctx.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Ctx, meta.GetDynamicObjectType(Ctx));
                Ctx.UpdateMdlSchema(meta.Id);

                var data = dm.Select(this.LogInfo.Id) as DynamicObject;
                if (data == null)
                {
                    data = meta.GetDynamicObjectType(Ctx).CreateInstance() as DynamicObject;
                    data["Id"] = LogInfo.Id;
                    if (LogInfo.Content != null)
                    {
                        data["fmsg"] = LogInfo.Content.Length > 2000 ? LogInfo.Content.Substring(0, 2000) : LogInfo.Content;
                    }
                    if (LogInfo.BeginTime > DateTime.MinValue)
                    {
                        data["fbegindate"] = LogInfo.BeginTime;
                    } 
                    if (LogInfo.EndTime > DateTime.MinValue)
                    {
                        data["fenddate"] = LogInfo.EndTime;
                    }
                    data["fcreatedate"] = DateTime.Now;
                    data["flogname"] = LogInfo.Caption;
                    data["ftaskid"] = LogInfo.TaskId;
                }

                DynamicObjectCollection entity = data["fentity"] as DynamicObjectCollection;
                foreach (var item in LogInfo.Details)
                {
                    if (entity.Any(f => (f["fdescribe_e"]?.ToString()).EqualsIgnoreCase(item.Content)))
                    {
                        continue;
                    }
                    if (LogInfo.EndTime > DateTime.MinValue)
                    {
                        if (LogInfo.Content != null)
                        {
                            data["fmsg"] = LogInfo.Content.Length > 2000 ? LogInfo.Content.Substring(0, 2000) : LogInfo.Content;
                        }
                        data["fenddate"] = LogInfo.EndTime;
                    }
                    
                    DynamicObject dy = entity.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                    dy["Id"] = Guid.NewGuid ().ToString ();
                    if (item.Content != null)
                    {
                        dy["fdescribe_e"] = item.Content.Length > 2000 ? item.Content.Substring(0, 2000) : item.Content;
                    }

                    if (item.BeginTime > DateTime.MinValue)
                    {
                        dy["ftasktime"] = item.BeginTime;
                    }                    
                    dy["fexecutestate"] = item.IsSuccess == true ? "ew_exstart001" : "ew_exstart002";
                    entity.Add(dy);
                }

                var defCalulator = this.Ctx.Container.GetService<IDefaultValueCalculator>();
                defCalulator.Execute(this.Ctx, meta, new DynamicObject[] { data });
                var seqSrv = this.Ctx.Container.GetService<IDataEntityPkService>();
                seqSrv.AutoSetPrimaryKey(this.Ctx, data,
                meta.GetDynamicObjectType(this.Ctx));//赋主键      
                var option = OperateOption.InstanceBulkCopyAndNoCache;
                option.SetVariableValue ("IgnoreWaring", true);
                option.SetVariableValue("IgnoreCheckPermssion", true);
                dm.Save(data,null , option); 
            }
            catch (Exception ex)
            {
                //System.Diagnostics.Debug.Assert(false, ex.Message + ex.StackTrace);
            } 
        }


        /// <summary>
        /// 资源释放
        /// </summary>
        public virtual void Dispose()
        {

        }



        /// <summary>
        /// 更新任务的执行进度：进度条显示用，任务的执行进度=当前已经完成的任务数 / 总任务数
        /// </summary>
        /// <param name="totalCount">总任务数</param>
        /// <param name="finishCount">当前已经完成的任务数</param>
        /// <param name="tips">提示信息</param>
        public void UpdateTaskExeProcess(int totalCount, int finishCount, string tips)
        {
            if (LogInfo == null || LogInfo.Id.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            if(finishCount >totalCount )
            {
                finishCount = totalCount;
            }
            var key = _cacheKey.Fmt(LogInfo.Id);
            bool isSuccess = true;
            if (LogInfo.Details.Count > 0)
            {
                var detail = LogInfo.Details.FirstOrDefault(f => !f.IsSuccess);
                isSuccess = detail.IsNullOrEmptyOrWhiteSpace() ? isSuccess : detail.IsSuccess;
            }
            var cache = this.Ctx.Container.GetService<IRedisCache>();
            TaskExeProcessInfo process = new TaskExeProcessInfo()
            {
                Id = LogInfo.Id,
                TotalCount = totalCount,
                FinishCount = finishCount,
                Tips = tips,
                IsSuccess = isSuccess
        };

            cache.Set(key, process.ToJson(), TimeSpan.FromDays(3));
        }

        
        /// <summary>
        /// 任务执行完成时更新执行进度
        /// </summary>
        private void UpdateTaskExeProcessWhenFinish()
        {
            if (LogInfo == null || LogInfo.Id.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }

            var key = _cacheKey.Fmt(LogInfo.Id);
            var cache = this.Ctx.Container.GetService<IRedisCache>();
            var str = cache.Get<string>(key);
            if(!str.IsNullOrEmptyOrWhiteSpace ())
            {
                bool isSuccess = true;
                if (LogInfo.Details.Count > 0)
                {
                    var detail = LogInfo.Details.FirstOrDefault(f => !f.IsSuccess);
                    isSuccess = detail.IsNullOrEmptyOrWhiteSpace() ? isSuccess : detail.IsSuccess;
                }
                TaskExeProcessInfo process = str.FromJson<TaskExeProcessInfo>();
                if (isSuccess)
                {
                    process.Tips = "【{0}】任务执行完毕，执行过程中无错误发生。".Fmt(TaskInfo.Caption);
                }
                else
                {
                    process.Tips = "【{0}】任务执行完毕，执行过程中有错误发生，具体查看相关日志".Fmt(TaskInfo.Caption);
                }
                process.FinishCount = process.TotalCount;
                process.IsFinish = true;
                process.LogFileUrl = LogFileUrl;
                process.IsSuccess = isSuccess;
                cache.Set(key, process.ToJson(), TimeSpan.FromDays(3));
            } 
        }


    }


}
