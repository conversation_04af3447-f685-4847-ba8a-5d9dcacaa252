using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.BizTask;
using JieNor.Framework.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.Framework.Interface.BizTask
{

     




    /// <summary>
    /// 任务服务接口
    /// </summary>
    public interface ITaskService
    {
        /// <summary>
        /// 清除缓存
        /// </summary>
        /// <param name="ctx"></param>
        void ClearCache(UserContext ctx);


        /// <summary>
        /// 获取所有的后台任务列表
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <returns></returns>
        List<BackgroundTaskInfo> GetAllTaskItems(UserContext ctx);


        /// <summary>
        /// 获取所有的后台任务列表
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="status">任务状态</param>
        /// <returns></returns>
        List<BackgroundTaskInfo> GetAllTaskItems(UserContext ctx,EnumTaskStatus status);


        /// <summary>
        /// 获取所有的后台任务列表
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="status">任务的执行状态</param>
        /// <returns></returns>
        List<BackgroundTaskInfo> GetAllTaskItems(UserContext ctx, EnumTaskRunStatus status);


        /// <summary>
        /// 任务调度：将数据库中状态为启动的任务，加入到任务调度中
        /// </summary>
        /// <param name="ctx">上下文</param> 
        /// <returns></returns>
        IOperationResult StartSchedule(UserContext ctx);

        /// <summary>
        /// 启用任务：将未启动的任务，加入任务调度中，并设置任务的启动状态
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="taskId">任务Id</param>
        /// <returns></returns>
        IOperationResult StartTask(UserContext ctx, string  taskId);

        /// <summary>
        /// 启用任务：将未启动的任务，加入任务调度中，并设置任务的启动状态
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="taskId">任务Id</param>
        /// <returns></returns>
        IOperationResult StartTask(UserContext ctx, IEnumerable< string> taskId);

        /// <summary>
        /// 启用任务：将未启动的任务，加入任务调度中，并设置任务的启动状态
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="task">任务信息</param>
        /// <returns></returns>
        IOperationResult StartTask(UserContext ctx , BackgroundTaskInfo task);

        /// <summary>
        /// 启用任务：将未启动的任务，加入任务调度中，并设置任务的启动状态
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="task">任务信息</param>
        /// <returns></returns>
        IOperationResult StartTask(UserContext ctx,IEnumerable < BackgroundTaskInfo> task);


        /// <summary>
        /// 停用任务：将已启动的任务停掉，并从任务调度中取消调度，并设置任务为 停用状态
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="taskId">任务Id</param>
        /// <returns></returns>
        IOperationResult StopTask(UserContext ctx, string taskId);

        /// <summary>
        /// 停用任务：将已启动的任务停掉，并从任务调度中取消调度，并设置任务为 停用状态
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="taskId">任务Id</param>
        /// <returns></returns>
        IOperationResult StopTask(UserContext ctx, IEnumerable<string> taskId);

        /// <summary>
        /// 停用任务：将已启动的任务停掉，并从任务调度中取消调度，并设置任务为 停用状态
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="task">任务信息</param>
        /// <returns></returns>
        IOperationResult StopTask(UserContext ctx, BackgroundTaskInfo task);

        /// <summary>
        /// 停用任务：将已启动的任务停掉，并从任务调度中取消调度，并设置任务为 停用状态
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="task">任务信息</param>
        /// <returns></returns>
        IOperationResult StopTask(UserContext ctx, IEnumerable<BackgroundTaskInfo> task);






        /// <summary>
        /// 执行任务
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="taskId">任务Id</param>
        /// <returns></returns>
        IOperationResult RunTask(UserContext ctx, string taskId);

        /// <summary>
        /// 执行任务
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="taskId">任务Id</param>
        /// <returns></returns>
        IOperationResult RunTask(UserContext ctx, IEnumerable<string> taskId);

        /// <summary>
        /// 执行任务
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="task">任务信息</param>
        /// <returns></returns>
        IOperationResult RunTask(UserContext ctx, BackgroundTaskInfo task);


        /// <summary>
        /// 执行任务
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="task">任务信息</param>
        /// <returns></returns>
        IOperationResult RunTask(UserContext ctx, IEnumerable<BackgroundTaskInfo> task);


        /// <summary>
        /// 任务中断执行
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="taskId">任务Id</param>
        /// <returns></returns>
        IOperationResult InterrupteTask(UserContext ctx, string taskId);


        /// <summary>
        /// 任务中断执行
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="taskId">任务Id</param>
        /// <returns></returns>
        IOperationResult InterrupteTask(UserContext ctx, IEnumerable<string> taskId);

        /// <summary>
        /// 任务中断执行
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="task">任务信息</param>
        /// <returns></returns>
        IOperationResult InterrupteTask(UserContext ctx, BackgroundTaskInfo task);

        /// <summary>
        /// 任务中断执行
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="task">任务信息</param>
        /// <returns></returns>
        IOperationResult InterrupteTask(UserContext ctx, IEnumerable<BackgroundTaskInfo> task);



        /// <summary>
        /// 立即执行一个临时任务（常用于各个业务环节中执行某个耗时的操作，比如后台重算库存）
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="task">任务信息</param>
        /// <param name="isAsynch">是否异步执行</param>
        /// <returns></returns>
        IOperationResult RunTemporaryTask(UserContext ctx, BackgroundTaskInfo task,bool isAsynch=true);




        /// <summary>
        /// 获取某个任务的所有执行日志
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="taskId">任务Id</param>
        /// <returns></returns>
        IOperationResult GetTaskRunningLog(UserContext ctx, string taskId);



        /// <summary>
        /// 获取某个任务的某次执行日志
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="taskId">任务Id</param>
        /// <param name="patchId">执行的批次Id</param>
        /// <returns></returns>
        IOperationResult GetTaskRunningLog(UserContext ctx, string taskId, string patchId);


        /// <summary>
        /// 获取某个任务的当前执行进度
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="taskId">任务Id</param>
        /// <param name="patchId">执行的批次Id</param>
        /// <returns></returns>
        IOperationResult GetTaskRunningProgress(UserContext ctx, string taskId, string patchId);


        /// <summary>
        /// 返回表达式执行的频率
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="cronExpression">表达式</param>
        /// <returns></returns>
        List<string> GetTaskeFireTime(UserContext ctx,  string cronExpression, int numTimes = 20);

        


    }




}
