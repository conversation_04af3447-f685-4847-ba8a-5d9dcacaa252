using JieNor.Framework.DataTransferObject.IM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.IM
{


    /// <summary>
    /// 通信相关数据的存储服务(消息存储服务）
    /// </summary>
    public interface IMessageRepositoryService
    {

        /// <summary>
        /// 检查用户是否存在
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="userKey"></param>
        /// <returns></returns>
        bool ExistUser(UserContext userCtx, string userKey);

        /// <summary>
        /// 同步业务系统用户到IM用户
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="sysUser"></param>
        /// <returns></returns>
        void SynchroSystemUser(UserContext userCtx, IEnumerable<IMUser> sysUser);



        /// <summary>
        /// 从redis获取所有系统用户
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        IEnumerable<IMUser> GetAllIMUser(UserContext userCtx);


        /// <summary>
        /// 从redis获取所有聊天群组信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        IEnumerable<IMGroup> GetAllIMGroup(UserContext userCtx);

        /// <summary>
        /// 保存聊天群组信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="group"></param>
        void SaveIMGroup(UserContext userCtx, IMGroup group);


        /// <summary>
        /// 获取用户的聊天群组信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="userKey"></param>
        IEnumerable<IMGroup> GetUserIMGroup(UserContext userCtx, string userKey);


        /// <summary>
        /// 保存用户信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="user"></param>
        void SaveIMUser(UserContext userCtx, IMUser user);


        /// <summary>
        /// 客户端发送的消息，缓存到服务器
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="messages"></param>
        void SaveMessage(UserContext userCtx, List<IMMessage> messages);

        /// <summary>
        /// 获取消息对象
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="msgId"></param>
        /// <returns></returns>
        IMMessage GetMessage(UserContext userCtx, string msgId);

        /// <summary>
        /// 获取消息头对象
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="msgId"></param>
        /// <returns></returns>
        IMMessageHead GetMessageHead(UserContext userCtx, string msgId);

        /// <summary>
        /// 获取消息体对象
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="msgId"></param>
        /// <returns></returns>
        List<IMMessageBody> GetMessageBody(UserContext userCtx, string msgId);


        /// <summary>
        /// 获取用户的所有未读消息
        /// </summary> 
        /// <param name="userCtx"></param>
        /// <param name="userKey"></param>
        /// <returns></returns>
        List<IMMessageHead> GetUserUnReadMessage(UserContext userCtx, string userKey);

        /// <summary>
        /// 获取用户的所有已读消息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="userKey"></param>
        /// <returns></returns>
        List<IMMessageHead> GetUserReadMessage(UserContext userCtx, string userKey);

        /// <summary>
        /// 获取对应用户的所有消息
        /// </summary> 
        /// <param name="userCtx"></param>
        /// <param name="userKey"></param>
        /// <param name="qid"></param>
        /// <returns>所有未读消息</returns>
        List<IMMessageHead> GetUserAllMessage(UserContext userCtx, string userKey, string qid = "");


        /// <summary>
        /// 获取当前用户的系统广播消息
        /// </summary> 
        /// <returns></returns>
        List<IMMessageHead> GetBroadcastMessage(UserContext userCtx);

        /// <summary>
        /// 获取待处理的消息列表
        /// </summary>
        /// <returns></returns>
        List<IMMessageHead> GetUnProcessMessage(UserContext userCtx);

        /// <summary>
        /// 设置redis中缓存的消息的读取状态
        /// </summary> 
        /// <param name="userCtx"></param>
        /// <param name="msgId"></param>
        /// <param name="read"></param>
        void SetMessageReadStatus(UserContext userCtx, string msgId, bool read);

        /// <summary>
        /// 设置redis中缓存的消息的读取状态
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="msgIds"></param>
        /// <param name="read"></param>
        void SetMessageReadStatus(UserContext userCtx, IEnumerable<string> msgIds, bool read);

        /// <summary>
        /// 设置消息的处理进度
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="msgId"></param>
        /// <param name="status"></param>
        void SetMessageHandleStatus(UserContext userCtx, string msgId, IMMsgHandleStatus status);

        /// <summary>
        /// 批量设置消息的处理进度
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="msgId"></param>
        /// <param name="status"></param>
        void SetMessageHandleStatus(UserContext userCtx, IEnumerable<string> msgId, IMMsgHandleStatus status);



        /// <summary>
        /// 保存分组
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="category"></param>
        /// <param name="chatUserId"></param>  
        void SaveIMCategory(UserContext userCtx, string chatUserId, string category);

        /// <summary>
        /// 获取我的分组信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="chatUserId"></param>
        /// <returns></returns>
        string GetIMCategory(UserContext userCtx, string chatUserId);

        /// <summary>
        /// 保存我的好友信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="message"></param>  
        void SaveMyFriend(UserContext userCtx, IMMessage message);

        /// <summary>
        /// 获取好友列表
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="chatUserId"></param>
        /// <returns></returns>
        string GetMyIMFriend(UserContext userCtx, string chatUserId);

    }






}
