using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.Enums;
using System;
using System.Collections.Generic;

namespace JieNor.Framework.Interface.IM
{


    /// <summary>
    /// 即时通信的用户获取服务接口
    /// </summary>
    public interface IIMAddressService
    {

        /// <summary>
        /// 获取所有用户
        /// </summary> 
        /// <param name="userCtx"></param>
        /// <returns></returns>
        IEnumerable<IMUser> GetAllIMUser(UserContext userCtx);

        /// <summary>
        /// 获取所有朋友
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        IEnumerable<IMMyFrineds> GetAllFrineds(UserContext userCtx);

        /// <summary>
        /// 获取所有组
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        IEnumerable<IMGroup> GetAllGroup(UserContext userCtx);
    }





}
