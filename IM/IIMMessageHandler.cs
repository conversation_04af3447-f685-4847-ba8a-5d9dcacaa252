using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.DataTransferObject.Poco;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.IM
{
    /// <summary>
    /// 消息消费者
    /// </summary>
    public interface IIMMessageHandler
    {
        /// <summary>
        /// 插件优先级，注意如果同一个消息有多个相同的消息处理插件时，
        /// 消息处理总线只会调用优先级最高的消息处理插件进行处理。
        /// 消息的默认优先级为1
        /// </summary>
        int Priority { get; }

        /// <summary>
        /// 初始化消息消费参数
        /// </summary>
        /// <param name="option"></param>
        void InitializeOption(MessageHandlerOption option);

        /// <summary>
        /// 以指定上下文处理特定消息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="message"></param>
        /// <param name="result"></param>
        void ProcessMessage(UserContext userCtx, IMMessage message, MessageHandlerResult result);
    }

    /// <summary>
    /// 消息处理选项
    /// </summary>
    public class MessageHandlerOption
    {
        /// <summary>
        /// 消息处理确认方式：0-自动确认，1-手动确认
        /// </summary>
        public int AckMode { get; set; }

        /// <summary>
        /// 消息批量大小
        /// </summary>
        public int BatchSize { get; set; }

        /// <summary>
        /// 处理失败重新入队
        /// </summary>
        public bool RequeueWhenFailed { get; set; }

        /// <summary>
        /// 重新入队阀值
        /// </summary>
        public int RequeueThreshold { get; set; }

        /// <summary>
        /// 死信队列名称
        /// </summary>
        public string DeadQueueName { get; set; }


    }

    /// <summary>
    /// 消息处理结果
    /// </summary>
    public class MessageHandlerResult
    {
        /// <summary>
        /// 是否手动确认
        /// </summary>
        public bool Ack { get; set; }

        /// <summary>
        /// 消息处理结果
        /// </summary>
        public IOperationResult OperationResult { get; set; }
    }
}
