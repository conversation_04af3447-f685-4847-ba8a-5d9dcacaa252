using JieNor.Framework.DataTransferObject.IM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.IM
{
    /// <summary>
    /// 通讯用户管理相关代理服务接口定义
    /// </summary>
    public interface IUserServiceProxy
    {

        void InitHubContext(UserContext userCtx, object hub);

        #region IM  User


        /// <summary>
        /// 同步业务系统用户到IM用户
        /// </summary>
        /// <param name="sysUsers"></param>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        void SynchroSystemUser(UserContext userCtx, IEnumerable<IMUser> sysUsers);



        /// <summary>
        /// 更改用户头像
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="userKey"></param>
        /// <param name="url"></param>
        void ChangeIMUserGravatar(UserContext userCtx, string userKey, string url);


        /// <summary>
        /// 设置用户状态：正常还是禁用
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="userKey"></param>
        /// <param name="active">true ：正常；false ：禁用</param>
        /// <param name="reason"></param>
        void SetIMUserForbitSatus(UserContext userCtx, string userKey, bool active, string reason);




        /// <summary>
        ///  获取所有系统用户信息 
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        string GetIMUserlist(UserContext userCtx);


        /// <summary>
        ///  获取在线用户列表
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        string GetOnlineIMUserlist(UserContext userCtx);



        /// <summary>
        /// 设置用户的在线状态，并通知客户端更新用户的状态
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="userKey"></param>
        /// <param name="status"></param>
        void SetIMUserStatus(UserContext userCtx, string userKey, IMUserStatus status);




        #endregion IM  User
    }
}
