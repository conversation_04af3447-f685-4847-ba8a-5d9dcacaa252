//using JieNor.Framework.DataTransferObject.IM;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace JieNor.Framework.Interface.IM
//{



//    /// <summary>
//    /// IM代理实现：模拟一个客户端进行连接后委托调用
//    /// </summary>
//    public interface IIMClientProxy
//    {
//        #region IM MyFriend

//        /// <summary>
//        /// 同意添加好友、同意加入群
//        /// </summary>
//        /// <param name="msg"></param>
//        /// <param name="agree"></param>
//        /// <returns></returns>
//        Task AgreeChatFriend(string msg,bool agree);


//        /// <summary>
//        /// 获取好友列表
//        /// </summary>
//        /// <param name="chatUserId"></param>
//        /// <returns></returns>
//        Task<string> GetMyIMFriend(string chatUserId);


//        #endregion IM MyFriend


//        #region IM Category

//        /// <summary>
//        /// 创建分组
//        /// </summary>
//        /// <param name="category">分组名称</param>
//        /// <param name="chatUserId"></param>
//        /// <returns></returns>
//        Task CreateIMCategory(string chatUserId, string category);

//        /// <summary>
//        /// 获取我的分组
//        /// </summary> 
//        /// <returns></returns>
//        Task<string> GetIMCategory(string chatUserId);



//        #endregion





//        #region IM  Group


//        /// <summary>
//        /// 创建聊天群组
//        /// </summary>
//        /// <param name="jsonGroups">IMGRoup </param> 
//        Task CreatIMGroup(string jsonGroups);


//        Task<IMGroup> CreateSignGroup(string sender, string reciver);


//        /// <summary>
//        /// 邀请某人加入聊天群组
//        /// </summary>
//        /// <param name="userKey">被邀请用户标识</param>
//        /// <param name="grpKey">群组标识</param>
//        Task InviteToIMGroup(string userKey, string grpKey);


//        /// <summary>
//        /// 申请加入聊天群组
//        /// </summary> 
//        /// <param name="grpKey">群组标识</param>
//        Task JoinToIMGroup(string grpKey);

//        /// <summary>
//        /// 设置用户在聊天群组里面的昵称
//        /// </summary> 
//        /// <param name="grpKey">群组标识</param>
//        Task SetNickInIMGroup(string grpKey,string userKey,string nick);



//        /// <summary>
//        /// 将某人踢出聊天群组
//        /// </summary>
//        /// <param name="beRemoveUserKey">即将被移除的用户key</param>
//        /// <param name="groupKey">聊天群组Key</param>
//        Task KickFromIMGroup(string beRemoveUserKey, string groupKey, string reason);

//        /// <summary>
//        /// 退出聊天群组(自己主动退出)
//        /// </summary> 
//        /// <param name="groupKey">聊天群组Key</param>
//        Task LeaveFromIMGroup(string groupKey, string reason);


//        /// <summary>
//        /// 更新聊天群主题（公告）
//        /// </summary>
//        /// <param name="groupKey"></param>
//        /// <param name="topic"></param>
//        Task SetIMGroupTopic(string groupKey, string topic);

//        /// <summary>
//        /// 更新聊天群的欢迎词
//        /// </summary>
//        /// <param name="groupKey"></param>
//        /// <param name="welcome"></param>
//        Task SetIMGroupWelcome(string groupKey, string welcome);


//        /// <summary>
//        /// 设置群成员的群管理员属性
//        /// </summary>
//        /// <param name="groupKey"></param>
//        /// <param name="userKey">用户标识，多个用分号分隔开</param>
//        Task SetIMGroupAdmin(string groupKey, string userKey,bool isAdmin);




//        /// <summary>
//        /// 允许、拒绝别人的邀请
//        /// </summary> 
//        ///<param name="msgId">消息标识</param> 
//        /// <param name="agree" > 是否同意</param>
//        Task AgreeJoinIMGroup(string msgId, bool agree);


//        /// <summary>
//        /// 允许某人加入聊天群（通常是管理员允许）
//        /// </summary> 
//        /// <param name="msgId">消息标识</param> 
//        /// <param name="agree" > 是否同意</param>
//        ///  <param name="never">是否永久拒绝该人加入</param>
//        Task AllowJoinIMGroup(string msgId,  bool agree,bool never);


//        /// <summary>
//        /// 是否禁止某些神仙在群里发言
//        /// </summary>
//        /// <param name="groupKey"></param>
//        /// <param name="userKey">多个用户分号分开</param>
//        ///  <param name="isBan">是否禁言</param>
//        /// <param name="reason"></param>
//        Task SetBanInIMGroup(string groupKey, string userKey, bool isBan, string reason);



//        /// <summary>
//        /// 检查是否被禁言
//        /// </summary>
//        /// <param name="groupKey"></param>
//        /// <param name="userKey"></param>
//        Task CheckBannedInIMGroup(string groupKey, string userKey);

//        /// <summary>
//        /// 设置聊天群的锁定状态
//        /// </summary>
//        /// <param name="groupKey"></param> 
//        Task SetIMGroupLockStatus(string groupKey, bool isLock, string reason);



//        /// <summary>
//        /// 解散聊天群
//        /// </summary>
//        /// <param name="groupKey"></param>
//        Task DissolveIMGroup(string groupKey,string reason);


//        /// <summary>
//        /// 获取当前登录用户的所属聊天群组信息 
//        /// </summary>
//        Task<string> GetCurrUserIMGroupList();

//        /// <summary>
//        /// 获取当前登录用户的所属聊天群组信息 
//        /// </summary>
//        Task<string > GetAllIMGroupList();





//        #endregion IM  Group




//        #region IM  User



//        /// <summary>
//        /// 同步业务系统用户到IM用户
//        /// </summary>
//        /// <returns></returns>
//        Task SynchroSystemUser(string jsonUsers);


//        /// <summary>
//        /// 创建聊天用户
//        /// </summary>
//        /// <param name="jsonUser">IMUser</param> 
//        Task CreatIMUser(string jsonUser );



//        /// <summary>
//        /// 更改用户头像
//        /// </summary>
//        /// <param name="userKey"></param>
//        /// <param name="url"></param>
//        Task ChangeIMUserGravatar(string userKey, string url);


//        /// <summary>
//        /// 设置用户状态：正常还是禁用
//        /// </summary>
//        /// <param name="userKey"></param>
//        /// <param name="active">true ：正常；false ：禁用</param>
//        /// <param name="reason"></param>
//        Task SetIMUserForbitSatus(string userKey, bool active, string reason);




//        /// <summary>
//        ///  获取所有系统用户信息 
//        /// </summary>
//        /// <returns></returns>
//        Task< string> GetIMUserlist();


//        /// <summary>
//        ///  获取在线用户列表
//        /// </summary>
//        /// <returns></returns>
//        Task< string> GetOnlineIMUserlist();



//        /// <summary>
//        /// 设置用户的在线状态，并通知客户端更新用户的状态
//        /// </summary>
//        /// <param name="status"></param>
//        Task SetIMUserStatus(string userKey, IMUserStatus status);




//        #endregion IM  User



//        #region IM  Message

//        /// <summary>
//        /// 发送消息（发送方调用）：依据消息的RecGroupKey 和 RecUserKes 判断
//        /// 1、是否系统广播，如果是则发送系统广播消息
//        /// 2、是否单聊，如果是则创建聊天组，点对点发送消息
//        /// 3、群聊，则直接发送消息  
//        /// </summary> 
//        /// <param name="jsonMsgs">消息</param>  
//        Task SendMessage(string jsonMsgs);





//        /// <summary>
//        /// 设置消息已读状态
//        /// </summary>
//        /// <param name="msgId">消息id</param>
//        /// <param name="read">true ：已读 ，false ：未读</param>
//        Task SetMessageReadStatus(string msgId, bool read);

//        /// <summary>
//        /// 设置消息的处理状态
//        /// </summary>
//        /// <param name="msgId">消息id</param>
//        /// <param name="status">消息处理状态</param>
//        Task SetMessageProcessStatus(string msgId, IMMsgHandleStatus status);


//        /// <summary>
//        /// 获取某条消息
//        /// </summary> 
//        Task< string >  GetMessage(string msgId);




//        /// <summary>
//        /// 获取当前用户的未读信息，并推送给客户端
//        /// </summary> 
//        Task   GetUnReadMessage();



//        /// <summary>
//        /// 获取当前用户的所有信息，并推送给客户端
//        /// </summary> 
//        Task GetAllMessage();



//        /// <summary>
//        /// 获取当前用户的系统广播消息
//        /// </summary> 
//        Task GetSysBaroadCastMsg();









//        #endregion IM  Message



//    }






//}
