using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;
using System.Dynamic;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.DataTransferObject.Report;
using Newtonsoft.Json;

namespace JieNor.Framework.AppService.ReportService
{


    /// <summary>
    /// 获取系统模块的业务对象信息 
    /// </summary>
    [InjectService]
    [FormId("bas_officetmpl")]
    [OperationNo("LoadBizObjectList")]
    public class LoadBizObjectList : AbstractOperationServicePlugIn
    {


        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            var sql = @"select distinct c.fname as fmodule, a.fname,a.fbillformid as fsrcformid
                        from T_SYS_MENUITEM a
                        inner join T_SYS_MENUGROUP b on a.fgroupid=b.fgroupid
                        inner join T_SYS_BIZMODULE c on b.fmoduleid=c.fmoduleid
                        where a.fdomaintype='list'";
            //var sql = @"select distinct b.fname as fmodule,a.fname,a.fbillformid as fsrcformid 
            //            from T_SYS_MENUGROUP a 
            //            inner join T_SYS_BIZMODULE b on a.fmoduleid=b.fmoduleid";

            List<OfficeTemplateInfo> tmpls = new List<OfficeTemplateInfo>();
            using (var reader = this.DBService.ExecuteReader(this.Context,  sql))
            {
                while (reader.Read())
                {
                    if(reader["fsrcformid"].IsNullOrEmptyOrWhiteSpace  () )
                    {
                        continue;
                    }
                    OfficeTemplateInfo tmpl = new OfficeTemplateInfo();
                    tmpl.FId = reader.GetValue<string>("fsrcformid");                    
                    tmpl.FName = reader.GetValue<string>("fname");
                    tmpl.FNumber = reader.GetValue<string>("fname");
                    tmpl.FSrcFormId = reader.GetValue<string>("FSrcFormId");
                    tmpl.FQuerySrv = reader.GetValue<string>("FSrcFormId");
                    tmpl.FModule = reader.GetValue<string>("fmodule");
                    tmpl.FDescription = reader.GetValue<string >("fname");
                    tmpl.FFileType = "xlsx";
                    tmpl.FTmplType  = "list";
                    tmpl.FOption = "[]";
                    tmpls.Add(tmpl); 
                }
            }

            this.Result.SrvData = JsonConvert.SerializeObject(tmpls);
        }

        



    }


     




     

}
