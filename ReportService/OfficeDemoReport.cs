using JieNor.Framework.AppService.QueryBuilder;
using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace JieNor.Framework.AppService.ReportService
{

    /// <summary>
    /// Office报表示例.
    /// Excel报表开发步骤：
    /// 1、定义报表模型：在Excel报表模版中新增报表模型，定义报表字段等信息，
    ///    注意 [取数服务] 字段要设置，这个字段填写 服务的操作码，比如这个示例，则填写 为 OfficeDemoReport
    /// 2、定义过滤界面，未定义则用通用过滤界面
    /// 3、编写服务端取数服务：继承基类服务 OfficeRptDataService
    ///     A、如果返回的报表标题是动态的，请 override  --  DataTitle ， 具体参照本示例  
    ///     B、如果报表数据显示界面需要显示过滤条件信息，请 override  --  FilterTitle ， 具体参照本示例
    ///     C、如果返回的报表字段是动态的（报表模型里面未定义），请override  --   Columns ， 具体参照本示例
    ///     D、最最重要的部分：重写报表取数服务函数 BuildQueryData ，返回报表数据，具体参照本示例
    /// </summary> 
    [InjectService("OfficeDemoReport")]
    public     class OfficeDemoReport : OfficeRptDataService
    {



         /// <summary>
         /// 在这里返回报表的标题，比如：销售毛利表
         /// </summary>
        protected override string DataTitle
        {
            get
            {
                return "科目余额表(Demo)";
            }
            set
            {

            }
        }


        /// <summary>
        /// 在这里返回相关的过滤条件描述信息。
        /// 一般情况下，报表的数据界面上方，都会显示一些简要的查询条件信息，方便用户查看
        /// </summary>
        public override Dictionary<string, string> FilterTitle
        {
            get
            {
                var desc = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
                desc.Add("组织", "杰诺科技");
                desc.Add("科目", "所有科目");
                desc.Add("月份", "2017-02");

                return desc;
            }
        }



        /// <summary>
        /// 返回报表查询字段：默认返回报表模型里面定义的字段列表。
        /// 1、对于动态的报表（字段动态），请在这个方法里面构建
        /// 2、对于多行表头，注意构建字段顺序，否则出错
        /// </summary>
        /// <returns></returns>
        protected override ColumnHeadObject BuildColumnHeads()
        {
            ColumnHeadObject rptHead = new ColumnHeadObject();
            rptHead.Caption = this.DataTitle ;

            //动态增加字段 
            var col = new ColumnObject("FAccountName", "科目", HtmlElementType.HtmlField_TextField);
            rptHead.AddSubHead(col);
            col = new ColumnObject("FYear", "年度", HtmlElementType.HtmlField_IntegerField );
            rptHead.AddSubHead(col);
            col = new ColumnObject("FPeriod", "期间", HtmlElementType.HtmlField_TextField);
            rptHead.AddSubHead(col);
            col = new ColumnObject("FCyName", "币别", HtmlElementType.HtmlField_TextField);
            rptHead.AddSubHead(col);

            BuildAmountHead(rptHead);

            BuildQtyHead(rptHead);
             
            return rptHead;
        }



        /// <summary>
        /// 构建返回的报表模型信息：默认返回报表模型里面定义的信息
        /// 对于动态的报表（字段动态），请在这个方法里面构建动态构建
        /// </summary>
        /// <returns></returns>
        protected override RptTemplateInfo GetRptTemplateInfo()
        {
            var tmpl = base.GetRptTemplateInfo();
            tmpl.col.Clear();
            tmpl.chart.Clear();
            foreach (var item in ColumnHead.AllColumns)
            {
                RptColInfo col = new RptColInfo();
                col.caption = item.Caption;
                col.el = item.ElementType;
                col.id = item.Id;
                col.order = item.ListTabIndex;
                col.visible =(long) item.VisibleScene;
                col.width = item.Width;

                tmpl.col.Add(col);
            }

            //动态添加图表
            RptChartInfo chart = new RptChartInfo();
            chart.type = 37;//折线图
            chart.title = "科目余额";
            chart.categoryaxis = "FAccountName,FYear,FPeriod,FCyName";
            chart.valueaxis = "FPBeginAmt,FPEndAmt";
            tmpl.chart.Add(chart);

            return tmpl;
        }


        private ColumnHeadObject BuildQtyHead(ColumnHeadObject rptHead )
        {
            ColumnHeadObject qtyHead = new ColumnHeadObject("数量");
            rptHead.AddSubHead(qtyHead);                    

            ColumnHeadObject periodHead = new ColumnHeadObject("本期");
            ColumnHeadObject yearHead = new ColumnHeadObject("本年");
            qtyHead.AddSubHead(periodHead).AddSubHead(yearHead);

            periodHead.AddSubHead(new ColumnObject("FPBeginQty", "期初数", HtmlElementType.HtmlField_AmountField))
                .AddSubHead(new ColumnObject("FPQty", "发生数", HtmlElementType.HtmlField_AmountField))
                .AddSubHead(new ColumnObject("FPEndQty", "期末数", HtmlElementType.HtmlField_AmountField));
                            
            yearHead.AddSubHead(new ColumnObject("FYBeginQty", "年初数", HtmlElementType.HtmlField_AmountField))
                .AddSubHead(new ColumnObject("FYQty", "发生数", HtmlElementType.HtmlField_AmountField))
                .AddSubHead(new ColumnObject("FYEndQty", "年末数", HtmlElementType.HtmlField_AmountField)) ;

            rptHead.AddSubHead(new ColumnObject("FEndQty", "期末数", HtmlElementType.HtmlField_AmountField));

            return qtyHead;
        }

        private void  BuildAmountHead(ColumnHeadObject rptHead)
        {
            ColumnHeadObject amtHead = new ColumnHeadObject("金额");
            rptHead.AddSubHead(amtHead);

            ColumnHeadObject periodHead = new ColumnHeadObject("本期");
            ColumnHeadObject yearHead = new ColumnHeadObject("本年");
            ColumnHeadObject balHead = new ColumnHeadObject("余额");
            amtHead.AddSubHead(periodHead).AddSubHead(yearHead).AddSubHead(balHead);

            ColumnHeadObject periodBHead = new ColumnHeadObject("本期发生额");
            periodHead.AddSubHead(new ColumnObject("FPBeginAmt", "期初余额", HtmlElementType.HtmlField_AmountField))
               .AddSubHead(periodBHead)
               .AddSubHead(new ColumnObject("FPEndAmt", "期末余额", HtmlElementType.HtmlField_AmountField));

            periodBHead.AddSubHead(new ColumnObject("FPDebitAmt", "借方", HtmlElementType.HtmlField_AmountField))
              .AddSubHead(new ColumnObject("FPCreditAmt", "贷方", HtmlElementType.HtmlField_AmountField));
                        
            ColumnHeadObject yearBHead = new ColumnHeadObject("本年发生额");
            yearHead.AddSubHead(new ColumnObject("FYBeginAmt", "年初余额", HtmlElementType.HtmlField_AmountField))
               .AddSubHead(yearBHead)
               .AddSubHead(new ColumnObject("FYEndAmt", "年末余额", HtmlElementType.HtmlField_AmountField));

            yearBHead.AddSubHead(new ColumnObject("FYDebitAmt", "借方", HtmlElementType.HtmlField_AmountField))
              .AddSubHead(new ColumnObject("FYCreditAmt", "贷方", HtmlElementType.HtmlField_AmountField));

            balHead.AddSubHead(new ColumnObject("FBalance", "余额", HtmlElementType.HtmlField_AmountField))
              .AddSubHead(new ColumnObject("FDC", "方向", HtmlElementType.HtmlField_TextField));
              
        }








        /// <summary>
        /// 构建报表数据 ：
        /// 1、出于性能考虑，要求返回IDataReader类型的数据包
        /// 2、返回的desc数据包，office端查询，强制要求要返回本次查询返回的记录数，
        ///    即 ListDesc.CurrentRows 字段要赋值 ，以便构建二维数组做批量填充
        /// </summary>
        protected override IDataReader BuildQueryData(out ListDesc desc)
        {
            //按各自的报表取数要求，编写报表取数的业务逻辑，返回数据。

            string view = @" select fid, ROW_NUMBER() OVER(order by fid) AS FJNIdentity 
                            from t_sec_user  ";
            var allCols = this.ColumnHead.AllColumns;

            List<string> selFlds = new List<string>();
            foreach (var item in allCols)
            {
                if( item.ElementType == HtmlElementType.HtmlField_TextField )
                {
                    selFlds.Add(" FID AS " + item.Id);
                }
                else
                {
                    selFlds.Add(" FJNIdentity AS " + item.Id);
                }
            }

            string sql =string.Format ( "select {0} From ({1}) y "  ,string.Join (",",selFlds ),view ) ;

            string sqlCount =string.Format ( "select count(1) from ({0}) x " ,sql );

            desc = new ListDesc();
            var dbSvc = this.Container.GetService<IDBService>();

            using (var reader = dbSvc.ExecuteReader(this.UserCtx, sqlCount))
            {
                while (reader.Read())
                {
                    desc.CurrentRows  = Convert.ToInt64(reader[0]);
                    break;
                }
            }

            var data = dbSvc.ExecuteReader(this.UserCtx, sql);

            return data;
        }

        protected override void BuildRptChartDataDesc(UserContext userCtx, ReportOperationContext rptOperCtx, ReportModelDesc rptModelDesc)
        {
            base.BuildRptChartDataDesc(userCtx, rptOperCtx, rptModelDesc);
        }

        protected override void BuildRptChartDataSource(UserContext userCtx, ReportOperationContext rptOperCtx, ReportDataSource rptDataSource)
        {
            base.BuildRptChartDataSource(userCtx, rptOperCtx, rptDataSource);
        }
    }









}
