using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;
using System.Dynamic;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.Framework.AppService.ReportService
{






    /// <summary>
    /// 获取模板选项信息
    /// </summary>
    [InjectService]
    [FormId("bas_officetmpl")]
    [OperationNo("LoadOfficeTmplOption")]
    public class LoadOfficeTmplOption : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            List<dynamic> tmpls = new List<dynamic>();
            var tmplId = this.GetQueryOrSimpleParam<string>("id", "");
            using (var reader = this.DBService.ExecuteReader(this.Context, "select FID,foption from t_bas_officetmpl where fid='" + tmplId + "'"))
            {
                while (reader.Read())
                {
                    this.Result.SrvData = reader.GetValue<string>("foption");
                    this.Result.IsSuccess = true;
                    break;
                }
            }

        }





    }


}
