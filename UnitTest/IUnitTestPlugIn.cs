using JieNor.Framework.DataTransferObject.TestCase;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.UnitTest
{


    /// <summary>
    /// 单元测试插件接口
    /// </summary>
    public interface IUnitTestPlugIn
    {
        TestCaseInfo TestCaseInfo
        {
            get; 
        }


        void IniUnitTestContext(TestCasePara para);



        /// <summary>
        /// 执行测试用例
        /// </summary> 
        TestCaseExeResult ExcuteUnitTestCase();

         
    }


     







}
