using Autofac.Features.Metadata;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.TestCase;
using JieNor.Framework.Interface.UnitTest;
using JieNor.Framework.MetaCore.FormMeta;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.UnitTest
{
    public class UnitTestService :  IUnitTestCase
    {

         



        /// <summary>
        /// 执行测试用例
        /// </summary> 
        public List<TestCaseExeResult> ExcuteTestCase(UserContext ctx)
        {  
            List<TestCaseInfo> testCases = GetAllTestCase(ctx);
            var result = ExcuteTestCase(ctx, testCases);

            return result;
        }


        public List<TestCaseExeResult> ExcuteTestCase(UserContext ctx, List<TestCaseInfo> testCase)
        {
            TestCasePara para = BuildTestCasePara(ctx);

            List<TestCaseExeResult> result = new List<TestCaseExeResult>();
            testCase.OrderBy(f => f.Index);
            foreach (var tc in testCase)
            {
                var svc = ctx.Container.GetService<IEnumerable<Meta<Lazy<IUnitTestPlugIn>>>>()
                    .Where(o => o.Metadata.GetString("AliasName").EqualsIgnoreCase("UnitTest")
                                    && o.Metadata.GetString("testcasename").EqualsIgnoreCase(tc.TestCaseKey));
                if (tc.TestCaseKey.StartsWithIgnoreCase("billcommonop."))
                {
                    svc = ctx.Container.GetService<IEnumerable<Meta<Lazy<IUnitTestPlugIn>>>>()
                        .Where(o => o.Metadata.GetString("AliasName").EqualsIgnoreCase("UnitTest")
                                    && o.Metadata.GetString("testcasename").EqualsIgnoreCase("billcommonop"));
                }
                if (svc == null || !svc.Any())
                {
                    continue;
                }

                var taskId = Guid.NewGuid().ToString().Replace("-", "").Substring(0, 6);
                foreach (var item in svc)
                {
                    if (tc.TestCaseKey.StartsWithIgnoreCase("billcommonop."))
                    {
                        item.Value.Value.TestCaseInfo.TestCaseKey = tc.TestCaseKey;
                        item.Value.Value.TestCaseInfo.TestCaseDesc = tc.TestCaseDesc;
                    }

                    item.Value.Value.IniUnitTestContext(para); 
                    var res = item.Value.Value.ExcuteUnitTestCase();
                    if (res != null)
                    {
                        result.Add(res);
                    } 
                }
            }

            return result;
        }


        /// <summary>
        /// 构建单元测试的相关参数
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        private TestCasePara BuildTestCasePara(UserContext ctx)
        {
            TestCasePara para = new TestCasePara();

            return para;
        }



        /// <summary>
        /// 获取测试用例信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        public List<TestCaseInfo> GetAllTestCase(UserContext ctx)
        {
            var testCases = GetAllCommonBillEditTestCase(ctx);

            //各业务系统自定义的测试用例信息
            var svc = ctx.Container.GetService<IEnumerable<Meta<Lazy<IUnitTestPlugIn>>>>()
                .Where(o => o.Metadata.GetString("AliasName").EqualsIgnoreCase("UnitTest")
                            && !o.Metadata.GetString("testcasename").EqualsIgnoreCase("billcommonop"));
            foreach (var item in svc)
            {
                testCases.Add(item.Value.Value.TestCaseInfo);
            }

            //从配置文件获取设置的测试用例执行顺序
            var appPath = PathUtils.GetStartupPath(); 
            var file = Path.Combine(appPath, "UnitTestCase.json");
            if (File.Exists(file))
            {
                var exeOrder = File.ReadAllText(file, Encoding.UTF8);
                List<TestCaseInfo> tcInfors = exeOrder?.FromJson<List<TestCaseInfo>>();
                if(tcInfors!=null && tcInfors.Any ())
                {
                    foreach (var item in tcInfors)
                    {
                        var exist = testCases.FirstOrDefault(f => f.TestCaseKey.EqualsIgnoreCase(item.TestCaseKey));
                        if(exist !=null )
                        {
                            exist.Index = item.Index;
                        }
                    }
                }
            }
            
            return testCases;
        }



        /// <summary>
        /// 获取通用单元测试用例，如基础资料（单据）的增、删、改、查、导出、打印、提交、审核等等通用操作的测试用例
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        private List<TestCaseInfo> GetAllCommonBillEditTestCase(UserContext ctx)
        {
            List<TestCaseInfo> testCases = new List<TestCaseInfo>();
            var mdls = HtmlParser.LoadBizObjectList();
            foreach (var mdl in mdls)
            {
                var htmlForm = HtmlParser.LoadFormMetaFromCache(mdl.Key, ctx);
                if (htmlForm.ElementType != HtmlElementType.HtmlForm_BillForm
                    && htmlForm.ElementType != HtmlElementType.HtmlForm_BaseForm)
                {
                    //只处理单据、基础资料的通用操作
                    continue;
                }

                if (htmlForm.BillHeadTableName.IsNullOrEmptyOrWhiteSpace() ||
                    htmlForm.BillHeadTableName.StartsWithIgnoreCase("v_"))
                {
                    //视图的不处理
                    continue;
                }

                TestCaseInfo tcInfo = new TestCaseInfo()
                {
                    TestCaseKey = "billcommonop.{0}".Fmt(mdl.Key),
                    TestCaseDesc = "{0}--通用操作测试用例".Fmt(htmlForm.Caption),
                    Default = true ,
                };
                testCases.Add(tcInfo);
            }

            return testCases;
        }



    }

}
