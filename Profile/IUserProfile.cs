using JieNor.Framework.MetaCore.FormModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.Profile
{
    /// <summary>
    /// 用户配置信息服务接口
    /// </summary>
    public interface IUserProfile
    {
        /// <summary>
        /// 保存用户配置文件
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="profileData"></param>
        /// <param name="isActiveProfile"></param>
        void SaveUserProfile(UserContext ctx, string formId, FormUserProfile profileData, bool isActiveProfile = false);

        /// <summary>
        /// 加载用户配置文件
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="category"></param>
        /// <param name="profile"></param>
        void SaveUserProfile<T>(UserContext ctx, string formId, string category, T profile);

        /// <summary>
        /// 加载用户配置文件
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="category"></param>
        /// <returns></returns>
        FormUserProfile LoadUserProfile(UserContext ctx, string formId, string category = "list");

        /// <summary>
        /// 加载用户配置文件
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="category"></param>
        /// <returns></returns>
        T LoadUserProfile<T>(UserContext ctx, string formId, string category);

        /// <summary>
        /// 删除指定表单的用户布局信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="category"></param>
        void DeleteUserProfile(UserContext ctx, string formId, string category = "list");
    }
}
