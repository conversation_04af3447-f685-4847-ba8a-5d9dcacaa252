using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 系统参数接口
    /// </summary>
    public interface ISystemProfile
    {
        /// <summary>
        /// 获取指定类别的所有系统参数
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="category"></param>
        /// <param name="withoutTopOrg">忽略总部</param>
        /// <returns></returns>
        Dictionary<string, string> GetProfile(UserContext ctx, string category, bool withoutTopOrg = false);

        /// <summary>
        /// 获取具体的某个标识的系统参数
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="category"></param>
        /// <param name="key"></param>
        /// <param name="withoutTopOrg">忽略总部</param>
        /// <returns></returns>
        string GetProfile(UserContext ctx, string category, string key, bool withoutTopOrg = false);

        /// <summary>
        /// 获取所有参数内容
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        Dictionary<string, string> GetAllProfiles(UserContext ctx);

        /// <summary>
        /// 创建或更新一个系统参数
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="category"></param>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="desc"></param>
        void CreateOrUpdateProfile(UserContext ctx, string category, string key, string value, string desc = "");


        /// <summary>
        /// 获取系统参数信息（参数设置页面的参数值）
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="paraFormId"></param>
        /// <param name="withoutTopOrg">忽略总部</param>
        /// <returns></returns>
        DynamicObject GetSystemParameter(UserContext ctx, string paraFormId, bool withoutTopOrg = false);

        /// <summary>
        /// 将数据实体保存为系统参数形式
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="dataEntity"></param>
        /// <param name="option"></param>
        void SaveSystemParameter(UserContext userCtx, HtmlForm htmlForm, DynamicObject dataEntity, OperateOption option);

        /// <summary>
        /// 获取指定字段的系统参数
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="ctx"></param>
        /// <param name="paraFormId"></param>
        /// <param name="paraKey"></param>
        /// <param name="defVal"></param>
        /// <param name="withoutTopOrg">忽略总部</param>
        /// <returns></returns>
        T GetSystemParameter<T>(UserContext ctx, string paraFormId, string paraKey, T defVal = default(T), bool withoutTopOrg = false);
    }
}
