using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.DataTransferObject.Report;
using System.Data;
using JieNor.Framework.DataTransferObject;
using System.Data.SqlClient;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.CustomException;
using Newtonsoft.Json.Linq;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Serialization;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.Interface.Profile;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Consts;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 报表服务插件基类
    /// </summary>
    public abstract class AbstractReportServicePlugIn : AbstractOperationServicePlugIn
    {
        #region 屏蔽标准操作插件的方法
        public sealed override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            base.OnPrepareOperationOption(e);
        }

        public sealed override void PrepareBusinessServices(PrepareBusinessServiceEventArgs e)
        {
            base.PrepareBusinessServices(e);
        }

        public sealed override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
        }

        public sealed override void OnCheckPermssion(OnCheckPermssionArgs e)
        {
            base.OnCheckPermssion(e);
        }

        public sealed override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
        }

        public sealed override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
        }

        public sealed override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
        }

        #endregion

        /// <summary>
        /// 操作上下文
        /// </summary>
        protected ListReportOperationContext OpContext
        {
            get
            {
                return this.OperationContext as ListReportOperationContext;
            }
        }

        /// <summary>
        /// 原用户上下文
        /// </summary>
        public UserContext OriginContext
        {
            get { return base.Context; }
        }

        private UserContext _queryContent = null;
        /// <summary>
        /// 查询上下文
        /// </summary>
        public new UserContext Context
        //public UserContext QueryContext
        {
            get
            {
                if (_queryContent == null)
                {
                    _queryContent = base.Context.CreateQueryDBContext(this.HtmlForm);
                }

                return _queryContent;
            }
        }

        /// <summary>
        /// 获得列表模型存储表名：在listReportFormController里面传递进来
        /// </summary>
        protected string DataSourceTableName
        {
            get
            {
                return this.OpContext.RptTempTableName;
            }
        }

        private HtmlForm _customFilterForm;
        /// <summary>
        /// 自定义过滤页面模型
        /// </summary>
        protected HtmlForm CustomFilterFormMeta
        {
            get
            {
                if (_customFilterForm.IsNullOrEmpty())
                {
                    var customFilterForm = this.HtmlForm.CustomFilterForm;
                    if (customFilterForm.IsNullOrEmptyOrWhiteSpace()) return null;

                    _customFilterForm = this.MetaModelService.LoadFormModel(this.Context, customFilterForm);
                }
                return _customFilterForm;
            }
        }

        private DynamicObject _customFilterObj = null;
        /// <summary>
        /// 自定义过滤条件（从每个业务表单提供过滤子表单中收集而来的过滤条件）
        /// </summary>
        protected DynamicObject CustomFilterObject
        {
            get
            {
                if (_customFilterObj == null)
                {
                    var customFilter = this.GetQueryOrSimpleParam<string>("__customFilter__", "");
                    JArray jObjs = new JArray();
                    if (!customFilter.IsNullOrEmptyOrWhiteSpace())
                    {
                        var jObjCustomFilter = JObject.Parse(customFilter);
                        jObjs.Add(jObjCustomFilter);
                    }


                    if (this.CustomFilterFormMeta.IsNullOrEmpty()) return null;
                    var dtCustomFilterForm = this.CustomFilterFormMeta.GetDynamicObjectType(this.Context);
                    var defCalulator = this.Container.GetService<IDefaultValueCalculator>();

                    if (jObjs.Any())
                    {
                        List<DynamicObject> lstBillObjs = new List<DynamicObject>();
                        var dcSerializer = this.Container.GetService<IDynamicSerializer>();
                        dcSerializer.Sync(dtCustomFilterForm, lstBillObjs, jObjs, (propKey) =>
                            {
                                var el = this.OperationContext.HtmlForm?.GetElement(propKey);
                                if (el is HtmlField) return (el as HtmlField).DynamicProperty;
                                if (el is HtmlEntryEntity) return (el as HtmlEntryEntity).DynamicProperty;

                                return null;
                            },
                         null,
                         null,
                         null);

                        _customFilterObj = lstBillObjs.FirstOrDefault();
                    }

                    if (_customFilterObj == null)
                    {
                        _customFilterObj = dtCustomFilterForm.CreateInstance() as DynamicObject;
                        defCalulator.Execute(this.Context, this.CustomFilterFormMeta, new DynamicObject[] { _customFilterObj });
                    }

                    this.Container.GetService<LoadReferenceObjectManager>()?.Load(this.Context, _customFilterObj.DynamicObjectType, _customFilterObj, false);

                }
                return _customFilterObj;
            }
        }

        /// <summary>
        /// 前端是否有传递过滤条件
        /// </summary>
        protected bool IsExistsFilter
        {
            get
            {
                var quickFilter = this.GetQueryOrSimpleParam<string>("quickfilter");  //快捷过滤条件
                if (quickFilter.IsNullOrEmptyOrWhiteSpace()
                    && this.OpContext.WhereString.Count <= 0  //过滤方案
                    && this.OpContext.FilterString.IsNullOrEmptyOrWhiteSpace()  //简单过滤条件
                    && this.OpContext.DynamicParam.IsNullOrEmptyOrWhiteSpace()  //动态过滤条件
                    && this.CustomFilterObject == null) //报表自定义过滤条件
                {
                    return false;
                }
                return true;
            }
        }


        /// <summary>
        /// 报表查询过滤条件及排序条件的干预
        /// </summary>
        /// <param name="e"></param>
        public sealed override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName.ToLower())
            {
                case "oninihtmlform":
                    var htmlForm = e.EventData as HtmlForm;
                    if (htmlForm != null)
                    {
                        this.OnIniHtmlForm(htmlForm);
                    }
                    break;
                case "preparequerybuilderparameter":
                    var queryPara = e.EventData as SqlBuilderParameter;
                    if (queryPara != null)
                    {
                        this.OnPrepareReportQueryParameter(queryPara);
                    }
                    break;
                case "afterlistdata":
                    var listData = e.EventData as List<Dictionary<string, object>>;
                    if (listData != null)
                    {
                        this.OnAfterListData(listData);
                    }
                    break;
            }
        }



        /// <summary>
        /// 模型初始化完成后（业务操作之前），可以进行模型的动态构建，比如动态增加列
        /// </summary>
        /// <param name="htmlForm">模型信息</param>
        protected virtual void OnIniHtmlForm(HtmlForm htmlForm)
        {

        }



        /// <summary>
        /// 插件在此事件里，实现列表模型的修改和列表数据的填充
        /// </summary>
        /// <param name="e"></param>
        public sealed override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            using (var tran = this.Context.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                switch (this.OperationNo.ToLower())
                {
                    case "query":
                        this.OnExecuteLogic();
                        break;

                    case "querydata":
                        //统一判断是否要执行数据源准备，通过 Option 与宿主 querydata 之间联系
                        bool needRefresh = this.Option.GetVariableValue<bool>("__rptneedrefresh__");
                        if (needRefresh)
                        {
                            //清空报表临时表数据
                            this.TruncateRptTempTabel();

                            try
                            {
                                this.OnExecuteLogic();
                            }
                            catch (SqlException)
                            {
                                //清除缓存的过滤条件，以便重新获取报表数据
                                var filterCacheKey = CacheKeyConst.CacheKey_Rpt_Filter.Fmt(Context.UserSession.Id, this.OperationContext.HtmlForm.Id).HashString();
                                var cacheMem = this.Context.Container.GetService<IMemoryCache>();
                                cacheMem?.Remove(this.Context, filterCacheKey);

                                var dm = this.GetDataManager();
                                dm.Option = OperateOption.Create();
                                dm.Option.SetVariableValue("forceRecreate", true);
                                dm.Option.SetAutoUpdateScheme(true);
                                dm.InitDbContext(this.Context, this.OpContext.HtmlForm.GetDynamicObjectType(this.Context));

                                throw;
                            }
                        }
                        break;
                }
                tran.Complete();
            }
        }

        /// <summary>
        /// 清空报表临时表数据
        /// </summary>
        private void TruncateRptTempTabel()
        {
            var strSql = $@"
            if exists (select 1 from sysobjects where name='{this.DataSourceTableName}' and xtype='u')
            begin
                truncate table {this.DataSourceTableName}
            end";
            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();
            dbServiceExt.Execute(this.Context, strSql);
        }

        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected virtual void OnExecuteLogic()
        {

        }

        /// <summary>
        /// 将报表查询过滤对象放给插件基类来处理
        /// </summary>
        /// <param name="listQueryPara"></param>
        protected virtual void OnPrepareReportQueryParameter(SqlBuilderParameter listQueryPara)
        {

        }

        /// <summary>
        /// 将报表数据放给业务插件干预
        /// </summary>
        protected virtual void OnAfterListData(List<Dictionary<string, object>> listData)
        {

        }

        /// <summary>
        /// 获取或添加报表数据列字段
        /// </summary>
        /// <param name="id"></param>
        /// <param name="caption"></param>
        /// <param name="elType">字段类型，参考：<seealso cref="HtmlElementType.HtmlField_MinValue"/>至<seealso cref="HtmlElementType.HtmlField_MaxValue"/></param>
        /// <returns></returns>
        protected HtmlField GetOrAddReportField(string id, string caption, int elType)
        {
            if (this.HtmlForm.FieldList.ContainsKey(id))
            {
                return this.HtmlForm.FieldList[id];
            }

            //todo:将datacolumn翻译转换成内部的htmltextfield或htmldatetimefield，然后加入htmlform里
            var htmlField = HtmlParser.GetHtmlFieldInstance(this.Container, elType);
            htmlField.Id = id;
            htmlField.FieldName = id;
            htmlField.EntityKey = "fbillhead";
            htmlField.Caption = caption;
            htmlField.Group = this.HtmlForm.Caption;
            htmlField.Visible = -1;
            this.HtmlForm.FieldList.Add(id, htmlField);
            return htmlField;
        }
        ///// <summary>
        ///// 添加报表动态数据列字段并为报表临时表添加对应列（暂弃用，未实际应用上，已有其他更好方案支持解决）
        ///// </summary>
        ///// <param name="model"></param>
        //protected void AddDynamicReportField(List<ReportFieldModel> model)
        //{
        //    if (model.IsNullOrEmpty() || model.Any(x => x.Id.IsNullOrEmptyOrWhiteSpace() || x.Caption.IsNullOrEmptyOrWhiteSpace() || x.ElType.IsNullOrEmptyOrWhiteSpace()))
        //    {
        //        return;
        //    }
        //    string addColumns = string.Empty;
        //    var entityKey = "fbillhead";
        //    var defEntity = this.HtmlForm.GetEntity(entityKey);
        //    FormUserProfile lastProfileInfo = null;
        //    DynamicObject dcUserProfile = null;
        //    IMetaModelService metaSrv = null;
        //    HtmlForm userProfileMeta = null;
        //    IDataManager dm = null;
        //    foreach (var item in model)
        //    {
        //        if (!this.HtmlForm.FieldList.ContainsKey(item.Id))
        //        {
        //            if (metaSrv == null || userProfileMeta == null)
        //            {
        //                InitDynamicReportField(ref dm, ref userProfileMeta, ref metaSrv, ref dcUserProfile, ref lastProfileInfo);
        //            }
        //            //todo:将datacolumn翻译转换成内部的htmltextfield或htmldatetimefield，然后加入htmlform里
        //            var htmlField = HtmlParser.GetHtmlFieldInstance(this.Container, item.ElType);
        //            htmlField.Id = item.Id;
        //            htmlField.FieldName = item.Id;
        //            htmlField.EntityKey = entityKey;
        //            htmlField.Caption = item.Caption;
        //            htmlField.Group = this.HtmlForm.Caption;
        //            htmlField.Visible = -1;
        //            htmlField.Length = item.Length;
        //            htmlField.ListTabIndex = item.lix;
        //            this.HtmlForm.FieldList.Add(item.Id, htmlField);
        //            addColumns += $"{item.Id} varchar({item.Length}),";

        //            if (lastProfileInfo.IsNullOrEmpty()||lastProfileInfo.ListLayout.IsNullOrEmpty() || lastProfileInfo.ListLayout.Count == 0) continue;
        //            lastProfileInfo.ListLayout.Add(new ElementLayout
        //            {
        //                Id = item.Id,
        //                Width = 100,
        //                Hidden = false,
        //                DispOrder = lastProfileInfo.ListLayout.Count == 0 ? 1 : lastProfileInfo.ListLayout.Max(x => x.DispOrder) + 1,
        //                SortDirection = "",
        //                SortOrder = 0,
        //                QuickFilter = false,
        //                Frozen = false,
        //                AggreateStyle = 0
        //            });
        //        }
        //    }

        //    if (!addColumns.IsNullOrEmptyOrWhiteSpace() && !this.HtmlForm.BillHeadTableName.IsNullOrEmptyOrWhiteSpace())
        //    {
        //        var strSql = $"alter table {this.HtmlForm.BillHeadTableName} add ({addColumns.TrimEnd(',')});";
        //        var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();
        //        dbServiceExt.Execute(this.Context, strSql);

        //        if (!lastProfileInfo.IsNullOrEmpty()&&!lastProfileInfo.ListLayout.IsNullOrEmpty() && lastProfileInfo.ListLayout.Count() > 0)
        //        {
        //            var profileService = this.Container.GetService<IUserProfile>();
        //            profileService.SaveUserProfile(this.OperationContext.UserContext, this.OperationContext.HtmlForm.Id, lastProfileInfo, true);
        //        }
        //        this.AddRefreshPageAction();
        //    }
        //}

        //private void InitDynamicReportField(ref IDataManager dm, ref HtmlForm userProfileMeta, ref IMetaModelService metaSrv, ref DynamicObject dcUserProfile, ref FormUserProfile lastProfileInfo)
        //{
        //    dm = this.GetDataManager();
        //    dm.InitDbContext(this.Context, this.OpContext.HtmlForm.GetDynamicObjectType(this.Context));
        //    metaSrv = this.Container?.TryGetService<IMetaModelService>();
        //    userProfileMeta = metaSrv?.LoadFormModel(this.Context, "sys_userprofile");
        //    var pkIdReader = this.Context.GetPkIdDataReader(userProfileMeta,
        //    $"fisactive='1' and fcategory=@fcategory and fuserid=@fuserid and fbillformid=@fbillformid and fmainorgid=@fmainorgid",
        //    new SqlParam[]
        //    {
        //                    new SqlParam("fcategory", System.Data.DbType.String, "listreport"),
        //                    new SqlParam("fuserid", System.Data.DbType.String,this.Context.UserId),
        //                    new SqlParam("fbillformid", System.Data.DbType.String, this.HtmlForm.Id),
        //                    new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
        //    });
        //    dcUserProfile = dm.SelectBy(pkIdReader).OfType<DynamicObject>().FirstOrDefault();

        //    if (dcUserProfile == null)
        //    {
        //        //不做处理，显示默认项
        //        //lastProfileInfo = new FormUserProfile()
        //        //{
        //        //    Id = this.HtmlForm.Id,
        //        //    Category = "listreport",
        //        //    FilterSchemeId = "__default",
        //        //    ListPageCount = 50,
        //        //    FormWidth = "",
        //        //    FormHeight = "",
        //        //    FilterFields = new List<string>(),
        //        //    UseAdvancedFilterPanel = false,
        //        //    SplitterPosition = new Dictionary<string, decimal>(),
        //        //    ListLayout = new List<ElementLayout>(),
        //        //    ListParamData = new ListParamData { UnmergeBillHeadField = false, ZeroNoDispaly = false }
        //        //};
        //    }
        //    else
        //    {
        //        lastProfileInfo = dcUserProfile["fprofile"] as FormUserProfile;
        //    }
        //}
    }
}
