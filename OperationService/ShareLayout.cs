using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Profile;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 共享布局
    /// </summary>
    [InjectService("sharelayout")]
    public class ShareLayout : AbstractOperationService
    {
        const string cacheKey = "UserProfile:{0}:{1}:{2}.{3}";
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var profileDataObj = this.GetQueryOrSimpleParam<string>("profileData")
                .FromJson<FormUserProfile>();

            var profileService = this.Container.GetService<IUserProfile>();
            if (profileDataObj == null)
            {
                profileDataObj = profileService.LoadUserProfile(this.UserCtx, this.OperationContext.HtmlForm.Id, this.DomainType.ToString());
            }
            if (profileDataObj == null
                || profileDataObj.ListLayout.Any()==false)
            {
                throw new BusinessException("共享失败：当前用户还未设置好布局信息，请尝试关闭或保存当前页面后，重新打开试试！");
            }

            //获取当前要共享的所有目标用户信息
            var dctUserIds = GetShareUserIds();
            if (dctUserIds?.Any() == false)
            {
                throw new BusinessException("请先录入角色与用户后再进行布局共享！");
            }

            //将当前用户信息导入临时表，找到目标用户关联的布局信息进行删除，然后将当前待共享的布局信息进行保存
            var tempTableName = CreateUserInTempTable(dctUserIds);

            if (tempTableName.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("请先录入角色与用户后再进行布局共享！");
            }
            //关联更新目标用户当前布局id至临时表
            var strSql = $@"
update {tempTableName} as temp set (fprofileid)=(
    select t0.fid
    from t_sys_userprofile t0
    where t0.fuserid=temp.fuserid and t0.fbillformid=@formId and t0.fmainorgid=@companyId and t0.fcategory=@domainType and t0.fisactive='1'
)
";
            var dbServiceEx = this.Container.GetService<IDBServiceEx>();
            var affectedRows = dbServiceEx.Execute(this.UserCtx, strSql, new SqlParam[]
            {
                new SqlParam("formId", DbType.String, this.OperationContext.HtmlForm.Id),
                new SqlParam("companyId", DbType.String, this.UserCtx.Company),
                new SqlParam("domainType", DbType.String, this.DomainType.ToString())
            });

            //更新用户账号关联的用户名
            strSql = $@"
update {tempTableName} as temp set (fusername)=(
    select case when t1.fname ='' then t1.fnumber else t1.fname end fusername
    from t_sec_user t1 
    where temp.fuserid=t1.fid
)
";
            affectedRows = dbServiceEx.Execute(this.UserCtx, strSql);

            var userProfileMeta = this.MetaModelService.LoadFormModel(this.UserCtx, "sys_userprofile");
            
            List<object> lstDeleteLayout = new List<object>();
            List<DynamicObject> lstTargetLayoutObjs = new List<DynamicObject>();
            List<string> lstOpMessage = new List<string>();
            using(var reader = this.DBService.ExecuteReader(this.UserCtx, $"select * from {tempTableName}"))
            {
                while (reader.Read())
                {
                    var targetLayoutProfileId = Convert.ToString(reader["fprofileid"]);
                    if (!targetLayoutProfileId.IsEmptyPrimaryKey())
                    {
                        lstDeleteLayout.Add(targetLayoutProfileId);
                    }

                    var dcUserProfile = userProfileMeta.GetDynamicObjectType(this.UserCtx).CreateInstance() as DynamicObject;
                    dcUserProfile["fbillformid"] = this.OperationContext.HtmlForm.Id;
                    dcUserProfile["fcategory"] = this.DomainType.ToString();
                    dcUserProfile["fformid"] = "sys_userprofile";
                    dcUserProfile["fprofile"] = profileDataObj.ToJson();
                    dcUserProfile["fuserid"] = Convert.ToString(reader["fuserid"]);
                    dcUserProfile["fisactive"] = "1";
                    dcUserProfile["fmainorgid"] = this.UserCtx.Company;
                    lstTargetLayoutObjs.Add(dcUserProfile);

                    lstOpMessage.Add($"布局已成功分享给：{reader["fusername"]}");
                }
            }
            var _userprofiledm = this.UserCtx.GetDefaultDataManager(userProfileMeta);
            if (lstDeleteLayout.Any())
                _userprofiledm.Delete(lstDeleteLayout);
            
            if (lstTargetLayoutObjs.Any())
            {
                var prepareService = this.Container.GetService<IPrepareSaveDataService>();
                prepareService.PrepareDataEntity(this.UserCtx, userProfileMeta, lstTargetLayoutObjs.ToArray(), OperateOption.Create());
                _userprofiledm.Save(lstTargetLayoutObjs);
                this.OperationContext.Result.ComplexMessage.SuccessMessages.AddRange(lstOpMessage);
                this.OperationContext.Result.IsSuccess = true;
            }

            //清空目标所有用户的布局缓存信息
            foreach (var item in dctUserIds)
            {
                var key = cacheKey.Fmt(this.UserCtx.Company, item, this.OperationContext.HtmlForm.Id, this.DomainType.ToString());
                this.CacheMem.Remove(this.UserCtx, key);
            }

            DBService.DeleteTempTableByName(UserCtx, tempTableName, true);
        }

        /// <summary>
        /// 将待共享的用户导入临时表
        /// </summary>
        /// <param name="dctUserIds"></param>
        /// <returns></returns>
        private string CreateUserInTempTable(Dictionary<string, string> dctUserIds)
        {
            var dtTempData = new DataTable();
            dtTempData.Columns.AddRange(new DataColumn[]
            {
                new DataColumn("fuserid",typeof(string)),
                new DataColumn("fusername",typeof(string)),
                new DataColumn("fprofileid",typeof(string))
            });
            dtTempData.BeginLoadData();
            foreach(var kvpItem in dctUserIds)
            {
                dtTempData.LoadDataRow(new object[] { kvpItem.Key, kvpItem.Value, "" }, true);
            }
            dtTempData.EndLoadData();
            return this.DBService.CreateTempTableWithDataTable(this.UserCtx, dtTempData,0,null,null,false);
        }

        private Dictionary<string,string> GetShareUserIds()
        {
            var roleIds = this.GetQueryOrSimpleParam<string>("roleIds", "");
            var userIds = this.GetQueryOrSimpleParam<string>("userIds", "");
            var dctUserIds = userIds.Split(new char[] { ',', '，', ';' }, StringSplitOptions.RemoveEmptyEntries)
                .Distinct(StringComparer.OrdinalIgnoreCase)
                .ToDictionary(k => k, StringComparer.OrdinalIgnoreCase);

            var lstRoleIds = roleIds.Split(new char[] { ',', '，', ';' }, StringSplitOptions.RemoveEmptyEntries)
                .Distinct(StringComparer.OrdinalIgnoreCase);
            var strSql = "";
            if (lstRoleIds.IsGreaterThan(30))
            {
                var tmpName = this.DBService.CreateTempTableWithDataList(this.UserCtx, lstRoleIds,false);
                strSql = $@"
select distinct t0.fuserid,t1.fnumber,t1.fname
from t_sec_roleuser t0
inner join {tmpName} temp on t0.froleid=temp.fid
inner join t_sec_user t1 on t0.fuserid=t1.fid 
where t1.fmainorgid=@companyId";
            }
            else if (lstRoleIds.Any())
            {
                strSql = $@"
select distinct t0.fuserid,t1.fname,t1.fnumber
from t_sec_roleuser t0
inner join t_sec_user t1 on t0.fuserid=t1.fid 
where t1.fmainorgid=@companyId and t0.froleid in ({lstRoleIds.JoinEx(",", true)})";
            }

            if (!strSql.IsNullOrEmptyOrWhiteSpace())
            {
                using (var reader = this.DBService.ExecuteReader(this.UserCtx, strSql, new SqlParam("companyId", DbType.String, this.UserCtx.Company)))
                {
                    while (reader.Read())
                    {
                        var userName = Convert.ToString(reader[1]);
                        if (userName.IsNullOrEmptyOrWhiteSpace())
                        {
                            userName = Convert.ToString(reader[2]);
                        }
                        dctUserIds[Convert.ToString(reader[0])] = userName;
                    }
                }
            }

            return dctUserIds;
        }
    }
}
