using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.FormService;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.Framework.AppService.OperationService
{

    /// <summary>
    /// 反审核操作
    /// </summary>
    [InjectService("UnAudit")]
    public class UnAudit : AbstractSetStatus
    {
        protected override string OperationName
        {
            get
            {
                return "反审核";
            }
        }
        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_Unaudit;
            }
        }

        /// <summary>
        /// 状态值
        /// </summary>
        protected override string StatusValue
        {
            get
            {
                return Enum.GetName(typeof(BillStatus), BillStatus.C);
            }
        }
        protected override bool AutoSaveData
        {
            get
            {
                return true;
            }
        }
        bool ExistCheckLogEntry
        {
            get;
            set;
        }

        protected override void PrepareBusinessService(List<FormServiceDesc> lstOpServices)
        {
            base.PrepareBusinessService(lstOpServices);

            var formServiceLoaders = this.Container.GetService<IEnumerable<IFormServiceLoader>>();
            if (formServiceLoaders != null && formServiceLoaders.Any())
            {
                foreach (var formServiceLoader in formServiceLoaders)
                {
                    var syncServiceInst = formServiceLoader.CreateSyncService(this.UserCtx, this.OperationContext.HtmlForm, Enu_SyncTimePoint.SyncWhenUnAudit, this.OperationContext.Option);
                    if (syncServiceInst != null)
                    {
                        lstOpServices.AddRange(syncServiceInst);
                    }
                }
            }
        }
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Count() == 0)
            {
                return;
            }

            HtmlComplexMessage msg = new HtmlComplexMessage();

            IList checkSetting = Audit.GetAuditSetting(this.OperationContext);
            if (checkSetting == null || checkSetting.Count == 0)
            {
                //没有设置审批信息，直接反审批
                SetUnCheckInfo(ref dataEntities);

                msg.SuccessMessages.Add("操作完成：反审核成功");

                // 这里提示信息不能直接覆盖，而是要做合并，否则会导致业务插件中的校验提示信息被清掉
                this.OperationContext.Result.ComplexMessage.ErrorMessages.AddRange(msg.ErrorMessages);
                this.OperationContext.Result.ComplexMessage.WarningMessages.AddRange(msg.WarningMessages);
                this.OperationContext.Result.ComplexMessage.SuccessMessages.AddRange(msg.SuccessMessages);
                this.OperationContext.Result.IsSuccess = true;
                return;
            }

            ExistCheckLogEntry = this.OperationContext.HtmlForm.ExistsCheckLogEntry();

            DynamicObject setting = checkSetting[0] as DynamicObject;
            //按单据进行处理
            foreach (var billData in dataEntities)
            {
                if (!IsSubmit(setting, billData))
                {
                    //未提交审核的，直接退出
                    msg.ErrorMessages.Add(Audit.GetMsg(OperationContext, billData, "数据状态不是已提交，不能进行审核"));
                    continue;
                }

                //取单据的当前审批级次
                DynamicObject currentLevel = GetBillLastestAuditLevel(setting, billData);
                if (currentLevel != null)
                {
                    if (Audit.IsIncludeUser(OperationContext, currentLevel))
                    {
                        SetUnAuditInfo(setting, billData);
                        msg.SuccessMessages.Add(Audit.GetMsg(OperationContext, billData, "反审批成功"));
                    }
                    else
                    {
                        msg.ErrorMessages.Add(Audit.GetMsg(OperationContext, billData, "您不在审批人范围内，无权进行审批"));
                    }
                }
                else
                {
                    SetUnAuditInfo(setting, billData);
                    msg.SuccessMessages.Add(Audit.GetMsg(OperationContext, billData, "反审批成功"));
                    //msg.ErrorMessages.Add(Audit.GetMsg(OperationContext, billData, "未能找到对应的审批级次信息，无法进行反审批，请检查单据的多级审批定义是否正确"));
                }
            }

            // 这里提示信息不能直接覆盖，而是要做合并，否则会导致业务插件中的校验提示信息被清掉
            this.OperationContext.Result.ComplexMessage.ErrorMessages.AddRange(msg.ErrorMessages);
            this.OperationContext.Result.ComplexMessage.WarningMessages.AddRange(msg.WarningMessages);
            this.OperationContext.Result.ComplexMessage.SuccessMessages.AddRange(msg.SuccessMessages);
            this.OperationContext.Result.IsSuccess = true;
            this.OperationContext.Result.IsShowMessage = true;
        }

        /// <summary>
        /// 获取最后审核对应的审核级次 
        /// </summary>
        /// <param name="setting"></param>
        /// <param name="billData"></param>
        /// <returns></returns>
        private DynamicObject GetBillLastestAuditLevel(DynamicObject setting, DynamicObject billData)
        {
            var checkInfo = setting["FEntity"] as DynamicObjectCollection;
            if (checkInfo == null || checkInfo.Count == 0)
            {
                return null;
            }
            int levelIndex = Audit.GetLastAuditLog(billData, checkInfo);

            var currentLevel = checkInfo.FirstOrDefault(f => f.GetValue<int>("findex", 0) == levelIndex);

            return currentLevel;
        }


        /// <summary>
        /// 反审核时，设置为 保存状态
        /// </summary>
        /// <param name="setting"></param>
        /// <param name="billData"></param>
        private void SetUnAuditInfo(DynamicObject setting, DynamicObject billData)
        {
            var statusFldKey = this.OperationContext.HtmlForm.BizStatusFldKey;// setting.GetValue<string>("fstatusfldkey", "").Trim();
            var foperatorfldkey = "fapproveid"; // setting.GetValue<string>("foperatorfldkey", "").Trim();
            var fdatefldkey = "fapprovedate"; //setting.GetValue<string>("fdatefldkey", "").Trim();

            Audit.SetAuditFldValue(billData, fdatefldkey, null);
            Audit.SetAuditFldValue(billData, foperatorfldkey, 0);
            Audit.SetAuditFldValue(billData, statusFldKey, BillStatus.B.ToString());

            //增加反审核日志
            if (ExistCheckLogEntry)
            {
                var checkLogs = billData["fchecklogentry"] as DynamicObjectCollection;
                var log = checkLogs.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                log.SetValue("fclogcheckdate", DateTime.Now);
                log.SetValue("fcloguserid", this.OperationContext.UserContext.UserId);
                log.SetValue("fclogisagree", true);
                log.SetValue("fclogcheckreason", "反审核");
                log.SetValue("fchecklevelid", "0");
                log.SetValue("fcheckleveldesc", "反审核");
                log.SetValue("fcloghandler", this.OperationContext.UserContext.UserId);

                checkLogs.Add(log);
            }
        }


        /// <summary>
        /// 判断是否已经提交
        /// </summary>
        /// <param name="setting"></param>
        /// <param name="billData"></param>
        /// <returns></returns>
        private bool IsSubmit(DynamicObject setting, DynamicObject billData)
        {
            var statusFldKey = this.OperationContext.HtmlForm.BizStatusFldKey;// setting.GetValue<string>("fstatusfldkey", "").Trim();
            if (statusFldKey.IsNullOrEmptyOrWhiteSpace())
            {
                return false;
            }

            var status = billData.GetValue<string>(statusFldKey, "A");
            if (status.EqualsIgnoreCase(BillStatus.A.ToString()) || status.EqualsIgnoreCase(BillStatus.B.ToString()))
            {
                return false;
            }

            return true;
        }

        private void SetUnCheckInfo(ref DynamicObject[] dataEntities)
        {
            foreach (var billData in dataEntities)
            {
                Audit.SetAuditFldValue(billData, this.OperationContext.HtmlForm.BizStatusFldKey, BillStatus.C.ToString());
                Audit.SetAuditFldValue(billData, "fapproveid", 0);
                Audit.SetAuditFldValue(billData, "fapprovedate", null);
            }
        }



        /// <summary>
        /// 处理反审核操作通用校验规则
        /// </summary>
        /// <returns></returns>
        protected override List<IDataValidRule> PrepareValidationRules()
        {
            //IList checkSetting = Audit.GetAuditSetting(this.OperationContext);
            //if (checkSetting == null || checkSetting.Count == 0)
            //{
            //    //没有设置审批信息，直接审核
            //    return null;
            //}
            var rules = base.PrepareValidationRules();

            var errorMessage = string.Empty;
            rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return ValiStatus(newData, oldData, out errorMessage);
            }).WithMessage("{0}", (dyObj, key) => errorMessage));

            return rules;
        }

        /// <summary>
        /// 状态校验
        /// </summary>
        /// <param name="newData"></param>
        /// <param name="oldData"></param>
        /// <param name="errorMessage"></param>
        /// <returns></returns>
        private bool ValiStatus(DynamicObject newData, DynamicObject oldData, out string errorMessage)
        {
            bool bPassed = true;
            HtmlForm formInfo = this.OperationContext.HtmlForm;
            var forbidFld = formInfo.GetField(formInfo.ForbidStatusFldKey);
            var statusFld = formInfo.GetField(formInfo.BizStatusFldKey);
            var cancelFld = formInfo.GetField(formInfo.CancelStatusFldKey);
            var numberFld = formInfo.GetField(formInfo.NumberFldKey);
            var numberVal = numberFld.DynamicProperty.GetValue<string>(newData);

            //未禁用
            bPassed = bPassed &&
                (forbidFld == null || forbidFld != null && !forbidFld.DynamicProperty.GetValue<bool>(newData));

            //状态=已审核 
            bPassed = bPassed &&
                (statusFld == null || statusFld != null && statusFld.DynamicProperty.GetValue<string>(newData).EqualsIgnoreCase(BillStatus.E.ToString()));

            //未作废
            bPassed = bPassed &&
                (cancelFld == null || cancelFld != null && !cancelFld.DynamicProperty.GetValue<bool>(newData));

            errorMessage = $"{numberFld.Caption}为【{numberVal}】的{formInfo.Caption} 不是已审核状态 或 已禁用 或 已作废，不允许反审核！";

            return bPassed;
        }
    }
}