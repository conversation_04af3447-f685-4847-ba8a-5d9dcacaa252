using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Drivers;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.DataTransferObject.Sys;
using JieNor.Framework.Interface.Utils;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 刷新操作
    /// </summary>
    [InjectService("Refresh")]
    public class Refresh : View
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName
        {
            get
            {
                return ""; //该操作成功后不需要前端提示信息，所以将其设置为空
            }
        }

        /// <summary>
        /// 检查权限
        /// </summary>
        protected override void CheckPermission()
        {
            //对于不是单据或基础资料类的（通常是动态表单，报表，参数等），先查一下是否有新增权限，有则直接放行（因为动态表单里面配置的都是新增权限）
            if (this.OperationContext.HtmlForm.ElementType != HtmlElementType.HtmlForm_BillForm
                && this.OperationContext.HtmlForm.ElementType != HtmlElementType.HtmlForm_BaseForm)
            {
                var permSrv = this.OperationContext.Container.GetService<IPermissionService>();
                var hasPermission = permSrv.HasPermission(this.OperationContext.UserContext, new MetaCore.PermData.PermAuth(this.OperationContext.UserContext)
                {
                    FormId = this.PermFormId,
                    OperationName = this.OperationContext.HtmlOperation?.OperationName ?? this.OperationName,
                    PermId = PermConst.PermssionItem_New
                });
                if (hasPermission) return;
            }

            base.CheckPermission();
        }

        /// <summary>
        /// 是否只查询数据
        /// </summary>
        protected virtual bool IsQueryDataOnly
        {
            get { return false; }
        }

        protected override void BeforeExecute(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null)
            {
                var dcDataEntity = this.OperationContext.HtmlForm.GetDynamicObjectType(this.UserCtx).CreateInstance() as DynamicObject;
                var defCalulator = this.OperationContext.Container.GetService<IDefaultValueCalculator>();
                defCalulator.Execute(this.OperationContext.UserContext, this.OperationContext.HtmlForm, new DynamicObject[] { dcDataEntity });
                dataEntities = new DynamicObject[] { dcDataEntity };
            }
        }

        /// <summary>
        /// 操作执行
        /// </summary>
        //[PerfMonitor]
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            //当前主键Id
            string currPkId = this.OperationContext.SelectedRows.FirstOrDefault()?.PkValue;

            // 尝试加锁
            bool tryLockBill = this.GetQueryOrSimpleParam<bool>("__TryLockBill__");

            Enu_BillStatus billStatus = Enu_BillStatus.New;

            //兼容一下直接从url传过来的情况
            if (currPkId.IsEmptyPrimaryKey())
            {
                currPkId = this.GetQueryOrSimpleParam<string>("id");
            }

            //单据菜单
            var metaService = this.OperationContext.Container.GetService<IMetaModelService>();
            var standarMenu = metaService.GetBillMenuByGroup(this.OperationContext.UserContext, this.OperationContext.HtmlForm.Id, "standard", this.OperationContext.FormLayoutId, Enu_BillStatus.Modify);
            var businessMenu = metaService.GetBillMenuByGroup(this.OperationContext.UserContext, this.OperationContext.HtmlForm.Id, "business", this.OperationContext.FormLayoutId, Enu_BillStatus.Modify);
            var dm = this.GetDataManager();
            dm.InitDbContext(this.UserCtx, this.OperationContext.HtmlForm.GetDynamicObjectType(this.UserCtx));
            var dcDataEntity = dataEntities?.FirstOrDefault();

            //如果是主键ID不为空，则说明是“修改状态下”的刷新操作，那么需要根据主键ID获取整单数据包
            if (dcDataEntity == null)
            {
                if (!currPkId.IsEmptyPrimaryKey())
                {
                    //查看修改时，直接从db及缓存中读取整单数据包
                    dcDataEntity = dm.Select(currPkId) as DynamicObject;
                }
            }

            if (dcDataEntity.IsNullOrEmpty())
            {
                if (this.OperationContext is BillOperationContext)
                {
                    if (billStatus == Enu_BillStatus.New)
                    {
                        throw new JieNor.Framework.CustomException.BusinessException("单据还未保存，不支持刷新操作！");
                    }
                    throw new JieNor.Framework.CustomException.BusinessException("当前单据可能已被删除，请关闭表单回到列表刷新后再操作！");
                }
                return;
            }

            if (!dcDataEntity["id"].IsEmptyPrimaryKey())
            {
                billStatus = Enu_BillStatus.Modify;
            }



            //加载引用数据
            var loadRefMgr = this.OperationContext.Container.GetService<LoadReferenceObjectManager>();
            loadRefMgr?.Load(this.UserCtx, dm.DataEntityType, new DynamicObject[] { dcDataEntity }, false);
            if (this.IsHtmlDecode)
            {
                this.OperationContext.Option.SetOptionFlag(Enu_OpFlags.HtmlDecodeWhenModify);
            }
            var uiConverter = this.OperationContext.Container.GetService<IUiDataConverter>();
            var billData = uiConverter.CreateUIDataObject(this.OperationContext.UserContext, this.OperationContext.HtmlForm, dcDataEntity, this.OperationContext.Option);
            ManageModelUtil.SetDataEntityManageModel(this.OperationContext.UserContext,this.OperationContext.HtmlForm,new DynamicObject[]{dcDataEntity});

            #region 如果是参数设置表单，则返回系统变量值
            if (this.OperationContext is ParameterOperationContext)
            {
                var spService = this.Container.GetService<ISystemProfile>();
                var sysProfileValue = spService?.GetProfile(this.UserCtx, "fw", $"{this.OperationContext.HtmlForm.Id}_parameter");
                if (!sysProfileValue.IsNullOrEmptyOrWhiteSpace())
                {
                    //将用户保存的参数合并到默认数据包中，以解决系统新增参数字段后，数据包中没有新增参数字段默认字段值的问题
                    var uiData = billData["uidata"] as JObject;
                    uiData.Merge(JObject.Parse(sysProfileValue));

                    this.OperationContext.Result.SrvData = new
                    {
                        Data = new { billData = new { uidata = uiData } }
                    };
                    this.OperationContext.Result.IsSuccess = true;
                    this.OperationContext.Result.IsShowMessage = true;
                    return;
                }
            }
            #endregion


            if (this.IsQueryDataOnly)
            {
                this.OperationContext.Result.SrvData = new
                {
                    Data = new
                    {
                        billData = billData
                    }
                };
            }
            else
            {
                //允许插件干预打包数据过程
                var ae = new OnCustomServiceEventArgs()
                {
                    EventName = "afterCreateUIData",
                    EventData = billData.GetJsonValue<JObject>("uidata"),
                };
                this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", ae);
                if (ae.EventData is JObject)
                {
                    billData["uidata"] = ae.EventData as JObject;
                }
                var profileService = this.Container.GetService<IListQuryProfile>();
                var permService = this.UserCtx.Container.GetService<IPermissionService>();
                var lstInvisibleFields = permService.GetInvisibleFieldByFormId(this.UserCtx, this.OperationContext.HtmlForm.Id) ?? new List<string>();
                var uiMetaAction = new Action<HtmlElement, JObject>((el, uiMeta) =>
                {
                    if (el is HtmlField)
                    {
                        if (lstInvisibleFields.Contains(el.Id, StringComparer.OrdinalIgnoreCase))
                        {
                            uiMeta["visible"] = false;
                            uiMeta["useMask"] = true;
                        }
                    }
                    else if (el is HtmlEntryEntity)
                    {
                        var entity = el as HtmlEntryEntity;
                        if (entity.Personalized)
                        {
                            //加载单元格颜色设置
                            var styleSet = profileService.GetListCellColor(this.Context, this.OperationContext.HtmlForm.Id, $"{entity.Id}color");
                            if (styleSet != null)
                            {
                                styleSet = styleSet.OrderBy(f => f.Priority).ToList();
                                uiMeta["styleSet"] = JArray.Parse(styleSet.ToJson());
                            }
                        }
                    }
                });
                var billUiMeta = uiConverter.CreateUIMetaObject(this.UserCtx, this.OperationContext.HtmlForm.Id, this.DomainType, billStatus == Enu_BillStatus.New, uiMetaAction);

                LockTokenInfo lockTokenInfo = null;
                if (tryLockBill && EnableBillLock(true))
                {
                    var numberFld = this.HtmlForm.GetNumberField();
                    var number = numberFld?.DynamicProperty?.GetValue<string>(dcDataEntity) ?? "";

                    this.BillLockService.TryLock(this.UserCtx, this.HtmlForm.Id, currPkId, number, out lockTokenInfo);
                }

                this.OperationContext.Result.SrvData = new
                {
                    Data = new
                    {
                        FormTopMenu = standarMenu,
                        FormBottomMenu = businessMenu,
                        uiMeta = billUiMeta,
                        billData = billData,
                        Status = billStatus,
                        LockTokenInfo = lockTokenInfo
                    }
                };
            }

            this.OperationContext.Result.IsSuccess = true;
            this.OperationContext.Result.IsShowMessage = true;
        }
    }
}