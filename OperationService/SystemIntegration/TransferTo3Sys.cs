using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.SystemIntegration;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using System.Collections.Generic;

namespace JieNor.Framework.AppService.OperationService.SystemIntegration
{
    /// <summary>
    /// 传输数据至第三方系统
    /// </summary>
    [InjectService("to3sys")]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public class TransferTo3Sys : AbstractOperationService
    {
        /// <summary>
        /// 系统集成插件代理
        /// </summary>
        protected List<ISyncDataTo3SysPlugIn> PlugIns { get; set; }

        /// <summary>
        /// 初始化操作参数，禁用事务
        /// </summary>
        protected override void InitializeOperationDataEntities(ref DynamicObject[] dataEntities)
        {
            this.OpCtlParam.DisableTransaction = true;
            base.InitializeOperationDataEntities(ref dataEntities);
        }

        /// <summary>
        /// 将请求转发至服务上进行处理
        /// </summary>
        /// <param name="lstOpServices"></param>
        protected override void PrepareBusinessService(List<FormServiceDesc> lstOpServices)
        {
            base.PrepareBusinessService(lstOpServices);
            lstOpServices.Add(new FormServiceDesc()
            {
                ServiceId = HtmlElementType.HtmlBizService_TransferTo3Sys,
                ServiceAlias = "数据集成服务",
                ParamString = "{'opcode':'save'}"
            });
        }

        /// <summary>
        /// 处理业务数据同步逻辑
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
        }
    }
}
