using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.AppService.QueryBuilder;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.Report;
using Newtonsoft.Json.Linq;
using JieNor.Framework.Consts;

namespace JieNor.Framework.AppService.OperationService
{


    /// <summary>
    /// 下拉列表的数据查询：主要用于下拉列表的数据加载显示
    /// 传递的 SimpleData 包含如下参数：
    ///     fieldkey ： 必填，查询的字段Key，
    ///     比如 销售订单上的 单据状态（假定为 FStatus） ，则 传递 FStatus 
    /// </summary>
    [InjectService("QueryCombo")]
    public class QueryCombo : AbstractOperationService
    {
        protected override string OperationName
        {
            get
            {
                return "";
            }
        }

        protected override string PermItem
        {
            get
            {
                return "";
            }
        }

        protected override void CheckPermission()
        {

        }

       

        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            //todo:根据前端传回的 fieldkey，searchkey，fetchcount，matchrule，得返回数据
            var key = GetQueryOrSimpleParam<string>("fieldkey");
            var comboField = this.OperationContext.HtmlForm.GetField(key) as HtmlComboField;
            if (comboField == null)
            {
                this.OperationContext.Result.IsSuccess = false;
                this.OperationContext.Result.SimpleMessage = $"不是有效的辅助资料字段！{key}";
                return;
            }

            var comSvc = this.Container.GetService<IComboDataService>();
            var enumDatas = comSvc.GetComboDatas(this.UserCtx, comboField.CategoryFilter, comboField.Filter);
               
            //设置返回的数据
            this.OperationContext.Result.SrvData = new
            {
                //Columns = lstColumns,                
                Data = enumDatas,
            };
            this.OperationContext.Result.IsSuccess = true;
        }

         


         



    }
}
