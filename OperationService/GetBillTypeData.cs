using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 获取指定表单的单据类型参数
    /// </summary>
    [InjectService("getbilltypedata")]
    public class GetBillTypeData : AbstractOperationService
    {
        /// <summary>
        /// 获取指定单据类型的数据
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var billTypeId = this.GetQueryOrSimpleParam<string>("billTypeId");
            if (dataEntities != null && dataEntities.Length == 1)
            {
                var billTypeField = this.OperationContext.HtmlForm.GetFieldList().FirstOrDefault(f => f is HtmlBillTypeField && f.Entity is HtmlHeadEntity);
                billTypeId = billTypeField.DynamicProperty.GetValue<string>(dataEntities[0]);
            }
            if (billTypeId.IsNullOrEmptyOrWhiteSpace())
            {
                this.OperationContext.Result.IsSuccess = false;
                this.OperationContext.Result.SimpleMessage = "当前数据上下文中获取不到单据类型Id，注意检查selectedRow.pkValue或者simpleData.billTypeId传递是否正确！";
            }

            var billTypeMeta = this.MetaModelService.LoadFormModel(this.UserCtx, "bd_billtype");
            var billTypeService = this.Container.GetService<IBillTypeService>();
            var billTypeObj = billTypeService.GetBillTypeById(this.UserCtx, billTypeId);

            var bizFormId = billTypeObj?["fbizobject"] as string;
            if (!bizFormId.IsNullOrEmptyOrWhiteSpace())
            {
                var bizForm = this.MetaModelService.LoadFormModel(this.UserCtx, bizFormId);
                var allFieldList = bizForm.GetFieldList();
                var billFields = allFieldList.Where(o => o.IsVisible(DataTransferObject.HtmlElementVisibleScene.Bill)).ToList();
                var entity = billTypeMeta.GetEntity("fentity");
                var entrys = billTypeObj["fentity"] as DynamicObjectCollection;
                if (entrys.Count <= 0)
                {
                    //将所有业务字段填充到单据类型明细中
                    foreach (var field in billFields)
                    {
                        this.FillBillTypeEntry(field, entity, entrys);
                    }
                }
                else
                {
                    //补齐单据类型明细中缺少的业务字段
                    var tempFields = new List<HtmlField>();
                    foreach (var field in billFields)
                    {
                        var entry = entrys.FirstOrDefault(o => Convert.ToString(o["ffieldid"]).EqualsIgnoreCase(field.Id));
                        if (entry == null) tempFields.Add(field);
                    }
                    foreach (var field in tempFields)
                    {
                        this.FillBillTypeEntry(field, entity, entrys);
                    }
                }
            }

            //var uidataService = this.Container.GetService<IUiDataConverter>();
            //var uiBillTypeData = uidataService.CreateUIDataObject(this.UserCtx, billTypeMeta, billTypeObj, this.OperationContext.Option);
            this.OperationContext.Result.IsSuccess = true;
            this.OperationContext.Result.SrvData = new
            {
                //uiBillTypeParam = uiBillTypeData.GetJsonValue<JObject>("uiData", new JObject()),
                uiBillTypeParam = billTypeService.CreateBillTypeUIDataObject(this.UserCtx, billTypeObj)
            };
        }

        /// <summary>
        /// 填充单据类型明细
        /// </summary>
        /// <param name="field"></param>
        /// <param name="entity"></param>
        /// <param name="entrys"></param>
        private void FillBillTypeEntry(HtmlField field, HtmlEntity entity, DynamicObjectCollection entrys)
        {
            var entry = new DynamicObject(entity.DynamicObjectType);
            entry["fgroupname"] = field.Entity.Caption;
            entry["ffieldid"] = field.Id;
            entry["ffieldid_txt"] = field.Caption;
            entry["fmustinput"] = field.MustInput.ToString();
            entry["flock"] = "";
            entry["flock_txt"] = "";

            switch (field.Lock)
            {
                case -1:
                    entry["flock"] = "-1";
                    entry["flock_txt"] = "全部锁定";
                    break;

                default:
                    if ((field.Lock & 1) == 1)
                    {
                        if (!entry["flock"].IsNullOrEmptyOrWhiteSpace())
                        {
                            entry["flock"] += ",1";
                            entry["flock_txt"] += ",新增时锁定";
                        }
                        else
                        {
                            entry["flock"] = "1";
                            entry["flock_txt"] = "新增时锁定";
                        }
                    }
                    if ((field.Lock & 2) == 2)
                    {
                        if (!entry["flock"].IsNullOrEmptyOrWhiteSpace())
                        {
                            entry["flock"] += ",2";
                            entry["flock_txt"] += ",修改时锁定";
                        }
                        else
                        {
                            entry["flock"] = "2";
                            entry["flock_txt"] = "修改时锁定";
                        }
                    }
                    break;
            }

            entrys.Add(entry);
        }
    }
}
