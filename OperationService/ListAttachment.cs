using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 显示附件列表
    /// </summary>

    [InjectService("ListAttachment")]
    public class ListAttachment : AbstractOperationService
    {
        /// <summary>
        /// 操作名称为空，不会产生操作成功的提示信息
        /// </summary>
        protected override string OperationName
        {
            get
            {
                return "";
            }
        }

        /// <summary>
        /// 本操作不检查权限
        /// </summary>
        protected override string PermItem
        {
            get
            {
                return null;
            }
        }

        /// <summary>
        /// 实现附件查看或新增的逻辑
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length == 0)
            {
                throw new BusinessException("请选择有效的表单数据后执行附件操作！");
            }

            if (dataEntities.Length > 1)
            {
                throw new BusinessException("不支持批量查看或上传表单关联附件！");
            }

            var currBillObj = dataEntities.FirstOrDefault();
            if (!currBillObj.DataEntityState.FromDatabase)
            {
                throw new BusinessException("请先保存表单后，再尝试打开附件进行操作！");
            }


            var attachFormMeta = this.MetaModelService.LoadFormModel(this.UserCtx, "bd_attachlist");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.UserCtx, attachFormMeta.GetDynamicObjectType(this.UserCtx));
            List<SqlParam> param = new List<SqlParam>
            {
                new SqlParam("linkFormId", System.Data.DbType.String, this.OperationContext.HtmlForm.Id),
                new SqlParam("linkBillId", System.Data.DbType.String, currBillObj["id"]),
            };
            string strWhere = " flinkformid=@linkFormId and flinkbillinterid=@linkBillId ";
            //让插件返回源单数据及同源表单数据
            var eventData = new OnCustomServiceEventArgs()
            {
                EventName = "getWhereString",
                EventData = new Dictionary<string, object>
                                {
                                    { "strWhere",strWhere },
                                    { "param",param }
                                },
                DataEntities= dataEntities
            };
            this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", eventData);
            strWhere = ((Dictionary<string, object>)eventData.EventData)["strWhere"] as string;
            param = ((Dictionary<string, object>)eventData.EventData)["param"] as List<SqlParam>;
            var pkIdReader = this.UserCtx.GetPkIdDataReader(attachFormMeta, strWhere, param);
            var linkAttachBillObj = dm.SelectBy(pkIdReader)
                .OfType<DynamicObject>()
                .FirstOrDefault();
            if (linkAttachBillObj == null)
            {
                linkAttachBillObj = dm.DataEntityType.CreateInstance() as DynamicObject;
                linkAttachBillObj["flinkformid"] = this.OperationContext.HtmlForm.Id;
                var numberField = this.OperationContext.HtmlForm.GetNumberField();
                linkAttachBillObj["flinkbillno"] = numberField?.DynamicProperty?.GetValue(currBillObj);
                linkAttachBillObj["flinkbillinterid"] = currBillObj["id"];
                var tranIdField = this.OperationContext.HtmlForm.GetField(this.OperationContext.HtmlForm.TranFldKey);
                linkAttachBillObj["flinkbilltranid"] = tranIdField?.DynamicProperty?.GetValue(currBillObj);

                var prepareService = this.Container.GetService<IPrepareSaveDataService>();
                prepareService.PrepareDataEntity(this.UserCtx, attachFormMeta, new DynamicObject[] { linkAttachBillObj }, this.OperationContext.Option);
                dm.Save(linkAttachBillObj);
            }
            //设置页面打开方式（如果前端有传递，则用传递的方式打开，否则按默认的方式打开）
            var action = this.OperationContext.UserContext.ShowSpecialForm(attachFormMeta,
                linkAttachBillObj,
                false,
                this.OperationContext.PageId,
                Enu_OpenStyle.Modal,
                Enu_DomainType.Bill,
                this.GlobalParameter,
                (formPara) =>
                {
                    formPara.FormCaption = $"{this.OperationContext.HtmlForm.Caption} - 附件列表";

                    var dctShowPara = this.OperationContext.Option.GetFormShowParameter();
                    if (dctShowPara != null)
                    {
                        formPara.CustomParameter.Merge(dctShowPara);
                    }
                },
                GetQueryOrSimpleParam<string>("containerId"));

            this.OperationContext.Result.HtmlActions.Add(action);

            this.OperationContext.Result.IsSuccess = true;

        }
    }
}
