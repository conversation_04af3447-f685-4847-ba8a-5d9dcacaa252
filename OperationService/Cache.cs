using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using System;

namespace JieNor.Framework.AppService.OperationService
{

    /// <summary>
    /// 缓存测试操作：缓存测试
    /// </summary>
    [InjectService("Cache")]
    public class Cache : AbstractOperationService
    {
        protected override string OperationName
        {
            get
            {
                return "缓存操作";
            }
        }
        protected override string PermItem
        {
            get
            {
                return "";
            }
        }
        protected override void AfterExecute(ref DynamicObject[] dataEntities)
        {
            base.AfterExecute(ref dataEntities);
        }
        protected override void BeforeExecute(ref DynamicObject[] dataEntities)
        {
            base.BeforeExecute(ref dataEntities);
        }
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        { 
            //移除指定标识的缓存
            var key = this.GetQueryOrSimpleParam<string>("key","");

            if (key.EqualsIgnoreCase("*"))
            {
                this.Cache.RemoveAll(this.UserCtx);
                this.CacheMem.RemoveAll(this.UserCtx);
            }
            else if (!key.StartsWith ("*") && key.EndsWith("*"))
            {
                var xkey = key.Substring (0,key.Length -1);
                this.Cache.RemoveAllByKeyPattern(this.UserCtx, xkey, Enu_SearchType.StartWith);
                this.CacheMem.RemoveAllByKeyPattern(this.UserCtx, xkey, Enu_SearchType.StartWith);
            }
            else if (key.StartsWith("*") && !key.EndsWith("*"))
            {
                var xkey = key.Substring(1);
                this.Cache.RemoveAllByKeyPattern(this.UserCtx, xkey, Enu_SearchType.EndWith);
                this.CacheMem.RemoveAllByKeyPattern(this.UserCtx, xkey, Enu_SearchType.EndWith);
            }
            else if (!key.IsNullOrEmptyOrWhiteSpace())
            {
                this.Cache.RemoveAllByKeyPattern(this.UserCtx, key, Enu_SearchType.RegEx);
                this.CacheMem.RemoveAllByKeyPattern(this.UserCtx, key, Enu_SearchType.RegEx);
            }
             
            var formId = this.GetQueryOrSimpleParam<string>("formId");
            if (!formId.IsNullOrEmptyOrWhiteSpace())
            {
                //var formMeta = this.MetaModelService.LoadFormModel(this.OperationContext.UserContext, formId);
                //if (formMeta != null)
                //{
                //    this.CacheOrm?.RemoveAllDcData(this.UserCtx, formMeta.GetDynamicObjectType(this.OperationContext.UserContext));
                //}
            }

        }

    }
}
