using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 设为共享
    /// </summary>
    [InjectService("topreset")]
    public class ToPreset : AbstractOperationService
    {
        /// <summary>
        /// 设置操作名称
        /// </summary>
        protected override string OperationName => "设为共享";

        /// <summary>
        /// 自动保存数据
        /// </summary>
        protected override bool AutoSaveData => true;

        /// <summary>
        /// 业务表单
        /// </summary>
        protected HtmlForm HtmlForm => this.OperationContext.HtmlForm;

        /// <summary>
        /// 处理预置数据逻辑
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                throw new BusinessException("设为共享操作失败：请至少选择一条数据再操作！");
            }

            var isPresetField = this.HtmlForm.GetField("fispreset");
            if (isPresetField == null)
            {
                throw new BusinessException("当前业务对象单据头没有系统预设字段(fispreset)，不支持设为共享操作！");
            }

            var mainOrgIdField = this.HtmlForm.GetField("fmainorgid");
            var primitiveIdField = this.HtmlForm.GetField("fprimitiveid");
            var numberField = this.HtmlForm.GetNumberField();

            var preService = this.Container.GetService<IPrepareSaveDataService>();

            var dataMgr = this.GetDataManager();
            dataMgr.InitDbContext(this.UserCtx, this.HtmlForm.GetDynamicObjectType(this.UserCtx));

            foreach (var dataEntity in dataEntities)
            {
                var number = numberField.DynamicProperty.GetValue<string>(dataEntity);
                var isPreset = isPresetField.DynamicProperty.GetValue<bool>(dataEntity);
                var mainOrgId = mainOrgIdField.DynamicProperty.GetValue<string>(dataEntity);
                if (mainOrgId.EqualsIgnoreCase("0"))
                {
                    //已经是预置，不再处理
                    if (isPreset == true)
                    {
                        this.OperationContext.Result.ComplexMessage.ErrorMessages.Add($"{this.HtmlForm.Caption}【{number}】已经设置过共享！");
                        continue;
                    }
                    //设为预置
                    isPresetField.DynamicProperty.SetValue(dataEntity, true);
                }
                else
                {
                    var primitiveId = primitiveIdField.DynamicProperty.GetValue<string>(dataEntity);
                    if (primitiveId.IsNullOrEmptyOrWhiteSpace())
                    {
                        //设为预置
                        isPresetField.DynamicProperty.SetValue(dataEntity, true);
                        //将组织设置为0，作为全局共享
                        mainOrgIdField?.DynamicProperty?.SetValue(dataEntity, "0");
                    }
                    else
                    {
                        if (!this.OperationContext.Option.HasInteractionFlag("confirmtopreset"))
                        {
                            throw new InteractionException("confirmtopreset", "此数据是基于系统预置数据保存的，继续设为共享将会以此数据来覆盖系统预置数据，确认继续吗？");
                        }

                        var sysPrsetObj = dataMgr.Select(primitiveId) as DynamicObject;
                        if (sysPrsetObj != null)
                        {
                            //清空所有明细
                            foreach (var entity in this.HtmlForm.EntryList)
                            {
                                entity?.DynamicProperty?.GetValue<DynamicObjectCollection>(sysPrsetObj)?.Clear();
                            }

                            //用当前数据覆盖关联的系统预置数据
                            dataEntity.CopyTo(sysPrsetObj, null, true, false, false,
                            (newObj, oldObj) =>
                            {

                            },
                            (dtName, oldObj, newObjs) =>
                            {
                                return true;
                            });

                            //保证预置数据主键Id不变
                            sysPrsetObj["id"] = primitiveId;

                            preService.PrepareDataEntity(this.UserCtx, this.HtmlForm, new DynamicObject[] { sysPrsetObj }, OperateOption.Create());

                            //清空原始Id字段值
                            primitiveIdField?.DynamicProperty?.SetValue(sysPrsetObj, "");
                            //设为预置
                            isPresetField.DynamicProperty.SetValue(sysPrsetObj, true);
                            //将组织设置为0，作为全局共享
                            mainOrgIdField?.DynamicProperty?.SetValue(sysPrsetObj, "0");

                            dataMgr.Save(sysPrsetObj);
                        }
                    }
                }
            }
        }
    }
}
