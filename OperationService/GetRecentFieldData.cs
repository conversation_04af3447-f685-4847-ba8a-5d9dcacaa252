using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 获取某表单字段的最近录入值
    /// </summary>
    [InjectService("getrecentfielddata")]
    public class GetRecentFieldData : AbstractOperationService
    {
        protected override string OperationName => "获取字段最近录入值";

        protected override bool IgnoreOpMessage => true;

        /// <summary>
        /// 处理操作逻辑
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var fieldId = this.GetQueryOrSimpleParam<string>("fieldKey");
            if (fieldId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("参数错误：字段标识不可以为空(fieldKey)！");
            }

            var field = this.OperationContext.HtmlForm.GetField(fieldId);
            if (field == null)
            {
                throw new BusinessException($"参数错误：传入的字段标识在模型中未定义（{fieldId}）！");
            }
            //频度，使用过3次的内容都查出来
            var frequency = this.GetQueryOrSimpleParam<int>("frequency");
            if (frequency <= 0)
            {
                frequency = 3;
            }

            var topCount = this.GetQueryOrSimpleParam<int>("topcount");
            if (topCount <= 0)
            {
                topCount = 5;
            }
            var queryColObjs = this.OperationContext.HtmlForm.GetListColumnObject(this.UserCtx)
                .Where(o => o.Value.FieldId.EqualsIgnoreCase(fieldId))
                .Select(o => o.Value)
                .ToList();
            
            SqlBuilderParameter queryPara = new SqlBuilderParameter(this.OperationContext.UserContext, this.OperationContext.HtmlForm);
            queryPara.QueryUserFieldOnly = true;
            queryPara.SelectedFieldKeys.AddRange(queryColObjs.Select(o => o.Id));
            queryPara.PageIndex = -1;
            queryPara.PageCount = -1;
            queryPara.IsDistinct = true;
            queryPara.NoColorSetting = true;

            var queryObject = QueryService.BuilQueryObject(queryPara);

            var strSql = $@"
select top {topCount} *
from(
{queryObject.SqlSelect},count(1) fusecount
{queryObject.SqlFrom}
{queryObject.SqlWhere}
group by {queryObject.SqlField}
having count(1) >{frequency}
) t
order by t.fusecount desc
";
            List<Dictionary<string,object>> lstFieldVals = new List<Dictionary<string, object>>();
            using(var reader = this.DBService.ExecuteReader(this.UserCtx, strSql, queryPara.DynamicParams))
            {
                while (reader.Read())
                {
                    Dictionary<string, object> dctObj = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
                    for(int i = 0; i < reader.FieldCount; i++)
                    {
                        var colname = reader.GetName(i);
                        if (colname.EqualsIgnoreCase("fusecount")) continue;
                        dctObj[colname] = reader[i];
                    }
                    lstFieldVals.Add(dctObj);
                }
            }

            this.OperationContext.Result.SrvData = lstFieldVals;
            this.OperationContext.Result.IsSuccess = true;

        }
    }
}
