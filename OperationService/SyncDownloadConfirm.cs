using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.CustomException;
using Newtonsoft.Json;
using JieNor.Framework.SuperOrm;
using Newtonsoft.Json.Linq;
using JieNor.Framework.SuperOrm.Serialization;
using JieNor.Framework.MetaCore.FormMeta;
using System.Collections.Specialized;
using JieNor.Framework.MetaCore;
using Autofac.Features.Metadata;
using System.Text.RegularExpressions;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Autofac;
using JieNor.Framework.Consts;

namespace JieNor.Framework.AppService.OperationService
{
    ///// <summary>
    ///// 基础资料下载确定操作
    ///// </summary>
    //[InjectService("SyncDownloadConfirm")]
    //public class SyncDownloadConfirm : AbstractOperationService
    //{
    //    List<object> publishChaindatas = new List<object>();

    //    /// <summary>
    //    /// 
    //    /// </summary>
    //    /// <param name="dataEntities"></param>
    //    protected override void DoExecute(ref DynamicObject[] dataEntities)
    //    {
    //        string billDataJsonStr = this.GetQueryOrSimpleParam<string>("billDataJson");

    //        JArray jArray = null;

    //        if (false == string.IsNullOrWhiteSpace(billDataJsonStr))
    //        {
    //            jArray= JArray.Parse(billDataJsonStr);
    //        }

    //        if (jArray == null || jArray.Count <= 0)
    //        {
    //            this.OperationContext.Result.IsSuccess = false;
    //            this.OperationContext.Result.IsShowMessage = true;
    //            this.OperationContext.Result.SimpleMessage = "请传递需要下载的资料";
    //            return;
    //        }

    //        List<Exception> exceptions = new List<Exception>();
    //        List<string> loadedChainDataIds = jArray.Select(x => (string)x["chainDataId"]).Where(x => !string.IsNullOrWhiteSpace(x)).ToList();
    //        Dictionary<string, PlugInProxy<IOperationServicePlugIn>> pluginProxyCache = new Dictionary<string, PlugInProxy<IOperationServicePlugIn>>();
    //        List<Dictionary<string, object>> records = this.OperationContext.Option.GetVariableValue("records", new List<Dictionary<string, object>>());
    //        List<DynamicObject> rollbackEntities = new List<DynamicObject>();

    //        try
    //        {
    //            dealDownLoadData(this.UserCtx, jArray, records, loadedChainDataIds, exceptions, publishChaindatas, rollbackEntities, pluginProxyCache);
    //        }
    //        catch (Exception ex)
    //        {
    //            exceptions.Add(ex);
    //        }

    //        if (exceptions.Count <= 0)
    //        {
    //            this.OperationContext.Result.IsSuccess = true;
    //        }
    //        else
    //        {
    //            rollbackOperationData(rollbackEntities);
    //            this.OperationContext.Result.SimpleMessage = "下载失败!";
    //            this.OperationContext.Result.IsSuccess = false;
    //            this.OperationContext.Result.ComplexMessage.ErrorMessages.AddRange(exceptions.Select(x => x.ToString()));
    //        }

    //    }

    //    protected override void AfterExecute(ref DynamicObject[] dataEntities)
    //    {
    //        base.AfterExecute(ref dataEntities);

    //        if (publishChaindatas == null || publishChaindatas.Count <= 0 || this.OperationContext.Result.ComplexMessage.ErrorMessages.Count > 0)
    //        {
    //            return;
    //        }

    //        Gateway.Invoke(this.UserCtx,
    //        TargetSEP.EisService,
    //            new CommonBillDTO()
    //            {
    //                FormId = "syn_shareddata",
    //                OperationNo = "downloadpublish",
    //                SimpleData = new Dictionary<string, string> { { "downloadData", JsonConvert.SerializeObject(publishChaindatas) } }
    //            });
    //    }

    //    private static void dealDownLoadData(UserContext userContext,
    //                                         JArray jArray,
    //                                         List<Dictionary<string, object>> records,
    //                                         List<string> loadedChainDataIds,
    //                                         List<Exception> exceptions,
    //                                         List<object> publishChaindatas,
    //                                         List<DynamicObject> rollbackEntities,
    //                                         Dictionary<string, PlugInProxy<IOperationServicePlugIn>> pluginProxyCache)
    //    {
    //        if (jArray == null || jArray.Count <= 0)
    //        {
    //            return;
    //        }

    //        loadedChainDataIds.AddRange(jArray.Select(x => (string)x["chainDataId"]).Where(x => !string.IsNullOrWhiteSpace(x)));

    //        var billFormDatas = jArray.Where(x => !string.IsNullOrWhiteSpace((string)x["dataContent"]) &&
    //                                              !string.IsNullOrWhiteSpace((string)x["billFormId"]) &&
    //                                              !string.IsNullOrWhiteSpace((string)x["dataId"]) &&
    //                                              !string.IsNullOrWhiteSpace((string)x["chainDataId"]) &&
    //                                              !string.IsNullOrWhiteSpace((string)x["publishCompanyId"]) &&
    //                                              !string.IsNullOrWhiteSpace((string)x["publishProductId"]) &&
    //                                              !(string.Equals((string)x["publishCompanyId"], userContext.Company) &&
    //                                              string.Equals((string)x["publishProductId"], userContext.Product))).ToList();

    //        if (billFormDatas == null || billFormDatas.Count <= 0)
    //        {
    //            return;
    //        }

    //        IMetaModelService metaModelService = userContext.Container.GetService<IMetaModelService>();
    //        IHttpServiceInvoker gateway = userContext.Container.GetService<IHttpServiceInvoker>();
    //        PlugInProxy<IOperationServicePlugIn> pluginProxy = null;
    //        ISequenceService seqService = userContext.Container.GetService<ISequenceService>();
    //        var dcSerializer = userContext.Container.GetService<IDynamicSerializer>();

    //        var billFormGroup = billFormDatas
    //                            .Where(x => records.Any(y => (string)y["chainDataId"] == (string)x["chainDataId"]) == false)
    //                            .Select(x =>
    //                            {
    //                                var dataContent = JObject.Parse((string)x["dataContent"]);
    //                                JObject linkChainData = null;
    //                                string linkChainDataJsonString = (string)x["linkChainData"];
    //                                if (string.IsNullOrWhiteSpace(linkChainDataJsonString) == false)
    //                                {
    //                                    linkChainData = JObject.Parse(linkChainDataJsonString);
    //                                }

    //                                if (linkChainData == null)
    //                                {
    //                                    linkChainData = new JObject();
    //                                }

    //                                return new Dictionary<string, object>
    //                                {
    //                                    { "billFormId", (string)x["billFormId"] },
    //                                    { "dataId", (string)x["dataId"] },
    //                                    { "dataName",(string)x["dataName"]},
    //                                    { "dataNumber",(string)x["dataNumber"]},
    //                                    { "chainDataId", (string)x["chainDataId"] },
    //                                    { "publishCompanyId", (string)x["publishCompanyId"] },
    //                                    { "publishProductId", (string)x["publishProductId"] },
    //                                    { "publishCompany",(string)x["publishCompany"]},
    //                                    { "dataContent", dataContent },
    //                                    { "linkChainData", linkChainData },
    //                                    { "publishStatus",(string)x["publishStatus"]},
    //                                    { "updateToken",(string)x["updateToken"]},
    //                                    { "localDataId",null},
    //                                    { "dataEntity",null}
    //                                };
    //                            }).GroupBy(x => Convert.ToString(x["billFormId"])).ToList();


    //        foreach(var groupItem in billFormGroup)
    //        {
    //            records.AddRange(groupItem);
    //            var htmlForm = metaModelService.LoadFormModel(userContext, groupItem.Key);
    //            pluginProxy = getPlugInProxy(userContext, pluginProxyCache, htmlForm.Id);

    //            var unDistributes = groupItem.Where(x => Convert.ToString(x["publishStatus"]) == "2").ToList();
    //            if (unDistributes != null && unDistributes.Count > 0)
    //            {
    //                var unDistributeGroup = unDistributes.GroupBy(x => (string)x["updateToken"]).ToList();
    //                foreach(var unItems in unDistributeGroup)
    //                {
    //                    var eventName = string.Format("undistribute_{0}", unItems.Key);
    //                    var unDistributeDownloadED = new OnCustomServiceEventArgs()
    //                    {
    //                        EventName = eventName,
    //                        EventData = new Dictionary<string, object>
    //                                {
    //                                    { "htmlForm", htmlForm},
    //                                    { "billDatas",unItems.ToList()},
    //                                    { "userContext",userContext}
    //                                },
    //                    };
    //                    pluginProxy.InvokePlugInMethod("OnCustomServiceEvent", unDistributeDownloadED);

    //                    var result = unDistributeDownloadED.Result as List<Dictionary<string, string>>;
    //                    foreach (var data in unItems)
    //                    {
    //                        var chainDataId = (string)data["chainDataId"];
    //                        var resultItem = result?.FirstOrDefault(x => Convert.ToString(x["chainDataId"]) == chainDataId);
    //                        var publishData = new
    //                        {
    //                            localDataId = resultItem == null ? string.Empty : resultItem["localDataId"],
    //                            localDataNumber = resultItem == null ? string.Empty : resultItem["localDataNumber"],
    //                            localDataName = resultItem == null ? string.Empty : resultItem["localDataName"],
    //                            chainDataId = chainDataId
    //                        };
    //                        publishChaindatas.Add(publishData);
    //                    }
    //                }
    //            }

    //            var datas= groupItem.Where(x => Convert.ToString(x["publishStatus"]) == "1").ToList();

    //            if (datas == null || datas.Count <= 0)
    //            {
    //                continue;
    //            }


    //            var dealDataContentED = new OnCustomServiceEventArgs()
    //            {
    //                EventName = "dealDataContents",
    //                EventData = new Dictionary<string, object>
    //                            {
    //                                { "htmlForm",htmlForm },
    //                                { "billDatas",datas.ToList()},
    //                                { "userContext",userContext}
    //                            },
    //            };
    //            pluginProxy.InvokePlugInMethod("OnCustomServiceEvent", dealDataContentED);

    //            var linkChainDataIds = datas.Where(x => x["linkChainData"] != null)//过滤出有依赖的选项
    //                                    .SelectMany(x =>
    //                                    {
    //                                        return ((JObject)x["linkChainData"]).Properties().Select(y => y.Value.Value<string>());
    //                                    })
    //                                    .Distinct()
    //                                    .Where(x => !loadedChainDataIds.Any(y => string.Equals(y, x, StringComparison.CurrentCultureIgnoreCase)))//筛选出未加载过的chaindataid
    //                                    .ToList();

    //            if (linkChainDataIds != null && linkChainDataIds.Count > 0)
    //            {
    //                #region 向EIS站点下载依赖数据包

    //                var response = gateway.Invoke(userContext,
    //                    TargetSEP.EisService,
    //                    new CommonBillDTO()
    //                    {
    //                        FormId = "syn_shareddata",
    //                        OperationNo = "downloadlinkdata",
    //                        SimpleData = new Dictionary<string, string> { { "chainDataIds", string.Join(",", linkChainDataIds) } }
    //                    }) as CommonBillDTOResponse;

    //                if (response == null || response.OperationResult == null)
    //                {
    //                    throwException(exceptions, "系统未返回任何响应！");
    //                    return;
    //                }

    //                #endregion

    //                if (response.OperationResult.IsSuccess && response.OperationResult.SrvData != null)
    //                {
    //                    JArray srvData = JArray.Parse(response.OperationResult.SrvData.ToString());
    //                    dealDownLoadData(userContext, srvData, records, loadedChainDataIds, null, publishChaindatas, rollbackEntities, pluginProxyCache);
    //                }
    //                else
    //                {
    //                    throwException(exceptions, "下载失败依赖失败!");
    //                    return;
    //                }
    //            }

    //            var dm = userContext.Container.GetService<IDataManager>();
    //            var dt = htmlForm.GetDynamicObjectType(userContext);
    //            dm.InitDbContext(userContext, dt);

    //            var fchaindataids = datas.Select(x => (string)x["chainDataId"]).ToList();

    //            StringBuilder where = new StringBuilder("fmainorgid = @fmainorgid and ");
    //            var sqlParam = new List<SqlParam>
    //            {
    //                new SqlParam("@fmainorgid",System.Data.DbType.String,userContext.Company)
    //            };
    //            //根据fchaindataids的个数调整sql语句的查询条件
    //            if (fchaindataids.Count == 1)
    //            {
    //                where.Append("ffromchaindataid = @fchaindataid");
    //                sqlParam.Add(new SqlParam("@fchaindataid", System.Data.DbType.String, fchaindataids[0]));
    //            }
    //            else
    //            {
    //                where.Append("ffromchaindataid in (");
    //                where.Append(string.Join(",", fchaindataids.Select((x, i) => string.Format("@fchaindataid{0}", i))));
    //                where.Append(")");
    //                sqlParam.AddRange(fchaindataids.Select((x, i) => new SqlParam(string.Format("@fchaindataid{0}", i), System.Data.DbType.String, x)));
    //            }
    //            var dataReader = userContext.GetPkIdDataReader(htmlForm, where.ToString(), sqlParam);
    //            var dynamicObjects = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();
    //            rollbackEntities.AddRange(dynamicObjects);

    //            if (dynamicObjects == null)
    //            {
    //                dynamicObjects = new List<DynamicObject>();
    //            }

    //            JArray billDatas = new JArray();

    //            foreach (var item in datas)
    //            {
    //                var dataContent = item["dataContent"] as JObject;
    //                var linkChainData = item["linkChainData"] as JObject;
    //                List<JProperty> formLinkChainData = null;

    //                if (linkChainData != null && linkChainData.Count > 0)
    //                {
    //                    var getFormLinkChainDataED = new OnCustomServiceEventArgs()
    //                    {
    //                        EventName = "getFormLinkChainData",
    //                        EventData = new Dictionary<string, object>
    //                        {
    //                            { "htmlForm",htmlForm },
    //                            { "dataContent",dataContent},
    //                            { "linkChainData",linkChainData},
    //                            { "userContext",userContext}
    //                        },
    //                    };
    //                    pluginProxy.InvokePlugInMethod("OnCustomServiceEvent", getFormLinkChainDataED);
    //                    formLinkChainData = getFormLinkChainDataED.Result as List<JProperty>;
    //                    if (formLinkChainData == null)
    //                    {
    //                        formLinkChainData = linkChainData.Properties().ToList();
    //                    }
    //                }

    //                if (formLinkChainData != null && formLinkChainData.Count > 0)
    //                {
    //                    foreach (var prop in formLinkChainData)
    //                    {
    //                        var chainDataId = (string)linkChainData[prop.Name];
    //                        var record = records.FirstOrDefault(x => x["chainDataId"].ToString() == chainDataId);
    //                        var localDataId = string.Empty;

    //                        if (record != null)
    //                        {
    //                            localDataId = (string)record["localDataId"];
    //                        }

    //                        dealId(userContext, dataContent, prop.Name, localDataId, htmlForm, chainDataId, records);
    //                    }
    //                }

    //                var fchaindataid = (string)item["chainDataId"];
    //                dataContent["ffromchaindataid"] = fchaindataid;
    //                dataContent["fpublishcid_txt"] = (string)item["publishCompany"];
    //                dataContent["fpublishcid"] = (string)item["publishCompanyId"];
    //                dataContent["fpublishcid_pid"] = (string)item["publishProductId"];

    //                var entity = dynamicObjects.FirstOrDefault(x => Convert.ToString(x["ffromchaindataid"]) == fchaindataid);
    //                if (entity != null)
    //                {
    //                    dealId(dataContent, entity, htmlForm, seqService);
    //                }
    //                billDatas.Add(dataContent);
    //            }

    //            dcSerializer.Sync(dt, dynamicObjects, billDatas, (propKey) =>
    //            {
    //                var el = htmlForm?.GetElement(propKey);
    //                if (el is HtmlField) return (el as HtmlField).DynamicProperty;
    //                if (el is HtmlEntryEntity) return (el as HtmlEntryEntity).DynamicProperty;

    //                return null;
    //            },
    //             null,
    //             null,
    //             null);

    //            //保存前预处理
    //            var prepareService = userContext.Container.GetService<IPrepareSaveDataService>();
    //            prepareService?.PrepareDataEntity(userContext, htmlForm, dynamicObjects.ToArray(), OperateOption.Create());

    //            //通知插件分析表单模型无法分析的依赖资料
    //            var dealDataEntitiesED = new OnCustomServiceEventArgs()
    //            {
    //                EventName = "dealDataEntities",
    //                EventData = new Dictionary<string, object>
    //                            {
    //                                { "userContext",userContext},
    //                                { "htmlForm",htmlForm },
    //                                { "dataEntities",dynamicObjects}
    //                            },
    //            };
    //            pluginProxy.InvokePlugInMethod("OnCustomServiceEvent", dealDataEntitiesED);

    //            DateTime downloadDate = DateTime.Now;
    //            foreach (var item in dynamicObjects)
    //            {
    //                item["fdataorigin"] = "下载";
    //                item["fdownloaddate"] = downloadDate;
    //            }

    //            //准备本地下载资料信息上传到云链
    //            foreach (var dataEntity in dynamicObjects)
    //            {
    //                string chainDataId = Convert.ToString(dataEntity["ffromchaindataid"]);
    //                string localDataId = Convert.ToString(dataEntity["id"]);
    //                var record = records.FirstOrDefault(x => (string)x["chainDataId"] == chainDataId);
    //                record["localDataId"] = localDataId;
    //                record["dataEntity"] = dataEntity;
    //                var data = new
    //                {
    //                    localDataId = localDataId,
    //                    localDataNumber = Convert.ToString(dataEntity[htmlForm.NumberFldKey]),
    //                    localDataName = Convert.ToString(dataEntity[htmlForm.NameFldKey]),
    //                    chainDataId = chainDataId
    //                };
    //                publishChaindatas.Add(data);
    //                dealId(userContext, localDataId, chainDataId, records, metaModelService);
    //            }

    //            var saveEd = new OnCustomServiceEventArgs()
    //            {
    //                EventName = "saveDataEntities",
    //                EventData = new Dictionary<string, object>
    //                {
    //                    { "htmlFormId",htmlForm.Id },
    //                    { "userContext",userContext},
    //                    { "records",records},
    //                    { "dataEntities",dynamicObjects},
    //                    { "rollbackEntities",rollbackEntities},
    //                    { "billDatas",datas.ToList()}
    //                },
    //            };
    //            pluginProxy.InvokePlugInMethod("OnCustomServiceEvent", saveEd);

    //            if ((saveEd.Result as string) == "OK")
    //            {
    //                continue;
    //            }


    //            dm.Save(dynamicObjects);
    //        }
    //    }

    //    private void rollbackOperationData(List<DynamicObject> dataEntites)
    //    {
    //        foreach (var dataEntity in dataEntites)
    //        {
    //            if (dataEntity.DataEntityState.FromDatabase == false)
    //            {
    //                continue;
    //            }
    //            var pkId = dataEntity.DynamicObjectType.PrimaryKey.GetValue<string>(dataEntity);
    //            this.CacheOrm.RemoveDcData(this.UserCtx, dataEntity.DynamicObjectType, pkId);
    //        }
    //    }

    //    private static void dealId(JObject dataContent, DynamicObject dataEntity, HtmlForm htmlForm, ISequenceService seqService)
    //    {
    //        removeIdKey(dataContent, null);
    //        string dataEntityId = Convert.ToString(dataEntity["id"]);

    //        if (string.IsNullOrWhiteSpace(dataEntityId))
    //        {
    //            dataEntityId = seqService.GetSequence<string>();
    //            dataEntity["id"] = dataEntityId;
    //        }

    //        dataContent["id"] = dataEntityId;

    //        if (htmlForm.EntryList == null || htmlForm.EntryList.Count <= 0)
    //        {
    //            return;
    //        }

    //        foreach (var entry in htmlForm.EntryList)
    //        {
    //            if (entry is HtmlSubEntryEntity)
    //            {
    //                continue;
    //            }
    //            var entryArray = dataContent[entry.Id] as JArray;
    //            if (entryArray == null || entryArray.Count <= 0)
    //            {
    //                continue;
    //            }
    //            dealId(entryArray, dataEntity, entry.Id);
    //            foreach (var entryItem in entryArray)
    //            {
    //                var entryObject = entryItem as JObject;
    //                var entryEntity = dealId(entryObject, dataEntity, entry.Id, seqService);
    //                if (entry.SubEntryList == null || entry.SubEntryList.Count <= 0)
    //                {
    //                    continue;
    //                }
    //                foreach (var subEntry in entry.SubEntryList)
    //                {
    //                    var subEntryArray = entryObject[subEntry.Id] as JArray;
    //                    if (subEntryArray == null || subEntryArray.Count <= 0)
    //                    {
    //                        continue;
    //                    }
    //                    dealId(subEntryArray, entryEntity, subEntry.Id);
    //                    foreach (var subEntryItem in subEntryArray)
    //                    {
    //                        var subEntryObject = subEntryItem as JObject;
    //                        dealId(subEntryObject, entryEntity, subEntry.Id, seqService);
    //                    }
    //                }
    //            }
    //        }
    //    }

    //    private static void dealId(JArray dataContents, DynamicObject dataEntity, string entryId)
    //    {
    //        var tranIds = dataContents.Select(x =>
    //        {
    //            var dataContent = x as JObject;
    //            var property = dataContent.Properties().FirstOrDefault(y => string.Equals(y.Name, $"{entryId}_ftranid", StringComparison.OrdinalIgnoreCase));
    //            if (property == null)
    //            {
    //                return string.Empty;
    //            }
    //            return (string)property.Value;
    //        }).Where(x => string.IsNullOrWhiteSpace(x) == false).ToList();

    //        if (tranIds == null)
    //        {
    //            tranIds = new List<string>();
    //        }

    //        var entries = dataEntity[entryId] as DynamicObjectCollection;
    //        var removeEntries = entries.Where(x =>
    //        {
    //            var tranId = Convert.ToString(x["fparenttranid"]);
    //            return false == string.IsNullOrWhiteSpace(tranId) && false == tranIds.Contains(tranId) && "下载" == Convert.ToString(x["fdataorigin"]);
    //        }).ToList();

    //        if (removeEntries == null || removeEntries.Count <= 0)
    //        {
    //            return;
    //        }

    //        foreach (var reomveEntry in removeEntries)
    //        {
    //            entries.Remove(reomveEntry);
    //        }

    //    }

    //    private static DynamicObject dealId(JObject dataContent, DynamicObject dataEntity, string entryId, ISequenceService seqService)
    //    {
    //        string entryTranId = removeIdKey(dataContent, entryId);
    //        var entryCollection = dataEntity[entryId] as DynamicObjectCollection;
    //        DynamicObject entryEntity = null;

    //        if (false == string.IsNullOrWhiteSpace(entryTranId))
    //        {
    //            entryEntity = entryCollection.FirstOrDefault(x => Convert.ToString(x["fparenttranid"]) == entryTranId);
    //        }

    //        if (entryEntity == null)
    //        {
    //            entryEntity = entryCollection.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
    //            entryEntity["id"] = seqService.GetSequence<string>();
    //            entryCollection.Add(entryEntity);
    //        }

    //        dataContent["id"] = Convert.ToString(entryEntity["id"]);
    //        return entryEntity;
    //    }

    //    private static void dealId(UserContext userContext,
    //                               string newId,
    //                               string chainDataId,
    //                               List<Dictionary<string, object>> records,
    //                               IMetaModelService metaModelService)
    //    {
    //        var datas = records.Where(x => x["dataEntity"] != null &&
    //                                       x["linkChainData"] != null &&
    //                                       ((JObject)x["linkChainData"]).PropertyValues().Any(y => (string)y == chainDataId))
    //                           .GroupBy(x => (string)x["billFormId"])
    //                           .Select(x => new
    //                           {
    //                               billFormId = x.Key,
    //                               fieldId = ((JObject)(x.FirstOrDefault()["linkChainData"])).Properties().FirstOrDefault(z => (string)z.Value == chainDataId).Name,
    //                               dataEntities = x.Select(y => y["dataEntity"] as DynamicObject).ToList()
    //                           }).ToList();

    //        foreach (var data in datas)
    //        {
    //            var htmlForm = metaModelService.LoadFormModel(userContext, data.billFormId);
    //            var fieldIds = split(data.fieldId);//data.fieldId.Split('_');
    //            var fldIds = fieldIds[0].Split(new[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
    //            ExtendedDataEntitySet dataEntitySet = new ExtendedDataEntitySet();
    //            dataEntitySet.Parse(userContext, data.dataEntities, htmlForm);

    //            var fields = htmlForm.GetFieldList().Where(x => x.Id == fldIds[0] && x is IRefFormField).ToList();

    //            if (fields == null || fields.Count <= 0)
    //            {
    //                continue;
    //            }

    //            foreach (var field in fields)
    //            {
    //                var refFormField = field as IRefFormField;
    //                var form = metaModelService.LoadFormModel(userContext, refFormField.RefFormId);
    //                var setDataEntities = dataEntitySet.FindByEntityKey(field.EntityKey);

    //                List<DynamicObject> auxPropCache = new List<DynamicObject>();

    //                foreach (var item in setDataEntities)
    //                {
    //                    if (field is HtmlBaseDataField)
    //                    {
    //                        var id = field.DynamicProperty.GetValue<string>(item.DataEntity);
    //                        if (id == newId)
    //                        {
    //                            continue;
    //                        }
    //                        if (id == fieldIds[1])
    //                        {
    //                            field.DynamicProperty.SetValue(item.DataEntity, newId);
    //                        }
    //                        continue;
    //                    }
    //                    if (field is HtmlAuxPropertyField)
    //                    {
    //                        var auxObj = refFormField.RefDynamicProperty.GetValue<DynamicObject>(item.DataEntity);
    //                        if (auxObj != null)
    //                        {
    //                            auxPropCache.Add(auxObj);
    //                            continue;
    //                        }

    //                        //var flexId = field.DynamicProperty.GetValue<string>(item.DataEntity);

    //                        //if (flexId.IsNullOrEmptyOrWhiteSpace())
    //                        //{
    //                        //    var dm = userContext.Container.GetService<IDataManager>();
    //                        //    dm.InitDbContext(userContext, form.GetDynamicObjectType(userContext));
    //                        //    auxObj = dm.Select(flexId) as DynamicObject;
    //                        //    if (auxObj != null)
    //                        //    {
    //                        //        refFormField.RefDynamicProperty.SetValue(item.DataEntity, auxObj);
    //                        //        auxPropCache.Add(auxObj);
    //                        //    }
    //                        //}
    //                    }
    //                }

    //                if (auxPropCache == null || auxPropCache.Count <= 0)
    //                {
    //                    continue;
    //                }

    //                var dSet = new ExtendedDataEntitySet();
    //                dSet.Parse(userContext, auxPropCache, form);

    //                var flds = form.GetFieldList().Where(x => x.Id == fieldIds[1] && x is IRefFormField).ToList();

    //                if (flds == null || flds.Count <= 0)
    //                {
    //                    continue;
    //                }

    //                foreach (var fld in flds)
    //                {
    //                    var reffld = fld as IRefFormField;
    //                    var fm = metaModelService.LoadFormModel(userContext, refFormField.RefFormId);
    //                    var setDE = dataEntitySet.FindByEntityKey(fld.EntityKey);

    //                    foreach (var item in setDE)
    //                    {
    //                        if (fld is HtmlBaseDataField)
    //                        {
    //                            var id = fld.DynamicProperty.GetValue<string>(item.DataEntity);
    //                            if (id == fieldIds[1])
    //                            {
    //                                fld.DynamicProperty.SetValue(item.DataEntity, newId);
    //                            }
    //                        }
    //                    }
    //                }
    //            }
    //        }
    //    }

    //    private static void dealId(UserContext userContext,
    //                               JObject dataContent,
    //                               string fieldId,
    //                               string newId,
    //                               HtmlForm form,
    //                               string chainDataId,
    //                               List<Dictionary<string, object>> records)
    //    {
    //        var fieldIds = split(fieldId);//fieldId.Split('_');
    //        var fldIds = fieldIds[0].Split(new[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
    //        var fields = form.GetFieldList().Where(x => x.Id == fldIds[0]).ToList();
    //        if (fields == null || fields.Count <= 0)
    //        {
    //            return;
    //        }
    //        foreach (var field in fields)
    //        {
    //            if (field.Entity is HtmlHeadEntity)
    //            {
    //                if (field is HtmlBaseDataField)
    //                {
    //                    if (dealId(userContext, dataContent, fldIds[0], fieldIds[1], newId, field, chainDataId, records))
    //                    {
    //                        return;
    //                    }
    //                    continue;
    //                }
    //                if (field is HtmlAuxPropertyField)
    //                {
    //                    var fattrinfo = dataContent[fldIds[0]] as JObject;
    //                    //if (fattrinfo != null && (string)fattrinfo["id"] == fieldIds[1])
    //                    if (fattrinfo != null)
    //                    {
    //                        //var htmlForm = (field as HtmlAuxPropertyField).RefHtmlForm(userContext);
    //                        //dealId(userContext, fattrinfo, string.Format("{0}_{1}", fieldIds[2], fieldIds[3]), newId, htmlForm, chainDataId, records);
    //                        dealId(userContext, fattrinfo, fldIds[1], fieldIds[1], newId, chainDataId, records);
    //                        continue;
    //                    }
    //                }
    //                continue;
    //            }

    //            if (field.Entity is HtmlSubEntryEntity)
    //            {
    //                var sEntry = field.Entity as HtmlSubEntryEntity;
    //                var pEntry = sEntry.ParentEntity;
    //                var entries = dataContent[pEntry.Id] as JArray;
    //                if (entries != null && entries.Count > 0)
    //                {
    //                    foreach (var entry in entries)
    //                    {
    //                        var subEntries = entry[sEntry.Id] as JArray;
    //                        if (subEntries != null && subEntries.Count > 0)
    //                        {
    //                            foreach (var subEntry in subEntries)
    //                            {
    //                                if (field is HtmlBaseDataField)
    //                                {
    //                                    if (dealId(userContext, subEntry as JObject, fldIds[0], fieldIds[1], newId, field, chainDataId, records))
    //                                    {
    //                                        return;
    //                                    }
    //                                    continue;
    //                                }
    //                                if (field is HtmlAuxPropertyField)
    //                                {
    //                                    var fattrinfo = subEntry[fldIds[0]] as JObject;
    //                                    if (fattrinfo != null)
    //                                    {
    //                                        //var htmlForm = (field as HtmlAuxPropertyField).RefHtmlForm(userContext);
    //                                        //dealId(userContext, fattrinfo, string.Format("{0}_{1}", fieldIds[2], fieldIds[3]), newId, htmlForm, chainDataId, records);
    //                                        dealId(userContext, fattrinfo, fldIds[1], fieldIds[1], newId, chainDataId, records);
    //                                        continue;
    //                                    }
    //                                }
    //                                continue;
    //                            }
    //                        }
    //                    }
    //                }
    //            }

    //            if (field.Entity is HtmlEntryEntity)
    //            {
    //                var entries = dataContent[field.EntityKey] as JArray;
    //                if (entries != null && entries.Count > 0)
    //                {
    //                    foreach (var entry in entries)
    //                    {
    //                        if (field is HtmlBaseDataField)
    //                        {
    //                            if (dealId(userContext, entry as JObject, fldIds[0], fieldIds[1], newId, field, chainDataId, records))
    //                            {
    //                                return;
    //                            }
    //                            continue;
    //                        }
    //                        if (field is HtmlAuxPropertyField)
    //                        {
    //                            var fattrinfo = entry[fldIds[0]] as JObject;
    //                            if (fattrinfo != null)
    //                            {
    //                                //var htmlForm = (field as HtmlAuxPropertyField).RefHtmlForm(userContext);
    //                                //dealId(userContext, fattrinfo, string.Format("{0}_{1}", fieldIds[2], fieldIds[3]), newId, htmlForm, chainDataId, records);
    //                                dealId(userContext, fattrinfo, fldIds[1], fieldIds[1], newId, chainDataId, records);
    //                                continue;
    //                            }
    //                        }
    //                        continue;
    //                    }
    //                }
    //                continue;
    //            }
    //        }
    //    }

    //    /// <summary>
    //    /// 处理辅助属性
    //    /// </summary>
    //    /// <param name="userContext"></param>
    //    /// <param name="dataContent"></param>
    //    /// <param name="fieldId"></param>
    //    /// <param name="oldId"></param>
    //    /// <param name="newId"></param>
    //    /// <param name="chainDataId"></param>
    //    /// <param name="records"></param>
    //    /// <returns></returns>
    //    private static bool dealId(UserContext userContext,
    //                               JObject dataContent,
    //                               string fieldId,
    //                               string oldId,
    //                               string newId,
    //                               string chainDataId,
    //                               List<Dictionary<string, object>> records)
    //    {
    //        JToken value = null;
    //        string refFormId = null;
    //        removeIdKey(dataContent, null);
    //        if ("fmaterialid" == fieldId)
    //        {
    //            refFormId = "bd_material";
    //            value = dataContent[fieldId];
    //            if (value.Type == JTokenType.Object)
    //            {
    //                dataContent = value as JObject;
    //                value = dataContent["id"];
    //            }
    //            else if (value.Type == JTokenType.String)
    //            {
    //                var valueObject = new JObject();
    //                dataContent[fieldId] = valueObject;
    //                valueObject["id"] = value;
    //                dataContent = valueObject;
    //            }
    //        }
    //        else if ("fauxpropid" == fieldId)
    //        {
    //            refFormId = "bd_auxproperty";
    //            var fentities = dataContent["fentity"] as JArray;
    //            if (fentities == null || fentities.Count <= 0)
    //            {
    //                throw new Exception("辅助属性格式不正确!");
    //            }
    //            foreach (var fentity in fentities)
    //            {
    //                removeIdKey(fentity as JObject, "fentity");
    //                value = fentity["fauxpropid"];
    //                if (value.Type == JTokenType.Object)
    //                {
    //                    value = value["id"];
    //                }
    //                if (oldId == (string)value)
    //                {
    //                    fentity["fvaluenumber"] = fentity["fvalueid"];
    //                    fentity["fvaluename"] = fentity["fvalueid"];
    //                    var auxObject = new JObject();
    //                    fentity["fauxpropid"] = auxObject;
    //                    auxObject["id"] = value;
    //                    dataContent = auxObject;
    //                    break;
    //                }
    //            }
    //        }

    //        if (refFormId == null)
    //        {
    //            throw new Exception(string.Format("无法识别{0}字段!", fieldId));
    //        }

    //        if (value.Type == JTokenType.String)
    //        {
    //            if (oldId == (string)value)
    //            {
    //                if (string.IsNullOrWhiteSpace(newId))
    //                {
    //                    var metaModelServcie = userContext.Container.GetService<IMetaModelService>();
    //                    var htmlForm = metaModelServcie.LoadFormModel(userContext, refFormId);
    //                    var where = "ffromchaindataid=@chaindataid and fmainorgid=@fmainorgid";
    //                    SqlParam[] sqlParams = new SqlParam[]
    //                    {
    //                        new SqlParam("@chaindataid",System.Data.DbType.String,chainDataId),
    //                        new SqlParam("@fmainorgid",System.Data.DbType.String,userContext.Company)
    //                    };
    //                    var dataReader = userContext.GetPkIdDataReader(htmlForm, where, sqlParams);
    //                    var dm = userContext.Container.GetService<IDataManager>();
    //                    dm.InitDbContext(userContext, htmlForm.GetDynamicObjectType(userContext));
    //                    var dataEntity = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
    //                    if (dataEntity != null)
    //                    {
    //                        newId = Convert.ToString(dataEntity["id"]);
    //                        records.Add(new Dictionary<string, object>
    //                        {
    //                            { "billFormId", htmlForm.Id},
    //                            { "dataId", oldId },
    //                            { "dataName",null},
    //                            { "dataNumber", null},
    //                            { "chainDataId", chainDataId },
    //                            { "publishCompanyId", Convert.ToString(dataEntity["fpublishcid"]) },
    //                            { "publishProductId", Convert.ToString(dataEntity["fpublishcid_pid"]) },
    //                            { "publishCompany",Convert.ToString(dataEntity["fpublishcid_txt"])},
    //                            { "dataContent", null },
    //                            { "linkChainData", null },
    //                            { "localDataId",newId},
    //                            { "dataEntity",dataEntity}
    //                        });
    //                    }
    //                }
    //                if (string.IsNullOrEmpty(newId) == false)
    //                {
    //                    dataContent["id"] = newId;
    //                }
    //                return true;
    //            }
    //        }
    //        return false;
    //    }

    //    private static bool dealId(UserContext userContext,
    //                               JObject dataContent,
    //                               string fieldId,
    //                               string oldId,
    //                               string newId,
    //                               HtmlField field,
    //                               string chainDataId,
    //                               List<Dictionary<string, object>> records)
    //    {
    //        var property = dataContent.Property(fieldId);
    //        if (property == null)
    //        {
    //            return false;
    //        }
    //        var value = dataContent[fieldId];
    //        if (value.Type == JTokenType.Object)
    //        {
    //            fieldId = "id";
    //            dataContent = value as JObject;
    //            value = dataContent[fieldId];
    //        }
    //        if (value.Type == JTokenType.String)
    //        {
    //            if (oldId == (string)value)
    //            {
    //                if (string.IsNullOrWhiteSpace(newId))
    //                {
    //                    IRefFormField refFormField = field as IRefFormField;
    //                    var metaModelServcie = userContext.Container.GetService<IMetaModelService>();
    //                    var htmlForm = metaModelServcie.LoadFormModel(userContext, refFormField.RefFormId);
    //                    var where = "ffromchaindataid=@chaindataid and fmainorgid=@fmainorgid";
    //                    SqlParam[] sqlParams = new SqlParam[]
    //                    {
    //                        new SqlParam("@chaindataid",System.Data.DbType.String,chainDataId),
    //                        new SqlParam("@fmainorgid",System.Data.DbType.String,userContext.Company)
    //                    };
    //                    var dataReader = userContext.GetPkIdDataReader(htmlForm, where, sqlParams);
    //                    var dm = userContext.Container.GetService<IDataManager>();
    //                    dm.InitDbContext(userContext, htmlForm.GetDynamicObjectType(userContext));
    //                    var dataEntity = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
    //                    if (dataEntity != null)
    //                    {
    //                        newId = Convert.ToString(dataEntity["id"]);
    //                        records.Add(new Dictionary<string, object>
    //                        {
    //                            { "billFormId", htmlForm.Id},
    //                            { "dataId", oldId },
    //                            { "dataName",null},
    //                            { "dataNumber", null},
    //                            { "chainDataId", chainDataId },
    //                            { "publishCompanyId", Convert.ToString(dataEntity["fpublishcid"]) },
    //                            { "publishProductId", Convert.ToString(dataEntity["fpublishcid_pid"]) },
    //                            { "publishCompany",Convert.ToString(dataEntity["fpublishcid_txt"])},
    //                            { "dataContent", null },
    //                            { "linkChainData", null },
    //                            { "localDataId",newId},
    //                            { "dataEntity",dataEntity}
    //                        });
    //                    }
    //                }
    //                if (string.IsNullOrEmpty(newId) == false)
    //                {
    //                    dataContent[fieldId] = newId;
    //                }
    //                return true;
    //            }
    //        }
    //        return false;
    //    }

    //    private static PlugInProxy<IOperationServicePlugIn> getPlugInProxy(UserContext userContext,
    //                                                                       Dictionary<string, PlugInProxy<IOperationServicePlugIn>> pluginProxyCache,
    //                                                                       string htmlFormId)
    //    {
    //        if (pluginProxyCache.Keys.Contains(htmlFormId))
    //        {
    //            return pluginProxyCache[htmlFormId];
    //        }
    //        else
    //        {
    //            var pluginProxy = userContext.Container.GetService<PlugInProxy<IOperationServicePlugIn>>(new NamedParameter("formId", htmlFormId),
    //                                                                                            new NamedParameter("operationNo", "SyncDownloadConfirm"));
    //            pluginProxyCache[htmlFormId] = pluginProxy;
    //            pluginProxy.InitPlugInEvn(userContext.Container);
    //            return pluginProxy;
    //        }
    //    }

    //    private static void throwException(List<Exception> exceptions, string msg)
    //    {
    //        var ex = new BusinessException(msg);
    //        if (exceptions != null)
    //        {
    //            exceptions.Add(ex);
    //            return;
    //        }
    //        throw ex;
    //    }

    //    private static string[] split(string value)
    //    {
    //        int index = value.LastIndexOf('_');
    //        string[] result = new string[2];
    //        result[0] = value.Substring(0, index);
    //        result[1] = value.Substring(index + 1);
    //        return result;
    //    }

    //    private static string removeIdKey(JObject dataContent, string entryId)
    //    {
    //        (new[] { "id", "Id", "ID", "iD" }).FirstOrDefault(x => dataContent.Remove(x));
    //        var tranIdFldKey = string.IsNullOrWhiteSpace(entryId) ? "ftranid" : $"{entryId}_ftranid";
    //        var topTranIdFldKey = string.IsNullOrWhiteSpace(entryId) ? "ftoptranid" : $"{entryId}_ftoptranid";
    //        var parentTranIdFldKey = string.IsNullOrWhiteSpace(entryId) ? "fparenttranid" : $"{entryId}_fparenttranid";
    //        var dataOriginFldKey = string.IsNullOrWhiteSpace(entryId) ? "fdataorigin" : $"{entryId}_fdataorigin";
    //        var tranProperty = dataContent.Properties().FirstOrDefault(x => string.Equals(x.Name, tranIdFldKey, StringComparison.OrdinalIgnoreCase));
    //        var topTranProperty = dataContent.Properties().FirstOrDefault(x => string.Equals(x.Name, topTranIdFldKey, StringComparison.OrdinalIgnoreCase));
    //        var parentTranProperty = dataContent.Properties().FirstOrDefault(x => string.Equals(x.Name, parentTranIdFldKey, StringComparison.OrdinalIgnoreCase));
    //        var dataOriginProperty = dataContent.Properties().FirstOrDefault(x => string.Equals(x.Name, dataOriginFldKey, StringComparison.OrdinalIgnoreCase));

    //        string tranId = null;

    //        if (tranProperty != null)
    //        {
    //            tranId = (string)tranProperty.Value;
    //            dataContent.Remove(tranProperty.Name);
    //        }

    //        if (parentTranProperty != null)
    //        {
    //            parentTranProperty.Value = tranId;
    //        }
    //        else
    //        {
    //            dataContent[parentTranIdFldKey] = tranId;
    //        }

    //        if (topTranProperty != null)
    //        {
    //            var entryTopTranId = (string)topTranProperty.Value;
    //            if (string.IsNullOrWhiteSpace(entryTopTranId))
    //            {
    //                topTranProperty.Value = tranId;
    //            }
    //        }
    //        else
    //        {
    //            dataContent[topTranIdFldKey] = tranId;
    //        }

    //        if (dataOriginProperty != null)
    //        {
    //            dataOriginProperty.Value = "下载";
    //        }
    //        else
    //        {
    //            dataContent[dataOriginFldKey] = "下载";
    //        }

    //        return tranId;
    //    }

    //}

    /// <summary>
    /// 基础资料下载确定操作
    /// </summary>
    [InjectService("SyncDownloadConfirm")]
    public class SyncDownloadConfirm : AbstractOperationService
    {
        List<object> publishChaindatas = new List<object>();

        protected override string OperationName
        {
            get
            {
                return "下载确定";
            }
        }

        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_New;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            string billDataJsonStr = this.GetQueryOrSimpleParam<string>("billDataJson");
            List<Dictionary<string,object>> pushDatas = null;
            this.OperationContext.Result.IsSuccess = true;


            if (false == string.IsNullOrWhiteSpace(billDataJsonStr))
            {
                pushDatas = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(billDataJsonStr);
            }

            if (pushDatas == null || pushDatas.Count <= 0)
            {
                this.OperationContext.Result.IsSuccess = false;
                this.OperationContext.Result.IsShowMessage = true;
                this.OperationContext.Result.SimpleMessage = "请传递需要下载的资料";
                return;
            }

            var linkChainDataIds = pushDatas.Where(x =>
              {
                  var linkChainDataStr = Convert.ToString(x["linkChainData"]);
                  Dictionary<string, string> linkChainData = null;
                  if (false == string.IsNullOrWhiteSpace(linkChainDataStr))
                  {
                      linkChainData = JsonConvert.DeserializeObject<Dictionary<string, string>>(linkChainDataStr);
                  }
                  if (linkChainData == null)
                  {
                      linkChainData = new Dictionary<string, string>();
                  }
                  x["linkChainData"] = linkChainData;
                  return linkChainData.Count > 0;
              })
              .SelectMany(x => (x["linkChainData"] as Dictionary<string, string>).Values)
              .Distinct()
              .ToList();


            if (linkChainDataIds != null && linkChainDataIds.Count > 0)
            {
                #region 向EIS站点下载依赖数据包

                var response = this.Gateway.Invoke(this.UserCtx,
                    TargetSEP.EisService,
                    new CommonBillDTO()
                    {
                        FormId = "syn_shareddata",
                        OperationNo = "downloadlinkdata",
                        SimpleData = new Dictionary<string, string> { { "chainDataIds", string.Join(",", linkChainDataIds) } }
                    }) as CommonBillDTOResponse;

                if (response == null || response.OperationResult == null || response.OperationResult.SrvData == null)
                {
                    throw new BusinessException("下载失败依赖失败");
                }

                #endregion

                var srvData = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(response.OperationResult.SrvData.ToString());

                if (srvData != null && srvData.Count > 0)
                {
                    foreach (var srvItem in srvData)
                    {
                        pushDatas.Add(srvItem);
                    }
                }
            }

            pushDatas = pushDatas.Distinct(x => Convert.ToString(x["chainDataId"])).ToList();

            var companyInfos = new List<Dictionary<string, object>>
            {
                new Dictionary<string, object>
                {
                    { "companyId",this.UserCtx.Company}
                }
            };
            
            foreach(var pushData in pushDatas)
            {
                pushData["companyInfos"] = companyInfos;
            }

            var msgId = Guid.NewGuid().ToString();
            var pushShareDataMsgService = this.Container.GetService<IPushShareDataMsgService>();
            this.OperationContext.Result.IsSuccess = pushShareDataMsgService.ProcessMessage(this.UserCtx, pushDatas, msgId);

            if (false == this.OperationContext.Result.IsSuccess)
            {
                this.OperationContext.Result.SimpleMessage = $"下载失败，详情请看消息日志，消息id为{msgId}";
            }
        }

    }


}
