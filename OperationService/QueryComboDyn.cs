using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json.Linq;
using JieNor.Framework.Consts;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface.Cache;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 下拉列表的数据查询：主要用于下拉列表的数据加载显示
    /// 传递的 SimpleData 包含如下参数：
    ///     fieldkey ： 必填，查询的字段Key，
    ///     比如 销售订单上的 单据状态（假定为 FStatus） ，则 传递 FStatus 
    /// </summary>
    [InjectService("QueryComboDyn")]
    public class QueryComboDyn : AbstractOperationService
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName { get { return ""; } }

        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem { get { return ""; } }

        

        /// <summary>
        /// 操作执行过程
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        { 
            var svc = this.Container.GetService<IComboDataService>();
            var datas = svc.GetFormComboDatas(this.UserCtx, this.OperationContext.HtmlForm.Id, FieldKey);
              
            //返回数据
            this.OperationContext.Result.SrvData = new { Data = datas };
            this.OperationContext.Result.IsSuccess = true;
        }
         
        
        /// <summary>
        /// 模糊查询的字段标识（如销售订单上的部门字段标识）
        /// </summary>
        protected string FieldKey
        {
            get
            {
                var key = GetQueryOrSimpleParam<string>("fieldkey");
                if (key.IsNullOrEmptyOrWhiteSpace())
                {
                    throw new Exception(string.Format("参数不正确，字段标识 {0} 不存在", key));
                }
                var field = this.OperationContext.HtmlForm.GetField(key);
                if (field == null)
                {
                    throw new Exception(string.Format("参数不正确，字段标识 {0} 不存在", key));
                }
                return key;
            }
        }
    }
}