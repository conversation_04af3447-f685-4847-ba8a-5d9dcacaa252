using JieNor.Framework.Consts;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Profile;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.FormOp.OpData;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Drivers;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JieNor.Framework.Interface.Utils;

namespace JieNor.Framework.AppService.OperationService
{

    /// <summary>
    /// 暂存操作
    /// </summary>
    [InjectService("Draft")]
    public class Draft : AbstractSetStatus
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName
        {
            get
            {
                return "暂存";
            }
        }

        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_New;
            }
        }

        /// <summary>
        /// 操作后状态值
        /// </summary>
        protected override string StatusValue
        {
            get { return "A"; }
        }

        /// <summary>
        /// 是否自动保存数据
        /// </summary>
        protected override bool AutoSaveData
        {
            get
            {
                return true;
            }
        }

        /// <summary>
        /// 业务表单
        /// </summary>
        protected HtmlForm HtmlForm => this.OperationContext.HtmlForm;



        protected override void CheckPermission()
        {
            var ignoreCheck = this.GetQueryOrSimpleParam<string>("IgnoreCheckPermssion", "false").EqualsIgnoreCase("true")
               || this.GetQueryOrSimpleParam<string>("IgnoreCheckPermssion", "0").EqualsIgnoreCase("1");
            if (ignoreCheck)
            {
                return;
            }

            //没开启权限的表单，不验权
            if (this.OperationContext.HtmlForm.EnableRAC == false)
            {
                return;
            }

            var e = new OnCheckPermssionArgs();
            e.FormId = this.PermFormId;
            e.PermItem = this.PermItem;//新增权限
            this.PlugInProxy.InvokePlugInMethod("OnCheckPermssion", e);
            if (e.IgnoreCheck || e.FormId.IsNullOrEmptyOrWhiteSpace()
                || e.PermItem.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }

            var permSrv = this.OperationContext.Container.GetService<IPermissionService>();
            var have = permSrv.HasPermission(this.OperationContext.UserContext, new MetaCore.PermData.PermAuth(this.OperationContext.UserContext)
            {
                FormId = e.FormId,
                OperationName = this.OperationContext.HtmlOperation?.OperationName ?? this.OperationName,
                PermId = e.PermItem,
            });
            if (have)
            {
                return;
            }

            e.PermItem = PermConst.PermssionItem_Modify;//修改权限
            this.PlugInProxy.InvokePlugInMethod("OnCheckPermssion", e);
            if (e.IgnoreCheck || e.FormId.IsNullOrEmptyOrWhiteSpace()
                || e.PermItem.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }

            have = permSrv.HasPermission(this.OperationContext.UserContext, new MetaCore.PermData.PermAuth(this.OperationContext.UserContext)
            {
                FormId = e.FormId,
                OperationName = this.OperationContext.HtmlOperation?.OperationName ?? this.OperationName,
                PermId = e.PermItem,
            });
            if (have)
            {
                return;
            }

            var userStr = this.OperationContext.UserContext.DisplayName;
            if (userStr.IsNullOrEmptyOrWhiteSpace())
            {
                userStr = this.OperationContext.UserContext.UserName;
            }

            throw new BusinessException("您没有【{0}】的{1}操作权限(用户：{2})".Fmt(HtmlForm?.Caption, "新增或修改", userStr));
        }



        /// <summary>
        /// 服务初始化
        /// </summary>
        protected override void InitializeService()
        {
            base.InitializeService();

            this.ServiceControlOption.SupportIdemotency = true;
        }
        /// <summary>
        /// 为数据包部分内置字段准备数据
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void InitializeOperationDataEntities(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0) return;

            var agent = this.UserCtx.Company;
            //var MainagentName = Convert.ToString(GetMaingAgent(this.UserCtx, agent)?["fname"]);
            //if (!MainagentName.IsNullOrEmptyOrWhiteSpace())
            //{ 
            //    throw new BusinessException($"对不起，当前组织为子经销商，请切换到【{MainagentName}】组织下操作！");
            //}

            //需要排除表单
            var excludeFormIds = new string[] { "sys_company" };
            var mainOrgIdField = this.OperationContext.HtmlForm.GetField("fmainorgid");
            if (mainOrgIdField != null && !excludeFormIds.Contains(this.HtmlForm.Id.ToLower()))
            {
                var entryMainOrgIdField = this.OperationContext.HtmlForm.GetField("fenmainorgid");
                var primitiveIdField = this.OperationContext.HtmlForm.GetField("fprimitiveid");
                var presetField = this.OperationContext.HtmlForm.GetField("fispreset");
                var numberField = this.OperationContext.HtmlForm.GetNumberField();

                var dataMgr = this.GetDataManager();
                dataMgr.InitDbContext(this.UserCtx, this.OperationContext.HtmlForm.GetDynamicObjectType(this.UserCtx));

                List<DynamicObject> lstObjs = new List<DynamicObject>();
                foreach (var billObj in dataEntities)
                {
                    var newDynObj = billObj;
                    var mainOrgId = mainOrgIdField.DynamicProperty.GetValue<string>(billObj);

                    //目前在保存预置的数据时
                    if (mainOrgId.EqualsIgnoreCase("0") && !primitiveIdField.IsNullOrEmptyOrWhiteSpace())
                    {
                        //检查当前预置的数据是否已经保存过
                        var paramList = new List<SqlParam> { new SqlParam("@fmainorgid", System.Data.DbType.String, this.UserCtx.Company) };
                        var whereStr = $"fmainorgid=@fmainorgid and {primitiveIdField.FieldName}!='' and {primitiveIdField.FieldName}='{billObj["id"]}'";
                        var dataReader = this.UserCtx.GetPkIdDataReader(this.OperationContext.HtmlForm, whereStr, paramList);
                        var existsCustom = dataMgr.SelectBy(dataReader)?.OfType<DynamicObject>()?.FirstOrDefault();
                        if (existsCustom != null)
                        {
                            throw new BusinessException("该预置数据在当前企业中已经保存过，如要修改，请打开已保存过的数据进行修改后保存！");
                        }

                        if (!this.OperationContext.Option.HasInteractionFlag("confirmsavepresetdata"))
                        {
                            throw new InteractionException("confirmsavepresetdata", "您正在修改系统预置数据，继续保存将会为您企业生成一份新的资料，确认继续吗？");
                        }
                        newDynObj = this.OperationContext.HtmlForm.GetDynamicObjectType(this.UserCtx).CreateInstance() as DynamicObject;
                        billObj.CopyTo(newDynObj, null, true, false, false,
                            (newObj, oldObj) =>
                            {

                            },
                            (dtName, oldObj, newObjs) =>
                            {
                                return true;
                            });

                        mainOrgIdField.DynamicProperty.ResetValue(newDynObj);
                        presetField?.DynamicProperty?.ResetValue(newDynObj);
                        numberField?.DynamicProperty?.ResetValue(newDynObj);
                        primitiveIdField?.DynamicProperty?.SetValue(newDynObj, billObj["id"]);

                        newDynObj.GetDataEntityType().SetDirty(newDynObj, true);
                    }
                    lstObjs.Add(newDynObj);
                }
                dataEntities = lstObjs.ToArray();
            }

            var prepareService = this.Container.GetService<IPrepareSaveDataService>();
            prepareService?.PrepareDataEntity(this.UserCtx, this.OperationContext.HtmlForm, dataEntities, this.OperationContext.Option);
        }

        
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            ManageModelUtil.SetDataEntityManageModel(this.OperationContext.UserContext,this.OperationContext.HtmlForm,dataEntities);
        }

        protected override void AfterExecute(ref DynamicObject[] dataEntities)
        {
            base.AfterExecute(ref dataEntities);
             
            //excel导入的或者其他系统集成时，设置 TopOrperationNo 标记，这时候时后台处理数据，不需要加载引用属性
            var excelImport = this.GetQueryOrSimpleParam<string>("TopOrperationNo", "");

            ////保存成功后加载引用数据
            //if (dataEntities != null && dataEntities.Length > 0)
            //{ 
            //    if (!excelImport.EqualsIgnoreCase("ExcelImport"))
            //    {
            //        var dt = this.OperationContext.HtmlForm.DataEntityType(UserCtx);
            //        this.OperationContext.Container.GetService<LoadReferenceObjectManager>()?.Load(this.UserCtx, dt, dataEntities, false);
            //    }
            //}

            //保存后需要将保存成功的id返回给前端。
            List<string> ids = new List<string>();
            var simpleData = dataEntities?.Select(o =>
             {
                 var numberField = this.OperationContext.HtmlForm.GetNumberField();
                 var nameField = this.OperationContext.HtmlForm.GetNameField();
                 var pkId = o["Id"] as string;
                 ids.Add(pkId);
                 var number = numberField == null ? "" : numberField.DynamicProperty.GetValue<string>(o);
                 var name = nameField == null ? "" : nameField.DynamicProperty.GetValue<string>(o);
                 string caption = "{0}-{1}".Fmt(this.OperationContext.HtmlForm.Caption, "修改");

                 //参数设置表单不需要加 -修改 后缀
                 if(this.OperationContext is ParameterOperationContext)
                 {
                     caption = this.OperationContext.HtmlForm.Caption;
                 }

                 return new BillSavedSummary
                 {
                     Id = pkId,
                     Name = name,
                     Number = number,
                     Caption = caption
                 };
             });
            this.OperationContext.Result.SrvData = simpleData;
            var pkids = (from p in dataEntities
                         select Convert.ToString(p["id"])).ToList();
            this.OperationContext.Result.SimpleData["pkids"] = pkids.ToJson();

            ////保存个性化配置数据：个性化配置数据，与业务单据的保存关系不大，
            ////不放在事物中执行（不放 DoExecute 中执行）
            //if (!excelImport.EqualsIgnoreCase("ExcelImport"))
            //{
            //    SaveUserProfile();
            //}
        }


        /// <summary>
        /// 保存个性化配置数据
        /// </summary> 
        private void SaveUserProfile()
        {
            string lastProfileInfo = "";
            if (this.OperationContext.SimpleData.TryGetValue("profileData", out lastProfileInfo))
            {
                var formUserProfile = lastProfileInfo.FromJson<FormUserProfile>();
                if (formUserProfile != null)
                {
                    var profileService = this.Container.GetService<IUserProfile>();
                    profileService.SaveUserProfile(this.OperationContext.UserContext, this.OperationContext.HtmlForm.Id, formUserProfile, true);
                }
            }
        }
        /// <summary>
        /// 是否存在当前经销商配置在子经销商
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="agentId"></param>
        /// <returns></returns>
        public DynamicObject GetMaingAgent(UserContext userCtx, string agentId)
        {
            List<string> result = new List<string>();
            string sql = $@"
                        select ag.fname
                        from t_bas_mac m 
                        inner join t_bas_macentry me on m.fid=me.fid
                        inner join t_bas_agent ag on ag.fid = m.fmainagentid
                        where me.fsubagentid='{agentId}' and m.fforbidstatus='0'
                        ";
            var Mainagent = this.Container.GetService<IDBService>().ExecuteDynamicObject(this.UserCtx, sql).FirstOrDefault();
            return Mainagent;
        }

        private Dictionary<DynamicObject, bool> dctFromDatabase = new Dictionary<DynamicObject, bool>();

        /// <summary>
        /// 记录新增与修改的数据包
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void BeforeExecute(ref DynamicObject[] dataEntities)
        {
            base.BeforeExecute(ref dataEntities);
            if (dataEntities.IsNullOrEmpty()) return;
            dctFromDatabase = dataEntities.ToDictionary(k => k, v => v.DataEntityState.FromDatabase);
        }

        /// <summary>
        /// 获得真实操作名称
        /// </summary>
        /// <param name="htmlForm"></param>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        protected override string GetActualOperationName(HtmlForm htmlForm, DynamicObject dataEntity)
        {
            bool bIsFromDb = false;
            dctFromDatabase.TryGetValue(dataEntity, out bIsFromDb);
            return bIsFromDb ? "修改" : "创建";
        }
    }
}
