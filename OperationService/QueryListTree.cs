using JieNor.Framework.Consts;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.SuperOrm.DataEntity;
using System.Collections.Generic;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 获取列表页面左侧树形数据源
    /// </summary>
    [InjectService("QueryListTree")]
    public class QueryListTree : AbstractOperationService
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName
        {
            get
            {
                return "";
            }
        }

        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_View;
            }
        }

        /// <summary>
        /// 操作执行过程
        /// </summary>
        /// <param name="dataEntities">数据包</param>
        //[PerfMonitor]
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            //获取各个业务插件实现该接口后返回的数据
            var treeProvider = this.Container.GetServiceByMeta<IListTreeDataProvider>(dctMeta =>
            {
                object formId;
                dctMeta.TryGetValue("formid", out formId);
                return this.OperationContext.HtmlForm.Id.EqualsIgnoreCase(formId as string);
            });

            //如果当前表单有实现 IListTreeDataProvider 接口，则取接口返回的数据
            if (treeProvider != null)
            {
                this.OperationContext.Result.SrvData = treeProvider.GetTreeDataSource(this.OperationContext.UserContext, this.OperationContext.HtmlForm, this.OperationContext.Option);
            }
            else
            {
                //否则默认一个根节点数据
                this.OperationContext.Result.SrvData = new ListTreeNode()
                {
                    Name = "所有",
                    Filter = "",
                    Children = new List<ListTreeNode>()
                };
            }
        }
    }
}