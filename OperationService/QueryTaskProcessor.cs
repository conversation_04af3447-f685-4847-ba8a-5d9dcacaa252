using System.Collections.Generic;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Interface.BfTask;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 查询任务处理器
    /// </summary>
    [InjectService("QueryTaskProcessor")]
    public class QueryTaskProcessor : AbstractOperationService
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName
        {
            get
            {
                return "查询任务处理器";
            }
        }

        /// <summary>
        /// 不验权
        /// </summary>
        protected override string PermItem
        {
            get
            {
                return "";
            }
        }

        /// <summary>
        /// 忽略操作消息
        /// </summary>
        protected override bool IgnoreOpMessage
        {
            get
            {
                return true;
            }
        }

        /// <summary>
        /// 提供任务处理器查询接口
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            List<Dictionary<string, string>> taskProcessors = new List<Dictionary<string, string>>();

            //获取任务处理器
            var allTaskProcessors = this.Container.GetAllServiceWithMeta<ITaskProcessor>();
            foreach (var taskProcessor in allTaskProcessors)
            {
                object taskprocessor, caption;
                taskProcessor.Item2.TryGetValue("taskprocessor", out taskprocessor);
                taskProcessor.Item2.TryGetValue("caption", out caption);

                if (!taskprocessor.IsNullOrEmptyOrWhiteSpace() && !caption.IsNullOrEmptyOrWhiteSpace())
                {
                    taskProcessors.Add(new Dictionary<string, string>
                    {
                        { "id", taskprocessor.ToString() },
                        { "name", caption.ToString() },
                    });
                }
            }

            List<Dictionary<string, object>> opGroups = new List<Dictionary<string, object>>()
            {
                new Dictionary<string, object>
                {
                    { "group", "所有任务处理器" },
                    { "fields", taskProcessors }
                }
            };

            this.OperationContext.Result.SrvData = opGroups;
            this.OperationContext.Result.IsSuccess = true;
        }
    }
}