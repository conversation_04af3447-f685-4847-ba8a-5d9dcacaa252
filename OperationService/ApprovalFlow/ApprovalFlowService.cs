using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.BPM;
using JieNor.Framework.MetaCore.BizState;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.Enums;
using JieNor.Framework.MetaCore;
using JieNor.Framework.DataTransferObject;

namespace JieNor.Framework.AppService.OperationService.ApprovalFlow
{
    /// <summary>
    /// 审批流服务
    /// </summary>
    [InjectService]
    public class ApprovalFlowService : IApprovalFlowService
    {
        /// <summary>
        /// 元数据服务
        /// </summary>
        [InjectProperty]
        protected IMetaModelService MetaModelService { get; set; }

        /// <summary>
        /// 数据读取服务
        /// </summary>
        [InjectProperty]
        protected IDBService DBService { get; set; }

        /// <summary>
        /// 日志服务
        /// </summary>
        [InjectProperty]
        protected ILogServiceEx LogServiceEx { get; set; }

        /// <summary>
        /// 日志服务
        /// </summary>
        [InjectProperty]
        protected ILogService Loger { get; set; }

        /// <summary>
        /// 获取单据启用的审批流程列表
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="bizForm"></param>
        /// <returns></returns>
        public List<DynamicObject> GetBillStartFlowList(UserContext userCtx, HtmlForm bizForm)
        {
            if (!this.IsStartFlow(userCtx, bizForm)) return null;

            var bizApproval = this.GetBusinessApprovalByFormId(userCtx, bizForm.Id);
            if (bizApproval == null) return null;

            //业务审批流程信息
            var flowEntrys = bizApproval["fflowentry"] as DynamicObjectCollection;
            if (flowEntrys == null || !flowEntrys.Any()) return null;

            //启用的审批流信息
            var startFlowEntrys = flowEntrys.Where(t => (Convert.ToString(t["fflowstatus"]) ?? "").Trim().EqualsIgnoreCase("flow_sta02")).ToList();

            return startFlowEntrys;
        }

        /// <summary>
        /// 系统是否已经启用审批流
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="bizForm"></param>
        /// <returns></returns>
        public bool IsStartFlow(UserContext userCtx, HtmlForm bizForm)
        {
            if (!bizForm.ApprovalFlow) return false;

            //审批流设置中选择的外部应用Id
            var bpmFormMeta = this.MetaModelService.LoadFormModel(userCtx, "bpm_businessapproval");
            var pkIdReader = userCtx.GetPkIdDataReaderWithHeadField(bpmFormMeta, "fbizformid", new object[] { bizForm.Id });
            using (pkIdReader)
            {
                if (pkIdReader.Read())
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 根据流程Id获得一个新的流程版本号
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="flowId">流程Id</param>
        /// <param name="currentMaxVerNo">当前流程的最大版本号</param>
        /// <returns>新的流程版本号</returns>
        public string GetNewFlowVersionNo(UserContext userCtx, string flowId, string currentMaxVerNo = "")
        {
            if (currentMaxVerNo.IsNullOrEmptyOrWhiteSpace())
            {
                var strSql = $"select max(fvername) as 'fvername' from t_bpm_flowversion with(nolock) where fentryid=@fentryid";
                List<SqlParam> paramList = new List<SqlParam>()
                {
                    new SqlParam("@fentryid", DbType.String, flowId)
                };
                var dbService = userCtx.Container.GetService<IDBService>();
                using (var reader = dbService.ExecuteReader(userCtx, strSql, paramList))
                {
                    if (reader.Read())
                    {
                        currentMaxVerNo = Convert.ToString(reader["fvername"]);
                    }
                }
            }
            if (currentMaxVerNo.IsNullOrEmptyOrWhiteSpace())
            {
                currentMaxVerNo = "V1.0";
            }
            else
            {
                decimal val = 0.0M;
                string verNumber = currentMaxVerNo.ToLower().Replace("v", "");
                if (decimal.TryParse(verNumber, out val))
                {
                    val += 0.1M;
                    currentMaxVerNo = "V" + val.ToString("f1");
                }
            }
            return currentMaxVerNo;
        }

        /// <summary>
        /// 提交审批流
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="bizForm"></param>
        /// <param name="dataEntitys"></param>
        /// <param name="startFlowList"></param>
        /// <param name="dicFlows"></param>
        /// <returns></returns>
        public IOperationResult SubmitFlow(UserContext userCtx,
            HtmlForm bizForm,
            DynamicObject[] dataEntitys,
            List<DynamicObject> startFlowList,
            List<Dictionary<string, string>> dicFlows)
        {
            OperationResult result = new OperationResult();

            //记录每个业务对象的可用流程
            List<Dictionary<string, object>> billFlows = new List<Dictionary<string, object>>();
            List<Dictionary<string, object>> flowInstances = new List<Dictionary<string, object>>();

            var metaService = userCtx.Container.GetService<IMetaModelService>();
            var dataManager = userCtx.Container.GetService<IDataManager>();
            var dbService = userCtx.Container.GetService<IDBService>();

            var pkidProperty = bizForm?.GetDynamicObjectType(userCtx)?.PrimaryKey;
            var numberProperty = bizForm?.GetNumberField()?.DynamicProperty;
            var statusProperty = bizForm?.GetField(bizForm?.BizStatusFldKey)?.DynamicProperty;

            var noFlowDataEntitys = new List<DynamicObject>();
            foreach (var dataEntity in dataEntitys)
            {
                var billPkid = pkidProperty?.GetValue<string>(dataEntity);
                var billNo = numberProperty?.GetValue<string>(dataEntity);

                //记录每个单据提交过程中产生的其他表单数据包，以便在出现异常时回滚数据包，如果没有出现异常则在最后统一保存数据包
                Dictionary<string, List<DynamicObject>> otherDynObjs = new Dictionary<string, List<DynamicObject>>();

                try
                {
                    //主版本流程
                    DynamicObject mainVerFlow = null;

                    //如果有提交过审批流（单据上面有流程实例ID）的单据，继续按照以前的审批流程走
                    var billFlowInstanceId = dataEntity["fflowinstanceid"] as string;

                    //流程实例
                    DynamicObject flowInstance = null;
                    if (!billFlowInstanceId.IsNullOrEmptyOrWhiteSpace() && (startFlowList == null || startFlowList.Count <= 0))
                    {
                        flowInstance = this.GetFlowInstanceById(userCtx, billFlowInstanceId);
                        if (flowInstance == null)
                        {
                            result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】关联的流程实例不存在，无法提交！");
                            continue;
                        }
                        if (!Convert.ToString(flowInstance["fflowstatus"]).EqualsIgnoreCase("flowinstancestatus_03"))
                        {
                            result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】已提交过审批流程，不允许重复提交！");
                            continue;
                        }
                        //单据上一次使用过的流程版本信息
                        var flowVerId = flowInstance["fflowid"] as string;
                        mainVerFlow = dbService.ExecuteDynamicObject(userCtx,
                            "select fdetailid id,fflowname,fvername,fismain,fdetails from t_bpm_flowversion with(nolock) where fdetailid=@fdetailid",
                            new List<SqlParam> { new SqlParam("@fdetailid", DbType.String, flowVerId) })?.FirstOrDefault();

                        if (!this.CheckBusinessFlow(userCtx, null, bizForm.Id, billPkid, mainVerFlow))
                        {
                            result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】无法满足上一次提交过的审批流程，请检查审批流中开始节点的启用条件 或者 删除流程实例后重新提交！");
                            continue;
                        }
                    }
                    else
                    {
                        //flowInstance = this.GetFlowInstanceByWhere(userCtx, bizForm.Id, billPkid);
                        //if (flowInstance != null && !Convert.ToString(flowInstance["fflowstatus"]).EqualsIgnoreCase("flowinstancestatus_03"))
                        //{
                        //    result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】已提交过审批流程，不允许重复提交！");
                        //    continue;
                        //}

                        //只要是单据上面没有流程实例ID，则提交审批流时，就应该生成新的流程实例，而不应该根据业务单据反向找上一次的流程实例后继续使用

                        //最终唯一匹配的流程
                        DynamicObject businessFlow = null;

                        var dicFlow = dicFlows?.FirstOrDefault(o => o.ContainsKey("billPkid") && o["billPkid"].EqualsIgnoreCase(billPkid));
                        var flowId = dicFlow?["flowId"] ?? "";

                        //如果没有传递流程ID，则根据业务对象去业务审批中匹配
                        if (flowId.IsNullOrEmptyOrWhiteSpace())
                        {
                            //筛选满足启动条件的流程信息
                            List<DynamicObject> validatedList = new List<DynamicObject>();
                            foreach (var startFlow in startFlowList)
                            {
                                if (this.CheckBusinessFlow(userCtx, startFlow, bizForm.Id, billPkid))
                                {
                                    validatedList.Add(startFlow);
                                }
                            }
                            if (!validatedList.Any())
                            {
                                noFlowDataEntitys.Add(dataEntity);
                                continue;
                            }
                            if (validatedList.Count > 1)
                            {
                                var flowList = new List<Dictionary<string, object>>();
                                foreach (var item in validatedList)
                                {
                                    flowList.Add(new Dictionary<string, object>
                                    {
                                        { "id", item["id"] },
                                        { "fverno", item["fverno"] },
                                        { "fflowtitle", item["fflowtitle"] },
                                        { "fdescription", item["fdescription"] },
                                        { "fdistributedate", item["fdistributedate"] },
                                        { "fcreatedate", item["fcreatedate"] }
                                    });
                                }
                                billFlows.Add(new Dictionary<string, object>
                                {
                                    { "formId", bizForm.Id },
                                    { "formCaption", bizForm.Caption },
                                    { "billPkid", billPkid },
                                    { "billNo", billNo },
                                    { "flows", flowList },
                                });
                                continue;
                            }
                            businessFlow = validatedList[0];
                        }
                        else
                        {
                            businessFlow = startFlowList?.FirstOrDefault(t => Convert.ToString(t["id"]).EqualsIgnoreCase(flowId));
                            if (businessFlow == null)
                            {
                                result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】根据流程ID【{flowId}】查不到流程信息，请检查！");
                                continue;
                            }
                            if (!this.CheckBusinessFlow(userCtx, businessFlow, bizForm.Id, billPkid))
                            {
                                noFlowDataEntitys.Add(dataEntity);
                                continue;
                            }
                        }
                        mainVerFlow = (businessFlow["fversionentry"] as DynamicObjectCollection)?.FirstOrDefault(t => Convert.ToBoolean(t["fismain"]));
                    }

                    if (mainVerFlow == null)
                    {
                        result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】没有匹配到主版本流程，请检查！");
                        continue;
                    }

                    //将主版本流程图转化为状态机并且执行
                    var billResult = this.ParseFlowChartAndExecuteStateMachine(userCtx, bizForm, dataEntity, mainVerFlow, flowInstance, result, otherDynObjs);
                    if (billResult == null) continue;

                    var isResubmit = Convert.ToBoolean(billResult["isResubmit"]);
                    var flowInstanceId = Convert.ToString(billResult["flowInstanceId"]);
                    var formOpCode = Convert.ToString(billResult["formOpCode"]);
                    var formOpName = Convert.ToString(billResult["formOpName"]);
                    var nextProcNode = Convert.ToString(billResult["nextProcNode"]);

                    //如果不是重新提交审批流，才需要反写单据流程实例ID
                    if (!isResubmit)
                    {
                        dataEntity["fflowinstanceid"] = flowInstanceId;
                    }

                    flowInstances.Add(new Dictionary<string, object>
                    {
                        { "billPkId", billPkid },
                        { "flowInstanceId", flowInstanceId }
                    });

                    //调用服务生成操作日志
                    this.LogServiceEx.SyncBatchWriteLog(userCtx, new LogEntry[]
                    {
                        new LogEntry()
                        {
                            BillFormId = bizForm.Id,
                            OpCode = "SubmitFlow",
                            OpName = "提交审批流",
                            BillIds = billPkid,
                            BillNos = billNo,
                            Level = Enu_LogLevel.Info.ToString(),
                            LogType = Enu_LogType.RecordType_03,
                            Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                            Content = "所在节点【开始】",
                            Detail = ""
                        }
                    });

                    this.Loger.WriteLogToFile($"审批流调用日志记录-修改前，业务对象：{bizForm.Id}，单据ID：{billPkid}，单据编号：{billNo}，单据状态：{statusProperty?.GetValue<string>(dataEntity)}，流程实例ID：{flowInstanceId}，formOpCode：{formOpCode}，", "ApproveFlowStatus");

                    //调用表单操作
                    this.InvokeBillOperation(userCtx, bizForm, dataEntity, result, formOpCode, formOpName);

                    this.Loger.WriteLogToFile($"审批流调用日志记录-修改后，业务对象：{bizForm.Id}，单据ID：{billPkid}，单据编号：{billNo}，单据状态：{statusProperty?.GetValue<string>(dataEntity)}，流程实例ID：{flowInstanceId}，formOpCode：{formOpCode}，", "ApproveFlowStatus");

                    //反写单据的“下一步处理节点”字段值
                    dataEntity["fnextprocnode"] = nextProcNode;
                    this.AddOtherDynObjs(otherDynObjs, bizForm.Id, dataEntity);

                    //最后统一保存数据包
                    var prepareService = userCtx.Container.GetService<IPrepareSaveDataService>();
                    foreach (var formId in otherDynObjs.Keys)
                    {
                        var htmlForm = metaService?.LoadFormModel(userCtx, formId);
                        dataManager.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));
                        prepareService?.PrepareDataEntity(userCtx, htmlForm, otherDynObjs[formId].ToArray(), null);
                        //提交审批流，不存在出现单据状态=B的情况。
                        if (bizForm.Id.Equals(formId))
                        {
                            if (Convert.ToString(dataEntity[htmlForm.BizStatusFldKey]).Equals(BillStatus.B.ToString()))
                            {
                                result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】单据状态异常，当前单据状态 {dataEntity[htmlForm.BizStatusFldKey]}！");
                                result?.ThrowIfHasError(true, $"审批流回调【{formOpName}】操作时出错！");
                            }
                        }
                        dataManager.Save(otherDynObjs[formId]);
                    }

                    result.ComplexMessage.SuccessMessages.Add($"{bizForm.Caption}【{billNo}】审批流提交成功！");
                }
                catch (Exception ex)
                {
                    throw;
                }
            }
            if (noFlowDataEntitys.Any())
            {
                var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
                result = gateway?.InvokeBillOperation(userCtx, bizForm.Id, dataEntitys, "submit", new Dictionary<string, object>()) as OperationResult;
                if (result.InteractionData != null)
                {
                    var ex = new InteractionException(result.InteractionData.Sponsor, result.InteractionData.SimpleMessage);
                    ex.InteractionData.Sponsor = result.InteractionData.Sponsor;
                    ex.InteractionData.SimpleMessage = result.InteractionData.SimpleMessage;
                    ex.InteractionData.CustomFormId = result.InteractionData.CustomFormId;
                    ex.InteractionData.CustomFormParameter = result.InteractionData.CustomFormParameter;
                    ex.InteractionData.SimpleMessage = result.InteractionData.SimpleMessage;
                    throw ex;
                }
                return result;
            }
            result.SrvData = new
            {
                billFlows = billFlows,
                flowInstances = flowInstances
            };
            result.IsShowMessage = true;
            result.IsSuccess = result.ComplexMessage.SuccessMessages.Count > 0;

            return result;
        }

        /// <summary>
        /// 终止审批流
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="bizForm"></param>
        /// <param name="dataEntitys"></param>
        /// <param name="execOpinion"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        public IOperationResult TerminateFlow(UserContext userCtx, HtmlForm bizForm, DynamicObject[] dataEntitys, string execOpinion, OperateOption option = null)
        {
            OperationResult result = new OperationResult();

            var pkidProperty = bizForm?.GetDynamicObjectType(userCtx)?.PrimaryKey;
            var numberProperty = bizForm?.GetNumberField()?.DynamicProperty;
            var flowInstanceProperty = bizForm?.GetField("fflowinstanceid")?.DynamicProperty;

            foreach (var dataEntity in dataEntitys)
            {
                var billPkid = pkidProperty?.GetValue<string>(dataEntity);
                var billNo = numberProperty?.GetValue<string>(dataEntity);
                var flowInstanceId = flowInstanceProperty?.GetValue<string>(dataEntity);
                if (flowInstanceId.IsNullOrEmptyOrWhiteSpace())
                {
                    result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】未提交审批流程，不允许撤销！");
                    continue;
                }
                //流程实例
                var flowInstance = this.GetFlowInstanceById(userCtx, flowInstanceId);
                if (flowInstance == null)
                {
                    result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】关联的流程实例不存在，不允许撤销！");
                    continue;
                }
                if (Convert.ToString(flowInstance["fflowstatus"]).EqualsIgnoreCase("flowinstancestatus_03"))
                {
                    result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】未提交审批流程，不允许撤销！");
                    continue;
                }

                //记录每个单据提交过程中产生的其他表单数据包，以便在出现异常时回滚数据包，如果没有出现异常则在最后统一保存数据包
                Dictionary<string, List<DynamicObject>> otherDynObjs = new Dictionary<string, List<DynamicObject>>();

                //记录数据包
                this.AddOtherDynObjs(otherDynObjs, "bpm_flowinstance", flowInstance);

                try
                {
                    //反序列化状态机
                    var jsonStateMachine = flowInstance["fstatemachine"] as string;
                    var _stateMachine = JsonConvert.DeserializeObject<BusinessStateMachine<BusinessState>>(jsonStateMachine);

                    //当前节点
                    BusinessState state = _stateMachine.CurrentState;

                    //当前状态是否是结束状态
                    var currentStateIsFinalState = state == _stateMachine.FinalState;

                    //节点数据
                    //如果当前状态是结束状态，则对结束状态的前一个状态所对应的节点做权限校验
                    var node = JsonConvert.DeserializeObject<BPMSectionModel>(currentStateIsFinalState ? (state.PrevState.Data as string) : (state.Data as string));

                    //节点权限
                    List<BPMPermissionModel> nodePermList = node?.param?.costData?.fparamentry;
                    if (nodePermList == null || nodePermList.Count() <= 0)
                    {
                        result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】所对应的【{state.Name}】节点没有设置权限控制，不允许撤销！");
                        continue;
                    }
                    BPMPermissionModel nodePerm = nodePermList.FirstOrDefault(t => (t.fotype?.id ?? "").EqualsIgnoreCase("opera_type_02") && t.fpermit);
                    if (nodePerm == null)
                    {
                        result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】所对应的【{state.Name}】节点没有设置【终止】的操作权限，不允许撤销！");
                        continue;
                    }
                    var formOpCode = "unsubmit";
                    var formOpName = "撤销";

                    if (Convert.ToString(dataEntity["fchangestatus"]) == "0")
                    {
                        bool canTerminate = true;
                        //节点参与人用户列表:如果当前状态是结束状态，则对结束状态的前一个状态所对应的节点做权限校验
                        Dictionary<string, string> userList = this.GetNodeParticipantsUserList(userCtx, currentStateIsFinalState ? state.PrevState : state, flowInstance);
                        if (!userList.ContainsKey(userCtx?.UserName))
                        {
                            canTerminate = IsStartNodeHasTerminateFlowPermission(userCtx, state, flowInstance);
                        }

                        if (!canTerminate)
                        {
                            result.ComplexMessage.ErrorMessages.Add($"您不是{bizForm.Caption}【{billNo}】所对应的【{state.Name}】节点的参与人 或 已超出流程代理时间，不允许撤销！");
                            continue;
                        }
                    }

                    //更新流程实例相关字段值
                    flowInstance["finitiator"] = "";
                    flowInstance["finitiatornumber"] = "";
                    flowInstance["fcurrentnode"] = "";
                    flowInstance["foperator"] = "";
                    flowInstance["foperatornumber"] = "";
                    flowInstance["fflowstatus"] = "flowinstancestatus_03"; //流程实例状态改为“未提交”
                    flowInstance["fstatemachine"] = "";
                    flowInstance["ferrormsg"] = "";

                    //当前节点ID
                    string currentNodeId = flowInstance["fcurrentnode"] as string;
                    var executionForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, "bpm_execution");
                    var _executionDm = userCtx.GetDefaultDataManager(executionForm);

                    var nodeName = string.Empty;
                    var currentNode = _executionDm.Select(currentNodeId) as DynamicObject;
                    if (currentNode != null)
                    {
                        nodeName = currentNode["fname"] as string;

                        //更新当前节点信息
                        currentNode["fopname"] = "终止";
                        currentNode["fsectionstatus"] = "flownodestatus_02";

                        var entrys = currentNode["fentry"] as DynamicObjectCollection;
                        foreach (var entry in entrys)
                        {
                            entry["fexec"] = this.GetOperator(userCtx);
                            entry["fexecnumber"] = userCtx?.UserName;
                            entry["fexecdate"] = DateTime.Now;
                            entry["fexecopinion"] = execOpinion;
                        }

                        this.AddOtherDynObjs(otherDynObjs, "bpm_execution", currentNode);
                    }

                    //调用服务生成操作日志
                    this.LogServiceEx.SyncBatchWriteLog(userCtx, new LogEntry[]
                    {
                        new LogEntry()
                        {
                            BillFormId = bizForm.Id,
                            OpCode = "terminate",
                            OpName = "审批流终止",
                            BillIds = billPkid,
                            BillNos = billNo,
                            Level = Enu_LogLevel.Info.ToString(),
                            LogType = Enu_LogType.RecordType_03,
                            Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                            Content = $"所在节点【{nodeName}】{execOpinion}",
                            Detail = ""
                        }
                    });

                    //调用表单操作
                    if (!formOpCode.IsNullOrEmptyOrWhiteSpace())
                    {
                        var isCallBackFormOp = true;
                        if (option != null && !option.TryGetVariableValue<bool>("isCallBackFormOp", out isCallBackFormOp))
                        {
                            isCallBackFormOp = true;
                        }
                        if (isCallBackFormOp)
                        {
                            this.InvokeBillOperation(userCtx, bizForm, dataEntity, result, formOpCode, formOpName);
                        }
                    }

                    dataEntity["fnextprocnode"] = ""; //反写单据的“下一步处理节点”字段值
                    dataEntity["fflowinstanceid"] = string.Empty; //清除当前单据的流程实例引用
                    this.AddOtherDynObjs(otherDynObjs, bizForm.Id, dataEntity);

                    //最后统一保存数据包
                    var prepareService = userCtx.Container.GetService<IPrepareSaveDataService>();
                    foreach (var formId in otherDynObjs.Keys)
                    {
                        var htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, formId);
                        var dataManager = userCtx.Container.GetService<IDataManager>();
                        dataManager.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));
                        prepareService?.PrepareDataEntity(userCtx, htmlForm, otherDynObjs[formId].ToArray(), null);
                        dataManager.Save(otherDynObjs[formId]);
                    }

                    result.ComplexMessage.SuccessMessages.Add($"{bizForm.Caption}【{billNo}】审批流撤销成功！");
                }
                catch (Exception)
                {
                    throw;
                }
            }

            result.IsShowMessage = true;
            result.IsSuccess = result.ComplexMessage.SuccessMessages.Count > 0;

            return result;
        }

        /// <summary>
        /// 开始节点是否拥有终止流程的权限
        /// </summary>
        /// <param name="userCtx">当前用户上下文</param>
        /// <param name="state">当前节点业务状态</param>
        /// <param name="flowInstance">流程实例</param>
        /// <returns></returns>
        private bool IsStartNodeHasTerminateFlowPermission(UserContext userCtx, BusinessState state, DynamicObject flowInstance)
        {
            var prevState = state.PrevState;
            var prevNode = JsonConvert.DeserializeObject<BPMSectionModel>(prevState.Data as string);//当前节点的前一节点
            if (prevNode.stepType == (int)SectionTypes.Start)
            {
                Dictionary<string, string> prevNodeUserList = this.GetNodeParticipantsUserList(userCtx, prevState, flowInstance);
                if (prevNodeUserList.ContainsKey(userCtx?.UserName))
                {
                    List<BPMPermissionModel> prevNodePermList = prevNode?.param?.costData?.fparamentry;
                    if (prevNodePermList != null && prevNodePermList.Count() > 0)
                    {
                        BPMPermissionModel prevNodeUnSubmitPerm = prevNodePermList.FirstOrDefault(t => (t.fotype?.id ?? "").EqualsIgnoreCase("opera_type_03") && t.fpermit); //拥有“撤销”权限
                        if (prevNodeUnSubmitPerm != null)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 审核审批流
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="bizForm"></param>
        /// <param name="dataEntitys"></param>
        /// <param name="execOpinion"></param>
        /// <returns></returns>
        public IOperationResult AuditFlow(UserContext userCtx, HtmlForm bizForm, DynamicObject[] dataEntitys, string execOpinion)
        {
            OperationResult result = new OperationResult();

            var pkidProperty = bizForm?.GetDynamicObjectType(userCtx)?.PrimaryKey;
            var numberProperty = bizForm?.GetNumberField()?.DynamicProperty;
            var statusProperty = bizForm?.GetField(bizForm?.BizStatusFldKey)?.DynamicProperty;
            var flowInstanceProperty = bizForm?.GetField("fflowinstanceid")?.DynamicProperty;

            foreach (var dataEntity in dataEntitys)
            {
                var billPkid = pkidProperty?.GetValue<string>(dataEntity);
                var billNo = numberProperty?.GetValue<string>(dataEntity);
                var status = statusProperty?.GetValue<string>(dataEntity);
                var flowInstanceId = flowInstanceProperty?.GetValue<string>(dataEntity);
                if (flowInstanceId.IsNullOrEmptyOrWhiteSpace())
                {
                    result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】未提交审批流程，不允许审核！");
                    continue;
                }

                //流程实例
                var flowInstance = this.GetFlowInstanceById(userCtx, flowInstanceId);
                if (flowInstance == null)
                {
                    result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】关联的流程实例不存在，不允许审核！");
                    continue;
                }
                if (Convert.ToString(flowInstance["fflowstatus"]).EqualsIgnoreCase("flowinstancestatus_02"))
                {
                    result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】提交的审批流程已完成，不允许审核！");
                    continue;
                }
                if (Convert.ToString(flowInstance["fflowstatus"]).EqualsIgnoreCase("flowinstancestatus_03"))
                {
                    result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】未提交审批流程，不允许审核！");
                    continue;
                }

                //记录每个单据提交过程中产生的其他表单数据包，以便在出现异常时回滚数据包，如果没有出现异常则在最后统一保存数据包
                Dictionary<string, List<DynamicObject>> otherDynObjs = new Dictionary<string, List<DynamicObject>>();

                //记录数据包
                this.AddOtherDynObjs(otherDynObjs, "bpm_flowinstance", flowInstance);

                try
                {
                    //状态机执行前的当前节点信息
                    var executionForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, "bpm_execution");
                    var _executionDm = userCtx.GetDefaultDataManager(executionForm);
                    var nodeName = string.Empty;
                    var sectionId = flowInstance?["fcurrentnode"] as string;
                    if (!sectionId.IsNullOrEmptyOrWhiteSpace())
                    {
                        var _currentNode = _executionDm.Select(sectionId) as DynamicObject;
                        if (_currentNode != null)
                        {
                            nodeName = _currentNode["fname"] as string;
                        }
                    }

                    //反序列化状态机
                    var jsonStateMachine = flowInstance["fstatemachine"] as string;
                    var _stateMachine = JsonConvert.DeserializeObject<BusinessStateMachine<BusinessState>>(jsonStateMachine);
                    _stateMachine.StateFunc = new BusinessStateEvent();

                    // 变更时检查当前登录用户是否包含在当前节点的参与人列表
                    var changeStatus = Convert.ToInt32(dataEntity["fchangestatus"]);
                    if (changeStatus > 0)
                    {
                        //当前节点
                        BusinessState state = _stateMachine.CurrentState;
                        //当前状态是否是结束状态
                        var currentStateIsFinalState = state == _stateMachine.FinalState;
                        Dictionary<string, string> userList = this.GetNodeParticipantsUserList(userCtx, currentStateIsFinalState ? state.PrevState : state, flowInstance);
                        if (!userList.ContainsKey(userCtx?.UserName))
                        {
                            result.ComplexMessage.ErrorMessages.Add($"您不是{bizForm.Caption}【{billNo}】所对应的【{state.Name}】节点的参与人 或 已超出流程代理时间，不允许审核！");
                            continue;
                        }
                    }

                    //执行状态机
                    dynamic dataContext = new System.Dynamic.ExpandoObject();
                    dataContext.Name = "通过";
                    dataContext.OperationResult = new OperationResult();
                    dataContext.UserContext = userCtx;
                    dataContext.FlowInstance = flowInstance;
                    dataContext.CurrentNode = null;
                    dataContext.OtherDynObjs = otherDynObjs;
                    dataContext.InitialState = _stateMachine.InitialState;
                    dataContext.FinalState = _stateMachine.FinalState;
                    dataContext.ExecOpinion = execOpinion;

                    //当前执行节点上面配置的表单审核操作
                    var node = JsonConvert.DeserializeObject<BPMSectionModel>(_stateMachine.CurrentState.Data as string);
                    List<BPMPermissionModel> nodePermList = node?.param?.costData?.fparamentry;
                    BPMPermissionModel nodePerm = nodePermList?.FirstOrDefault(t => (t.fotype?.id ?? "").EqualsIgnoreCase("opera_type_01"));
                    var formOpCode = nodePerm?.fformop?.id;
                    var formOpName = nodePerm?.fformop?.fname;

                    //执行到下一状态
                    _stateMachine.TriggerToNextState(dataContext);

                    //反写状态机Json配置信息到流程实例中
                    flowInstance["fstatemachine"] = JsonConvert.SerializeObject(_stateMachine);

                    //状态机执行后的当前节点信息
                    var currentNode = dataContext.CurrentNode as DynamicObject;
                    if (currentNode == null)
                    {
                        sectionId = flowInstance?["fcurrentnode"] as string;
                        if (!sectionId.IsNullOrEmptyOrWhiteSpace())
                        {
                            currentNode = _executionDm.Select(sectionId) as DynamicObject;
                        }
                    }
                    //获取流程实例中的当前节点状态
                    var stateComplete = false;
                    if (currentNode != null)
                    {
                        stateComplete = Convert.ToString(currentNode["fsectionstatus"]).EqualsIgnoreCase("flownodestatus_02");
                    }

                    //调用服务生成操作日志
                    this.LogServiceEx.SyncBatchWriteLog(userCtx, new LogEntry[]
                    {
                        new LogEntry()
                        {
                            BillFormId = bizForm.Id,
                            OpCode = "audit",
                            OpName = "审批流审核",
                            BillIds = billPkid,
                            BillNos = billNo,
                            Level = Enu_LogLevel.Info.ToString(),
                            LogType = Enu_LogType.RecordType_03,
                            Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                            Content = $"所在节点【{nodeName}】{execOpinion}",
                            Detail = ""
                        }
                    });

                    this.Loger.WriteLogToFile($"审批流调用日志记录-修改前，业务对象：{bizForm.Id}，单据ID：{billPkid}，单据编号：{billNo}，单据状态：{status}，流程实例ID：{flowInstanceId}，formOpCode：{formOpCode}，", "ApproveFlowStatus");
                    //调用表单操作
                    //根据节点的完成情况来确定是否要更新单据状态（以便区分是会签模式 还是 非会签模式）
                    if (!formOpCode.IsNullOrEmptyOrWhiteSpace() && stateComplete)
                    {
                        this.InvokeBillOperation(userCtx, bizForm, dataEntity, result, formOpCode, formOpName, execOpinion);
                    }
                    this.Loger.WriteLogToFile($"审批流调用日志记录-修改后，业务对象：{bizForm.Id}，单据ID：{billPkid}，单据编号：{billNo}，单据状态：{statusProperty?.GetValue<string>(dataEntity)}，流程实例ID：{flowInstanceId}，formOpCode：{formOpCode}，", "ApproveFlowStatus");

                    //反写单据的“下一步处理节点”字段值
                    dataEntity["fnextprocnode"] = _stateMachine.CurrentState.Name;
                    this.AddOtherDynObjs(otherDynObjs, bizForm.Id, dataEntity);

                    //最后统一保存数据包
                    var prepareService = userCtx.Container.GetService<IPrepareSaveDataService>();
                    foreach (var formId in otherDynObjs.Keys)
                    {
                        var htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, formId);
                        var dataManager = userCtx.Container.GetService<IDataManager>();
                        dataManager.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));
                        prepareService?.PrepareDataEntity(userCtx, htmlForm, otherDynObjs[formId].ToArray(), null);
                        dataManager.Save(otherDynObjs[formId]);
                    }

                    result.ComplexMessage.SuccessMessages.Add($"{bizForm.Caption}【{billNo}】审批流审核成功！");
                }
                catch (Exception)
                {
                    throw;
                }
            }

            result.IsShowMessage = true;
            result.IsSuccess = result.ComplexMessage.SuccessMessages.Count > 0;

            return result;
        }

        /// <summary>
        /// 驳回审批流
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="bizForm"></param>
        /// <param name="dataEntitys"></param>
        /// <param name="execOpinion"></param>
        /// <param name="terminate"></param>
        /// <returns></returns>
        public IOperationResult RejectFlow(UserContext userCtx, HtmlForm bizForm, DynamicObject[] dataEntitys, string execOpinion, bool terminate = false)
        {
            OperationResult result = new OperationResult();

            var executionForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, "bpm_execution");
            var _executionDm = userCtx.GetDefaultDataManager(executionForm);

            var pkidProperty = bizForm?.GetDynamicObjectType(userCtx)?.PrimaryKey;
            var numberProperty = bizForm?.GetNumberField()?.DynamicProperty;
            var statusProperty = bizForm?.GetField(bizForm?.BizStatusFldKey)?.DynamicProperty;
            var flowInstanceProperty = bizForm?.GetField("fflowinstanceid")?.DynamicProperty;

            foreach (var dataEntity in dataEntitys)
            {
                var billPkid = pkidProperty?.GetValue<string>(dataEntity);
                var billNo = numberProperty?.GetValue<string>(dataEntity);
                var status = statusProperty?.GetValue<string>(dataEntity);
                var flowInstanceId = flowInstanceProperty?.GetValue<string>(dataEntity);
                if (flowInstanceId.IsNullOrEmptyOrWhiteSpace())
                {
                    result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】未提交审批流程，不允许反审核！");
                    continue;
                }
                //流程实例
                var flowInstance = this.GetFlowInstanceById(userCtx, flowInstanceId);
                if (flowInstance == null)
                {
                    result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】关联的流程实例不存在，不允许反审核！");
                    continue;
                }
                if (Convert.ToString(flowInstance["fflowstatus"]).EqualsIgnoreCase("flowinstancestatus_03"))
                {
                    result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】未提交审批流程，不允许反审核！");
                    continue;
                }

                //记录每个单据提交过程中产生的其他表单数据包，以便在出现异常时回滚数据包，如果没有出现异常则在最后统一保存数据包
                Dictionary<string, List<DynamicObject>> otherDynObjs = new Dictionary<string, List<DynamicObject>>();

                //记录数据包
                this.AddOtherDynObjs(otherDynObjs, "bpm_flowinstance", flowInstance);

                var formOpCode = string.Empty;
                var formOpName = string.Empty;
                var flowOpName = "审批流驳回";

                try
                {
                    //反序列化状态机
                    var jsonStateMachine = flowInstance["fstatemachine"] as string;
                    var _stateMachine = JsonConvert.DeserializeObject<BusinessStateMachine<BusinessState>>(jsonStateMachine);

                    //当前节点ID
                    string currentNodeId = flowInstance["fcurrentnode"] as string;
                    var currentNode = _executionDm.Select(currentNodeId) as DynamicObject;
                    var nodeName = currentNode?["fname"] as string;

                    //终止流程
                    if (terminate)
                    {
                        #region 审批流终止

                        flowOpName = "审批流终止";

                        //当前节点
                        BusinessState state = _stateMachine.CurrentState;

                        //当前状态是否是结束状态
                        var currentStateIsFinalState = state == _stateMachine.FinalState;

                        //节点数据
                        //如果当前状态是结束状态，则对结束状态的前一个状态所对应的节点做权限校验
                        var node = JsonConvert.DeserializeObject<BPMSectionModel>(currentStateIsFinalState ? (state.PrevState.Data as string) : (state.Data as string));

                        //节点权限
                        List<BPMPermissionModel> nodePermList = node?.param?.costData?.fparamentry;
                        if (nodePermList == null || nodePermList.Count() <= 0)
                        {
                            result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】所对应的【{state.Name}】节点没有设置权限控制，不允许撤销！");
                            continue;
                        }
                        BPMPermissionModel nodePerm = nodePermList.FirstOrDefault(t => (t.fotype?.id ?? "").EqualsIgnoreCase("opera_type_02") && t.fpermit);
                        if (nodePerm == null)
                        {
                            result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】所对应的【{state.Name}】节点没有设置【终止】的操作权限，不允许撤销！");
                            continue;
                        }

                        //节点参与人用户列表
                        //如果当前状态是结束状态，则对结束状态的前一个状态所对应的节点做权限校验
                        Dictionary<string, string> userList = this.GetNodeParticipantsUserList(userCtx, currentStateIsFinalState ? state.PrevState : state, flowInstance);
                        if (!userList.ContainsKey(userCtx?.UserName))
                        {
                            //您不是销售合同【DHD20180730001】所对应的【店长审批】节点的参与人，无法撤销审批流！
                            result.ComplexMessage.ErrorMessages.Add($"您不是{bizForm.Caption}【{billNo}】所对应的【{state.Name}】节点的参与人 或 已超出流程代理时间，不允许撤销！");
                            continue;
                        }

                        //更新流程实例相关字段值
                        flowInstance["finitiator"] = "";
                        flowInstance["finitiatornumber"] = "";
                        flowInstance["fcurrentnode"] = "";
                        flowInstance["foperator"] = "";
                        flowInstance["foperatornumber"] = "";
                        flowInstance["fflowstatus"] = "flowinstancestatus_03"; //流程实例状态改为“未提交”
                        flowInstance["fstatemachine"] = "";
                        flowInstance["ferrormsg"] = "";

                        if (currentNode != null)
                        {
                            //更新当前节点信息
                            currentNode["fopname"] = "终止";
                            currentNode["fsectionstatus"] = "flownodestatus_02";

                            var entrys = currentNode["fentry"] as DynamicObjectCollection;
                            foreach (var entry in entrys)
                            {
                                entry["fexec"] = this.GetOperator(userCtx);
                                entry["fexecnumber"] = userCtx?.UserName;
                                entry["fexecdate"] = DateTime.Now;
                                entry["fexecopinion"] = execOpinion;
                            }

                            //记录数据包
                            this.AddOtherDynObjs(otherDynObjs, "bpm_execution", currentNode);
                        }

                        //如果此时的单据状态为“已提交”，则自动回调“撤销操作”
                        if (status.EqualsIgnoreCase(BillStatus.D.ToString()))
                        {
                            formOpCode = "unsubmit";
                            formOpName = "撤销";
                        }
                        else if (status.EqualsIgnoreCase(BillStatus.E.ToString()))
                        {
                            //如果此时的单据状态为“已审核”，则自动回调“反审核操作”
                            formOpCode = "unaudit";
                            formOpName = "反审核";
                        }

                        dataEntity["fnextprocnode"] = ""; //反写单据的“下一步处理节点”字段值
                        dataEntity["fflowinstanceid"] = string.Empty; //清除当前单据的流程实例引用

                        this.AddOtherDynObjs(otherDynObjs, bizForm.Id, dataEntity);
                        #endregion
                    }
                    else
                    {
                        #region 审批流驳回

                        _stateMachine.StateFunc = new BusinessStateEvent();

                        //执行状态机
                        dynamic dataContext = new System.Dynamic.ExpandoObject();
                        dataContext.Name = "驳回";
                        dataContext.OperationResult = new OperationResult();
                        dataContext.UserContext = userCtx;
                        dataContext.FlowInstance = flowInstance;
                        dataContext.CurrentNode = null;
                        dataContext.OtherDynObjs = otherDynObjs;
                        dataContext.InitialState = _stateMachine.InitialState;
                        dataContext.FinalState = _stateMachine.FinalState;
                        dataContext.ExecOpinion = execOpinion;

                        //当前状态的上一状态
                        var prevState = _stateMachine.CurrentState.PrevState;

                        //执行到上一状态
                        _stateMachine.TriggerToPrevState(dataContext);

                        if (prevState != _stateMachine.InitialState)
                        {
                            //反写状态机Json配置信息到流程实例中
                            flowInstance["fstatemachine"] = JsonConvert.SerializeObject(_stateMachine);

                            //如果此时的单据状态为“已审核”，则自动回调“反审核操作”后，再自动提交审批流
                            if (status.EqualsIgnoreCase(BillStatus.E.ToString()))
                            {
                                //调用表单操作
                                this.InvokeBillOperation(userCtx, bizForm, dataEntity, result, "unaudit", "反审核");

                                formOpCode = "submit";
                                formOpName = "提交";
                            }
                        }
                        else
                        {
                            //如果上一状态是结束状态，说明已经驳回到了开始节点

                            //如果此时的单据状态为“已提交”，则自动回调“撤销操作”
                            if (status.EqualsIgnoreCase(BillStatus.D.ToString()))
                            {
                                formOpCode = "unsubmit";
                                formOpName = "撤销";
                            }
                            else if (status.EqualsIgnoreCase(BillStatus.E.ToString()))
                            {
                                //如果此时的单据状态为“已审核”，则自动回调“反审核操作”
                                formOpCode = "unaudit";
                                formOpName = "反审核";
                            }
                        }

                        //反写单据的“下一步处理节点”字段值
                        dataEntity["fnextprocnode"] = _stateMachine.CurrentState == _stateMachine.InitialState ? "" : _stateMachine.CurrentState.Name;
                        this.AddOtherDynObjs(otherDynObjs, bizForm.Id, dataEntity);
                        #endregion
                    }

                    //调用服务生成操作日志
                    this.LogServiceEx.SyncBatchWriteLog(userCtx, new LogEntry[]
                    {
                        new LogEntry()
                        {
                            BillFormId = bizForm.Id,
                            OpCode = "reject",
                            OpName = flowOpName,
                            BillIds = billPkid,
                            BillNos = billNo,
                            Level = Enu_LogLevel.Info.ToString(),
                            LogType = Enu_LogType.RecordType_03,
                            Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                            Content = $"所在节点【{nodeName}】{execOpinion}",
                            Detail = ""
                        }
                    });

                    //调用表单操作
                    this.InvokeBillOperation(userCtx, bizForm, dataEntity, result, formOpCode, formOpName, auto: true);

                    //最后统一保存数据包
                    var prepareService = userCtx.Container.GetService<IPrepareSaveDataService>();
                    foreach (var formId in otherDynObjs.Keys)
                    {
                        var htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, formId);
                        var dataManager = userCtx.Container.GetService<IDataManager>();
                        dataManager.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));
                        prepareService?.PrepareDataEntity(userCtx, htmlForm, otherDynObjs[formId].ToArray(), null);
                        dataManager.Save(otherDynObjs[formId]);
                    }

                    result.ComplexMessage.SuccessMessages.Add($"{bizForm.Caption}【{billNo}】{flowOpName}成功！");
                }
                catch (Exception)
                {
                    throw;
                }
            }

            result.IsShowMessage = true;
            result.IsSuccess = result.ComplexMessage.SuccessMessages.Count > 0;

            return result;
        }

        /// <summary>
        /// 终止所有进行中的审批流(业务审批操作执行)
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="bizForm"></param>
        /// <param name="dataEntitys"></param>
        /// <param name="execOpinion"></param>
        /// <returns></returns>
        public IOperationResult RejectAllFlow(UserContext userCtx, HtmlForm bizForm, DynamicObject[] dataEntitys, string execOpinion)
        {
            OperationResult result = new OperationResult();

            var executionForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, "bpm_execution");
            var _executionDm = userCtx.GetDefaultDataManager(executionForm);

            var pkidProperty = bizForm?.GetDynamicObjectType(userCtx)?.PrimaryKey;
            var numberProperty = bizForm?.GetNumberField()?.DynamicProperty;
            var statusProperty = bizForm?.GetField(bizForm?.BizStatusFldKey)?.DynamicProperty;
            var changeStatusProperty = bizForm?.GetField(bizForm?.ChangeStatusFldKey)?.DynamicProperty;
            var flowInstanceProperty = bizForm?.GetField("fflowinstanceid")?.DynamicProperty;

            foreach (var dataEntity in dataEntitys)
            {
                var billPkid = pkidProperty?.GetValue<string>(dataEntity);
                var billNo = numberProperty?.GetValue<string>(dataEntity);
                var status = statusProperty?.GetValue<string>(dataEntity);
                var changestatus = changeStatusProperty?.GetValue<string>(dataEntity);
                var flowInstanceId = flowInstanceProperty?.GetValue<string>(dataEntity);
                var createName = (dataEntity["fcreatorid_ref"] as DynamicObject)?["fname"];
                if (flowInstanceId.IsNullOrEmptyOrWhiteSpace())
                {
                    result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】未提交审批流程，终止失败！");
                    continue;
                }
                //流程实例
                var flowInstance = this.GetFlowInstanceById(userCtx, flowInstanceId);
                if (flowInstance == null)
                {
                    result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】关联的流程实例不存在，终止失败！");
                    continue;
                }
                if (Convert.ToString(flowInstance["fflowstatus"]).EqualsIgnoreCase("flowinstancestatus_03"))
                {
                    result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】未提交审批流程，终止失败！");
                    continue;
                }

                if (changestatus.EqualsIgnoreCase("3"))
                {
                    //变更状态为变更已提交，不允许撤销
                    result.ComplexMessage.WarningMessages.Add($"{billNo + createName}");
                    continue;
                }

                //记录每个单据提交过程中产生的其他表单数据包，以便在出现异常时回滚数据包，如果没有出现异常则在最后统一保存数据包
                Dictionary<string, List<DynamicObject>> otherDynObjs = new Dictionary<string, List<DynamicObject>>();

                //记录数据包
                this.AddOtherDynObjs(otherDynObjs, "bpm_flowinstance", flowInstance);

                var formOpCode = string.Empty;
                var formOpName = string.Empty;
                var flowOpName = "审批流驳回";

                try
                {
                    //反序列化状态机
                    var jsonStateMachine = flowInstance["fstatemachine"] as string;
                    var _stateMachine = JsonConvert.DeserializeObject<BusinessStateMachine<BusinessState>>(jsonStateMachine);

                    //当前节点ID
                    string currentNodeId = flowInstance["fcurrentnode"] as string;
                    var currentNode = _executionDm.Select(currentNodeId) as DynamicObject;
                    var nodeName = currentNode?["fname"] as string;

                    //终止流程，在业务审批单据操作终止审批流不用判断，直接终止
                    #region 审批流终止

                    flowOpName = "审批流终止";

                    //更新流程实例相关字段值
                    flowInstance["finitiator"] = "";
                    flowInstance["finitiatornumber"] = "";
                    flowInstance["fcurrentnode"] = "";
                    flowInstance["foperator"] = "";
                    flowInstance["foperatornumber"] = "";
                    flowInstance["fflowstatus"] = "flowinstancestatus_03"; //流程实例状态改为“未提交”
                    flowInstance["fstatemachine"] = "";
                    flowInstance["ferrormsg"] = "";

                    if (currentNode != null)
                    {
                        //更新当前节点信息
                        currentNode["fopname"] = "终止";
                        currentNode["fsectionstatus"] = "flownodestatus_02";

                        var entrys = currentNode["fentry"] as DynamicObjectCollection;
                        foreach (var entry in entrys)
                        {
                            entry["fexec"] = this.GetOperator(userCtx);
                            entry["fexecnumber"] = userCtx?.UserName;
                            entry["fexecdate"] = DateTime.Now;
                            entry["fexecopinion"] = execOpinion;
                        }

                        //记录数据包
                        this.AddOtherDynObjs(otherDynObjs, "bpm_execution", currentNode);
                    }

                    //如果此时的单据状态为“已提交”，则自动回调“撤销操作”
                    if (status.EqualsIgnoreCase(BillStatus.D.ToString()))
                    {
                        formOpCode = "unsubmit";
                        formOpName = "撤销";
                    }
                    else if (status.EqualsIgnoreCase(BillStatus.E.ToString()))
                    {
                        //如果此时的单据状态为“已审核”，则自动回调“反审核操作”
                        formOpCode = "unaudit";
                        formOpName = "反审核";
                    }

                    dataEntity["fnextprocnode"] = ""; //反写单据的“下一步处理节点”字段值
                    dataEntity["fflowinstanceid"] = string.Empty; //清除当前单据的流程实例引用

                    this.AddOtherDynObjs(otherDynObjs, bizForm.Id, dataEntity);
                    #endregion

                    //调用服务生成操作日志
                    this.LogServiceEx.SyncBatchWriteLog(userCtx, new LogEntry[]
                    {
                        new LogEntry()
                        {
                            BillFormId = bizForm.Id,
                            OpCode = "reject",
                            OpName = flowOpName,
                            BillIds = billPkid,
                            BillNos = billNo,
                            Level = Enu_LogLevel.Info.ToString(),
                            LogType = Enu_LogType.RecordType_03,
                            Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                            Content = $"所在节点【{nodeName}】{execOpinion}",
                            Detail = ""
                        }
                    });

                    //调用表单操作
                    this.InvokeBillOperation(userCtx, bizForm, dataEntity, result, formOpCode, formOpName);

                    //最后统一保存数据包
                    var prepareService = userCtx.Container.GetService<IPrepareSaveDataService>();
                    foreach (var formId in otherDynObjs.Keys)
                    {
                        var htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, formId);
                        var dataManager = userCtx.Container.GetService<IDataManager>();
                        dataManager.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));
                        prepareService?.PrepareDataEntity(userCtx, htmlForm, otherDynObjs[formId].ToArray(), null);
                        dataManager.Save(otherDynObjs[formId]);
                    }

                    result.ComplexMessage.SuccessMessages.Add($"{billNo + createName}");
                }
                catch (Exception e)
                {
                    result.ComplexMessage.ErrorMessages.Add($"{billNo + createName}终止失败，原因：" + e.Message + " 请手工处理！");
                }
            }

            result.IsShowMessage = true;
            result.IsSuccess = result.ComplexMessage.SuccessMessages.Count > 0;

            return result;
        }

        /// <summary>
        /// 调用表单操作
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="bizForm"></param>
        /// <param name="dataEntity"></param>
        /// <param name="result"></param>
        /// <param name="formOpCode"></param>
        /// <param name="formOpName"></param>
        /// <param name="execOpinion"></param>
        /// <param name="auto">自动回调</param>
        private void InvokeBillOperation(UserContext userCtx,
            HtmlForm bizForm,
            DynamicObject dataEntity,
            OperationResult result,
            string formOpCode,
            string formOpName,
            string execOpinion = "",
            bool auto = false)
        {
            //调用表单操作
            if (!formOpCode.IsNullOrEmptyOrWhiteSpace())
            {
                var option = new Dictionary<string, object>();
                if (!execOpinion.IsNullOrEmptyOrWhiteSpace())
                {
                    option["execOpinion"] = execOpinion;
                }
                //审核操作时加上参数
                if (formOpCode.EqualsIgnoreCase("audit"))
                {
                    option["AudiByBPM"] = "true";
                }
                //反审核 自动回调提交 操作时加上参数
                if (formOpCode.EqualsIgnoreCase("submit") && auto)
                {
                    option["submitByUnaudit"] = "true";
                }
                var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
                var invokeResult = gateway.InvokeBillOperation(userCtx,
                    bizForm.Id,
                    new DynamicObject[] { dataEntity },
                    formOpCode,
                    option);
                if (invokeResult != null && !invokeResult.IsSuccess)
                {
                    var errorMsgs = invokeResult.ComplexMessage.ErrorMessages;
                    if (errorMsgs?.Count > 0)
                    {
                        result.ComplexMessage.ErrorMessages.AddRange(errorMsgs);
                        this.LogServiceEx.Error($"审批流回调【{formOpName}】操作时出错：" + string.Join("\r\n", errorMsgs));
                    }
                }
                invokeResult?.ThrowIfHasError(true, $"审批流回调【{formOpName}】操作时出错！");
            }
        }

        /// <summary>
        /// 将主版本流程图转化为状态机并且执行
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="bizForm"></param>
        /// <param name="bizData"></param>
        /// <param name="mainVerFlow"></param>
        /// <param name="flowInstance"></param>
        /// <param name="result"></param>
        /// <param name="otherDynObjs"></param>
        /// <returns></returns>
        private Dictionary<string, object> ParseFlowChartAndExecuteStateMachine(UserContext userCtx,
            HtmlForm bizForm,
            DynamicObject bizData,
            DynamicObject mainVerFlow,
            DynamicObject flowInstance,
            OperationResult result,
            Dictionary<string, List<DynamicObject>> otherDynObjs)
        {
            var pkidProperty = bizForm?.GetDynamicObjectType(userCtx)?.PrimaryKey;
            var numberProperty = bizForm?.GetNumberField()?.DynamicProperty;
            var billPkid = pkidProperty?.GetValue<string>(bizData);
            var billNo = numberProperty?.GetValue<string>(bizData);

            //主版本的设计图信息
            BPMDesignDrawingModel details = JsonConvert.DeserializeObject<BPMDesignDrawingModel>(mainVerFlow["fdetails"] as string);

            //开始节点
            var startNode = details.sections.FirstOrDefault(t => t.stepType == (int)SectionTypes.Start);
            if (startNode.param.costData.fnodename.IsNullOrEmptyOrWhiteSpace())
            {
                startNode.param.costData.fnodename = "开始";
            }
            var initialState = this.SectionToBusinessState(startNode);

            //节点数据
            var node = JsonConvert.DeserializeObject<BPMSectionModel>(initialState.Data as string);

            //节点权限
            List<BPMPermissionModel> nodePermList = node?.param?.costData?.fparamentry;
            if (nodePermList == null || nodePermList.Count() <= 0)
            {
                result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】所对应的【{initialState.Name}】节点没有设置权限控制，无法提交审批流！");
                return null;
            }
            BPMPermissionModel nodePerm = nodePermList.FirstOrDefault(t => (t.fotype?.id ?? "").EqualsIgnoreCase("opera_type_01") && t.fpermit);
            if (nodePerm == null)
            {
                result.ComplexMessage.ErrorMessages.Add($"{bizForm.Caption}【{billNo}】所对应的【{initialState.Name}】节点没有设置【通过】的操作权限，无法提交审批流！");
                return null;
            }

            var htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, "bpm_flowinstance");

            //是否是重新提交审批流
            bool isResubmit = false;
            if (!Convert.ToString(bizData["fflowinstanceid"]).IsNullOrEmptyOrWhiteSpace())
            {
                isResubmit = true;
            }
            else
            {
                //创建流程实例信息
                flowInstance = new DynamicObject(htmlForm.GetDynamicObjectType(userCtx));
                flowInstance["fbizformid"] = bizForm.Id;
                flowInstance["fbizbillno"] = billNo;
                flowInstance["fbizbillpkid"] = billPkid;
                flowInstance["fstatemachine"] = "";
                flowInstance["ferrormsg"] = "";
            }
            //更新流程实例相关字段值
            flowInstance["fflowid"] = mainVerFlow["id"];
            flowInstance["fflowname"] = mainVerFlow["fflowname"];
            flowInstance["fflowver"] = mainVerFlow["fvername"];
            flowInstance["finitiator"] = this.GetOperator(userCtx);
            flowInstance["finitiatornumber"] = userCtx?.UserName;
            flowInstance["fflowstatus"] = "flowinstancestatus_01";
            flowInstance["foperator"] = this.GetOperator(userCtx);
            flowInstance["foperatornumber"] = userCtx?.UserName;

            //操作人编码
            var userNumber = userCtx?.UserName;

            if (Convert.ToString(bizData["fchangestatus"]) == "0")
            {
                //节点参与人用户列表
                Dictionary<string, string> userList = this.GetNodeParticipantsUserList(userCtx, initialState, flowInstance);
                if (!userList.ContainsKey(userNumber))
                {
                    //您不是销售合同【DHD20180730001】所对应的【开始】节点的参与人，无法提交审批流！
                    result.ComplexMessage.ErrorMessages.Add($"您不是{bizForm.Caption}【{billNo}】所对应的【{initialState.Name}】节点的参与人 或 已超出流程代理时间，无法提交审批流！");
                    return null;
                }
            }


            //结束节点
            var endNode = details.sections.FirstOrDefault(t => t.stepType == (int)SectionTypes.Finished);
            if (endNode.param.costData.fnodename.IsNullOrEmptyOrWhiteSpace())
            {
                endNode.param.costData.fnodename = "结束";
            }
            var finalState = this.SectionToBusinessState(endNode);

            //所有的过程节点，包括内部节点 和 外部节点
            var processNodes = details.sections.Where(t => t.stepType == (int)SectionTypes.InterProcess || t.stepType == (int)SectionTypes.ExterProcess);

            //将流程图中的节点转换为状态机中的业务状态
            var billStates = new List<BusinessState>() { initialState, finalState };
            foreach (var processNode in processNodes)
            {
                billStates.Add(this.SectionToBusinessState(processNode));
            }

            //状态迁移路径
            var _transitions = new List<BusinessTransition<BusinessState>>();
            foreach (var line in details.lines)
            {
                var startState = billStates.FirstOrDefault(t => t.Value.EqualsIgnoreCase(line.from));
                var endState = billStates.FirstOrDefault(t => t.Value.EqualsIgnoreCase(line.to));
                _transitions.Add(new BusinessTransition<BusinessState>()
                {
                    StartState = startState,
                    EndState = endState,
                    Condition = line?.param?.costData?.fexpper?.id ?? "",
                    Name = line?.param?.costData?.flinkname ?? "",
                    Description = line?.param?.costData?.flinkdes ?? ""
                });
            }

            //状态机
            var _stateMachine = new BusinessStateMachine<BusinessState>()
            {
                Name = "{0}_{1}_{2}_状态机".Fmt(bizForm.Caption, mainVerFlow["fflowname"], mainVerFlow["fvername"]),
                StateTransitions = _transitions.ToArray(),
                CurrentState = initialState,
                InitialState = initialState,
                FinalState = finalState,
                StateFunc = new BusinessStateEvent()
            };

            //保存前预处理
            var prepareService = userCtx.Container.GetService<IPrepareSaveDataService>();
            prepareService?.PrepareDataEntity(userCtx, htmlForm, new DynamicObject[] { flowInstance }, OperateOption.Create());

            //记录数据包
            this.AddOtherDynObjs(otherDynObjs, "bpm_flowinstance", flowInstance);

            //创建审批流开始执行节点
            var startSection = this.CreateApprovalFlowStartSection(userCtx, initialState, node, flowInstance);
            flowInstance["fcurrentnode"] = startSection["id"];

            //执行状态机
            dynamic dataContext = new System.Dynamic.ExpandoObject();
            dataContext.Name = "提交审批流";
            dataContext.OperationResult = new OperationResult();
            dataContext.UserContext = userCtx;
            dataContext.FlowInstance = flowInstance;
            dataContext.CurrentNode = startSection;
            dataContext.OtherDynObjs = otherDynObjs;
            dataContext.InitialState = initialState;
            dataContext.FinalState = finalState;
            dataContext.ExecOpinion = "";

            //执行到下一状态
            _stateMachine.TriggerToNextState(dataContext);

            //反写状态机Json配置信息到流程实例中
            flowInstance["fstatemachine"] = JsonConvert.SerializeObject(_stateMachine);

            //开始节点对应的表单操作
            var paramEntry = startNode?.param?.costData?.fparamentry?.FirstOrDefault(t => (t?.fotype?.id ?? "").EqualsIgnoreCase("opera_type_01"));

            //返回流程实例ID
            Dictionary<string, object> billResult = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
            billResult["isResubmit"] = isResubmit;
            billResult["flowInstanceId"] = flowInstance["id"];
            billResult["formOpCode"] = paramEntry?.fformop?.id;
            billResult["formOpName"] = paramEntry?.fformop?.fname;
            billResult["nextProcNode"] = _stateMachine.CurrentState.Name;

            return billResult;
        }

        /// <summary>
        /// 向指定的字典中追加动态对象
        /// </summary>
        /// <param name="otherDynObjs"></param>
        /// <param name="formId"></param>
        /// <param name="formData"></param>
        public void AddOtherDynObjs(Dictionary<string, List<DynamicObject>> otherDynObjs, string formId, DynamicObject formData)
        {
            if (formData == null) return;

            if (otherDynObjs == null)
            {
                otherDynObjs = new Dictionary<string, List<DynamicObject>>();
            }
            if (!otherDynObjs.ContainsKey(formId))
            {
                otherDynObjs[formId] = new List<DynamicObject>();
            }
            var exists = otherDynObjs[formId].FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(formData["id"])));
            if (exists == null)
            {
                otherDynObjs[formId].Add(formData);
            }
        }

        /// <summary>
        /// 将流程节点设计图转换成业务状态
        /// </summary>
        /// <param name="section"></param>
        /// <returns></returns>
        private BusinessState SectionToBusinessState(BPMSectionModel section)
        {
            BusinessState state = new BusinessState();
            state.Name = section?.param?.costData?.fnodename ?? "";
            state.Value = section.key;
            state.Description = section?.param?.costData?.fnodedes ?? "";
            state.Data = section.ToJson();
            return state;
        }

        /// <summary>
        /// 检查业务流程是否满足启动条件
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="businessFlow"></param>
        /// <param name="formId"></param>
        /// <param name="billPkid"></param>
        /// <param name="mainVerFlow"></param>
        /// <returns></returns>
        private bool CheckBusinessFlow(UserContext userCtx, DynamicObject businessFlow, string formId, string billPkid, DynamicObject mainVerFlow = null)
        {
            //流程版本信息
            if (businessFlow != null)
            {
                var versionEntrys = businessFlow["fversionentry"] as DynamicObjectCollection;
                if (versionEntrys != null)
                {
                    //主版本流程
                    mainVerFlow = versionEntrys.FirstOrDefault(t => Convert.ToBoolean(t["fismain"]));
                }
            }
            if (mainVerFlow == null) return false;

            string jsonDetails = mainVerFlow["fdetails"] as string;
            if (jsonDetails.IsNullOrEmptyOrWhiteSpace()) return false;

            BPMDesignDrawingModel details = JsonConvert.DeserializeObject<BPMDesignDrawingModel>(jsonDetails);
            if (details == null) return false;

            //开始节点
            var startNode = details.sections.LastOrDefault(t => t.stepType == (int)SectionTypes.Start);
            if (startNode == null) return false;

            //条件
            var condition = startNode?.param?.costData?.fexpper?.id ?? "";

            //开始节点的启动条件（前端用户配置的公式）
            if (condition.IsNullOrEmptyOrWhiteSpace())
            {
                //没有配置启动条件，则默认认为满足启动条件（因为有些流程不需要设置启动条件）
                return true;
            }

            //根据公式提取表达式变量（如果有配置启动条件，则根据配置的条件通过表达式引擎计算出是否满足启动要求）
            var bizExpr = userCtx.Container.GetService<IBizExpression>();
            bizExpr.ExpressionText = condition;
            var evaluator = userCtx.Container.GetService<IBizExpressionEvaluator>();
            var exprKeys = evaluator.GetNameExpression(bizExpr);
            if (exprKeys != null && exprKeys.Length > 0)
            {
                Dictionary<string, object> dicVars = this.GetBusinessApprovalVarValues(userCtx, formId, billPkid, string.Join(",", exprKeys));
                if (dicVars == null || dicVars.Count <= 0) return false;

                //调用表达式引擎
                var exprCtx = userCtx.Container.GetService<IBizExpressionContext>();
                foreach (var dicVar in dicVars)
                {
                    exprCtx.AddVariable(dicVar.Key, dicVar.Value);
                }
                condition = this.RemoveConditionTablePrefix(condition, exprKeys);
                try
                {
                    return evaluator.CheckCondition(condition, exprCtx, false);
                }
                catch (Exception ex)
                {
                    return false;
                }
            }

            return false;
        }

        /// <summary>
        /// 获取操作人
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        private string GetOperator(UserContext userCtx)
        {
            //操作人取当前请求来源上下文中的用户
            if (userCtx != null)
            {
                var strOperator = userCtx.DisplayName;
                if (strOperator.IsNullOrEmptyOrWhiteSpace())
                {
                    strOperator = userCtx.UserName;
                }
                return strOperator;
            }
            return "";
        }

        /// <summary>
        /// 创建审批流开始执行节点
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="state"></param>
        /// <param name="node"></param>
        /// <param name="flowInstance"></param>
        /// <returns></returns>
        private DynamicObject CreateApprovalFlowStartSection(UserContext userCtx, BusinessState state, BPMSectionModel node, DynamicObject flowInstance)
        {
            //执行节点表单模型
            var htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, "bpm_execution");

            var strUserNumbers = new List<string>();
            var strUserNames = new List<string>();

            var flowService = userCtx.Container.GetService<IApprovalFlowService>();
            Dictionary<string, string> userList = flowService.GetNodeParticipantsUserList(userCtx, state, flowInstance);
            foreach (var user in userList)
            {
                strUserNumbers.Add(user.Key);
                strUserNames.Add(userList[user.Key]);
            }

            //表头字段
            DynamicObject billHead = new DynamicObject(htmlForm.GetDynamicObjectType(userCtx));
            billHead["fname"] = state.Name;
            billHead["fsectionid"] = state.Value;
            billHead["fdescription"] = state.Description;
            billHead["fsectionstatus"] = "flownodestatus_01";
            billHead["finstanceid"] = flowInstance["id"];

            //表体字段
            var htmlEntry = htmlForm.GetEntryEntity("fentry");
            DynamicObjectCollection entryList = billHead["fentry"] as DynamicObjectCollection;
            DynamicObject billEntry = new DynamicObject(htmlEntry.DynamicObjectType);
            billEntry["fexecutors"] = string.Join(",", strUserNumbers);
            billEntry["fexecutors_txt"] = string.Join(",", strUserNames);
            entryList.Add(billEntry);

            DynamicObject[] dynObjs = new DynamicObject[] { billHead };

            //保存前预处理
            var prepareService = userCtx.Container.GetService<IPrepareSaveDataService>();
            prepareService?.PrepareDataEntity(userCtx, htmlForm, dynObjs, OperateOption.Create());

            return billHead;
        }

        /// <summary>
        /// 根据业务流程ID获取业务审批信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="flowId"></param>
        /// <returns></returns>
        private DynamicObject GetBusinessApprovalByFlowId(UserContext userCtx, string flowId)
        {
            //根据流程ID获取到业务审批对象主键ID
            var pkIdSql = @"select fid from t_bpm_businessflow with(nolock) where fentryid=@fentryid";
            List<SqlParam> paramList = new List<SqlParam>()
            {
                new SqlParam("@fentryid", DbType.String, flowId)
            };
            var businessFlows = this.DBService.ExecuteDynamicObject(userCtx, pkIdSql, paramList);
            if (businessFlows != null && businessFlows.Any())
            {
                //业务审批对象主键ID
                string businessFlowId = businessFlows[0]?["fid"] as string;
                if (!businessFlowId.IsNullOrEmptyOrWhiteSpace())
                {
                    var htmlForm = this.MetaModelService.LoadFormModel(userCtx, "bpm_businessapproval");
                    var _businessapprovalDm = userCtx.GetDefaultDataManager(htmlForm);
                    return _businessapprovalDm.Select(businessFlowId) as DynamicObject;
                }
            }
            return null;
        }

        /// <summary>
        /// 根据业务对象获取业务审批
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        private DynamicObject GetBusinessApprovalByFormId(UserContext userCtx, string formId)
        {
            var htmlForm = this.MetaModelService.LoadFormModel(userCtx, "bpm_businessapproval");
            var _businessapprovalDm = userCtx.GetDefaultDataManager(htmlForm);

            var pkIdSql = @"
            select top 1 fid from t_bpm_businessapproval with(nolock) 
            where fmainorgid=@fmainorgid and fbizformid=@fbizformid";

            List<SqlParam> paramList = new List<SqlParam>()
            {
                new SqlParam("@fmainorgid", DbType.String, userCtx.Company),
                new SqlParam("@fbizformid", DbType.String, formId)
            };

            var pkIds = this.DBService.ExecuteDynamicObject(userCtx, pkIdSql, paramList)
                .Select(o => o[0])
                .ToArray();

            DynamicObject[] dynObjs = _businessapprovalDm.Select(pkIds).OfType<DynamicObject>().ToArray();
            if (dynObjs != null && dynObjs.Any())
            {
                return dynObjs[0];
            }
            return null;
        }

        /// <summary>
        /// 根据业务对象和业务单据主键ID获取流程实例
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formId"></param>
        /// <param name="billPkId"></param>
        /// <returns></returns>
        private DynamicObject GetFlowInstanceByWhere(UserContext userCtx, string formId, string billPkId)
        {
            var htmlForm = this.MetaModelService.LoadFormModel(userCtx, "bpm_flowinstance");
            var _flowinstanceDm = userCtx.GetDefaultDataManager(htmlForm);

            // 此处按照流程实例的创建时间降序排序，取最新的那一个流程实例
            var pkIdSql = @"select top 1 {0} from {1} with(nolock) where fbizformid=@fbizformid and fbizbillpkid=@fbizbillpkid order by fcreatedate desc"
                            .Fmt(htmlForm.HeadEntity.PkFieldName, htmlForm.HeadEntity.TableName);

            List<SqlParam> paramList = new List<SqlParam>()
            {
                new SqlParam("@fbizformid", DbType.String, formId),
                new SqlParam("@fbizbillpkid", DbType.String, billPkId),
            };

            var pkIds = this.DBService.ExecuteDynamicObject(userCtx, pkIdSql, paramList)
                .Select(o => o[0])
                .ToArray();

            DynamicObject[] dynObjs = _flowinstanceDm.Select(pkIds).OfType<DynamicObject>().ToArray();
            if (dynObjs != null && dynObjs.Any())
            {
                return dynObjs[0];
            }
            return null;
        }

        /// <summary>
        /// 获取节点参与人用户列表
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="state"></param>
        /// <param name="flowInstance"></param>
        /// <returns></returns>
        public Dictionary<string, string> GetNodeParticipantsUserList(UserContext userCtx, BusinessState state, DynamicObject flowInstance)
        {
            Dictionary<string, string> userList = new Dictionary<string, string>();

            if (state is null || (state.Data as string).IsNullOrEmptyOrWhiteSpace()) return userList;

            var node = JsonConvert.DeserializeObject<BPMSectionModel>(state.Data as string);

            //节点类型
            switch ((SectionTypes)node.stepType)
            {
                case SectionTypes.Start:
                case SectionTypes.Finished:
                case SectionTypes.InterProcess:
                    //开始节点，结束节点，内部节点
                    userList = this.GetInnerNodeParticipantsUserList(userCtx, state, node, flowInstance);
                    break;
                case SectionTypes.ExterProcess:
                    //外部节点

                    break;
                default:
                    break;
            }

            //加上节点提交时收到待办任务的人员：比如节点处理后流转到下一个节点，则在流程执行表里面会记录对应的处理人信息，这时候如果修改相关联数据，比如部门负责人修改了，
            //那么之前的部门负责人已经收到了待办任务，则他应该能处理该已经在执行中的流程
            var executionUsers = GetExecutionUserList(userCtx, state, flowInstance);
            userList.Merge(executionUsers);

            return userList;
        }


        /// <summary>
        /// 获取节点提交时收到待办任务的人员
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="state"></param>
        /// <param name="flowInstance"></param>
        /// <returns></returns>
        private Dictionary<string, string> GetExecutionUserList(UserContext userCtx, BusinessState state, DynamicObject flowInstance)
        {
            Dictionary<string, string> userList = new Dictionary<string, string>();

            string sectionId = state.Value;
            string flowInstanceId = Convert.ToString(flowInstance["id"]);

            var strSql = @"select b.fexecutors ,b.fexecutors_txt,a.fname  
                                from t_bpm_execution a 
                                inner join t_bpm_executionentry b on a.fid=b.fid 
                                where a.finstanceid ='{0}' and a.fsectionid ='{1}' 
                                ".Fmt(flowInstanceId, sectionId);

            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, strSql))
            {
                while (reader.Read())
                {
                    var fexecutors = reader.GetString("fexecutors");
                    var fexecutors_txt = reader.GetString("fexecutors_txt");
                    if (fexecutors.IsNullOrEmptyOrWhiteSpace() || fexecutors_txt.IsNullOrEmptyOrWhiteSpace())
                    {
                        continue;
                    }

                    var nos = fexecutors.SplitKey();
                    var names = fexecutors_txt.SplitKey();
                    if (nos.Count == names.Count)
                    {
                        for (int i = 0; i < nos.Count; i++)
                        {
                            userList[nos[i]] = names[i];
                        }
                    }
                }
            }

            return userList;
        }


        /// <summary>
        /// 检查用户是否是某个节点的参与人
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="node"></param>
        /// <param name="userNumber"></param>
        /// <returns></returns>
        public bool CheckUserIsNodeParticipant(UserContext userCtx, BPMSectionModel node, string userNumber)
        {
            if (userNumber.IsNullOrEmptyOrWhiteSpace()) return false;

            var nodeData = node?.param?.costData;

            var users = new Dictionary<string, string>();

            #region 参与人（用户）
            string joinUserIdStr = nodeData?.fjoinuser?.id ?? "";
            if (!joinUserIdStr.IsNullOrEmptyOrWhiteSpace())
            {
                users.Clear();
                var userIds = joinUserIdStr.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                this.LoadUserByUserIds(userCtx, userIds, users, true);
                if (users.ContainsKey(userNumber)) return true;
            }
            #endregion

            #region 参与人（部门）
            string joinDeptIdStr = nodeData?.fjoindept?.id ?? "";
            if (!joinDeptIdStr.IsNullOrEmptyOrWhiteSpace())
            {
                var _deptIds = joinDeptIdStr.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                if (_deptIds.Count > 0)
                {
                    var baseFormProvider = userCtx.Container.GetService<IBaseFormProvider>();
                    var staffIds = new List<string>();
                    var staffs = new List<BaseDataSummary>();

                    var _staffs = baseFormProvider.GetDeptAllStaffs(userCtx, _deptIds);
                    if (_staffs != null) staffs.AddRange(_staffs);

                    foreach (var staff in staffs)
                    {
                        if (!staff.Id.IsNullOrEmptyOrWhiteSpace() && !staffIds.Contains(staff.Id))
                        {
                            staffIds.Add(staff.Id);
                        }
                    }
                    if (staffIds.Count > 0)
                    {
                        users.Clear();
                        var _users = baseFormProvider.GetStaffUsers(userCtx, staffIds, true);
                        foreach (var _user in _users)
                        {
                            users[_user.Number] = _user.Name;
                        }
                        if (users.ContainsKey(userNumber)) return true;
                    }
                }
            }
            #endregion

            #region 参与人（角色）
            string joinRoleIdStr = nodeData?.fjoinrole?.id ?? "";
            if (!joinRoleIdStr.IsNullOrEmptyOrWhiteSpace())
            {
                var paramNames = new List<string>();
                var paramList = new List<SqlParam>
                {
                    new SqlParam("@fmainorgid", DbType.String, userCtx.Company)
                };
                var roleIds = joinRoleIdStr.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                for (int i = 0; i < roleIds.Length; i++)
                {
                    if (roleIds[i].IsNullOrEmptyOrWhiteSpace()) continue;
                    var paramName = $"@froleid{i}";
                    paramNames.Add(paramName);
                    paramList.Add(new SqlParam(paramName, DbType.String, roleIds[i]));
                }
                if (paramNames.Count > 0)
                {
                    var sqlText = $@"
                    select distinct u.fid,u.fname,u.fnumber from t_sec_roleuser ru with(nolock) 
                    inner join t_sec_user u with(nolock) on u.fid=ru.fuserid  
                    where u.fmainorgid=@fmainorgid and ru.froleid in({string.Join(",", paramNames)})";
                    using (var reader = this.DBService.ExecuteReader(userCtx, sqlText, paramList))
                    {
                        while (reader.Read())
                        {
                            var number = reader.GetString("fnumber");
                            if (number.IsNullOrEmptyOrWhiteSpace()) continue;
                            if (number.EqualsIgnoreCase(userNumber)) return true;
                        }
                    }
                }
            }
            #endregion

            return false;
        }

        /// <summary>
        /// 获取（开始节点，结束节点，内部节点）参与人用户列表
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="state"></param>
        /// <param name="node"></param>
        /// <param name="flowInstance"></param>
        /// <returns></returns>
        private Dictionary<string, string> GetInnerNodeParticipantsUserList(UserContext userCtx, BusinessState state, BPMSectionModel node, DynamicObject flowInstance)
        {
            Dictionary<string, string> userList = new Dictionary<string, string>();

            #region 以前使用的单个字段存储参与人时的取数逻辑（兼容历史数据，保证用户在不使用多字段参与人的情况下能正常工作）
            //参与人对象
            string participant = node?.param?.costData?.fbizformid?.id ?? "";
            if (!participant.IsNullOrEmptyOrWhiteSpace())
            {
                string[] userNumbers = participant.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                //参与人类型
                string joinType = (node?.param?.costData?.fjointype?.id ?? "").ToLower().Trim();
                switch (joinType)
                {
                    //用户
                    case "fjointype_001":
                        var strUserNames = node?.param?.costData?.fbizformid?.name ?? "";
                        if (!strUserNames.IsNullOrEmptyOrWhiteSpace())
                        {
                            string[] userNames = strUserNames.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                            if (userNumbers.Length == userNames.Length)
                            {
                                for (int i = 0; i < userNumbers.Length; i++)
                                {
                                    userList.Add(userNumbers[i], userNames[i]);
                                }
                            }
                        }
                        break;

                    //部门
                    case "fjointype_002":
                        userList = this.GetUserListByDeptIds(userCtx, participant);
                        break;

                    //角色
                    case "fjointype_003":
                        userList = this.GetUserListByRoleIds(userCtx, participant);
                        break;

                    //参与人变量
                    case "fjointype_004":

                        var formId = flowInstance["fbizformid"] as string;
                        var billPkid = flowInstance["fbizbillpkid"] as string;
                        userList = this.GetUserListByVars(userCtx, formId, billPkid, participant);

                        //解析出特定的用户
                        foreach (var userNumber in userNumbers)
                        {
                            switch (userNumber.ToLower().Trim())
                            {
                                //流程发布人，其实就是流程实例的初始人
                                case "@publisher":
                                    var distributor = flowInstance["finitiator"] as string;
                                    var distributorNumber = flowInstance["finitiatornumber"] as string;
                                    if (!distributor.IsNullOrEmptyOrWhiteSpace()
                                        && !distributorNumber.IsNullOrEmptyOrWhiteSpace()
                                        && !userList.ContainsKey(distributorNumber))
                                    {
                                        userList.Add(distributorNumber, distributor);
                                    }
                                    break;

                                //上一步处理人
                                case "@prevhandler":
                                    //加载指定节点的上一节点处理人（严格来说应该是上一个节点的参与人）
                                    //this.LoadPrevSectionProcessor(userCtx, state.PrevState?.Value, userList);

                                    //加载指定节点的上一节点处理人（严格来说应该是上一个节点的参与人）
                                    var _prevUsersKv = this.GetNodeParticipantsUserList(userCtx, state.PrevState, flowInstance);
                                    foreach (var item in _prevUsersKv)
                                    {
                                        userList[item.Key] = item.Value;
                                    }
                                    break;

                                default:
                                    break;
                            }
                        }
                        break;

                    default:
                        break;
                }
            }
            #endregion

            //现在使用多个字段存储参与人时的取数逻辑
            this.LoadParticipantsInfo(userCtx, state, node, flowInstance, userList);

            return userList;
        }

        /// <summary>
        /// 加载参与人信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="state"></param>
        /// <param name="node"></param>
        /// <param name="flowInstance"></param>
        /// <param name="users"></param>
        private void LoadParticipantsInfo(UserContext userCtx, BusinessState state, BPMSectionModel node, DynamicObject flowInstance, Dictionary<string, string> users)
        {
            var nodeData = node?.param?.costData;
            string bizFormId = flowInstance["fbizformid"] as string;
            if (bizFormId.IsNullOrEmptyOrWhiteSpace()) return;

            var baseFormProvider = userCtx.Container.GetService<IBaseFormProvider>();
            var staffIds = new List<string>();
            var staffs = new List<BaseDataSummary>();

            #region 参与人（用户）
            string joinUserIdStr = nodeData?.fjoinuser?.id ?? "";
            if (!joinUserIdStr.IsNullOrEmptyOrWhiteSpace())
            {
                var userIds = joinUserIdStr.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                this.LoadUserByUserIds(userCtx, userIds, users, true);
            }
            #endregion

            #region 参与人（部门）
            string joinDeptIdStr = nodeData?.fjoindept?.id ?? "";
            if (!joinDeptIdStr.IsNullOrEmptyOrWhiteSpace())
            {
                var _deptIds = joinDeptIdStr.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                if (_deptIds.Count > 0)
                {
                    var _staffs = baseFormProvider.GetDeptAllStaffs(userCtx, _deptIds);
                    if (_staffs != null) staffs.AddRange(_staffs);
                }
            }
            #endregion

            #region 参与人（角色）
            string joinRoleIdStr = nodeData?.fjoinrole?.id ?? "";
            if (!joinRoleIdStr.IsNullOrEmptyOrWhiteSpace())
            {
                var paramNames = new List<string>();
                var paramList = new List<SqlParam>
                {
                    new SqlParam("@fmainorgid", DbType.String, userCtx.Company)
                };
                var roleIds = joinRoleIdStr.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                for (int i = 0; i < roleIds.Length; i++)
                {
                    if (roleIds[i].IsNullOrEmptyOrWhiteSpace()) continue;
                    var paramName = $"@froleid{i}";
                    paramNames.Add(paramName);
                    paramList.Add(new SqlParam(paramName, DbType.String, roleIds[i]));
                }
                if (paramNames.Count > 0)
                {
                    var sqlText = $@"
                    select distinct u.fid,u.fname,u.fnumber from t_sec_roleuser ru with(nolock) 
                    inner join t_sec_user u with(nolock) on u.fid=ru.fuserid  
                    where u.fmainorgid=@fmainorgid and ru.froleid in({string.Join(",", paramNames)}) and u.fforbidstatus='0'";
                    using (var reader = this.DBService.ExecuteReader(userCtx, sqlText, paramList))
                    {
                        while (reader.Read())
                        {
                            var number = reader.GetString("fnumber");
                            if (number.IsNullOrEmptyOrWhiteSpace()) continue;
                            users[number] = reader.GetString("fname");
                        }
                    }
                }
            }
            #endregion

            #region 参与人（变量）
            string joinVarStr = nodeData?.fjoinvar?.id ?? "";
            if (!joinVarStr.IsNullOrEmptyOrWhiteSpace())
            {
                var vars = joinVarStr.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                //特定变量
                var specialVals = new string[] { "@publisher", "@prevhandler", "@prevhandlerdeptmain" };

                foreach (var userNumber in vars)
                {
                    switch (userNumber.ToLower().Trim())
                    {
                        //流程发布人，其实就是流程实例的初始人
                        case "@publisher":
                            var distributor = flowInstance["finitiator"] as string;
                            var distributorNumber = flowInstance["finitiatornumber"] as string;
                            if (!distributor.IsNullOrEmptyOrWhiteSpace()
                                && !distributorNumber.IsNullOrEmptyOrWhiteSpace()
                                && !users.ContainsKey(distributorNumber))
                            {
                                users.Add(distributorNumber, distributor);
                            }
                            break;
                        //上一步处理人
                        case "@prevhandler":

                            //加载指定节点的上一节点处理人（严格来说应该是上一个节点的参与人）
                            //this.LoadPrevSectionProcessor(userCtx, state.PrevState?.Value, users);

                            //加载指定节点的上一节点处理人（严格来说应该是上一个节点的参与人）
                            var _prevUsersKv = this.GetNodeParticipantsUserList(userCtx, state.PrevState, flowInstance);
                            foreach (var item in _prevUsersKv)
                            {
                                users[item.Key] = item.Value;
                            }
                            break;

                        //上一步处理人所属部门负责人
                        case "@prevhandlerdeptmain":
                            //加载指定节点的上一节点处理人所属部门负责人
                            this.LoadPrevSectionProcessorDeptMain(userCtx, state.PrevState, flowInstance, users);
                            break;
                        default:
                            break;
                    }
                }

                //其他变量
                var billPkid = flowInstance["fbizbillpkid"] as string;
                var otherVars = vars.Where(o => !specialVals.Contains(o.ToLower().Trim())).ToList(); //排除特殊变量
                if (!billPkid.IsNullOrEmptyOrWhiteSpace() && otherVars.Count > 0)
                {
                    var bizForm = this.MetaModelService.LoadFormModel(userCtx, bizFormId);
                    var dm = userCtx.Container.GetService<IDataManager>();
                    dm.InitDbContext(userCtx, bizForm.GetDynamicObjectType(userCtx));
                    var dataEntity = dm.Select(billPkid) as DynamicObject;
                    if (dataEntity != null)
                    {
                        ExtendedDataEntitySet dataEntitySet = new ExtendedDataEntitySet();
                        dataEntitySet.Parse(userCtx, new DynamicObject[] { dataEntity }, bizForm);

                        var staffFormId = baseFormProvider.GetStaffFormObject(userCtx);
                        var deptFormId = baseFormProvider.GetDeptFormObject(userCtx);

                        for (int i = 0; i < otherVars.Count; i++)
                        {
                            if (otherVars[i].IsNullOrEmptyOrWhiteSpace()) continue;
                            var fieldKeys = otherVars[i].Split(new char[] { '$' }, StringSplitOptions.RemoveEmptyEntries);
                            var fieldKey = fieldKeys[0];
                            var field = bizForm.GetField(fieldKey) as HtmlBaseDataField;
                            if (field == null) continue;

                            if (field.RefFormId.EqualsIgnoreCase("sec_user"))
                            {
                                var _userIds = new List<string>();
                                this.FindFieldValue(bizForm, dataEntitySet, fieldKey, _userIds);
                                if (_userIds != null)
                                {
                                    if (fieldKeys.Length == 2)
                                    {
                                        //获取指定用户所在的部门信息
                                        var _depts = baseFormProvider.GetUserDepts(userCtx, _userIds);
                                        var _deptIds = _depts?.Select(o => o.Id)?.ToList();
                                        if (_deptIds != null)
                                        {
                                            switch (fieldKeys[1].ToLower())
                                            {
                                                //用户所属部门负责人
                                                case "deptmain":
                                                    var _leaders = baseFormProvider.GetDeptLeaders(userCtx, _deptIds);
                                                    if (_leaders != null) staffs.AddRange(_leaders);
                                                    break;
                                                //用户所属部门所有员工
                                                case "deptall":
                                                    var _staffs = baseFormProvider.GetDeptAllStaffs(userCtx, _deptIds);
                                                    if (_staffs != null) staffs.AddRange(_staffs);
                                                    break;
                                                default:
                                                    break;
                                            }
                                        }
                                    }
                                    else
                                    {
                                        //根据用户Id加载用户信息
                                        this.LoadUserByUserIds(userCtx, _userIds, users);
                                    }
                                }
                            }
                            else if (field.RefFormId.EqualsIgnoreCase(staffFormId))
                            {
                                if (fieldKeys.Length == 2)
                                {
                                    var _staffIds = new List<string>();
                                    this.FindFieldValue(bizForm, dataEntitySet, fieldKey, _staffIds);
                                    if (_staffIds != null)
                                    {
                                        //获取指定员工所在的部门信息
                                        var _depts = baseFormProvider.GetStaffDepts(userCtx, _staffIds);
                                        var _deptIds = _depts?.Select(o => o.Id)?.ToList();
                                        if (_deptIds != null)
                                        {
                                            switch (fieldKeys[1].ToLower())
                                            {
                                                //员工所属部门负责人
                                                case "deptmain":
                                                    var _leaders = baseFormProvider.GetDeptLeaders(userCtx, _deptIds);
                                                    if (_leaders != null) staffs.AddRange(_leaders);
                                                    break;
                                                //员工所属部门所有员工
                                                case "deptall":
                                                    var _staffs = baseFormProvider.GetDeptAllStaffs(userCtx, _deptIds);
                                                    if (_staffs != null) staffs.AddRange(_staffs);
                                                    break;
                                                default:
                                                    break;
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    //查找员工Id字段值
                                    this.FindFieldValue(bizForm, dataEntitySet, fieldKey, staffIds);
                                }
                            }
                            else if (field.RefFormId.EqualsIgnoreCase(deptFormId))
                            {
                                if (fieldKeys.Length == 2)
                                {
                                    var _deptIds = new List<string>();
                                    this.FindFieldValue(bizForm, dataEntitySet, fieldKey, _deptIds);
                                    switch (fieldKeys[1].ToLower())
                                    {
                                        //部门负责人
                                        case "main":
                                            var _leaders = baseFormProvider.GetDeptLeaders(userCtx, _deptIds);
                                            if (_leaders != null) staffs.AddRange(_leaders);
                                            break;
                                        //部门内所有员工
                                        case "all":
                                            var _staffs = baseFormProvider.GetDeptAllStaffs(userCtx, _deptIds);
                                            if (_staffs != null) staffs.AddRange(_staffs);
                                            break;
                                        default:
                                            break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            #endregion

            foreach (var staff in staffs)
            {
                if (!staff.Id.IsNullOrEmptyOrWhiteSpace() && !staffIds.Contains(staff.Id))
                {
                    staffIds.Add(staff.Id);
                }
            }

            if (staffIds.Count > 0)
            {
                var _users = baseFormProvider.GetStaffUsers(userCtx, staffIds, true);
                foreach (var _user in _users)
                {
                    users[_user.Number] = _user.Name;
                }
            }

            //加载流程代理人
            this.LoadFlowProxy(userCtx, flowInstance, users);
        }

        /// <summary>
        /// 加载指定节点的上一节点处理人所属部门负责人
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="state"></param>
        /// <param name="flowInstance"></param>
        /// <param name="users"></param>
        private void LoadPrevSectionProcessorDeptMain(UserContext userCtx, BusinessState state, DynamicObject flowInstance, Dictionary<string, string> users)
        {
            //加载指定节点的处理人（严格来说应该是节点的参与人）
            var _prevUsersKv = this.GetNodeParticipantsUserList(userCtx, state, flowInstance);
            if (!_prevUsersKv.Any()) return;

            var baseFormProvider = userCtx.Container.GetService<IBaseFormProvider>();

            //用户编码
            var userNumbers = _prevUsersKv.Keys.ToList();

            //用户Id
            var userIds = this.GetUserIdByUserNumber(userCtx, userNumbers);

            //获取指定用户所在的部门信息
            var _depts = baseFormProvider.GetUserDepts(userCtx, userIds);
            var _deptIds = _depts?.Select(o => o.Id)?.ToList();
            if (_deptIds == null || !_deptIds.Any()) return;

            //获取指定部门的负责人信息（员工）
            var _leaders = baseFormProvider.GetDeptLeaders(userCtx, _deptIds);
            var staffIds = _leaders?.Select(o => o.Id)?.Distinct()?.ToList();
            if (staffIds == null || !staffIds.Any()) return;

            //获取指定员工所关联的用户信息
            var _users = baseFormProvider.GetStaffUsers(userCtx, staffIds, true);
            if (_users == null || !_users.Any()) return;

            foreach (var _user in _users)
            {
                users[_user.Number] = _user.Name;
            }
        }

        /// <summary>
        /// 加载流程代理人
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="flowInstance"></param>
        /// <param name="users"></param>
        private void LoadFlowProxy(UserContext userCtx, DynamicObject flowInstance, Dictionary<string, string> users)
        {
            if (users == null && users.Count <= 0) return;

            //构建流程代理表结构
            //var flowProxyForm = this.MetaModelService.LoadFormModel(userCtx, "bpm_flowproxy");
            //if (_flowproxyDm == null)
            //{
            //    _flowproxyDm = userCtx.Container.GetService<IDataManager>();
            //    _flowproxyDm.InitDbContext(userCtx, flowProxyForm.GetDynamicObjectType(userCtx));
            //}
            //var _flowproxyDm = userCtx.GetDefaultDataManager(flowProxyForm);

            var sqlText = $@"select distinct u.fnumber,u.fname from t_bpm_flowproxy f with(nolock)
            inner join t_bpm_flowproxyentry pe with(nolock) on pe.fid=f.fid 
            inner join t_sec_user u with(nolock) on u.fid=f.fproxyid 
            inner join t_sec_user u2 with(nolock) on u2.fid=f.fbyproxyid and u2.fnumber in('{string.Join("','", users.Keys)}')
            where f.fmainorgid=@fmainorgid and pe.fverid=@fverid and pe.fisenabled='1' and u.fforbidstatus='0' 
            and @fdate>=fbegindate and @fdate<dateadd(d,1,fenddate)";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("fmainorgid", DbType.String, userCtx.Company),
                new SqlParam("fverid", DbType.String, flowInstance["fflowid"]),
                new SqlParam("fdate", DbType.DateTime, DateTime.Now)
            };

            using (var reader = this.DBService.ExecuteReader(userCtx, sqlText, sqlParam))
            {
                while (reader.Read())
                {
                    var userNumber = reader.GetString("fnumber");
                    if (userNumber.IsNullOrEmptyOrWhiteSpace()) continue;
                    users[userNumber] = reader.GetString("fname");
                }
            }
        }

        /// <summary>
        /// 更新流程代理明细
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="flowId"></param>
        /// <param name="verId"></param>
        /// <param name="flowName"></param>
        /// <param name="verName"></param>
        public void UpdateFlowProxyEntry(UserContext userCtx, string flowId, string verId, string flowName, string verName)
        {
            if (flowId.IsNullOrEmptyOrWhiteSpace() || verId.IsNullOrEmptyOrWhiteSpace()) return;

            var sqlText = $@"select distinct t.fid from t_bpm_flowproxy t with(nolock) 
            inner join t_bpm_flowproxyentry te with(nolock) on te.fid=t.fid and te.fflowid=@fflowid 
            where t.fmainorgid=@fmainorgid";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("fmainorgid", DbType.String, userCtx.Company),
                new SqlParam("fflowid", DbType.String, flowId)
            };

            var pkids = this.DBService.ExecuteDynamicObject(userCtx, sqlText, sqlParam)?.Select(o => o["fid"]);
            if (pkids == null || !pkids.Any()) return;

            var htmlForm = this.MetaModelService.LoadFormModel(userCtx, "bpm_flowproxy");
            var _flowproxyDm = userCtx.GetDefaultDataManager(htmlForm);
            var flowProxys = _flowproxyDm.Select(pkids)?.OfType<DynamicObject>();
            if (flowProxys == null || !flowProxys.Any()) return;

            var saveObjs = new List<DynamicObject>();
            foreach (var item in flowProxys)
            {
                var exists = false;
                var entrys = item["fentry"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    if (Convert.ToString(entry["fflowid"]).EqualsIgnoreCase(flowId))
                    {
                        exists = true;
                        entry["fverid"] = verId;
                        entry["fvername"] = verName;
                        entry["fflowname"] = flowName;
                    }
                }
                if (exists)
                {
                    saveObjs.Add(item);
                }
            }
            if (saveObjs.Any())
            {
                _flowproxyDm.Save(saveObjs);
            }
        }

        /// <summary>
        /// 根据用户编码获取用户Id
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="userNumbers"></param>
        /// <returns></returns>
        private List<string> GetUserIdByUserNumber(UserContext userCtx, List<string> userNumbers)
        {
            var userIds = new List<string>();
            if (userNumbers == null || userNumbers.Count <= 0) return userIds;

            var paramNames = new List<string>();
            var paramList = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, userCtx.Company)
            };
            for (int i = 0; i < userNumbers.Count; i++)
            {
                var userNumber = userNumbers[i];
                if (userNumber.IsNullOrEmptyOrWhiteSpace()) continue;
                var paramName = $"@no{i}";
                paramNames.Add(paramName);
                paramList.Add(new SqlParam(paramName, DbType.String, userNumber));
            }
            if (!paramNames.Any()) return userIds;

            var sqlText = @"select fid from t_sec_user with(nolock) where fmainorgid=@fmainorgid";
            if (userNumbers.Count > 1)
            {
                sqlText += $" and fnumber in({string.Join(",", paramNames)})";
            }
            else
            {
                sqlText += $" and fnumber={paramNames[0]}";
            }

            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, sqlText, paramList))
            {
                while (reader.Read())
                {
                    var userId = reader.GetString("fid");
                    if (userId.IsNullOrEmptyOrWhiteSpace()) continue;
                    userIds.Add(userId);
                }
            }

            return userIds;
        }

        /// <summary>
        /// 根据用户Id加载用户信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="userIds"></param>
        /// <param name="users"></param>
        /// <param name="inNumber"></param>
        private void LoadUserByUserIds(UserContext userCtx, List<string> userIds, Dictionary<string, string> users, bool inNumber = false)
        {
            if (userIds == null || userIds.Count <= 0) return;

            var paramNames = new List<string>();
            var paramList = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, userCtx.Company)
            };
            for (int i = 0; i < userIds.Count; i++)
            {
                if (userIds[i].IsNullOrEmptyOrWhiteSpace()) continue;
                var paramName = $"@fid{i}";
                paramNames.Add(paramName);
                paramList.Add(new SqlParam(paramName, DbType.String, userIds[i]));
            }
            if (paramNames.Count <= 0) return;

            var sqlText = @"
            select fid,fnumber,fname from t_sec_user with(nolock) 
            where fmainorgid=@fmainorgid and fforbidstatus='0'";
            if (userIds.Count > 1)
            {
                //为了兼容历史参与人字段中存用户编码的情况
                if (inNumber)
                {
                    sqlText += $" and (fid in({string.Join(",", paramNames)}) or fnumber in({string.Join(",", paramNames)}))";
                }
                else
                {
                    sqlText += $" and fid in({string.Join(",", paramNames)})";
                }
            }
            else
            {
                //为了兼容历史参与人字段中存用户编码的情况
                if (inNumber)
                {
                    sqlText += $" and (fid={paramNames[0]} or fnumber={paramNames[0]})";
                }
                else
                {
                    sqlText += $" and fid={paramNames[0]}";
                }
            }

            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, sqlText, paramList))
            {
                while (reader.Read())
                {
                    var number = reader.GetString("fnumber");
                    if (number.IsNullOrEmptyOrWhiteSpace()) continue;
                    if (users.ContainsKey(number)) continue;
                    users[number] = reader.GetString("fname");
                }
            }
        }

        /// <summary>
        /// 根据字段标识在指定的数据包中查找字段值（包括表体中的字段值）
        /// </summary>
        /// <param name="bizForm"></param>
        /// <param name="dataEntitySet"></param>
        /// <param name="fieldKey"></param>
        /// <param name="values"></param>
        private void FindFieldValue(HtmlForm bizForm, ExtendedDataEntitySet dataEntitySet, string fieldKey, List<string> values)
        {
            var field = bizForm.GetField(fieldKey);
            if (field == null) return;

            var extendeds = dataEntitySet.FindByEntityKey(field.EntityKey);
            if (extendeds == null) return;

            foreach (var extended in extendeds)
            {
                var value = field?.DynamicProperty?.GetValue<string>(extended.DataEntity);
                if (!value.IsNullOrEmptyOrWhiteSpace() && !values.Contains(value))
                {
                    values.Add(value);
                }
            }
        }

        /// <summary>
        /// 移除条件表前缀连接符
        /// </summary>
        /// <param name="condition"></param>
        /// <param name="exprKeys"></param>
        /// <returns></returns>
        public string RemoveConditionTablePrefix(string condition, string[] exprKeys)
        {
            foreach (var exprKey in exprKeys)
            {
                var field = exprKey.Split(new char[] { '.' }, StringSplitOptions.RemoveEmptyEntries);
                if (field.Length >= 2)
                {
                    var customField = string.Join("", field);
                    condition = condition.Replace(exprKey, customField);
                }
            }
            return condition;
        }

        /// <summary>
        /// 获取业务审批流条件变量值，目前只支持获取单据头的字段值
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="formId">业务表单ID</param>
        /// <param name="billPkid">业务单据主键ID</param>
        /// <param name="varNames">变量名称串，多个变量名称之间用逗号“,”隔开</param>
        /// <returns>变量键值对字典</returns>
        public Dictionary<string, object> GetBusinessApprovalVarValues(UserContext userCtx, string formId, string billPkid, string varNames)
        {
            return this.GetMutliBillFieldValueByFieldIds(userCtx, formId, billPkid, varNames, true);
        }

        /// <summary>
        /// 获取业务审批流条件变量值（多表）
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formId"></param>
        /// <param name="billPkid"></param>
        /// <param name="fieldIds"></param>
        /// <param name="bdFetchName"></param>
        /// <returns></returns>
        /// <exception cref="BusinessException"></exception>
        public Dictionary<string, object> GetMutliBillFieldValueByFieldIds(UserContext userCtx, string formId, string billPkid, string fieldIds, bool bdFetchName = false)
        {
            Dictionary<string, object> fieldValues = new Dictionary<string, object>();
            //1.拆解出字段ID
            var fieldIdArray = fieldIds.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (fieldIdArray == null || fieldIdArray.Length <= 0)
            {
                throw new BusinessException($"业务字段ID为空，请检查！");
            }

            var metaModelService = userCtx.Container.GetService<IMetaModelService>();

            var htmlForm = metaModelService?.LoadFormModel(userCtx, formId); //表单模型
            if (htmlForm == null)
            {
                throw new BusinessException($"业务表单【{htmlForm.HeadEntity.Caption}】不存在，请检查！");
            }

            //2.获取主要表单基本数据
            var formData = GetFormDataById(userCtx, formId, billPkid);
            var sourceTypeFld = htmlForm.GetField("fsourcetype");
            var sourceNumberFld = htmlForm.GetField("fsourcenumber");
            var orgFld = htmlForm.GetField("fmainorgid");
            string sourceType = "", sourceNmber = "", orgId = "";
            if (sourceTypeFld != null && sourceTypeFld.IsBillHeadField)
            {
                sourceType = Convert.ToString(formData["fsourcetype"]);
            }
            if (sourceNumberFld != null && sourceNumberFld.IsBillHeadField)
            {
                sourceNmber = Convert.ToString(formData["fsourcenumber"]);
            }
            if (orgFld != null && orgFld.IsBillHeadField)
            {
                orgId = Convert.ToString(formData["fmainorgid"]);
            }

            //3.检查每个字段所属表单
            foreach (var fieldId in fieldIdArray)
            {
                //多表单"FormId.FieldId[.SubFieldId]"，其中SubFieldId不一定存在
                //兼容原单一表单设计"FieldId"，单一表单等价于"CurFormId.FieldId"
                var field = fieldId.Split(new char[] { '.' }, StringSplitOptions.RemoveEmptyEntries);

                //自定义的Key：一是为了后面解析表达式识别不了‘.’符号、二是为了保持唯一
                var customFieldkey = string.Join("", field);

                string curFormId = string.Empty, curFieldId = string.Empty, subFieldId = string.Empty;
                HtmlField curField = null, subField = null;
                if (field.Length == 3)
                {
                    curFormId = field[0];
                    curFieldId = field[1];
                    subFieldId = field[2];
                }
                else if (field.Length == 2)
                {
                    curFormId = field[0];
                    curFieldId = field[1];
                }
                else
                {
                    curFormId = formId;
                    curFieldId = fieldId;
                }

                var curForm = curFormId.EqualsIgnoreCase(formId) ? htmlForm : metaModelService.LoadFormModel(userCtx, curFormId);
                // 检查当前字段是否存在
                if (!CheckFieldExist(curForm, curFieldId, out curField))
                {
                    continue;
                }

                // 如果有下级字段，检查是否存在
                if (!subFieldId.IsNullOrEmptyOrWhiteSpace())
                {
                    // 只支持基础资料
                    if (curField.ElementType != HtmlElementType.HtmlField_BaseDataField)
                    {
                        throw new BusinessException($"表达式 {fieldId} 的字段 {curFieldId} 不是基础资料类型，请检查！");
                    }

                    var subForm = ((HtmlBaseDataField)curField).RefHtmlForm(userCtx);
                    // 检查下游字段是否存在
                    if (!CheckFieldExist(subForm, subFieldId, out subField))
                    {
                        continue;
                    }
                }

                var curFormData = curFormId.EqualsIgnoreCase(formId)
                    ? formData
                    : GetUpstreamFormData(userCtx, curFormId, sourceNmber, orgId);

                if (subFieldId.IsNullOrEmptyOrWhiteSpace())
                {
                    fieldValues.Add(customFieldkey, GetFieldValue(userCtx, curFormData, curForm, curField, bdFetchName));
                }
                else
                {
                    var curFieldValue = curField.DynamicProperty.GetValue(curFormData)?.ToString();
                    if (curFieldValue.IsNullOrEmptyOrWhiteSpace())
                    {
                        fieldValues.Add(customFieldkey, "");
                    }
                    else
                    {
                        var subForm = ((HtmlBaseDataField)curField).RefHtmlForm(userCtx);
                        var subFormData = userCtx.LoadBizDataById(subForm.Id, curFieldValue, true);

                        fieldValues.Add(customFieldkey, GetFieldValue(userCtx, subFormData, subForm, subField, bdFetchName));
                    }
                }
            }
            return fieldValues;
        }

        ///// <summary>
        ///// 检测字段是否存在
        ///// </summary>
        ///// <param name="userCtx"></param>
        ///// <param name="formId"></param>
        ///// <param name="fieldId"></param>
        ///// <returns></returns>
        ///// <exception cref="BusinessException"></exception>
        //private HtmlField GetHtmlField(UserContext userCtx, string formId, string fieldId)
        //{
        //    var htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, formId); //表单模型
        //    if (htmlForm == null)
        //    {
        //        throw new BusinessException($"上游业务表单【{htmlForm.HeadEntity.Caption}】不存在，请检查！");
        //    }
        //    var fieldList = htmlForm.GetFieldList().Where(t => t.IsBillHeadField).ToList();//当前业务表单的所有表头字段 
        //    return fieldList.FirstOrDefault(t => t.Id.EqualsIgnoreCase(fieldId));
        //}

        /// <summary>
        /// 检测字段是否存在
        /// </summary>
        /// <param name="formMeta"></param>
        /// <param name="fieldId"></param>
        /// <returns></returns>
        /// <exception cref="BusinessException"></exception>
        private bool CheckFieldExist(HtmlForm formMeta, string fieldId, out HtmlField field)
        {
            field = null;

            if (formMeta == null)
            {
                return false;
            }

            field = formMeta.GetField(fieldId);

            //当前业务表单的所有表头字段 
            return field != null && field.IsBillHeadField;
        }

        ///// <summary>
        ///// 检测字段是否存在
        ///// </summary>
        ///// <param name="fieldList"></param>
        ///// <param name="fieldId"></param>
        ///// <returns></returns>
        //private bool CheckFieldExist(List<HtmlField> fieldList, string fieldId)
        //{
        //    return fieldList.FirstOrDefault(t => t.Id.EqualsIgnoreCase(fieldId)) != null;
        //}

        /// <summary>
        /// 获取字段值
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataObj"></param>
        /// <param name="formId"></param>
        /// <param name="fieldId"></param>
        /// <param name="bdFetchName"></param>
        /// <returns></returns>
        /// <exception cref="BusinessException"></exception>
        private object GetFieldValue(UserContext userCtx, DynamicObject dataObj, string formId, string fieldId, bool bdFetchName = false)
        {
            //表单模型
            var htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, formId);
            if (htmlForm == null)
            {
                throw new BusinessException($"业务表单【{htmlForm.HeadEntity.Caption}】不存在，请检查！");
            }
            //当前业务表单的所有表头字段
            List<HtmlField> fieldList = htmlForm.GetFieldList().Where(t => t.IsBillHeadField).ToList();

            //提取字段值
            HtmlField field = fieldList.FirstOrDefault(t => t.Id.EqualsIgnoreCase(fieldId));
            if (field == null)
            {
                return null;
            }
            return GetFieldValue(userCtx, dataObj, htmlForm, field, bdFetchName);
        }

        /// <summary>
        /// 获取字段值
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataObj"></param>
        /// <param name="htmlForm"></param>
        /// <param name="field"></param>
        /// <param name="bdFetchName"></param>
        /// <returns></returns>
        private object GetFieldValue(UserContext userCtx, DynamicObject dataObj, HtmlForm htmlForm, HtmlField field, bool bdFetchName = false)
        {
            object fieldValue = null;
            //基础资料/辅助资料 字段是否取名称
            if (bdFetchName && field is HtmlBaseDataField)
            {
                var baseField = field as HtmlBaseDataField;
                var refObjData = baseField?.RefDynamicProperty?.GetValue(dataObj) as DynamicObject;
                if (refObjData != null)
                {
                    var refNameField = baseField?.GetRefNameField(userCtx);
                    fieldValue = refNameField?.DynamicProperty?.GetValue<string>(refObjData)?.Trim();
                }
            }
            else
            {
                fieldValue = field?.DynamicProperty?.GetValue(dataObj) ?? "";
                //简单下拉框字段
                if (field is HtmlSimpleSelectField)
                {
                    var simpleSelectField = field as HtmlSimpleSelectField;
                    fieldValue = simpleSelectField.GetDisplayValue(userCtx, htmlForm, dataObj, OperateOption.Create());
                }
                //源单编号字段(这里没源单编号获取到的值是带空格的，要去除)
                if (field is HtmlSourceBillNoField && fieldValue.ToString() == " ")
                {
                    fieldValue = string.Empty;
                }
            }
            return fieldValue;
        }

        /// <summary>
        /// 通过ID获取表单数据
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formId"></param>
        /// <param name="billPkid"></param>
        /// <returns></returns>
        /// <exception cref="BusinessException"></exception>
        private DynamicObject GetFormDataById(UserContext userCtx, string formId, string billPkid)
        {
            var dataObj = userCtx.LoadBizDataById(formId, billPkid, true);
            if (dataObj == null)
            {
                throw new BusinessException($"业务单据{billPkid}不存在，请检查！");
            }
            return dataObj;
        }

        /// <summary>
        /// 获取上游表单数据包
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formId"></param>
        /// <param name="fbillno"></param>
        /// <param name="orgId"></param>
        /// <returns></returns>
        private DynamicObject GetUpstreamFormData(UserContext userCtx, string formId, string fbillno, string orgId)
        {
            var htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, formId); //表单模型
            if (htmlForm == null)
            {
                throw new BusinessException($"上游业务表单【{htmlForm.HeadEntity.Caption}】不存在，请检查！");
            }
            var fieldList = htmlForm.GetFieldList().Where(t => t.IsBillHeadField).ToList();//当前业务表单的所有表头字段 

            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));
            var sql = $"select fid from {htmlForm.HeadEntity.TableName} with(nolock) where fmainorgid=@fmainorgid and fbillno=@fbillno";
            List<SqlParam> paramList = new List<SqlParam>()
            {
                new SqlParam("@fmainorgid", DbType.String, orgId),
                new SqlParam("@fbillno", DbType.String, fbillno)
            };
            var pkId = this.DBService.ExecuteDynamicObject(userCtx, sql, paramList).Select(o => o[0]).FirstOrDefault();
            if (pkId == null)
            {
                throw new BusinessException($"上游业务表单【{htmlForm.HeadEntity.Caption}】数据不存在，请检查！");
            }
            var data = userCtx.LoadBizDataById(formId, pkId.ToString(), true);
            return data;
        }

        ///// <summary>
        ///// 获取上游表单字段值
        ///// </summary>
        ///// <param name="userCtx"></param>
        ///// <param name="formId"></param>
        ///// <param name="field"></param>
        ///// <param name="fbillno"></param>
        ///// <param name="orgId"></param>
        ///// <param name="bdFetchName"></param>
        ///// <returns></returns>
        //private object GetUpstreamFormFieldValue(UserContext userCtx, string formId, string field, string fbillno, string orgId, bool bdFetchName = false)
        //{
        //    var data = GetUpstreamFormData(userCtx, formId, fbillno, orgId);
        //    return GetFieldValue(userCtx, data, formId, field, bdFetchName);
        //}

        /// <summary>
        /// 获取指定业务单据中指定字段值，目前只支持获取单据头的字段值
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formId"></param>
        /// <param name="billPkid"></param>
        /// <param name="fieldIds"></param>
        /// <param name="bdFetchName"></param>
        /// <returns></returns>
        public Dictionary<string, object> GetBillFieldValueByFieldIds(UserContext userCtx, string formId, string billPkid, string fieldIds, bool bdFetchName = false)
        {
            Dictionary<string, object> fieldValues = new Dictionary<string, object>();

            //表单模型
            var htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, formId);
            if (htmlForm == null) throw new BusinessException($"业务表单【{htmlForm.HeadEntity.Caption}】不存在，请检查！");

            //字段ID数组
            var fieldIdArray = fieldIds.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (fieldIdArray == null || fieldIdArray.Length <= 0) throw new BusinessException($"业务字段ID为空，请检查！");

            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));
            var dataObj = dm.Select(billPkid) as DynamicObject;
            if (dataObj == null) throw new BusinessException($"业务单据{billPkid}不存在，请检查！");

            //基础资料/辅助资料 字段是否取名称
            if (bdFetchName)
            {
                //加载引用数据
                var refObjMrg = userCtx.Container.GetService<LoadReferenceObjectManager>();
                refObjMrg?.Load(userCtx, htmlForm.GetDynamicObjectType(userCtx), dataObj, false);
            }

            //当前业务表单的所有表头字段
            List<HtmlField> fieldList = htmlForm.GetFieldList().Where(t => t.IsBillHeadField).ToList();

            //提取字段值
            foreach (var fieldId in fieldIdArray)
            {
                HtmlField field = fieldList.FirstOrDefault(t => t.Id.EqualsIgnoreCase(fieldId));
                if (field == null) continue;

                object fieldValue = null;

                //基础资料/辅助资料 字段是否取名称
                if (bdFetchName && field is HtmlBaseDataField)
                {
                    var baseField = field as HtmlBaseDataField;
                    var refObjData = baseField?.RefDynamicProperty?.GetValue(dataObj) as DynamicObject;
                    if (refObjData != null)
                    {
                        var refNameField = baseField?.GetRefNameField(userCtx);
                        fieldValue = refNameField?.DynamicProperty?.GetValue<string>(refObjData)?.Trim();
                    }
                }
                else
                {
                    fieldValue = field?.DynamicProperty?.GetValue(dataObj) ?? "";

                    //简单下拉框字段
                    if (field is HtmlSimpleSelectField)
                    {
                        var simpleSelectField = field as HtmlSimpleSelectField;
                        fieldValue = simpleSelectField.GetDisplayValue(userCtx, htmlForm, dataObj, OperateOption.Create());
                    }
                }

                fieldValues.Add(fieldId, fieldValue);
            }

            return fieldValues;
        }

        /// <summary>
        /// 获取指定部门下的所有用户
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="deptIds">部门ID串，多个用逗号“,”隔开</param>
        /// <returns>用户列表：编码为键，名称为值</returns>
        private Dictionary<string, string> GetUserListByDeptIds(UserContext userCtx, string deptIds)
        {
            Dictionary<string, string> userList = new Dictionary<string, string>();

            var deptIdArray = deptIds.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (deptIdArray != null && deptIdArray.Length > 0)
            {
                var strSql = @"select distinct t3.fnumber,t3.fname from t_bd_department t1 with(nolock) 
                inner join t_bd_staff t2 with(nolock) on t2.fdeptid = t1.fid 
                inner join t_sec_user t3 with(nolock) on t3.fphone = t2.fphone or t3.fnumber = t2.fphone 
                where t1.fmainorgid=@fmainorgid and t2.fmainorgid=@fmainorgid and t3.fmainorgid=@fmainorgid and t3.fforbidstatus='0'";

                StringBuilder sbWhere = new StringBuilder();
                List<SqlParam> paramList = new List<SqlParam>

                {
                    new SqlParam("@fmainorgid", DbType.String, userCtx.Company)
                };
                for (int i = 0; i < deptIdArray.Length; i++)
                {
                    var paramName = "@deptid" + i;
                    sbWhere.Append(paramName).Append(",");
                    paramList.Add(new SqlParam(paramName, DbType.String, deptIdArray[i]));
                }
                strSql += $" and t1.fid in({sbWhere.ToString().TrimEnd(',')})";

                var dbService = userCtx.Container.GetService<IDBService>();
                using (var reader = dbService.ExecuteReader(userCtx, strSql, paramList))
                {
                    while (reader.Read())
                    {
                        var number = reader.GetString("fnumber");
                        if (!number.IsNullOrEmptyOrWhiteSpace())
                        {
                            var name = reader.GetString("fname");
                            userList.Add(number, name);
                        }
                    }
                }
            }

            return userList;
        }

        /// <summary>
        /// 获取指定角色下的所有用户
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="roleIds">角色ID串，多个用逗号“,”隔开</param>
        /// <returns>用户列表：编码为键，名称为值</returns>
        private Dictionary<string, string> GetUserListByRoleIds(UserContext userCtx, string roleIds)
        {
            Dictionary<string, string> userList = new Dictionary<string, string>();

            var roleIdArray = roleIds.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (roleIdArray != null && roleIdArray.Length > 0)
            {
                var strSql = @"select distinct t2.fnumber,t2.fname from t_sec_roleuser t1 with(nolock) 
                inner join t_sec_user t2 with(nolock) on t2.fid = t1.fuserid 
                where t2.fmainorgid=@fmainorgid and t2.fforbidstatus='0'";

                StringBuilder sbWhere = new StringBuilder();
                List<SqlParam> paramList = new List<SqlParam>
                {
                    new SqlParam("@fmainorgid", DbType.String, userCtx.Company)
                };
                for (int i = 0; i < roleIdArray.Length; i++)
                {
                    var paramName = "@roleid" + i;
                    sbWhere.Append(paramName).Append(",");
                    paramList.Add(new SqlParam(paramName, DbType.String, roleIdArray[i]));
                }
                strSql += $" and t1.froleid in({sbWhere.ToString().TrimEnd(',')})";

                var dbService = userCtx.Container.GetService<IDBService>();
                using (var reader = dbService.ExecuteReader(userCtx, strSql, paramList))
                {
                    while (reader.Read())
                    {
                        var number = reader.GetString("fnumber");
                        if (!number.IsNullOrEmptyOrWhiteSpace())
                        {
                            var name = reader.GetString("fname");
                            userList.Add(number, name);
                        }
                    }
                }
            }

            return userList;
        }

        /// <summary>
        /// 获取指定单据下的指定字段值（一般是用户类型字段）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="formId">表单ID</param>
        /// <param name="billPkid">单据主键ID</param>
        /// <param name="fieldIds">字段ID串，多个用逗号“,”隔开</param>
        /// <returns>用户列表：编码为键，名称为值</returns>
        private Dictionary<string, string> GetUserListByVars(UserContext userCtx, string formId, string billPkid, string fieldIds)
        {
            Dictionary<string, string> userList = new Dictionary<string, string>();

            Dictionary<string, object> fieldValues = this.GetBillFieldValueByFieldIds(userCtx, formId, billPkid, fieldIds);
            if (fieldValues.Count > 0)
            {
                var strSql = @"
                select distinct fnumber,fname from t_sec_user with(nolock) 
                where fmainorgid=@fmainorgid and fforbidstatus='0'";

                StringBuilder sbWhere = new StringBuilder();
                List<SqlParam> paramList = new List<SqlParam>
                {
                    new SqlParam("@fmainorgid", DbType.String, userCtx.Company)
                };

                int index = 0;
                foreach (var key in fieldValues.Keys)
                {
                    var paramName = "@fid" + index;
                    sbWhere.Append(paramName).Append(",");
                    paramList.Add(new SqlParam(paramName, DbType.String, fieldValues[key] as string));

                    index++;
                }
                strSql += $" and fid in({sbWhere.ToString().TrimEnd(',')})";

                var dbService = userCtx.Container.GetService<IDBService>();
                using (var reader = dbService.ExecuteReader(userCtx, strSql, paramList))
                {
                    while (reader.Read())
                    {
                        var number = reader.GetString("fnumber");
                        if (!number.IsNullOrEmptyOrWhiteSpace())
                        {
                            var name = reader.GetString("fname");
                            userList.Add(number, name);
                        }
                    }
                }
            }

            return userList;
        }

        /// <summary>
        /// 加载指定节点的上一节点处理人
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="designSectionId"></param>
        /// <param name="userList"></param>
        private void LoadPrevSectionProcessor(UserContext userCtx, string designSectionId, Dictionary<string, string> userList)
        {
            var dbService = userCtx.Container.GetService<IDBService>();
            var metaService = userCtx.Container.GetService<IMetaModelService>();

            string strSql = @"
            select t2.fexecutors,t2.fexecutors_txt from t_bpm_execution t1 with(nolock) 
            inner join t_bpm_executionentry t2 with(nolock) on t1.fid=t2.fid 
            where t1.fmainorgid=@fmainorgid and t1.fsectionid=@fsectionid and t2.fexec<>' ' and t2.fexecnumber<>' '";

            List<SqlParam> paramList = new List<SqlParam>()
            {
                new SqlParam("@fmainorgid", DbType.String, userCtx.Company),
                new SqlParam("@fsectionid", DbType.String, designSectionId)
            };

            var dicExecutor = new Dictionary<string, string>();

            using (var reader = dbService.ExecuteReader(userCtx, strSql, paramList))
            {
                while (reader.Read())
                {
                    var executors = reader.GetValue<string>("fexecutors");
                    var executors_txt = reader.GetValue<string>("fexecutors_txt");
                    if (executors.IsNullOrEmptyOrWhiteSpace() || executors_txt.IsNullOrEmptyOrWhiteSpace()) continue;

                    dicExecutor[executors] = executors_txt;
                }
            }
            foreach (var key in dicExecutor.Keys)
            {
                var numbers = key.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                var names = dicExecutor[key].Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                for (int i = 0; i < numbers.Length; i++)
                {
                    if (!userList.ContainsKey(numbers[i]))
                    {
                        userList.Add(numbers[i], names[i]);
                    }
                }
            }
        }

        /// <summary>
        /// 根据流程实例ID获取流程实例
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="flowInstanceId"></param>
        /// <returns></returns>
        public DynamicObject GetFlowInstanceById(UserContext userCtx, string flowInstanceId)
        {
            var htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, "bpm_flowinstance");
            var _flowinstanceDm = userCtx.GetDefaultDataManager(htmlForm);

            return _flowinstanceDm.Select(flowInstanceId) as DynamicObject;
        }
    }
}