using System.Linq;
using System.Collections.Generic;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Consts;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.Framework.AppService.OperationService.ApprovalFlow
{
    /// <summary>
    /// 审批流驳回操作
    /// </summary>
    [InjectService("RejectFlow")]
    public class RejectFlow : AbstractOperationService
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName { get { return ""; } }

        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem { get { return PermConst.PermssionItem_Unaudit; } }

        /// <summary>
        /// 服务初始化
        /// </summary>
        protected override void InitializeService()
        {
            base.InitializeService();

            this.ServiceControlOption.SupportIdemotency = true;
        }

        /// <summary>
        /// 执行过程
        /// </summary>
        /// <param name="dataEntities">数据包</param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length == 0) return;

            //当前系统是否已启用审批流
            var flowService = this.Container.GetService<IApprovalFlowService>();
            var isStartFlow = flowService.IsStartFlow(this.UserCtx, this.OperationContext.HtmlForm);
            var option = getOption();
            if (isStartFlow)
            {
                //没有提交过审批流的单据（可能在启用审批流之前，已经执行了提交操作，此时应该继续走平台的标准操作，以便兼容历史单据）
                DynamicObject[] dataEntitys = dataEntities.Where(o => o["fflowinstanceid"].IsNullOrEmptyOrWhiteSpace() == true).ToArray();
                if (dataEntitys != null && dataEntitys.Length > 0)
                {
                    //走平台通用“反审核操作”
                    var result = this.Gateway.InvokeBillOperation(this.UserCtx,
                            this.OperationContext.HtmlForm.Id,
                            dataEntitys,
                            "unaudit",
                            option
                            );
                    if (result.InteractionData != null)
                    {
                        var ex = new CustomException.InteractionException(result.InteractionData.Sponsor, result.InteractionData.SimpleMessage);
                        ex.InteractionData.Sponsor = result.InteractionData.Sponsor;
                        ex.InteractionData.SimpleMessage = result.InteractionData.SimpleMessage;
                        ex.InteractionData.CustomFormId = result.InteractionData.CustomFormId;
                        ex.InteractionData.CustomFormParameter = result.InteractionData.CustomFormParameter;
                        ex.InteractionData.SimpleMessage = result.InteractionData.SimpleMessage;
                        throw ex;
                    }
                    this.OperationContext.Result.MergeResult(result);
                }

                //有提交过审批流的单据
                DynamicObject[] flowDataEntitys = dataEntities.Where(o => o["fflowinstanceid"].IsNullOrEmptyOrWhiteSpace() == false).ToArray();
                if (flowDataEntitys != null && flowDataEntitys.Length > 0)
                {
                    //审批意见
                    var execOpinion = this.GetQueryOrSimpleParam<string>("execOpinion");

                    //是否终止流程
                    var terminate = this.GetQueryOrSimpleParam<bool>("terminate", false);

                    //走审批流“驳回操作”
                    var result = flowService.RejectFlow(this.UserCtx, this.OperationContext.HtmlForm, flowDataEntitys, execOpinion, terminate);
                    this.OperationContext.Result.MergeResult(result);
                }
            }
            else
            {
                //走平台通用“反审核操作”
                var result = this.Gateway.InvokeBillOperation(this.UserCtx,
                        this.OperationContext.HtmlForm.Id,
                        dataEntities,
                        "unaudit",
                        option);
                if (result.InteractionData != null)
                {
                    var ex = new CustomException.InteractionException(result.InteractionData.Sponsor, result.InteractionData.SimpleMessage);
                    ex.InteractionData.Sponsor = result.InteractionData.Sponsor;
                    ex.InteractionData.SimpleMessage = result.InteractionData.SimpleMessage;
                    ex.InteractionData.CustomFormId = result.InteractionData.CustomFormId;
                    ex.InteractionData.CustomFormParameter = result.InteractionData.CustomFormParameter;
                    ex.InteractionData.SimpleMessage = result.InteractionData.SimpleMessage;
                    throw ex;
                }
                this.OperationContext.Result.MergeResult(result);
            }

            this.OperationContext.Result.IsShowMessage = true;
            this.OperationContext.Result.IsSuccess = this.OperationContext.Result.ComplexMessage.SuccessMessages.Count > 0
                                        && (this.OperationContext.Result.ComplexMessage.ErrorMessages == null || this.OperationContext.Result.ComplexMessage.ErrorMessages.Count == 0);
        }

        private Dictionary<string, object> getOption()
        {
            var result = new Dictionary<string, object>();
            setOption(result, this.OperationContext.QueryStringParam);
            setOption(result, this.OperationContext.SimpleData);
            setOption(result, this.OperationContext.Option);
            return result;
        }

        private void setOption(Dictionary<string, object> option, IEnumerable<KeyValuePair<string, string>> sourceDatas)
        {
            foreach (var data in sourceDatas)
            {
                option[data.Key] = data.Value;
            }
        }

        private void setOption(Dictionary<string, object> option, IEnumerable<KeyValuePair<string, object>> sourceDatas)
        {
            foreach (var data in sourceDatas)
            {
                option[data.Key] = data.Value;
            }
        }
    }
}