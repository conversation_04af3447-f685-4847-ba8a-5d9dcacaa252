using System.Linq;
using System.Collections.Generic;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Consts;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.Framework.AppService.OperationService.ApprovalFlow
{
    /// <summary>
    /// 审批流终止操作
    /// </summary>
    [InjectService("TerminateFlow")]
    public class TerminateFlow : AbstractOperationService
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName { get { return ""; } }

        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem { get { return PermConst.PermssionItem_UnSubmit; } }

        /// <summary>
        /// 服务初始化
        /// </summary>
        protected override void InitializeService()
        {
            base.InitializeService();

            this.ServiceControlOption.SupportIdemotency = true;
        }

        /// <summary>
        /// 执行过程
        /// </summary>
        /// <param name="dataEntities">数据包</param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length == 0) return;

            //当前系统是否已启用审批流
            var flowService = this.Container.GetService<IApprovalFlowService>();
            var isStartFlow = flowService.IsStartFlow(this.UserCtx, this.OperationContext.HtmlForm);
            if (isStartFlow)
            {
                //没有提交过审批流的单据（可能在启用审批流之前，已经执行了提交操作，此时应该继续走平台的标准操作，以便兼容历史单据）
                DynamicObject[] dataEntitys = dataEntities.Where(o => o["fflowinstanceid"].IsNullOrEmptyOrWhiteSpace() == true).ToArray();
                if (dataEntitys != null && dataEntitys.Length > 0)
                {
                    //走平台通用“撤销操作”
                    var result = this.Gateway.InvokeBillOperation(this.UserCtx,
                            this.OperationContext.HtmlForm.Id,
                            dataEntitys,
                            "unsubmit",
                            new Dictionary<string, object>());
                    this.OperationContext.Result.MergeResult(result);
                }

                //有提交过审批流的单据
                DynamicObject[] flowDataEntitys = dataEntities.Where(o => o["fflowinstanceid"].IsNullOrEmptyOrWhiteSpace() == false).ToArray();
                if (flowDataEntitys != null && flowDataEntitys.Length > 0)
                {
                    //审批意见
                    var execOpinion = this.GetQueryOrSimpleParam<string>("execOpinion");

                    //走审批流“终止操作”
                    var result = flowService.TerminateFlow(this.UserCtx, this.OperationContext.HtmlForm, flowDataEntitys, execOpinion);
                    this.OperationContext.Result.MergeResult(result);
                }
            }
            else
            {
                //走平台通用“撤销操作”
                var result = this.Gateway.InvokeBillOperation(this.UserCtx,
                        this.OperationContext.HtmlForm.Id,
                        dataEntities,
                        "unsubmit",
                        new Dictionary<string, object>());
                this.OperationContext.Result.MergeResult(result);
            }

            this.OperationContext.Result.IsShowMessage = true;
            this.OperationContext.Result.IsSuccess = this.OperationContext.Result.ComplexMessage.SuccessMessages.Count > 0;
        }
    }
}