using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject;
using Newtonsoft.Json.Linq;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.Consts;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 基础资料查询发布操作
    /// </summary>
    [InjectService("SyncQuery")]
    public class SyncQuery : AbstractOperationService
    {
        protected override string OperationName
        {
            get
            {
                return "发布查询";
            }
        }

        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_View;
            }
        }


        /// <summary>
        /// 查询发布操作
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            //this.OperationContext.SimpleData["billFormId"] = this.OperationContext.HtmlForm.BillFormIdFldKey;

            #region 向EIS站点发送协同数据包
            var response = this.Gateway.Invoke(this.UserCtx,
                TargetSEP.EisService,
                new CommonBillDTO()
                {
                    FormId = "syn_shareddata",
                    OperationNo = "querypublish",
                    SimpleData = this.OperationContext.SimpleData
                }) as CommonBillDTOResponse;

            if (response == null || response.OperationResult == null)
            {
                this.OperationContext.Result.IsSuccess = false;
                this.OperationContext.Result.IsShowMessage = true;
                this.OperationContext.Result.SimpleMessage = "系统未返回任何响应！";
                return;
            }
            #endregion  
            response.OperationResult.ThrowIfHasError(true, "查询可下载资料时出现意外错误！");
            
            if (response.OperationResult.IsSuccess)
            {
                string srv = response.OperationResult.SrvData as string;
                //Dictionary<string, object> srvData = srv?.FromJson<Dictionary<string, object>>() ?? new Dictionary<string, object>();
                //Dictionary<string, int> listDesc = srvData["listDesc"]?.ToString().FromJson<Dictionary<string, int>>() ??
                //                                            new Dictionary<string, string> { { "count", "0" }, { "pages", "0" } };
                //List<Dictionary<string, string>> listData = srvData["listData"]?.ToString().FromJson<List<Dictionary<string, string>>>() ?? new List<Dictionary<string, string>>();
                //srvData["listDesc"] = listDesc;
                //srvData["listData"] = listData;
                this.OperationContext.Result.SrvData = JObject.Parse(srv);
                this.OperationContext.Result.SimpleMessage = "查询成功!";
                this.OperationContext.Result.IsSuccess = true;
            }
            else
            {
                this.OperationContext.Result.SimpleMessage = "查询失败!";
                this.OperationContext.Result.IsSuccess = false;
            }
        }
    }
}
