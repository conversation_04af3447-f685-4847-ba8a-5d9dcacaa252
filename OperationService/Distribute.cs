using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;
using Newtonsoft.Json;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.Consts;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 基础资料发布操作
    /// </summary>
    [InjectService("Distribute")]
    public class Distribute : AbstractOperationService
    {
        const string keyFormat = "{0}_{1}";

        protected override string OperationName
        {
            get
            {
                return "发布资料";
            }
        }

        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_Distribute;
            }
        }

        protected override bool AutoSaveData
        {
            get
            {
                return true;
            }
        }

        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            //获取范围信息
            var rangeInfo = getDistributeRangeInfo();

            //获取当前表单
            var htmlForm = OperationContext.HtmlForm;

            //获取分析过的表单
            var parsedHtmlForms = this.OperationContext.Option.GetVariableValue<Dictionary<string, List<DynamicObject>>>("parsedHtmlForms", null);
            if (parsedHtmlForms == null)
            {
                parsedHtmlForms = new Dictionary<string, List<DynamicObject>>();
                this.OperationContext.Option.SetVariableValue("parsedHtmlForms", parsedHtmlForms);
            }

            List<DynamicObject> parsedDynamicObjects = null;

            //判断当前表单是否已分析过
            if(parsedHtmlForms.TryGetValue(htmlForm.Id,out parsedDynamicObjects) == false)
            {
                parsedDynamicObjects = new List<DynamicObject>();
                parsedHtmlForms[this.OperationContext.HtmlForm.Id] = parsedDynamicObjects;
            }

            //本地id与云链id的映射,key为htmlFormId_dynamicObjectId,value为chaindataid
            var localIdToChainDataIdMap = this.OperationContext.Option.GetVariableValue<Dictionary<string, string>>("localIdToChainDataIdMap", null);
            if (localIdToChainDataIdMap == null)
            {
                localIdToChainDataIdMap = new Dictionary<string, string>();
                this.OperationContext.Option.SetVariableValue("localIdToChainDataIdMap", localIdToChainDataIdMap);
            }

            //实体引用表单实体的本地id，key为htmlFormId_dynamicObjectId,value为{fieldid_dynamicObjectValue,htmlFormId_dynamicObjectId}
            var referenceFormLocalIds = this.OperationContext.Option.GetVariableValue<Dictionary<string, Dictionary<string, string>>>("referenceFormLocalIds", null);
            if (referenceFormLocalIds == null)
            {
                referenceFormLocalIds = new Dictionary<string, Dictionary<string, string>>();
                this.OperationContext.Option.SetVariableValue("referenceFormLocalIds", referenceFormLocalIds);
            }

            //过滤出已分析过的实体
            var parsedEntities = dataEntities.Where(x => parsedDynamicObjects.Any(y => Convert.ToString(y["id"]) == Convert.ToString(x["id"]))).ToArray();

            //对分析过的记录进行校验，如果没有本地id与云链的映射，则当其没有分析过
            var unPublishEntities = parsedEntities.Where(x =>
              {
                  var key = getKey(htmlForm.Id, x);
                  string chaindataid = null;
                  if (localIdToChainDataIdMap.TryGetValue(key, out chaindataid) == false)
                  {
                      return true;
                  }
                  return string.IsNullOrWhiteSpace(chaindataid);
              }).ToArray();

            //过滤出未分析过的实体
            dataEntities = dataEntities.Where(x => parsedEntities.Contains(x) == false).ToArray();

            //获取当前表单所有可拷贝的字段
            var filedList = htmlForm.GetFieldList().Where(x => x.CanCopy == 1).ToList();
            //从可拷贝的字段中获取辅助资料
            var comboFieldList = filedList.Where(x => x is HtmlComboField).Select(x => x as HtmlComboField).ToList();
            //从可拷贝的字段中获取辅助属性
            var auxFieldList = filedList.Where(x => x is HtmlAuxPropertyField).Select(x => x as HtmlAuxPropertyField).ToList();
            //从可拷贝的字段中获取基础资料
            var baseDataFieldList = filedList.Where(x => x is HtmlBaseDataField).Select(x => x as HtmlBaseDataField).ToList();

            //分布未分析过的资料
            publishDatas(dataEntities,
                         htmlForm,
                         referenceFormLocalIds,
                         localIdToChainDataIdMap,
                         rangeInfo,
                         comboFieldList,
                         auxFieldList,
                         baseDataFieldList,
                         parsedDynamicObjects,
                         parsedHtmlForms,
                         false);

            //分布已分析过但未发布的资料
            publishDatas(unPublishEntities, 
                         htmlForm, 
                         referenceFormLocalIds, 
                         localIdToChainDataIdMap, 
                         rangeInfo, 
                         comboFieldList, 
                         auxFieldList, 
                         baseDataFieldList, 
                         parsedDynamicObjects,
                         parsedHtmlForms,
                         true);
        }

        private void publishDatas(DynamicObject[] dataEntities,
                                  HtmlForm htmlForm,
                                  Dictionary<string, Dictionary<string, string>> referenceFormLocalIds,
                                  Dictionary<string, string> localIdToChainDataIdMap,
                                  Dictionary<string, string> rangeInfo,
                                  List<HtmlComboField> comboFieldList,
                                  List<HtmlAuxPropertyField> auxFieldList,
                                  List<HtmlBaseDataField> baseDataFieldList,
                                  List<DynamicObject> parsedDynamicObjects,
                                  Dictionary<string, List<DynamicObject>> parsedHtmlForms,
                                  bool isParsed
                                  )
        {
            //如果没有分析过的表单实体，则返回
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            if (false == isParsed)
            {
                //将当前未分析过的实体加入已分析实体例表
                parsedDynamicObjects.AddRange(dataEntities);
            }

            //分析辅助资料
            parseHtmlForm(this.UserCtx, comboFieldList, dataEntities, htmlForm, this.OperationContext.Option, referenceFormLocalIds, parsedHtmlForms);
            //分析辅助属性
            parseHtmlForm(this.UserCtx, auxFieldList, dataEntities, htmlForm, this.OperationContext.Option, referenceFormLocalIds, parsedHtmlForms);
            //分析基础资料
            parseHtmlForm(this.UserCtx, baseDataFieldList, dataEntities, htmlForm, this.OperationContext.Option, referenceFormLocalIds, parsedHtmlForms);

            //通知插件分析表单模型无法分析的依赖资料
            var pareED = new OnCustomServiceEventArgs()
            {
                EventName = "parseHtmlForm",
                EventData = new Dictionary<string, object>
                {
                    { "dataEntities", dataEntities},
                    { "referenceFormLocalIds",referenceFormLocalIds},
                    { "parsedHtmlForms", parsedHtmlForms}
                },
            };
            this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", pareED);


            var sendDatas = dataEntities.Where(x => (Convert.ToDateTime(x["fmodifydate"]) > Convert.ToDateTime(x["fsenddate"])) ||
                                                     Convert.ToDateTime(x["fsenddate"]) == DateTime.MinValue ||
                                                     Convert.ToString(x["fsendstatus"]) == "已取消").Distinct(x => Convert.ToString(x["id"])).ToArray();

            //如果是系统预置资料不用发布，所以将它们过滤出来
            if (sendDatas != null && sendDatas.Length > 0)
            {
                sendDatas = sendDatas.Where(x => isPreset(htmlForm, x) == false).ToArray();
            }

            if (sendDatas != null && sendDatas.Length > 0)
            {
                var publishList = loadPublishData(this.UserCtx, htmlForm, sendDatas, referenceFormLocalIds, localIdToChainDataIdMap, this.PlugInProxy);

                if (publishList != null || publishList.Count > 0)
                {
                    var billFormId = htmlForm.Id;
                    var billFormName = htmlForm.Caption;

                    var simpleData = new Dictionary<string, string>
                                        {
                                            { "billFormId",billFormId},
                                            { "billFormName",billFormName},
                                            { "sharedData",JsonConvert.SerializeObject(publishList)},
                                            { "range",rangeInfo["range"]},
                                            { "companyInfos",rangeInfo["companyInfos"]}
                                        };

                    #region 向EIS站点发送协同数据包
                    var response = this.Gateway.Invoke(this.UserCtx,
                        TargetSEP.EisService,
                        new CommonBillDTO()
                        {
                            FormId = "syn_shareddata",
                            OperationNo = "publish",
                            SimpleData = simpleData
                        }) as CommonBillDTOResponse;

                    if (response == null || response.OperationResult == null)
                    {
                        throw new BusinessException($"系统未返回任何响应！");
                    }
                    #endregion

                    if (response.OperationResult.IsSuccess)
                    {
                        //保存云链数据id
                        string srcDataStr = response.OperationResult.SrvData as string;
                        List<Dictionary<string, string>> srvData = srcDataStr?.FromJson<List<Dictionary<string, string>>>() ?? new List<Dictionary<string, string>>();
                        foreach (var item in srvData)
                        {
                            var chainDataId = Convert.ToString(item["chainDataId"]);
                            var isSucess = Convert.ToBoolean(item["isSuccess"]);
                            var reason = Convert.ToString(item["reason"]);

                            if (string.IsNullOrWhiteSpace(chainDataId) || isSucess == false)
                            {
                                if (string.IsNullOrWhiteSpace(reason) == false)
                                {
                                    this.OperationContext.Result.ComplexMessage.ErrorMessages.Add(reason);
                                }
                                continue;
                            }

                            foreach (var itemEntity in dataEntities.Where(x => Convert.ToString(x["id"]) == item["dataId"]))
                            {
                                itemEntity["fchaindataid"] = chainDataId;
                                itemEntity["fsendstatus"] = "已发布";
                                itemEntity["fsenddate"] = DateTime.Now;
                            }
                        }
                    }
                    else
                    {
                        throw new BusinessException(string.Format("{0}发布失败", htmlForm.Caption));
                    }
                }
            }

            foreach (var dataEntity in dataEntities)
            {
                var key = getKey(htmlForm.Id, dataEntity);
                localIdToChainDataIdMap[key] = Convert.ToString(dataEntity["fchaindataid"]);
            }
        }

        private static List<object> loadPublishData(UserContext userContext, 
                                                    HtmlForm htmlForm, 
                                                    DynamicObject[] dataEntities, 
                                                    Dictionary<string, Dictionary<string, string>> referenceFormLocalIds, 
                                                    Dictionary<string, string> localIdToChainDataIdMap,
                                                    PlugInProxy<IOperationServicePlugIn> plugInProxy)
        {
            List<object> publishList = new List<object>();
            IUiDataConverter dataConverter = userContext.Container.GetService<IUiDataConverter>();
            var option = OperateOption.Create();
            option.SetVariableValue("__OpFlags__", Enu_OpFlags.HtmlEncodeWhenPublish);

            foreach (DynamicObject dataEntity in dataEntities)
            {
                var dataContent = dataConverter.CreateUIDataObject(userContext,
                                                               htmlForm,
                                                               dataEntity.Clone() as DynamicObject,
                                                               option)["uidata"];

                var dataContentED = new OnCustomServiceEventArgs()
                {
                    EventName = "modifyDataContent",
                    EventData = new Dictionary<string, object>
                    {
                        { "dataContent", dataContent},
                        { "dataEntity",dataEntity}
                    },
                };
                plugInProxy.InvokePlugInMethod("OnCustomServiceEvent", dataContentED);

                var key = getKey(htmlForm.Id, dataEntity);
                Dictionary<string, string> linkChainData = null;
                Dictionary<string, Dictionary<string,object>> repairChainData = null;

                Dictionary<string, string> fieldToLocalIdMap = null;

                if(referenceFormLocalIds.TryGetValue(key,out fieldToLocalIdMap))
                {
                    if (fieldToLocalIdMap != null && fieldToLocalIdMap.Count > 0)
                    {
                        //将依赖项的本地id转为云链id
                        linkChainData = fieldToLocalIdMap.Where(x => localIdToChainDataIdMap.Keys.Contains(x.Value)) //筛选出所有具有云链id的依赖项
                                                         .ToDictionary(k => k.Key, v => localIdToChainDataIdMap[v.Value]);
                        //获取需要修复的闭环依赖项
                        repairChainData = referenceFormLocalIds.Where(x => 
                                                                {
                                                                    //注：一个结点已发布过，但它引用的结点却没有发布过才需要修复

                                                                    //检测x结点是否在当前结点之前发布过
                                                                    if (!localIdToChainDataIdMap.Keys.Contains(x.Key))
                                                                    {
                                                                        return false;
                                                                    }
                                                                    //检测x结点是否引用当前结点，如果不是则返回false过滤掉
                                                                    if (!x.Value.Values.Contains(key))
                                                                    {
                                                                        return false;
                                                                    }
                                                                    //筛选x结点的未有云链id的引用结点
                                                                    var unChainDataIds = x.Value.Where(y => !localIdToChainDataIdMap.Keys.Contains(y.Value)).ToList();
                                                                    //如果未有云链id的结点数量不是1则过滤掉，因为多于1个时过早修复没有意义，少于1个时则不需要修复
                                                                    if (unChainDataIds.Count != 1)
                                                                    {
                                                                        return false;
                                                                    }
                                                                    //检测未有云链id的结点是否是当前结点
                                                                    return unChainDataIds.First().Value == key;
                                                                })
                                                               .ToDictionary(k => localIdToChainDataIdMap[k.Key], v => new Dictionary<string, object>
                                                                {
                                                                { "fieldId", v.Value.First(x=>x.Value==key).Key.Split('_')[0] },
                                                                { "linkChainData",v.Value.Where(x=>localIdToChainDataIdMap.Keys.Contains(x.Value))
                                                                                            .ToDictionary(kk=>kk.Key,vv=>localIdToChainDataIdMap[vv.Value])}
                                                                });
                    }
                }

                var publishItemED = new OnCustomServiceEventArgs()
                {
                    EventName = "getPublishItem",
                    EventData = new Dictionary<string, object>
                    {
                        { "dataEntity",dataEntity},
                        { "dataContent",dataContent},
                        { "linkChainData",linkChainData},
                        { "repairChainData",repairChainData}
                    },
                };
                plugInProxy.InvokePlugInMethod("OnCustomServiceEvent", publishItemED);

                object publishItem = publishItemED.Result;

                if (publishItem == null)
                {
                    publishItem = new Dictionary<string, object>
                    {
                        { "dataId" , Convert.ToString(dataEntity["id"]) },
                        { "dataNumber" , Convert.ToString(dataEntity[htmlForm.NumberFldKey]) },
                        { "dataName" , Convert.ToString(dataEntity[htmlForm.NameFldKey])},
                        { "dataContent" , dataContent.ToJson() },
                        { "linkChainData" , linkChainData.ToJson() },
                        { "repairChainData" , repairChainData.ToJson() }
                    };
                }

                publishList.Add(publishItem);
            }

            return publishList;
        }

        /// <summary>
        /// 分析辅助属性资料
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="refFormFieldList"></param>
        /// <param name="dataEntities"></param>
        /// <param name="form"></param>
        /// <param name="option"></param>
        /// <param name="referenceFormLocalIds"></param>
        /// <param name="parsedHtmlForms"></param>
        private static void parseHtmlForm(UserContext userContext,
                                          IEnumerable<HtmlAuxPropertyField> refFormFieldList,
                                          DynamicObject[] dataEntities,
                                          HtmlForm form,
                                          OperateOption option,
                                          Dictionary<string, Dictionary<string, string>> referenceFormLocalIds,
                                          Dictionary<string, List<DynamicObject>> parsedHtmlForms)
        {
            if (refFormFieldList == null || refFormFieldList.Count() <= 0)
            {
                return;
            }
            IMetaModelService metaModelService = userContext.Container.GetService<IMetaModelService>();
            ExtendedDataEntitySet dataEntitySet = new ExtendedDataEntitySet();
            dataEntitySet.Parse(userContext, dataEntities, form);

            List<Tuple<string,string,DynamicObject>> auxPropCache = new List<Tuple<string, string, DynamicObject>>();
            var htmlForm = metaModelService.LoadFormModel(userContext, refFormFieldList.FirstOrDefault().RefFormId);
            foreach (var refFormField in refFormFieldList)
            {
                var field = refFormField as HtmlField;
                var setDataEntities = dataEntitySet.FindByEntityKey(field.EntityKey);
                var dm = userContext.Container.GetService<IDataManager>();
                dm.InitDbContext(userContext, htmlForm.GetDynamicObjectType(userContext));

                foreach (var item in setDataEntities)
                {
                    DynamicObject obj = null;
                    var id = field.DynamicProperty.GetValue<string>(item.DataEntity);
                    if (string.IsNullOrWhiteSpace(id) == false)
                    {
                        obj = dm.Select(id) as DynamicObject;
                    }
                    if (obj == null)
                    {
                        continue;
                    }
                    refFormField.RefDynamicProperty.SetValue(item.DataEntity, obj);

                    //检查是否是系统预置的资料，如果是不用建立引用关系;
                    if (isPreset(htmlForm, obj))
                    {
                        continue;
                    }

                    //暂时不考虑多个辅助属性的情况
                    //auxPropCache.Add(new Tuple<string, string, DynamicObject>(Convert.ToString(item.DataEntity["id"]), getKey(field.Id, id), obj));
                    var itemEntitiy = item.DataEntity;
                    while (itemEntitiy.Parent != null)
                    {
                        itemEntitiy = itemEntitiy.Parent as DynamicObject;
                    }
                    auxPropCache.Add(new Tuple<string, string, DynamicObject>(Convert.ToString(itemEntitiy["id"]), field.Id, obj));
                }
            }

            if (auxPropCache == null || auxPropCache.Count <= 0)
            {
                return;
            }

            var baseFieldList = htmlForm.GetFieldList().Where(x => x.CanCopy == 1 && x is HtmlBaseDataField).Select(x => x as HtmlBaseDataField).ToList();

            if (baseFieldList == null || baseFieldList.Count <= 0)
            {
                return;
            }

            dataEntities = auxPropCache.Select(x => x.Item3).Distinct(x => Convert.ToString(x["id"])).ToArray();

            //引用表单数据列表
            Dictionary<string, List<DynamicObject>> refHtmlForms = new Dictionary<string, List<DynamicObject>>();
            dataEntitySet = new ExtendedDataEntitySet();
            dataEntitySet.Parse(userContext, dataEntities, htmlForm);

            foreach (var refFormField in baseFieldList)
            {
                htmlForm = metaModelService.LoadFormModel(userContext, refFormField.RefFormId);
                var field = refFormField as HtmlField;
                var setDataEntities = dataEntitySet.FindByEntityKey(field.EntityKey);
                var dm = userContext.Container.GetService<IDataManager>();
                dm.InitDbContext(userContext, htmlForm.GetDynamicObjectType(userContext));

                foreach (var item in setDataEntities)
                {
                    DynamicObject obj = null;
                    var id = field.DynamicProperty.GetValue<string>(item.DataEntity);
                    if (string.IsNullOrWhiteSpace(id) == false)
                    {
                        obj = dm.Select(id) as DynamicObject;
                    }
                    if (obj == null)
                    {
                        continue;
                    }
                    refFormField.RefDynamicProperty.SetValue(item.DataEntity, obj);

                    //检查是否是系统预置的资料，如果是不用建立引用关系;
                    if (isPreset(htmlForm, obj))
                    {
                        continue;
                    }

                    var headEntity = item.DataEntity;
                    var parentEntity = item.DataEntity.Parent as DynamicObject;

                    while (parentEntity != null)
                    {
                        headEntity = parentEntity;
                        parentEntity = parentEntity.Parent as DynamicObject;
                    }

                    foreach (var cacheItem in auxPropCache.Where(x => Convert.ToString(x.Item3["id"]) == Convert.ToString(headEntity["id"])))
                    {
                        //建立引用关系
                        var key = getKey(form.Id, cacheItem.Item1);
                        Dictionary<string, string> fieldToLocalIdMap = null;

                        if (referenceFormLocalIds.TryGetValue(key, out fieldToLocalIdMap) == false)
                        {
                            fieldToLocalIdMap = new Dictionary<string, string>();
                            referenceFormLocalIds[key] = fieldToLocalIdMap;
                        }

                        key = getKey(string.Concat(cacheItem.Item2, "|", field.Id), id);
                        if (fieldToLocalIdMap.Keys.Contains(key) == false)
                        {
                            fieldToLocalIdMap.Add(key, getKey(htmlForm.Id, id));
                        }
                    }

                    //如果已分析过，无需再分析
                    List<DynamicObject> parsedDynamicObjects = null;
                    parsedHtmlForms.TryGetValue(htmlForm.Id, out parsedDynamicObjects);
                    if (parsedDynamicObjects != null && parsedDynamicObjects.Any(x => Convert.ToString(x["id"]) == Convert.ToString(obj["id"])))
                    {
                        continue;
                    }

                    //将当前实体加入引用表单数据列表中
                    List<DynamicObject> dynamicObjects = null;

                    if (refHtmlForms.TryGetValue(htmlForm.Id, out dynamicObjects) == false)
                    {
                        dynamicObjects = new List<DynamicObject>();
                        refHtmlForms[htmlForm.Id] = dynamicObjects;
                    }

                    if (dynamicObjects.Any(x => Convert.ToString(x["id"]) == Convert.ToString(obj["id"])) == false)
                    {
                        dynamicObjects.Add(obj);
                    }
                }
            }

            if (refHtmlForms == null || refHtmlForms.Count <= 0)
            {
                return;
            }

            IHttpServiceInvoker gateway = userContext.Container.GetService<IHttpServiceInvoker>();
            foreach (var billForm in refHtmlForms)
            {
                var result = gateway.InvokeBillOperation(userContext,
                                billForm.Key,
                                billForm.Value,
                                "Distribute",
                                option.ToDictionary(k => k.Key, v => v.Value)
                             );

                if (result.IsSuccess)
                {
                    continue;
                }

                if (result.ComplexMessage.ErrorMessages.Count > 0)
                {
                    throw new BusinessException("上传依赖失败!");
                }
            }
        }

        /// <summary>
        /// 分析引用资料
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="refFormFieldList"></param>
        /// <param name="dataEntities"></param>
        /// <param name="form"></param>
        /// <param name="option"></param>
        /// <param name="referenceFormLocalIds"></param>
        /// <param name="parsedHtmlForms"></param>
        private static void parseHtmlForm(UserContext userContext,
                                          IEnumerable<IRefFormField> refFormFieldList,
                                          DynamicObject[] dataEntities,
                                          HtmlForm form,
                                          OperateOption option,
                                          Dictionary<string, Dictionary<string, string>> referenceFormLocalIds,
                                          Dictionary<string, List<DynamicObject>> parsedHtmlForms)
        {
            if (refFormFieldList == null || refFormFieldList.Count() <= 0)
            {
                return;
            }
            IMetaModelService metaModelService = userContext.Container.GetService<IMetaModelService>();
            ExtendedDataEntitySet dataEntitySet = new ExtendedDataEntitySet();
            dataEntitySet.Parse(userContext, dataEntities, form);

            //引用表单数据列表
            Dictionary<string, List<DynamicObject>> refHtmlForms = new Dictionary<string, List<DynamicObject>>();
            foreach (var refFormField in refFormFieldList)
            {
                if (refFormField.RefDynamicProperty == null) continue;

                var htmlForm = metaModelService.LoadFormModel(userContext, refFormField.RefFormId);
                var field = refFormField as HtmlField;
                var setDataEntities = dataEntitySet.FindByEntityKey(field.EntityKey);
                var dm = userContext.Container.GetService<IDataManager>();
                dm.InitDbContext(userContext, htmlForm.GetDynamicObjectType(userContext));

                foreach (var item in setDataEntities)
                {
                    DynamicObject obj = null;
                    var id = field.DynamicProperty.GetValue<string>(item.DataEntity);
                    if (string.IsNullOrWhiteSpace(id) == false)
                    {
                        obj = dm.Select(id) as DynamicObject;
                    }
                    if (obj == null)
                    {
                        continue;
                    }

                    refFormField?.RefDynamicProperty?.SetValue(item.DataEntity, obj);

                    //检查是否是系统预置的资料，如果是不用建立引用关系;
                    if (isPreset(htmlForm, obj))
                    {
                        continue;
                    }

                    //建立引用关系
                    var key = getKey(form.Id, item.DataEntity);
                    Dictionary<string,string> fieldToLocalIdMap = null;

                    if (referenceFormLocalIds.TryGetValue(key, out fieldToLocalIdMap) == false)
                    {
                        fieldToLocalIdMap = new Dictionary<string, string>();
                        referenceFormLocalIds[key] = fieldToLocalIdMap;
                    }

                    key = getKey(field.Id, id);
                    if (fieldToLocalIdMap.Keys.Contains(key)==false)
                    {
                        fieldToLocalIdMap.Add(key, getKey(htmlForm.Id,id));
                    }

                    //如果已分析过，无需再分析，但对于同一表单的自引用要进一步分析，如果是同一个实体的自引用不必再分析，如果是父子引用要对父结点进一步分析
                    List<DynamicObject> parsedDynamicObjects = null;
                    parsedHtmlForms.TryGetValue(htmlForm.Id, out parsedDynamicObjects);
                    if (parsedDynamicObjects != null && parsedDynamicObjects.Any(x => Convert.ToString(x["id"]) == Convert.ToString(obj["id"])))
                    {
                        //检查是否是同一表单的自引用
                        if (false == htmlForm.Id.EqualsIgnoreCase(form.Id))
                        {
                            //不是同一表单的引用不必再分析
                            continue;
                        }
                        //检查是否是同一实体的自引用
                        var parentEntity = item.DataEntity;
                        while (parentEntity.Parent != null)
                        {
                            parentEntity = parentEntity.Parent as DynamicObject;
                        }
                        //如果是同一实例的引用，不必再分析
                        if (Convert.ToString(parentEntity["id"]).EqualsIgnoreCase(id))
                        {
                            continue;
                        }
                        //现在排除了同一实例的引用，那就是父子实例的引用，且如果父子实例在同一批次中，需要对父子结点进一步分析
                        var dataEntity = dataEntities.FirstOrDefault(x => Convert.ToString(x["id"]).EqualsIgnoreCase(id));
                        if (dataEntity == null)
                        {
                            //不是同一批次不必再分析
                            continue;
                        }
                    }

                    //将当前实体加入引用表单数据列表中
                    List<DynamicObject> dynamicObjects = null;

                    if (refHtmlForms.TryGetValue(htmlForm.Id, out dynamicObjects) == false)
                    {
                        dynamicObjects = new List<DynamicObject>();
                        refHtmlForms[htmlForm.Id] = dynamicObjects;
                    }

                    if (dynamicObjects.Any(x => Convert.ToString(x["id"]) == Convert.ToString(obj["id"])) == false)
                    {
                        dynamicObjects.Add(obj);
                    }
                }
            }

            if (refHtmlForms == null || refHtmlForms.Count <= 0)
            {
                return;
            }

            IHttpServiceInvoker gateway = userContext.Container.GetService<IHttpServiceInvoker>();
            foreach (var billForm in refHtmlForms)
            {
                var result = gateway.InvokeBillOperation(userContext,
                                billForm.Key,
                                billForm.Value,
                                "Distribute",
                                option.ToDictionary(k => k.Key, v => v.Value)
                             );

                if (result.IsSuccess)
                {
                    continue;
                }

                if (result.ComplexMessage.ErrorMessages.Count > 0)
                {
                    throw new BusinessException("上传依赖失败!");
                }
            }
        }

        protected override void SaveDataEntities(ref DynamicObject[] dataEntities)
        {
            if (dataEntities != null && dataEntities.Length <= 0)
            {
                return;
            }
            var saveED = new OnCustomServiceEventArgs()
            {
                EventName = "saveDataEntities",
                EventData = dataEntities,
            };
            this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", saveED);
            var result = saveED.Result as string;
            if (result != "OK")
            {
                var dm = this.Container.GetService<IDataManager>();
                dm.InitDbContext(this.UserCtx, this.OperationContext.HtmlForm.GetDynamicObjectType(this.UserCtx));
                //更新本地数据为已发布状态
                dm.Save(dataEntities);
            }
        }

        private Dictionary<string, string> getDistributeRangeInfo()
        {
            List<Dictionary<string, string>> companyInfos = new List<Dictionary<string, string>>();
            var range = this.GetQueryOrSimpleParam<string>("range", "0");
            var cooCompanyIds = this.GetQueryOrSimpleParam<string>("cooCompanyIds")?.Split(new[] { ',', '，' }, StringSplitOptions.RemoveEmptyEntries);

            if (range == "2")
            {
                if (cooCompanyIds == null || cooCompanyIds.Length <= 0)
                {
                    throw new BusinessException("部分发布时，请选择您允许下载的企业!");
                }

                var metaModelService = this.Container.GetService<IMetaModelService>();
                var htmlForm = metaModelService.LoadFormModel(this.UserCtx, "coo_company");
                var dm = this.Container.GetService<IDataManager>();
                dm.InitDbContext(this.UserCtx, htmlForm.GetDynamicObjectType(this.UserCtx));

                var cooCompanyList = dm.Select(cooCompanyIds).OfType<DynamicObject>().ToList();
                
                foreach(var cooCompany in cooCompanyList)
                {
                    companyInfos.Add(new Dictionary<string, string>
                    {
                        { "companyId", Convert.ToString(cooCompany["fcompanyid"])},
                        { "companyName",Convert.ToString(cooCompany["fname"])},
                        { "productId",Convert.ToString(cooCompany["fproductid"])}
                    });
                }
            }

            return new Dictionary<string, string>
            {
                { "range",range},
                { "companyInfos",Newtonsoft.Json.JsonConvert.SerializeObject(companyInfos)}
            };
        }

        private static bool isPreset(HtmlForm htmlForm, DynamicObject dataEntity)
        {
            //var fispreset = htmlForm.GetField("fispreset");
            //if (fispreset != null)
            //{
            //    var ispreset = Convert.ToBoolean(dataEntity[fispreset.PropertyName]);
            //    if (ispreset)
            //    {
            //        return true;
            //    }
            //}
            var fmainorgid = htmlForm.GetField("fmainorgid");
            if (fmainorgid != null)
            {
                var mainorgid = Convert.ToString(dataEntity[fmainorgid.PropertyName]);
                if (dataEntity.DataEntityState.FromDatabase && (string.IsNullOrWhiteSpace(mainorgid) || mainorgid == "0"))
                {
                    return true;
                }
            }
            return false;
        }

        private static string getKey(string symbol, DynamicObject dynamicObject)
        {
            while (dynamicObject.Parent != null)
            {
                dynamicObject = dynamicObject.Parent as DynamicObject;
            }
            return getKey(symbol, Convert.ToString(dynamicObject["id"]));
        }

        private static string getKey(string symbol,string value)
        {
            return string.Format(keyFormat, symbol, value);
        }
    }
}
