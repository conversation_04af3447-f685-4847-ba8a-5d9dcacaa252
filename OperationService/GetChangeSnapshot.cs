using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using JieNor.Framework.Consts;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 获取变更快照
    /// </summary>
    [InjectService("getchangesnapshot")]
    public class GetChangeSnapshot : AbstractOperationService
    {
        protected override string OperationName
        {
            get
            {
                return "获取变更快照";
            }
        }

        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_View;
            }
        }

        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var metaSnap = this.MetaModelService.LoadFormModel(this.UserCtx, "bas_billchangesnapshot");
            var dmSnap = this.GetDataManager();
            dmSnap.InitDbContext(this.UserCtx, metaSnap.GetDynamicObjectType(this.UserCtx));

            List<string> billIds = new List<string>();

            foreach (DynamicObject data in dataEntities)
            {
                if (data["Id"].IsNullOrEmptyOrWhiteSpace() || billIds.Contains(data["Id"].ToString()))
                {
                    continue;
                }
                billIds.Add("'" + data["Id"].ToString() + "'");
            }
            List<DynamicObject> snaps = dmSnap.SelectBy("fbillinterid in ({0}) and fmainorgid='{1}' and fworkobject='{2}'".Fmt(string.Join(",", billIds),
                this.UserCtx.Company, this.OperationContext.HtmlForm?.Id)).OfType<DynamicObject>().ToList();
            if (snaps.Count == 0)
            {
                return;
            }
            var billItems = new List<string>();
            foreach (DynamicObject data in dataEntities)
            {
                List<DynamicObject> snapAll = (from s in snaps
                                               where (s["fbillinterid"]?.ToString()).EqualsIgnoreCase(data["Id"]?.ToString())
                                               select s).ToList();
                if (snapAll.Count == 0)
                {
                    continue;
                }
                DynamicObject snap = snapAll.OrderByDescending(f => f["fcreatedate"]).FirstOrDefault();
                if (snap["fbillsnapshot"].IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }
                billItems.Add(snap["fbillsnapshot"].ToString());
            }
            var result = JArray.Parse($"[{string.Join(",", billItems)}]");
            this.OperationContext.Result.SrvData = new { datas = result };
            this.OperationContext.Result.IsSuccess = true;
        }
    }
}
