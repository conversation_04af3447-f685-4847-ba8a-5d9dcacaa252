using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Report;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.OperationService.Report
{
    /// <summary>
    /// 加载报表数据
    /// </summary>
    [InjectService("loadrptdata")]
    public class LoadRptData : AbstractOperationService
    {
        protected override string OperationName
        {
            get
            {
                return "";
            }
        }

        protected override string PermItem
        {
            get
            {
                return "";
            }
        }

        protected ReportOperationContext ReportOperationContext
        {
            get
            {
                return this.OperationContext as ReportOperationContext;
            }
        }

        /// <summary>
        /// 初始化时对报表概要信息进行回传
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void InitializeOperationDataEntities(ref DynamicObject[] dataEntities)
        {

        }


        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            if (this.ReportOperationContext.IsNullOrEmpty())
            {
                throw new BusinessException("本操作只支持报表的领域模型调用！");
            }
            if (this.ReportOperationContext.ReportTemplate.IsNullOrEmpty())
            {
                throw new BusinessException($"报表配置文件没有配置，请检查:{this.OperationContext.HtmlForm.Id}.rpt.json！");
            }

            if (this.ReportOperationContext.ReportTemplate.service.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"报表配置文件({this.OperationContext.HtmlForm.Id}.rpt.json)中没有配置数据源（service属性）信息！");
            }
            this.ReportOperationContext.Option.SetVariableValue("_plugInProxy_", this.PlugInProxy);

            //var rptService = this.Container.GetServiceByName<IReportDataService>(this.ReportOperationContext.ReportTemplate.service);
            var rptService = this.Container.GetAllServiceByMeta<IReportDataService>("formId", this.ReportOperationContext.ReportTemplate.service)
                .OrderByDescending(o => o.Priority)
                .FirstOrDefault();

            var rptGridData = rptService?.GetRptDetailData(this.UserCtx, this.ReportOperationContext);

            this.OperationContext.Result.SrvData = rptGridData;

            this.OperationContext.Result.IsSuccess = true;
            this.OperationContext.Result.IsShowMessage = true;
        }
    }
}
