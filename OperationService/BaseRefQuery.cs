using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.IoC;
using System.Collections.Generic;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 基础资料引用属性查询
    /// </summary>
    [InjectService("baserefquery")]
    public class BaseRefQuery : FuzzyQuery
    {
        protected override bool IsQueryDataOnly
        {
            get
            {
                return true;
            }
        }
        /// <summary>
        /// 重置过滤条件信息，只允许
        /// </summary>
        /// <param name="lstColumns"></param>
        /// <param name="matchRule"></param>
        /// <returns></returns>
        protected override List<FilterRowObject> GetFilter(List<ColumnObject> lstColumns, string matchRule)
        {
            //默认只用主键列进行精确匹配返回数据。
            var pkId = this.GetQueryOrSimpleParam<string>("id");

            return new List<FilterRowObject>()
            {
                new FilterRowObject()
                {
                    Id=this.OperationContext.HtmlForm?.BillPKFldName,
                    Operator="=",
                    Value=pkId,
                    RowIndex=0,
                }
            };
        }
    }
}
