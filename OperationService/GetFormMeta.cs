using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.OperationService
{
    [InjectService("getformmeta")]
    public class GetFormMeta : AbstractOperationService
    {
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var uiDataService = this.Container.GetService<IUiDataConverter>();
            this.OperationContext.Result.SrvData = uiDataService.CreateUIMetaObject(this.UserCtx, this.OperationContext.HtmlForm.Id, this.DomainType, true);            
        }
    }
}
