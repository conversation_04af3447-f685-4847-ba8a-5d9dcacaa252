using JieNor.Framework.Consts;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Drivers;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 查看
    /// </summary>
    [InjectService("View")]
    public class View : AbstractOperationService
    {
        protected override string OperationName
        {
            get
            {
                return "查看";
            }
        }
        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_View;
            }
        }

        /// <summary>
        /// 是否需要解码
        /// </summary>
        protected virtual bool IsHtmlDecode => true;

        /// <summary>
        /// 获取数据状态
        /// </summary>
        /// <returns></returns>
        protected virtual Enu_BillStatus GetBillStatus()
        {
            return Enu_BillStatus.View;
        }

        /// <summary>
        /// 打开页面时的领域模型
        /// </summary>
        protected override Enu_DomainType DomainType
        {
            get
            {
                switch (this.OperationContext.HtmlForm.ElementType)
                {
                    case HtmlElementType.HtmlForm_DynamicForm:
                        return Enu_DomainType.Dynamic;
                    case HtmlElementType.HtmlForm_BillForm:
                    case HtmlElementType.HtmlForm_BaseForm:
                        return Enu_DomainType.Bill;
                    case HtmlElementType.HtmlForm_ReportForm:
                        return Enu_DomainType.Report;
                    case HtmlElementType.HtmlForm_ParameterForm:
                        return Enu_DomainType.Parameter;
                    default:
                        return base.DomainType;
                }
            }
        }

        /// <summary>
        /// 当前用户上下文
        /// </summary>
        protected UserContext Context { get { return this.OperationContext.UserContext; } }

        /// <summary>
        /// 当前操作的表单模型
        /// </summary>
        protected HtmlForm HtmlForm { get { return this.OperationContext.HtmlForm; } }

        /// <summary>
        /// 操作执行
        /// </summary>
        //[PerfMonitor]
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            Stopwatch watch = Stopwatch.StartNew();

            if (dataEntities == null)
            {
                dataEntities = new DynamicObject[] { };
            }
            string currPkId = this.OperationContext.SelectedRows?.FirstOrDefault()?.PkValue;

            //兼容一下直接从url传过来的情况
            if (currPkId.IsEmptyPrimaryKey())
            {
                currPkId = this.GetQueryOrSimpleParam<string>("id");
            }

            DynamicObject dcDataEntity = dataEntities.FirstOrDefault();
            if (dcDataEntity == null)
            {
                //查看修改时，直接从db及缓存中读取整单数据包
                if (!currPkId.IsEmptyPrimaryKey())
                {
                    var dm = this.OperationContext.Container.GetService<IDataManager>();
                    dm.InitDbContext(this.UserCtx, this.OperationContext.HtmlForm.GetDynamicObjectType(UserCtx));
                    dcDataEntity = dm.Select(currPkId) as DynamicObject;
                }
                if (dcDataEntity == null)
                {
                    throw new BusinessException("操作失败：待查看或修改的数据档案可能已被删除！");
                }
            }

            dataEntities = new DynamicObject[] { dcDataEntity };

            var numberPropertyName = this.HtmlForm.GetNumberField()?.PropertyName ?? "id";
            var number = Convert.ToString(dcDataEntity[numberPropertyName]);
            var logInfo = $"{this.Context.Company}/{this.Context.UserName} {this.HtmlForm.Caption}/{this.HtmlForm.Id} {dcDataEntity["id"]}/{number} 操作{this.OperationContext.OperationNo}/{this.OperationContext.OperationName}";

            var logService = this.Container.GetService<ILogServiceEx>();
            logService.Info($"{logInfo}加载查看操作数据包耗时：{watch.Elapsed}");

            if (dataEntities == null || !dataEntities.Any()) return;

            watch.Restart();
            //加载引用数据
            this.OperationContext.Container.GetService<LoadReferenceObjectManager>()?.Load(this.UserCtx, this.OperationContext.HtmlForm.GetDynamicObjectType(UserCtx), dataEntities, false);

            logService.Info($"{logInfo}加载数据包引用数据时耗时：{watch.Elapsed}");

            if (this.IsHtmlDecode)
            {
                this.OperationContext.Option.SetOptionFlag(Enu_OpFlags.HtmlDecodeWhenModify);
            }

            watch.Restart();
            var uiConverter = this.OperationContext.Container.GetService<IUiDataConverter>();
            var billJsonData = uiConverter.CreateUIDataObject(this.OperationContext.UserContext, this.OperationContext.HtmlForm, dataEntities.FirstOrDefault(), this.OperationContext.Option);
            logService.Info($"{logInfo}打包表单数据包时耗时：{watch.Elapsed}");

            if (GetBillStatus() == Enu_BillStatus.Modify)
            {
                var svc = this.UserCtx.Container.GetService<Interface.UsrMgr.IPermissionService>();
                var para = new MetaCore.FormModel.List.SqlBuilderParameter(this.UserCtx, this.HtmlForm.Id);
                var rulePara = new Interface.QueryBuilder.DataQueryRuleParaInfo()
                {
                    FormId = this.HtmlForm.Id,
                    SrcFormId = para.SrcFormId,
                    SrcPara = para.SrcPara,
                    SrcFldId = para.SrcFldId,
                };

                var filterRowObjs = svc.GetDataRowACLFilter(para.Ctx, rulePara);
                if (filterRowObjs.Count() >= 1)
                {
                    //列表数据
                    var listQueryBuilder = this.OperationContext.Container.GetService<Interface.QueryBuilder.IListSqlBuilder>();

                    List<FilterRowObject> lstFilter = new List<FilterRowObject>();
                    lstFilter.Add(new FilterRowObject { Id = $"{this.HtmlForm.BillPKFldName}", Operator = "=", Value = dcDataEntity["Id"].ToString() });
                    para.SetFilter(lstFilter);

                    MetaCore.QueryObject queryObj = listQueryBuilder.GetQueryObject(this.UserCtx, para);

                    int allCount = 0;

                    using (var reader = this.DBService.ExecuteReader(this.UserCtx, queryObj.AllCountSql, para.DynamicParams))
                    {
                        if (reader.Read())
                        {
                            allCount = Convert.ToInt32(reader[0]);
                            if (allCount <= 0)
                            {
                                throw new CustomException.BusinessException($"对不起，您没有编码为{ dcDataEntity[this.HtmlForm.NumberFldKey]}单据的查看权限。");
                            }
                        }
                    }
                }
            }

            watch.Restart();

            //前端页面容器标识
            var containerId = GetQueryOrSimpleParam<string>("containerId");

            //设置页面打开方式（如果前端有传递，则用传递的方式打开，否则按默认的方式打开）
            var layoutId = this.GetQueryOrSimpleParam<string>("layoutId", "");
            if (layoutId.IsNullOrEmptyOrWhiteSpace())
            {
                layoutId = this.OperationContext.FormLayoutId;
            }

            var action = this.OperationContext.UserContext.ShowForm(this.OperationContext.HtmlForm,
                currPkId,
                this.OperationContext.PageId,
                Enu_DomainType.Bill,
                this.OpenStyle,
                this.GlobalParameter,
                (formPara) =>
                {
                    formPara.UiData = billJsonData.GetJsonValue("uiData", new JObject());
                    this.SetFormCaption(formPara);

                    if (EnableBillLock(true))
                    {
                        if (this.BillLockService.TryLock(this.UserCtx, this.HtmlForm.Id, currPkId, number, out var lockTokenInfo))
                        {
                            formPara.LockTokenInfo = lockTokenInfo;

                            // 把锁加到页面缓存里
                            this.SetPageSessionValue("__LockToken__", lockTokenInfo.LockToken, this.HtmlForm.Id, formPara.PageId);
                            this.SetPageSessionValue("__LockBizObjId__", currPkId, this.HtmlForm.Id, formPara.PageId);
                        }
                        else
                        {
                            formPara.IsLock = true;
                            formPara.Status = Enu_BillStatus.View;

                            this.OperationContext.Result.ComplexMessage.WarningMessages.Add($"{this.OperationContext.HtmlForm.Caption} {number} 正在被 {lockTokenInfo?.DisplayName} 编辑中，您只能查看！");
                        }
                    }

                    //允许插件干预打包数据过程
                    var ae = new OnCustomServiceEventArgs()
                    {
                        EventName = "afterCreateUIData",
                        EventData = formPara.UiData,
                    };
                    this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", ae);
                    formPara.UiData = ae.EventData as JObject;

                    var dctShowPara = this.OperationContext.Option.GetFormShowParameter();
                    if (dctShowPara != null)
                    {
                        formPara.CustomParameter.Merge(dctShowPara);
                    }

                    //将单据类型的控制参数放入自定义参数包
                    var billTypeField = this.HtmlForm.GetFieldList().FirstOrDefault(f => f is HtmlBillTypeField && f.Entity is HtmlHeadEntity);
                    if (billTypeField != null)
                    {
                        var billTypeId = billTypeField.DynamicProperty.GetValue<string>(dcDataEntity);
                        if (!billTypeId.IsEmptyPrimaryKey())
                        {
                            var billTypeService = this.Context.Container.GetService<IBillTypeService>();
                            var billTypeObj = billTypeService.GetBillTypeById(this.Context, billTypeId);
                            formPara.uiBillTypeParam = billTypeService.CreateBillTypeUIDataObject(this.Context, billTypeObj); 
                        }
                    }

                }, containerId, layoutId);

            logService.Info($"{logInfo}生成操作指令时耗时：{watch.Elapsed}");

            this.OperationContext.Result.HtmlActions.Add(action);
            this.OperationContext.Result.IsSuccess = true;
            this.OperationContext.Result.IsShowMessage = true;
        }

        protected override void AfterExecute(ref DynamicObject[] dataEntities)
        {
            base.AfterExecute(ref dataEntities);

            if (dataEntities.IsNullOrEmpty())
            {
                return;
            }

            var action = this.OperationContext.Result.HtmlActions.FirstOrDefault(s => s is HtmlViewAction) as HtmlViewAction;
            if (action != null)
            {
                if (action.ActionParams.TryGetValue("fsp", out var fsp))
                {
                    var formShowPara = fsp as FormShowParameter;
                    if (formShowPara != null && formShowPara.IsLock)
                    {
                        var numberFld = this.HtmlForm.GetNumberField();

                        #region 锁定所有菜单

                        // 忽略单据锁定的菜单
                        var ignoreBillLockMenus = "tbNew,tbCopy,tbLookauditlog,tbOperateLogs,tbExportExcelByPrintTmpl,tbPrint,tbPrintTmpl,tbShareBill,tbResetlayout,tbSharelayout,tbLinkFormSearch,tbListAttachment,tbShowRecord,tbQueryChange".SplitKey();
                        foreach (JObject topMenu in formShowPara.FormTopMenu)
                        {
                            var ignoreBillLock = topMenu.GetJsonValue<bool>("ignoreBillLock");
                            if (ignoreBillLock)
                            {
                                var menuId = topMenu.GetJsonValue<string>("id");
                                ignoreBillLockMenus.Add(menuId);
                            }
                        }

                        var lockAllMenus = new JObject();
                        lockAllMenus["id"] = "__lock_alls";
                        lockAllMenus["expression"] = $"field:*$;menu:*${ignoreBillLockMenus.JoinEx(",", false)}|{numberFld.Id}!='' and fmainorgid!='' ";

                        // 加上UiRule
                        var lockRules = formShowPara.UiRule.GetJsonValue<JArray>("lockRules", new JArray());
                        lockRules.Add(lockAllMenus);

                        formShowPara.UiRule["lockRules"] = lockRules;

                        formShowPara.IgnoreBillLockMenus = ignoreBillLockMenus;

                        #endregion
                    }
                }
            }
        }
    }
}