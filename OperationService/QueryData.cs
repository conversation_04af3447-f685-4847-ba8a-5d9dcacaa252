using JieNor.Framework.Consts;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.QueryFilter;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Profile;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SQL.Exception;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JieNor.Framework.AppService.Integration;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface.Cache;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 列表查询数据操作
    /// </summary>
    [InjectService("QueryData")]
    public class QueryData : AbstractQueryListData
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName
        {
            get
            {
                return "";
            }
        }

        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_View;
            }
        }

        protected override void InitializeOperationDataEntities(ref DynamicObject[] dataEntities)
        {
            base.InitializeOperationDataEntities(ref dataEntities);
            this.OpCtlParam.DisableTransaction = true;
        }

        /// <summary>
        /// 服务初始化
        /// </summary>
        protected override void InitializeService()
        {
            base.InitializeService();

            this.ServiceControlOption.SupportIdemotency = true;
        }

        /// <summary>
        /// 操作执行过程
        /// </summary>
        /// <param name="dataEntities">数据包</param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            if (this.ListOperationContext == null)
            {
                throw new ArgumentException("列表操作上下文不可以为空！");
            }

            var category = this.DomainType.ToString().ToLower();
            var userProfile = this.Container.GetService<IUserProfile>()?.LoadUserProfile(this.UserCtx, this.QueryHtmlForm.Id, category);
            var isUnmergeBillHeadField = userProfile?.ListParamData?.UnmergeBillHeadField == true;

            string activeEntityKey = "";
            //参数对象
            SqlBuilderParameter para = BuildQueryParameter(ref activeEntityKey);

            //是否合并单据头字段
            para.MergeBillHeadField = !isUnmergeBillHeadField;

            //允许插件干预过滤条件设置
            var ae = new OnCustomServiceEventArgs()
            {
                EventName = "afterBuildSqlParam",
                EventData = para,
            };
            this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", ae);

            //列表数据
            var listQueryBuilder = this.OperationContext.Container.GetService<IListSqlBuilder>();

            QueryObject queryObj = listQueryBuilder.GetQueryObject(this.UserCtx, para);

            //分页信息：总纪录数，总页数，每页条数，单据数
            para.IsGetDesc = CanGetDesc;
            if (para.PageIndex > 1)
            {
                //para.IsGetDesc = false;
            }

            ListDesc listDesc = GetListDesc(listQueryBuilder, para, queryObj, activeEntityKey);

            List<Dictionary<string, object>> listData = listQueryBuilder.GetQueryData(this.QueryUserContent, para, queryObj);
            //允许插件干预列表数据
            ae = new OnCustomServiceEventArgs()
            {
                EventName = "afterListData",
                EventData = listData,
            };
            this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", ae);
            listData = ae.EventData as List<Dictionary<string, object>>;
             
            //this.ProcStatisFieldInfo(userProfile, para, listDesc, listData);

            //处理列表结果数据
            this.ProcessListResult(new QueryDataInfo()
            {
                Datas = listData,
                DatasDesc = listDesc
            },
            para);
            //返回影响行数给埋点，此时的rows获取不到，因为异步了
            //this.OperationContext.SimpleData.Add("rows", listDesc?.Rows.ToString());
            this.OperationContext.SimpleData.Add("filters", queryObj.SqlWhere.ToString());
        }


        private static bool CanGetDesc
        {
            get
            {
                var canGet = "".GetAppConfig<string>("ms.listquerydata.cangetdesc", "");
                if (canGet.IsNullOrEmptyOrWhiteSpace() || canGet.EqualsIgnoreCase("1") || canGet.EqualsIgnoreCase("true"))
                {
                    return true;
                }

                return false ;
            }
        }


        /// <summary>
        /// 异步执行查询分页信息并缓存到内存中，前端通过 JieNor.Framework.AppService.SystemPlugIn.MainFw.QueryDataDesc 获取
        /// </summary>
        /// <param name="listQueryBuilder"></param>
        /// <param name="para"></param>
        /// <param name="queryObj"></param>
        /// <param name="activeEntityKey"></param>
        /// <returns></returns>
        private ListDesc GetListDesc(IListSqlBuilder listQueryBuilder, SqlBuilderParameter para, QueryObject queryObj, string activeEntityKey)
        {
            ListDesc result = null;
            var key = "lstdesc.{0}".Fmt(this.OperationContext.PageId);
            if (this.ListOperationContext.IsAsynLstDesc)
            {
                result = new ListDesc();
                SystemPlugIn.MainFw.QueryDataDesc._cacheQueryDataDesc.TryAdd(key, result);
                Task task = new Task(() =>
              {
                  //分页信息：总纪录数，总页数，每页条数，单据数
                  var lstDesc = listQueryBuilder.GetListDesc(this.QueryUserContent, para, queryObj);
                  lstDesc.ActiveEntityKey = activeEntityKey;
                  //返回影响行数给埋点
                  this.OperationContext.SimpleData.Add("rows", lstDesc?.Rows.ToString()); 
                  SystemPlugIn.MainFw.QueryDataDesc._cacheQueryDataDesc.AddOrUpdate(key, lstDesc, (p, v) => lstDesc);
              });
                ThreadWorker.QuequeTask(task, x =>
                {
                    if (x?.Exception != null)
                    {
                        this.OperationContext?.Container.GetService<ILogServiceEx>().Error("异步执行查询分页信息并缓存到redis时出现错误：" + x.Exception.Message);
                    }
                });
            }
            else
            {
                result = listQueryBuilder.GetListDesc(this.QueryUserContent, para, queryObj);
                result.ActiveEntityKey = activeEntityKey;
                SystemPlugIn.MainFw.QueryDataDesc._cacheQueryDataDesc.TryAdd(key, result);
            }
              
            return result;
        }


        /// <summary>
        /// 处理统计字段信息（合计，平均，最大，最新，计数）
        /// </summary>
        /// <param name="userProfile"></param>
        /// <param name="para"></param>
        /// <param name="listDesc"></param>
        /// <param name="listData"></param>
        protected void ProcStatisFieldInfo(FormUserProfile userProfile, SqlBuilderParameter para, ListDesc listDesc, List<Dictionary<string, object>> listData)
        {
            Dictionary<string, object> statis = new Dictionary<string, object>();
            listDesc.UserData = statis;

            if (userProfile == null || userProfile.ListLayout == null || userProfile.ListLayout.Count <= 0) return;

            //用户设置的统计字段
            var sumFldKeys = userProfile.ListLayout.Where(o => o.AggreateStyle == 1).Select(o => o.Id).ToList();
            var avgFldKeys = userProfile.ListLayout.Where(o => o.AggreateStyle == 2).Select(o => o.Id).ToList();
            var maxFldKeys = userProfile.ListLayout.Where(o => o.AggreateStyle == 3).Select(o => o.Id).ToList();
            var minFldKeys = userProfile.ListLayout.Where(o => o.AggreateStyle == 4).Select(o => o.Id).ToList();
            var countFldKeys = userProfile.ListLayout.Where(o => o.AggreateStyle == 5).Select(o => o.Id).ToList();
            foreach (var fldKey in sumFldKeys) statis[fldKey] = 0m;
            foreach (var fldKey in avgFldKeys) statis[fldKey] = 0m;
            foreach (var fldKey in maxFldKeys) statis[fldKey] = 0m;
            foreach (var fldKey in minFldKeys) statis[fldKey] = 0m;
            foreach (var fldKey in countFldKeys) statis[fldKey] = 0m;

            if (sumFldKeys.Count > 0 || avgFldKeys.Count > 0)
            {
                foreach (var data in listData)
                {
                    //合计
                    foreach (var fldKey in sumFldKeys)
                    {
                        object value;
                        if (data.TryGetValue(fldKey, out value))
                        {
                            decimal temp = 0;
                            if (decimal.TryParse(Convert.ToString(value), out temp))
                            {
                                statis[fldKey] = (decimal)statis[fldKey] + temp;
                            }
                        }
                    }
                    //先合计
                    foreach (var fldKey in avgFldKeys)
                    {
                        object value;
                        if (data.TryGetValue(fldKey, out value))
                        {
                            decimal temp = 0;
                            if (decimal.TryParse(Convert.ToString(value), out temp))
                            {
                                statis[fldKey] = (decimal)statis[fldKey] + temp;
                            }
                        }
                    }
                }
                //再平均
                foreach (var fldKey in avgFldKeys)
                {
                    var sum = Convert.ToDecimal(statis[fldKey]);
                    if (sum > 0) statis[fldKey] = sum / listData.Count;
                }
            }

            //最大
            if (maxFldKeys.Count > 0)
            {
                foreach (var fldKey in maxFldKeys)
                {
                    var max = 0M;
                    foreach (var data in listData)
                    {
                        object value;
                        if (data.TryGetValue(fldKey, out value))
                        {
                            decimal temp = 0;
                            if (decimal.TryParse(Convert.ToString(value), out temp))
                            {
                                if (temp > max) max = temp;
                            }
                        }
                    }
                    statis[fldKey] = max;
                }
            }

            //最小
            if (minFldKeys.Count > 0)
            {
                foreach (var fldKey in minFldKeys)
                {
                    object firstValue = 0M;
                    if (listData.Count > 0 && listData[0].TryGetValue(fldKey, out firstValue))
                    {
                        decimal min = 0M;
                        if (decimal.TryParse(Convert.ToString(firstValue), out min))
                        {
                            foreach (var data in listData)
                            {
                                object value;
                                if (data.TryGetValue(fldKey, out value))
                                {
                                    decimal temp = 0;
                                    if (decimal.TryParse(Convert.ToString(value), out temp))
                                    {
                                        if (temp < min) min = temp;
                                    }
                                }
                            }
                        }
                        statis[fldKey] = min;
                    }
                }
            }

            //计数
            if (countFldKeys.Count > 0)
            {
                foreach (var fldKey in countFldKeys)
                {
                    statis[fldKey] = listDesc.Bills;
                }
            }
        }

    }
}