using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Profile;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 保存最后一次使用的特定设备
    /// </summary>
    [InjectService("savedeviceinfo")]
    public class SaveDeviceInfo : AbstractOperationService
    {
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            //获得本次要请求的设备类型，默认为电子签名板
            var deviceType = this.GetQueryOrSimpleParam<string>("deviceType", "1");

            //获得当前请求设备信息是哪个单上的哪个字段：用于定位个人用户信息，最近使用哪个设备
            var fieldKey = this.GetQueryOrSimpleParam<string>("fieldKey");

            var lastUseDeviceKey = $"lastUseDeviceId_{fieldKey}_{deviceType}";
            var userProfileService = this.Container.GetService<IUserProfile>();

            //获取传入的最后一次使用设备id，并保存至用户个性化信息中
            var lastUseDeviceId = this.GetQueryOrSimpleParam<string>("deviceId", "");

            userProfileService.SaveUserProfile(this.UserCtx, this.OperationContext.HtmlForm.Id, lastUseDeviceKey, lastUseDeviceId);

            this.OperationContext.Result.IsSuccess = true;
            
        }
    }
}
