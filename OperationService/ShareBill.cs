using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject.Shell;
using System.IO;
using JieNor.Framework.Consts;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.MetaCore.FormOp.OpData;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 分享单据
    /// </summary>
    [InjectService("sharebill")]
    public class ShareBill : AbstractOperationService
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName
        {
            get
            {
                return "分享单据";
            }
        }

        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem
        {
            get
            {
                return null;
                //return PermConst.PermssionItem_Share;
            }
        }

        /// <summary>
        /// 共享单据逻辑处理
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            Dictionary<string, IEnumerable<string>> dctPermData = new Dictionary<string, IEnumerable<string>>(StringComparer.OrdinalIgnoreCase);
            var permDataStr = this.GetQueryOrSimpleParam<string>("permdata");
            var status = this.GetQueryOrSimpleParam<Enu_BillStatus>("status");
            var isShowMenuBar = this.GetQueryOrSimpleParam<bool>("isShowMenuBar");
            var isShowBrandBar = this.GetQueryOrSimpleParam<bool>("isShowBrandBar", true);
            if (!permDataStr.IsNullOrEmptyOrWhiteSpace())
            {
                dctPermData = permDataStr.FromJson<Dictionary<string, IEnumerable<string>>>();
            }
            else
            {
                dctPermData = new Dictionary<string, IEnumerable<string>>(StringComparer.OrdinalIgnoreCase);
                //默认给当前表单所有权限
                dctPermData[this.OperationContext.HtmlForm.Id] = this.OperationContext.HtmlForm.FormPermItems.Select(o => o.Id).ToArray();
            }
            ShellUrlData<ShareBillParam> shellData = new ShellUrlData<ShareBillParam>();
            shellData.TokenId = this.UserCtx.GetTempTokenId(dctPermData);
            shellData.ActionCode = "ShellOpenForm";
            Enu_DomainType domainType = Enu_DomainType.Dynamic;
            switch (this.OperationContext.HtmlForm.ElementType)
            {
                case HtmlElementType.HtmlForm_BillForm:
                case HtmlElementType.HtmlForm_BaseForm:
                    domainType = Enu_DomainType.Bill;
                    break;
                case HtmlElementType.HtmlForm_ReportForm:
                    domainType = Enu_DomainType.Report;
                    break;
                case HtmlElementType.HtmlForm_ParameterForm:
                    domainType = Enu_DomainType.Parameter;
                    break;
            }
            shellData.ParamData = new ShareBillParam()
            {
                FormId = this.OperationContext.HtmlForm.Id,
                PkId = (dataEntities?.FirstOrDefault()?["id"] as string) ?? "",
                Status = status,
                IsShowMenuBar = isShowMenuBar,
                IsShowBrandBar = isShowBrandBar,
                DomainType = domainType
            };
            var shellService = this.Container.GetService<ISignDataService>();
            var signData = shellService.CreateShellSignData(this.UserCtx, shellData);

            if (signData == null)
            {
                throw new BusinessException("url生成签名链接失败！"); 
            }

            this.OperationContext.Result.SrvData = signData.ToUrlQueryString(
                new Uri(Path.Combine(this.GetCurrentAppServer().AbsoluteUri, "views/shell.html")));
        }

    }
}
