using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Profile;
using JieNor.Framework.Interface.Session;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 关闭表单
    /// </summary>
    [InjectService("Close")]
    public class Close : AbstractOperationService
    {
        protected override string OperationName
        {
            get
            {
                return "";
            }
        }
        protected override string PermItem
        {
            get
            {
                return null;
            }
        }

        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {

            string lastProfileInfo = "";
            if (this.OperationContext.SimpleData.TryGetValue("profileData", out lastProfileInfo))
            {
                Task createDataBase = new Task(() =>
                {
                    var formUserProfile = lastProfileInfo.FromJson<FormUserProfile>();
                    if (formUserProfile != null)
                    {
                        var profileService = this.Container.GetService<IUserProfile>();
                        profileService.SaveUserProfile(this.OperationContext.UserContext, this.OperationContext.HtmlForm.Id, formUserProfile, true);
                    }
                });
                ThreadWorker.QuequeTask(createDataBase, result =>
                {
                    if (result?.Exception != null)
                    {
                        WinEventUtil.WriteError("保存用户个性化信息的线程执行错误：", result.Exception, 2018);
                    }
                });
            }

            ReleaseLockToken();

            var pageMgr = this.OperationContext.Container.GetService<IPageManager>();
            pageMgr?.Dispose(this.UserCtx, this.OperationContext.PageId);
            this.OperationContext.Result.IsSuccess = true;

        }

        protected override void CheckPermission()
        {
            //
        }

        /// <summary>
        /// 释放锁
        /// </summary>
        private void ReleaseLockToken()
        {
            var lockToken = this.GetQueryOrSimpleParam<string>("__LockToken__", "");
            var lockBizObjId = this.GetQueryOrSimpleParam<string>("__LockBizObjId__", "");

            if (lockToken.IsNullOrEmptyOrWhiteSpace() || lockBizObjId.IsNullOrEmptyOrWhiteSpace())
            {
                lockToken = this.GetPageSessionValue("__LockToken__", "", this.OperationContext.HtmlForm.Id, this.OperationContext.PageId);
                lockBizObjId = this.GetPageSessionValue("__LockBizObjId__", "", this.OperationContext.HtmlForm.Id, this.OperationContext.PageId);

                if (lockToken.IsNullOrEmptyOrWhiteSpace() || lockBizObjId.IsNullOrEmptyOrWhiteSpace())
                {
                    return;
                }
            }

            this.BillLockService.ReleaseLock(this.UserCtx, this.OperationContext.HtmlForm.Id, lockBizObjId, lockToken);
        }
    }
}
