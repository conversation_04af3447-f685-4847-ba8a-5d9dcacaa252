using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Consts;

namespace JieNor.Framework.AppService.OperationService.Background
{
    /// <summary>
    /// 查询异步任务执行结果
    /// </summary>
    [InjectService("bg-queryresult")]
    public class QueryResult : AbstractOperationService
    {
        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem => null;

        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName => "查询异步任务结果";

        /// <summary>
        /// 查询结果
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var taskId = this.GetQueryOrSimpleParam<string>("taskid");
            var redisCache = this.Container.BeginLifetimeScope(Guid.NewGuid().ToString()).GetService<JieNor.Framework.Interface.Cache.IRedisCache>();
            var resp = redisCache.Get<DynamicDTOResponse>(RedisKeyConst.CST_BackgroundTask_QueueId_Result.Fmt(taskId));

            this.OperationContext.Result.SrvData = resp;
        }
    }
}
