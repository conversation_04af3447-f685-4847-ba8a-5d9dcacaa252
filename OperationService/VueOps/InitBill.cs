using JieNor.Framework.Consts;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System.Linq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm;

namespace JieNor.Framework.AppService.OperationService.VueOps
{
    /// <summary>
    /// 表单初始数据包操作
    /// </summary>
    [InjectService("InitBill")]
    public class InitBill : AbstractOperationService
    {
        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_View;
            }
        }

        /// <summary>
        /// 操作名称 
        /// </summary>
        protected override string OperationName => "";

        /// <summary>
        /// 处理逻辑
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            DynamicObject dataEntity = dataEntities?.FirstOrDefault();
            
            string currPkId = this.OperationContext.SelectedRows?.FirstOrDefault()?.PkValue;

            //兼容一下直接从url传过来的情况
            if (currPkId.IsEmptyPrimaryKey())
            {
                currPkId = this.GetQueryOrSimpleParam<string>("id");
            }

            if (dataEntity==null)
            {
                if (currPkId.IsEmptyPrimaryKey() == false)
                {
                    var dm = this.GetDataManager();
                    dm.InitDbContext(this.UserCtx, this.OperationContext.HtmlForm.GetDynamicObjectType(this.UserCtx));

                    var lookupmode = this.GetQueryOrSimpleParam<string>("lookupmode");

                    if (lookupmode.EqualsIgnoreCase("bynumber"))
                    {
                        var pkIdReader = this.OperationContext.UserContext.GetPkIdDataReaderWithNumber(this.OperationContext.HtmlForm, new string[] { currPkId });
                        dataEntity = dm.SelectBy(pkIdReader)
                                .OfType<DynamicObject>()
                                .FirstOrDefault();
                    }
                    else
                    {                        
                        dataEntity = dm.Select(currPkId) as DynamicObject;

                        //兼容一下传递编码取数的场景
                        if (dataEntity == null)
                        {
                            var pkIdReader = this.OperationContext.UserContext.GetPkIdDataReaderWithNumber(this.OperationContext.HtmlForm, new string[] { currPkId });
                            dataEntity = dm.SelectBy(pkIdReader)
                                    .OfType<DynamicObject>()
                                    .FirstOrDefault();
                        }
                    }
                }
                else
                {
                    dataEntity = this.OperationContext.HtmlForm.GetDynamicObjectType(this.UserCtx).CreateInstance() as DynamicObject;

                    //表单模型字段默认值计算器
                    var defCalulator = this.UserCtx.Container.GetService<IDefaultValueCalculator>();
                    defCalulator.Execute(this.UserCtx, this.OperationContext.HtmlForm, new DynamicObject[] { dataEntity });

                    //前端传递过来的默认值，比如：树形列表中的新增操作，需要将选中的树节点作为新增部门的上级部门
                    var defaultDataJson = this.GetQueryOrSimpleParam<string>("__defaultData__", "");
                    if (!defaultDataJson.IsNullOrEmptyOrWhiteSpace())
                    {
                        var pageDefaultData = defaultDataJson.FromJson<Dictionary<string, object>>() ?? new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
                        if (pageDefaultData != null && pageDefaultData.Keys.Count > 0)
                        {
                            foreach (var fieldKey in pageDefaultData.Keys)
                            {
                                if (fieldKey.IsNullOrEmptyOrWhiteSpace()) continue;
                                var field = this.OperationContext.HtmlForm.GetField(fieldKey);
                                if (field != null)
                                {
                                    field.DynamicProperty.SetValue(dataEntity, pageDefaultData[fieldKey]);
                                }
                            }
                        }
                    }
                }
            }
            if (dataEntity.IsNullOrEmpty())
            {
                throw new BusinessException($"待查看或修改的数据档案({currPkId})可能已被删除！");
            }

            this.Container.GetService<LoadReferenceObjectManager>()?.Load(this.UserCtx, this.OperationContext.HtmlForm.GetDynamicObjectType(this.UserCtx), dataEntity, false);

            var option = OperateOption.Create();
            if ((this.UserCtx.ClientType & (long)Enu_ClientType.Mobile) == (long)Enu_ClientType.Mobile && this.UserCtx.IsAuthenticated)
            {
                option.SetOptionFlag(Enu_OpFlags.AppRequest);
            }

            var uiConverter = this.Container.GetService<IUiDataConverter>();
            var billJsonData = uiConverter.CreateUIDataObject(this.UserCtx, this.OperationContext.HtmlForm, dataEntity, option);
            var billUiData = billJsonData.GetJsonValue("uiData", new JObject());
            //允许插件干预打包数据过程
            var ae = new OnCustomServiceEventArgs()
            {
                EventName = "afterCreateUIData",
                EventData = billUiData,
            };
            this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", ae);
            billUiData = ae.EventData as JObject;


            this.OperationContext.Result.SrvData = new
            {
                UiData = billUiData,
                PageId = Guid.NewGuid().ToString(),
                tagData=ae.Result??new JObject(),
            };

        }
    }
}
