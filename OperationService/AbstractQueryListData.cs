using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.QueryFilter;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Profile;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 列表数据查询实现类
    /// </summary>
    public abstract class AbstractQueryListData : AbstractOperationService
    {
        /// <summary>
        /// 列表操作上下文
        /// </summary>
        protected virtual ListOperationContext ListOperationContext
        {
            get
            {
                return this.OperationContext as ListOperationContext;
            }
        }

        /// <summary>
        /// 表单信息
        /// </summary>
        protected virtual HtmlForm QueryHtmlForm
        {
            get
            {
                return this.OperationContext?.HtmlForm;
            }
        }

        private UserContext _queryUserContent = null;
        /// <summary>
        /// 查询上下文
        /// </summary>
        protected UserContext QueryUserContent
        {
            get
            {
                if (_queryUserContent == null)
                {
                    _queryUserContent = this.UserCtx.CreateQueryDBContext(this.QueryHtmlForm);
                }

                return _queryUserContent;
            }
        }

        /// <summary>
        /// 列表数据是否压缩处理后再返回
        /// </summary>
        protected bool DataCompress
        {
            get
            {
                return this.GetQueryOrSimpleParam<int>("dataCompress") == 1;
            }
        }

        /// <summary>
        /// 处理列表查询结果数据
        /// </summary>
        /// <param name="queryInfo"></param>
        /// <param name="sqlQueryParam"></param>
        protected virtual void ProcessListResult(QueryDataInfo queryInfo, SqlBuilderParameter sqlQueryParam)
        {
            sqlQueryParam.SrcPara.TryGetValue("selectDataType", out string selectdataType);
            object srvData = new
            {
                data = queryInfo.Datas,
                dataDesc = queryInfo.DatasDesc,
                footerrow = true,
                userDataOnFooter = true,
                selectDataType = selectdataType
            };

            //列表数据是否压缩处理后再返回
            if (this.DataCompress)
            {
                //本次返回的数据列标识（前端根据此标识进行重新组装表格数据源）
                List<string> columnKeys = null;

                //本次返回的压缩精简后的数据
                List<List<object>> compressDatas = null;

                if (queryInfo.Datas != null && queryInfo.Datas.Count > 0)
                {
                    columnKeys = queryInfo.Datas.First().Keys.ToList();
                    compressDatas = new List<List<object>>(queryInfo.Datas.Count);

                    foreach (var data in queryInfo.Datas)
                    {
                        compressDatas.Add(data.Values.ToList());
                    }
                }

                srvData = new
                {
                    columnKeys = columnKeys,
                    data = compressDatas,
                    dataDesc = queryInfo.DatasDesc,
                    footerrow = true,
                    userDataOnFooter = true,
                    selectDataType = selectdataType
                };
            }

            //设置好返回给前端的数据包
            this.OperationContext.Result.SrvData = srvData;
            this.OperationContext.Result.IsSuccess = true;
        }

        /// <summary>
        /// 解析自定义过滤条件
        /// </summary>
        /// <param name="para"></param>
        private void ParseCustomFilter(SqlBuilderParameter para)
        {
            var json = this.GetQueryOrSimpleParam<string>("__customFilter__", "");
            if (json.IsNullOrEmptyOrWhiteSpace()) return;
            var joFilter = JObject.Parse(json);
            if (joFilter == null) return;

            //由业务插件解析自定义过滤条件和参数
            var e = new OnCustomServiceEventArgs()
            {
                EventName = "onParseCustomFilterParam",
                EventData = joFilter
            };
            this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", e);

            if (e.Cancel && e.Result != null)
            {
                //Item1：sql条件表达式字符串，比如：fnumber=@fnumber and fname=@fname
                //Item2：sql参数集合
                var result = e.Result as Tuple<string, List<SqlParam>>;
                if (result != null && !result.Item1.IsNullOrEmptyOrWhiteSpace())
                {
                    //拼接在 SqlBuilderParameter 的 FilterString 后面
                    if (para.FilterString.IsNullOrEmptyOrWhiteSpace())
                    {
                        para.FilterString = $"({result.Item1})";
                    }
                    else
                    {
                        para.FilterString = para.FilterString.JoinFilterString(result.Item1);
                    }

                    if (result.Item2 != null)
                    {
                        para.AddParameter(result.Item2);
                    }
                }
            }
        }

        /// <summary>
        /// 构建查询参数
        /// </summary>
        /// <returns></returns>
        protected SqlBuilderParameter BuildQueryParameter(ref string activeEntityKey)
        {
            HtmlBaseDataField lookupField = null;
            HtmlForm lookupForm = this.QueryHtmlForm;
            var lookUpPara = this.GetPageSessionValue<Tuple<string, HtmlForm>>("LookupParam", null, lookupForm.Id, this.GetParentPageId());
            if (lookUpPara == null)
            {
                lookUpPara = this.GetPageSessionValue<Tuple<string, HtmlForm>>("LookupParam", null, lookupForm.Id);
            }

            if (lookUpPara != null)
            {
                lookupField = lookUpPara.Item2?.GetField(lookUpPara.Item1) as HtmlBaseDataField;
                if (lookupField is HtmlBaseDataEntryField || lookupField is HtmlMulClassTypeDataField)
                {
                    //如果配置了视图名称，则以配置的视图为准
                    var dynTableName = lookupField.RefDynamicObjectType.GetTableName();
                    if (lookupField is HtmlBaseDataEntryField)
                    {
                        var bdEntryField = lookupField as HtmlBaseDataEntryField;
                        if (!bdEntryField.DataViewName.IsNullOrEmptyOrWhiteSpace())
                        {
                            dynTableName = bdEntryField.DataViewName;
                        }
                    }
                    if (lookupField is HtmlMulClassTypeDataField)
                    {
                        var mulClassTypeField = lookupField as HtmlMulClassTypeDataField;
                        var ctlField = lookUpPara.Item2?.GetField(mulClassTypeField?.ControlFieldKey) as HtmlMulClassTypeField;
                        if (ctlField != null)
                        {
                            dynTableName = ctlField.DataViewName;
                        }
                    }
                    var formCacheId = $"{lookupField.RefFormId}_{dynTableName}";
                    lookupForm = HtmlParser.LoadFormMetaByCacheID(this.UserCtx, lookupField.RefFormId, formCacheId, hForm =>
                    {
                        hForm.HeadEntity.TableName = dynTableName;
                    });
                }
            }

            activeEntityKey = null;
            //参数对象
            SqlBuilderParameter para = new SqlBuilderParameter(this.UserCtx, lookupForm);
            para.PageCount = this.ListOperationContext?.PageCount ?? 100;
            para.PageIndex = this.ListOperationContext?.PageIndex ?? 1;
            para.NoIsolation = this.UserCtx.Company.EqualsIgnoreCase("0");
            var refid = this.GetQueryOrSimpleParam<string>("refid");
            if (!refid.IsNullOrEmptyOrWhiteSpace())
            {
                //para.EnableDataRowACL = false;
            }

            if (lookUpPara != null)
            {
                var lookUpFormId = lookUpPara.Item2.Id;
                //获取当前参与过滤的表单信息
                var bizMeta = HtmlParser.LoadFormMetaFromCache(lookUpFormId, this.UserCtx);
                para.SrcFormId = lookUpFormId;

                if (bizMeta.ElementType == 0)
                {
                    try
                    {
                        var currentParentForm = this.OperationContext?.GetPage(this.GetParentPageId());//当前页面的父级页面
                        var rootParentForm = this.OperationContext?.GetPage(currentParentForm?.ParentPageId);//根目录的父级页面   
                                                                                                             //此时打开的页面是父子窗口的子弹窗界面
                        para.SrcFormId = rootParentForm.FormId;
                    }
                    catch (Exception)
                    {
                    }

                }
                para.SrcFldId = lookupField?.Id;
            }
            var srcPara = this.GetQueryOrSimpleParam<string>("srcPara");
            if (srcPara.IsNullOrEmptyOrWhiteSpace())
            {
                srcPara = this.GetPageSessionValue<string>("srcPara", null, lookupForm.Id, this.GetParentPageId());
            }
            para.SrcPara = srcPara.FromJson<Dictionary<string, string>>();

            //快速过滤
            var quickFilter = this.GetQueryOrSimpleParam<string>("quickfilter");
            if (!quickFilter.IsNullOrEmptyOrWhiteSpace())
            {
                var category = this.DomainType.ToString().ToLower();
                var svc = this.Container.GetService<IListQuryProfile>();
                var qf = svc.GetListQuickFilterObject(this.OperationContext.UserContext, this.OperationContext.HtmlForm.Id, quickFilter, category);
                para.SetFilter(qf);
            }

            //构建查询字段列表
            this.BuildListQuerySelectField(para, ref activeEntityKey);

            // 如果是下推/选单，根据单据转换规则获取主键
            var cvtRuleId = this.GetPageSessionValue<string>("CvtRuleId");
            if (cvtRuleId.IsNullOrEmptyOrWhiteSpace())
            {
                cvtRuleId = this.GetPageSessionValue<string>("CvtRuleId", null, lookupForm.Id, this.GetParentPageId());
            }

            if (!cvtRuleId.IsNullOrEmptyOrWhiteSpace())
            {
                var rule = this.MetaModelService.LoadConvertRule(this.UserCtx, cvtRuleId);
                if (rule != null)
                {
                    activeEntityKey = rule.ActiveEntityKey;

                    var entity = this.QueryHtmlForm.GetEntity(activeEntityKey);

                    var fieldKey = $"{activeEntityKey}.{entity.PkFieldName}";
                    if (!para.SelectedFieldKeys.Any(s => s.EqualsIgnoreCase(fieldKey)))
                    {
                        para.SelectedFieldKeys.Add(fieldKey);
                    }
                }
            }

            //过滤条件
            if (this.ListOperationContext != null && this.ListOperationContext.WhereString != null)
            {
                //将用户传递的过滤条件加一层（）
                if (this.ListOperationContext.WhereString.Count > 0)
                {
                    var firstFilter = this.ListOperationContext.WhereString[0];
                    firstFilter.LeftBracket += "(";
                    var lastFilter = this.ListOperationContext.WhereString[this.ListOperationContext.WhereString.Count - 1];
                    lastFilter.RightBracket += ")";
                }
                para.SetFilter(this.ListOperationContext?.WhereString);
            }

            //选单时的过滤条件
            var pullFilter = this.GetPageSessionValue<List<FilterRowObject>>("PullFilter", null, lookupForm.Id, this.GetParentPageId());
            if (pullFilter != null)
            {
                para.SetFilter(pullFilter);
            }

            //简单过滤字符串处理
            para.FilterString = para.FilterString.JoinFilterString(this.ListOperationContext?.FilterString);

            ////排除已经被另存过的预置数据
            //var primitiveIdField = this.QueryHtmlForm.GetField("fprimitiveid");
            //if (primitiveIdField != null)
            //{
            //    var primiteveFilter = @"not exists(select top 1 1 from ( 
            //                                                   select fprimitiveid from {0} where fprimitiveid!='' and fmainorgid='{1}' ) bcr 
            //                                       where bcr.fprimitiveid=t0.fid)".Fmt(this.QueryHtmlForm.HeadEntity.TableName, this.UserCtx.Company);
            //    para.FilterString = para.FilterString.JoinFilterString(primiteveFilter);
            //}

            //选单时单据转换规则中的 filterString
            var pullFilterString = this.GetPageSessionValue<string>("PullFilterString", "", lookupForm.Id, this.GetParentPageId());
            if (!pullFilterString.IsNullOrEmptyOrWhiteSpace())
            {
                para.FilterString = para.FilterString.JoinFilterString(pullFilterString);
            }

            //处理一些特殊基础资料的过滤控制规则
            var lookupFilterString = this.GetPageSessionValue<string>("FilterString", "", lookupForm.Id, this.GetParentPageId());
            if (!lookupFilterString.IsNullOrEmptyOrWhiteSpace())
            {
                para.FilterString = para.FilterString.JoinFilterString(lookupFilterString);
            }

            //处理主控台菜单上配置的过滤参数（只有查询的表单和当前上下文表单一致时才拼接该条件，也就是只有列表页面才拼接该条件）
            if (this.OperationContext.HtmlForm.Id.EqualsIgnoreCase(this.QueryHtmlForm.Id))
            {
                var sessionFilter = this.GetSessionValue<string>("filter");
                if (!sessionFilter.IsNullOrEmptyOrWhiteSpace())
                {
                    para.FilterString = para.FilterString.JoinFilterString(sessionFilter);
                }
            }

            //允许插件在before或begin操作中设置额外过滤条件。
            var plugInFilterString = this.GetQueryOrSimpleParam<string>("filterString");
            para.FilterString = para.FilterString.JoinFilterString(plugInFilterString ?? "");

            if (lookupForm.Id.EqualsIgnoreCase("bd_recordlist"))
            {
                var permiSvc = this.Container.GetService<Interface.UsrMgr.IPermissionService>();
                var isDevOpsUser = permiSvc.IsDevOpsUser(this.UserCtx, new MetaCore.PermData.PermAuth(this.UserCtx));
                if (!isDevOpsUser)
                {
                    para.FilterString = para.FilterString.JoinFilterString(" fisdevops='0' ");
                }
            }

            //字段排序
            if (this.ListOperationContext != null && !this.ListOperationContext.OrderByString.IsNullOrEmptyOrWhiteSpace())
            {
                para.OrderByString = this.ListOperationContext.OrderByString;
            }

            //列表统计汇总字段
            if (this.ListOperationContext != null && !this.ListOperationContext.SumExprString.IsNullOrEmptyOrWhiteSpace())
            {
                para.SumExprString = this.ListOperationContext.SumExprString;
            }

            //解析动态参数
            this.ParseDynamicParam(para);

            //解析自定义过滤条件
            this.ParseCustomFilter(para);

            //如果是单位字段，则根据所关联的物料进行过滤
            this.SetUnitFilter(para);

            //列表数据
            var listQueryBuilder = this.OperationContext.Container.GetService<IListSqlBuilder>();

            //设置数据隔离条件
            if (lookupForm.EnableDAC)
            {
                var listAccessCondition = listQueryBuilder.GetListAccessControlFilter(this.UserCtx, para.HtmlForm?.Id ?? para.FormId);
                para.SetFilter(listAccessCondition ?? new List<FilterRowObject>());
            }

            //允许插件干预查询参数
            var queryEvent = new OnCustomServiceEventArgs()
            {
                EventName = OnCustomServiceEventArgs.PrepareQueryBuilderParameter,
                EventData = para,
            };
            this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", queryEvent);
            para.ReadDirty = true;

            return para;
        }

        /// <summary>
        /// 构建列表查询字段列表
        /// </summary>
        /// <param name="para"></param>
        /// <param name="activeEntityKey"></param>
        protected virtual void BuildListQuerySelectField(SqlBuilderParameter para, ref string activeEntityKey)
        {
            HtmlSubEntryEntity subEntryEntity = null;
            HtmlEntryEntity entryEntity = null;

            //List<HtmlField> lstQueryFields = new List<HtmlField>();
            Dictionary<HtmlField, string> dctQueryFields = new Dictionary<HtmlField, string>();

            //要查询的字段：如果前端没有回传要查询的字段列表，则取模型中配置为显示的字段列表
            var isReport = this.QueryHtmlForm.ElementType == HtmlElementType.HtmlForm_ListReportForm;
            if (this.ListOperationContext?.SelectFields == null || this.ListOperationContext?.SelectFields?.Length == 0)
            {
                var fieldList = this.QueryHtmlForm.GetFieldList();
                foreach (var field in fieldList)
                {
                    if (!field.IsVisible(HtmlElementVisibleScene.List)
                        && !field.IsVisible(HtmlElementVisibleScene.ShowOrHide)) continue;
                    //非数据列
                    if (field is HtmlButtonField) continue;
                    //不是列表默认查询字段也不查询
                    if (!isReport && !field.DefaultListSelect) continue;
                    dctQueryFields[field] = field.Id;
                }
            }
            else
            {
                //非报表列表性能优化（不要强制查询所有表头字段）
                var headFields = this.QueryHtmlForm.GetFieldList()
                      .Where(x => x.Entity is HtmlHeadEntity && !x.FieldName.IsNullOrEmptyOrWhiteSpace() && ((!isReport && x.DefaultListSelect)) || isReport);

                //是否存在强制查询的字段
                var fqdFields = headFields.Where(o => o.ForceQueryData == 1);
                if (fqdFields.Any())
                {
                    foreach (var field in fqdFields)
                    {
                        dctQueryFields[field] = field.Id;
                    }
                }
                else
                {
                    foreach (var field in headFields)
                    {
                        dctQueryFields[field] = field.Id;
                    }
                }

                //取模型中配置为显示的字段列表
                foreach (var item in this.ListOperationContext.SelectFields)
                {
                    var strItems = item.Split('.');
                    var fldKey = strItems[0].Trim();

                    if (fldKey.EndsWithIgnoreCase("_txt")) fldKey = fldKey.Substring(0, fldKey.Length - 4);

                    var field = this.QueryHtmlForm.GetField(fldKey);
                    if (field is HtmlButtonField) continue;
                    if (field != null)
                    {
                        dctQueryFields[field] = item;
                    }
                }
            }

            //如果存在子明细字段 但又不存在 明细字段时，则自动添加一个明细字段
            var subEntryFields = dctQueryFields.Keys.Where(o => o.Entity is HtmlSubEntryEntity).ToList();
            if (subEntryFields?.Count > 0)
            {
                var entity = (subEntryFields.First().Entity as HtmlSubEntryEntity)?.ParentEntity;
                var entryFields = dctQueryFields.Keys.Where(o => o.EntityKey.EqualsIgnoreCase(entity?.Id)).ToList();
                if (entryFields?.Count <= 0)
                {
                    entryFields = this.QueryHtmlForm.GetEntryFieldList(entity?.Id);
                    var entityField = entryFields?.FirstOrDefault(o => o is HtmlTextField || o is HtmlDecimalField);
                    entityField = entityField ?? entryFields?.First();
                    if (entityField != null)
                    {
                        dctQueryFields[entityField] = entityField.Id;
                    }
                }
            }

            //当过滤条件中存在明细字段时，但是当前查询的字段中又不存在条件字段所属明细下的任何字段时，
            //则自动将其中的一个条件字段添加到当前查询字段列表中，避免底层SQL错误。
            var entryEntityKeyKv = new Dictionary<string, string>();
            this.ListOperationContext.WhereString?.Select(o => o.Id)?.Distinct()?.ToList()?.ForEach(fieldKey =>
            {
                var field = this.QueryHtmlForm.GetField(fieldKey);
                if (field != null && field.Entity is HtmlEntryEntity)
                {
                    entryEntityKeyKv[field.EntityKey] = field.Id;

                    // 如果是子单据体，添加单据体某中一个字段到当前查询字段列表中，避免底层SQL错误
                    if (field.Entity is HtmlSubEntryEntity)
                    {
                        var parentEntity = ((HtmlSubEntryEntity)field.Entity).ParentEntity;
                        if (!entryEntityKeyKv.ContainsKey(parentEntity.Id))
                        {
                            entryEntityKeyKv[parentEntity.Id] = this.QueryHtmlForm.GetEntryFieldList(parentEntity.Id).First().Id;
                        }
                    }
                }
            });
            para.WhereString?.Select(o => o.Id)?.Distinct()?.ToList()?.ForEach(fieldKey =>
            {
                var field = this.QueryHtmlForm.GetField(fieldKey);
                if (field != null && field.Entity is HtmlEntryEntity)
                {
                    entryEntityKeyKv[field.EntityKey] = field.Id;

                    // 如果是子单据体，添加单据体某中一个字段到当前查询字段列表中，避免底层SQL错误
                    if (field.Entity is HtmlSubEntryEntity)
                    {
                        var parentEntity = ((HtmlSubEntryEntity)field.Entity).ParentEntity;
                        if (!entryEntityKeyKv.ContainsKey(parentEntity.Id))
                        {
                            entryEntityKeyKv[parentEntity.Id] = this.QueryHtmlForm.GetEntryFieldList(parentEntity.Id).First().Id;
                        }
                    }
                }
            });
            foreach (var entryEntityKey in entryEntityKeyKv.Keys)
            {
                if (!dctQueryFields.Keys.Any(o => o.EntityKey.EqualsIgnoreCase(entryEntityKey)))
                {
                    var fieldKey = entryEntityKeyKv[entryEntityKey];
                    var field = this.QueryHtmlForm.GetField(fieldKey);
                    dctQueryFields[field] = field.Id;
                }
            }

            foreach (var field in dctQueryFields.Keys)
            {
                if (field.Entity is HtmlSubEntryEntity)
                {
                    subEntryEntity = field.Entity as HtmlSubEntryEntity;
                }
                else if (field.Entity is HtmlEntryEntity)
                {
                    entryEntity = field.Entity as HtmlEntryEntity;
                }

                List<ColumnObject> columnList = field.ToListColumn(this.UserCtx);
                foreach (var column in columnList)
                {
                    para.SelectedFieldKeys.Add(column.Id);
                }
            }
            activeEntityKey = subEntryEntity?.Id ?? entryEntity?.Id ?? "fbillhead";
        }

        /// <summary>
        /// 如果是单位字段，则根据所关联的物料进行过滤
        /// </summary>
        /// <param name="para"></param>
        protected virtual void SetUnitFilter(SqlBuilderParameter para)
        {
            //单位基础资料过滤条件（在 QuerySelector 中存放的过滤条件）
            var unitBaseFilter = this.GetPageSessionValue<Tuple<string, SqlParam>>("UnitBaseFilter", null, this.QueryHtmlForm.Id, this.GetParentPageId());
            if (unitBaseFilter != null && !unitBaseFilter.Item1.IsNullOrEmptyOrWhiteSpace() && unitBaseFilter.Item2 != null)
            {
                if (para.FilterString.IsNullOrEmptyOrWhiteSpace())
                {
                    para.FilterString = $"({unitBaseFilter.Item1})";
                }
                else
                {
                    para.FilterString = para.FilterString.JoinFilterString(unitBaseFilter.Item1);
                }
                para.AddParameter(unitBaseFilter.Item2);
            }
        }

        /// <summary>
        /// 解析动态参数
        /// </summary>
        /// <param name="para"></param>
        protected virtual void ParseDynamicParam(SqlBuilderParameter para)
        {
            if (this.ListOperationContext == null) return;

            string jsonDynamicParam = this.ListOperationContext.DynamicParam;
            if (jsonDynamicParam.IsNullOrEmptyOrWhiteSpace()) return;

            //将 Json 字符串转成对象
            DynamicQueryParam dynamicParam = JsonConvert.DeserializeObject<DynamicQueryParam>(jsonDynamicParam);
            if (dynamicParam != null && !dynamicParam.FilterString.IsNullOrEmptyOrWhiteSpace())
            {
                //拼接在 SqlBuilderParameter 的 FilterString 后面
                if (para.FilterString.IsNullOrEmptyOrWhiteSpace())
                {
                    para.FilterString = $"({dynamicParam.FilterString})";
                }
                else
                {
                    para.FilterString = para.FilterString.JoinFilterString(dynamicParam.FilterString);
                }

                //如果有传递参数
                if (dynamicParam.Params != null && dynamicParam.Params.Count > 0)
                {
                    //基础资料表单（比如：客户 ydj_customer）
                    HtmlForm fieldKeyForm = this.QueryHtmlForm;
                    //基础资料字段所在的表单（比如：订货单 ydj_order）
                    HtmlForm parentHtmlForm = this.MetaModelService.LoadFormModel(this.UserCtx, dynamicParam.FormId);
                    if (parentHtmlForm == null || fieldKeyForm == null) return;

                    foreach (var param in dynamicParam.Params)
                    {
                        //先在基础资料自己的表单中查找（比如：客户 ydj_customer）
                        HtmlField field = fieldKeyForm.GetField(param.FieldId);
                        if (field == null)
                        {
                            //如果上面没有找到，再去基础资料字段所在的表单中查找（比如：订货单 ydj_order）
                            field = parentHtmlForm.GetField(param.FieldId);
                        }
                        var dbType = DbType.String;
                        if (field != null) dbType = field.ElementType.ToDbType();

                        //添加参数
                        para.AddParameter(new SqlParam("@" + param.FieldId, dbType, param.PValue));
                    }
                }
            }
        }
    }
}
