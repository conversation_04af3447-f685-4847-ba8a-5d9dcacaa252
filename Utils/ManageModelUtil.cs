using System;
using System.Linq;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.Framework.Interface.Utils
{
    /// <summary>
    /// 设置表单数据包的经营模式
    /// </summary>
    public class ManageModelUtil
    {
        /// <summary>
        /// 设置表单里的经营模式字段
        /// </summary>
        /// <param name="ctx">用户上下文</param>
        /// <param name="htmlForm">表单</param>
        /// <param name="dataEntities">用户数据包</param>
        public static void SetDataEntityManageModel(UserContext ctx, HtmlForm htmlForm, DynamicObject[] dataEntities)
        {
            if (htmlForm != null)
            {
                var manageModelField = htmlForm.GetField("fmanagemodel");
                //经营模式
                if (manageModelField != null)
                {
                    if (dataEntities != null && dataEntities.Any())
                    {
                        foreach (var dataEntity in dataEntities)
                        {
                            var dataEntityDynamicObjectType = dataEntity.DynamicObjectType;
                            //这个只有单据头有
                            if ("fbillhead".EqualsIgnoreCase(dataEntityDynamicObjectType.ExtendName))
                            {
                                var manageModel = Convert.ToString(dataEntity["fmanagemodel"]);
                                if (manageModel.IsNullOrEmptyOrWhiteSpace())
                                {
                                    if (ctx.IsDirectSale)
                                    {
                                        dataEntity["fmanagemodel"] = "1";
                                    }
                                    else
                                    {
                                        dataEntity["fmanagemodel"] = "0";
                                    }
                                }
                            }
                            
                        }
                    }
                }
            }
        }
    }
}