using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// Http服务异步调用
    /// </summary>
    public interface IHttpServiceInvokerAsync
    {
        /// <summary>
        /// 向特定目标发起网际请求
        /// </summary>
        /// <typeparam name="TReq"></typeparam>
        /// <param name="ctx"></param>
        /// <param name="target"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<object> InvokeAsync<TReq>(UserContext ctx, TargetSEP target, TReq request) where TReq : DynamicDTOWrapper;

        /// <summary>
        /// 向指定服务器发送请求
        /// </summary>
        /// <typeparam name="TReq"></typeparam>
        /// <param name="ctx"></param>
        /// <param name="targetServer"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<object> InvokeAsync<TReq>(UserContext ctx, TargetServer targetServer, TReq request) where TReq : DynamicDTOWrapper;

        /// <summary>
        /// 本机调用跨业务对象操作
        /// </summary>
        /// <typeparam name="TReq"></typeparam>
        /// <param name="ctx"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<object> InvokeLocalAsync<TReq>(UserContext ctx, object request);
    }
}
