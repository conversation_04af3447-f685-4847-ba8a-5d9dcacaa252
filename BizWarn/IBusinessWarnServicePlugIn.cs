using JieNor.Framework.MetaCore.FormOp.BizWarn.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.BizWarn
{
    /// <summary>
    /// 业务预警服务插件接口
    /// </summary>
    public interface IBusinessWarnServicePlugIn
    {
        /// <summary>
        /// 准备查询对象
        /// </summary>
        /// <param name="e"></param>
        void PrepareQueryObject(PrepareQueryObjectEventArgs e);

        /// <summary>
        /// 消息发送前事件
        /// </summary>
        /// <param name="e"></param>
        void BeforeSendMessage(BeforeSendMessageEventArgs e);

        /// <summary>
        /// 消息发送后事件
        /// </summary>
        /// <param name="e"></param>
        void AfterSendMessage(AfterSendMessageEventArgs e);
    }
}
