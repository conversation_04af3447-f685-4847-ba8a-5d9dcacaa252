using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using System.Text.RegularExpressions;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface.BizWarn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.Message;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.Framework.AppService.BizWarn
{
    /// <summary>
    /// 业务预警服务定义
    /// </summary>
    [InjectService]
    public class BusinessWarnService : IBusinessWarnService
    {
        /// <summary>
        /// 元数据服务
        /// </summary>
        [InjectProperty]
        protected IMetaModelService MetaModelService { get; set; }

        /// <summary>
        /// 数据读取服务
        /// </summary>
        [InjectProperty]
        protected IDBService DBService { get; set; }

        /// <summary>
        /// 发送消息
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="bizForm">业务表单</param>
        /// <param name="dataEntity">业务对象数据包</param>
        /// <param name="msgParam">消息参数</param>
        /// <returns>消息发送结果</returns>
        public IOperationResult SendMessage(UserContext userCtx, HtmlForm bizForm, DynamicObject dataEntity,
            SendMessageParam msgParam)
        {
            IOperationResult result = new OperationResult();

            //获取发送对象所关联的用户信息
            var users = this.GetUsersBySendObj(userCtx, bizForm, dataEntity, msgParam);
            if (users.Count <= 0) return result;

            var parseService = userCtx.Container.GetService<ITextTmplParseService>();

            //解析消息模版
            var _msgTitle = parseService.ParseFieldPlaceholder(userCtx, bizForm, dataEntity, msgParam.MsgTitle);
            var _msgContent = _msgTitle;
            if (!msgParam.MsgContent.EqualsIgnoreCase(msgParam.MsgTitle))
            {
                _msgContent = parseService.ParseFieldPlaceholder(userCtx, bizForm, dataEntity, msgParam.MsgContent);
            }

            var msgTypes = msgParam.MsgType.Split(new[] { "," }, StringSplitOptions.RemoveEmptyEntries).Select(s => s.ToLowerInvariant()).ToList();
            if (msgTypes.Count == 0) return result;

            // 默认是成功
            result.IsSuccess = true;

            // 站内消息
            if (msgTypes.Contains("ew_message001"))
            {
                result.MergeResult(SendTaskMessage(userCtx, bizForm, dataEntity, users, _msgTitle, _msgContent));
            }

            // 邮件
            if (msgTypes.Contains("ew_message002"))
            {
                // 获取所有员工的邮箱
                var emails = GetEmailsByUserIds(userCtx, users.Select(s => s.Id).ToList());
                if (emails != null && emails.Any(s => s.IsNullOrEmptyOrWhiteSpace() == false))
                {
                    var smtpSrv = userCtx.Container.GetService<ISmtpService>();
                    smtpSrv
                        .Reset()
                        .To(string.Join(",", emails.Where(s => s.IsNullOrEmptyOrWhiteSpace() == false)))
                        .SubjectEncoding(Encoding.UTF8)
                        .Subject(_msgTitle)
                        .IsBodyHtml(true)
                        .BodyEncoding(Encoding.UTF8)
                        .Body(_msgContent)
                        .Send();
                }

                result.MergeResult(new OperationResult { IsSuccess = true });
            }

            // 企业微信
            if (msgTypes.Contains("ew_message003"))
            {
                //解析企业微信跳转地址消息模版
                string _msgLinkUrl =null;
                if (!msgParam.MsgLinkUrl.IsNullOrEmptyOrWhiteSpace())
                {
                    _msgLinkUrl = parseService.ParseFieldPlaceholder(userCtx, bizForm, dataEntity, msgParam.MsgLinkUrl);
                }
                // 获取所有用户id
                var userIds = users.Select(s => s.Id);
                if (userIds.Any())
                {
                    var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();

                    var option = new Dictionary<string, object>
                    {
                        { "msgtype", _msgLinkUrl.IsNullOrEmptyOrWhiteSpace()?"text":"miniprogram_notice" },
                        { "userids", string.Join(",", userIds) },
                        { "msgtitle", _msgTitle },
                        { "msgcontent", _msgContent },
                        { "msglinkurl",_msgLinkUrl}
                    };

                    var result2 = gateway.InvokeBillOperation(userCtx, bizForm.Id, null, "sendqywxmessage", option);
                    result.MergeResult(result2);
                }
                else
                {
                    result.MergeResult(new OperationResult { IsSuccess = true });
                }
            }

            return result;
        }

        /// <summary>
        /// 发送任务消息
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="bizForm">业务表单</param>
        /// <param name="dataEntity">业务对象数据包</param>
        /// <param name="msgParam">消息参数</param>
        /// <returns>消息发送结果</returns>
        public IOperationResult SendTaskMessage(UserContext userCtx, HtmlForm bizForm, DynamicObject dataEntity, SendMessageParam msgParam)
        {
            IOperationResult result = new OperationResult();

            //获取发送对象所关联的用户信息
            var users = this.GetUsersBySendObj(userCtx, bizForm, dataEntity, msgParam);
            if (users.Count <= 0) return result;

            var parseService = userCtx.Container.GetService<ITextTmplParseService>();

            //解析消息模版
            var _msgTitle = parseService.ParseFieldPlaceholder(userCtx, bizForm, dataEntity, msgParam.MsgTitle);
            var _msgContent = _msgTitle;
            if (!msgParam.MsgContent.EqualsIgnoreCase(msgParam.MsgTitle))
            {
                _msgContent = parseService.ParseFieldPlaceholder(userCtx, bizForm, dataEntity, msgParam.MsgContent);
            }

            return SendTaskMessage(userCtx, bizForm, dataEntity, users, _msgTitle, _msgContent);
        }

        /// <summary>
        /// 发送任务消息
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="bizForm">业务表单</param>
        /// <param name="dataEntity">业务对象数据包</param>
        /// <param name="users">发送用户</param>
        /// <param name="msgTitle">消息标题</param>
        /// <param name="msgContent">消息正文</param>
        /// <returns>消息发送结果</returns>
        private IOperationResult SendTaskMessage(UserContext userCtx, HtmlForm bizForm, DynamicObject dataEntity, List<BaseDataSummary> users,
            string msgTitle, string msgContent)
        {
            IOperationResult result;
            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
            var taskForm = this.MetaModelService.LoadFormModel(userCtx, "bf_task");
            var taskType = taskForm.GetDynamicObjectType(userCtx);

            //生成业务预警消息
            List<DynamicObject> taskDynObjs = new List<DynamicObject>();
            foreach (var user in users)
            {
                var taskDynObj = taskType.CreateInstance() as DynamicObject;
                taskDynObj["ftaskname"] = msgTitle;
                taskDynObj["fnote"] = msgContent;
                taskDynObj["fexcuter"] = user.Id;
                taskDynObj["fjoiner"] = user.Id;
                taskDynObj["fjoiner_txt"] = user.Name;
                taskDynObj["ftasksource"] = bizForm.Id;
                taskDynObj["ftaskman"] = dataEntity["id"];
                taskDynObj["ftaskstatus"] = "taskstatus_01";
                taskDynObjs.Add(taskDynObj);
            }

            result = gateway.InvokeBillOperation(userCtx, taskForm.Id, taskDynObjs, "save", 
                new Dictionary<string, object>() { { "IgnoreCheckPermssion", "true"} });
            return result;
        }

        /// <summary>
        /// 获取发送对象所关联的用户信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="bizForm"></param>
        /// <param name="dataEntity"></param>
        /// <param name="msgParam"></param>
        /// <returns></returns>
        private List<BaseDataSummary> GetUsersBySendObj(UserContext userCtx, HtmlForm bizForm, DynamicObject dataEntity, SendMessageParam msgParam)
        {
            var baseFormProvider = userCtx.Container.GetService<IBaseFormProvider>();
            var metaModelService = userCtx.Container.GetService<IMetaModelService>();
            var users = new List<BaseDataSummary>();
            var staffIds = new List<string>();

            #region 发送对象（员工）
            if (!msgParam.SendObjStaff.IsNullOrEmptyOrWhiteSpace())
            {
                staffIds.AddRange(msgParam.SendObjStaff.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList());
            }
            #endregion

            #region 发送对象（角色）
            if (!msgParam.SendObjRole.IsNullOrEmptyOrWhiteSpace())
            {
                var paramNames = new List<string>();
                var paramList = new List<SqlParam>
                {
                    new SqlParam("@fmainorgid", DbType.String, userCtx.Company)
                };
                var roleIds = msgParam.SendObjRole.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                for (int i = 0; i < roleIds.Length; i++)
                {
                    if (roleIds[i].IsNullOrEmptyOrWhiteSpace()) continue;
                    var paramName = $"@froleid{i}";
                    paramNames.Add(paramName);
                    paramList.Add(new SqlParam(paramName, DbType.String, roleIds[i]));
                }
                if (paramNames.Count > 0)
                {
                    var sqlText = $@"
                    select distinct u.fid,u.fname,u.fnumber from t_sec_roleuser ru 
                    inner join t_sec_user u on u.fid=ru.fuserid  
                    where u.fmainorgid=@fmainorgid and ru.froleid in({string.Join(",", paramNames)})";
                    using (var reader = this.DBService.ExecuteReader(userCtx, sqlText, paramList))
                    {
                        while (reader.Read())
                        {
                            var id = reader.GetString("fid");
                            if (id.IsNullOrEmptyOrWhiteSpace()) continue;
                            if (users.Exists(o => o.Id.EqualsIgnoreCase(id))) continue;
                            users.Add(new BaseDataSummary
                            {
                                Id = id,
                                Name = reader.GetString("fname"),
                                Number = reader.GetString("fnumber")
                            });
                        }
                    }
                }
            }
            #endregion

            #region 发送对象（变量）
            if (!msgParam.SendObjVar.IsNullOrEmptyOrWhiteSpace())
            {
                var staffFormId = baseFormProvider.GetStaffFormObject(userCtx);
                var deptFormId = baseFormProvider.GetDeptFormObject(userCtx);

                var staffs = new List<BaseDataSummary>();
                var vars = msgParam.SendObjVar.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                for (int i = 0; i < vars.Length; i++)
                {
                    var _var = vars[i];
                    if (_var.IsNullOrEmptyOrWhiteSpace()) continue;

                    // 判断是当前业务对象还是上下游的
                    var fieldKeys = _var.Split(new char[] { '$' }, StringSplitOptions.RemoveEmptyEntries);
                    var lastFieldKey = fieldKeys.Last().ToLowerInvariant();
                    #region 上游
                    if (lastFieldKey.StartsWith("up_"))
                    {
                        var formId = lastFieldKey.Substring(3);

                        var sourceTypeField = bizForm.GetField("fsourcetype") as HtmlBaseDataField;
                        // 表示没有源单类型字段，跳过
                        if (sourceTypeField == null) continue;

                        string sourceType = Convert.ToString(dataEntity["fsourcetype"]);
                        string sourceNumber = Convert.ToString(dataEntity["fsourcenumber"]);
                        // 表示当前业务对象没有上游，跳过
                        if (sourceType.IsNullOrEmptyOrWhiteSpace()) continue;

                        ExtendedDataEntitySet dataEntitySet = null;
                        HtmlForm sourceForm = null;

                        // 如果上游即为源单据，直接获取对象
                        if (formId.EqualsIgnoreCase(sourceType))
                        {
                            sourceForm = metaModelService.LoadFormModel(userCtx, sourceType);
                            var sourceFormNumberField = sourceForm.GetNumberField();
                            if (sourceFormNumberField != null)
                            {
                                var sourceDataEntity = userCtx.LoadBizDataByNo(sourceType, sourceFormNumberField.Id,
                                    new string[] { sourceNumber }).FirstOrDefault();
                                if (sourceDataEntity != null)
                                {
                                    dataEntitySet = new ExtendedDataEntitySet();
                                    dataEntitySet.Parse(userCtx, new DynamicObject[] { sourceDataEntity }, sourceForm);
                                }
                            }
                        }
                        else
                        {
                            // 如果配置里的单据标识不是上游的单据标识，再向上游找
                            while (formId.EqualsIgnoreCase(sourceType) == false)
                            {
                                sourceForm = metaModelService.LoadFormModel(userCtx, sourceType);
                                var sourceFormNumberField = sourceForm.GetNumberField();
                                if (sourceFormNumberField == null) break;

                                var sourceDataEntity = userCtx.LoadBizDataByNo(sourceType, sourceFormNumberField.Id,
                                    new string[] { sourceNumber }).FirstOrDefault();
                                if (sourceDataEntity == null) break;

                                sourceTypeField = sourceForm.GetField("fsourcetype") as HtmlBaseDataField;
                                if (sourceTypeField == null) break;

                                sourceType = Convert.ToString(sourceDataEntity["fsourcetype"]);
                                sourceNumber = Convert.ToString(sourceDataEntity["fsourcenumber"]);

                                // 判断是否当前业务对象
                                if (formId == sourceType)
                                {
                                    sourceForm = metaModelService.LoadFormModel(userCtx, sourceType);
                                    sourceFormNumberField = sourceForm.GetNumberField();
                                    if (sourceFormNumberField == null) break;
                                    sourceDataEntity = userCtx.LoadBizDataByNo(sourceType, sourceFormNumberField.Id,
                                        new string[] { sourceNumber }).FirstOrDefault();
                                    if (sourceDataEntity == null) break;

                                    dataEntitySet = new ExtendedDataEntitySet();
                                    dataEntitySet.Parse(userCtx, new DynamicObject[] { sourceDataEntity }, sourceForm);
                                    break;
                                }
                            }
                        }
                        if (dataEntitySet != null)
                        {
                            // 去掉后缀的变量，避免影响原有的方法逻辑
                            string removedSuffixVar = string.Join("$", fieldKeys.Take(fieldKeys.Length - 1));

                            LoadBySendObjVarItem(userCtx, sourceForm, removedSuffixVar, dataEntitySet, baseFormProvider, staffFormId, deptFormId, staffIds, staffs, users);
                        }

                    }
                    #endregion
                    #region 下游
                    else if (lastFieldKey.StartsWith("down_"))
                    {
                        var targetFormId = lastFieldKey.Substring(5);

                        // 找到从业务对象到formId的所有转换路径
                        var convertRulePaths = metaModelService.LoadConvertRulePaths(userCtx, bizForm.Id, targetFormId);
                        if (convertRulePaths == null || convertRulePaths.Count() == 0) continue;

                        HtmlForm targetForm = metaModelService.LoadFormModel(userCtx, targetFormId);

                        // 遍历所有转换路径
                        foreach (var path in convertRulePaths)
                        {
                            // 保存中间单据的单据编码
                            List<string> numbers = new List<string>();

                            var numberField = bizForm.GetNumberField();
                            if (numberField == null) continue;

                            string number = Convert.ToString(dataEntity[numberField.Id]);
                            if (number.IsNullOrEmptyOrWhiteSpace()) continue;

                            numbers.Add(number);

                            // 保存找到的目标实体
                            List<DynamicObject> targetDataEntities = null;

                            // 遍历路径里的转换规则
                            foreach (var rule in path)
                            {
                                // 中间目标单据
                                HtmlForm midTargetForm = metaModelService.LoadFormModel(userCtx, rule.TargetFormId);
                                // 如果单据没有编码字段，跳出
                                var midTargetFormNumberField = midTargetForm.GetNumberField();
                                if (midTargetFormNumberField == null) break;

                                // 如果没有源单
                                if (midTargetForm.GetField("fsourcetype") == null) break;

                                // 找下游单据
                                var list = userCtx.LoadBizDataByFilter(midTargetForm.Id, $"fsourcetype='{rule.SourceFormId}' and fsourcenumber in ({string.Join(",", numbers.Select(s => $"'{s}'"))})");
                                // 没找到就跳出
                                if (list.Count == 0) break;

                                // 如果找到目标单据，则跳出
                                if (rule.TargetFormId.EqualsIgnoreCase(targetFormId))
                                {
                                    targetDataEntities = list;
                                    break;
                                }

                                // 保存中间目标单据的编码
                                numbers = list.Select(s => Convert.ToString(s[midTargetFormNumberField.Id]))
                                    .Where(s => s.IsNullOrEmptyOrWhiteSpace() == false).ToList();
                            }

                            // 如果有找到目标实体，则获取用户
                            if (targetDataEntities != null && targetDataEntities.Count > 0)
                            {
                                // 去掉后缀的变量，避免影响原有的方法逻辑
                                string removedSuffixVar = string.Join("$", fieldKeys.Take(fieldKeys.Length - 1));

                                var dataEntitySet = new ExtendedDataEntitySet();
                                dataEntitySet.Parse(userCtx, targetDataEntities, targetForm);
                                LoadBySendObjVarItem(userCtx, targetForm, removedSuffixVar, dataEntitySet, baseFormProvider, staffFormId, deptFormId, staffIds, staffs, users);
                            }
                        }
                    }
                    #endregion
                    #region 当前业务对象
                    else
                    {
                        ExtendedDataEntitySet dataEntitySet = new ExtendedDataEntitySet();
                        dataEntitySet.Parse(userCtx, new DynamicObject[] { dataEntity }, bizForm);

                        LoadBySendObjVarItem(userCtx, bizForm, _var, dataEntitySet, baseFormProvider, staffFormId, deptFormId, staffIds, staffs, users);
                    }
                    #endregion
                }
                foreach (var staff in staffs)
                {
                    if (!staff.Id.IsNullOrEmptyOrWhiteSpace() && !staffIds.Contains(staff.Id))
                    {
                        staffIds.Add(staff.Id);
                    }
                }
            }
            #endregion

            if (staffIds.Count > 0)
            {
                var _users = baseFormProvider.GetStaffUsers(userCtx, staffIds);
                foreach (var _user in _users)
                {
                    if (users.Exists(o => o.Id.EqualsIgnoreCase(_user.Id))) continue;
                    users.Add(_user);
                }
            }
            return users;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="bizForm"></param>
        /// <param name="_var"></param>
        /// <param name="dataEntitySet"></param>
        /// <param name="baseFormProvider"></param>
        /// <param name="staffFormId"></param>
        /// <param name="deptFormId"></param>
        /// <param name="staffIds"></param>
        /// <param name="staffs"></param>
        /// <param name="users"></param>
        private void LoadBySendObjVarItem(UserContext userCtx, HtmlForm bizForm, string _var,
            ExtendedDataEntitySet dataEntitySet, IBaseFormProvider baseFormProvider, string staffFormId, string deptFormId,
            List<string> staffIds, List<BaseDataSummary> staffs, List<BaseDataSummary> users)
        {
            var fieldKeys = _var.Split(new char[] { '$' }, StringSplitOptions.RemoveEmptyEntries);
            var fieldKey = fieldKeys[0];
            var field = bizForm.GetField(fieldKey) as HtmlBaseDataField;
            if (field == null) return;

            if (field.RefFormId.EqualsIgnoreCase("sec_user"))
            {
                var _userIds = new List<string>();
                this.FindFieldValue(bizForm, dataEntitySet, fieldKey, _userIds);
                if (_userIds != null)
                {
                    if (fieldKeys.Length == 2)
                    {
                        //获取指定用户所在的部门信息
                        var _depts = baseFormProvider.GetUserDepts(userCtx, _userIds);
                        var _deptIds = _depts?.Select(o => o.Id)?.ToList();
                        if (_deptIds != null)
                        {
                            switch (fieldKeys[1].ToLower())
                            {
                                //用户所属部门负责人
                                case "deptmain":
                                    var _leaders = baseFormProvider.GetDeptLeaders(userCtx, _deptIds);
                                    if (_leaders != null) staffs.AddRange(_leaders);
                                    break;
                                //用户所属部门所有员工
                                case "deptall":
                                    var _staffs = baseFormProvider.GetDeptAllStaffs(userCtx, _deptIds);
                                    if (_staffs != null) staffs.AddRange(_staffs);
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                    else
                    {
                        //根据用户Id加载用户信息
                        this.LoadUserByUserIds(userCtx, _userIds, users);
                    }
                }
            }
            else if (field.RefFormId.EqualsIgnoreCase(staffFormId))
            {
                if (fieldKeys.Length == 2)
                {
                    var _staffIds = new List<string>();
                    this.FindFieldValue(bizForm, dataEntitySet, fieldKey, _staffIds);
                    if (_staffIds != null)
                    {
                        //获取指定员工所在的部门信息
                        var _depts = baseFormProvider.GetStaffDepts(userCtx, _staffIds);
                        var _deptIds = _depts?.Select(o => o.Id)?.ToList();
                        if (_deptIds != null)
                        {
                            switch (fieldKeys[1].ToLower())
                            {
                                //员工所属部门负责人
                                case "deptmain":
                                    var _leaders = baseFormProvider.GetDeptLeaders(userCtx, _deptIds);
                                    if (_leaders != null) staffs.AddRange(_leaders);
                                    break;
                                //员工所属部门所有员工
                                case "deptall":
                                    var _staffs = baseFormProvider.GetDeptAllStaffs(userCtx, _deptIds);
                                    if (_staffs != null) staffs.AddRange(_staffs);
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                }
                else
                {
                    this.FindFieldValue(bizForm, dataEntitySet, fieldKey, staffIds);
                }
            }
            else if (field.RefFormId.EqualsIgnoreCase(deptFormId))
            {
                if (fieldKeys.Length == 2)
                {
                    var _deptIds = new List<string>();
                    this.FindFieldValue(bizForm, dataEntitySet, fieldKey, _deptIds);
                    switch (fieldKeys[1].ToLower())
                    {
                        //部门负责人
                        case "main":
                            var _leaders = baseFormProvider.GetDeptLeaders(userCtx, _deptIds);
                            if (_leaders != null) staffs.AddRange(_leaders);
                            break;
                        //部门内所有员工
                        case "all":
                            var _staffs = baseFormProvider.GetDeptAllStaffs(userCtx, _deptIds);
                            if (_staffs != null) staffs.AddRange(_staffs);
                            break;
                        default:
                            break;
                    }
                }
            }
        }

        /// <summary>
        /// 根据用户Id加载用户信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="userIds"></param>
        /// <param name="users"></param>
        private void LoadUserByUserIds(UserContext userCtx, List<string> userIds, List<BaseDataSummary> users)
        {
            if (userIds == null || userIds.Count <= 0) return;

            var paramNames = new List<string>();
            var paramList = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, userCtx.Company)
            };
            for (int i = 0; i < userIds.Count; i++)
            {
                if (userIds[i].IsNullOrEmptyOrWhiteSpace()) continue;
                var paramName = $"@fid{i}";
                paramNames.Add(paramName);
                paramList.Add(new SqlParam(paramName, DbType.String, userIds[i]));
            }
            if (paramNames.Count <= 0) return;

            var sqlText = @"
            select fid,fnumber,fname from t_sec_user 
            where fmainorgid=@fmainorgid";
            if (userIds.Count > 1)
            {
                sqlText += $" and fid in({string.Join(",", paramNames)})";
            }
            else
            {
                sqlText += $" and fid={paramNames[0]}";
            }

            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, sqlText, paramList))
            {
                while (reader.Read())
                {
                    var id = reader.GetString("fid");
                    if (id.IsNullOrEmptyOrWhiteSpace()) continue;
                    if (users.Exists(o => o.Id.EqualsIgnoreCase(id))) continue;
                    users.Add(new BaseDataSummary
                    {
                        Id = id,
                        Name = reader.GetString("fname"),
                        Number = reader.GetString("fnumber")
                    });
                }
            }
        }

        /// <summary>
        /// 根据字段标识在指定的数据包中查找字段值（包括表体中的字段值）
        /// </summary>
        /// <param name="bizForm"></param>
        /// <param name="dataEntitySet"></param>
        /// <param name="fieldKey"></param>
        /// <param name="values"></param>
        private void FindFieldValue(HtmlForm bizForm, ExtendedDataEntitySet dataEntitySet, string fieldKey, List<string> values)
        {
            var field = bizForm.GetField(fieldKey);
            if (field == null) return;

            var extendeds = dataEntitySet.FindByEntityKey(field.EntityKey);
            if (extendeds == null) return;

            foreach (var extended in extendeds)
            {
                var value = field?.DynamicProperty?.GetValue<string>(extended.DataEntity);
                if (!value.IsNullOrEmptyOrWhiteSpace() && !values.Contains(value))
                {
                    values.Add(value);
                }
            }
        }

        /// <summary>
        /// 根据用户Id获取用户Email
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="userIds"></param>
        /// <returns></returns>
        private List<string> GetEmailsByUserIds(UserContext userCtx, List<string> userIds)
        {
            List<string> emails = new List<string>();

            if (userIds == null || userIds.Count <= 0) return emails;

            var paramNames = new List<string>();
            var paramList = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, userCtx.Company)
            };
            for (int i = 0; i < userIds.Count; i++)
            {
                if (userIds[i].IsNullOrEmptyOrWhiteSpace()) continue;
                var paramName = $"@fid{i}";
                paramNames.Add(paramName);
                paramList.Add(new SqlParam(paramName, DbType.String, userIds[i]));
            }
            if (paramNames.Count <= 0) return emails;

            var sqlText = @"
            select fid,femail from t_sec_user 
            where fmainorgid=@fmainorgid";
            if (userIds.Count > 1)
            {
                sqlText += $" and fid in({string.Join(",", paramNames)})";
            }
            else
            {
                sqlText += $" and fid={paramNames[0]}";
            }

            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, sqlText, paramList))
            {
                while (reader.Read())
                {
                    var id = reader.GetString("fid");
                    if (id.IsNullOrEmptyOrWhiteSpace()) continue;
                    emails.Add(reader.GetString("femail"));
                }
            }

            return emails;
        }
    }
}