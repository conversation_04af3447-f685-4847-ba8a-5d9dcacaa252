using JieNor.Framework.DataTransferObject.IM;
using System.Collections.Generic;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 消息分发器
    /// </summary>
    public interface IMessageDispatcher
    {
        /// <summary>
        /// 发送消息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="channelName">消息通道名称</param>
        /// <param name="message">消息对象</param>
        void PublishMessage(UserContext userCtx, string channelName, IEnumerable<IMMessage> message);

        /// <summary>
        /// 设置消息为死信
        /// </summary>
        /// <param name="container"></param>
        /// <param name="messages"></param>
        void SetDeadMessage(IServiceContainer container, IEnumerable<IMMessage> messages);
        
    }
}
