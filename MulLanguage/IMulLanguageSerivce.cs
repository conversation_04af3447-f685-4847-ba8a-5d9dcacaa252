using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.MulLanguage
{

    public interface     IMulLanguageSerivce
    {



        /// <summary>
        /// 加载通用平台的词条多语言字符（SubSystemType.Common ）
        /// </summary>
        /// <param name="mulLangTxt">汉字字符串</param>
        /// <param name="resourceID">资源ID</param>
        /// <returns></returns>
        string LoadMulLangString(string mulLangTxt, string resourceID);


        /// <summary>
        /// 加载多语言字符
        /// </summary>
        /// <param name="mulLangTxt">汉字字符串</param>
        /// <param name="resourceID">资源ID</param>
        /// <param name="subSystem">子系统标志：为避免所有资源词条放一个资源文件导致文件过大，按子系统归类存放</param>
        /// <param name="args">参数列表</param>
        /// <returns>对应语言翻译后的字符串 </returns>
        string LoadMulLangString(string mulLangTxt, string resourceID, string subSystem, params object[] args);



        /// <summary>
        /// 加载通用平台的词条多语言字符（SubSystemType.Common  %1 %2 带参数的多语言字符串）
        /// </summary>
        /// <param name="mulLangTxt">汉字字符串</param>
        /// <param name="resourceID">资源ID</param>
        /// <param name="args">参数列表</param>
        /// <returns>对应语言翻译后的字符串</returns>
        string LoadMulLangStringFmt(string mulLangTxt, string resourceID, params object[] args);


        /// <summary>
        /// 加载通用平台的词条多语言字符 （ %1 %2 带参数的多语言字符串）
        /// </summary> 
        /// <param name="subSystem">子系统标志：为避免所有资源词条放一个资源文件导致文件过大，按子系统归类存放</param>
        /// <param name="mulLangTxt">汉字字符串</param>
        /// <param name="resourceID">资源ID</param>
        /// <param name="args">参数列表</param>
        /// <returns>对应语言翻译后的字符串</returns>
        string LoadMulLangStringFmt(string mulLangTxt, string resourceID, string subSystem, params object[] args);







    }

}
