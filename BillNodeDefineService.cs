using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore;
using JieNor.Framework.Utils;

namespace JieNor.Framework.AppService
{
    [InjectService]
    public class BillNodeDefineService : IBillNodeDefineService
    {
        protected UserContext Context { get; private set; }
        protected HtmlForm HtmlForm { get; private set; }
        protected PlugInProxy<IOperationServicePlugIn> PlugInProxy { get; private set; }
        protected IServiceContainer Container { get; private set; }
        protected IMetaModelService MetaModelService { get; private set; }
        protected IDBService DBService { get; private set; }

        public void Init(UserContext userContext, HtmlForm htmlForm, PlugInProxy<IOperationServicePlugIn> plugInProxy)
        {
            this.Context = userContext;
            this.HtmlForm = htmlForm;
            this.PlugInProxy = plugInProxy;
            this.Container = userContext.Container;
            this.MetaModelService = userContext.Container.GetService<IMetaModelService>();
            this.DBService = userContext.Container.GetService<IDBService>();

            //使用本服务时，先初始化节点定义模型，不然下述各sql语句会中断。
            var billNodeDefMeta = this.MetaModelService.LoadFormModel(this.Context, "bd_billnodedefine");
            var _billnodedefineDM = this.Context.GetDefaultDataManager(billNodeDefMeta);

        }

        public void FillBillNodeDefineInfos(DynamicObject[] dataEntities)
        {
            dataEntities = dataEntities?.Where(x => false == x.DataEntityState.FromDatabase).ToArray();
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            var htmlBillNodeEntry = this.HtmlForm.EntryList.FirstOrDefault(x => x.ElementType == HtmlElementType.HtmlEntity_BillNodeEntryEntity);
            if (htmlBillNodeEntry == null)
            {
                return;
            }

            //查找业务进度表
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "bd_billnodedefine");
            var htmlEntry = htmlForm.GetEntryEntity("FEntity");

            var sql = $"select distinct e.fnodename,e.fnodetype,e.fnodeindex from {htmlForm.BillHeadTableName} t inner join {htmlEntry.TableName} e on e.fid=t.fid where t.fbizobject='{this.HtmlForm.Id}' and t.fmainorgid='{this.Context.Company}'";

            var nodeInfos = Enumerable.Empty<string>().Select(x => new { nodeName = x, nodeType = x, nodeIndex = 0 }).ToList();

            using (var dataReader = this.DBService.ExecuteReader(this.Context, sql))
            {
                while (dataReader.Read())
                {
                    nodeInfos.Add(new
                    {
                        nodeName = dataReader.GetValueToString("fnodename"),
                        nodeType = dataReader.GetValueToString("fnodetype"),
                        nodeIndex = dataReader.GetValueToInt("fnodeindex")
                    });
                }
            }

            if (nodeInfos == null || nodeInfos.Count <= 0)
            {
                return;
            }

            foreach(var dataEntity in dataEntities)
            {
                var entries = dataEntity[htmlBillNodeEntry.Id] as DynamicObjectCollection;
                if (entries == null)
                {
                    entries = new DynamicObjectCollection(htmlBillNodeEntry.DynamicObjectType);
                    dataEntity[htmlBillNodeEntry.Id] = entries;
                }
                if (entries.Count > 0)
                {
                    continue;
                }
                foreach(var nodeInfo in nodeInfos)
                {
                    var entry = new DynamicObject(htmlBillNodeEntry.DynamicObjectType);
                    entry["fnodename"] = nodeInfo.nodeName;
                    entry["fnodetype"] = nodeInfo.nodeType;
                    entry["fnodeindex"] = nodeInfo.nodeIndex;
                    entry["fnodestatus"] = "0";
                    entries.Add(entry);
                }
            }
        }

        //public void UpdateBillNodeDefine(DynamicObject[] dataEntities, bool isClear)
        //{
        //    if (dataEntities == null || dataEntities.Length <= 0)
        //    {
        //        return;
        //    }

        //    //查找业务进度表
        //    var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "bd_billnodedefine");
        //    var htmlEntry = htmlForm.GetEntryEntity("FEntity");

        //    var sql = $"select distinct t.fbizobject,e.flinkfieldid,e.fnodename from {htmlForm.BillHeadTableName} t inner join {htmlEntry.TableName} e on e.fid=t.fid where e.flinkbizobject='{this.HtmlForm.Id}' and t.fmainorgid='{this.Context.Company}' and t.fforbidstatus='0' and e.fnodetype='1'";

        //    var datas = Enumerable.Empty<string>().Select(x => new { fbizobject = x, flinkfieldid = x, fnodename = x }).ToList();

        //    using (var dataReader = this.DBService.ExecuteReader(this.Context, sql))
        //    {
        //        while (dataReader.Read())
        //        {
        //            datas.Add(new
        //            {
        //                fbizobject = Convert.ToString(dataReader["fbizobject"]),
        //                flinkfieldid = Convert.ToString(dataReader["flinkfieldid"]),
        //                fnodename = Convert.ToString(dataReader["fnodename"])
        //            });
        //        }
        //    }

        //    datas = datas.Where(x => false == string.IsNullOrWhiteSpace(x.fbizobject) &&
        //                             false == string.IsNullOrWhiteSpace(x.fnodename))
        //                 .ToList();

        //    if (datas == null || datas.Count <= 0)
        //    {
        //        return;
        //    }

        //    var groups = datas.GroupBy(x => x.fbizobject).ToList();

        //    foreach (var group in groups)
        //    {
        //        var fbizobject = group.Key;
        //        var sourceForm = this.MetaModelService.LoadFormModel(this.Context, fbizobject);
        //        var sourceDataEntities = new List<DynamicObject>();

        //        //让插件返回源单数据
        //        var eventData = new OnCustomServiceEventArgs()
        //        {
        //            EventName = "getSourceDatas",
        //            EventData = new Dictionary<string, object>
        //                        {
        //                            { "sourceForm",sourceForm }
        //                        },
        //            DataEntities = dataEntities
        //        };
        //        this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", eventData);

        //        var eventResult = eventData.Result as Dictionary<string, object>;
        //        if (eventResult != null)
        //        {
        //            sourceDataEntities = eventResult["sourceEntities"] as List<DynamicObject>;
        //        }

        //        //如果插件没有处理源单数据，那么flinkfieldid按源单的id或编号处理
        //        if (false == eventData.Cancel)
        //        {
        //            var rules = this.MetaModelService.LoadConvertRule(this.Context, sourceForm.Id, this.HtmlForm.Id);
        //            var fromSourceFormEntities = new List<DynamicObject>();

        //            if (rules != null)
        //            {
        //                var sourceMapFormId = $"'{sourceForm.Id}'";
        //                foreach (var rule in rules)
        //                {
        //                    var fieldMapping = rule.FieldMappings;
        //                    if (fieldMapping == null)
        //                    {
        //                        continue;
        //                    }
        //                    var entities = dataEntities.Where(x => fromSourceFormEntities.Contains(x) == false).ToList();
        //                    if (entities == null || entities.Count <= 0)
        //                    {
        //                        break;
        //                    }
        //                    var dataEntitySet = new ExtendedDataEntitySet();
        //                    dataEntitySet.Parse(this.Context, entities, this.HtmlForm);
        //                    foreach (var fieldMap in fieldMapping)
        //                    {
        //                        if (string.Equals(fieldMap.SrcFieldId, sourceMapFormId, StringComparison.OrdinalIgnoreCase)==false)
        //                        {
        //                            continue;
        //                        }
        //                        var field = this.HtmlForm.GetField(fieldMap.Id);
        //                        if (field == null)
        //                        {
        //                            continue;
        //                        }
        //                        var setEntities = dataEntitySet.FindByEntityKey(field.EntityKey);
        //                        if (setEntities == null || setEntities.Length <= 0)
        //                        {
        //                            continue;
        //                        }
        //                        foreach(var setItem in setEntities)
        //                        {
        //                            var fieldValue = field.DynamicProperty.GetValue<string>(setItem.DataEntity);
        //                            if (string.Equals(fieldValue, sourceMapFormId, StringComparison.OrdinalIgnoreCase))
        //                            {
        //                                var setEntity = setItem.DataEntity;
        //                                while (setEntity.Parent != null)
        //                                {
        //                                    setEntity = setEntity.Parent as DynamicObject;
        //                                }
        //                                fromSourceFormEntities.Add(setEntity);
        //                            }
        //                        }
        //                    }
        //                }
        //            }

        //            if (fromSourceFormEntities != null && fromSourceFormEntities.Count > 0)
        //            {
        //                var dm = this.Container.GetService<IDataManager>();
        //                dm.InitDbContext(this.Context, sourceForm.GetDynamicObjectType(this.Context));

        //                foreach (var item in group)
        //                {
        //                    var flinkfieldid = string.IsNullOrWhiteSpace(item.flinkfieldid) ? "fsourcenumber" : item.flinkfieldid;
        //                    var field = this.HtmlForm.GetField(flinkfieldid);
        //                    if (field == null)
        //                    {
        //                        continue;
        //                    }
        //                    var dataEntitySet = new ExtendedDataEntitySet();
        //                    dataEntitySet.Parse(this.Context, fromSourceFormEntities, this.HtmlForm);
        //                    var setEntities = dataEntitySet.FindByEntityKey(field.EntityKey);
        //                    if (setEntities == null || setEntities.Length <= 0)
        //                    {
        //                        continue;
        //                    }
        //                    var sourceInfos = setEntities.Select(x => field.DynamicProperty.GetValue<string>(x.DataEntity))
        //                                                 .Where(x => false == string.IsNullOrWhiteSpace(x))
        //                                                 .Distinct()
        //                                                 .ToList();
        //                    if (sourceInfos == null || sourceInfos.Count <= 0)
        //                    {
        //                        continue;
        //                    }
        //                    var dataReader = this.Context.GetPkIdDataReaderWithNumber(sourceForm, sourceInfos);
        //                    var sourceEntities = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();
        //                    if (sourceEntities != null && sourceEntities.Count > 0)
        //                    {
        //                        sourceDataEntities.AddRange(sourceEntities);
        //                        continue;
        //                    }
        //                    sourceEntities = dm.Select(sourceInfos).OfType<DynamicObject>().ToList();
        //                    if (sourceEntities != null && sourceEntities.Count > 0)
        //                    {
        //                        sourceDataEntities.AddRange(sourceEntities);
        //                    }
        //                }
        //            }
        //        }

        //        if (sourceDataEntities == null || sourceDataEntities.Count <= 0)
        //        {
        //            continue;
        //        }

        //        //处理反写逻辑
        //        WriteSourceDataEntities(sourceForm, sourceDataEntities, dataEntities, group.Select(x => new KeyValuePair<string, string>(x.fnodename, x.flinkfieldid)).ToList(), isClear);
        //    }
        //}

        //protected void WriteSourceDataEntities(HtmlForm sourceForm, List<DynamicObject> sourceDataEntities, DynamicObject[] dataEntities, List<KeyValuePair<string, string>> nodeInfos, bool isClear)
        //{
        //    var htmlEntry = sourceForm.EntryList.FirstOrDefault(x => x.ElementType == HtmlElementType.HtmlEntity_BillNodeEntryEntity);
        //    if (htmlEntry == null)
        //    {
        //        return;
        //    }

        //    foreach (var sourceDataEntity in sourceEntities)
        //    {
        //        var entries = sourceDataEntity[htmlEntry.Id] as DynamicObjectCollection;
        //        if (entries == null && entries.Count <= 0)
        //        {
        //            continue;
        //        }

        //        foreach (var nodeInfo in nodeInfos)
        //        {
        //            var entry = entries.FirstOrDefault(x => string.Equals(Convert.ToString(x["fnodename"]), nodeInfo.Key));
        //            if (entry == null)
        //            {
        //                continue;
        //            }

        //            //让插件判断是否清空当前源单明细
        //            var eventData = new OnCustomServiceEventArgs()
        //            {
        //                EventName = "isClearSourceEntry",
        //                EventData = new Dictionary<string, object>
        //                        {
        //                            { "sourceForm",sourceForm },
        //                            { "sourceEntity",sourceDataEntity},
        //                            { "sourceEntry",entry},
        //                            { "fnodename",nodeInfo.Key},
        //                            { "flinkfieldid",nodeInfo.Value}
        //                        },
        //                DataEntities = dataEntities
        //            };
        //            this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", eventData);

        //            var eventResult = eventData.Result as Dictionary<string, object>;
        //            if (eventResult != null)
        //            {
        //                if (eventResult.Keys.Contains("isClear"))
        //                {
        //                    isClear = Convert.ToBoolean(eventResult["isClear"]);
        //                }
        //            }

        //            var finishTimeObj = entry["ffinishtime"];
        //            var finishTime = DateTime.MinValue;
        //            if (finishTimeObj != null)
        //            {
        //                finishTime = Convert.ToDateTime(finishTimeObj);
        //            }

        //            if (isClear)
        //            {
        //                entry["fnodestatus"] = "0";
        //                entry["ffinishtime"] = null;
        //                if (finishTime != DateTime.MinValue && finishTime != DateTime.MaxValue)
        //                {
        //                    entry["freponseuserid"] = this.Context.UserId;
        //                }
        //            }
        //            else
        //            {
        //                entry["fnodestatus"] = "1";
        //                if (finishTime == DateTime.MinValue || finishTime == DateTime.MaxValue)
        //                {
        //                    entry["freponseuserid"] = this.Context.UserId;
        //                    entry["ffinishtime"] = DateTime.Now;
        //                }
        //            }
        //        }
        //    }

        //    var dm = this.Container.GetService<IDataManager>();
        //    dm.InitDbContext(this.Context, sourceForm.GetDynamicObjectType(this.Context));

        //    var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
        //    prepareSaveDataService.PrepareDataEntity(this.Context, sourceForm, sourceEntities.ToArray(), OperateOption.Create());
        //    dm.Save(sourceEntities);
        //}

        public void UpdateBillNodeDefine(DynamicObject[] dataEntities, bool isClear)
        {
            //获取源单及同源单据
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            //查找业务进度表
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "bd_billnodedefine");
            var htmlEntry = htmlForm.GetEntryEntity("FEntity");
            var sql = $"select t.fid from {htmlForm.BillHeadTableName} t inner join {htmlEntry.TableName} e on e.fid=t.fid where e.flinkbizobject='{this.HtmlForm.Id}' and (t.fmainorgid='{this.Context.Company}' or t.fmainorgid='0') and t.fforbidstatus='0' and e.fnodetype='1'";
            var billNodeDm = this.Container.GetService<IDataManager>();
            billNodeDm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            var billNodeDataReader = this.DBService.ExecuteReader(this.Context, sql);
            var billNodeEntities = billNodeDm.SelectBy(billNodeDataReader).OfType<DynamicObject>().ToList();

            if (billNodeEntities == null || billNodeEntities.Count <= 0)
            {
                return;
            }

            foreach(var billNodeEntity in billNodeEntities)
            {
                var bizObject = Convert.ToString(billNodeEntity["fbizobject"]);
                if (string.IsNullOrWhiteSpace(bizObject))
                {
                    continue;
                }
                var sourceForm = this.MetaModelService.LoadFormModel(this.Context, bizObject);
                var sourceEntry = sourceForm.EntryList.FirstOrDefault(x => x.ElementType == HtmlElementType.HtmlEntity_BillNodeEntryEntity);
                if (sourceEntry == null)
                {
                    continue;
                }

                var sourceEntities = new List<DynamicObject>();
                var isogenyEntities = new List<DynamicObject>();

                //让插件返回源单数据及同源表单数据
                var eventData = new OnCustomServiceEventArgs()
                {
                    EventName = "getSourceDatas",
                    EventData = new Dictionary<string, object>
                                {
                                    { "sourceForm",sourceForm }
                                },
                    DataEntities = dataEntities
                };
                this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", eventData);

                var eventResult = eventData.Result as Dictionary<string, object>;
                if (eventResult != null)
                {
                    sourceEntities = eventResult["sourceEntities"] as List<DynamicObject>;
                    isogenyEntities = eventResult["isogenyEntities"] as List<DynamicObject>;
                }

                var billNodeEntries = billNodeEntity[htmlEntry.Id] as DynamicObjectCollection;
                var formEntries = billNodeEntries.Where(x => Convert.ToString(x["flinkbizobject"]).EqualsIgnoreCase(this.HtmlForm.Id))
                                                 .ToList();
                var dataEntitySet = new ExtendedDataEntitySet();
                dataEntitySet.Parse(this.Context, dataEntities, this.HtmlForm);

                var sourceDm = this.Container.GetService<IDataManager>();
                sourceDm.InitDbContext(this.Context, sourceForm.GetDynamicObjectType(this.Context));

                //如果插件没有处理源单数据，那么flinkfieldid按源单的id或编号处理
                if (false == eventData.Cancel)
                {
                    var multiValueQueryService = this.Container.GetService<IMultiValueQueryService>();
                    foreach (var formEntry in formEntries)
                    {
                        var linkField = getLinkFieldId(formEntry);
                        if (linkField == null)
                        {
                            continue;
                        }
                        var isLinkNumberField = linkField is HtmlSourceBillNoField;
                        var sourceFormField = getSourceFormField(linkField, sourceForm);

                        if(sourceFormField == null && isLinkNumberField)
                        {
                            continue;
                        }
                        var setEntities = dataEntitySet.FindByEntityKey(linkField.EntityKey);
                        if (setEntities == null || setEntities.Length <= 0)
                        {
                            continue;
                        }
                        var sourceInfos = new List<string>();
                        foreach (var setEntity in setEntities)
                        {
                            var sourceInfo = linkField.DynamicProperty.GetValue<string>(setEntity.DataEntity);
                            if (sourceInfo.IsNullOrEmptyOrWhiteSpace())
                            {
                                continue;
                            }
                            if (isLinkNumberField)
                            {
                                var sourceFormId = sourceFormField.DynamicProperty.GetValue<string>(setEntity.DataEntity);
                                if (false == sourceForm.Id.EqualsIgnoreCase(sourceFormId))
                                {
                                    continue;
                                }
                            }
                            sourceInfos.Add(sourceInfo);
                        }
                        if (sourceInfos == null || sourceInfos.Count <= 0)
                        {
                            continue;
                        }
                        
                        List<DynamicObject> sourceDbEntities = null;
                        if (isLinkNumberField)
                        {
                            var sourceDataReader = this.Context.GetPkIdDataReaderWithNumber(sourceForm, sourceInfos);
                            sourceDbEntities = sourceDm.SelectBy(sourceDataReader).OfType<DynamicObject>().ToList();
                        }
                        else
                        {
                            sourceDbEntities = sourceDm.Select(sourceInfos).OfType<DynamicObject>().ToList();
                        }
                        if (sourceDbEntities != null && sourceDbEntities.Count > 0)
                        {
                            sourceEntities.AddRange(sourceDbEntities);
                        }

                        var multiValueQueryParams = new List<SqlParam>
                        {
                            new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company)
                        };

                        var isogenyWhere = builderIsogenyWhere(sourceFormField, sourceForm);
                        var isogenyDbEntities = multiValueQueryService.Select(this.Context, 
                                                                              isogenyWhere, 
                                                                              multiValueQueryParams, 
                                                                              this.HtmlForm, 
                                                                              linkField.Id, 
                                                                              sourceInfos);
                        if (isogenyDbEntities != null && isogenyDbEntities.Count > 0)
                        {
                            isogenyEntities.AddRange(isogenyDbEntities);
                        }
                    }
                }

                if (sourceEntities == null || sourceEntities.Count <= 0)
                {
                    continue;
                }

                //反写源单信息
                writeSourceDataEntities(sourceForm, 
                                        sourceEntry, 
                                        sourceEntities, 
                                        isogenyEntities, 
                                        billNodeEntries, 
                                        formEntries, 
                                        dataEntities, 
                                        dataEntitySet, 
                                        billNodeEntity, 
                                        sourceDm, 
                                        isClear);
            }
        }

        /// <summary>
        /// 反写源单信息
        /// </summary>
        /// <param name="sourceForm"></param>
        /// <param name="sourceEntry"></param>
        /// <param name="sourceEntities"></param>
        /// <param name="isogenyEntities"></param>
        /// <param name="billNodeEntries"></param>
        /// <param name="formEntries"></param>
        /// <param name="dataEntities"></param>
        /// <param name="dataEntitySet"></param>
        /// <param name="billNodeEntity"></param>
        /// <param name="sourceDm"></param>
        /// <param name="isClear"></param>
        private void writeSourceDataEntities(HtmlForm sourceForm,
                                             HtmlEntryEntity sourceEntry,
                                             List<DynamicObject> sourceEntities,
                                             List<DynamicObject> isogenyEntities,
                                             DynamicObjectCollection billNodeEntries,
                                             List<DynamicObject> formEntries,
                                             DynamicObject[] dataEntities,
                                             ExtendedDataEntitySet dataEntitySet,
                                             DynamicObject billNodeEntity,
                                             IDataManager sourceDm,
                                             bool isClear)
        {
            var isogenyEntitySet = new ExtendedDataEntitySet();
            isogenyEntitySet.Parse(this.Context, isogenyEntities, this.HtmlForm);
            foreach (var sourceEntity in sourceEntities)
            {
                var entries = sourceEntity[sourceEntry.Id] as DynamicObjectCollection;
                if (entries == null && entries.Count <= 0)
                {
                    continue;
                }

                foreach (var formEntry in formEntries)
                {
                    var nodeName = Convert.ToString(formEntry["fnodename"]);
                    var linkField = getLinkFieldId(formEntry);
                    if (linkField == null)
                    {
                        continue;
                    }
                    var entry = entries.FirstOrDefault(x => string.Equals(Convert.ToString(x["fnodename"]), nodeName));
                    if (entry == null)
                    {
                        continue;
                    }

                    //让插件判断是否清空当前源单明细
                    var eventData = new OnCustomServiceEventArgs()
                    {
                        EventName = "isClearSourceEntry",
                        EventData = new Dictionary<string, object>
                        {
                                    { "sourceForm",sourceForm },
                                    { "sourceEntity",sourceEntity},
                                    { "sourceEntry",entry},
                                    { "fnodename", nodeName},
                                    { "flinkfieldid",linkField.Id},
                                    { "isogenyEntities", isogenyEntities}
                        },
                        DataEntities = dataEntities
                    };
                    this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", eventData);

                    if (eventData.Cancel)
                    {
                        continue;
                    }

                    //插件是否已分析同源单据的标识
                    var isParsedIsogeny = false;
                    var eventResult = eventData.Result as Dictionary<string, object>;
                    if (eventResult != null)
                    {
                        if (eventResult.Keys.Contains("isClear"))
                        {
                            isClear = Convert.ToBoolean(eventResult["isClear"]);
                            isParsedIsogeny = Convert.ToBoolean(eventResult["isParsedIsogeny"]);
                        }
                    }

                    //如果插件没有分析过同源单据，那么就要分析同源单据，如果分析不通过返回，因为还有同源单据没有操作，无需更新当前结点的业务进度
                    if (false == isParsedIsogeny && false == parseIsogenyEntities(sourceForm, sourceEntity, linkField, dataEntitySet, isogenyEntitySet))
                    {
                        return;
                    }

                    //更新当前结点的业务进度
                    var finishTimeObj = entry["ffinishtime"];
                    var finishTime = DateTime.MinValue;
                    if (finishTimeObj != null)
                    {
                        finishTime = Convert.ToDateTime(finishTimeObj);
                    }

                    if (isClear)
                    {
                        entry["fnodestatus"] = "0";
                        entry["ffinishtime"] = null;
                        if (finishTime != DateTime.MinValue && finishTime != DateTime.MaxValue)
                        {
                            entry["freponseuserid"] = this.Context.UserId;
                        }
                    }
                    else
                    {
                        entry["fnodestatus"] = "1";
                        if (finishTime == DateTime.MinValue || finishTime == DateTime.MaxValue)
                        {
                            entry["freponseuserid"] = this.Context.UserId;
                            entry["ffinishtime"] = DateTime.Now;
                        }
                    }

                    //更新表头最新业务进度
                    updateBizLastNodeInfo(sourceForm, billNodeEntity, formEntry, sourceEntity, billNodeEntries, entries, isClear);
                }
            }

            var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
            prepareSaveDataService.PrepareDataEntity(this.Context, sourceForm, sourceEntities.ToArray(), OperateOption.Create());
            sourceDm.Save(sourceEntities);
        }

        /// <summary>
        /// 更新源单最新业务进度信息
        /// </summary>
        /// <param name="sourceForm"></param>
        /// <param name="billNodeEntity"></param>
        /// <param name="billNodeEntry"></param>
        /// <param name="sourceEntity"></param>
        /// <param name="billNodeEntries"></param>
        /// <param name="sourceEntries"></param>
        /// <param name="isClear"></param>
        private void updateBizLastNodeInfo(HtmlForm sourceForm,
                                           DynamicObject billNodeEntity,
                                           DynamicObject billNodeEntry,
                                           DynamicObject sourceEntity,
                                           DynamicObjectCollection billNodeEntries, 
                                           DynamicObjectCollection sourceEntries,
                                           bool isClear)
        {
            var bizLastNodeNameFieldId = Convert.ToString(billNodeEntity["fbizlastnodename"]);
            var bizLastNodeWeigthFieldId = Convert.ToString(billNodeEntity["fbizlastnodeweight"]);
            if (string.IsNullOrWhiteSpace(bizLastNodeNameFieldId) || string.IsNullOrWhiteSpace(bizLastNodeWeigthFieldId))
            {
                return;
            }

            var bizLastNodeNameField = sourceForm.GetField(bizLastNodeNameFieldId);
            var bizLastNodeWeigthField = sourceForm.GetField(bizLastNodeWeigthFieldId);
            if (bizLastNodeNameField == null || 
                bizLastNodeWeigthField == null || 
                false == bizLastNodeNameField.IsBillHeadField || 
                false == bizLastNodeWeigthField.IsBillHeadField)
            {
                return;
            }

            //获取当前结点的权重
            var nodeWeight = Convert.ToDecimal(billNodeEntry["fnodeweight"]);
            //获取源单最新业务进度权重
            var sourceWeigth = bizLastNodeWeigthField.DynamicProperty.GetValue<dynamic>(sourceEntity);
            //如果是新增操作，且当前结点的权重比源单最新业务进度权重小，则不能更新源单最新业务进度
            //如果是清除操作，且当前结点的权重和源单最新业务进度权重不相等，则不能更新源单最新业务进度
            if ((isClear && nodeWeight != sourceWeigth) || (false == isClear && nodeWeight <= sourceWeigth))
            {
                return;
            }
            
            //获取当前结点的名称
            var nodeName = Convert.ToString(billNodeEntry["fnodename"]);
            //如果当前是追加结点操作，而不是清除结点，则直接用当前结点更新源单的最新业务进度信息
            if (false == isClear)
            {
                bizLastNodeWeigthField.DynamicProperty.SetValue(sourceEntity, nodeWeight);
                bizLastNodeNameField.DynamicProperty.SetValue(sourceEntity, nodeName);
                return;
            }

            //如果是清除结点，则不能用当前结点更新源单的最新业务进度信息，
            //而是用当前源单业务进度明细中已完成的最新的最大权重的结点更新，
            //如果没有这样的结点则源单最新业务进度清空，权重清零

            //获取源单所有已完成结点
            var sourceFinishedNodes = sourceEntries.Where(x => Convert.ToString(x["fnodestatus"]) == "1")
                                                    .ToList();
            //如果源单没有已完成的结点的名称，则清空源单的最新业务进度信息
            if (sourceFinishedNodes == null || sourceFinishedNodes.Count <= 0)
            {
                bizLastNodeWeigthField.DynamicProperty.SetValue(sourceEntity, 0);
                bizLastNodeNameField.DynamicProperty.SetValue(sourceEntity, string.Empty);
                return;
            }

            //求最高度权重的已完成结点
            var finishedNodeNames = sourceFinishedNodes.Select(x => Convert.ToString(x["fnodename"])).ToList();
            var finishedNodes = billNodeEntries.Where(x => finishedNodeNames.Contains(Convert.ToString(x["fnodename"]))).ToList();
            //如果没有已完成的结点，则清空源单的最新业务进度信息
            if (finishedNodes == null || finishedNodes.Count <= 0)
            {
                bizLastNodeWeigthField.DynamicProperty.SetValue(sourceEntity, 0);
                bizLastNodeNameField.DynamicProperty.SetValue(sourceEntity, string.Empty);
                return;
            }
            
            var maxWeight = finishedNodes.Max(x => Convert.ToDecimal(x["fnodeweight"]));
            var maxFinishedNode = finishedNodes.Where(x => Convert.ToDecimal(x["fnodeweight"]) == maxWeight)
                                               .Select(x =>
                                               {
                                                   var xNodeName = Convert.ToString(x["fnodeName"]);
                                                   var sourceNode = sourceFinishedNodes.FirstOrDefault(s => Convert.ToString(s["fnodeName"]) == xNodeName);
                                                   return new
                                                   {
                                                       nodeName = xNodeName,
                                                       nodeWeight = Convert.ToDecimal(x["fnodeweight"]),
                                                       finishTime = Convert.ToDateTime(sourceNode["ffinishtime"])
                                                   };
                                               })
                                               .OrderByDescending(x => x.finishTime)
                                               .FirstOrDefault();
            bizLastNodeWeigthField.DynamicProperty.SetValue(sourceEntity, maxFinishedNode.nodeWeight);
            bizLastNodeNameField.DynamicProperty.SetValue(sourceEntity, maxFinishedNode.nodeName);
        }

        /// <summary>
        /// 分析同源表单信息
        /// </summary>
        /// <param name="sourceForm"></param>
        /// <param name="sourceEntity"></param>
        /// <param name="linkField"></param>
        /// <param name="dataEntitySet"></param>
        /// <param name="isogenyEntitySet"></param>
        /// <returns>只有datqaEntitySet全部包含isogenyEntitySet时才返回true,否则返回false</returns>
        private bool parseIsogenyEntities(HtmlForm sourceForm,
                                          DynamicObject sourceEntity,
                                          HtmlField linkField, 
                                          ExtendedDataEntitySet dataEntitySet, 
                                          ExtendedDataEntitySet isogenyEntitySet)
        {
            var dataEntities = dataEntitySet.FindByEntityKey(linkField.EntityKey);
            var isogenyEntities = isogenyEntitySet.FindByEntityKey(linkField.EntityKey);
            var sourceFormField = getSourceFormField(linkField, sourceForm);
            var isSourceNumberField = linkField is HtmlSourceBillNoField;
            var sourceInfo = isSourceNumberField ? Convert.ToString(sourceEntity[sourceForm.NumberFldKey]) : Convert.ToString(sourceEntity["id"]);
            var dataIds = getPkValueByLinkField(dataEntities, linkField, sourceFormField, sourceForm, sourceInfo, isSourceNumberField);
            var isogenyIds= getPkValueByLinkField(isogenyEntities, linkField, sourceFormField, sourceForm, sourceInfo, isSourceNumberField);
            return isogenyIds.All(x => dataIds.Contains(x));
        }

        /// <summary>
        /// 根据源单信息获取实体主键
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="linkField"></param>
        /// <param name="sourceFormField"></param>
        /// <param name="sourceForm"></param>
        /// <param name="sourceInfo"></param>
        /// <param name="isSourceNumberField"></param>
        /// <returns></returns>
        private List<string> getPkValueByLinkField(ExtendedDataEntity[] dataEntities, 
                                                   HtmlField linkField, 
                                                   HtmlField sourceFormField, 
                                                   HtmlForm sourceForm, 
                                                   string sourceInfo, 
                                                   bool isSourceNumberField)
        {
            return dataEntities.Where(x =>
            {
                var sourceLinkValue = linkField.DynamicProperty.GetValue<string>(x.DataEntity);
                if (false == sourceInfo.EqualsIgnoreCase(sourceInfo))
                {
                    return false;
                }
                if (false == isSourceNumberField)
                {
                    return true;
                }
                if (sourceFormField == null)
                {
                    return false;
                }
                var sourceLinkFormValue = sourceFormField.DynamicProperty.GetValue<string>(x.DataEntity);
                return sourceLinkFormValue.EqualsIgnoreCase(sourceForm.Id);
            })
            .Select(x => Convert.ToString(x["id"]))
            .Distinct()
            .ToList();
        }

        /// <summary>
        /// 获取业务关联字段同级的源单类型字段
        /// </summary>
        /// <param name="linkField"></param>
        /// <returns></returns>
        private HtmlField getSourceFormField(HtmlField linkField, HtmlForm sourceForm)
        {
            var rules = this.MetaModelService.LoadConvertRule(this.Context, sourceForm.Id, this.HtmlForm.Id);
            if (rules == null || rules.Any() == false)
            {
                return null;
            }

            var sourceMapFormId = $"'{sourceForm.Id}'";
            foreach (var rule in rules)
            {
                var fieldMapping = rule.FieldMappings;
                if (fieldMapping == null)
                {
                    continue;
                }
                
                foreach (var fieldMap in fieldMapping)
                {
                    if (string.Equals(fieldMap.SrcFieldId, sourceMapFormId, StringComparison.OrdinalIgnoreCase) == false)
                    {
                        continue;
                    }
                    var field = this.HtmlForm.GetField(fieldMap.Id);
                    if (field == null || false == field.EntityKey.EqualsIgnoreCase(linkField.EntityKey))
                    {
                        continue;
                    }
                    return field;
                }
            }
            return null;
        }

        /// <summary>
        /// 获取关联业务字段
        /// </summary>
        /// <param name="formEntry"></param>
        /// <returns></returns>
        private HtmlField getLinkFieldId(DynamicObject formEntry)
        {
            var linkFieldId = Convert.ToString(formEntry["flinkfieldid"]);
            linkFieldId = string.IsNullOrWhiteSpace(linkFieldId) ? "fsourcenumber" : linkFieldId;
            return this.HtmlForm.GetField(linkFieldId);
        }

        /// <summary>
        /// 构建同源查询条件
        /// </summary>
        /// <returns></returns>
        private string builderIsogenyWhere(HtmlField sourceFormField, HtmlForm sourceForm)
        {
            var sourceFormWhere = string.Empty;
            if (sourceFormField != null)
            {
                var entity = sourceFormField.Entity;
                var alias = entity is HtmlHeadEntity ? "t" : entity is HtmlEntryEntity ? "t1" : "t2";
                sourceFormWhere = $" and {alias}.{sourceFormField.FieldName}='{sourceForm.Id}' ";
            }
            return $"fmainorgid=@fmainorgid {sourceFormWhere}";
        }
    }
}
