using System.Linq;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.Framework.AppService.SystemPlugIn.AuxProperty
{
    /// <summary>
    /// 辅助属性：删除
    /// </summary>
    [InjectService]
    [FormId("bd_auxproperty")]
    [OperationNo("Delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "bd_auxpropvaluemap");
                var dm = this.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                var dynObj = dm.SelectBy($"fauxpropid='{newData["id"]}'").OfType<DynamicObject>().FirstOrDefault();
                if (dynObj != null)
                {
                    return false;
                }
                return true;

            }).WithMessage("辅助属性已经被其他单据引用，不允许删除！"));
        }
    }
}