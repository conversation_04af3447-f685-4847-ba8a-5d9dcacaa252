using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.Framework.AppService.SystemPlugIn.AuxProperty
{
    /// <summary>
    /// 保存辅助属性映射信息
    /// </summary>
    [InjectService]
    [FormId("bd_auxpropeditor")]
    [OperationNo("savemapdata")]
    public class SaveAuxPropMapData : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 操作执行完成事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var jMapData = this.GetQueryOrSimpleParam<string>("mapdata");
            if (jMapData.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.SimpleData["opmsg"] = "没有需要保存的数据！";
                return;
            }

            var allAuxPropMapData = jMapData.FromJson<IEnumerable<AuxPropMapData>>();
            if (allAuxPropMapData == null)
            {
                this.Result.SimpleData["opmsg"] = "没有需要保存的数据！";
                return;
            }

            var pkService = this.Container.GetService<IDataEntityPkService>();

            //保存【物料-->属性】的映射关系
            var auxPropMapMeta = this.MetaModelService.LoadFormModel(this.Context, "bd_auxpropmap");
            var auxPropMapEntity = auxPropMapMeta.GetEntryEntity("fentity");
            var dm = this.Container.GetService<IDataManager>();
            foreach (var auxPropMap in allAuxPropMapData.GroupBy(o => o.MaterialId))
            {
                dm.InitDbContext(this.Context, auxPropMapMeta.GetDynamicObjectType(this.Context));
                var auxPropMapObj = dm.SelectBy($"fmaterialid='{auxPropMap.Key}' and fmainorgid='{this.Context.Company}'")
                    .OfType<DynamicObject>()
                    .FirstOrDefault();
                if (auxPropMapObj == null) auxPropMapObj = auxPropMapMeta.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;
                var auxPropMapEntryRowObjs = auxPropMapEntity.DynamicProperty.GetValue<DynamicObjectCollection>(auxPropMapObj);
                //采用覆盖式存储
                auxPropMapEntryRowObjs.Clear();
                auxPropMapObj["fmaterialid"] = auxPropMap.Key;
                foreach (var auxProp in auxPropMap.Select(o => o.AuxPropId).Distinct())
                {
                    var entryRowObj = auxPropMapEntryRowObjs.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                    entryRowObj["fauxpropid"] = auxProp;
                    auxPropMapEntryRowObjs.Add(entryRowObj);
                }
                pkService.AutoSetPrimaryKey(this.Context, auxPropMapObj, dm.DataEntityType);
                dm.Save(auxPropMapObj, null, this.Option);
            }


            //保存【物料-->属性-->属性值】的映射关系
            //var auxPropValueMapMeta = this.MetaModelService.LoadFormModel(this.Context, "bd_auxpropvaluemap");
            //var auxPropValueMapEntity = auxPropValueMapMeta.GetEntryEntity("fentity");
            //foreach (var auxPropMap in allAuxPropMapData)
            //{
            //    dm.InitDbContext(this.Context, auxPropValueMapMeta.GetDynamicObjectType(this.Context));
            //    var auxPropValueMapObj = dm.SelectBy($"fmaterialid='{auxPropMap.MaterialId}' and fauxpropid='{auxPropMap.AuxPropId}' and fmainorgid='{this.Context.Company}'")
            //        .OfType<DynamicObject>()
            //        .FirstOrDefault();
            //    if (auxPropValueMapObj == null) auxPropValueMapObj = auxPropValueMapMeta.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;
            //    var auxPropValueMapEntryRowObjs = auxPropValueMapEntity.DynamicProperty.GetValue<DynamicObjectCollection>(auxPropValueMapObj);
            //    //采用覆盖式存储
            //    //auxPropValueMapEntryRowObjs.Clear();
            //    auxPropValueMapObj["fmaterialid"] = auxPropMap.MaterialId;
            //    auxPropValueMapObj["fauxpropid"] = auxPropMap.AuxPropId;
            //    foreach (var auxProp in auxPropMap.MapValue)
            //    {
            //        var entryRowObj = auxPropValueMapEntryRowObjs.FirstOrDefault(x => Convert.ToString(x["fvalueid"]) == auxProp.ValueId);
            //        if (entryRowObj == null)
            //        {
            //            entryRowObj = auxPropValueMapEntryRowObjs.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
            //            entryRowObj["fvalueid"] = auxProp.ValueId;
            //            entryRowObj["fvaluenumber"] = auxProp.ValueNumber;
            //            entryRowObj["fvaluename"] = auxProp.ValueName;

            //            auxPropValueMapEntryRowObjs.Add(entryRowObj);
            //        }
                    
            //    }
            //    pkService.AutoSetPrimaryKey(this.Context, auxPropValueMapObj, dm.DataEntityType);
            //    dm.Save(auxPropValueMapObj, null, this.Option);
            //}


            this.Result.IsSuccess = true;
        }
    }

    internal class AuxPropMapData
    {
        public string MaterialId { get; set; }

        public string AuxPropId { get; set; }

        public List<AuxPropMapValue> MapValue { get; set; }
    }


    internal class AuxPropMapValue
    {
        /// <summary>
        /// 值内码
        /// </summary>
        public string ValueId { get; set; }
        /// <summary>
        /// 值编码
        /// </summary>
        public string ValueNumber { get; set; }
        /// <summary>
        /// 值名称
        /// </summary>
        public string ValueName { get; set; }
    }
}
