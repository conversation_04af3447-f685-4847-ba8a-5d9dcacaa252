using System;
using System.Linq;
using System.Text.RegularExpressions;

using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System.Collections.Generic;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.DataTransferObject;

namespace JieNor.Framework.AppService.SystemPlugin.OpSerConfig
{
    /// <summary>
    /// 操作服务配置保存
    /// </summary>
    [InjectService]
    [FormId("sys_opserconfig")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);
            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;
            foreach (var item in e.DataEntitys)
            {
                item["fname"] = item["id"];
            }

        }

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */

            //校验（企业ID + 业务对象 + 业务操作）不能重复
            var bizObjUniqueRule = this.Container.GetValidRuleService(HtmlElementType.HtmlValidator_BillUniqueValidation);
            if (bizObjUniqueRule != null)
            {
                bizObjUniqueRule.Initialize(this.Context, "fmainorgid,fbizobject,fbizop");
                e.Rules.Add(bizObjUniqueRule);
            }
        }
    }
}