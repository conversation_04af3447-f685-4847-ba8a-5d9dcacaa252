using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.DataTransferObject.Print;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.Framework.AppService.SystemPlugIn.OfficeTmpl
{
    /// <summary>
    /// 打印模版：保存
    /// </summary>
    [InjectService]
    [FormId("bas_officetmpl")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var currDataEntity = e.DataEntitys.FirstOrDefault();
            if (currDataEntity == null) return;

            var bizFormId = currDataEntity["fsrcformid"]?.ToString();
            var bizMeta = MetaCore.FormMeta.HtmlParser.LoadFormMetaFromCache(bizFormId, this.Context);
            var ens = bizMeta?.EntryList.Where(f => f is MetaCore.FormMeta.HtmlEntryEntity && !(f is MetaCore.FormMeta.HtmlSubEntryEntity)).ToList();
            if (ens?.Count > 0)
            {
                var beAdd = new List<MetaCore.FormMeta.HtmlEntryEntity>();
                var enFilter = e.DataEntitys[0]["fentity"] as DynamicObjectCollection;
                foreach (var item in ens)
                {
                    if (enFilter.Any(f => item.Id.ToString().EqualsIgnoreCase(f["ffilterentrykey"]?.ToString())))
                    {
                        continue;
                    }
                    beAdd.Add(item);
                }

                if (beAdd.Count > 0)
                {
                    foreach (var item in beAdd)
                    {
                        var addRow = enFilter.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                        addRow["ffilterentrykey"] = item.Id;
                        addRow["ffilterentrykey_txt"] = item.Caption;
                        addRow["ffilterentrydesc"] = item.Caption;
                        addRow["fenpapercount"] = 1;

                        enFilter.Add(addRow);
                    }
                }
            }

            //控制只能有一个默认
            var isDefault = Convert.ToBoolean(currDataEntity["fdefault"]);
            if (isDefault)
            {
                var dm = this.GetDataManager();
                dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));

                var pkIdReader = this.Context.GetPkIdDataReader(this.HtmlForm,
                    $"fmainorgid=@fmainorgid and fsrcformid=@fsrcformid and fid<>@fid",
                    new SqlParam[]
                    {
                        new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
                        new SqlParam("fsrcformid", System.Data.DbType.String, currDataEntity["fsrcformid"] as string),
                        new SqlParam("fid", System.Data.DbType.String, currDataEntity["id"] as string)
                    });

                var otherObjs = dm.SelectBy(pkIdReader).OfType<DynamicObject>();
                if (otherObjs.Any())
                {
                    foreach (var otherObj in otherObjs)
                    {
                        otherObj["fdefault"] = false;
                    }
                    dm.Save(otherObjs, null, this.Option);
                }
            }

            var printService = this.Container.GetService<IPrintService>();
            foreach (var dataEntity in e.DataEntitys)
            {
                string tmplId = Convert.ToString(dataEntity["Id"]);
                printService.ClearCache(this.Context, tmplId);
            }
        }
    }
}