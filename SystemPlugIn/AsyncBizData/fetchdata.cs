using JieNor.Framework.Consts;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System.Linq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.Serialization;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 查询待下载（同步）的数据：用于站点间的数据同步
    /// </summary>
    [InjectService("fetchdata")]
    public class FetchData : AbstractOperationService
    {
        protected override string OperationName
        {
            get
            {
                return "查询下载数据";
            }
        }
        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_View;
            }
        }


        /// <summary>
        /// 查询待下载（同步）的数据
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var dm = this.OperationContext.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserCtx, this.OperationContext.HtmlForm.GetDynamicObjectType(UserCtx));
            var datas = dm.SelectBy("").OfType<DynamicObject>().ToList();
            var ser = this.Container.GetService<IDynamicSerializer>();
            this.OperationContext.Result.SrvData = ser.ToDynamicJson(this.OperationContext.HtmlForm.GetDynamicObjectType(this.OperationContext.UserContext), datas);// Newtonsoft.Json.JsonConvert.SerializeObject(datas);
            this.OperationContext.Result.IsShowMessage = false;
        }




    }
}
