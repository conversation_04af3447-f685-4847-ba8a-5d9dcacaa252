using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataManager;
using ServiceStack;

namespace JieNor.Framework.AppService.SystemPlugIn.Company
{
    /// <summary>
    /// 我的企业：更改企业名称
    /// </summary>
    [InjectService]
    [FormId("sys_company")]
    [OperationNo("UpdateCompanyName")]
    public class UpdateCompanyName: AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            this.Result.IsSuccess = false;
            base.BeginOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            string companyNameStr = this.GetQueryOrSimpleParam<string>("companyName");
            string[] companyNames = companyNameStr?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            if (companyNames == null || companyNames.Length != e.DataEntitys.Length)
            {
                throw new BusinessException("您没有传公司新名称或新名称的个数和所传的实例个数不匹配!");
            }

            //发送给云链，并只在云链校验名称的唯一性
            var result = this.Gateway.Invoke(this.Context,
            TargetSEP.EisService,
            new CommonFormDTO()
            {
                FormId = "eis_company",
                OperationNo = "UpdateCompanyName",
                SelectedRows = e.DataEntitys.Select(x => new SelectedRow { PkValue = Convert.ToString(x["id"]) }),
                SimpleData = new Dictionary<string, string>
                {
                    { "companyName",companyNameStr}
                }
            }) as DynamicDTOResponse;

            result?.OperationResult?.ThrowIfHasError(true, "云链更名失败!");

            //发送给认证站点
            result = this.Gateway.Invoke(this.Context,
            TargetSEP.AuthService,
            new CommonFormDTO()
            {
                FormId = "auth_company",
                OperationNo = "UpdateCompanyName",
                SelectedRows = e.DataEntitys.Select(x => new SelectedRow { PkValue = Convert.ToString(x["id"]) }),
                SimpleData = new Dictionary<string, string>
                {
                    { "companyName",companyNameStr}
                }
            }) as DynamicDTOResponse;

            result?.OperationResult?.ThrowIfHasError(true, "认证站点更名失败!");


            //更新本地信息
            var companyInfos = DataCenterExtentions.GetAllCompanys(this);
            var dataCenterChange = false;
            var sessionChange = false;

            //更新当前表单
            for (var i = 0; i < e.DataEntitys.Length; i++)
            {
                var dataEntity = e.DataEntitys[i];
                var companyName = companyNames[i];
                var companyId = Convert.ToString(dataEntity["id"]);

                dataEntity[this.HtmlForm.NameFldKey] = companyName;

                if (companyInfos != null && companyInfos.Keys.Contains(companyId))
                {
                    companyInfos[companyId].CompanyName = companyName;
                    dataCenterChange = true;
                }
                var company = this.Context.Companys?.FirstOrDefault(x => x.CompanyId == companyId);
                if (company != null)
                {
                    company.CompanyName = companyName;
                    sessionChange = true;
                }
            }

            if (dataCenterChange)
            {
                DataCenterExtentions.SaveDataCenterConfig();
            }

            if (sessionChange)
            {
                HostContext.TryGetCurrentRequest()
                    ?.SaveSession(this.Context.UserSession, TimeSpan.FromMinutes(HostConfigView.Auth.SessionExpiry));
            }

            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(e.DataEntitys);
            this.Result.IsSuccess = true;
        }
    }
}
