using JieNor.Framework.DataEntity.MainFw;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Profile;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.SystemPlugIn.CommonFunctionMenu
{
    /// <summary>
    /// 保存用户对通用功能菜单的个性化配置
    /// </summary>
    [InjectService]
    [FormId("sys_cfunction")]
    [OperationNo("LoadCFMUserProfile")]
    public class LoadCFMUserProfile: AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 实现操作逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            var profileService = this.Container.GetService<IUserProfile>();
            var userProfile = profileService.LoadUserProfile<CommonFunctionMenuUserProfile>(this.Context, this.HtmlForm.Id, "other");
            var menus = userProfile?.Menus;

            if (menus != null && menus.Count>0)
            {
                var result = this.Gateway.InvokeBillOperation(this.Context, "sys_mainfw", null, "listmenu", new Dictionary<string, object>());
                result?.ThrowIfHasError(true, "加载菜单失败!");

                var bizModuleItems = result.SrvData as IEnumerable<BizModuleItem>;

                var menuIds = new List<string>();
                if (bizModuleItems != null)
                {
                    var itemIds = bizModuleItems.SelectMany(x => x.MenuGroups ?? new List<MenuGroupItem>())
                                                .SelectMany(x => x.MenuItems ?? new List<MenuEntryItem>())
                                                .Select(x => x.Id)
                                                .Where(x => false == string.IsNullOrWhiteSpace(x))
                                                .ToList();
                    if (itemIds != null)
                    {
                        menuIds.AddRange(itemIds);
                    }
                }

                userProfile.Menus = userProfile.Menus.Where(x => menuIds.Contains(x.Id)).ToList();
            }

            if (userProfile == null || userProfile.Menus==null || userProfile.Menus.Count  == 0)
            {
                var svc = this.Container.GetService<IDefaultWidgetUserProfile>();
                if (svc != null)
                {
                    userProfile = svc.GetDefaultCFMUserProfile(this.Context);
                }
            }

            if (userProfile == null)
            {
                userProfile = new CommonFunctionMenuUserProfile();
            }
            var SrcDataDic = new Dictionary<string, object> { { "profileData", userProfile } };

            var resp = this.Container.GetService<DynamicDTOResponse>();
            var userService = this.Container.TryGetService<IUserService>();
            var company = this.Context.Companys.FirstOrDefault(o => o.CompanyId.EqualsIgnoreCase(this.Context.Company));
            //验证是否子经销商
            var mainAgent = userService.GetMaingAgent(company);
            if (!mainAgent.IsNullOrEmpty()) 
            {
                SrcDataDic.Add("IsSubAgent", "1");
            }
            this.Result.SrvData = SrcDataDic;
        }
    }
}
