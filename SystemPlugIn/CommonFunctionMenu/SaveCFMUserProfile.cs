using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Profile;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.SystemPlugIn.CommonFunctionMenu
{
    /// <summary>
    /// 保存用户对通用功能菜单的个性化配置
    /// </summary>
    [InjectService]
    [FormId("sys_cfunction")]
    [OperationNo("SaveCFMUserProfile")]
    public class SaveCFMUserProfile: AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 实现操作逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            string lastProfileInfo = "";
            if (this.OperationContext.SimpleData.TryGetValue("profileData", out lastProfileInfo))
            {
                var userProfile = lastProfileInfo.FromJson<CommonFunctionMenuUserProfile>();
                if (userProfile != null)
                {
                    var profileService = this.Container.GetService<IUserProfile>();
                    profileService.SaveUserProfile(this.Context, this.HtmlForm.Id, "other", userProfile);
                }
            }
        }
    }
}
