using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;

namespace JieNor.Framework.AppService.SystemPlugIn.UserEdit
{
    /// <summary>
    /// 查看我的资料的接口插件
    /// </summary>
    [InjectService]
    [FormId("sec_user")]
    [OperationNo("view")]
    public class ViewMyUser : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 操作前提供我个资料对应的用户主键
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            var isGetMyProfile = this.GetQueryOrSimpleParam<bool>("getMyProfile", false);
            if (isGetMyProfile)
            {
                //添加查看表单的主键信息
                this.SimpleData["id"] = this.Context.UserId;
            }
        }

        /// <summary>
        /// 自定义服务事件处理
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName.ToLower())
            {
                //查看操作数据打包后事件
                case "aftercreateuidata":
                    var billData = e.EventData as JObject;
                    if (billData.IsNullOrEmpty()) return;

                    //追加企业信息进用户数据包里
                    var companyInfo = this.Context.Companys.FirstOrDefault(x => x.CompanyId.EqualsIgnoreCase(this.Context.Company));
                    billData["fcompanyname"] = companyInfo?.CompanyName ?? "";

                    //追加部门信息进用户数据包里
                    var baseObjProvider = this.Container.GetService<IBaseFormProvider>();
                    var deptInfo = baseObjProvider?.GetMyDepartment(this.Context);
                    if (deptInfo != null)
                    {
                        billData["fdeptname"] = deptInfo.Name;
                    }
                    // 关联员工
                    var staffInfo = baseObjProvider?.GetMyStaff(this.Context);
                    if (staffInfo != null)
                    {
                        billData["fstaffname"] = staffInfo.Name;
                    }
                    break;
            }
        }
    }


    [InjectService]
    [FormId("sec_user")]
    [OperationNo("refresh")]
    public class RefreshMyUser : ViewMyUser
    {
    }
}
