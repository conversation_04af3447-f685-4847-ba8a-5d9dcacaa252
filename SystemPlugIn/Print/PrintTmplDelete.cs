using System;
using System.Collections.Generic;

using Newtonsoft.Json.Linq;

using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FileServer;
using JieNor.Framework.MetaCore.FormMeta;
using System.Linq;

namespace JieNor.Framework.AppService.SystemPlugIn.Print
{
    /// <summary>
    /// 打印模板删除
    /// </summary>
    [InjectService]
    [FormId("bas_officetmplsetting")]
    [OperationNo("printmpl_delete")]
    public class PrintTmplDelete : AbstractOperationServicePlugIn
    {

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            this.Result.IsSuccess = false;
            this.Result.IsShowMessage = true;
            this.Result.MsgStyle = (int)Enu_MsgStyle.Dialog;
            if (e.DataEntitys == null || e.DataEntitys.Length == 0)
            {
                this.Result.SimpleMessage = "请选择要删除的打印模板";
                return;
            }

            var tmpls = e.DataEntitys[0]["fentity"] as DynamicObjectCollection;
            if (tmpls == null || tmpls.Count ==0)
            {
                this.Result.SimpleMessage = "请选择要删除的打印模板";
                return;
            }
            var pkids = tmpls.Where(f => (bool)f["fselect"]).ToList();
            if (pkids == null || pkids.Count == 0)
            {
                this.Result.SimpleMessage = "请选择要删除的打印模板";
                return;
            }
            
            var pkid =from p in pkids
                      select p["fpkid"]?.ToString();
            
            var meta = HtmlParser.LoadFormMetaFromCache("bas_officetmpl", this.Context);
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(Context, meta.GetDynamicObjectType(Context));
            dm.Delete(pkid)  ;

            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "打印模板删除成功";
        }
        
    }
}