using System;
using System.Collections.Generic;

using Newtonsoft.Json.Linq;

using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FileServer;
using JieNor.Framework.MetaCore.FormMeta;
using System.Linq;
using System.Net;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace JieNor.Framework.AppService.SystemPlugIn.Print
{
    /// <summary>
    /// 引入默认模板
    /// </summary>
    [InjectService]
    [FormId("bas_officetmpl")]
    [OperationNo("importusertemp")]
    public class PrintTmplImportUserTemp : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            this.Result.MsgStyle = (int)Enu_MsgStyle.Dialog;
            string linkFormId = this.GetQueryOrSimpleParam<string>("linkFormId");
            if (linkFormId.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            //打印模板
            var dmTemp = this.Container.GetService<IDataManager>();
            dmTemp.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            this.Context.UpdateMdlSchema(this.HtmlForm.Id);
            //默认模板
            var metaSelect = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "bas_selecttemp");
            var dmSelect = this.Container.GetService<IDataManager>();
            dmSelect.InitDbContext(this.Context, metaSelect.GetDynamicObjectType(this.Context));
            this.Context.UpdateMdlSchema("bas_selecttemp");

            DynamicObject temp = dmTemp.SelectBy("FSrcFormId='{0}' and ftmpltype='print' and fdefault='1'".Fmt(linkFormId)).OfType<DynamicObject>().FirstOrDefault();
            if (!temp.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.SimpleMessage = "已经存在默认模板，无需再次引入！";
                this.Result.IsSuccess = false;
                return;
            }
            DynamicObject dySelect = dmSelect.SelectBy("fworkobject='{0}' and fmainorgid='{1}'".Fmt(linkFormId, this.Context.Company)).OfType<DynamicObject>().FirstOrDefault();
            if (dySelect.IsNullOrEmptyOrWhiteSpace())
            {
                dySelect = metaSelect.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;
                dySelect["fworkobject"] = linkFormId;
            }
            //DynamicObjectCollection entitys = dySelect["fentity"] as DynamicObjectCollection;
            string dirPath = PathUtils.GetStartupPath() + @"\exceltemp";
            if (!Directory.Exists(dirPath))
            {
                Directory.CreateDirectory(dirPath);
            }
            DirectoryInfo dir = new DirectoryInfo(dirPath);
            foreach(FileInfo finfo in dir.GetFiles())
            {
                if (!finfo.Name.Contains(linkFormId))
                {
                    continue;
                }
                MD5 md5 = MD5.Create();
                FileStream stream = File.OpenRead(finfo.FullName);
                byte[] data2 = md5.ComputeHash(stream);
                string fileMd5 = GetbyteToString(data2);
                if ((dySelect["ftempfile"]?.ToString()).EqualsIgnoreCase(fileMd5))
                {
                    break;
                }
                dySelect["ftempfile"] = fileMd5;
                dySelect["ftempfile_txt"] = this.HtmlForm.Caption + "打印模板.xlsx";
                break;
                //后续可以考虑表体中放置模板，然后可以放多个模板，用户自己选择自己需要的模板
                //DynamicObject entity = entitys.FirstOrDefault(f => (f["ffile"]?.ToString()).EqualsIgnoreCase(fileMd5));
                //if (entity.IsNullOrEmptyOrWhiteSpace())
                //{
                //    entity = entitys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                //    entity["ffile"] = fileMd5;
                //    entitys.Add(entity);
                //}
            }
            var res = this.Gateway.InvokeBillOperation(this.Context, metaSelect.Id, new DynamicObject[] { dySelect }, "save", new Dictionary<string, object>() { });
            if (res?.ComplexMessage?.ErrorMessages.Count > 0)
            {
                this.Result.ComplexMessage = res.ComplexMessage;
                this.Result.SimpleMessage = res.SimpleMessage;
                return;
            }
            var action = this.Context.ShowSpecialForm(metaSelect, dySelect, false, "0", Enu_OpenStyle.Modal, Enu_DomainType.Bill);
            this.Result.HtmlActions.Add(action);
            this.Result.IsShowMessage = false;
            //Dictionary<string, object> dic = new Dictionary<string, object>();
            //dic.Add("filehash", fileMd5);
            //var res = this.Gateway.InvokeBillOperation(this.Context, "sys_mainfw", null, "uploadfile", dic);
            //if (res.IsSuccess)
            //{

            //}
        }

        private static string GetbyteToString(byte[] data)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < data.Length; i++)
            {
                sb.Append(data[i].ToString("x2"));
            }
            return sb.ToString();
        }

        private string GetTmplFileContent(string fileUrl)
        {
            try
            {
                // 设置参数
                HttpWebRequest request = WebRequest.Create(fileUrl) as HttpWebRequest;
                request.Method = "Get";
                request.ContentType = "application/vnd.ms-excel";      //给ContentType赋值
                request.Accept = "application/vnd.ms-excel";

                //发送请求并获取相应回应数据
                HttpWebResponse response = request.GetResponse() as HttpWebResponse;
                Stream responseStream = response.GetResponseStream();

                //将基础流写入内存流
                var memoryStream = new MemoryStream();
                const int bufferLength = 1024;
                byte[] buffer = new byte[bufferLength];
                int actual;
                while ((actual = responseStream.Read(buffer, 0, bufferLength)) > 0)
                {
                    memoryStream.Write(buffer, 0, actual);
                }
                memoryStream.Position = 0;

                //转换成string
                string strData = Convert.ToBase64String(memoryStream.ToArray());

                return strData;
            }
            catch (Exception ex)
            {
                this.Result.IsSuccess = false;
                Result.ComplexMessage.ErrorMessages.Add("从文件服务器加载打印模板Excel文件时出错：{0} {1}".Fmt(ex.Message, ex.StackTrace));
                return null;
            }
        }
    }
}