using System;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System.Collections.Generic;

namespace JieNor.Framework.AppService.SystemPlugIn.ApprovalFlowSet
{
    /// <summary>
    /// 审批流设置保存
    /// </summary>
    [InjectService]
    [FormId("sys_approvalflowset")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            var errorMessage = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return this.CheckIsCanDisable(newData, out errorMessage);
            }).WithMessage("{0}", (newData, oldData) => errorMessage));
        }

        /// <summary>
        /// 检查是否可以禁用审批流功能
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="errorMessage"></param>
        /// <returns></returns>
        private bool CheckIsCanDisable(DynamicObject dataEntity, out string errorMessage)
        {
            errorMessage = string.Empty;

            if (Convert.ToBoolean(dataEntity["fisenable"])) return true;

            var sqlText = $@"
            select top 1 fbizformid,fbizbillno from t_bpm_flowinstance 
            where fmainorgid=@fmainorgid and fflowstatus<>'flowinstancestatus_03'";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company)
            };

            var bizFormId = string.Empty;
            var bizBillNo = string.Empty;
            using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    bizFormId = reader.GetString("fbizformid");
                    bizBillNo = reader.GetString("fbizbillno");
                }
            }

            if (!bizFormId.IsNullOrEmptyOrWhiteSpace())
            {
                try
                {
                    var bizForm = this.MetaModelService.LoadFormModel(this.Context, bizFormId);
                    errorMessage = $"已经在{bizForm?.Caption}【{bizBillNo}】中提交了审批流，不允许禁用审批流，请将所有提交的审批流终止后再执行该操作！";
                    return false;
                }
                catch { }
            }

            return true;
        }
    }
}