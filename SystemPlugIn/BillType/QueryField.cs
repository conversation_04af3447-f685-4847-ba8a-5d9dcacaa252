using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.SystemPlugIn.BillType
{
    /// <summary>
    /// 单据类型中查询字段列表接口
    /// </summary>
    [InjectService]
    [FormId("bd_billtype")]
    [OperationNo("queryfield")]
    public class QueryField : AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName.ToLower())
            {
                case "onfilterqueryfield":
                    var eventData = e.EventData as Tuple<string, string>;
                    if (eventData == null) return;
                    var formModel = this.MetaModelService.LoadFormModel(this.Context, eventData.Item2);
                    if (formModel == null) return;
                    var allFieldList = formModel.GetFieldList();
                    e.Result = allFieldList
                        .Where(o=> o.IsVisible(DataTransferObject.HtmlElementVisibleScene.Bill))
                        .GroupBy(o => o.Entity.Caption)                        
                        .Select(o => new
                        {
                            group = o.Key.IsNullOrEmptyOrWhiteSpace() ? "基本" : o.Key,
                            fields = o.OrderBy(f => f.TabIndex)
                                .Select(f => new
                                {
                                    id = f.Id,
                                    name = f.Caption,
                                    mustInput = f.MustInput.ToString(),
                                    Lock = f.Lock,
                                    useAnyUnLock = f.UseAnyUnLock
                                }),
                        })
                        .ToArray();
                    e.Cancel = true;
                    break;
            }
        }
    }
}
