using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.Framework.AppService.SystemPlugIn.BillType
{
    /// <summary>
    /// 单据类型：禁用
    /// </summary>
    [InjectService]
    [FormId("bd_billtype")]
    [OperationNo("forbid")]
    public class Forbid : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var isPreset = Convert.ToBoolean(newData["fispreset"]);
                var isDefault = Convert.ToBoolean(newData["fisdefault"]);
                return !isPreset && !isDefault;
            }).WithMessage("编码为【{0}】的{1}是默认或者预置{1}，不允许禁用！", (billObj, propObj) => propObj["fnumber"], (billObj, propObj) => this.HtmlForm.Caption));
        }




        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0)
            {
                return;
            }

            var pubSubService = this.Container.GetService<IPubSubService>();
            pubSubService.PublishMessage<string>(DataTransferObject.Const.PubSubChannel.BillTypeChange, this.Context.Company);
        }



    }




    /// <summary>
    /// 单据类型：反禁用
    /// </summary>
    [InjectService]
    [FormId("bd_billtype")]
    [OperationNo("UnForbid")]
    public class UnForbid : AbstractOperationServicePlugIn
    {
        



        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0)
            {
                return;
            }

            var pubSubService = this.Container.GetService<IPubSubService>();
            pubSubService.PublishMessage<string>(DataTransferObject.Const.PubSubChannel.BillTypeChange, this.Context.Company);
        }



    }


}