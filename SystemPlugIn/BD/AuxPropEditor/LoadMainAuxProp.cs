using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.Framework.AppService.SystemPlugIn.BD.AuxPropEditor
{
    /// <summary>
    /// 辅助属性编辑：加载主属性关联的辅属性信息
    /// </summary>
    [InjectService]
    [FormId("bd_auxpropeditor")]
    [OperationNo("LoadMainAuxProp")]
    public class LoadMainAuxProp : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 辅助属性服务
        /// </summary>
        [InjectProperty]
        protected IAuxPropService FlexAuxPropService { get; set; }

        /// <summary>
        /// 所有辅助属性基础资料数据包
        /// </summary>
        protected IEnumerable<DynamicObject> AllAuxPropObjs { get; set; }

        /// <summary>
        /// 检测权限时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCheckPermssion(OnCheckPermssionArgs e)
        {
            base.OnCheckPermssion(e);

            e.IgnoreCheck = true;
        }

        /// <summary>
        /// 准备操作选项时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            base.OnPrepareOperationOption(e);

            e.OpCtlParam.IgnoreOpMessage = true;
        }

        /// <summary>
        /// 执行操作事务前事件，通知插件对要处理的数据进行排序等预处理
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            var materialId = this.GetQueryOrSimpleParam<string>("materialId"); //物料ID
            if (materialId.IsEmptyPrimaryKey())
            {
                this.OperationContext.Result.SimpleMessage = $"参数值 materialId 为空，请检查！";
                return;
            }

            //所有辅助属性基础资料数据包
            this.AllAuxPropObjs = this.FlexAuxPropService.LoadAllAuxProperties(this.Context);
            if (this.AllAuxPropObjs == null || !this.AllAuxPropObjs.Any()) return;

            //【材质】属性类别数据包，如果不存在则无需处理
            var auxPropObj = this.AllAuxPropObjs.FirstOrDefault(o => Convert.ToString(o["fname"]).EqualsIgnoreCase("材质"));
            if (auxPropObj == null)
            {
                //this.OperationContext.Result.SimpleMessage = $"【材质】辅助属性不存在或已被删除！";
                return;
            }

            //不是主属性，无需处理
            var mainProp = Convert.ToBoolean(auxPropObj["fmainprop"]);
            if (!mainProp) return;

            //此处的商品模型ID由业务站点提供
            var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
            var productFormId = baseFormProvider?.GetProductFormObject(this.Context);
            if (productFormId.IsNullOrEmptyOrWhiteSpace()) productFormId = "bd_material";

            var mtrlMeta = this.MetaModelService.LoadFormModel(this.Context, productFormId);
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, mtrlMeta.GetDynamicObjectType(this.Context));
            var mtrlObj = dm.Select(materialId) as DynamicObject;
            if (mtrlObj == null)
            {
                this.OperationContext.Result.SimpleMessage = $"辅助属性关联的{mtrlMeta?.Caption ?? "物料"}字段录入值已不可用（可能被删除或禁用了），无法加载辅属性信息！";
                return;
            }

            //单据上面选择辅助属性组合值集
            var auxPropValsJson = this.GetQueryOrSimpleParam<string>("auxPropVals");
            var auxPropVals = auxPropValsJson.FromJson<List<Dictionary<string, string>>>() ?? new List<Dictionary<string, string>>();

            //【材质】属性类别ID
            var auxPropId = Convert.ToString(auxPropObj?["id"]);

            //【材质】属性类别值
            var auxPropVal = auxPropVals.FirstOrDefault(o => Convert.ToString(o["fauxpropid"]).EqualsIgnoreCase(auxPropId));
            var auxPropValueId = auxPropVal?.GetValue("fvalueid");

            //始终是根据【材质】类别值匹配主辅属性约束
            var existsMainAuxPropInfo = false;
            var valueList = this.FillMainAuxPropInfo(materialId, auxPropValueId, auxPropObj, auxPropVals, out existsMainAuxPropInfo);

            //如果不存在【颜色】辅属性信息，则加载商品上维护的【颜色】属性值集
            if (!existsMainAuxPropInfo)
            {
                valueList.Clear();

                //【颜色】属性类别数据包
                var colorAuxPropObj = this.AllAuxPropObjs.FirstOrDefault(o => Convert.ToString(o["fname"]).EqualsIgnoreCase("颜色"));
                var colorAuxPropId = Convert.ToString(colorAuxPropObj?["id"]);
                if (!colorAuxPropId.IsNullOrEmptyOrWhiteSpace())
                {
                    valueList = this.GetLinkAuxPropEntryInfo(materialId, colorAuxPropId);
                }
            }

            this.OperationContext.Result.SrvData = new { valueList = valueList };
            this.OperationContext.Result.IsSuccess = true;
        }

        /// <summary>
        /// 填充主辅属性约束中设置的辅属性类别信息
        /// </summary>
        private JArray FillMainAuxPropInfo(
            string materialId,
            string auxPropValueId,
            DynamicObject auxPropObj,
            List<Dictionary<string, string>> auxPropVals,
            out bool existsMainAuxPropInfo)
        {
            existsMainAuxPropInfo = false;

            var valueList = new JArray();

            //是否存在主辅属性约束，如果存在，则将主属性关联的辅属性信息返回给前端
            var mainAuxPropInfo = this.LoadMainAuxPropInfo(auxPropObj, auxPropValueId)?.FirstOrDefault();
            if (mainAuxPropInfo == null) return valueList;

            //辅属性信息明细
            var entrys = mainAuxPropInfo["fentity"] as DynamicObjectCollection;
            var groups = entrys
                ?.Where(o => !Convert.ToBoolean(o["fisdisable"]))
                ?.GroupBy(o => Convert.ToString(o["fauxpropid"]));
            if (groups == null || !groups.Any()) return valueList;

            //例外信息明细
            var exclEntrys = mainAuxPropInfo["fexclentity"] as DynamicObjectCollection;

            //前端单据传递的辅助属性组合值
            var _auxPropSetNumber = this.BuildAuxPropValueSetNumber(auxPropVals);

            foreach (var item in groups)
            {
                //辅助属性类别ID
                var auxPropertieId = item.Key;
                //有效的辅助属性类型值集
                var auxPropValObjs = item.ToList();
                if (!auxPropValObjs.Any()) continue;

                existsMainAuxPropInfo = true;

                var entry = entrys.FirstOrDefault(o => Convert.ToString(o["fauxpropid"]).EqualsIgnoreCase(auxPropertieId));
                var auxPropertie = entry["fauxpropid_ref"] as DynamicObject;
                var auxPropId = auxPropertie["id"] as string;

                //如果当前【商品+辅助属性】存在例外信息，则需要排除掉
                var _exclVals = exclEntrys
                    ?.Where(o =>
                    {
                        //例外信息中的辅助属性组合值编码
                        var auxPropSetObj = o["fattrinfo_ref"] as DynamicObject;
                        var auxPropSetNumber = this.BuildAuxPropValueSetNumber(auxPropSetObj);
                        return Convert.ToString(o["fproductid"]).EqualsIgnoreCase(materialId)
                            && Convert.ToString(o["fauxpropid"]).EqualsIgnoreCase(auxPropId)
                            && auxPropSetNumber.EqualsIgnoreCase(_auxPropSetNumber);
                    })
                    ?.Select(o => Convert.ToString(o["frefenumvalue"]));

                foreach (var auxPropValObj in auxPropValObjs)
                {
                    //禁用的忽略掉
                    if (Convert.ToBoolean(auxPropValObj["fisdisable"])) continue;

                    var enumObj = auxPropValObj["frefenumvalue_ref"] as DynamicObject;
                    var enumId = Convert.ToString(enumObj["id"]);
                    var enumName = Convert.ToString(enumObj["fenumitem"]);

                    //在例外信息中的忽略掉
                    if (_exclVals != null && _exclVals.Contains(enumId)) continue;

                    var jValObj = new JObject();
                    jValObj["id"] = enumName; //都取名称文本值
                    jValObj["fnumber"] = enumName;
                    jValObj["fname"] = enumName;
                    jValObj["fisdefval"] = false;
                    valueList.Add(jValObj);
                }
            }

            return valueList;
        }

        /// <summary>
        /// 加载主辅属性约束信息
        /// </summary>
        /// <param name="auxPropObj"></param>
        /// <param name="auxPropValueId"></param>
        /// <returns></returns>
        private IEnumerable<DynamicObject> LoadMainAuxPropInfo(
            DynamicObject auxPropObj,
            string auxPropValueId)
        {
            //是否存在【主辅属性约束】表单，目前麦浩有这个表单模型
            HtmlForm mainAuxPropForm = null;
            try
            {
                mainAuxPropForm = this.MetaModelService.LoadFormModel(this.Context, "bas_mainauxprop");
            }
            catch { }
            if (mainAuxPropForm == null) return null;

            var mainAuxPropId = Convert.ToString(auxPropObj["id"]);

            var sqlText = $@"
            select top 1 map.fid from t_bas_mainauxprop map 
            inner join t_bd_enumdataentry ede on ede.fentryid=map.frefenumvalue and ede.fenumitem=@auxPropValueId 
            where map.fmainorgid=@fmainorgid and map.fforbidstatus='0' and map.fauxpropid='{mainAuxPropId}'";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("@auxPropValueId", System.Data.DbType.String, auxPropValueId),
            };

            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, mainAuxPropForm.GetDynamicObjectType(this.Context));

            var mainPkids = this.DBService.ExecuteDynamicObject(this.Context, sqlText, sqlParam)?.Select(o => o["fid"]);
            var dynObjs = dm.Select(mainPkids).OfType<DynamicObject>();

            //加载引用数据
            var refMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refMgr.Load(this.Context, mainAuxPropForm.GetDynamicObjectType(this.Context), dynObjs, false);

            return dynObjs;
        }

        /// <summary>
        /// 构建辅助属性组合值编码
        /// </summary>
        /// <param name="auxPropSetObj">辅助属性组合值数据包</param>
        /// <returns>辅助属性组合值编码</returns>
        private string BuildAuxPropValueSetNumber(DynamicObject auxPropSetObj)
        {
            if (this.AllAuxPropObjs == null || !this.AllAuxPropObjs.Any()) return "";

            var auxPropNumber = "";
            var entrys = (auxPropSetObj?["fentity"] as DynamicObjectCollection)?.OrderBy(o => o["fauxpropid"])?.ToList(); //按照辅助属性类别ID做升序排序;
            if (entrys != null)
            {
                //这里只匹配主属性值
                var mainAuxPropVals = new List<DynamicObject>();
                foreach (var item in entrys)
                {
                    var auxPropId = Convert.ToString(item["fauxpropid"]);
                    var auxPropObj = this.AllAuxPropObjs.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(auxPropId));
                    if (auxPropObj != null && Convert.ToBoolean(auxPropObj["fmainprop"]))
                    {
                        mainAuxPropVals.Add(item);
                    }
                }

                auxPropNumber = string.Join(",", mainAuxPropVals.Select(o =>
                string.Format("{0}:{1}", Convert.ToString(o["fauxpropid"]).Trim(), Convert.ToString(o["fvalueid"]).Trim())));
            }
            return auxPropNumber;
        }

        /// <summary>
        /// 构建辅助属性组合值编码
        /// </summary>
        /// <param name="auxPropVals">辅助属性组合值集</param>
        /// <returns>辅助属性组合值编码</returns>
        private string BuildAuxPropValueSetNumber(List<Dictionary<string, string>> auxPropVals)
        {
            if (this.AllAuxPropObjs == null || !this.AllAuxPropObjs.Any()) return "";

            var auxPropNumber = "";
            if (auxPropVals != null && auxPropVals.Any())
            {
                auxPropVals = auxPropVals.OrderBy(o => o["fauxpropid"]).ToList(); //按照辅助属性类别ID做升序排序

                //这里只匹配主属性值
                var mainAuxPropVals = new List<Dictionary<string, string>>();
                foreach (var item in auxPropVals)
                {
                    var auxPropId = Convert.ToString(item["fauxpropid"]);
                    var auxPropObj = this.AllAuxPropObjs.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(auxPropId));
                    if (auxPropObj != null && Convert.ToBoolean(auxPropObj["fmainprop"]))
                    {
                        mainAuxPropVals.Add(item);
                    }
                }

                auxPropNumber = string.Join(",", mainAuxPropVals.Select(o =>
                string.Format("{0}:{1}", Convert.ToString(o["fauxpropid"]).Trim(), Convert.ToString(o["fvalueid"]).Trim())));

            }
            return auxPropNumber;
        }

        /// <summary>
        /// 获取指定物料的属性与值之间的关联关系
        /// </summary>
        /// <param name="lMtrlId"></param>
        /// <param name="lAuxPropId"></param>
        /// <returns></returns>
        protected JArray GetLinkAuxPropEntryInfo(string lMtrlId, string lAuxPropId)
        {
            var linkAuxPropValuesObj = this.FlexAuxPropService?.LoadLinkAuxPropValuesByMaterialId(this.Context, lMtrlId, lAuxPropId);
            if (linkAuxPropValuesObj != null)
            {
                var auxPropValueObjs = linkAuxPropValuesObj["fentity"] as DynamicObjectCollection;
                if (auxPropValueObjs != null)
                {
                    JArray jValueObjs = new JArray();

                    foreach (var item in auxPropValueObjs)
                    {
                        if (Convert.ToBoolean(item["fisdisable"])) continue;

                        var isDefVal = Convert.ToBoolean(item["fisdefval"]);

                        var jValObj = new JObject();
                        jValueObjs.Add(jValObj);
                        jValObj["id"] = JToken.FromObject(item["fvalueid"] ?? "");
                        jValObj["fnumber"] = JToken.FromObject(item["fvaluenumber"] ?? "");
                        jValObj["fname"] = JToken.FromObject(item["fvaluename"] ?? "");
                        jValObj["fisdefval"] = isDefVal;
                    }

                    return jValueObjs;
                }
            }

            return new JArray();
        }
    }
}
