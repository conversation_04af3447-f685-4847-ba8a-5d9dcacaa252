using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Data;
using Newtonsoft.Json.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormOp.FormService;
using System.Threading.Tasks;
using System.Threading;

namespace JieNor.Framework.AppService.SystemPlugIn.BD.RecordArchiveRule
{
    /// <summary>
    /// 归档规则：保存
    /// </summary>
    [InjectService]
    [FormId("bd_recordarchiverule")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 操作日志归档任务标识
        /// </summary>
        public const string opLogArchiveTaskId = "OpLogArchiveTask";

        /// <summary>
        /// 操作日志归档清理任务标识
        /// </summary>
        public const string opLogArchiveClearTaskId = "OpLogArchiveClearTask";

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var jobScheduleService = this.Container.GetService<IJobScheduleService>();

            //校验归档频率
            var hertzMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var enable = Convert.ToBoolean(newData["fenable"]);
                if (enable)
                {
                    var hertz = Convert.ToString(newData["fhertz"]);
                    if (hertz.IsNullOrEmptyOrWhiteSpace())
                    {
                        hertzMessage = "归档频率不能为空，请检查！";
                        return false;
                    }
                    var isOk = jobScheduleService.CheckCronExpr(this.Context, hertz);
                    if (!isOk)
                    {
                        hertzMessage = "归档频率格式错误，请检查！";
                        return false;
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => hertzMessage));

            //校验清理频率
            var clearHertzMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var enableClear = Convert.ToBoolean(newData["fenableclear"]);
                if (enableClear)
                {
                    var hertz = Convert.ToString(newData["fclearhertz"]);
                    if (hertz.IsNullOrEmptyOrWhiteSpace())
                    {
                        clearHertzMessage = "清理频率不能为空，请检查！";
                        return false;
                    }
                    var isOk = jobScheduleService.CheckCronExpr(this.Context, hertz);
                    if (!isOk)
                    {
                        clearHertzMessage = "清理频率格式错误，请检查！";
                        return false;
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => clearHertzMessage));

            //校验存储路径
            var errorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var enableClear = Convert.ToBoolean(newData["fenableclear"]);
                if (enableClear)
                {
                    var bakPath = Convert.ToString(newData["fbakpath"]);
                    if (bakPath.IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = $"{this.HtmlForm.Caption}的【清理的备份日志存储至路径】字段不能为空，请检查！";
                        return false;
                    }
                    if (!PathUtils.IsValidFolderPath(bakPath))
                    {
                        errorMessage = $"{this.HtmlForm.Caption}的【清理的备份日志存储至路径】字段值不是合法的路径，请检查！";
                        return false;
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            var dataEntity = e.DataEntitys?.FirstOrDefault();
            if (dataEntity == null) return;

            this.CreateScheduleTask(dataEntity, "fenable", "fhertz", opLogArchiveTaskId, "操作日志归档任务");
            this.CreateScheduleTask(dataEntity, "fenableclear", "fclearhertz", opLogArchiveClearTaskId, "操作日志归档清理任务");
        }

        /// <summary>
        /// 创建计划任务
        /// </summary>
        private void CreateScheduleTask(
            DynamicObject dataEntity,
            string enableFieldKey,
            string hertzFieldKey,
            string taskId,
            string taskName)
        {
            var taskForm = this.MetaModelService.LoadFormModel(this.Context, "bas_task");
            var taskType = taskForm.GetDynamicObjectType(this.Context);
            var taskDm = this.GetDataManager();
            taskDm.InitDbContext(this.Context, taskType);

            // 同一个企业下相同的任务，只允许存在一个
            var sqlWhere = $"fmainorgid=@fmainorgid and fpluginid=@fpluginid";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
                new SqlParam("@fpluginid", DbType.String, taskId),
            };
            var reader = this.Context.GetPkIdDataReader(taskForm, sqlWhere, sqlParam);
            var taskObj = taskDm.SelectBy(reader)?.OfType<DynamicObject>()?.FirstOrDefault();

            // 是否启用计划任务，如果启用则生成计划任务，不启用则删除计划任务
            var enable = Convert.ToBoolean(dataEntity[enableFieldKey]);
            if (enable)
            {
                if (taskObj == null)
                {
                    taskObj = taskType.CreateInstance() as DynamicObject;
                    taskObj["fnumber"] = $"RW_SYS_{taskId}";
                    taskObj["fname"] = taskName;
                    taskObj["fpluginid"] = taskId;
                    taskObj["fpluginid_txt"] = taskName;
                    taskObj["fexecuteplan"] = dataEntity[hertzFieldKey];
                    taskObj["fstartstatus"] = "ew_start001";

                    var saveResult = this.Gateway.InvokeBillOperation(
                        this.Context,
                        taskForm.Id,
                        new DynamicObject[] { taskObj },
                        "save",
                        new Dictionary<string, object>());
                    saveResult?.ThrowIfHasError(true, $"生成关联的{taskName}失败！");
                }

                // 执行状态
                // ew_start001 正常
                // ew_start002 完成
                // ew_start003 调度中
                // ew_start004 执行中
                // ew_start005 错误

                // 如果不为【调度中】时，则执行任务调度
                if (!Convert.ToString(taskObj["fstartstatus"]).EqualsIgnoreCase("ew_start003"))
                {
                    var result = this.Gateway.InvokeBillOperation(
                        this.Context,
                        taskForm.Id,
                        new DynamicObject[] { taskObj },
                        "ewtest",
                        new Dictionary<string, object>());
                    result?.ThrowIfHasError(true, $"执行关联的{taskName}失败！");
                }
            }
            else
            {
                if (taskObj != null)
                {
                    var result = this.Gateway.InvokeBillOperation(
                        this.Context,
                        taskForm.Id,
                        new DynamicObject[] { taskObj },
                        "delete",
                        new Dictionary<string, object>());
                    result?.ThrowIfHasError(true, $"删除关联的{taskName}失败！");
                }
            }
        }
    }
}