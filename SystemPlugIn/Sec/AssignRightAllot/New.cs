using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.Framework.AppService.SystemPlugIn.Sec.AssignRightAllot
{
    /// <summary>
    /// 角色分配
    /// </summary>
    [InjectService]
    [FormId("sec_assignrightallot")]
    [OperationNo("new")]
    public class New : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 权限检测
        /// </summary>
        /// <param name="e"></param>
        public override void OnCheckPermssion(OnCheckPermssionArgs e)
        {
            base.OnCheckPermssion(e);

            //此处需要检查“角色授权”的分配权限
            e.FormId = "sec_assignright";
            e.PermItem = "fw_allotrole";
        }
    }
}