using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JieNor.Framework.AppService.SystemPlugIn.Auth;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.Framework.AppService.SystemPlugIn.Sec.AssignRightAllot
{
    /// <summary>
    /// 角色分配：确认分配
    /// </summary>
    [InjectService]
    [FormId("sec_assignrightallot")]
    [OperationNo("allocation")]
    public class Allocation : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 权限检测
        /// </summary>
        /// <param name="e"></param>
        public override void OnCheckPermssion(OnCheckPermssionArgs e)
        {
            base.OnCheckPermssion(e);

            //此处需要检查“角色授权”的分配权限
            e.FormId = "sec_assignright";
            e.PermItem = "fw_allotrole";
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var companys = this.GetQueryOrSimpleParam<string>("companys", "");
            var companyList = companys.FromJson<List<Dictionary<string, string>>>();
            if (companyList == null || companyList.Count <= 0)
            {
                throw new BusinessException("企业Id参数 companyIds 为空 ，请检查！");
            }

            var roleUsers = this.GetQueryOrSimpleParam<string>("roleUsers", "");
            var roleUserList = roleUsers.FromJson<List<Dictionary<string, string>>>();

            var permission = this.GetQueryOrSimpleParam<string>("permission", "");
            var roleACLDetail = permission.FromJson<RolePermitInfo>();
            if (roleACLDetail == null || roleACLDetail.RoleId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("参数 permission 为空 或者 permission.roleId 为空  ，请检查！");
            }
            var roleId = roleACLDetail.RoleId;
            var roleForm = this.MetaModelService.LoadFormModel(this.Context, "sec_role");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, roleForm.GetDynamicObjectType(this.Context));
            var roleObj = dm.Select(roleId) as DynamicObject;
            if (roleObj == null)
            {
                throw new BusinessException($"角色 {roleId} 不存在或者已被删除 ，请检查！");
            }
            if (!Convert.ToBoolean(roleObj["fispreset"]) || Convert.ToString(roleObj["fmainorgid"]) != "0")
            {
                throw new BusinessException($"您选择的角色【{roleObj["fname"]}】没有手工设为共享，不允许分配！");
            }

            //去除重复的业务对象
            this.RemoveDuplicateBizObj(roleACLDetail);

            roleACLDetail.ProductId = this.Context.Product;
            roleACLDetail.RoleId = roleId;

            //每处理一个企业所占用的进度比
            decimal singleProgressVal = 100M / companyList.Count;
            //已处理数
            var processed = 0;

            //根据企业Id分组后循环分配角色
            var groups = roleUserList?.GroupBy(o => o["userCompanyId"])?.ToList();
            foreach (var company in companyList)
            {
                var companyId = company["companyId"];
                var companyName = company["companyName"];

                roleACLDetail.CompanyId = companyId;
                roleACLDetail.RoleUsers = new Dictionary<string, string>();

                this.TaskProgressService.SetTaskProgressMessage(this.Context, this.TaskId, $"正在为 {companyName} 分配角色，请稍等...");

                //测试代码
                //System.Threading.Thread.Sleep(2000);

                var group = groups?.FirstOrDefault(o => o.Key.EqualsIgnoreCase(companyId))?.ToList();
                if (group != null)
                {
                    foreach (var user in group)
                    {
                        roleACLDetail.RoleUsers[user["userId"]] = user["userName"];
                    }
                }
                PermHelper.SaveRolePermission(this.Context, roleACLDetail);

                //任务处理进度
                processed++;
                var progressValue = processed * singleProgressVal;
                if (companyList.Count == processed)
                {
                    //如果已经处理完最后一个，则将进度设为100
                    progressValue = 100;
                    this.TaskProgressService.SetTaskProgressMessage(this.Context, this.TaskId, $"所有企业已经分配完毕。");
                }

                this.TaskProgressService.SetTaskProgressValue(this.Context, this.TaskId, progressValue);
            }

            this.Result.IsShowMessage = true;
            this.Result.IsSuccess = true;
            this.Result.ComplexMessage.SuccessMessages.Add("分配成功！");
        }

        /// <summary>
        /// 去除重复的业务对象：
        /// 相同的业务对象只存一份，此时需要去除重复的业务对象（前端虽然已经去重了，但是为了保险，后端还得去重一次）
        /// </summary>
        /// <param name="roleACLDetail"></param>
        private void RemoveDuplicateBizObj(RolePermitInfo roleACLDetail)
        {
            var _bizObjIds = new List<string>();
            if (roleACLDetail != null && roleACLDetail.MdlPermission != null)
            {
                foreach (var item in roleACLDetail.MdlPermission)
                {
                    if (item == null || item.BizObjPermission == null) continue;

                    var toRemoves = new List<BizObjPermitInfo>();
                    foreach (var bizObj in item.BizObjPermission)
                    {
                        if (bizObj == null || bizObj.BizObjId.IsNullOrEmptyOrWhiteSpace()) continue;

                        if (_bizObjIds.Contains(bizObj.BizObjId))
                        {
                            toRemoves.Add(bizObj);
                        }
                        else
                        {
                            _bizObjIds.Add(bizObj.BizObjId);
                        }
                    }
                    foreach (var bizObj in toRemoves)
                    {
                        item.BizObjPermission.Remove(bizObj);
                    }
                }
            }
        }
    }
}