using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;

using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.Framework.AppService.SystemPlugIn.Sys
{
    /// <summary>
    /// 外部应用保存
    /// </summary>
    [InjectService]
    [FormId("sys_externalapp")]
    [OperationNo("save")]
    public class ExternalAppSave : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fname"]).NotEmpty().WithMessage("名称不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fserverurl"]).NotEmpty().WithMessage("服务地址不能为空！"));
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            var dataEntity = e.DataEntitys[0];

            //新增
            if (!dataEntity.DataEntityState.FromDatabase)
            {
                //生成应用标识字段值
                dataEntity["fappid"] = Guid.NewGuid().ToString().Replace("-", "").ToLower();
            }

            //自动补全服务地址最后的斜杠“/”
            string serverUrl = dataEntity["fserverurl"] as string;
            if (!serverUrl.IsNullOrEmptyOrWhiteSpace())
            {
                if (serverUrl[serverUrl.Length - 1] != '/')
                {
                    dataEntity["fserverurl"] = serverUrl + '/';
                }
            }
        }
    }
}