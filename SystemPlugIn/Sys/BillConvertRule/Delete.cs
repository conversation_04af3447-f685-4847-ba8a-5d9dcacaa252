using System;
using System.Linq;
using System.IO;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.Framework.AppService.SystemPlugIn.Sys.BillConvertRule
{
    /// <summary>
    /// 单据转换规则：删除
    /// </summary>
    [InjectService]
    [FormId("sys_billconvertrule")]
    [OperationNo("Delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            //string errorMessage = "";
            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    var isPreset = Convert.ToBoolean(newData["fispreset"]);
            //    var mainOrgId = newData["fmainorgid"] as string;
            //    if (mainOrgId.IsNullOrEmptyOrWhiteSpace()
            //        || mainOrgId.EqualsIgnoreCase("0")
            //        || !mainOrgId.EqualsIgnoreCase(this.Context.Company)
            //        || isPreset)
            //    {
            //        errorMessage = $"系统预置的{this.HtmlForm.Caption}【{newData["fnumber"]}】不允许删除！";
            //        return false;
            //    }
            //    return true;
            //}).WithMessage("{0}", (billObj, propObj) => errorMessage));
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            foreach (var dataEntity in e.DataEntitys)
            {
                var sourceFormId = Convert.ToString(dataEntity["fsourceformid"]);
                var targetFormId = Convert.ToString(dataEntity["ftargetformid"]);

                var ruleId = $"{sourceFormId}2{targetFormId}";
                var ruleFileName = $"{ruleId}.{this.Context.Company}.cvt.json";

                var ruleFilePath = PathUtils.SearchWebFiles("/mdl", ruleFileName).FirstOrDefault();
                if (ruleFilePath.IsNullOrEmptyOrWhiteSpace()) return;

                if (File.Exists(ruleFilePath))
                {
                    File.Delete(ruleFilePath);
                }

                //清除规则文件缓存
                this.MetaModelService.ClearConvertRuleCache(this.Context, ruleId);
            }
        }
    }
}