using System;
using System.Linq;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Security.Cryptography;

using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.Validator;
using Newtonsoft.Json.Linq;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.Framework.AppService.SystemPlugIn.Sys
{
    /// <summary>
    /// 审计日志 --刷新操作
    /// </summary>
    [InjectService]
    [FormId("bas_auditinglog_list")]
    [OperationNo("getentrydata")]
    public class AuditingLogGetEntryData : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            string year = DateTime.Now.Year.ToString();
            string month = DateTime.Now.Month > 9 ? DateTime.Now.Month.ToString() : "0" + DateTime.Now.Month.ToString();
            string sql = @"/*dialect*/ select distinct fbillformid from t_sys_logdata_{0} a
                                       where not exists(select b.fid from T_BAS_AuditingLog b where a.fid=b.flogid) ".Fmt(year + month);
            DynamicObjectCollection dataList = this.DBService.ExecuteDynamicObject(this.Context, sql);
            if (dataList.Count == 0)
            {
                return;
            }

            var metaSer = this.Container.GetService<IMetaModelService>();

            sql = "";
            foreach (DynamicObject data in dataList)
            {
                string formid = data["fbillformid"]?.ToString();
                if (formid.IsNullOrEmptyOrWhiteSpace() || formid.EqualsIgnoreCase("sys_lookupboject") || formid.EqualsIgnoreCase("bas_auditinglog"))
                {
                    continue;
                }
                var metaModel = metaSer.LoadFormModel(this.Context, formid);
                if (metaModel.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                if (sql.IsNullOrEmptyOrWhiteSpace())
                {
                    sql = "/*dialect*/ ";
                }
                else
                {
                    sql += " union all ";
                }

                sql += @" select a.fid,a.flevel,a.fcategory,a.fbillformid,'{4}' as formname,a.fbillids,b.{2} as fnumber,a.fopcode,a.fcontent,a.fdetail,a.foperator,a.fopdate
                         from t_sys_logdata_{5} a 
                         inner join {0} b on a.fbillids=b.{1} 
                         where a.fbillformid='{3}' and not exists(select t.fid from T_BAS_AuditingLog t where a.fid=t.flogid)
                          ".Fmt(metaModel.BillHeadTableName, metaModel.BillPKFldName, metaModel.GetNumberField()?.FieldName, formid, metaModel.Caption, year + month);
            }
            if (sql.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            sql += " order by a.fbillformid,a.fbillids,a.fopdate desc ";
            List<object> objs = new List<object>();
            DynamicObjectCollection logList = this.DBService.ExecuteDynamicObject(this.Context, sql);
            foreach (DynamicObject log in logList)
            {
                object obj = new
                {
                    flogid = log["fid"],
                    fformid = log["fbillformid"],
                    fobjname = log["formname"],
                    forderno = log["fnumber"],
                    forderid = log["fbillids"],
                    fopcode = log["fopcode"],
                    foperator = log["foperator"],
                    foptime = log["fopdate"],
                    fcontent = log["fcontent"],
                    fdetail = log["fdetail"]
                };
                objs.Add(obj);
            }
            this.Result.SrvData = objs;
        }
    }

}