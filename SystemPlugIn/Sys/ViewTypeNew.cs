using System;
using System.Linq;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Security.Cryptography;

using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.Framework.AppService.SystemPlugIn.Sys
{
    /// <summary>
    /// 视图类型 --新增
    /// </summary>
    [InjectService]
    [FormId("sys_viewtype")]
    [OperationNo("New")]
    public class ViewTypeNew : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            if(e.DataEntitys ==null || e.DataEntitys.Length !=1)
            {
                return;
            }

            var menus = GetAllMenuItem(this.Context);
            if(menus==null || menus.Count ==0)
            {
                return;
            }

            var billData = e.DataEntitys[0];
            var menuRows = billData["fentity"] as DynamicObjectCollection;
            foreach (var item in menus)
            {
               var row= menuRows.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject ;
                row["fmodulename"] = item["fmodulename"];
                row["fgroupname"] = item["fgroupname"];
                //row["fmenuname"] = item["fmenuname"];
                row["fmenuid"] = item["fmenuid"];
                row["fvisible"] = false;

                menuRows.Add(row);
            }
        }

        public  static  DynamicObjectCollection  GetAllMenuItem(UserContext  ctx)
        {
            var sql = @"select t0.fmenuid, t0.fname fmenuname, t1.fname fgroupname,t2.fname fmodulename
                        from t_sys_menuitem t0
                        inner
                        join t_sys_menugroup t1 on t0.fgroupid = t1.fgroupid
                        inner
                        join t_sys_bizmodule t2 on t1.fmoduleid = t2.fmoduleid
                        where t0.fhidden <> '1'
                        order by t2.forder,t2.fmoduleid,t1.forder,t1.fgroupid,t0.forder,t0.fmenuid";
            var svc = ctx.Container.GetService<IDBService>();
            var memuDatas=  svc.ExecuteDynamicObject(ctx, sql);

            return memuDatas;
        }
    }
}