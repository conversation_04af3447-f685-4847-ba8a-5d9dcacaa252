using JieNor.Framework.DataEntity.MainFw;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.Framework.AppService.SystemPlugIn.Auth
{
    /// <summary>
    /// 保存角色权限信息到认证站点：用于角色授权界面
    /// </summary>
    [InjectService]
    [FormId("sec_role")]
    [OperationNo("SaveRolePermission")]
    public class SaveRolePermission : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 权限检测
        /// </summary>
        /// <param name="e"></param>
        public override void OnCheckPermssion(OnCheckPermssionArgs e)
        {
            base.OnCheckPermssion(e);

            //此处需要检查“角色授权”的分配权限
            e.FormId = "sec_assignright";
            e.PermItem = "fw_allotrole";
        }

        /// <summary>
        /// 保存权限信息至业务站点
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var companyId = this.Context.Company;
            var roleId = this.GetQueryOrSimpleParam<string>("roleId", "");
            var bizObjId = this.GetQueryOrSimpleParam<string>("bizObjId", "");
            var permission = this.GetQueryOrSimpleParam<string>("permission", "");
            var roleACLDetail = permission.FromJson<RolePermitInfo>();

            //相同的业务对象只存一份，此时需要去除重复的业务对象（前端虽然已经去重了，但是为了保险，后端还得去重一次）
            var _bizObjIds = new List<string>();
            if (roleACLDetail != null && roleACLDetail.MdlPermission != null)
            {
                foreach (var item in roleACLDetail.MdlPermission)
                {
                    if (item == null || item.BizObjPermission == null) continue;

                    var toRemoves = new List<BizObjPermitInfo>();
                    foreach (var bizObj in item.BizObjPermission)
                    {
                        if (bizObj == null || bizObj.BizObjId.IsNullOrEmptyOrWhiteSpace()) continue;

                        if (_bizObjIds.Contains(bizObj.BizObjId))
                        {
                            toRemoves.Add(bizObj);
                        }
                        else
                        {
                            _bizObjIds.Add(bizObj.BizObjId);
                        }
                    }
                    foreach (var bizObj in toRemoves)
                    {
                        item.BizObjPermission.Remove(bizObj);
                    }
                }
            }

            roleACLDetail.ProductId = this.Context.Product;
            roleACLDetail.CompanyId = companyId;
            roleACLDetail.RoleId = roleId;
            PermHelper.SaveRolePermission(this.Context, roleACLDetail, bizObjId);

            //创建日志对象
            var lstLogEntryObjs = new LogEntry()
            {
                BillFormId = this.OperationContext.HtmlForm.Id,
                OpCode = this.OperationContext.OperationNo,
                Level = Enu_LogLevel.Info.ToString(),
                Category = 5,
                Content = "执行了【修改】操作！",
                BillIds = this.Context.UserName,
                BillNos = this.Context.UserName,
                Detail = "",
                LogType = Enu_LogType.RecordType_03,
                DebugData = this.OperationContext.Result?.ToJson() ?? "",
                OpName = "修改权限"
            };
            this.Logger?.WriteLog(this.OperationContext.UserContext, lstLogEntryObjs);

            this.Result.IsShowMessage = true;
            this.Result.IsSuccess = true;
            this.Result.ComplexMessage.SuccessMessages.Add("保存成功！");
        }

        /// <summary>
        /// 当使用平台角色授权后，自动更新本地用户与角色的关系
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            var strSql = $@"
select t1.fuserid
        ,t3.fnumber fusernumber
        ,t3.fname fusername
        ,t1.froleid
        ,t2.fnumber frolenumber
        ,t2.fname frolename
from t_sec_roleuser t1
inner join t_sec_user t3 on t1.fuserid=t3.fid 
inner join t_sec_role t2 on t1.froleid=t2.fid
where t3.fmainorgid=@companyId
";
            var roleUserObjs = this.DBService.ExecuteDynamicObject(this.Context, strSql, new SqlParam[]
            {
                new SqlParam("companyId", System.Data.DbType.String, this.Context.Company)
            });

            var lstBatchSql = new List<KeyValuePair<string, IEnumerable<SqlParam>>>();

            var roleUserObjGroups = roleUserObjs.GroupBy(o => o["fuserid"] as string);
            foreach (var group in roleUserObjGroups)
            {
                var userId = group.Key;
                List<Tuple<string, string>> lstRoleIds = new List<Tuple<string, string>>();
                foreach (var obj in group)
                {
                    var roleId = obj["froleid"] as string;
                    if (roleId.IsEmptyPrimaryKey()) continue;
                    var roleName = obj["frolename"] as string;
                    if (roleName.IsNullOrEmptyOrWhiteSpace())
                    {
                        roleName = obj["frolenumber"] as string;
                    }
                    lstRoleIds.Add(Tuple.Create(roleId, roleName));
                }
                strSql = $@"
update t_sec_user set froleids='{lstRoleIds.Select(o => o.Item1).Distinct().JoinEx(",", false)}',froleids_txt='{lstRoleIds.Select(o => o.Item2).Distinct().JoinEx(",", false)}'
where fid=@userId
";
                lstBatchSql.Add(new KeyValuePair<string, IEnumerable<SqlParam>>(strSql, new SqlParam[]
                {
                    new SqlParam("userId", System.Data.DbType.String, userId)
                }));
            }

            if (lstBatchSql.Any())
            {
                var dbserviceEx = this.Container.GetService<IDBServiceEx>();
                dbserviceEx.ExecuteBatch(this.Context, lstBatchSql);
            }
        }
    }
}