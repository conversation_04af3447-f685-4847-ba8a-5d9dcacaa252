using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.Utils;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.CustomException;

namespace JieNor.Framework.AppService.SystemPlugIn.Auth
{
    /// <summary>
    /// 解绑微信用户
    /// </summary>
    [InjectService]
    [FormId("sec_user")]
    [OperationNo("UnBindWxUser")]
    public class UnBindWxUser : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 执行操作事务后事件，通知插件对象执行其它事务无关的业务逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            var wxOpenId = "";

            //是否是扫码解绑（取决于前端是否有传递 code 参数）
            var code = this.GetQueryOrSimpleParam<string>("code", "");
            if (!code.IsNullOrEmptyOrWhiteSpace())
            {
                //获取微信信息
                var wxResult = WinXinUtils.RequestAccessToken(code);
                //微信接口请求失败
                if (wxResult.errcode != 0)
                {
                    throw new BusinessException(wxResult.FormatMessage());
                }
                wxOpenId = wxResult.openid;
            }

            var request = new CommonFormDTO()
            {
                FormId = "auth_user",
                OperationNo = "UnBindWxUser",
            };
            request.SimpleData.Add("wxOpenIdType", ((int)WeiXinOpenIdType.WebsiteApp).ToString());
            request.SimpleData.Add("wxOpenId", wxOpenId);
            request.SimpleData.Add("userName", this.Context.UserName);
            request.SimpleData.Add("productId", this.Context.Product);

            var gateway = this.Container.GetService<IHttpServiceInvoker>();
            var targetSEP = TargetSEP.AuthService;
            if (!targetSEP.Headers.ContainsKey("X-AppId")) targetSEP.Headers.Add("X-AppId", this.Context.Product);

            var response = gateway.Invoke(this.Context, targetSEP, request) as DynamicDTOResponse;

            this.Result.MergeResult(response?.OperationResult);
        }
    }
}