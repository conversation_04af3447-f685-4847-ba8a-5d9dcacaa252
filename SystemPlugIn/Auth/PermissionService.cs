using Autofac.Features.Metadata;
using JieNor.Framework.AppService.QueryBuilder;
using JieNor.Framework.AppService.SystemPlugIn.Auth;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.PermData;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace JieNor.Framework.AppService.Auth
{
    /// <summary>
    /// 权限服务实现
    /// </summary>
    [InjectService]
    public class PermissionService : IPermissionService
    {
        /// <summary>
        /// 清空缓存
        /// </summary>
        /// <param name="ctx"></param>
        public   void ClearCache(UserContext ctx)
        {
            PermHelper.ClearCache(ctx);
        }

        /// <summary>
        /// 检测当前用户对某个表单某个操作是否有权限，无权直接异常报错
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="permData"></param>
        public void CheckPermission(UserContext ctx, PermAuth permData)
        {
            if (!HasPermission(ctx, permData))
            {
                var htmlForm = HtmlParser.LoadFormMetaFromCache(permData.FormId, ctx);

                //尝试获取一下权限项关联的操作名称 或者 权限项名称
                var operationName = permData.OperationName;
                if (operationName.IsNullOrEmptyOrWhiteSpace())
                {
                    operationName = htmlForm?.FormOperations?.FirstOrDefault(o => o.PermItemId.EqualsIgnoreCase(permData.PermId))?.Caption ?? "";
                    if (operationName.IsNullOrEmptyOrWhiteSpace())
                    {
                        operationName = htmlForm?.FormPermItems?.FirstOrDefault(o => o.Id.EqualsIgnoreCase(permData.PermId))?.Caption ?? "";
                    }
                }
                var userStr = ctx.DisplayName;
                if (userStr.IsNullOrEmptyOrWhiteSpace())
                {
                    userStr = ctx.UserName;
                    if (userStr.IsNullOrEmptyOrWhiteSpace())
                    {
                        userStr = ctx.UserId;
                    }
                    else
                    {
                        //是否是云链通信账号
                        if (userStr.EqualsIgnoreCase(ctx.Company))
                        {
                            var userObj = ctx.LoadBizDataById("sec_user", ctx.UserId);
                            userStr = Convert.ToString(userObj?["fname"]);
                            if (userStr.IsNullOrEmptyOrWhiteSpace()) userStr = ctx.UserId;
                        }
                    }
                }
                throw new BusinessException("您没有【{0}】的{1}操作权限(用户：{2})".Fmt(htmlForm?.Caption, operationName, userStr));
            }
        }

        /// <summary>
        /// 检测当前用户对某个表单某个操作是否有权限，无权不会报错。
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="permData"></param>
        /// <returns></returns>
        public bool HasPermission(UserContext ctx, PermAuth permData)
        {
            if (ctx.IsTempToken && ctx.PermData != null)
            {
                //处理共享链接的有限权限检查，如果当前待检查的权限项，没有有临时会话中指定，则不允许进一步操作
                IEnumerable<string> allAllowPermItems;
                ctx.PermData.TryGetValue(permData.FormId, out allAllowPermItems);
                //if (allAllowPermItems == null
                //    || allAllowPermItems.Any(x => x.EqualsIgnoreCase(permData.PermId)) == false)
                //{
                //    throw new BusinessException("当前临时会话没有足够权限完成此操作！");
                //}
                //临时给了权限
                return true;
            }

            //如果是管理员用户，不验证权限
            if (ctx?.UserName.EqualsIgnoreCase("administrator") == true)
            {
                return true;
            }
            
            //如果是预设的管理员用户，不验证权限
            if (ctx?.UserId.EqualsIgnoreCase("sysadmin") == true)
            {
                return true;
            }

            //判断当前用户是否是否是演示账套里面的体验用户 :是==》不参与授权
            if (IsDemoAccountAndTrialUser(ctx))
            {
                return true;
            }

            //如果用户属于管理员角色组，不验证权限
            if (ctx.IsTopOrg && IsRoleAdmin(ctx, permData))
            {
                return true;
            }

            var hasRight = PermHelper.HasPermission(ctx, permData);

            return hasRight;
        }

        /// <summary>
        /// 判断当前用户是否是否是演示账套里面的体验用户
        /// </summary>
        /// <param name="ctx"></param> 
        /// <returns></returns>
        private bool IsDemoAccountAndTrialUser(UserContext ctx)
        {
            var org = ctx.Companys.FirstOrDefault(f => f.CompanyId.EqualsIgnoreCase(ctx.Company));
            if (ctx != null && org != null && org.RegistType == DataTransferObject.DB.OrgAccountType.Demo
             && ctx.UserType.HasValue
             && (ctx.UserType.Value & (long)Enu_LoginUserType.TrialProductUser) == (long)Enu_LoginUserType.TrialProductUser)
            {
                return true;
            }

            return false;
        }
        /// <summary>
        /// 判断用户是否属于管理员角色组
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="permData"></param>
        /// <returns></returns>
        public bool IsRoleAdmin(UserContext ctx, PermAuth permData)
        {
            if (ctx.IsAdminRole(permData.CompanyId)) return true;

            return PermHelper.IsRoleAdmin(ctx, permData);
        }

        /// <summary>
        /// 判断用户是否属于系统运维角色组
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="permData"></param>
        /// <returns></returns>
        public bool IsDevOpsUser(UserContext ctx, PermAuth permData)
        {
            return PermHelper.IsDevOpsUser(ctx, new PermAuth(ctx));
        }


        /// <summary>
        /// 创建用户角色映射关系
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="dctUserRoleMap"></param>
        /// <param name="forceRebuild">强制重建，先删除关系再按当前关系重建，否则追加或更新方式创建</param>        
        public void CreateUserRoleMap(UserContext ctx, Dictionary<string, IEnumerable<string>> dctUserRoleMap, bool forceRebuild = false)
        {
            if (dctUserRoleMap == null
                || dctUserRoleMap.Any() == false) return;

            //先删除用户与角色的关系
            if (forceRebuild)
            {
                List<SqlParam> lstSqlParas = new List<SqlParam>()
                {
                    new SqlParam("@companyId", System.Data.DbType.String, ctx.Company),
                };
                var tempTableName = "";
                string strSql = $@" Delete u1 from t_sec_roleuser u1 ";
                if (dctUserRoleMap.Keys.IsGreaterThan(50))
                {
                    var dbService = ctx.Container.GetService<IDBService>();
                    tempTableName = dbService.CreateTempTableWithDataList(ctx, dctUserRoleMap.Keys.Distinct(),false);
                    strSql += $@"
inner join {tempTableName} temp on temp.fid=u1.fuserid
";
                }
                else
                {
                    int index = 0;
                    StringBuilder sbWhere = new StringBuilder();
                    foreach(var userId in dctUserRoleMap.Keys)
                    {
                        if (sbWhere.Length > 0)
                        {
                            sbWhere.Append(",");
                        }
                        sbWhere.Append($"@userId{index}");

                        lstSqlParas.Add(new SqlParam($"userId{index}", System.Data.DbType.String, userId));

                        index++;
                    }
                    strSql += $@"
where u1.fuserid in ({sbWhere.ToString()})
";
                }
                
                var svc = ctx.Container.GetService<IDBServiceEx>();
                svc.Execute(ctx, strSql, lstSqlParas);

                if (!tempTableName.IsNullOrEmptyOrWhiteSpace())
                {
                    var svcX = ctx.Container.GetService<IDBService>();
                    svcX.DeleteTempTableByName(ctx, tempTableName, true);
                }
            }

            var seqService = ctx.Container.GetService<ISequenceService>();
            var lstBatchSql = new List<KeyValuePair<string, IEnumerable<SqlParam>>>();
            foreach(var kvpItem in dctUserRoleMap)
            {
                foreach(var roleId in kvpItem.Value)
                {
                    List<SqlParam> lstParam = new List<SqlParam>();
                    lstParam.AddRange(new SqlParam[]
                    {
                        new SqlParam("userId", System.Data.DbType.String,kvpItem.Key),
                        new SqlParam("id", System.Data.DbType.String, seqService.GetSequence<string>()),
                        new SqlParam("formId",  System.Data.DbType.String,"sec_roleuser"),
                        new SqlParam("roleId", System.Data.DbType.String,roleId)
                    });
                    var strSql = "";
                    if (forceRebuild)
                    {
                        strSql = $@"insert into t_sec_roleuser(fid,fformid,fuserid,froleid) values(@id,@formId,@userId,@roleId)";                        
                    }
                    else
                    {
                        strSql = $@"
if not exists(select 1 from t_sec_roleuser where fuserid=@userId and froleid=@roleId)
begin
    insert into t_sec_roleuser(fid,fformid,fuserid,froleid) values(@id,@formId,@userId,@roleId)
end
";
                        
                    }
                    lstBatchSql.Add(new KeyValuePair<string, IEnumerable<SqlParam>>(strSql, lstParam));
                }
            }

            if (lstBatchSql.Any())
            {
                var dbServiceEx = ctx.Container.GetService<IDBServiceEx>();
                dbServiceEx.ExecuteBatch(ctx, lstBatchSql);
            }

            PermHelper.ClearCache(ctx);
        }

        /// <summary>
        /// 获取拥有某个业务对象的对应权限的用户列表，比如获取【拥有查看招标信息】的用户列表,
        /// 多用于发送消息时确定那些用户能收到此类消息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="permData"></param>
        /// <returns></returns>
        public List<string> HaveBizObPermitUsers(UserContext ctx, PermAuth permData)
        {
            var cacheKey = permData.GetFuncPermUsersCacheKey();

            var redisCache =PermHelper.GetRedisCacheSvc (ctx);
            var authResult = new List<string>();
            try
            {
                authResult = redisCache.Get<List<string>>(cacheKey);
            }
            catch (Exception ex)
            { 
                //忽略redis缓存取数错误
            }
            
            if (authResult != null && authResult.Count > 0)
            {
                return authResult;
            }

            authResult = new List<string>();
            var strSql = @"select distinct t2.fid,t2.fnumber,t2.fname
                            from t_sec_rolefuncacl t0
                            inner join t_sec_roleuser t1 on t0.froleid=t1.froleid
                            inner join t_sec_user t2 on t1.fuserid=t2.fid
                            where t0.fcompanyid=@fcompanyid 
                                    and t0.fbizobjid=@fbizobjid and t0.fpermititemid=@fpermititemid
                                    and t0.fallow='1' and t0.frefuse='0'
                            ";
            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("fcompanyid", System.Data.DbType.String,permData.CompanyId),
                new SqlParam("fbizobjid", System.Data.DbType.String,permData.FormId),
                new SqlParam("fpermititemid", System.Data.DbType.String,permData.PermId),
            };


            var dbService = ctx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(ctx, strSql, lstSqlParas))
            {
                while (reader.Read())
                {
                    var userId = Convert.ToString(reader["fid"]);
                    if (!userId.IsNullOrEmptyOrWhiteSpace())
                    {
                        BaseDataSummary bdSummary = new BaseDataSummary();
                        authResult.Add(userId);
                    }
                }
            }
            string roleId = "admin." + ctx.Company;
            Dictionary<string, string> adminUsers = PermHelper.GetRoleUser(ctx, roleId);
            if (adminUsers.Count == 0)
            {
                adminUsers = PermHelper.GetRoleUser(ctx, "admin");
            }
            foreach(KeyValuePair<string,string> pair in adminUsers)
            {
                authResult.Add(pair.Key);
            }

            //避免缓存失效
            if(authResult.Count == 0)
            {
                authResult.Add("#");
            }

            redisCache.Set( cacheKey, authResult);
            return authResult;
        }

        /// <summary>
        /// 保存角色对应的权限信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="roleACLDetail"></param>
        public void SaveRolePermission(UserContext ctx, RolePermitInfo roleACLDetail)
        {
            PermHelper.SaveRolePermission(ctx, roleACLDetail);
        }


        /// <summary>
        /// 角色授权中的数据范围
        /// </summary>
        /// <param name="ctx">上下文信息</param>
        /// <param name="para">查询参数</param> 
        /// <returns>数据行范围sql</returns>
        public IEnumerable<FilterRowObject> GetDataRowACLFilter(UserContext ctx, DataQueryRuleParaInfo para)
        {
            List<FilterRowObject> filters = new List<FilterRowObject>();

            //如果是管理员，则不设置数据范围权限
            if (ctx.IsTopOrg && IsRoleAdmin(ctx, new PermAuth(ctx)))
            { 
                return filters;
            }

            //如果是系统运维，则不设置数据范围权限
            if (PermHelper.IsDevOpsUser(ctx, new PermAuth(ctx)))
            {
                return filters;
            }

            //如果是从业务单据上查找的基础资料字段的数据，则看看数据范围中是否设置了相关的基础资料字段的可选范围
            var srcAuths = new List<DataRowAuthInfo>();
            if (!para.SrcFormId.IsNullOrEmptyOrWhiteSpace() && !para.SrcFldId.IsNullOrEmptyOrWhiteSpace())
            {
                srcAuths = PermHelper.GetDataRowACLPermDataByUser(ctx, para.SrcFormId); 
            }

            var metaModelService = ctx.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(ctx, para.FormId);

            //该业务对象设置的列表数据范围
            var drAuths = PermHelper.GetDataRowACLPermDataByUser(ctx, para.FormId);
            if (drAuths.Any(f => f.FilterType.EqualsIgnoreCase("all")))
            {
                //如果有任意一个角色设置为所有，则列表不需要再加过滤了
            }
            else
            {
                var userRoles = PermHelper.GetHasViewPermUserRoles(ctx, para.FormId);
                var drAuthsRoles = drAuths.Select(f => f.RoleId);
                if (userRoles.Any(f => !drAuthsRoles.Contains(f)))
                {
                    //如果有任意一个角色没有设置，则认为为所有，则列表不需要再加过滤了
                }
                else
                {
                    var allRoles = srcAuths.Select(f => f.RoleId).Union(drAuthsRoles).Distinct().ToList();
                    //查所有角色，按角色授权取并集权限
                    foreach (var item in allRoles)
                    {
                        var roleAuthX = new List<FilterRowObject>();
                        var srcAuth = srcAuths.Where(f => f.RoleId == item).ToList();
                        if (srcAuth != null)
                        {
                            roleAuthX.AddRange(GetBillFormBDFldDataRows(ctx, para, srcAuth));
                        }

                        var drAuth = drAuths.FirstOrDefault(f => f.RoleId == item);
                        if (drAuth != null)
                        {
                            var rowFilter = drAuth.ToFilterRowObject(ctx, htmlForm);
                            if (rowFilter != null && rowFilter.Count > 0)
                            {
                                roleAuthX.AddRange(rowFilter);
                            }
                        }

                        if (roleAuthX.Count > 0)
                        {
                            roleAuthX.First().Logic = "or"; //多个角色，取并集

                            if (roleAuthX.Count > 1)
                            {
                                roleAuthX.First().LeftBracket += "(";
                                roleAuthX.Last().RightBracket += ")";
                            }

                            filters.AddRange(roleAuthX);
                        }
                    }
                }
            }
            if (filters.Any())
            {
                filters.First().Logic = "AND";
            }

            return filters;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="para"></param>
        /// <param name="drAuth"></param>
        /// <returns></returns>
        private List<FilterRowObject> GetBillFormBDFldDataRows(UserContext ctx, DataQueryRuleParaInfo para, List<DataRowAuthInfo> drAuth)
        {
            var result = new List<FilterRowObject>();
            var filter = drAuth.SelectMany(f => f.BDFldsAuthInfo).ToList().Where(f => f.fldid.EqualsIgnoreCase(para.SrcFldId)).ToList();
            if(filter !=null && filter.Count >0)
            {
                foreach (var item in filter)
                {
                    if(item.filter.IsNullOrEmptyOrWhiteSpace ())
                    {
                        continue;
                    }
                    var str = System.Text.RegularExpressions.Regex.Unescape(item.filter).Trim('"');                     
                    var express = str.FromJson<List<FilterRowObject>>();
                    if(express !=null )
                    {
                        if(express.Count>1)
                        {
                            express.First().LeftBracket += "(";
                            express.Last().RightBracket += ")"; 
                        }
                        result.AddRange(express);
                    }
                }
            }

            if (result.Any())
            {
                result.First().Memo = "/*表单{0}上设置的可选数据范围*/".Fmt(para.SrcFormId );
                result.First().Logic = "AND";
            }

            return result;
        }


        /// <summary>
        /// 业务单据自定义的数据隔离条件
        /// </summary>
        /// <param name="ctx">上下文信息</param>
        /// <param name="formId">当前业务对象</param>
        /// <param name="srcFormId">来源业务对象（从哪个业务界面查找的数据，比如从采购订单上选择商品，则srcformid=采购订单，formId=商品）</param>
        /// <returns>>数据行范围sql</returns>
        public IEnumerable<FilterRowObject> GetDataQueryRuleFilter(UserContext ctx, DataQueryRuleParaInfo para)
        {
            if (ctx.IsTopOrg && ctx.UserName.EqualsIgnoreCase("administrator"))
            {
                //如果是管理员，则不设置数据范围权限
                return new FilterRowObject[] { };
            }

            var svc = ctx.Container.GetService<IEnumerable<Meta<Lazy<IDataQueryRule>>>>()
                     .Where(o => 
                     {
                         var formKey = o.Metadata.GetString("formid").Trim();
                         if(formKey.EqualsIgnoreCase(para.FormId))
                         {
                             return true;
                         }
                         var forms = formKey.Split('|');
                         if(forms.Any (f=>f.EqualsIgnoreCase (para.FormId)))
                         {
                             return true;
                         }

                         return  false;
                     }
                     ).ToList ();
            if (svc == null || !svc.Any())
            {
                return new FilterRowObject[] { };
            }

            List<FilterRowObject> lstFilterObjs = new List<FilterRowObject>();
            var rulePara = new DataQueryRuleParaInfo()
            {
                FormId = para.FormId,
                SrcFormId = para.SrcFormId,
                SrcPara = para.SrcPara,
            };
            foreach (var item in svc)
            {
                var sql = item.Value.Value.GetDataRowACLFilter(ctx, rulePara);
                if (sql==null || !sql.Any())
                {
                    continue;
                }
                lstFilterObjs.AddRange(sql);
            }

            if (lstFilterObjs.Any()/*&& !lstFilterObjs.First().IgnoreInclude*/)
            {
                lstFilterObjs.First().Logic = "AND";
            }

            return lstFilterObjs;
        }


        /// <summary>
        /// 获取当前用户对某个具体业务对象拥有的权限信息,
        /// 多用于UI页面的菜单按钮显示控制
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns>拥有权限的权限项信息</returns>
        public List<string> HaveBizPermitInfo(UserContext ctx, string formId)
        { 
            return PermHelper.HaveBizPermitInfo (ctx,formId);
        }

        /// <summary>
        /// 获取不可见的字段列表（无权限）
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        public IEnumerable<string> GetInvisibleFieldByFormId(UserContext ctx, string formId)
        {
            if (ctx.IsTopOrg && IsRoleAdmin(ctx, new PermAuth(ctx)))
            {
                //如果是管理员，则不设置字段权限
                return new string[] { };
            }

            return PermHelper.GetFieldACLPermDataByFormId(ctx, formId);
        }

        /// <summary>
        /// 检查某个角色是否有配置过权限
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        public bool IsRolePermissionSet(UserContext userCtx, string roleId)
        {
            //角色功能权限设置
            var have = PermHelper.IsRolePermissionSet(userCtx, roleId);

            return have;
        }


        /// <summary>
        /// 获取系统发布的业务对象信息（主要用于授权相关功能）
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        public List<Tuple<string, List<HtmlPermItem>, string, string>> GetBizObjPermitItem(UserContext ctx)
        {
            return PermHelper.GetBizObjPermitItem(ctx);
        }



        /// <summary>
        /// 获取角色关联的权限
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="roleId">角色</param>
        /// <returns>用户列表 key：用户id，value：用户名  </returns>
        public   Dictionary<string, string> GetRoleUser(UserContext ctx, string roleId)
        {
            return PermHelper.GetRoleUser(ctx, roleId);
        }


        /// <summary>
        /// 更新用户档案上的角色字段(在角色授权中关联用户或者导入角色用户时调用)
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="roleId"></param>
        public void UpdateUserRoleFldInfo(UserContext ctx, string roleId)
        {
            PermHelper.UpdateUserRoleFldInfo(ctx, roleId);
        }

        #region 获取特定角色关联的权限详情
        /// <summary>
        /// 获取角色权限数据
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="roleId"></param>
        /// <param name="fromRoleAuth">是否来源于角色授权的调用</param>
        /// <returns></returns>
        public RolePermitInfo GetRolePermission(UserContext userCtx, string roleId, bool fromRoleAuth = false, string billformid = "")
        { 
            //非总部用户，则获取总部给当前用户所属经销商企业管理员的权限信息（分级授权）
            var canSetPermData = PermHelper.GetLevelAdminCanSetPermDataInfo(userCtx);
             
            //角色关联的功能权限设置
            var authRoleFuncAcl = PermHelper.GetFuncACLPermDataByRole(userCtx, roleId);

            //角色字段权限设置
            var authRoleFldAcl = PermHelper.GetFieldACLPermDataByRole(userCtx, roleId);

            //角色的数据行权限设置
            var authRoleDataRowAcl = PermHelper.GetDataRowACLPermDataByRole(userCtx, roleId);

            //业务对象启用的权限项
            var authBizObjPermit = PermHelper.GetBizObjPermitItem(userCtx, billformid);

            //角色关联的用户
            var authRoleUsers = PermHelper.GetRoleUser(userCtx, roleId, fromRoleAuth);

            RolePermitInfo auth = BuildRolePermission(userCtx,
                                                    roleId,
                                                    authRoleFuncAcl,
                                                    authBizObjPermit,
                                                    authRoleUsers,
                                                    authRoleFldAcl,
                                                    authRoleDataRowAcl,
                                                    canSetPermData);
            return auth;
        }



        private RolePermitInfo BuildRolePermission(UserContext userCtx,
                                    string roleId,
                                    Dictionary<string, int> authRoleFuncAcl,
                                    List<Tuple<string, List<HtmlPermItem>, string, string>> authBizObjPermit,
                                    Dictionary<string, string> authRoleUsers,
                                    Dictionary<string, int> authRoleFldAcl,
                                    Dictionary<string, DataRowAuthInfo> authRoleDataRowAcl,
                                    Dictionary<string, int> canSetPermData)
        {
            //业务模块
            var group = authBizObjPermit.Select(o => o.Item1).Distinct();
            RolePermitInfo auth = new RolePermitInfo();
            auth.RoleId = roleId;
            auth.RoleUsers = authRoleUsers;
            auth.CompanyId = userCtx.Company;
            auth.CompanyName = userCtx.Companys.FirstOrDefault(f => f.CompanyId.EqualsIgnoreCase(userCtx.Company))?.CompanyName;

            foreach (var item in group)
            {
                var module = new MdlPermitInfo();
                module.ModuleId = item ?? "未知模块";
                module.ModuleName = item ?? "未知模块";

                BuildModuleBizObj(userCtx,
                                module,
                                authBizObjPermit,
                                authRoleFuncAcl,
                                authRoleUsers,
                                authRoleFldAcl,
                                authRoleDataRowAcl,
                                canSetPermData);

                //module.BizObjPermission.Sort(new Comparison<BizObjPermitInfo>((x, y) =>
                //{
                //    return string.Compare(x.BizObjName, y.BizObjName);
                //}));

                if (module.BizObjPermission.Count > 0)
                {
                    auth.MdlPermission.Add(module);
                }
            }


            //没有业务对象的模块去掉
            auth.MdlPermission = auth.MdlPermission.Where(f => f.BizObjPermission.Count > 0).ToList();
             
            return auth;
        }

        private void BuildModuleBizObj(UserContext userCtx,
                                        MdlPermitInfo module,
                                        List<Tuple<string, List<HtmlPermItem>, string, string>> authBizObjPermit,
                                        Dictionary<string, int> authRoleFuncAcl,
                                        Dictionary<string, string> authRoleUsers,
                                        Dictionary<string, int> authRoleFldAcl,
                                        Dictionary<string, DataRowAuthInfo> authRoleDataRowAcl,
                                        Dictionary<string, int> canSetPermData)
        {
            var allFormPerm = authBizObjPermit.Where(o => o.Item1.EqualsIgnoreCase(module.ModuleId)
                            || o.Item1.IsNullOrEmptyOrWhiteSpace() && module.ModuleId.EqualsIgnoreCase("未知模块"));
            //权限对象
            foreach (var bizObj in allFormPerm)
            {
                var pItems = bizObj.Item2;
                if (pItems == null || pItems.Count == 0)
                {
                    continue;
                }
                 
                BizObjPermitInfo bizObjAuth = new BizObjPermitInfo();
                bizObjAuth.BizObjId = bizObj.Item4;
                bizObjAuth.BizObjName = bizObj.Item3;

                //权限项：功能权限
                BuildFuncACL(userCtx, authRoleFuncAcl, pItems, bizObjAuth, canSetPermData);

                if(!bizObjAuth.FucntionACL .Any ())
                {
                    continue;
                }
                //字段权限
                BuildFieldACL(userCtx, authRoleFldAcl, bizObj.Item4, bizObjAuth);

                //数据行权限
                BuildDataRowACL(authRoleDataRowAcl, bizObj.Item4, bizObjAuth);

                module.BizObjPermission.Add(bizObjAuth);
            }
        }

        private static void BuildFuncACL(UserContext userCtx, Dictionary<string, int> authRolePermit,
                                        List<HtmlPermItem> pItems,BizObjPermitInfo bizObjAuth, Dictionary<string, int> canSetPermData)
        {
            var str = bizObjAuth.BizObjId + ":";
            var bizObjCan = canSetPermData.Where(f => f.Key.StartsWith(str) && f.Value == 1).ToList();
            pItems = pItems.OrderBy(f => f.Order).ThenBy(f => f.Caption).ToList();
            foreach (var item in pItems)
            {
                var key = $"{bizObjAuth.BizObjId}:{item.Id}";

                if (userCtx.IsTopOrg == false && !bizObjCan.Any(f => f.Key.EqualsIgnoreCase(key) && f.Value == 1))
                {
                    //总部没有给权限的
                    continue;
                }

                PermitItemAuthInfo authItem = new PermitItemAuthInfo();
                authItem.ItemId = item.Id;
                authItem.ItemName = item.Caption;
                
                //角色上是否设置了权限
                if (authRolePermit.ContainsKey(key))
                {
                    authItem.IsRefuse = authRolePermit[key] == -1;
                    authItem.IsAllow = authRolePermit[key] == 1;
                }

                bizObjAuth.FucntionACL.Add(authItem);
            }
        }

        private void BuildDataRowACL(Dictionary<string, DataRowAuthInfo> authRoleDataRowAcl,
                                    string bizObjId,
                                    BizObjPermitInfo bizObjAuth,
                                    HtmlPermItem permItem = null)
        {
            //默认行权限挂在查看权限项对应的操作上
            var permId = "fw_view";
            if (permItem != null)
            {
                permId = permItem.Id;
            }
            List<DataRowAuthInfo> lstRowACL = new List<DataRowAuthInfo>();
            DataRowAuthInfo rowAuthInfo;
            if (authRoleDataRowAcl.TryGetValue(bizObjId, out rowAuthInfo) && rowAuthInfo != null)
            {
                lstRowACL.Add(rowAuthInfo);
            }

            bizObjAuth.DataRowACL[permId] = lstRowACL;
        }

        private void BuildFieldACL(UserContext userCtx,
                                        Dictionary<string, int> authRoleFldAcl,
                                        string bizObjId,
                                        BizObjPermitInfo bizObjAuth)
        {
            try
            {
                HtmlForm meta = meta = HtmlParser.LoadFormMetaFromCache(bizObjId, userCtx);
                var flds = meta.GetFieldList().Where(f => f.FieldPermit == 1).ToList();
                if (flds == null || flds.Count == 0)
                {
                    return;
                }

                foreach (var item in flds)
                {
                    var key = string.Format("{0}:{1}", bizObjId, item.Id);
                    FieldAuthInfo x = new FieldAuthInfo();
                    x.FieldId = item.Id;
                    x.FieldName = item.Caption;
                    //默认都是可见，可编辑
                    x.Visible = true;
                    x.Modify = true;
                    if (authRoleFldAcl.ContainsKey(key))
                    {
                        //含位1，表示不可见
                        x.Visible = !((authRoleFldAcl[key] & 1) == 1);
                        //含位2表示不可编辑
                        x.Modify = !((authRoleFldAcl[key] & 2) == 2);
                    }

                    bizObjAuth.FieldACL.Add(x);
                }
            }
            catch (Exception)
            {
                //模型不存在
            }
        }



         
        #endregion
    }
}
