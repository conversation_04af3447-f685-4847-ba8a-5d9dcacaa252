using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm;

namespace JieNor.Framework.AppService.SystemPlugIn.Auth
{
    /// <summary>
    /// 重置密码操作:需要检查权限，当成普通操作权限来控制
    /// </summary>
    [InjectService]
    [FormId("sec_user")]
    [OperationNo("resetpwd")]
    public class ResetPassword : AbstractOperationServicePlugIn
    {
        public override void OnCheckPermssion(OnCheckPermssionArgs e)
        {
            base.OnCheckPermssion(e);
            //重置密码
            e.PermItem = "fw_resetpwd";
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null) return;

            var userService = this.Container.GetService<IUserRegisterService>();
            var defPwd = this.GetQueryOrSimpleParam<string>("password");
            var lstUsers = e.DataEntitys.Select(o => o["fnumber"] as string)
                .Where(o => !o.IsNullOrEmptyOrWhiteSpace())
                .ToArray();

            if (defPwd.IsNullOrEmptyOrWhiteSpace()) defPwd = "888888";

            var option = OperateOption.Create();
            option.SetVariableValue("isSysReset", true);

            var result = userService.ResetUserPassword(this.Context, lstUsers.Distinct(), defPwd, option);

            this.Result.MergeResult(result);

            this.Result.SimpleMessage = "重置密码成功！";
        }
    }
}
