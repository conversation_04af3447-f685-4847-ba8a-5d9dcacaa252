using JieNor.Framework.DataTransferObject.Permission;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using System.Collections;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.DataEntity.MainFw;
using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.AppService.SystemPlugIn.MainFw;
using JieNor.Framework.MetaCore.PermData;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface.UsrMgr;
using System.Data;

namespace JieNor.Framework.AppService.SystemPlugIn.Auth
{
    public static partial class PermHelper
    {

        public const string CST_CacheKey_BizDataRowACL = "sec:DRACL:{0}:{1}:{2}";


        public const string CST_CacheKey_BizFuncACL = "sec:menu:{0}:{1}:{2}";

        public const string CST_CacheKey_FieldACLData = "sec:fieldacl:{0}:{1}:{2}";

        public const string CST_CacheKey_BizViewRow = "sec:vrole:{0}:{1}";

        /// <summary>
        /// 系统运维角色编码
        /// </summary>
        public const string DevOpsRoleNumber = "Admin_DevOps";

        /// <summary>
        /// 系统运维角色ID
        /// </summary>
        private static string DevOpsRoleId = "";

        public static bool haveUpdateSchema = false;

        #region 功能权限


        /// <summary>
        /// 获取角色关联的功能权限项信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="roleId"></param>
        internal static Dictionary<string, int> GetFuncACLPermDataByRole(UserContext ctx, string roleId)
        {
            Dictionary<string, int> auth = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            string strSql = $@" 
                                select distinct fbizobjid,fpermititemid,fallow,frefuse,t0.fcompanyid 
                                from t_sec_rolefuncacl t0  with(nolock) 
                                where t0.froleId=@froleId  
                                    and (t0.fcompanyid=@fcompanyid or t0.fcompanyid='0')
                                order by t0.fcompanyid asc ";
            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@froleId", System.Data.DbType.String,roleId),
                new SqlParam("@fcompanyid", System.Data.DbType.String,ctx.Company),
            };
            var svc = ctx.Container.GetService<IDBService>();
            using (var reader = svc.ExecuteReader(ctx, strSql, lstSqlParas))
            {
                while (reader.Read())
                {
                    string bizObjId = "";
                    string permitItemId = "";
                    try
                    {
                        bizObjId = reader.GetValue<string>("fbizobjid");
                        permitItemId = reader.GetValue<string>("fpermititemid");
                        var allow = reader.GetValue<bool>("fallow");
                        var refuse = reader.GetValue<bool>("frefuse");
                        var key2 = GetBizPermitKey(bizObjId, permitItemId);
                        if (refuse)
                        {
                            //拒绝
                            auth[key2] = -1;
                        }
                        else if (allow)
                        {
                            //有权
                            auth[key2] = 1;
                        }
                        else
                        {
                            //无权
                            auth[key2] = 0;
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new Exception($"组织id:[{ctx.Company}],业务对象id:[{bizObjId}],权限项id:[{permitItemId}] 数据有误，出错原因：" + ex.ToString());
                    }
                }
            }
            return auth;
        }



        /// <summary>
        /// 检查某个角色是否有配置过权限
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="roleId"></param>
        internal static bool IsRolePermissionSet(UserContext ctx, string roleId)
        {
            string strSql = $@" select top 1  fbizobjid,fpermititemid,fallow,frefuse,t0.fcompanyid 
                                from t_sec_rolefuncacl t0  with(nolock) 
                                where fallow='1' and t0.froleId=@froleId and (t0.fcompanyid=@fcompanyid or t0.fcompanyid='0')
                                 ";
            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@froleId", System.Data.DbType.String,roleId),
                new SqlParam("@fcompanyid", System.Data.DbType.String,ctx.Company),
            };
            var svc = ctx.Container.GetService<IDBService>();
            var datas = svc.ExecuteDynamicObject(ctx, strSql, lstSqlParas);

            return datas != null && datas.Count > 0;
        }





        /// <summary>
        /// 获取总部给当前用户所属经销商企业管理员的权限信息（分级管理员授权信息）
        /// </summary>
        /// <param name="ctx"></param> 
        /// <returns></returns>
        internal static Dictionary<string, int> GetLevelAdminCanSetPermDataInfo(UserContext ctx)
        {
            Dictionary<string, int> auth = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            if (ctx.IsTopOrg)
            {
                //总部用户
                return auth;
            }

            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@topcompanyid", System.Data.DbType.String,ctx.TopCompanyId),
                new SqlParam("@companyid", System.Data.DbType.String,ctx.Company),
            };

            var strSql = @"SELECT  distinct t0.fbizobjid, t0.fpermititemid, t0.fallow, t0.frefuse, t0.fcompanyid 
                            FROM t_sec_rolefuncacl t0 WITH (NOLOCK) 
                            where exists ( select 1 from t_sec_role t1 WITH (NOLOCK) 
				                            INNER JOIN t_sec_roleuser t2 WITH (NOLOCK) ON t0.froleId = t2.froleId 
				                            INNER JOIN t_sec_user t3 WITH (NOLOCK) ON t2.fuserid = t3.fid 
				                            WHERE t1.fmainorgid = @topcompanyid  /*经销商的管理员角色在总部*/
                                                  AND t3.fmainorgid = @companyid  /*经销商的管理员用户在经销商企业里面*/ 
                                                and t0.froleId = t1.fid  and t1.fnumber<>'Admin_DevOps' 
                                         )
                            ORDER BY t0.fbizobjid ASC, t0.fpermititemid ASC 
                           ";
            var svc = ctx.Container.GetService<IDBService>();
            var datas = svc.ExecuteDynamicObject(ctx, strSql, lstSqlParas);
            var grp = datas.GroupBy(f => new { fbizobjid = Convert.ToString(f["fbizobjid"]), fpermititemid = Convert.ToString(f["fpermititemid"]) }).ToList();
            foreach (var item in grp)
            {
                string bizObjId = item.Key.fbizobjid;
                string permitItemId = item.Key.fpermititemid;
                if (bizObjId.IsNullOrEmptyOrWhiteSpace() || permitItemId.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                var key2 = GetBizPermitKey(bizObjId, permitItemId);
                try
                {
                    var allow = item.ToList().Any(f => Convert.ToString(f["fallow"]) == "1"); //任意一个角色有权
                    var refuse = item.ToList().Any(f => Convert.ToString(f["frefuse"]) == "1");//任意一个角色拒绝                    
                    if (refuse)
                    {
                        //拒绝
                        auth[key2] = -1;
                    }
                    else if (allow)
                    {
                        //有权
                        auth[key2] = 1;
                    }
                    else
                    {
                        //无权
                        auth[key2] = 0;
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"组织id:[{ctx.Company}],业务对象id:[{bizObjId}],权限项id:[{permitItemId}] 数据有误，出错原因：" + ex.ToString());
                }
            }

            return auth;
        }


        /// <summary>
        /// 获取redis缓存服务
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        internal static IRedisCache GetRedisCacheSvc(UserContext ctx)
        {
            var redisCache = ctx.Container.GetService<IRedisCache>();
            (redisCache as ISupportRegion).SetRegionId("Permission");

            return redisCache;
        }

        /// <summary>
        /// 获取指定业务对象的字段权限项信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        internal static IEnumerable<string> GetFieldACLPermDataByFormId(UserContext ctx, string formId)
        { 
            var cacheKey = CST_CacheKey_FieldACLData.Fmt(ctx.Company, ctx.UserId, formId);
            var redisCache = GetRedisCacheSvc(ctx);
            try
            {
                var cacheData = redisCache.Get<IEnumerable<string>>(cacheKey);
                if (cacheData?.Any() == true)
                {
                    return cacheData;
                }
            }
            catch (Exception ex)
            {
                //忽略redis缓存取数错误
            }

            List<string> lstNonvisibleFields = new List<string>();
            string strSql = $@" select distinct ffieldid,t0.fcompanyid
                                from t_sec_rolefieldacl t0 with(nolock) 
                                inner join t_sec_roleuser t1 with(nolock) on t0.froleid=t1.froleid
                                where t0.fvisible='0' 
                                    and t0.fbizobjid=@fformid  
                                    and (t0.fcompanyid=@fcompanyid or t0.fcompanyid='0' or t0.fcompanyid=@ftoporgid)
                                    and t1.fuserid=@fuserid ";
            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@fformid", System.Data.DbType.String,formId),
                new SqlParam("@fcompanyid", System.Data.DbType.String,ctx.Company),
                new SqlParam("@ftoporgid", System.Data.DbType.String,ctx.TopCompanyId),//经销商管理员角色在总部组织下
                new SqlParam("@fuserid", System.Data.DbType.String,ctx.UserId),
            };
            var svc = ctx.Container.GetService<IDBService>();
            using (var reader = svc.ExecuteReader(ctx, strSql, lstSqlParas))
            {
                while (reader.Read())
                {
                    var fieldId = reader.GetValue<string>("ffieldid");
                    if (!fieldId.IsNullOrEmptyOrWhiteSpace()
                        && !lstNonvisibleFields.Contains(fieldId, StringComparer.OrdinalIgnoreCase))
                        lstNonvisibleFields.Add(fieldId);
                }
            }

            //避免缓存失效
            if(lstNonvisibleFields.Any() == false)
            {
                lstNonvisibleFields.Add("#");
            }

            redisCache.Set(cacheKey, lstNonvisibleFields);
            return lstNonvisibleFields;
        }


        /// <summary>
        /// 获取角色的字段权限项信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="roleId"></param>
        internal static Dictionary<string, int> GetFieldACLPermDataByRole(UserContext ctx, string roleId)
        {
            Dictionary<string, int> auth = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            string strSql = $@" 
                            select distinct fbizobjid,ffieldid,fvisible,fmodify,t0.fcompanyid
                            from t_sec_rolefieldacl t0  with(nolock) 
                            where t0.froleId=@froleId   
                            and (t0.fcompanyid=@fcompanyid or t0.fcompanyid='0')
                            order by t0.fcompanyid asc
                            ";
            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@froleId", System.Data.DbType.String,roleId),
                new SqlParam("@fcompanyid", System.Data.DbType.String,ctx.Company),
            };
            var svc = ctx.Container.GetService<IDBService>();
            using (var reader = svc.ExecuteReader(ctx, strSql, lstSqlParas))
            {
                while (reader.Read())
                {
                    var bizObjId = reader.GetValue<string>("fbizobjid");
                    var fieldId = reader.GetValue<string>("ffieldid");
                    var visible = reader.GetValue<bool>("fvisible");
                    var modify = reader.GetValue<bool>("fmodify");
                    var key2 = GetBizPermitKey(bizObjId, fieldId);
                    if (!modify)
                    {
                        //可编辑
                        auth[key2] = 3;
                    }
                    else if (!visible)
                    {
                        //可见
                        auth[key2] = 1;
                    }
                    else
                    {
                        //可见，可编辑
                        auth[key2] = 0;
                    }
                }
            }
            return auth;
        }

        /// <summary>
        /// 根据角色获取数据行授权数据
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="roleId"></param>
        /// <returns></returns>
        internal static Dictionary<string, DataRowAuthInfo> GetDataRowACLPermDataByRole(UserContext ctx, string roleId)
        {
            Dictionary<string, DataRowAuthInfo> auth = new Dictionary<string, DataRowAuthInfo>(StringComparer.OrdinalIgnoreCase);
            string strSql = $@" 
                            select distinct froleId,fbizobjid,ffiltertype,fexpress,fdesc,fsuperiorperm,t0.fcompanyid,t0.fbdfldfilter
                            from t_sec_roledatarowacl t0  with(nolock) 
                            where t0.froleId=@froleId 
                                and (t0.fcompanyid=@fcompanyid or t0.fcompanyid='0')
                            order by t0.fcompanyid asc ";
            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@froleId", System.Data.DbType.String,roleId),
                new SqlParam("@fcompanyid", System.Data.DbType.String,ctx.Company),
            };
            var svc = ctx.Container.GetService<IDBService>();
            using (var reader = svc.ExecuteReader(ctx, strSql, lstSqlParas))
            {
                while (reader.Read())
                {
                    var bizObjId = reader.GetValue<string>("fbizobjid");
                    var authInfo = new DataRowAuthInfo()
                    {
                        RoleId = reader.GetValue<string>("froleid"),
                        FormId = bizObjId,
                        FilterType = reader.GetValue<string>("ffiltertype"),
                        Express = reader.GetValue<string>("fexpress"),
                        Desc = reader.GetValue<string>("fdesc"),
                        SuperiorPerm = reader.GetValue<bool>("fsuperiorperm")
                    };

                    var bdFilter = reader.GetValue<string>("fbdfldfilter");
                    if (!bdFilter.IsNullOrEmptyOrWhiteSpace())
                    {
                        authInfo.BDFldsAuthInfo = bdFilter.FromJson<List<BDFldDataRowAuthInfo>>();
                    }
                    auth[bizObjId] = authInfo;
                }
            }
            return auth;
        }

        /// <summary>
        /// 获取拥有查看权限的角色
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        internal static List<string> GetHasViewPermUserRoles(UserContext ctx, string formId)
        {
            var result = new List<string>();
            if (IsEmptyUser(ctx))
            {
                return result;
            }
             
            var svc = ctx.Container.GetService<IDBService>();
            var sql = @"select ru.froleid from t_sec_roleuser ru with(nolock) 
                        inner join t_sec_rolefuncacl acl with(nolock) on ru.froleid=acl.froleid 
                            and acl.fbizobjid='{1}' 
                            and acl.fpermititemid='fw_view' 
                            and acl.fallow=1 
                            and acl.frefuse=0
                        where ru.fuserid='{0}' ".Fmt(ctx.UserId, formId);
            var datas = svc.ExecuteDynamicObject(ctx, sql);
            if (datas != null && datas.Count > 0)
            {
                result = datas.Select(f => Convert.ToString(f["froleid"])).Distinct().ToList();
            }
             
            return result;
        }

        /// <summary>
        /// 获取业务对象启用的权限项信息
        /// </summary>
        /// <param name="ctx"></param>
        internal static List<Tuple<string, List<HtmlPermItem>, string, string>> GetBizObjPermitItem(UserContext ctx, string billformid = "")
        {
            List<Tuple<string, List<HtmlPermItem>, string, string>> auth = new List<Tuple<string, List<HtmlPermItem>, string, string>>();
            var whereBillForm = "";
            var billFormParam = new SqlParam("@fbillformid", System.Data.DbType.String, billformid);
            if (!string.IsNullOrEmpty(billformid))
            {
                whereBillForm = " and t0.fbillformid=@fbillformid";
            }
            string strSql = $@" 
            select *,(select fparameter from t_sys_menuitem  with(nolock) where t.fmenuid=fmenuid) fparameter from 
            (
	            select distinct t0.fbillformid
	            ,t2.fname fmodulename
	            ,t2.fmoduleid
	            ,t2.forder as forder2
	            ,t1.fgroupid
	            ,t1.forder as forder1
	            ,t0.fmenuid
	            ,t0.forder as forder0 
	            from t_sys_menuitem t0  with(nolock) 
	            inner join t_sys_menugroup t1  with(nolock) on t0.fgroupid=t1.fgroupid 
	            inner join t_sys_bizmodule t2  with(nolock) on t1.fmoduleid=t2.fmoduleid 
	            where t0.fenablerac='1' {whereBillForm}
            ) t 
            order by forder2,fmoduleid,forder1,fgroupid,forder0,fmenuid";

            var svc = ctx.Container.GetService<IDBService>();
            var metaService = ctx.Container.GetService<IMetaModelService>();
            List<Dictionary<string, object>> lstFormObjs = new List<Dictionary<string, object>>();
            using (var reader = svc.ExecuteReader(ctx, strSql, billFormParam))
            {
                while (reader.Read())
                {
                    Dictionary<string, object> dctFormObj = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
                    dctFormObj["formId"] = reader.GetValue<string>("fbillformid");
                    dctFormObj["moduleName"] = reader.GetString("fmodulename");
                    dctFormObj["parameter"] = reader.GetString("fparameter");
                    lstFormObjs.Add(dctFormObj);
                }
            }
            foreach (var formMetaObj in lstFormObjs)
            {
                try
                {
                    var bizObjId = Convert.ToString(formMetaObj.GetValue("formId", ""));
                    if (bizObjId.IsNullOrEmptyOrWhiteSpace()) continue;

                    var formMeta = metaService.LoadFormModel(ctx, bizObjId);
                    var formCaption = formMeta.Caption;

                    //菜单参数：获取菜单参数中的 formCaption，保证菜单与角色授权中的业务对象名称一致
                    var menuParamStr = Convert.ToString(formMetaObj.GetValue("parameter", ""));
                    if (!menuParamStr.IsNullOrEmptyOrWhiteSpace())
                    {
                        object _formCaption = null;
                        var menuParam = ("{" + menuParamStr + "}").FromJson<Dictionary<string, object>>();
                        menuParam?.TryGetValue("formCaption", out _formCaption);
                        if (!_formCaption.IsNullOrEmptyOrWhiteSpace())
                        {
                            formCaption = Convert.ToString(_formCaption);
                        }
                    }

                    if (!formMeta.EnableRAC || formMeta.FormPermItems.Any() == false)
                    {
                        ctx.Container?.GetService<ILogServiceEx>()?.Debug($"业务对象【{formCaption}】启用权限控制，但模型中并未配置任何权限项！");
                        continue;
                    }
                    var moduleName = Convert.ToString(formMetaObj.GetValue("moduleName", ""));
                    if (moduleName.IsNullOrEmptyOrWhiteSpace())
                    {
                        moduleName = formMeta.ModuleName;
                    }

                    // TODO  这里的 formMeta.FormPermItems 包含了所有的，实际应该按 业务单据上定义的菜单按钮来
                    //if (formMeta.FormOperations != null)
                    //{
                    //    List<HtmlPermItem> beRemove = new List<HtmlPermItem>();
                    //    foreach (var item in formMeta.FormPermItems)
                    //    { 
                    //        if (formMeta.Any(f =>f.IsVisible (HtmlElementVisibleScene. && f.PermItemId.EqualsIgnoreCase(item.Id)))
                    //        {

                    //        }
                    //    }
                    //}

                    var permItems = formMeta.FormPermItems;
                    permItems.Sort(new Comparison<HtmlPermItem>((x, y) => x.Order.CompareTo(y.Order)));
                    auth.Add(Tuple.Create(moduleName, permItems, formCaption, formMeta.Id));
                }
                catch (Exception ex)
                {
                    ctx.Container?.GetService<ILogServiceEx>()?.Error($"业务对象【{formMetaObj.GetValue("formId", "")}】模型文件不存在！", ex);
                    //吃掉异常，
                }
            }

            return auth;
        }

        /// <summary>
        /// 获取角色所包含的用户信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="roleId"></param>
        /// <param name="fromRoleAuth">是否来源于角色授权的调用</param>
        internal static Dictionary<string, string> GetRoleUser(UserContext ctx, string roleId, bool fromRoleAuth = false)
        {
            Dictionary<string, string> roles = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            string strSql = $@" select distinct t0.fuserid,t1.fname 
                                from t_sec_roleuser t0  with(nolock) 
                                inner join t_sec_user t1  with(nolock) on t1.fid=t0.fuserid 
                                inner join T_BAS_ORGANIZATION x  with(nolock) on t1.fmainorgid=x.fid
                                where t0.froleId=@froleId and x.ftopcompanyid=@ftoporgid ";

            if (fromRoleAuth)
            {
                // 如果是系统运维角色，则只加载总部运维用户
                if (IsDevOpsRole(ctx, roleId))
                {
                    strSql = $@"
                    select distinct t0.fuserid,t1.fname 
                    from t_sec_roleuser t0 with(nolock) 
                    inner join t_sec_user t1 with(nolock) on t1.fid=t0.fuserid 
                    where t0.froleId=@froleId and t1.fmainorgid=@ftoporgid";
                }
            }

            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@froleId", System.Data.DbType.String,roleId),
                new SqlParam("@ftoporgid", System.Data.DbType.String,ctx.TopCompanyId),
            };
            var svc = ctx.Container.GetService<IDBService>();
            using (var reader = svc.ExecuteReader(ctx, strSql, lstSqlParas))
            {
                while (reader.Read())
                {
                    var id = reader.GetValue<string>("fuserid");
                    var name = reader.GetValue<string>("fname");
                    roles[id] = name;
                }
            }
            return roles;
        }

        /// <summary>
        /// 判断指定的角色ID是否是运维角色
        /// </summary>
        /// <param name="ctx">上下文</param>
        /// <param name="roleId">角色ID</param>
        /// <returns>是否是运维角色</returns>
        private static bool IsDevOpsRole(UserContext ctx, string roleId)
        {
            if (!DevOpsRoleId.IsNullOrEmptyOrWhiteSpace())
            {
                return DevOpsRoleId.EqualsIgnoreCase(roleId);
            }

            var roleNumber = "";
            var sqlText = "select fnumber from t_sec_role with(nolock) where fid=@roleId";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@roleId", System.Data.DbType.String, roleId)
            };
            var dbService = ctx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(ctx, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    roleNumber = reader.GetValueToString("fnumber");
                }
            }

            if (!roleNumber.IsNullOrEmptyOrWhiteSpace()
                && roleNumber.EqualsIgnoreCase(DevOpsRoleNumber))
            {
                DevOpsRoleId = roleId;

                return true;
            }

            return false;
        }

        /// <summary>
        /// 获取角色所属组织的组织类型信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="roleId"></param>
        internal static string GetRoleOrgTypeInfo(UserContext ctx, string roleId)
        {
            Dictionary<string, string> orgInfos = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            string strSql = $@" select t1.fid,t1.forgtype
                                from t_sec_role t0  with(nolock) 
                                inner join t_bas_organization t1  with(nolock) on t1.fid=t0.fbizorgid 
                                where t0.fid=@froleId ";

            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@froleId", System.Data.DbType.String,roleId),
            };
            var svc = ctx.Container.GetService<IDBService>();
            var data = svc.ExecuteDynamicObject(ctx, strSql, lstSqlParas);
            if (data.Count == 0)
            {
                return "";
            }

            return data[0]["forgtype"]?.ToString();
        }


        /// <summary>
        /// 清空缓存
        /// </summary>
        /// <param name="ctx"></param>
        internal static void ClearCache(UserContext ctx)
        {
            Task.Run(() =>
            {
                var redisCache = GetRedisCacheSvc(ctx);

                redisCache.RemoveAllByKeyPattern("sec:DRACL:", Enu_SearchType.StartWith);
                redisCache.RemoveAllByKeyPattern("sec:menu:", Enu_SearchType.StartWith);
                redisCache.RemoveAllByKeyPattern("funcperm", Enu_SearchType.StartWith);
                redisCache.RemoveAllByKeyPattern("funcpermusers", Enu_SearchType.StartWith);
                redisCache.RemoveAllByKeyPattern("isadmin", Enu_SearchType.StartWith);
                redisCache.RemoveAllByKeyPattern("sec:fieldacl:", Enu_SearchType.StartWith);
                redisCache.RemoveAllByKeyPattern(MenuModuleService.__AllMenuCacheKey, Enu_SearchType.Contains);
                redisCache.RemoveAllByKeyPattern("isdevops", Enu_SearchType.StartWith);
                 
                //权限改变，清除各个页面上功能按钮的权限关联关系缓存
                MenuBarParser.ClearMenuCache(ctx, "");
            });
        }


        /// <summary>
        /// 获取当前用户对某个具体业务对象拥有的权限信息,
        /// 多用于UI页面的菜单按钮显示控制
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns>拥有权限的权限项信息</returns>
        internal static List<string> HaveBizPermitInfo(UserContext ctx, string formId)
        {
            var cacheKey = CST_CacheKey_BizFuncACL.Fmt(ctx.Company, ctx.UserId, formId);
            var redisCache = GetRedisCacheSvc(ctx);
            List<string> havPermit = null;
            try
            {
                havPermit = redisCache.Get<List<string>>(cacheKey);
            }
            catch (Exception ex)
            {
                //忽略redis缓存取数错误
            }
            
            if (havPermit != null && havPermit.Any())
            {
                return havPermit;
            }

            var strSql = @" select distinct t0.fpermititemid,t0.fallow,t0.frefuse
                            from t_sec_rolefuncacl t0 with(nolock) 
                            inner join t_sec_roleuser t1  with(nolock) on t0.froleid=t1.froleid
                            inner join t_sec_user t2  with(nolock) on t1.fuserid=t2.fid
                            where  (t0.fcompanyid=@fcompanyid or t0.fcompanyid=@ftoporgid )
                                    and t0.fbizobjid=@fbizobjid
                                    and t0.fallow='1' and t2.fid=@fuserid
                            ";
            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@fcompanyid", System.Data.DbType.String,ctx.Company),
                new SqlParam("@ftoporgid", System.Data.DbType.String,ctx.TopCompanyId),//经销商管理员角色在总部组织下
                new SqlParam("@fbizobjid", System.Data.DbType.String,formId),
                new SqlParam("@fuserid", System.Data.DbType.String,ctx.UserId),
            };

            var dbService = ctx.Container.GetService<IDBService>();
            var datas = dbService.ExecuteDynamicObject(ctx, strSql, lstSqlParas);

            havPermit = new List<string>();
            var grp = (from p in datas
                       select p["fpermititemid"].ToString()).Distinct().ToList();
            foreach (var item in grp)
            {
                if (datas.Any(f => f["frefuse"] != null && f["frefuse"].ToString() == "1"))
                {
                    //有任意一个角色设置拒绝，则认为没有权限
                    continue;
                }
                havPermit.Add(item);
            }

            //缓存起来
            redisCache.Set(cacheKey, havPermit);

            return havPermit;
        }



        #endregion 功能权限


        #region 保存角色权限数据
        /// <summary>
        /// 保存角色权限设置
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="roleACLDetail"></param>
        internal static void SaveRolePermission(UserContext ctx, RolePermitInfo roleACLDetail, string bizObjId = "")
        {
            //删除原先的角色--用户映射
            if (!roleACLDetail.IgnoreRoleUser)
            {
                DeleteOldRoleUserMap(ctx, roleACLDetail);
            }

            //删除原先的角色功能权限设置
            DeleteOldFuncACL(ctx, roleACLDetail, bizObjId);

            //删除原先的角色字段权限设置
            DeleteOldFieldACL(ctx, roleACLDetail, bizObjId);

            //删除原先的角色数据行授权设置
            DeleteOldDataRowACL(ctx, roleACLDetail, bizObjId);

            //保存： 角色--用户映射
            if (!roleACLDetail.IgnoreRoleUser)
            {
                SaveNewRoleUserMap(ctx, roleACLDetail);
            }

            //保存：角色功能权限设置
            SaveNewFuncACL(ctx, roleACLDetail);

            //保存：角色字段权限设置
            SaveNewFieldACL(ctx, roleACLDetail);

            //保存：角色数据行授权设置
            SaveNewDataRowACL(ctx, roleACLDetail);

            //更新用户档案上的角色字段
            UpdateUserRoleFldInfo(ctx, roleACLDetail);

            //大事务，异步执行，避免长时间阻塞
            Task.Run(() =>
            {
                var svc = ctx.Container.GetService<IPermissionEvent>();
                if (svc != null)
                {
                    svc.OnSaveRolePermission(ctx, roleACLDetail);
                    ClearCache(ctx);
                }
            });

            ClearCache(ctx);
        }


        /// <summary>
        /// 更新用户档案上的角色字段
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="roleACLDetail"></param>
        private static void UpdateUserRoleFldInfo(UserContext ctx, RolePermitInfo roleACLDetail)
        {
            if (roleACLDetail.IgnoreRoleUser)
            {
                return;
            }

            UpdateUserRoleFldInfo(ctx, roleACLDetail.RoleId);
        }


        internal static void UpdateUserRoleFldInfo(UserContext ctx, string roleId)
        {
            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@froleid", System.Data.DbType.String, roleId),
            };

            //找到拥有此角色的用户
            var sqlUser = @"SELECT DISTINCT
               u.fid, u.froleids, u.froleids_txt
        FROM T_SEC_ROLEUSER x WITH (NOLOCK)
		INNER JOIN T_SEC_USER u WITH (NOLOCK) ON x.fuserid = u.fid
        WHERE x.froleid = @froleid /*角色里面关联了用户的*/
                            ";
            //and y.fid in('{}')
            //if (roleUsers != null && roleUsers.Any())
            //{
            //    var userIds = roleUsers.Keys;
            //    sqlUser += sqlUser + $" and y.fid in('{string.Join("','", userIds)}')";
            //}
            //当前这些用户所关联的角色信息
            var sqlRoleUser = @"select DISTINCT a.fuserid,a.froleid, b.fname as frolename
                                from t_sec_roleuser a with (nolock)
                                inner join t_sec_role b with (nolock) on a.froleid =b.fid
                                where exists ( select 1 from ({0}) x where  a.fuserid =x.fid  ) 
                                order by a.fuserid,b.fname".Fmt(sqlUser);

            var dbSvc = ctx.Container.GetService<IDBService>();
            var needUpdateUsers = dbSvc.ExecuteDynamicObject(ctx, sqlUser, lstSqlParas)?.ToList();
            var roleUserDatas = dbSvc.ExecuteDynamicObject(ctx, sqlRoleUser, lstSqlParas).ToList();
            var grpDatas = roleUserDatas.GroupBy(f => Convert.ToString(f["fuserid"])).ToList();
            var join = (from x in needUpdateUsers
                        join y in grpDatas on Convert.ToString(x["fid"]) equals y.Key into temp
                        from z in temp.DefaultIfEmpty()
                        select new
                        {
                            userid = Convert.ToString(x["fid"]),
                            roleids = Convert.ToString(x["froleids"]),
                            roleuser = z?.ToList(),
                        }).ToList();
            var lstSql = new List<string>();
            join.ForEach(item =>
            {
                var roleuser = item.roleuser;
                if (roleuser != null && roleuser.Any())
                {
                    // 此用户的角色字段
                    var roleIdsFromUser = string.Join(",", Convert.ToString(item.roleids).Split(new[] { "," }, StringSplitOptions.RemoveEmptyEntries).Distinct().OrderBy(s => s));

                    // 此用户的用户角色表里的角色
                    var roleIdsFromRoleUser = string.Join(",", roleuser.Select(f => Convert.ToString(f["froleid"])).Distinct().OrderBy(s => s));

                    // 如果相等，表示不需要更新
                    if (!roleIdsFromUser.Equals(roleIdsFromRoleUser))
                    {
                        var froleids = string.Join(",", roleuser.Select(f => Convert.ToString(f["froleid"])).Distinct());
                        var froleids_txt = string.Join(",", roleuser.Select(f => Convert.ToString(f["frolename"])).Distinct());
                        lstSql.Add(@"update t_sec_user set froleids='{1}',froleids_txt='{2}' where fid='{0}' ".Fmt(item.userid, froleids, froleids_txt));
                    }
                }
                else
                {
                    lstSql.Add(@"update t_sec_user set froleids='',froleids_txt='' where fid='{0}' ".Fmt(item.userid));
                }
            });
            if (lstSql.Count > 0)
            {
                var svc = ctx.Container.GetService<IDBServiceEx>();
                svc.ExecuteBatch(ctx, lstSql);
            }
        }



        private static void SaveNewDataRowACL(UserContext ctx, RolePermitInfo roleACLDetail)
        {
            var metaType = HtmlParser.LoadFormMetaFromCache(AuthFormConst.Sec_RoleDataRowAcl, ctx).GetDynamicObjectType(ctx);
            var beSave = new List<DynamicObject>();
            foreach (var mdl in roleACLDetail.MdlPermission)
            {
                foreach (var bizObj in mdl.BizObjPermission)
                {
                    if (bizObj.DataRowACL.Any() == false) continue;

                    foreach (var filter in bizObj.DataRowACL.FirstOrDefault().Value)
                    {
                        var data = metaType.CreateInstance() as DynamicObject;
                        data["froleid"] = roleACLDetail.RoleId;
                        data["fcompanyid"] = roleACLDetail.CompanyId;
                        data["fcompanyname"] = roleACLDetail.CompanyName;
                        data["fbizobjid"] = bizObj.BizObjId;
                        data["fbizobjname"] = bizObj.BizObjName;
                        data["ffiltertype"] = filter.FilterType;
                        data["fexpress"] = filter.Express;
                        data["fdesc"] = filter.Desc;
                        data["fsuperiorperm"] = filter.SuperiorPerm; //上级查看下级数据权限
                        data["fbdfldfilter"] = filter.BDFldsAuthInfo?.ToJson();

                        beSave.Add(data);
                    }
                }
            }

            Save(ctx, metaType, beSave);
        }

        private static void SaveNewFieldACL(UserContext ctx, RolePermitInfo roleACLDetail)
        {
            var metaType = HtmlParser.LoadFormMetaFromCache(AuthFormConst.Sec_RoleFieldAcl, ctx).GetDynamicObjectType(ctx);
            var beSave = new List<DynamicObject>();
            foreach (var mdl in roleACLDetail.MdlPermission)
            {
                foreach (var bizObj in mdl.BizObjPermission)
                {
                    foreach (var fld in bizObj.FieldACL)
                    {
                        //默认行为不需要保存
                        if (fld.Visible && fld.Modify) continue;
                        var data = metaType.CreateInstance() as DynamicObject;
                        data["froleid"] = roleACLDetail.RoleId;
                        data["fcompanyid"] = roleACLDetail.CompanyId;
                        data["fcompanyname"] = roleACLDetail.CompanyName;
                        data["fbizobjid"] = bizObj.BizObjId;
                        data["fbizobjname"] = bizObj.BizObjName;
                        data["ffieldid"] = fld.FieldId;
                        data["ffieldname"] = fld.FieldName;
                        data["fvisible"] = fld.Visible;
                        data["fmodify"] = fld.Modify;

                        beSave.Add(data);
                    }
                }
            }

            Save(ctx, metaType, beSave);
        }

        private static void SaveNewFuncACL(UserContext ctx, RolePermitInfo roleACLDetail)
        {
            var metaType = HtmlParser.LoadFormMetaFromCache(AuthFormConst.Sec_RoleFuncAcl, ctx).GetDynamicObjectType(ctx);
            var beSave = new List<DynamicObject>();
            foreach (var mdl in roleACLDetail.MdlPermission)
            {
                foreach (var bizObj in mdl.BizObjPermission)
                {
                    foreach (var func in bizObj.FucntionACL)
                    {
                        if (func.IsAllow == false)
                        {
                            //没有授权的，就不需要保存到数据库中了，减少数据量
                            continue;
                        }

                        var data = metaType.CreateInstance() as DynamicObject;
                        data["froleid"] = roleACLDetail.RoleId;
                        data["fcompanyid"] = roleACLDetail.CompanyId;
                        data["fcompanyname"] = roleACLDetail.CompanyName;
                        data["fbizobjid"] = bizObj.BizObjId;
                        data["fbizobjname"] = bizObj.BizObjName;
                        data["fpermititemid"] = func.ItemId;
                        data["fallow"] = func.IsAllow;
                        data["frefuse"] = func.IsRefuse;

                        beSave.Add(data);
                    }
                }
            }

            Save(ctx, metaType, beSave);
        }

        private static void Save(UserContext ctx, DynamicObjectType metaType, List<DynamicObject> beSave)
        {
            if (beSave == null || beSave.Count == 0)
            {
                return;
            }

            var seqSrv = ctx.Container.GetService<IDataEntityPkService>();
            seqSrv.AutoSetPrimaryKey(ctx, beSave, metaType);
            IDataManager dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, metaType);

            var option = OperateOption.Create();
            option.SetBulkCopy(true);
            option.SetCacheData(false);
            dm.Save(beSave, null, option);
        }

        private static void SaveNewRoleUserMap(UserContext ctx, RolePermitInfo roleACLDetail)
        {
            var metaType = HtmlParser.LoadFormMetaFromCache(AuthFormConst.Sec_RoleUser, ctx).GetDynamicObjectType(ctx);
            var beSave = new List<DynamicObject>();
            foreach (var user in roleACLDetail.RoleUsers)
            {
                if (user.Key.IsNullOrEmptyOrWhiteSpace()) continue;
                var data = metaType.CreateInstance() as DynamicObject;
                data["froleid"] = roleACLDetail.RoleId;
                data["fuserid"] = user.Key;
                data["fformid"] = "sec_roleuser";
                beSave.Add(data);
            }

            Save(ctx, metaType, beSave);
        }



        private static void DeleteOldDataRowACL(UserContext ctx, RolePermitInfo roleACLDetail, string bizObjId = "")
        {
            string strSql = $@" delete from t_sec_roledatarowacl 
                                where froleId=@froleId 
                                    and fcompanyid=@fcompanyid   ";
            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@froleId", System.Data.DbType.String,roleACLDetail.RoleId  ),
                new SqlParam("@fcompanyid", System.Data.DbType.String,roleACLDetail.CompanyId ),
            };
            if (!string.IsNullOrEmpty(bizObjId))
            {
                strSql += " and fbizobjid=@fbizobjid ";
                lstSqlParas.Add(new SqlParam("@fbizobjid", System.Data.DbType.String, bizObjId));
            }
            List<object> beDel = new List<object>();
            var svc = ctx.Container.GetService<IDBServiceEx>();
            svc.Execute(ctx, strSql, lstSqlParas);
        }

        private static void DeleteOldFieldACL(UserContext ctx, RolePermitInfo roleACLDetail, string bizObjId = "")
        {
            string strSql = $@" delete from t_sec_rolefieldacl 
                                where froleId=@froleId 
                                    and fcompanyid=@fcompanyid   ";
            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@froleId", System.Data.DbType.String,roleACLDetail.RoleId  ),
                new SqlParam("@fcompanyid", System.Data.DbType.String,roleACLDetail.CompanyId ),
            };
            if (!string.IsNullOrEmpty(bizObjId))
            {
                strSql += " and fbizobjid=@fbizobjid ";
                lstSqlParas.Add(new SqlParam("@fbizobjid", System.Data.DbType.String, bizObjId));
            }
            List<object> beDel = new List<object>();
            var svc = ctx.Container.GetService<IDBServiceEx>();
            svc.Execute(ctx, strSql, lstSqlParas);
        }

        private static void DeleteOldFuncACL(UserContext ctx, RolePermitInfo roleACLDetail, string bizObjId = "")
        {
            string strSql = $@" delete from t_sec_rolefuncacl 
                                where froleId=@froleId 
                                    and fcompanyid=@fcompanyid   ";

            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@froleId", System.Data.DbType.String,roleACLDetail.RoleId  ),
                new SqlParam("@fcompanyid", System.Data.DbType.String,roleACLDetail.CompanyId ),
            };
            if (!string.IsNullOrEmpty(bizObjId))
            {
                strSql += " and fbizobjid=@fbizobjid ";
                lstSqlParas.Add(new SqlParam("@fbizobjid", System.Data.DbType.String, bizObjId));
            }
            List<object> beDel = new List<object>();
            var svc = ctx.Container.GetService<IDBServiceEx>();
            svc.Execute(ctx, strSql, lstSqlParas);
        }


        private static void DeleteOldRoleUserMap(UserContext ctx, RolePermitInfo roleACLDetail)
        {
            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@froleid", System.Data.DbType.String,roleACLDetail.RoleId ),
            };
            string strSql = $@" Delete u1 from t_sec_roleuser u1 
                                where u1.froleid=@froleid ";
            if (!ctx.IsOperationCompany)
            {
                strSql += @" and exists(select 1 from t_sec_user u2 with(nolock) 
                                        where ( u2.fmainorgid = @companyId or u2.fmainorgid = @ftoporgid ) and u2.fid = u1.fuserid ) ";
                lstSqlParas.Add(new SqlParam("@companyId", System.Data.DbType.String, roleACLDetail.CompanyId));
                lstSqlParas.Add(new SqlParam("@ftoporgid", System.Data.DbType.String, ctx.TopCompanyId));//经销商管理员角色在总部组织下
            }

            var svc = ctx.Container.GetService<IDBServiceEx>();
            svc.Execute(ctx, strSql, lstSqlParas);
        }

        #endregion



        /// <summary>
        /// 获取用户对于某个具体业务对象的数据行授权情况
        /// </summary>
        /// <param name="ctx"></param> 
        /// <param name="formId"></param>
        /// <returns>数据行授权情况</returns>
        internal static List<DataRowAuthInfo> GetDataRowACLPermDataByUser(UserContext ctx, string formId)
        {
            List<DataRowAuthInfo> drAuth = new List<DataRowAuthInfo>();
            if (IsEmptyUser(ctx))
            {
                return drAuth;
            }

            var cacheKey = CST_CacheKey_BizDataRowACL.Fmt(ctx.Company, ctx.UserId, formId);
            var redisCache = GetRedisCacheSvc(ctx);
            try
            { 
                var sAuth = redisCache.Get<string>(cacheKey);
                if (!sAuth.IsNullOrEmptyOrWhiteSpace())
                {
                    if (sAuth.EqualsIgnoreCase("none"))
                    {
                        return drAuth;
                    }

                    if (sAuth.StartsWith("["))
                    {
                        drAuth = sAuth.FromJson<List<DataRowAuthInfo>>();

                        if (drAuth == null)
                        {
                            drAuth = new List<DataRowAuthInfo>();
                        }

                        return drAuth;
                    }
                }
            }
            catch (Exception ex)
            {
                //忽略redis缓存取数错误
            }
            
             
            string strSql = $@"select distinct t0.froleid, t0.fbizobjid, t0.ffiltertype,t0.fexpress,t0.fdesc ,t0.fbdfldfilter
                                from t_sec_roledatarowacl t0  with(nolock) 
                                inner join t_sec_roleuser t1  with(nolock) on t0.froleId=t1.froleId
                                inner join t_sec_rolefuncacl t2 with(nolock) on t0.froleid=t2.froleid and t0.fbizobjid=t2.fbizobjid and t2.fpermititemid='fw_view' and t2.fallow=1 and t2.frefuse=0
                                where t1.fuserid=@fuserid 
                                    and (t0.fcompanyid=@fcompanyid or t0.fcompanyid=@ftoporgid)
                                    and t0.fbizobjid=@fbizobjid ";
            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@fuserid", System.Data.DbType.String,ctx.UserId),
                new SqlParam("@fcompanyid", System.Data.DbType.String,ctx.Company),
                new SqlParam("@ftoporgid", System.Data.DbType.String,ctx.TopCompanyId),//经销商管理员角色在总部组织下
                new SqlParam("@fbizobjid", System.Data.DbType.String,formId),
            };

            var svc = ctx.Container.GetService<IDBService>();
            using (var reader = svc.ExecuteReader(ctx, strSql, lstSqlParas))
            {
                while (reader.Read())
                {
                    var authInfo = new DataRowAuthInfo()
                    {
                        RoleId = reader.GetValue<string>("froleid"),
                        FormId = reader.GetValue<string>("fbizobjid"),
                        FilterType = reader.GetValue<string>("ffiltertype"),
                        Express = reader.GetValue<string>("fexpress"),
                        Desc = reader.GetValue<string>("fdesc"),
                    };
                    var bdFilter = reader.GetValue<string>("fbdfldfilter");
                    if (!bdFilter.IsNullOrEmptyOrWhiteSpace())
                    {
                        authInfo.BDFldsAuthInfo = bdFilter.FromJson<List<BDFldDataRowAuthInfo>>();
                    }

                    drAuth.Add(authInfo);
                }
            }

            if (drAuth.Count == 0)
            {
                redisCache.Set(cacheKey, "none");
            }
            else
            {
                redisCache.Set(cacheKey, drAuth.ToJson());
            }

            return drAuth;
        }



        /// <summary>
        /// 业务对象：权限项
        /// </summary>
        internal static string GetBizPermitKey(string bizObjId, string permitItemId)
        {
            return string.Format("{0}:{1}", bizObjId, permitItemId);
        }


        internal static void AutoUpdateSchema(UserContext ctx)
        {
            if (haveUpdateSchema)
            {
                return;
            }

            haveUpdateSchema = true;

            AutoUpdateSchema(ctx, AuthFormConst.Sec_BizObjPermit);
            AutoUpdateSchema(ctx, AuthFormConst.Sec_Role);
            AutoUpdateSchema(ctx, AuthFormConst.Sec_RoleDataRowAcl);
            AutoUpdateSchema(ctx, AuthFormConst.Sec_RoleFieldAcl);
            AutoUpdateSchema(ctx, AuthFormConst.Sec_RoleFuncAcl);
            AutoUpdateSchema(ctx, AuthFormConst.Sec_RoleUser);
            AutoUpdateSchema(ctx, AuthFormConst.Sec_User);
        }


        private static void AutoUpdateSchema(UserContext ctx, string formId)
        {
            var dm = ctx.Container?.GetService<IDataManager>();
            dm.Option = ctx.Container.GetService<OperateOption>();
            dm.Option.SetVariableValue("forceRecreate", true);
            dm.Option.SetVariableValue("AutoUpdateScheme", true);
            var meta = HtmlParser.LoadFormMetaFromCache(formId, ctx);
            dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));
        }



        /// <summary>
        /// 判断用户是否属于管理员角色组
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="permData"></param>
        /// <returns></returns>
        internal static bool IsRoleAdmin(UserContext ctx, PermAuth permData)
        {
            if (ctx.UserName.EqualsIgnoreCase("administrator")) return true;

            var redisCache = GetRedisCacheSvc(ctx);
            var cacheKey = permData.GetRoleAdminCacheKey();          
            try
            {
                var res = redisCache.Get<string>(cacheKey);
                if ("true".EqualsIgnoreCase(res))
                {
                    return true;
                }
                else if ("false".EqualsIgnoreCase(res))
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                //忽略redis缓存取数错误
            }
             
            var strSql = @"select t1.froleid,t1.fuserid
                            from t_sec_roleuser t1  with(nolock) 
                            inner join t_sec_role t2  with(nolock) on t1.froleid=t2.fid
                            where  t1.fuserid=@fuserid and (t1.froleid='admin' or t2.fisadmin=1 or
                            t1.froleid = 'admin.{0}'  ) and (t2.fmainorgid=@fcompanyid or t2.fmainorgid=@ftoporgid ) 
                            and t2.fnumber<>'Admin_DevOps'";
            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("fuserid", System.Data.DbType.String,ctx.UserId),
                new SqlParam("@fcompanyid", System.Data.DbType.String,ctx.Company),
                new SqlParam("@ftoporgid", System.Data.DbType.String,ctx.TopCompanyId),//经销商管理员角色在总部组织下
            };

            bool isAdmin = false;
            var dbService = ctx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(ctx, strSql, lstSqlParas))
            {
                while (reader.Read())
                {
                    isAdmin = true;
                    break;
                }
            }

            redisCache.Set(cacheKey, isAdmin ? "true" : "false");

            return isAdmin;
        }


        /// <summary>
        /// 判断用户是否属于系统运维人员
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="permData"></param>
        /// <returns></returns>
        internal static bool IsDevOpsUser(UserContext ctx, PermAuth permData)
        {
            if (IsEmptyUser(ctx))
            {
                return false;
            }
 
            var redisCache = GetRedisCacheSvc(ctx);
            var cacheKey = permData.GetRoleDevOpsCacheKey();
            try
            {
                var res = redisCache.Get<string>(cacheKey);
                if ("true".EqualsIgnoreCase(res))
                {
                    return true;
                }
                else if ("false".EqualsIgnoreCase(res))
                {
                    return false;
                }
            }
            catch (Exception)
            {
                //忽略redis缓存取数错误
            }
             
            var strSql = @"select t1.froleid,t1.fuserid
                            from t_sec_roleuser t1  with(nolock) 
                            inner join t_sec_role t2  with(nolock) on t1.froleid=t2.fid
                            where t2.fnumber='Admin_DevOps' and t1.fuserid=@fuserid and t2.fmainorgid=@ftoporgid ";
            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("fuserid", System.Data.DbType.String,ctx.UserId),
                new SqlParam("@ftoporgid", System.Data.DbType.String,ctx.TopCompanyId),//系统运维角色在总部组织下
            };

            bool isAdmin = false;
            var dbService = ctx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(ctx, strSql, lstSqlParas))
            {
                while (reader.Read())
                {
                    isAdmin = true;
                    break;
                }
            }

            redisCache.Set(cacheKey, isAdmin ? "true" : "false");

            return isAdmin;
        }


        /// <summary>
        /// 检测当前用户对某个表单某个操作是否有权限，无权不会报错。
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="permData"></param>
        /// <returns></returns>
        internal static bool HasPermission(UserContext ctx, PermAuth permData)
        {
            var cacheKey = permData.GetFuncPermCacheKey();

            var formModel = ctx.Container.GetService<IMetaModelService>()?.LoadFormModel(ctx, permData.FormId);
            if (formModel?.EnableRAC == false)
            {
                return true;
            }

            var redisCache = GetRedisCacheSvc(ctx);
            try
            {
                var res = redisCache.Get<string>(cacheKey);
                if ("true".EqualsIgnoreCase(res))
                {
                    return true;
                }
                else if ("false".EqualsIgnoreCase(res))
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                //忽略redis缓存取数错误
            }
            
            //取本组织与管理组织的权限
            var strSql = @"select t0.fallow,t0.frefuse,t0.fcompanyid
                            from t_sec_rolefuncacl t0 with(nolock) 
                            inner join t_sec_roleuser t1  with(nolock) on t0.froleid=t1.froleid
                            where t1.fuserid=@fuserid and ( t0.fcompanyid='0' or t0.fcompanyid=@fcompanyid or t0.fcompanyid=@ftoporgid )
                            and t0.fbizobjid=@fbizobjid and t0.fpermititemid=@fpermititemid
                            ";
            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("fuserid", System.Data.DbType.String,permData.UserId),
                new SqlParam("fcompanyid", System.Data.DbType.String,permData.CompanyId),
                new SqlParam("@ftoporgid", System.Data.DbType.String,ctx.TopCompanyId),//经销商管理员角色在总部组织下
                new SqlParam("fbizobjid", System.Data.DbType.String,permData.FormId),
                new SqlParam("fpermititemid", System.Data.DbType.String,permData.PermId),
            };

            bool hasRight = false;
            var dbService = ctx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(ctx, strSql, lstSqlParas))
            {
                while (reader.Read())
                {
                    var companyId = reader["fcompanyid"] as string;

                    var hasRefuse = Convert.ToString(reader["frefuse"]) == "1";
                    //如果非运维组织已经拒绝，则不用再继续判断
                    if (hasRefuse && !companyId.EqualsIgnoreCase("0"))
                    {
                        hasRight = false;
                        break;
                    }

                    if (Convert.ToString(reader["fallow"]) == "1")
                    {
                        hasRight = true;
                    }
                }
            }

            redisCache.Set(cacheKey, hasRight ? "true" : "false");

            return hasRight;
        }

        /// <summary>
        /// 是否空用户
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        internal static bool IsEmptyUser(UserContext ctx)
        {
            return ctx.UserId.IsNullOrEmptyOrWhiteSpace();
        }

        /// <summary>
        /// 预置超级管理员或总部管理员
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        internal static bool IsAdministrator(UserContext ctx)
        {
            if (IsEmptyUser(ctx))
            {
                return false;
            }

            if (ctx.UserName.EqualsIgnoreCase("administrator"))
            {
                return true;
            }

            if (!ctx.IsTopOrg)
            {
                return false;
            }

            var permData = new MetaCore.PermData.PermAuth(ctx);
            var cacheKey = permData.GetAdministratorCacheKey();
            var redisCache = GetRedisCacheSvc(ctx);
            try
            {                
                var res = redisCache.Get<string>(cacheKey);
                if ("true".EqualsIgnoreCase(res))
                {
                    return true;
                }
                else if ("false".EqualsIgnoreCase(res))
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                //忽略redis缓存取数错误
            }
            
            var dbService = ctx.Container.GetService<IDBService>();

            string sql = $"select fadminuser from v_auth_company with(nolock) where fid=@fmainorgid";

            var topOrgAdmin = string.Empty;

            using (var reader = dbService.ExecuteReader(ctx, sql,
                new List<SqlParam> { new SqlParam("@fmainorgid", DbType.String, ctx.Company) }))
            {
                if (reader.Read())
                {
                    topOrgAdmin = Convert.ToString(reader[0]);
                }
            }

            bool isAdmin = ctx.UserName.EqualsIgnoreCase(topOrgAdmin)
                      || ctx.UserPhone.EqualsIgnoreCase(topOrgAdmin);

            redisCache.Set(cacheKey, isAdmin ? "true" : "false");

            return isAdmin;
        }
        
        /// <summary>
        /// 获取当前用户的权限信息
        /// </summary>
        /// <param name="ctx"></param> 
        /// <returns></returns>
        internal static Dictionary<string, int> GetCurrUserPermDataInfo(UserContext ctx)
        {
            Dictionary<string, int> auth = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            if (ctx.IsTopOrg)
            {
                //总部用户
                return auth;
            }

            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@fuserid", System.Data.DbType.String, ctx.UserId)
            };

            var strSql = @" SELECT  distinct t0.fbizobjid, t0.fpermititemid, t0.fallow, t0.frefuse, t0.fcompanyid 
                            FROM t_sec_rolefuncacl t0 WITH (NOLOCK) 
                            where exists ( 
                                select 1 from t_sec_role t1 WITH (NOLOCK) 
                                INNER JOIN t_sec_roleuser t2 WITH (NOLOCK) ON t1.fid = t2.froleId
                                WHERE t2.fuserid = @fuserid and t0.froleid=t1.fid
                            )
                            ";
            var svc = ctx.Container.GetService<IDBService>();
            var datas = svc.ExecuteDynamicObject(ctx, strSql, lstSqlParas);
            var grp = datas.GroupBy(f => new { fbizobjid = Convert.ToString(f["fbizobjid"]), fpermititemid = Convert.ToString(f["fpermititemid"]) }).ToList();
            foreach (var item in grp)
            {
                string bizObjId = item.Key.fbizobjid;
                string permitItemId = item.Key.fpermititemid;
                if (bizObjId.IsNullOrEmptyOrWhiteSpace() || permitItemId.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                var key2 = GetBizPermitKey(bizObjId, permitItemId);
                try
                {
                    var allow = item.ToList().Any(f => Convert.ToString(f["fallow"]) == "1"); //任意一个角色有权
                    var refuse = item.ToList().Any(f => Convert.ToString(f["frefuse"]) == "1");//任意一个角色拒绝                    
                    if (refuse)
                    {
                        //拒绝
                        auth[key2] = -1;
                    }
                    else if (allow)
                    {
                        //有权
                        auth[key2] = 1;
                    }
                    else
                    {
                        //无权
                        auth[key2] = 0;
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"组织id:[{ctx.Company}],业务对象id:[{bizObjId}],权限项id:[{permitItemId}] 数据有误，出错原因：" + ex.ToString());
                }
            }

            return auth;
        }
    }

}
