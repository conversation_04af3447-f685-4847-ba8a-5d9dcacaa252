using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.Framework.AppService.SystemPlugIn.Auth
{
    /// <summary>
    /// 实现授权页面加载字段列表的过滤逻辑
    /// </summary>
    [InjectService]
    [FormId("sec_assignright|assignsimpleright")]
    [OperationNo("QueryField")]
    public class AssignRightQueryField : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 处理自定义服务事件过程
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            var baseFormProviders = this.Container.GetService<IEnumerable<IBaseFormProvider>>().ToList();

            switch (e.EventName.ToLower())
            {
                case "onfilterqueryfield":
                    var eventData = e.EventData as Tuple<string, string>;
                    if (eventData == null) return;
                    var formModel = this.MetaModelService.LoadFormModel(this.Context, eventData.Item2);
                    if (formModel == null) return;
                    var allFieldList = formModel.GetFieldList();
                    switch (eventData.Item1.ToLower())
                    {
                        case "feditfield":
                            //只取启用了字段授权的字段
                            e.Cancel = true;
                            var permFieldList = allFieldList.Where(o => o.FieldPermit==1);
                            e.Result = permFieldList.GroupBy(o => o.Group)                                
                                .Select(o => new
                                {
                                    group = o.Key.IsNullOrEmptyOrWhiteSpace() ? "基本" : o.Key,
                                    fields = o.OrderBy(f => f.TabIndex).Select(f => new
                                    {
                                        id = f.Id,
                                        name = f.Caption,
                                        entityName = f.Entity.Caption
                                    }),
                                })
                                .ToArray();
                            break;
                        case "fcurrentuser":
                            //仅处理指向sec_user的基础资料字段
                            e.Cancel = true;
                            var userFieldList = allFieldList.Where(o => o is HtmlBaseDataField
                                         && (o as HtmlBaseDataField).RefFormId.EqualsIgnoreCase("sec_user"));
                            e.Result = userFieldList.GroupBy(o => o.Group)
                                .Select(o => new
                                {
                                    group = o.Key.IsNullOrEmptyOrWhiteSpace() ? "基本" : o.Key,
                                    fields = o.OrderBy(f => f.TabIndex).Select(f => new
                                    {
                                        id = f.Id,
                                        name = $"{(o.Key.IsNullOrEmptyOrWhiteSpace() ? "基本" : o.Key)}.{f.Caption}"
                                    }),
                                })
                                .ToArray();
                            break;
                        case "fcurrentstaff":
                            e.Cancel = true;
                            var staffFieldList = allFieldList.Where(o => o is HtmlBaseDataField
                                      && baseFormProviders.Any(f => f.IsStaffFormObject(this.Context, (o as HtmlBaseDataField).RefFormId)));
                            e.Result = staffFieldList.GroupBy(o => o.Group)
                                .Select(o => new
                                {
                                    group = o.Key.IsNullOrEmptyOrWhiteSpace() ? "基本" : o.Key,
                                    fields = o.OrderBy(f => f.TabIndex).Select(f => new
                                    {
                                        id = f.Id,
                                        name = $"{(o.Key.IsNullOrEmptyOrWhiteSpace() ? "基本" : o.Key)}.{f.Caption}"
                                    }),
                                })
                                .ToArray();
                            break;
                        case "fmydepartment":
                        case "fmyandsubdepartment":
                            e.Cancel = true;
                            var deptFieldList = allFieldList.Where(o => o is HtmlBaseDataField
                                          && baseFormProviders.Any(f => f.IsDeptFormObject(this.Context, (o as HtmlBaseDataField).RefFormId)));
                            e.Result = deptFieldList.GroupBy(o => o.Group)
                                .Select(o => new
                                {
                                    group = o.Key.IsNullOrEmptyOrWhiteSpace() ? "基本" : o.Key,
                                    fields = o.OrderBy(f => f.TabIndex).Select(f => new
                                    {
                                        id = f.Id,
                                        name = $"{(o.Key.IsNullOrEmptyOrWhiteSpace() ? "基本" : o.Key)}.{f.Caption}"
                                    }),
                                })
                                .ToArray();
                            break;
                        default:
                            break;
                    }
                    break;
            }
        }
    }
}
