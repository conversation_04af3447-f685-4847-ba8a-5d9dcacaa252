using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.Framework.AppService.SystemPlugIn.IM.NoticeList
{
    /// <summary>
    /// 强制弹出公告
    /// </summary>
    [InjectService]
    [FormId("im_noticelist")]
    [OperationNo("compulsoryprompt")]
    public class CompulsoryPrompt : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            //判断用户是否是运维角色
            string strRoleSql = $"select 1 from t_sec_role with(nolock) where fid in(select froleid from T_SEC_ROLEUSER with(nolock) where fuserid = '{this.Context.UserId}') and fnumber='Admin_DevOps'";
            var IsDevOpsRole = this.DBService.ExecuteDynamicObject(this.Context, strRoleSql).ToList();
            RecordDTO recordDTO = new RecordDTO();
            //返回信息
            this.Result.SrvData = recordDTO;
            recordDTO.isNoRecord = false;

            //如果是总部,则跳过该条件
            string strWhere = "";
            if (!this.Context.TopCompanyId.Equals(this.Context.Company))
            {
                strWhere = $"OR ( t1.frecvcompanyid = '{this.Context.Company}'OR t1.frecvcompanyid = '{this.Context.TopCompanyId}' and fnoticeid not in(select fid from t_im_notice with(nolock) where fcancelstatus='1'))";
            }

            if (!IsDevOpsRole.Any())
            {
                //获取公告通知
                var strSql = $@"
                    Select t0.fid From t_im_noticelist  AS t0 with(nolock)
                    Left Join t_im_noticelistentry AS t1 with(nolock) On t1.fid = t0.fid
                    where ( t0.fmainorgid = '0'OR t0.fmainorgid = '{this.Context.Company}')
                    and ( t1.fread = '0'AND ( t1.frecvuserid = '{this.Context.UserId}'OR t1.frecvuserid = '*')and fnoticeid not in(select fid from t_im_notice with(nolock) where fcancelstatus='1'))
                    {strWhere}
                    Order By t0.fcreatedate desc ";

                var allnoticeids = this.DBService.ExecuteDynamicObject(this.Context, strSql)?.Select(i => Convert.ToString(i["fid"])).ToList();
                //创建日志对象
                var logService = this.Container.GetService<ILogService>();
                if (logService == null) return;

                //查找是否有浏览最新公告的历史记录
                if (allnoticeids.Any())
                {
                    var dbSvcEX = this.Container.GetService<IDBServiceEx>();
                    var logSql = $"select fbizobjno  From T_IM_NOTICELIST_LG with(nolock)where fcreatorid='{this.Context.UserId}' and fbizobjno in ('{string.Join("','", allnoticeids)}')";
                    var havelognoticeids = this.DBService.ExecuteDynamicObject(this.Context, logSql)?.Select(i => Convert.ToString(i["fbizobjno"])).ToList();

                    //找出没有记录日志的公告
                    var nonoticeids = allnoticeids.Except(havelognoticeids).ToList();

                    if (nonoticeids.Count() > 0)
                    {
                        List<LogEntry> logEntries = new List<LogEntry>();
                        foreach (var nonoticeid in nonoticeids)
                        {

                            var lstLogEntry = new LogEntry()
                            {
                                BillFormId = this.OperationContext.HtmlForm.Id,
                                OpCode = this.OperationContext.OperationNo,
                                Level = Enu_LogLevel.Info.ToString(),
                                Category = 5,
                                Content = "执行了【浏览】操作！",
                                BillIds = nonoticeid,
                                BillNos = nonoticeid,
                                Detail = "",
                                LogType = Enu_LogType.RecordType_03,
                                DebugData = this.OperationContext.Result?.ToJson() ?? "",
                                OpName = "浏览"
                            };
                            logEntries.Add(lstLogEntry);
                        }
                        //批量插入日志
                        logService.BatchWriteLog(this.Context, logEntries);
                        recordDTO.Data = nonoticeids;
                        recordDTO.isNoRecord = true;
                        this.Result.SrvData = recordDTO;
                    }
                }
            }
            this.Result.IsSuccess = true;
        }

        private class RecordDTO
        {
            public bool isNoRecord { get; set; }
            public List<string> Data { get; set; }
        }
    }
}
