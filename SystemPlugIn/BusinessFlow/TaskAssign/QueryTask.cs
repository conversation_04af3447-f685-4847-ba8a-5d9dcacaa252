using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System.Collections.Generic;

namespace JieNor.Framework.AppService.SystemPlugIn.BusinessFlow.TaskAssign
{
    /// <summary>
    /// 查询当前表单关联的所有任务
    /// </summary>
    [InjectService]
    [FormId("bf_assignment")]
    [OperationNo("queryformtask")]
    public class QueryTask : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 查询关联任务数据
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var parentFormId = this.GetQueryOrSimpleParam<string>("linkformid");
            var parentBillId = this.GetQueryOrSimpleParam<string>("linkbillid");

            SqlBuilderParameter para = new SqlBuilderParameter(this.Context, "bf_task");
            para.SelectedFieldKeys.AddRange(new string[]
            {
                "ftaskname",
                "fexcuter.fname",
                "ftaskstatus.fenumitem",
                "fenddate"
            });

            para.FilterString = $"ftasksource='{parentFormId}' and ftaskman='{parentBillId}'";

            var querySvc = this.Container.GetService<IListSqlBuilder>();
            var data = querySvc.GetQueryData(this.Context, para);
            this.Result.SrvData = new
            {
                Data = data
            };
        }

        
    }
}
