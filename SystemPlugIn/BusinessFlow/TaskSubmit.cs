using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.SystemPlugIn.BusinessFlow
{
    /// <summary>
    /// 任务--任务转交
    /// </summary>
    [InjectService]
    [FormId("bf_task")]
    [OperationNo("TSubmit")]
    public class TaskSubmit : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            var id = this.GetQueryOrSimpleParam<string>("id");
            if (id.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.SrvData = new
                {
                    result = false,
                    error = "请先保存当前单据！"
                };
                return;
            }
            var formMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "bf_task");//加载模型
            var dm = this.Container.GetService<IDataManager>();//当前IOC服务容器
            dm.InitDbContext(this.Context, formMeta.GetDynamicObjectType(this.Context));

            DynamicObject dyObj = dm.Select(id) as DynamicObject;//加载数据
            DynamicObjectCollection dyObjEntrys = dyObj["fdentity"] as DynamicObjectCollection;
            //新增转交记录
            DynamicObject entryObj = new DynamicObject(formMeta.GetEntryEntity("fdentity").DynamicObjectType);
            //获取页面的值
            entryObj["fdeliverydate"] = DateTime.Now;//转交时间为当前时间
            entryObj["flast_h"] = this.GetQueryOrSimpleParam<string>("flast");
            entryObj["fnow_h"] = this.GetQueryOrSimpleParam<string>("fnow");
            entryObj["freason_h"] = this.GetQueryOrSimpleParam<string>("freason");
            //var defCalulator = this.Context.Container.GetService<IDefaultValueCalculator>();//默认数据(这个可以暂时不考虑)
            //defCalulator.Execute(this.Context, this.HtmlForm, new DynamicObject[] { dyObj });            
            dyObjEntrys.Add(entryObj);
            this.Container.GetService<IDataEntityPkService>().AutoSetPrimaryKey(this.Context, dyObj, dm.DataEntityType);//自动创建主键
            dm.Save(dyObj);
            this.Result.SrvData = new
            {
                result = true,
                message = "转交成功！"

            };
            Dictionary<string, object> dso = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
            dso.Add("id", id);
            OperationResult re = this.Gateway.InvokeBillOperation(this.Context, "bf_task", null, "Receive", dso) as OperationResult;
            this.Result.SrvData = re.SrvData;
        }
    }
}
