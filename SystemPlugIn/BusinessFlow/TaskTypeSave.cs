using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.SystemPlugIn.BusinessFlow
{
    /// <summary>
    /// 任务类型--保存
    /// </summary>
    [InjectService]
    [FormId("bf_tasktype")]
    [OperationNo("save")]
    public class TaskTypeSave : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            //定义表头校验规则

            bool ss = this.GetQueryOrSimpleParam<bool>("flag");
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fname"]).NotEmpty().WithMessage("名称不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((n, o) =>
            {
                return ValiDation(n);
            }).WithMessage("该名称已存在"));
            if (ss == true)
            {
                e.Rules.Add(this.RuleFor("fbillhead", data => data["fdocobj"]).NotEmpty().WithMessage("对应单据不能为空！"));
                e.Rules.Add(this.RuleFor("fbillhead", data => data["fsbutton"]).NotEmpty().WithMessage("制单按钮不能为空！"));
            }
        }

        /// <summary>
        /// 名称唯一
        /// </summary>
        /// <param name="newData"></param>
        /// <returns></returns>
        private bool ValiDation(DynamicObject newData)
        {
            bool result = true;
            if (newData["fname"].IsNullOrEmptyOrWhiteSpace())
            {
                return false;
            }
            string product = newData["fname"].ToString();
            var formMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "bf_tasktype");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, formMeta.GetDynamicObjectType(this.Context));
            DynamicObject dyObj = null;
            if (newData["id"].IsNullOrEmptyOrWhiteSpace())
            {
                dyObj = dm.SelectBy("fname='{0}' ".Fmt(product)).OfType<DynamicObject>().FirstOrDefault();
            }
            else
            {
                dyObj = dm.SelectBy("fname='{0}' and t_bf_tasktype.fid !='{1}' ".Fmt(product, newData["id"].ToString())).OfType<DynamicObject>().FirstOrDefault();
            }
            if (dyObj != null) { result = false; }
            return result;

        }
    }
}
