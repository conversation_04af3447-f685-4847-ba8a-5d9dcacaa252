using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Enums;

namespace JieNor.Framework.AppService.SystemPlugIn.BPM.FlowInstance
{
    /// <summary>
    /// 流程实例：删除
    /// </summary>
    [InjectService]
    [FormId("bpm_flowinstance")]
    [OperationNo("Delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            string errorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                this.CheckFlowInstance(newData, out errorMessage);
                return errorMessage.IsNullOrEmptyOrWhiteSpace();
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));
        }

        /// <summary>
        /// 检查流程实例是否允许被删除
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="errorMessage"></param>
        private void CheckFlowInstance(DynamicObject dataEntity, out string errorMessage)
        {
            errorMessage = string.Empty;

            var bizBillNo = dataEntity["fbizbillno"] as string;
            var bizBillPkid = dataEntity["fbizbillpkid"] as string;
            var bizFormId = dataEntity["fbizformid"] as string;
            var bizForm = this.MetaModelService.LoadFormModel(this.Context, bizFormId);

            var strSql = $@"
            select top 1 fflowinstanceid,fstatus from {bizForm.BillHeadTableName} 
            where fmainorgid=@fmainorgid and fid=@fid";

            var sqlParam = new List<SqlParam>()
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("@fid", System.Data.DbType.String, bizBillPkid)
            };

            var dicFlows = new List<Dictionary<string, string>>();
            List<string> msgList = new List<string>();

            using (var reader = this.DBService.ExecuteReader(this.Context, strSql, sqlParam))
            {
                if (reader.Read())
                {
                    //如果业务单据上面的流程实例Id为空 或者 不等于当前删除的流程实例Id，则允许删除（此类可能是垃圾数据，可以允许被删除）
                    var flowInstanceId = reader.GetString("fflowinstanceid");
                    if(flowInstanceId.IsNullOrEmptyOrWhiteSpace()
                         || !flowInstanceId.EqualsIgnoreCase(Convert.ToString(dataEntity["id"])))
                    {
                        return;
                    }
                    var status = reader.GetString("fstatus");
                    if (!flowInstanceId.IsNullOrEmptyOrWhiteSpace() 
                            && !status.EqualsIgnoreCase(BillStatus.B.ToString()) 
                            && !status.EqualsIgnoreCase(BillStatus.C.ToString()))
                    {
                        errorMessage = $@"{bizForm.Caption}【{bizBillNo}】的数据状态不是 创建/重新审核，不允许删除关联的流程实例！";
                    }
                }
            }
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            var executionForm = this.MetaModelService.LoadFormModel(this.Context, "bpm_execution");
            var executionDm = this.GetDataManager();
            executionDm.InitDbContext(this.Context, executionForm.GetDynamicObjectType(this.Context));

            var msgForm = this.MetaModelService.LoadFormModel(this.Context, "bpm_sectionmsg");
            var msgDm = this.GetDataManager();
            msgDm.InitDbContext(this.Context, msgForm.GetDynamicObjectType(this.Context));

            //清空关联的业务单据的流程实例Id，以及删除该流程实例执行过程中产生的其他数据
            foreach (var dataEntity in e.DataEntitys)
            {
                var flowInstanceId = dataEntity["id"] as string;
                var bizBillPkid = dataEntity["fbizbillpkid"] as string;
                var bizFormId = dataEntity["fbizformid"] as string;

                var bizForm = this.MetaModelService.LoadFormModel(this.Context, bizFormId);
                var dm = this.GetDataManager();
                dm.InitDbContext(this.Context, bizForm.GetDynamicObjectType(this.Context));
                var bizObj = dm.Select(bizBillPkid) as DynamicObject;
                if (bizObj != null)
                {
                    var bizFlowInstanceId = bizObj["fflowinstanceid"] as string;
                    if(!bizFlowInstanceId.IsNullOrEmptyOrWhiteSpace()
                         && bizFlowInstanceId.EqualsIgnoreCase(flowInstanceId))
                    {
                        bizObj["fflowinstanceid"] = "";
                        dm.Save(bizObj);
                    }
                }

                //节点
                var where = "fmainorgid=@fmainorgid and finstanceid=@finstanceid";
                var paramList = new List<SqlParam>()
                {
                    new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                    new SqlParam("@finstanceid", System.Data.DbType.String, flowInstanceId)
                };
                var reader = this.Context.GetPkIdDataReader(executionForm, where, paramList);
                var executions = executionDm.SelectBy(reader).OfType<DynamicObject>();
                if (executions != null)
                {
                    var sectionIds = executions.Select(o => o["id"]);

                    var msgWhere = $"fmainorgid=@fmainorgid and fsectionid in('{string.Join("','", sectionIds)}')";
                    var msgParamList = new List<SqlParam>()
                    {
                        new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company)
                    };
                    var msgReader = this.Context.GetPkIdDataReader(msgForm, msgWhere, msgParamList);
                    var msgs = msgDm.SelectBy(msgReader).OfType<DynamicObject>();
                    if (msgs != null)
                    {
                        //删除节点消息
                        var msgIds = msgs.Select(o => o["id"]);
                        msgDm.Delete(msgIds);
                    }

                    //删除节点
                    executionDm.Delete(sectionIds);
                }
            }
        }
    }
}