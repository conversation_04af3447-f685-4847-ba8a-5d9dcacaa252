using Newtonsoft.Json.Linq;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.AppService.OperationService.ApprovalFlow;

namespace JieNor.Framework.AppService.SystemPlugIn.BPM.ProIssue
{
    /// <summary>
    /// 发布流程
    /// </summary>
    [InjectService]
    [FormId("bpm_proissue")]
    [OperationNo("new")]
    public class New : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName.ToLower())
            {
                case "aftercreateuidata":
                    var eventData = e.EventData as JObject;
                    if (eventData != null)
                    {
                        //根据流程Id生成新的版本号
                        var flowId = this.GetQueryOrSimpleParam<string>("flowId", "");
                        if (flowId.IsNullOrEmptyOrWhiteSpace()) return;

                        var approvalFlowService = this.Container.GetService<IApprovalFlowService>();
                        eventData["fverno"] = approvalFlowService.GetNewFlowVersionNo(this.Context, flowId);
                    }
                    break;
                default:
                    break;
            }
        }
    }
}