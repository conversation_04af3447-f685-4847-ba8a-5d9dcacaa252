using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.DataTransferObject;

namespace JieNor.Framework.AppService.SystemPlugIn.BPM.BusinessApproval
{
    /// <summary>
    /// 业务审批：保存
    /// </summary>
    [InjectService]
    [FormId("bpm_businessapproval")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fbizformid"]).NotEmpty().WithMessage("业务对象不能为空！"));

            var bizUniqueRule = this.Container.GetValidRuleService(HtmlElementType.HtmlValidator_BillUniqueValidation);
            if (bizUniqueRule != null)
            {
                bizUniqueRule.Initialize(this.Context, "fmainorgid,fbizformid");
                e.Rules.Add(bizUniqueRule);
            }
        }
    }
}