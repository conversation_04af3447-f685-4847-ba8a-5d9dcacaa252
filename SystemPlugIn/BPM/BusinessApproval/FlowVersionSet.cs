using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using JieNor.Framework.DataTransferObject.BPM;
using JieNor.Framework.AppService.OperationService.ApprovalFlow;
using JieNor.Framework.Utils;
using JieNor.Framework.DataTransferObject.Poco;

namespace JieNor.Framework.AppService.SystemPlugIn.BPM.BusinessApproval
{
    /// <summary>
    /// 版本记录：设置为主版本
    /// 目的是将子表体某行打为启用状态（进入写保护），并且更新父表体的流程版本号字段，同时更新为启用。
    /// </summary>
    [InjectService]
    [FormId("bpm_businessapproval")]
    [OperationNo("vnow")]
    public class FlowVersionSet : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            var verId = this.GetQueryOrSimpleParam<string>("rowid");
            if (verId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("参数 rowid 为空，请检查！");

            var strSql = $@"
            select fid,fentryid from t_bpm_businessflow 
            where fentryid in 
            (
                select top 1 fentryid from t_bpm_flowversion 
                where fdetailid=@fdetailid
            )";
            var sqlParam = new List<SqlParam>()
            {
                new SqlParam("@fdetailid", DbType.String, verId)
            };
            string approvalId = string.Empty;
            string flowId = string.Empty;
            using (var reader = this.DBService.ExecuteReader(this.Context, strSql, sqlParam))
            {
                if (reader.Read())
                {
                    approvalId = Convert.ToString(reader["fid"]);
                    flowId = Convert.ToString(reader["fentryid"]);
                }
            }
            if (approvalId.IsNullOrEmptyOrWhiteSpace() || flowId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"此版本记录【{verId}】不存在或已被删除！");
            }

            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "bpm_businessapproval");
            var dm = this.Context.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            var approval = dm.Select(approvalId) as DynamicObject;
            if (approval == null) throw new BusinessException($"此版本记录关联的业务审批对象【{approvalId}】不存在或已被删除！");

            var flowEntrys = approval["fflowentry"] as DynamicObjectCollection;
            var flowEntry = flowEntrys.FirstOrDefault(f => Convert.ToString(f["id"]).EqualsIgnoreCase(flowId));
            if (flowEntry == null) throw new BusinessException($"此版本记录关联流程信息【{flowId}】不存在或已被删除！");

            var versionEntrys = flowEntry["fversionentry"] as DynamicObjectCollection;
            var versionEntry = versionEntrys.FirstOrDefault(v => Convert.ToString(v["id"]).EqualsIgnoreCase(verId));
            if (versionEntry == null) throw new BusinessException($"此版本记录【{verId}】不存在或已被删除！");

            if (Convert.ToString(versionEntry["fverstatus"]).EqualsIgnoreCase("flowver_sta02") == false)
            {
                throw new BusinessException("此版本记录还未发布！");
            }

            //验证流程设计是否合理（节点、连线等）
            var jsonDetails = versionEntry["fdetails"] as string;
            if (jsonDetails.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"设置失败，此流程设计关联的版本记录【{verId}】的 fdetails 字段值为空，请检查！");
            }
            try
            {
                var designModel = JsonConvert.DeserializeObject<BPMDesignDrawingModel>(jsonDetails);
                if (designModel == null)
                {
                    throw new BusinessException($"设置失败，此流程设计关联的版本记录【{verId}】的 fdetails 字段值为空，请检查！");
                }
                //返回多个错误信息
                var checkResult = FlowDesignSettings.CheckDesign(designModel);
                if (checkResult != null && checkResult.Count > 0)
                {
                    for (int i = 0; i < Math.Min(10, checkResult.Count); i++)
                    {
                        this.Result.ComplexMessage.ErrorMessages.Add(checkResult[i]);
                    }
                    this.Result.IsSuccess = false;
                    return;
                }
            }
            catch (Exception ex)
            {
                throw new BusinessException("设置失败，此流程设计信息解析错误：" + ex.Message);
            }

            //将所有其他版本记录设置非主版本
            foreach (var ver in versionEntrys)
            {
                ver["fismain"] = false;
            }

            //将当前版本记录设置为主版本
            versionEntry["fismain"] = true;
            versionEntry["fverstatus"] = "flowver_sta02";

            //将当前版本记录的流程名称，设置给所关联的流程版本号，同时更新为启用
            flowEntry["fverno"] = versionEntry["fvername"];
            flowEntry["fflowstatus"] = "flow_sta02";

            //更新流程代理明细
            var flowService = this.Container.GetService<IApprovalFlowService>();
            flowService.UpdateFlowProxyEntry(this.Context, flowId, verId, flowEntry["fflowtitle"] as string, flowEntry["fverno"] as string);

            //保存业务审批
            dm.Save(approval);

            this.AddSetValueAction("fverno", versionEntry["fvername"], flowId);
            this.AddSetValueAction("fflowstatus", "flow_sta02", flowId);

            FlowVersionEdit.SetLatestMultiField(this.Context, versionEntry);


            //是否终止进行中的审批流实例
            var fstopflow = this.GetQueryOrSimpleParam<bool>("fstopflow", false);
            IOperationResult result = null;
            if (fstopflow)
            {
                var fbizformid = approval["fbizformid"] as string;

                //查询订单最新一条并且在进行中的流程实例
                string querySql = $@"select fbizbillpkid from (
                                    select fbizbillpkid,fflowstatus, row_number() over(partition by fbizbillno order by fcreatedate desc) as count 
                                    from t_bpm_flowinstance with(nolock) 
                                    where fmainorgid=@fmainorgid and fbizformid =@fbizformid
                                    ) pur where pur.count= 1 and fflowstatus='flowinstancestatus_01';";
                var querySqlParam = new List<SqlParam>()
                    {
                        new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
                        new SqlParam("@fbizformid", DbType.String, fbizformid),
                    };
                var fbizbillpkids = new List<string>();
                using (var reader = this.DBService.ExecuteReader(this.Context, querySql, querySqlParam))
                {
                    while (reader.Read())
                    {
                        fbizbillpkids.Add(reader.GetValueToString("fbizbillpkid"));
                    }
                }
                if (fbizbillpkids.Count > 0)
                {
                    //有提交过审批流的单据
                    var flowDataEntitys = this.Context.LoadBizDataByFilter(fbizformid, $" fid in ('{string.Join("','", fbizbillpkids)}') and fstatus !='E'",true).ToArray();
                    if (flowDataEntitys != null && flowDataEntitys.Length > 0)
                    {
                        var metaModelService = this.Container.GetService<IMetaModelService>();
                        var htmlForm_Reject = metaModelService.LoadFormModel(this.Context, fbizformid);
                        //走审批流“驳回操作”
                        result = flowService.RejectAllFlow(this.Context, htmlForm_Reject, flowDataEntitys, "业务审批驳回");
                    }
                }
            }

            this.Result.IsSuccess = true;
            this.Result.SrvData = new { rowid = verId, verEntry = versionEntry };
            if (fstopflow && result != null)
            {
                this.Result.Title = "审批流终止操作";
                if (result.ComplexMessage.SuccessMessages.Count > 0)
                    this.Result.ComplexMessage.SuccessMessages.Add($"{string.Join(",", result.ComplexMessage.SuccessMessages)}已终止成功，请手动重新提交审批。");

                if (result.ComplexMessage.WarningMessages.Count > 0)
                    this.Result.ComplexMessage.WarningMessages.Add($"{string.Join(",", result.ComplexMessage.WarningMessages)}终止失败，原因：“变更状态为变更已提交，不允许撤销，请执行审核。”，请手工处理！");

                if (result.ComplexMessage.ErrorMessages.Count > 0)
                    this.Result.ComplexMessage.ErrorMessages.AddRange(result.ComplexMessage.ErrorMessages);
            }
            else
            {
                this.Result.ComplexMessage.SuccessMessages.Add("设置成功！");
            }
        }
    }
}