using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.Framework.AppService.SystemPlugIn.BPM.BusinessApproval
{
    /// <summary>
    /// 业务审批：删除
    /// </summary>
    [InjectService]
    [FormId("bpm_businessapproval")]
    [OperationNo("Delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            //检查是否存在下游出入库记录
            string errorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                this.CheckBusinessApproval(newData, out errorMessage);
                return errorMessage.IsNullOrEmptyOrWhiteSpace();
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));
        }

        /// <summary>
        /// 检查业务审批是否允许被删除
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="errorMessage"></param>
        private void CheckBusinessApproval(DynamicObject dataEntity, out string errorMessage)
        {
            errorMessage = string.Empty;

            var strSql = $@"
            select t4.fid,t4.fflowname,t4.fbizformid,t4.fbizbillno from t_bpm_businessapproval t1 
            inner join t_bpm_businessflow t2 on t2.fid=t1.fid 
            inner join t_bpm_flowversion t3 on t3.fentryid=t2.fentryid 
            inner join t_bpm_flowinstance t4 on t4.fflowid=t3.fdetailid
            where t1.fmainorgid=@fmainorgid and t4.fmainorgid=@fmainorgid and t1.fid=@fid";

            var sqlParam = new List<SqlParam>()
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("@fid", System.Data.DbType.String, dataEntity["id"])
            };

            var dicFlows = new List<Dictionary<string, string>>();
            List<string> msgList = new List<string>();

            using (var reader = this.DBService.ExecuteReader(this.Context, strSql, sqlParam))
            {
                while (reader.Read())
                {
                    var dicFlow = new Dictionary<string, string>();
                    dicFlow["fid"] = reader.GetString("fid");
                    dicFlow["fflowname"] = reader.GetString("fflowname");
                    dicFlow["fbizformid"] = reader.GetString("fbizformid");
                    dicFlow["fbizbillno"] = reader.GetString("fbizbillno");
                    dicFlows.Add(dicFlow);
                }
            }
            foreach (var dicFlow in dicFlows)
            {
                if (!dicFlow["fid"].IsNullOrEmptyOrWhiteSpace())
                {
                    var caption = dicFlow["fbizformid"];
                    try
                    {
                        var bizForm = this.MetaModelService.LoadFormModel(this.Context, dicFlow["fbizformid"]);
                        caption = bizForm.Caption;
                    }
                    catch { }
                    msgList.Add($"流程版本【{dicFlow["fflowname"]}】已被{caption}【{dicFlow["fbizbillno"]}】使用！");
                }
            }
            if (msgList.Count > 0)
            {
                errorMessage = $@"{this.HtmlForm.Caption}【{dataEntity["fnumber"]}】中的以下流程版本已被使用，不允许删除，请检查：{Environment.NewLine}{string.Join(Environment.NewLine, msgList)}";
            }
        }
    }
}