using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;

namespace JieNor.Framework.AppService.SystemPlugIn.BPM.BusinessApproval
{
    /// <summary>
    /// 版本记录：修改
    /// </summary>
    [InjectService]
    [FormId("bpm_businessapproval")]
    [OperationNo("vedit")]
    public class FlowVersionEdit : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            var verId = this.GetQueryOrSimpleParam<string>("rowid");
            if (verId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("参数 rowid 为空，请检查！");

            string strSql = $@"
            select fid,fentryid from t_bpm_businessflow 
            where fentryid in 
            (
                select top 1 fentryid from t_bpm_flowversion 
                where fdetailid=@fdetailid
            )";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fdetailid", System.Data.DbType.String, verId)
            };
            string approvalId = string.Empty;
            string flowId = string.Empty;
            using (var reader = this.DBService.ExecuteReader(this.Context, strSql, sqlParam))
            {
                if (reader.Read())
                {
                    approvalId = Convert.ToString(reader["fid"]);
                    flowId = Convert.ToString(reader["fentryid"]);
                }
            }
            if (approvalId.IsNullOrEmptyOrWhiteSpace() || flowId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"此版本记录关联的业务审批对象【{approvalId}】不存在或已被删除！");
            }

            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "bpm_businessapproval");
            var dm = this.Context.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            DynamicObject approval = dm.Select(approvalId) as DynamicObject;
            if (approval == null) throw new BusinessException($"此版本记录关联的业务审批对象【{approvalId}】不存在或已被删除！");

            var flowEntrys = approval["fflowentry"] as DynamicObjectCollection;
            var flowEntry = flowEntrys.FirstOrDefault(f => Convert.ToString(f["id"]).EqualsIgnoreCase(flowId));
            if (flowEntry == null) throw new BusinessException($"此版本记录关联流程信息【{flowId}】不存在或已被删除！");

            var versionEntrys = flowEntry["fversionentry"] as DynamicObjectCollection;
            var versionEntry = versionEntrys.FirstOrDefault(v => Convert.ToString(v["id"]).EqualsIgnoreCase(verId));
            if (versionEntry == null) throw new BusinessException($"此版本记录【{verId}】不存在或已被删除！");

            //是否是查看
            var isView = this.GetQueryOrSimpleParam<bool>("isView", false);
            if (!isView)
            {
                if (Convert.ToString(versionEntry["fverstatus"]).EqualsIgnoreCase("flowver_sta02"))
                {
                    throw new BusinessException("此版本记录已启用，不允许修改！");
                }
            }

            var flowEntity = htmlForm.GetEntryEntity("fflowentry");
            var versionEntity = htmlForm.GetSubEntryEntity("fversionentry");

            SetLatestMultiField(this.Context, versionEntry);

            var uiConverter = this.Container.GetService<IUiDataConverter>();
            var uiFlowData = uiConverter.PackageEntityData(this.Context, htmlForm, flowEntity, flowEntry);
            var uiVersionData = uiConverter.PackageEntityData(this.Context, htmlForm, versionEntity, versionEntry);

            this.Result.IsSuccess = true;
            this.Result.SrvData = new
            {
                flowEntry = uiFlowData,
                verEntry = uiVersionData,
                verId = versionEntry["id"],
                rowid = flowId,
                fflowentry = new
                {
                    id = flowEntry["id"],
                    fflowtitle = flowEntry["fflowtitle"],
                    fdescription_e = flowEntry["fdescription"]
                }
            };
        }

        /// <summary>
        /// 设置最新的多选字段（用户、部门、角色）
        /// </summary>
        /// <param name="versionEntry"></param>
        public static void SetLatestMultiField(UserContext userCtx, DynamicObject versionEntry)
        {
            // 用户、部门、角色名字重新获取
            var details = Convert.ToString(versionEntry["fdetails"]);
            var detailsJObj = JObject.Parse(details);
            var sectionsJArray = detailsJObj["sections"] as JArray;
            if (sectionsJArray != null || sectionsJArray.Count > 0)
            {
                var baseFormProvider = userCtx.Container.GetService<IBaseFormProvider>();
                foreach (var sectionsJObj in sectionsJArray)
                {
                    var costDataJObj = sectionsJObj["param"]?["costData"] as JObject;
                    if (costDataJObj != null)
                    {
                        // 用户
                        var joinUserId = costDataJObj["fjoinuser"]?.GetJsonValue("id", "");
                        //var joinUserName = costDataJObj["fjoinuser"]?.GetJsonValue("name", "");

                        if (!joinUserId.IsNullOrEmptyOrWhiteSpace())
                        {
                            var joinUserIds = joinUserId.Split(new[] { "," }, StringSplitOptions.None);

                            var users = userCtx.LoadBizDataById("sec_user", joinUserIds);

                            List<string> joinUserNames = new List<string>();
                            foreach (var userId in joinUserIds)
                            {
                                var user = users.FirstOrDefault(s =>
                                    Convert.ToString(s["id"]).EqualsIgnoreCase(userId));
                                if (user == null)
                                {
                                    joinUserNames.Add("");
                                }
                                else
                                {
                                    joinUserNames.Add(Convert.ToString(user["fname"]));
                                }
                            }

                            costDataJObj["fjoinuser"]["name"] = string.Join(",", joinUserNames);
                        }

                        // 部门
                        var joinDeptId = costDataJObj["fjoindept"]?.GetJsonValue("id", "");
                        //var joinDeptName = costDataJObj["fjoindept"]?.GetJsonValue("name", "");

                        if (!joinDeptId.IsNullOrEmptyOrWhiteSpace())
                        {
                            var joinDeptIds = joinDeptId.Split(new[] { "," }, StringSplitOptions.None);

                            var deptFormId = baseFormProvider.GetDeptFormObject(userCtx);

                            var depts = userCtx.LoadBizDataById(deptFormId, joinDeptIds);

                            List<string> joinDeptNames = new List<string>();
                            foreach (var deptId in joinDeptIds)
                            {
                                var dept = depts.FirstOrDefault(s =>
                                    Convert.ToString(s["id"]).EqualsIgnoreCase(deptId));
                                if (dept == null)
                                {
                                    joinDeptNames.Add("");
                                }
                                else
                                {
                                    joinDeptNames.Add(Convert.ToString(dept["fname"]));
                                }
                            }

                            costDataJObj["fjoindept"]["name"] = string.Join(",", joinDeptNames);
                        }

                        // 角色
                        var joinRoleId = costDataJObj["fjoinrole"]?.GetJsonValue("id", "");
                        //var joinUserName = costDataJObj["fjoinuser"]?.GetJsonValue("name", "");

                        if (!joinRoleId.IsNullOrEmptyOrWhiteSpace())
                        {
                            var joinRoleIds = joinRoleId.Split(new[] { "," }, StringSplitOptions.None);

                            var roles = userCtx.LoadBizDataById("sec_role", joinRoleIds);

                            List<string> joinRoleNames = new List<string>();
                            foreach (var userId in joinRoleIds)
                            {
                                var role = roles.FirstOrDefault(s =>
                                    Convert.ToString(s["id"]).EqualsIgnoreCase(userId));
                                if (role == null)
                                {
                                    joinRoleNames.Add("");
                                }
                                else
                                {
                                    joinRoleNames.Add(Convert.ToString(role["fname"]));
                                }
                            }

                            costDataJObj["fjoinrole"]["name"] = string.Join(",", joinRoleNames);
                        }
                    }
                }

                versionEntry["fdetails"] = detailsJObj.ToString();
            }
        }
    }
}