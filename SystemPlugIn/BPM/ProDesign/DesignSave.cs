using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.BPM;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.Framework.AppService.SystemPlugIn.BPM.ProDesign
{
    /// <summary>
    /// 保存流程设计信息
    /// </summary>
    [InjectService]
    [FormId("bpm_prodesign")]
    [OperationNo("designsave")]
    public class DesignSave : AbstractOperationServicePlugIn
    {
        /*
         * 当流程设计器点击保存按钮时，后端处理逻辑：
         * 
         * 1、根据传回来的数据，所传数据包含：流程图关联的业务审批内码（flowId)，加上流程名称，编码，版本，启用条件以及流程图数据（json）
         * 
         * 2、服务取得上述数据，存储到关联的业务审批的表体里，并返回当前流程图对应的后台主键id，以便修改部分内容后，再次点击保存，可以让后端识别出是更新，而不是新增。
         * 
         */
        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            #region 参数校验
            var designData = this.GetQueryOrSimpleParam<string>("designData");
            var verId = this.GetQueryOrSimpleParam<string>("verId");
            var flowId = this.GetQueryOrSimpleParam<string>("flowId");

            if (designData.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("业务流程设计参数 designData 为空，请检查！");
            }
            JObject joDesignSettings = null;
            try
            {
                joDesignSettings = JsonConvert.DeserializeObject<JObject>(designData);
            }
            catch (Exception ex)
            {
                throw new BusinessException("业务流程设计参数 designData 解析错误：" + ex.Message);
            }
            if (joDesignSettings == null) throw new BusinessException("业务流程设计参数 designData 为空，请检查！");

            var linkDataArray = joDesignSettings?["linkDataArray"];
            var nodeDataArray = joDesignSettings?["nodeDataArray"];
            if (linkDataArray == null) throw new BusinessException("流程线路 linkDataArray 为空，请检查！");
            if (linkDataArray == null) throw new BusinessException("流程节点 nodeDataArray 为空，请检查！");

            //流程/版本
            var joFlowEntry = joDesignSettings?["fflowentry"];
            if (joFlowEntry == null) throw new BusinessException("流程名称不能为空，请检查！");

            var approvalId = Convert.ToString(joDesignSettings["parentId"]);
            var flowTitle = Convert.ToString(joFlowEntry["fflowtitle"]);
            var flowDesc = Convert.ToString(joFlowEntry["fdescription_e"]);
            if (approvalId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("流程设计参数 parentId 为空，请检查！");
            if (flowTitle.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("流程名称不能为空，请检查！");

            if (flowId.IsNullOrEmptyOrWhiteSpace())
            {
                flowId = Convert.ToString(joFlowEntry["id"]);
            }
            #endregion

            #region 校验设计图数据
            var jsonDetails = JsonConvert.SerializeObject(new
            {
                sections = nodeDataArray,
                lines = linkDataArray
            });
            try
            {
                var designModel = JsonConvert.DeserializeObject<BPMDesignDrawingModel>(jsonDetails);
                if (designModel == null)
                {
                    throw new BusinessException("保存失败，此流程设计信息解析错误，请检查流程设计！");
                }

                //检查所有线路是否都有起点跟终点
                var removeLines = new List<BPMLineModel>();
                foreach (var line in designModel.lines)
                {
                    var lineFrom = line?.from ?? "";
                    var lineTo = line?.to ?? "";
                    if (lineFrom.IsNullOrEmptyOrWhiteSpace() || lineTo.IsNullOrEmptyOrWhiteSpace())
                    {
                        //如果没有起点或者终点，则记录下来准备清除
                        removeLines.Add(line);
                    }
                }
                if (removeLines.Any())
                {
                    //清理异常的线路
                    removeLines.ForEach(l => designModel.lines.Remove(l));
                    jsonDetails = JsonConvert.SerializeObject(designModel);
                }

                //返回多个错误信息
                var checkResult = FlowDesignSettings.CheckDesign(designModel);
                if (checkResult != null && checkResult.Count > 0)
                {
                    for (int i = 0; i < Math.Min(10, checkResult.Count); i++)
                    {
                        this.Result.ComplexMessage.ErrorMessages.Add(checkResult[i]);
                    }
                    this.Result.IsSuccess = false;
                    return;
                }

                //对所有公式进行预编译检查（检查公式语法）
                var checkOk = this.CheckGrammer(designModel);
                if (!checkOk)
                {
                    this.Result.IsSuccess = false;
                    return;
                }
            }
            catch (Exception ex)
            {
                throw new BusinessException("保存失败，此流程设计信息解析错误：" + ex.Message);
            }
            #endregion

            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "bpm_businessapproval");
            var dm = this.Context.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            var approval = dm.Select(approvalId) as DynamicObject;
            if (approval == null) throw new BusinessException($"此流程设计关联的业务审批对象【{approvalId}】不存在或已被删除！");

            var flowEntity = htmlForm.GetEntryEntity("fflowentry");
            var versionEntity = htmlForm.GetSubEntryEntity("fversionentry");

            //流程信息
            DynamicObject flowEntry = null;
            var flowEntrys = approval["fflowentry"] as DynamicObjectCollection;
            if (!flowId.IsNullOrEmptyOrWhiteSpace())
            {
                //更新草稿
                flowEntry = flowEntrys.FirstOrDefault(f => Convert.ToString(f["id"]).EqualsIgnoreCase(flowId));
                if (flowEntry == null) throw new BusinessException($"此流程设计关联的流程信息【{flowId}】不存在或已被删除！");
                flowEntry["fflowtitle"] = flowTitle;
                flowEntry["fdescription"] = flowDesc;
            }
            else
            {
                //新增流程
                var seq = 1;
                if (flowEntrys.Count > 0)
                {
                    seq = flowEntrys.Select(o => Convert.ToInt32(o["fseq"])).Max() + 1;
                }
                flowEntry = new DynamicObject(flowEntity.DynamicObjectType);
                flowEntry["fseq"] = seq;
                flowEntry["fflowtitle"] = flowTitle;
                flowEntry["fverno"] = "";
                flowEntry["fdistributor"] = "";
                flowEntry["fdistributornumber"] = "";
                flowEntry["fcreatorid"] = this.Context.UserId;
                flowEntry["fcreatedate"] = DateTime.Now;
                flowEntry["fflowstatus"] = "flow_sta01";
                flowEntry["fdescription"] = flowDesc;
                flowEntrys.Add(flowEntry);
            }

            //版本信息
            DynamicObject versionEntry = null;
            var versionEntrys = flowEntry["fversionentry"] as DynamicObjectCollection;
            if (!verId.IsNullOrEmptyOrWhiteSpace())
            {
                //更新草稿
                versionEntry = versionEntrys.FirstOrDefault(f => Convert.ToString(f["id"]).EqualsIgnoreCase(verId));
                if (versionEntry == null)
                {
                    throw new BusinessException($"此流程设计关联的版本记录【{verId}】不存在或已被删除！");
                }
                if (Convert.ToString(versionEntry["fverstatus"]).EqualsIgnoreCase("flowver_sta02"))
                {
                    throw new BusinessException($"此流程设计关联的版本记录【{versionEntry["fflowname"]} {versionEntry["fvername"]}】已发布，不允许保存！");
                }
                versionEntry["fflowname"] = flowTitle;
                versionEntry["fdesc"] = flowDesc;
            }
            else
            {
                //新增版本
                var seq = 1;
                if (versionEntrys.Count > 0)
                {
                    seq = versionEntrys.Select(o => Convert.ToInt32(o["fseq"])).Max() + 1;
                }
                versionEntry = new DynamicObject(versionEntity.DynamicObjectType);
                versionEntry["fseq"] = seq;
                versionEntry["fflowname"] = flowTitle;
                versionEntry["fvername"] = "";
                versionEntry["fdistributor"] = "";
                versionEntry["fdistributornumber"] = "";
                versionEntry["foperationid"] = this.Context.UserId;
                versionEntry["fcreatedate"] = DateTime.Now;
                versionEntry["fdesc"] = flowDesc;
                versionEntry["fismain"] = false;
                versionEntry["fverstatus"] = "flowver_sta01";
                versionEntrys.Add(versionEntry);
            }
            //流程设计json
            versionEntry["fdetails"] = jsonDetails;

            //预处理
            var preService = this.Container.GetService<IPrepareSaveDataService>();
            preService.PrepareDataEntity(this.Context, htmlForm, new DynamicObject[] { approval }, this.Option);

            //保存业务审批
            dm.Save(approval, null, this.Option);

            //加载引用数据
            var refObjMrg = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMrg?.Load(this.Context, htmlForm.GetDynamicObjectType(this.Context), approval, false);

            //打包数据给前端（流程、版本）
            var uiConverter = this.Container.GetService<IUiDataConverter>();
            var uiFlowData = uiConverter.PackageEntityData(this.Context, htmlForm, flowEntity, flowEntry);
            var uiVersionData = uiConverter.PackageEntityData(this.Context, htmlForm, versionEntity, versionEntry);

            this.Result.IsSuccess = true;
            this.Result.SrvData = new { flowData = uiFlowData, verData = uiVersionData };
            this.Result.ComplexMessage.SuccessMessages.Add("保存成功！");
        }

        /// <summary>
        /// 验证表达式语法
        /// </summary>
        /// <param name="designModel"></param>
        /// <returns></returns>
        private bool CheckGrammer(BPMDesignDrawingModel designModel)
        {
            var checkOk = true;

            //对所有公式进行预编译检查（检查公式语法）
            var bizExpr = this.Container.GetService<IBizExpression>();
            var evaluator = this.Container.GetService<IBizExpressionEvaluator>();

            //开始节点
            var startNode = designModel.sections.FirstOrDefault(t => t.stepType == (int)SectionTypes.Start);
            var startExpper = startNode?.param?.costData?.fexpper?.id ?? "";
            if (!startExpper.IsNullOrEmptyOrWhiteSpace())
            {
                bizExpr.ExpressionText = startExpper;
                var errorMessage = "";
                if (!evaluator.CheckGrammer(bizExpr, out errorMessage))
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"开始节点 - 启用条件 - 公式 语法错误：" + errorMessage);
                    checkOk = false;
                }
            }

            //所有线路
            foreach (var line in designModel.lines)
            {
                var data = line?.param?.costData;
                var name = data?.flinkname ?? "";
                var expper = data?.fexpper?.id ?? "";
                if (expper.IsNullOrEmptyOrWhiteSpace()) continue;

                bizExpr.ExpressionText = expper;
                var errorMessage = "";
                if (!evaluator.CheckGrammer(bizExpr, out errorMessage))
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"连线设置【{name}】- 流转条件 - 公式 语法错误：" + errorMessage);
                    checkOk = false;
                }
            }

            return checkOk;
        }
    }
}
