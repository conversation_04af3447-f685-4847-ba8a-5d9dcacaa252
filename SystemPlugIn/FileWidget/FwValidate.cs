using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.SystemPlugIn.FileWidget
{
    /// <summary>
    /// 检查是否有文件上传组件权限（验证签名）
    /// </summary>
    [InjectService]
    [FormId("sys_filewidget")]
    [OperationNo("validate")]
    public class FwValidate : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            /*
             *  http://localhost:58359/views/attachlist.html?username=张三&companyid=123&productid=456&fileid=a,b,c,d,e&type=1&caller=222&sign=444
             * companyid:调用者企业标识，必传，系统集成配置时生成
             * productid:调用者产品标识，必传，系统集成配置时生成
             * fileid:文件id列表，以逗号分隔
             * type:0，表示修改，1表示只读
             * username:用户名，上传时用来传给后端服务器使用
             * caller:表示调用者标识，会在回调接口中原样返回给对方
             * sign:表示按调用方私钥签名的问题
             * 
             */
            var strUserName = this.GetQueryOrSimpleParam<string>("username");// 操作人名
            var strCompanyId = this.GetQueryOrSimpleParam<string>("companyid");// 企业id
            var strProductId = this.GetQueryOrSimpleParam<string>("productid");// 产品标识id
            var strCaller = this.GetQueryOrSimpleParam<string>("caller");// 调用者标识
            //var strFileIds = this.GetQueryOrSimpleParam<string>("fileid"); // 文件id（多个逗号隔开）
            //var strOptype = this.GetQueryOrSimpleParam<string>("type");// 0只读，1允许上传、删除文件
            var strSign = this.GetQueryOrSimpleParam<string>("sign");// 签名串：{company}#{product}

            if (strUserName.IsNullOrEmptyOrWhiteSpace() || strCompanyId.IsNullOrEmptyOrWhiteSpace()
                || strProductId.IsNullOrEmptyOrWhiteSpace() || strCaller.IsNullOrEmptyOrWhiteSpace()
                || strSign.IsNullOrEmptyOrWhiteSpace())
                throw new BusinessException("请求参数错误！");

            // 读取调用方公钥、加密算法配置
            var publicKey = this.GetAppConfig("kingdee.PublicKeyXml");
            var pubKeyAlgorithm = this.GetAppConfig("kingdee.PublicKeyAlgorithm");
            if (publicKey.IsNullOrEmptyOrWhiteSpace() || pubKeyAlgorithm.IsNullOrEmptyOrWhiteSpace())
                throw new BusinessException("未获取到文件上传组件公钥配置！");

            int dwKeySize = 2048;
            try
            {
                string reqParam = string.Format("{0}#{1}", strCompanyId, strProductId);
                using (RSACryptoServiceProvider rsa = new RSACryptoServiceProvider(dwKeySize))
                {
                    rsa.FromXmlString(publicKey);
                    RSAPKCS1SignatureDeformatter f = new RSAPKCS1SignatureDeformatter(rsa);
                    f.SetHashAlgorithm(pubKeyAlgorithm);
                    byte[] key = Convert.FromBase64String(strSign.Replace("%", "").Replace(",", "").Replace(" ", "+"));
                    HashAlgorithm hasher = HashAlgorithm.Create(pubKeyAlgorithm);
                    byte[] name = hasher.ComputeHash(Encoding.UTF8.GetBytes(reqParam));
                    // true 验证成功
                    this.Result.IsSuccess = f.VerifySignature(name, key);
                }
            }
            catch (Exception ex)
            {
                this.Result.SimpleMessage = "授权签名无效！" + ex.Message;
                this.Result.IsSuccess = false;
            }
        }

    }
}