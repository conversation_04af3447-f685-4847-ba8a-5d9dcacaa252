using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.DataTransferObject.Const;
using JieNor.Framework.Watcher;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Interface.Cache;

namespace JieNor.Framework.AppService.SystemPlugIn.MainFw
{
    [InjectService("BusinessObject")]
    [FormId("ydj_task")]
    [OperationNo("biobject")]
    public class BusinessObject : AbstractOperationService
    {

        protected override string OperationName
        {
            get
            {
                throw new NotImplementedException();
            }
        }

        protected override string PermItem
        {
            get
            {
                throw new NotImplementedException();
            }
        }

        protected override void DoExecute(ref SuperOrm.DataEntity.DynamicObject[] dataEntities)
        {
            Dictionary<string, string> bizObject  = HtmlParser.LoadBizObjectList();
             
            var metaSrv = this.Container.GetService<IMetaModelService>();
            var targetForm = metaSrv.LoadFormModel(this.UserCtx, "sys_bizobject");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserCtx, targetForm.GetDynamicObjectType(this.UserCtx));
            foreach (KeyValuePair<string, string> kv in bizObject)
            {
                DynamicObject dyObj = new DynamicObject(targetForm.GetDynamicObjectType(this.UserCtx));
                
                dyObj["fname"] = kv.Value;
                dyObj["fnumber"] = kv.Key;
                 
                this.Container.GetService<IDataEntityPkService>().AutoSetPrimaryKey(this.UserCtx, dyObj, dm.DataEntityType);//自动创建主键
                dm.Save(dyObj);
            }
        }
    }
}
