using JieNor.Framework.Interface;
using JieNor.Framework.Interface.UnitTest;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.Framework.AppService.SystemPlugIn.MainFw
{
    /// <summary>
    /// 单元测试引擎
    /// </summary>
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("ut")]
    public class UnitTestEngine : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 启动单元测试引擎
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {

            //触发整个站点的单元测试逻辑
            var allUnitTestCases = this.Container.GetService<IEnumerable<IDTOUnitTestCase>>().ToArray();

            this.TaskProgressService?.SetTaskProgressValue(this.Context, this.TaskId, 0);
            try
            {
                //JsonServiceClient client = new JsonServiceClient();
                //client.SetBaseUri(this.Context.AppServer.ToString());
                //int iFlag = 1;
                //foreach (var ut in allUnitTestCases)
                //{
                //    PerfmonUtil.StartMonitor();
                //    this.TaskProgressService?.SetTaskProgressMessage(this.Context, this.TaskId, $"正在执行第{iFlag}个（共{allUnitTestCases.Length}个）单元测试用例……");
                //    this.TaskProgressService?.SetTaskProgressValue(this.Context, this.TaskId, 100m * iFlag / allUnitTestCases.Length);

                //    var resp = client.Send<DynamicDTOResponse>(ut.CreateRequestObject(this.Context));

                //    if (resp != null)
                //    {
                //        this.TaskProgressService?.SetTaskReturnData(this.Context, this.TaskId, resp.OperationResult);
                //    }
                //    resp.OperationResult.ComplexMessage.SuccessMessages.Add(PerfmonUtil.EndMonitor());
                //    iFlag++;
                //}
            }
            finally
            {
                this.TaskProgressService?.SetTaskProgressValue(this.Context, this.TaskId, 100m);
            }
        }
    }
}
