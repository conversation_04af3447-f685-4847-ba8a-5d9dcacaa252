using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.IM;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;

namespace JieNor.Framework.AppService.SystemPlugIn.MainFw
{
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("sendmessage")]
    public class RealtimeMessageTest : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            var count = this.GetQueryOrSimpleParam<string>("count");
            int iMessageCount;
            int.TryParse(count, out iMessageCount);
            if (iMessageCount <= 0) iMessageCount = 50;

            string[] testMessages = new string[]
            {
                "当前系统正在维护，请稍候使用……",
                "系统将在23:00停机维护，请及时保存数据……",
                "检测当前系统许可非法，将禁止继续使用……",
                "您的操作出现一个未知错误，请重新执行……",
                "恭喜用户aaa注册成为云链系统用户……",
                "欢迎杰诺公司购买云链系统服务……",
                "销售员XXX已接单成功，请及时审核……",
            };

            try
            {
                var msgPiple = this.Container.GetService<IMessageServiceProxy>();
              
                var msg = testMessages.ToJson().CreateBroadcastMessage();
                msgPiple.SendMessage(this.Context,new IMMessage[] { msg });
            }
            catch
            { }

            this.Result.IsSuccess = true;

        }
    }
}
