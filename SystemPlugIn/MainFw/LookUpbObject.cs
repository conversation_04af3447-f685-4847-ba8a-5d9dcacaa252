using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.IoC;

namespace JieNor.Framework.AppService.SystemPlugIn.MainFw
{
    /// <summary>
    /// 基础资料引用关系不需要鉴权
    /// </summary>
    [InjectService]
    [FormId("sys_lookupboject")]
    [OperationNo("save")]
    public class LookUpbObject : AbstractOperationServicePlugIn
    {
        public override void OnCheckPermssion(OnCheckPermssionArgs e)
        {
            e.IgnoreCheck = true;
        }
    }
}
