using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.Framework.AppService.SystemPlugIn.DataDictionary
{
    /// <summary>
    /// 获取业务表单数据字典
    /// </summary>
    [InjectService]
    [FormId("bas_datadictionary")]
    [OperationNo("loaddatadictionary")]
    public class LoadDataDictionary : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var bizFormId = this.GetQueryOrSimpleParam<string>("bizFormId");
            if (bizFormId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("业务表单参数为空，请检查！");
            }

            var bizForm = this.MetaModelService.LoadFormModel(this.Context, bizFormId);
            if (bizForm == null)
            {
                throw new BusinessException("业务表单对应的模型文件不存在，请检查！");
            }

            var result = new Dictionary<string, object>();

            Dictionary<string, object> table = new Dictionary<string, object>();
            result.Add("table", table);

            List<Dictionary<string, object>> fieldGroups = new List<Dictionary<string, object>>();

            var htmlFieldList = bizForm.GetFieldList().ToList();
            if (htmlFieldList != null)
            {
                //始终都要排除的字段
                List<string> excludeFields = new List<string>() { "fbizruleid", "fname_py", "fname_py2" };

                //根据字段的 group 分组
                IEnumerable<IGrouping<string, HtmlField>> groups = htmlFieldList
                    .Where(t => !excludeFields.Contains(t.Id.ToLower()))
                    .GroupBy(t => t.Group, t => t);
                foreach (IGrouping<string, HtmlField> group in groups)
                {
                    List<Dictionary<string, string>> fields = new List<Dictionary<string, string>>();
                    List<HtmlField> htmlFields = group.OrderBy(t => t.ListTabIndex).ToList();
                    foreach (var htmlField in htmlFields)
                    {
                        fields.Add(new Dictionary<string, string>
                        {
                            { "id", htmlField.Id },
                            { "name", htmlField.Caption },
                            { "field", htmlField.FieldName }
                        });
                    }
                    fieldGroups.Add(new Dictionary<string, object>
                    {
                        { "group", group.Key },
                        { "fields", fields }
                    });
                    //报表如果没有数据时不会生成临时表 此时tablename取不到 所以改成取Caption
                    var tableDict = new Dictionary<string, string>
                    {
                        { "tablename", htmlFields[0].Entity.TableName == null ? htmlFields[0].Entity.Caption.ToLower(): htmlFields[0].Entity.TableName.ToLower() },
                        { "pkid", htmlFields[0].Entity.PkFieldName.ToLower() }
                    };
                    table.Add(group.Key, tableDict);
                }
            }

            result.Add("group", fieldGroups);

            this.Result.SrvData = result;
        }
    }
}