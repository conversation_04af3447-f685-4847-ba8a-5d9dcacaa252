using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.SystemPlugIn.Bas
{

    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("GetSumSetting")]
    public class GetSumSetting : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            var formId = this.GetQueryOrSimpleParam<string>("formId", "");
            //操作类型
            var typeid = this.GetQueryOrSimpleParam<int>("typeid", 0);
            if (formId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("参数异常，请指定要设置的业务对象标识");
            }
            if (typeid == 0)
            {
                throw new BusinessException("参数异常，请指定要设置的操作类型标识");
            }



            var svc = this.Container.GetService<IListQuryProfile>();
            //得到用户配置的可供合计的字段
            var setting = svc.GetSettingByType(this.Context, formId, (UserProfileConfigEnum)typeid);

            var fldInfo = GetFldInfo(formId, setting, this.Context, (UserProfileConfigEnum)typeid);
            var setInfo = GetSetfldInfo(formId, setting, this.Context, (UserProfileConfigEnum)typeid);




            this.Result.SimpleData["setting"] = setInfo.ToJson();//显示字段
            this.Result.SimpleData["fldInfo"] = fldInfo.ToJson();//隐藏字段
            this.Result.IsSuccess = true;
        }

        private object GetSetfldInfo(string formId, ListFuzzySetting setting, UserContext context, UserProfileConfigEnum type = UserProfileConfigEnum.SUM)
        {
            var htmlForm = HtmlParser.LoadFormMetaFromCache(formId, this.Context);
            List<HtmlField> flds = null;
            if (type ==  UserProfileConfigEnum.SUM)
                flds = GetCanSumFlds(htmlForm);
            else
                flds = GetDisplayFlds(htmlForm,type);
            List<object> fldInfo = new List<object>();
            foreach (var fld in flds)
            {
                if (setting.FldKeys.Contains(fld.Id))
                {
                    var obj = new
                    {
                        Id = fld.Id,
                        Caption = "{0}-{1}".Fmt(fld.Entity.Caption, fld.Caption),
                        ElementType = fld.ElementType,
                    };

                    fldInfo.Add(obj);
                }

            }

            return fldInfo;
        }




        /// <summary>
        /// 获取支持合计字段信息
        /// </summary>
        /// <param name="formId"></param>
        /// <returns></returns>
        internal static List<object> GetFldInfo(string formId, ListFuzzySetting setting, UserContext ctx, UserProfileConfigEnum type =  UserProfileConfigEnum.SUM)
        {
            var htmlForm = HtmlParser.LoadFormMetaFromCache(formId, ctx);
            List<HtmlField> flds = null;
            if (type ==  UserProfileConfigEnum.SUM)
                flds = GetCanSumFlds(htmlForm);
            else 
                flds = GetDisplayFlds(htmlForm, type);
            List<object> fldInfo = new List<object>();
            foreach (var fld in flds)
            {

                if (setting.FldKeys.Contains(fld.Id))
                {
                    continue;
                }
                var obj = new
                {
                    Id = fld.Id,
                    Caption = "{0}-{1}".Fmt(fld.Entity.Caption, fld.Caption),
                    ElementType = fld.ElementType,
                };

                fldInfo.Add(obj);


            }

            return fldInfo;
        }


        /// <summary>
        /// 获取可以作为合计的字段
        /// </summary>
        /// <param name="htmlForm"></param>
        /// <returns></returns>
        internal static List<HtmlField> GetCanSumFlds(HtmlForm htmlForm)
        {
            List<HtmlField> flds = new List<HtmlField>();

            var all = htmlForm.GetFieldList();
            foreach (var item in all)
            {
                //非列表可见
                if (!item.IsVisible(HtmlElementVisibleScene.ShowOrHide)) continue;
                //非数据列
                if (item is HtmlButtonField) continue;

                if (item.ElementType == HtmlElementType.HtmlField_PriceField || item.ElementType == HtmlElementType.HtmlField_IntegerField
                      || item.ElementType == HtmlElementType.HtmlField_DecimalField
                      || item.ElementType == HtmlElementType.HtmlField_QtyField
                      || item.ElementType == HtmlElementType.HtmlField_AmountField
                      )
                {
                    flds.Add(item);
                }
            }

            return flds;
        }


        /// <summary>
        /// 获取显示的字段
        /// </summary>
        /// <param name="htmlForm"></param>
        /// <returns></returns>
        internal static List<HtmlField> GetDisplayFlds(HtmlForm htmlForm, UserProfileConfigEnum type)
        {
            List<HtmlField> flds = new List<HtmlField>();

            var all = htmlForm.GetFieldList();
            foreach (var item in all)
            {
                //非列表可见
                if (!item.IsVisible(HtmlElementVisibleScene.List)
                       && !item.IsVisible(HtmlElementVisibleScene.ShowOrHide)) continue;
                //非数据列
                if (item is HtmlButtonField) continue;
                //单据分录冻结列
                if (type ==  UserProfileConfigEnum.FORZEN) {
                    if (!item.IsBillHeadField)
                        flds.Add(item);
                }
                else
                    flds.Add(item);


            }

            return flds;
        }
    }

}
