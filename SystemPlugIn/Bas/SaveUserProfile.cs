using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.Interface.Profile;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormModel.List;

namespace JieNor.Framework.AppService.SystemPlugIn.Bas
{
    /// <summary>
    /// 保存个性化信息
    /// </summary>
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("saveuserprofile")]
    public class SaveUserProfile : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var bizFormId = this.GetQueryOrSimpleParam<string>("bizFormId");
            var profileData = this.GetQueryOrSimpleParam<string>("profileData");
            var styleSetting = this.GetQueryOrSimpleParam<string>("styleSetting");
            if (bizFormId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("业务表单Id参数 bizFormId 为空 ，请检查！");
            }

            //保存个性化信息
            if (!profileData.IsNullOrEmptyOrWhiteSpace())
            {
                var formUserProfile = profileData.FromJson<FormUserProfile>();
                if (formUserProfile != null)
                {
                    var profileService = this.Container.GetService<IUserProfile>();
                    profileService.SaveUserProfile(this.Context, bizFormId, formUserProfile, true);
                }
            }

            //保存样式设置
            if (!styleSetting.IsNullOrEmptyOrWhiteSpace())
            {
                //默认是列表
                var category = "listcolor";
                var entityKey = this.GetQueryOrSimpleParam<string>("entityKey", "");
                if (!entityKey.IsNullOrEmptyOrWhiteSpace())
                {
                    category = $"{entityKey}color";
                }

                var styleSettingList = styleSetting.FromJson<List<ListColorSetting>>();
                var svc = this.Container.GetService<IListQuryProfile>();
                svc.SaveListCellColor(this.Context, bizFormId, styleSettingList, category);
            }

            this.Result.IsSuccess = true;
            this.Result.IsShowMessage = true;
            this.Result.ComplexMessage.SuccessMessages.Add("保存成功！");
        }
    }
}