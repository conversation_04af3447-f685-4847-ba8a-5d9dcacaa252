using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.Enums;

namespace JieNor.Framework.AppService.SystemPlugIn.Bas
{

    /// <summary>
    ///  保存列表快速查询的设置信息
    /// </summary>
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("SaveSumSetting")]
    public class SaveSumSetting : AbstractOperationServicePlugIn
    {

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            var formId = this.GetQueryOrSimpleParam<string>("formId", "");
            //操作类型
            var typeid = this.GetQueryOrSimpleParam<int>("typeid", 0);
            if (formId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("参数异常，请指定要设置的业务对象标识");
            }
            if (typeid==0)
            {
                throw new BusinessException("参数异常，请指定要设置的操作类型标识");
            }

            var setting = this.GetQueryOrSimpleParam<string>("setting", "");
            if (!setting.IsNullOrEmptyOrWhiteSpace())
            {
                var option = setting.FromJson<ListFuzzySetting>();
                var svc = this.Container.GetService<IListQuryProfile>();
                svc.SaveSettingByType(this.Context, formId, option,(UserProfileConfigEnum)typeid );

                this.Result.IsSuccess = true;
                //this.Result.SimpleMessage = "保存成功";
            }

        }

    }
}
