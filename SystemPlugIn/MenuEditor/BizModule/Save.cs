using JieNor.Framework.AppService.SystemPlugIn.MainFw;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.Framework.AppService.SystemPlugIn.MenuEditor.BizModule
{
    [InjectService]
    [FormId("sys_bizmodule")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (this.Result?.IsSuccess == true)
            {
                //TODO:update menu cache.
                var cache = Auth.PermHelper.GetRedisCacheSvc(this.Context);
                cache.RemoveAllByKeyPattern(MenuModuleService.__AllMenuCacheKey, Enu_SearchType.Contains);
            }
        }
    }

    [InjectService]
    [FormId("sys_bizmodule")]
    [OperationNo("delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (this.Result?.IsSuccess == true)
            {
                var cache = Auth.PermHelper.GetRedisCacheSvc(this.Context);
                cache.RemoveAllByKeyPattern(MenuModuleService.__AllMenuCacheKey, Enu_SearchType.Contains);
            }
        }
    }
}
