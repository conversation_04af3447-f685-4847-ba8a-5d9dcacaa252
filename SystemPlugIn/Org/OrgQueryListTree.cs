using System;
using System.Collections.Generic;
using System.Linq;

using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm;

namespace JieNor.Framework.AppService.SystemPlugIn.Org
{
    /// <summary>
    /// 列表页面左侧树形控件取数插件
    /// </summary>
    [InjectService]
    [FormId("bas_organization")]
    public class OrgQueryListTree : IListTreeDataProvider
    {
        /// <summary>
        /// 模型服务
        /// </summary>
        [InjectProperty]
        protected IMetaModelService MetaModelService { get; set; }

        /// <summary>
        /// 数据库服务
        /// </summary>
        [InjectProperty]
        protected IDBService DBService { get; set; }

        /// <summary>
        /// 获取树形根节点对象
        /// </summary>
        /// <param name="userCtx">当前登录用户上下文</param>
        /// <param name="form">表单对象</param>
        /// <returns>返回树形根节点对象，根节点对象中包括子节点列表</returns>
        public ListTreeNode GetTreeDataSource(UserContext userCtx, HtmlForm form, OperateOption option)
        {
            //根节点
            ListTreeNode rootNode = new ListTreeNode()
            {
                Id = "all",
                Name = "所有",
                Filter = "",
                Children = new List<ListTreeNode>()
            };

            IEnumerable<DynamicObject> allOrgs = this.GetAllOrgs(userCtx).OrderBy(f => f["fparentid"]?.ToString()).ToList();
            if (allOrgs == null || allOrgs.Count() <= 0) { return rootNode; }
            foreach (var orgInfo in allOrgs)
            {
                //所有一级组织，只要是 fparentid 为空的都认为是一级组织
                if (orgInfo["fparentid"].IsNullOrEmptyOrWhiteSpace())
                {
                    ListTreeNode parentNode = new ListTreeNode()
                    {
                        Id = orgInfo["id"] as string,
                        Name = orgInfo["fname"] as string,
                        Filter = $"fpath like '%/{orgInfo["id"] as string}%'",
                        Children = new List<ListTreeNode>()
                    };

                    //递归加载子组织
                    this.LoadChildOrgs(allOrgs, orgInfo["id"] as string, parentNode);

                    if (parentNode.Children.Count > 0)
                    {
                        rootNode.Children.Add(parentNode);
                    }
                }
            }

            return rootNode;
        }



        /// <summary>
        /// 递归加载子组织
        /// </summary>
        /// <param name="allOrgs">所有组织</param>
        /// <param name="parentId">父级组织ID</param>
        /// <param name="node">父节点对象</param>
        private void LoadChildOrgs(IEnumerable<DynamicObject> allOrgs, string parentId, ListTreeNode node)
        {
            IEnumerable<DynamicObject> childOrgs = allOrgs.Where(o => o["fparentid"] as string == parentId);
            if (childOrgs != null && childOrgs.Count() > 0)
            {
                foreach (DynamicObject childOrg in childOrgs)
                {
                    ListTreeNode childNode = new ListTreeNode()
                    {
                        Id = childOrg["id"] as string,
                        Name = childOrg["fname"] as string,
                        Filter = $"fpath like '%/{childOrg["id"] as string}%'",
                        Children = new List<ListTreeNode>()
                    };

                    node.Children.Add(childNode);
                    //递归加载子组织
                    this.LoadChildOrgs(allOrgs, childOrg["id"] as string, childNode);
                }
            }
        }

        /// <summary>
        /// 获取所有组织
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        private IEnumerable<DynamicObject> GetAllOrgs(UserContext userCtx)
        {
            var meta = this.MetaModelService.LoadFormModel(userCtx, "bas_organization");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, meta.GetDynamicObjectType(userCtx));

            var allOrgPkIdSql = @"select {0} from {1} where fmainorgid='{2}' and fforbidstatus != '1'"
                                .Fmt(meta.HeadEntity.PkFieldName, meta.HeadEntity.TableName, userCtx.Company);

            var allPkIds = this.DBService.ExecuteDynamicObject(userCtx, allOrgPkIdSql, null)
                .Select(o => o[0])
                .ToArray();

            return dm.Select(allPkIds).OfType<DynamicObject>().ToArray();
        }
    }
}