using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Profile;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.PermData;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.SystemPlugIn.DataWidget
{
    /// <summary>
    /// 加载用户对组件的个性化配置
    /// </summary>
    [InjectService]
    [FormId("sys_dashboard")]
    [OperationNo("LoadWidgetUserProfile")]
    public class LoadWidgetUserProfile : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 实现操作逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            // 判断是否有数据组件查看权限，如果没有就不显示默认仪表盘
            if (!HasPermission("sys_datawidgetlist", "fw_view"))
            {
                return;
            }

            base.AfterExecuteOperationTransaction(e);
            var profileService = this.Container.GetService<IUserProfile>();
            var formId = "sys_datawidgetlist";
            var userProfile = profileService.LoadUserProfile<List<DataWidgeUserProfile>>(this.Context, formId, "other");

            if (userProfile == null || userProfile.Count == 0)
            {
                var svc = this.Container.GetService<IDefaultWidgetUserProfile>();
                if (svc != null)
                {
                    userProfile = svc.GetDefaultWidgetUserProfile(this.Context);
                }
            }

            if (userProfile != null && userProfile.Count > 0)
            {
                var metaModelService = this.Container.GetService<IMetaModelService>();
                var htmlForm = metaModelService.LoadFormModel(this.Context, formId);
                var dm = this.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                var dataEntities = dm.Select(userProfile.Select(x => x.Id).Where(x => false == string.IsNullOrWhiteSpace(x))).OfType<DynamicObject>().ToList();
                var permissionService = this.Container.GetService<IPermissionService>();

                var permId = (this.OperationContext.UserContext.ClientType & (long)Enu_ClientType.Mobile) == (long)Enu_ClientType.Mobile ?
                                "mobile_view" : "fw_view";
                var widgetFormIds = dataEntities?.Select(x => Convert.ToString(x["fwidgetformid"]))
                                      .Where(x => false == string.IsNullOrWhiteSpace(x))
                                      .Distinct()
                                      .Where(x => permissionService.HasPermission(this.Context, new MetaCore.PermData.PermAuth(this.Context) { FormId = x, PermId = permId }))
                                      .ToList();

                if (widgetFormIds == null)
                {
                    widgetFormIds = new List<string>();
                }

                var ids = dataEntities?.Where(x => widgetFormIds.Contains(Convert.ToString(x["fwidgetformid"]))).Select(x => Convert.ToString(x["id"])).ToList();

                if (ids == null)
                {
                    ids = new List<string>();
                }

                userProfile = userProfile.Where(x => ids.Contains(x.Id)).ToList();
            }

            if (userProfile == null)
            {
                userProfile = new List<DataWidgeUserProfile>();
            }


            this.Result.SrvData = new Dictionary<string, object>
            {
                { "profileData",userProfile}
            };
        }


        private bool HasPermission(string formId, string permId)
        {
            var htmlForm = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, formId);

            //没开启权限的表单，不验权
            if (htmlForm.EnableRAC == false)
            {
                return true;
            }

            var permSrv = this.Container.GetService<IPermissionService>();
            return permSrv.HasPermission(this.Context, new PermAuth(this.Context)
            {
                FormId = formId,
                PermId = permId,
            });
        }
    }
}
