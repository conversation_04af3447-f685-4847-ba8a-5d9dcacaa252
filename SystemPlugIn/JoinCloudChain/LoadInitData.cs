using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Linq;

namespace JieNor.Framework.AppService.SystemPlugIn.JoinCloudChain
{
    [InjectService]
    [FormId("sys_joincloudchain")]
    [OperationNo("load")]
    public class LoadInitData : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            string strCCServer = "", strCCToken = "";
            string strCorpRegNo = "", strCorpName = "", strCorpId = "";
            string strOpenTokenId = "";
            string strExpireAt = "";

            strCCServer = HostConfigView.Site.Gateway;
            strCCToken = HostConfigView.Site.GatewayTokenId;

            var currCompany = this.Context.Companys?.FirstOrDefault(o => o.CompanyId.EqualsIgnoreCase(this.Context.Company));
            if (currCompany == null)
            {
                throw new BusinessException("当前登录用户未关联到具体企业主体，出现此问题通常是系统安装初始化出错(datacenter.json)！");
            }
            strCorpRegNo = currCompany.CompanyNumber;
            strCorpName = currCompany.CompanyName;
            strCorpId = currCompany.CompanyId;

            var companyMeta = this.MetaModelService.LoadFormModel(this.Context, "sys_company");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, companyMeta.GetDynamicObjectType(this.Context));
            var companyObj = dm.Select(strCorpId) as DynamicObject;
            if (companyObj != null)
            {
                strCorpRegNo = companyObj["fnumber"] as string;
                strCorpName = companyObj["fname"] as string;
                strOpenTokenId = companyObj["fopentokenid"] as string;
                strExpireAt = Convert.ToString(companyObj["fccexpireat"]);
            }

            this.Result.IsSuccess = true;
            this.Result.SrvData = new
            {
                fcompanyid = strCorpId,
                fcloudChainServer = strCCServer,
                fcloudChainToken = strCCToken,
                fcompanyno = strCorpRegNo,
                fcompanyName = strCorpName,
                fopentokenid = strOpenTokenId,
                fexpireat = strExpireAt
            };
        }
    }
}
