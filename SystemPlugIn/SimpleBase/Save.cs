using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormMeta;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.SystemPlugIn.SimpleBase
{
    /// <summary>
    /// 辅助资料保存
    /// </summary>
    [InjectService]
    [FormId("bd_enumdata")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 权限检测
        /// </summary>
        /// <param name="e"></param>
        public override void OnCheckPermssion(OnCheckPermssionArgs e)
        {
            //此处只需要检查“辅助资料编辑器”的新增权限即可
            e.FormId = "bd_enumeditor";
            e.PermItem = "fw_new";
        }

        /// <summary>
        /// 初始化数据上下文
        /// </summary>
        /// <param name="e"></param>
        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            e.Cancel = true;
        }

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            //验证辅助资料名称
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return ValidationName(newData);
            }).WithMessage("辅助资料类别名称不允许重复，请检查数据！"));

            //验证辅助资料枚举值
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return ValidationValue(newData);
            }).WithMessage("辅助资料枚举值不允许重复，请检查数据！"));
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var dataEntity = e.DataEntitys[0];

            var entitys = dataEntity["fentity"] as DynamicObjectCollection;

            //处理明细中的企业Id
            var pkid = dataEntity["id"] as string;
            if (!pkid.IsNullOrEmptyOrWhiteSpace())
            {
                var dm = this.GetDataManager();
                dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
                var dbData = dm.Select(pkid) as DynamicObject;
                var dbEntry = dbData?["fentity"] as DynamicObjectCollection;
                if (dbEntry != null)
                {
                    foreach (var item in entitys)
                    {
                        var _dbEntry = dbEntry.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(item["id"] as string));
                        if (_dbEntry != null)
                        {
                            item["fenmainorgid"] = _dbEntry["fenmainorgid"];
                        }
                    }
                }
            }
            foreach (var item in entitys)
            {
                if (item["fenmainorgid"].IsNullOrEmptyOrWhiteSpace())
                {
                    item["fenmainorgid"] = this.Context.Company;
                }
            }

            //如果是修改多企业共享的辅助资料，则在保存时需要将其他企业新增的辅助资料值做合并保存（避免覆盖其他企业新增的辅助资料值）
            if (dataEntity.DataEntityState.FromDatabase
                && !Convert.ToString(dataEntity["fmainorgid"]).EqualsIgnoreCase(this.Context.Company))
            {
                var sqlText = $@"
                select fentryid,fseq,fenumitem,fgroup,forder,fdisabled,fispreset,fenmainorgid,ftranid,
                fpublishcid,fpublishcid_txt,fpublishcid_pid,fchaindataid,ffromchaindataid,fsendstatus,
                fdataorigin,fsenddate,fdownloaddate,fmodifydate from t_bd_enumdataentry 
                where fenmainorgid<>@fenmainorgid and fenmainorgid<>' ' and fenmainorgid<>'0' and fid=@fid";

                var sqlParam = new List<SqlParam>
                {
                    new SqlParam("@fenmainorgid", System.Data.DbType.String, this.Context.Company),
                    new SqlParam("@fid", System.Data.DbType.String, dataEntity["id"]),
                };

                var entityDt = this.HtmlForm.GetEntity("fentity")?.DynamicObjectType;
                var entityObjs = this.DBService.ExecuteDynamicObject(this.Context, entityDt, sqlText, sqlParam);
                if (entityObjs != null && entityObjs.Count > 0)
                {
                    foreach (var entityObj in entityObjs)
                    {
                        //entitys.Add(entityObj);
                        if (entitys.Any(x => Convert.ToString(x["id"]) == Convert.ToString(entityObj["id"])) == false)
                        {
                            entitys.Add(entityObj);
                        }
                    }
                }
            }

            //更新多选辅助资料引用字段值
            UpdateBDSelectMultRefFieldValue(this.Context, this.HtmlForm, dataEntity);
        }

        /// <summary>
        /// 保存时删除缓存
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            if (e.DataEntitys == null) return;


            var svc = this.Container.GetService<IComboDataService>();
            List<string> names = e.DataEntitys.Select(s => s["fname"] as string).ToList();

            if (this.Context.IsTopOrg)
            {
                Task.Run(() =>
                {
                    foreach (var name in names)
                    {
                        svc.ClearCache(this.Context, name);
                    }
                });
            }
            else
            {
                foreach (var name in names)
                {
                    svc.ClearCache(this.Context, name);
                }
            }

        }

        /// <summary>
        /// 验证辅助资料名称
        /// </summary>
        /// <param name="newData"></param>
        /// <returns></returns>
        private bool ValidationName(DynamicObject newData)
        {
            string name = Convert.ToString(newData["fname"]);

            var formMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "bd_enumdata");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, formMeta.GetDynamicObjectType(this.Context));

            string where = "";
            string id = Convert.ToString(newData["id"]);
            if (id.IsNullOrEmptyOrWhiteSpace())
            {
                where = "fname='{0}' ".Fmt(name);
            }
            else
            {
                where = "fname='{0}' and t_bd_enumdata.fid != '{1}' ".Fmt(name, id);
            }

            DynamicObject dyObj = dm.SelectBy(where).OfType<DynamicObject>().FirstOrDefault();

            return dyObj == null ? true : false;
        }

        /// <summary>
        /// 验证辅助资料枚举值
        /// </summary>
        /// <param name="newData"></param>
        /// <returns></returns>
        private bool ValidationValue(DynamicObject newData)
        {
            DynamicObjectCollection entry = newData["fentity"] as DynamicObjectCollection;
            if (entry == null && entry.Count <= 0)
            {
                return true;
            }

            List<string> enumItems = new List<string>();

            foreach (DynamicObject item in entry)
            {
                var enumItem = item["fenumitem"] as string;
                var enmainorgid = item["fenmainorgid"] as string;
                if (enumItem.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }
                enumItem = string.Format("{0}_{1}", enumItem, enmainorgid);
                if (enumItems.Contains(enumItem, StringComparer.OrdinalIgnoreCase))
                {
                    return false;
                }
                enumItems.Add(enumItem);
            }

            enumItems = null;

            return true;
        }

        /// <summary>
        /// 更新多选基础资料引用字段值
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="hForm"></param>
        /// <param name="dataEntity"></param>
        private Task UpdateBDSelectMultRefFieldValue(UserContext userCtx, HtmlForm hForm, DynamicObject dataEntity)
        {
            // 判断是否基础资料
            if (hForm.ElementType != HtmlElementType.HtmlForm_BaseForm) return Task.CompletedTask;

            // 只对已在数据库的数据有用
            if (!dataEntity.DataEntityState.FromDatabase) return Task.CompletedTask;

            // 判断是否改名字了
            var dbDataEntity =
                userCtx.LoadBizDataById(hForm.Id, Convert.ToString(dataEntity["id"]));

            var changeNameEntrys = new List<DynamicObject>();
            var entrys = (DynamicObjectCollection)dataEntity["fentity"];
            var dbEntrys = (DynamicObjectCollection)dbDataEntity["fentity"];

            foreach (var entry in entrys)
            {
                string id = Convert.ToString(entry["id"]);

                var dbEntry = dbEntrys.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(id));
                // 数据库不存在，表示是新数据，忽略
                if (dbEntry == null) continue;

                // 同名忽略
                if (Convert.ToString(entry["fenumitem"]) == (Convert.ToString(dbEntry["fenumitem"]))) continue;

                changeNameEntrys.Add(entry);
            }

            if (changeNameEntrys.Count == 0) return Task.CompletedTask;

            return Task.Run(() =>
            {
                var svc = userCtx.Container.GetService<IDBService>();
                var logger = userCtx.Container.GetService<ILogServiceEx>();
                var dbServiceEx = userCtx.Container.GetService<IDBServiceEx>();

                #region 获取引用字段
                string sql = @"/*dialect*/select frefformid,frefformname,freffieldkey,
                            freffielddesc,freftablename,freffieldname                          
                            from t_sys_lookupboject with(nolock) 
                            where freftablename<>' ' and freftablename<>'' 
                                   and freffieldname<>'' And freffieldname<>' ' 
                                  and fbdformid='bd_enum' 
                                  and freftablename in (select [name] from SYS.tables) ";

                List<HtmlField> fields = new List<HtmlField>();

                using (var datas = svc.ExecuteReader(userCtx, sql))
                {
                    while (datas.Read())
                    {
                        var refFormId = datas["frefformid"]?.ToString();
                        HtmlForm refForm = null;
                        try
                        {
                            refForm = HtmlParser.LoadFormMetaFromCache(refFormId, userCtx);
                            if (refForm == null)
                            {
                                continue;
                            }
                        }
                        catch (Exception)
                        {
                            continue;
                        }

                        var fldKey = datas["freffieldkey"]?.ToString();
                        var fld = refForm.GetField(fldKey);
                        if (fld == null)
                        {
                            continue;
                        }

                        if (!(fld is IMultTextField))
                        {
                            continue;
                        }

                        fields.Add(fld);
                    }
                }
                #endregion

                if (fields.Count == 0) return;

                try
                {
                    foreach (var entry in changeNameEntrys)
                    {
                        // 找出数据
                        string id = Convert.ToString(entry["id"]);
                        string name = Convert.ToString(entry["fenumitem"]);

                        foreach (var field in fields)
                        {
                            #region 更新引用字段

                            // 引用字段所在表主键、引用字段列名、引用字段名字列名、引用表名
                            string refPKFieldName = field.Entity.PkFieldName;
                            string refFiledName = field.FieldName;
                            string refFiledNameTxt = field.FieldName + "_txt";
                            string refTableName = field.Entity.TableName;

                            sql = $@"select {refPKFieldName}, {refFiledName}, {refFiledNameTxt} from {refTableName} where {refFiledName} like '%{id}%'";

                            List<string> batchSqls = new List<string>();

                            using (var datas = svc.ExecuteReader(userCtx, sql))
                            {
                                while (datas.Read())
                                {
                                    var pkid = datas[refPKFieldName]?.ToString();
                                    var fieldId = datas[refFiledName]?.ToString();
                                    var fieldName = datas[refFiledNameTxt]?.ToString();

                                    var fieldIds = fieldId.Split(new[] { "," }, StringSplitOptions.None);
                                    var fieldNames = fieldName.Split(new[] { "," }, StringSplitOptions.None);

                                    for (int i = 0; i < fieldIds.Length; i++)
                                    {
                                        if (fieldIds[i].EqualsIgnoreCase(id))
                                        {
                                            if (fieldNames.Length >= i + 1)
                                            {
                                                fieldNames[i] = name;
                                            }
                                        }
                                    }

                                    fieldName = string.Join(",", fieldNames);

                                    batchSqls.Add($@"update {refTableName} set {refFiledNameTxt}='{fieldName}' where {refPKFieldName} = '{pkid}';");
                                }
                            }

                            if (batchSqls.Count == 0) continue;

                            dbServiceEx.ExecuteBatch(userCtx, batchSqls);

                            #endregion
                        }
                    }
                }
                catch (Exception ex)
                {
                    logger.Error("辅助资料更新缓存失败！", ex);
                }
            });
        }
    }
}