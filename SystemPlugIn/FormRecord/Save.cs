using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.CustomException;
using JieNor.Framework.AppService.ScheduledTask;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.Interface.UsrMgr;
using System.Collections.Concurrent;

namespace JieNor.Framework.AppService.SystemPlugIn.FormRecord
{
    /// <summary>
    /// 表单记录保存
    /// </summary>
    [InjectService]
    [FormId("bd_record")]
    [OperationNo("savelog")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!newData["freminddate"].IsNullOrEmptyOrWhiteSpace()
                    && Convert.ToDateTime(newData["freminddate"]) < DateTime.Now)
                {
                    return false;
                }
                return true;
            }).WithMessage("下次提醒时间不能小于当前系统时间！"));
        }



        static Regex _regexscript = new Regex("<script[^>]*?>.*?</script>", RegexOptions.IgnoreCase);

        static Regex _regexTagA = new Regex("<a[^>]+?href=\"([^\"]+)\"[^>]*>([^<]+)</a>", RegexOptions.IgnoreCase);

        static Regex _regexTagImg = new Regex(@"<img(.[^<]*)src=\""?(.[^<\""]*)\""?(.[^<]*)\/?>", RegexOptions.IgnoreCase);


        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            if (e.DataEntitys.Length > 1) throw new BusinessException($"该操作不支持批量执行！");
            var dataEntity = e.DataEntitys[0];

            var bizFormId = dataEntity["fbizformid"] as string;
            if (bizFormId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentException("业务对象表单ID为空，无法保存记录！");
            }

            //记录内容
            var content = Convert.ToString(dataEntity["fcontent"]);
            //删除脚本  
            content = _regexscript.Replace(content, "");

            var logServiceEx = this.Container.GetService<ILogServiceEx>();
            var bizLogDt = logServiceEx.LoadFormLogType(this.Context, bizFormId);

            //创建记录对象
            var logObj = bizLogDt.CreateInstance() as DynamicObject;
            //由于业务对象日志模型是由 bd_record 复制而来的，所以此处需要特殊处理（将所有字段值设置为已脏，目的是让系统保存本次所有赋值的字段值）
            logObj.DataEntityState.SetDirty(true);

            logObj["fbizformid"] = bizFormId;
            logObj["fbizobjid"] = dataEntity["fbizobjid"];
            logObj["ftype"] = "recordtype_01";
            logObj["fisremind"] = dataEntity["fisremind"];
            logObj["freminddate"] = dataEntity["freminddate"];
            logObj["fnotremind"] = false;
            logObj["fremindstatus"] = false;
            logObj["foperator"] = this.Context.DisplayName.IsNullOrEmptyOrWhiteSpace() ? this.Context.UserName : this.Context.DisplayName;
            logObj["fopdate"] = DateTime.Now;
            logObj["fopcode"] = "savelog";
            logObj["fopname"] = "提交记录";

            var permiSvc = this.Container.GetService<IPermissionService>();
            var isDevOpsUser = permiSvc.IsDevOpsUser(Context, new MetaCore.PermData.PermAuth(this.Context));
            logObj["fisdevops"] = isDevOpsUser ? true : false;

            //提取内容中所有的 fileId
            List<string> fileIds = GetHtmlAttrValues(content, "fileid", false);
            List<string> fileExts = GetHtmlAttrValues(content, "fileext", true);
            List<string> fileNames = GetHtmlAttrValues(content, "title", true);
            if (fileIds.Count > 0 && fileIds.Count == fileExts.Count && fileIds.Count == fileNames.Count)
            {
                //手动添加附件明细
                var entryEntity = this.HtmlForm.GetEntryEntity("fattachentry");
                var entryRowObjs = entryEntity.DynamicProperty.GetValue<DynamicObjectCollection>(logObj);

                for (int i = 0; i < fileIds.Count; i++)
                {
                    //或者 new DynamicObject(entryEntity.DynamicObjectType);
                    var attachEntry = entryEntity.DynamicObjectType.CreateInstance() as DynamicObject;
                    attachEntry["fseq"] = i + 1;
                    attachEntry["fattachid"] = fileIds[i];
                    attachEntry["fattachext"] = fileExts[i];
                    attachEntry["fattachname"] = fileNames[i];
                    entryRowObjs.Add(attachEntry);
                }
            }

            //删除所有的 a 标签
            content = _regexTagA.Replace(content, "");

            //删除所有的 img 标签
            content = _regexTagImg.Replace(content, "");

            logObj["fcontent"] = content;

            DynamicObject[] logObjs = new DynamicObject[] { logObj };

            //对数据对象预处理
            var prepareSaveService = this.Context?.Container.GetService<IPrepareSaveDataService>();
            prepareSaveService?.PrepareDataEntity(this.Context, this.HtmlForm, logObjs, OperateOption.Create());

            //保存
            var recordDm = this.Context.Container?.GetService<IDataManager>();
            recordDm.InitDbContext(this.Context, bizLogDt);
            recordDm.Save(logObjs);

            //将动态添加至动态提醒轮盘中
            DynamicRemindWheel.GetInstance().Add(this.Context, logObj);

            this.Result.IsSuccess = true;
            this.Result.SrvData = new
            {
                id = logObjs[0]["id"]
            };

            bizLogDt = null;

            ////匹配所有 a 标签的正则
            //var aPattern = "<a[^>]+?href=\"([^\"]+)\"[^>]*>([^<]+)</a>";
            ////匹配所有 img 标签的正则
            //var imgPattern = @"<img(.[^<]*)src=\""?(.[^<\""]*)\""?(.[^<]*)\/?>";
        }


        static ConcurrentDictionary<string, Regex> _mcList = new ConcurrentDictionary<string, Regex>(StringComparer.OrdinalIgnoreCase);

        /// <summary>
        /// 提取 html 字符串中所有标签中指定的属性值
        /// </summary>
        /// <param name="html">html代码</param>
        /// <param name="attrName">属性名，比如：fileid="abcd"</param>
        /// <param name="allowRepeat">允许重复，比如：重复的属性值</param>
        /// <returns>属性值集合</returns>
        private List<string> GetHtmlAttrValues(string html, string attrName, bool allowRepeat = true)
        {
            List<string> result = new List<string>();

            string pattern = string.Format("[^>]*?{0}=(['\"\"]?)(?<attr>[^'\"\"\\s>]+)\\1[^>]*", attrName);
            if (!_mcList.ContainsKey(attrName))
            {
                _mcList[attrName] = new Regex(pattern, RegexOptions.IgnoreCase);
            }

            MatchCollection mcList = _mcList[attrName].Matches(html);
            foreach (Match m in mcList)
            {
                string attrValue = m.Groups["attr"].Value;
                if (!allowRepeat)
                {
                    if (!result.Contains(attrValue))
                    {
                        result.Add(attrValue);
                    }
                }
                else
                {
                    result.Add(attrValue);
                }
            }

            return result;
        }
    }
}