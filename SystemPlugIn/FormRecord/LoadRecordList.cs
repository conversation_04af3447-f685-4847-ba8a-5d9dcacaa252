using System;
using System.Text;
using System.Linq;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface.UsrMgr;

namespace JieNor.Framework.AppService.SystemPlugIn.FormRecord
{
    /// <summary>
    /// 加载表单记录列表
    /// </summary>
    [InjectService]
    [FormId("bd_record")]
    [OperationNo("loadrecordlist")]
    public class LoadRecordList : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 业务表单ID
        /// </summary>
        protected virtual string BizFormId
        {
            get { return this.GetQueryOrSimpleParam<string>("bizformid"); }
        }

        /// <summary>
        /// 业务单据主键ID
        /// </summary>
        protected virtual string BizObjId
        {
            get { return this.GetQueryOrSimpleParam<string>("bizobjid"); }
        }

        /// <summary>
        /// 记录类型：recordtype_01 手工记录，recordtype_02 系统日志
        /// </summary>
        protected virtual string Type
        {
            get { return this.GetQueryOrSimpleParam<string>("type"); }
        }

        /// <summary>
        /// 操作码
        /// </summary>
        protected virtual string[] OpCode
        {
            get { return null; }
        }

        /// <summary>
        /// 排序方式
        /// </summary>
        protected virtual Enu_Sortord Sortord
        {
            get { return Enu_Sortord.Ascending; }
        }

        /// <summary>
        /// 排序字段
        /// </summary>
        protected virtual string OrderField
        {
            get { return "fcreatedate"; }
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (this.BizFormId.IsNullOrEmptyOrWhiteSpace() || this.BizObjId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentException("业务对象表单ID为空 或者 业务对象ID为空，无法获取记录！");
            }

            StringBuilder sbWhere = new StringBuilder();
            sbWhere.AppendFormat("fbizformid='{0}' and fbizobjid='{1}' ", this.BizFormId, this.BizObjId);
            if (!this.Type.IsNullOrEmptyOrWhiteSpace())
            {
                string[] types = this.Type.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                if (types != null)
                {
                    sbWhere.AppendFormat(" and ftype in('{0}') ", string.Join("','", types));
                }
            }
            if (this.OpCode != null && this.OpCode.Length > 0)
            {
                sbWhere.AppendFormat(" and fopcode in('{0}') ", string.Join("','", this.OpCode));
            }

            var permiSvc = this.Container.GetService<IPermissionService>();
            var isDevOpsUser = permiSvc.IsDevOpsUser(Context, new MetaCore.PermData.PermAuth(Context));
            if (!isDevOpsUser)
            {
                sbWhere.AppendFormat(" and fisdevops='0' ");
            }

            var logServiceEx = this.Container.GetService<ILogServiceEx>();
            var bizLogDt = logServiceEx.LoadFormLogType(this.Context, this.BizFormId);
            var dm = this.Context.Container?.TryGetService<IDataManager>();
            dm.InitDbContext(this.Context, bizLogDt);
            var dataEntitys = dm.SelectBy(sbWhere.ToString()).OfType<DynamicObject>()?.ToList();

            bizLogDt = null;

            //获取特定表单的上游单据的动态记录
            dataEntitys.AddRange(this.GetSpecificFormRecord(isDevOpsUser));

            //排序
            if (!this.OrderField.IsNullOrEmptyOrWhiteSpace())
            {
                if (this.Sortord == Enu_Sortord.Descending)
                {
                    dataEntitys = dataEntitys.OrderByDescending(t => t[this.OrderField]).ToList();
                }
                else
                {
                    dataEntitys = dataEntitys.OrderBy(t => t[this.OrderField]).ToList();
                }
            }
            else
            {
                //默认按操作时间升序
                dataEntitys = dataEntitys.OrderBy(t => t["fopdate"]).ToList();
            }

            var uiConverter = this.Context.Container.GetService<IUiDataConverter>();
            JArray jarray = new JArray();
            foreach (var dataEntity in dataEntitys)
            {
                //下次提醒日期如果已过期，则不用返回到前端（前端根据是否为空来显示下次提醒日期）
                if (!dataEntity["freminddate"].IsNullOrEmptyOrWhiteSpace()
                    && Convert.ToDateTime(dataEntity["freminddate"]) < new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day))
                {
                    dataEntity["freminddate"] = null;
                }

                var billJsonData = uiConverter.CreateUIDataObject(this.Context, this.HtmlForm, dataEntity as DynamicObject);
                jarray.Add(billJsonData);
            }
            this.Result.SrvData = new { records = jarray };
        }

        /// <summary>
        /// 获取特定表单的上游单据的动态记录
        /// 1.销售机会 => 销售线索
        /// 2.客户 => 销售机会 => 销售线索
        /// </summary>
        /// <returns></returns>
        private List<DynamicObject> GetSpecificFormRecord(bool isDevOpsUser)
        {
            var recordList = new List<DynamicObject>();
            IDataManager dm = null;

            if (this.BizFormId.EqualsIgnoreCase("ydj_customerrecord"))
            {
                dm = this.Context.Container?.TryGetService<IDataManager>();

                //查询销售机会
                var customerRecordForm = this.MetaModelService.LoadFormModel(this.Context, this.BizFormId);
                dm.InitDbContext(this.Context, customerRecordForm.GetDynamicObjectType(this.Context));
                var customerRecord = dm.Select(this.BizObjId) as DynamicObject;
                if (customerRecord != null)
                {
                    //销售线索关联的动态记录
                    recordList.AddRange(this.GetRecordsByBizObjId("ydj_leads", customerRecord["fleadssourceid"] as string,   isDevOpsUser));
                }
            }
            else if (this.BizFormId.EqualsIgnoreCase("ydj_customer"))
            {
                dm = this.Context.Container?.TryGetService<IDataManager>();

                //查询客户
                var customerForm = this.MetaModelService.LoadFormModel(this.Context, this.BizFormId);
                dm.InitDbContext(this.Context, customerForm.GetDynamicObjectType(this.Context));
                var customer = dm.Select(this.BizObjId) as DynamicObject;
                if (customer != null)
                {
                    //销售机会关联的动态记录
                    recordList.AddRange(this.GetRecordsByBizObjId("ydj_customerrecord", customer["fsourceid"] as string,   isDevOpsUser));

                    //查询销售机会
                    if (!customer["fsourceid"].IsNullOrEmptyOrWhiteSpace())
                    {
                        var customerRecordForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_customerrecord");
                        dm.InitDbContext(this.Context, customerRecordForm.GetDynamicObjectType(this.Context));
                        var customerRecord = dm.Select(customer["fsourceid"]) as DynamicObject;
                        if (customerRecord != null)
                        {
                            //销售线索关联的动态记录
                            recordList.AddRange(this.GetRecordsByBizObjId("ydj_leads", customerRecord["fleadssourceid"] as string,   isDevOpsUser));
                        }
                    }
                }
            }
            return recordList;
        }

        /// <summary>
        /// 根据业务表单和业务对象Id查询动态记录
        /// </summary>
        /// <param name="formId"></param>
        /// <param name="bizObjId"></param>
        /// <returns></returns>
        private IEnumerable<DynamicObject> GetRecordsByBizObjId(string formId, string bizObjId,bool isDevOpsUser)
        {
            IEnumerable<DynamicObject> records = new List<DynamicObject>();
            if (!formId.IsNullOrEmptyOrWhiteSpace() && !bizObjId.IsNullOrEmptyOrWhiteSpace())
            {
                var logServiceEx = this.Container.GetService<ILogServiceEx>();
                var dm = this.Context.Container?.TryGetService<IDataManager>();
                var bizLogDt = logServiceEx.LoadFormLogType(this.Context, formId);
                dm.InitDbContext(this.Context, bizLogDt );
                var where = $"fmainorgid='{this.Context.Company}' and fbizformid='{formId}' and fbizobjid='{bizObjId}' and ftype='recordtype_01'";
                if(!  isDevOpsUser)
                {
                    where += " and fisdevops='0' ";
                }

                records = dm.SelectBy(where).OfType<DynamicObject>();

                bizLogDt = null;
            }
            return records;
        }
    }
}