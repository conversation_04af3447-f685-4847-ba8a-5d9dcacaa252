using System;
using System.Collections.Generic;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.AppService.ScheduledTask;

namespace JieNor.Framework.AppService.SystemPlugIn.FormRecord
{
    /// <summary>
    /// 删除表单记录
    /// </summary>
    [InjectService]
    [FormId("bd_record")]
    [OperationNo("deleterecord")]
    public class DeleteRecord : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var bizFormId = this.GetQueryOrSimpleParam<string>("bizformid");
            var id = this.GetQueryOrSimpleParam<string>("id");
            if (bizFormId.IsNullOrEmptyOrWhiteSpace()|| id.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentException("业务对象表单ID为空 或者 记录ID为空，无法删除！");
            }

            var logServiceEx = this.Container.GetService<ILogServiceEx>();
            var bizLogDt = logServiceEx.LoadFormLogType(this.Context, bizFormId);

            var metaSrv = this.Context.Container?.TryGetService<IMetaModelService>();
            var dm = this.Context.Container?.TryGetService<IDataManager>();
            dm.InitDbContext(this.Context, bizLogDt);
            var record =  dm.Select(id) as DynamicObject;
            if (record == null)
            {
                throw new BusinessException("记录不存在，可能已被删除！");
            }

            if (!Convert.ToString(record["fcreatorid"]).EqualsIgnoreCase(this.Context.UserId))
            {
                throw new BusinessException("记录不是您自己提交的，不允许删除！");
            }

            List<object> lstPkId = new List<object>() { id };

            //执行删除操作
            dm.Delete(lstPkId);

            //将动态记录设置为不再提醒
            DynamicRemindWheel.GetInstance().SetNotRemind(this.Context, bizFormId, id);

            bizLogDt = null;

            this.Result.IsSuccess = true;
        }
    }
}