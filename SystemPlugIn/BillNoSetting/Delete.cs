using JieNor.Framework.Consts;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.FormService;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.SystemPlugIn.BillNoSetting
{
    /// <summary>
    /// 删除操作
    /// </summary>
    [InjectService]
    [FormId("bas_billnosetting")]
    [OperationNo("Delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0)
            {
                return;
            }

            var pubSubService = this.Container.GetService<IPubSubService>();
            pubSubService.PublishMessage<string>(DataTransferObject.Const.PubSubChannel.BillNoSetting, "");
        }

       
    }
}
