using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.BPM
{
    /// <summary>
    /// 修改单据页面打开时的标题
    /// </summary>
    public interface ISetFormCaption
    {
        /// <summary>
        /// 修改标题
        /// </summary>
        /// <param name="formId">表单id</param>
        /// <param name="paras">参数</param>
        /// <param name="operationNo">操作码</param>
        /// <param name="billPkId">当前实体主键</param>
        /// <returns></returns>
        string SetFormCaption(UserContext ctx, string formId, Dictionary<string, object> paras, string operationNo = "", string billPkId = "");
        /// <summary>
        /// 修改单据主题（oa系统中消息主题）
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="form"></param>
        /// <param name="caption"></param>
        /// <param name="billNo"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        string SetFormTitle(UserContext ctx, HtmlForm form, string caption, string billNo, DynamicObject data);
    }
}
