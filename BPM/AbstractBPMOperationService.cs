using Autofac.Features.Metadata;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.BPM;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.BPM;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.OpData;
using JieNor.Framework.MetaCore.PermData;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.BPM
{

    /// <summary>
    /// BPM系统请求
    /// </summary> 
    public abstract  class AbstractBPMOperationService : IBPMOperationService
    {



        /// <summary>
        /// 连接上下文
        /// </summary>
        protected UserContext Context
        {
            get;
            set;
        }



        /// <summary>
        /// 操作结果
        /// </summary>
        protected IOperationResult Result
        {
            get;
            set;
        }


        /// <summary>
        /// 请求参数
        /// </summary>
        protected BPMFlowDTO ReqDTO
        {
            get;
            set;
        }


        /// <summary>
        /// 会话关联的服务容器
        /// </summary>
        protected IServiceContainer Container
        {
            get;
            private set;
        }


        /// <summary>
        /// 服务插件
        /// </summary>
        protected IBPMOperationServicePlugIn PlugIn
        {
            get;
            private set;
        }





        /// <summary>
        /// 初始化插件代理对象
        /// </summary> 
        protected virtual void InitializePlugIn(BPMFlowDTO reqPara)
        {
            if (this.PlugIn == null)
            {
                this.PlugIn = Container.GetService<IEnumerable<Meta<Lazy<IBPMOperationServicePlugIn>>>>()
                    .FirstOrDefault(o => o.Metadata.GetString("AliasName").EqualsIgnoreCase(BPMReqServiceConst.BPMSys)
                                        && o.Metadata.GetString("formid").Trim().EqualsIgnoreCase(ReqDTO.FormId)
                                        && o.Metadata.GetString("bpmname").Trim().EqualsIgnoreCase(ReqDTO.BPMName))?.Value.Value;
            }
            if (this.PlugIn == null)
            {
                this.PlugIn = Container.GetService<IEnumerable<Meta<Lazy<IBPMOperationServicePlugIn>>>>()
                    .FirstOrDefault(o => o.Metadata.GetString("AliasName").EqualsIgnoreCase(BPMReqServiceConst.BPMSys)
                                        && o.Metadata.GetString("formid").Trim().EqualsIgnoreCase("bpm.main")
                                        && o.Metadata.GetString("bpmname").Trim().EqualsIgnoreCase(ReqDTO.BPMName))?.Value.Value;
            }
        }



        /// <summary>
        /// 执行BPM系统请求服务逻辑
        /// </summary>
        /// <param name="ctx"></param> 
        /// <param name="reqPara"></param>
        public virtual IOperationResult Execute(UserContext ctx, BPMFlowDTO reqPara)
        {
            this.Context = ctx;
            if (ctx != null)
            {
                this.Container = ctx.Container;
            }
           
            ReqDTO = reqPara;
            InitializePlugIn(reqPara);

            var permSrv = ctx.Container.GetService<IPermissionService>();
            PermAuth pParam = new PermAuth();
            pParam.FormId = reqPara.FormId;

            switch (reqPara.ReqType)
            {
                case BPMReqServiceConst.BPMAfterExeProc:
                    this.Result = this.PlugIn?.OnBPMAfterExeProc(ctx, ReqDTO);
                    break;
                case BPMReqServiceConst.BPMBeforExeProc:
                    this.Result = this.PlugIn?.OnBPMBeforExeProc(ctx, ReqDTO);
                    break;
                case BPMReqServiceConst.BPMTemplateFldVal:
                    this.Result = this.PlugIn?.GetBPMTemplateFldVal(ctx, ReqDTO);
                    break;
                case BPMReqServiceConst.BPMFlowChange:
                    this.Result = this.PlugIn?.OnBPMFlowChange(ctx, ReqDTO);
                    break;
                case BPMReqServiceConst.BPMProcessEvent:
                    this.Result = this.PlugIn?.GetBPMProcessEvent(ctx, ReqDTO);
                    break;
                case BPMReqServiceConst.BPMTemplate:
                    this.Result = this.PlugIn?.GetBPMTemplate(ctx, ReqDTO);
                    break;
                case BPMReqServiceConst.BPMTemplateFields:
                    this.Result = this.PlugIn?.GetBPMTemplateFields(ctx, ReqDTO);
                    break;
                case BPMReqServiceConst.BPMDeleteProcess:
                    this.Result = this.PlugIn?.DeleteProcess(ctx, ReqDTO);
                    break;
                case BPMReqServiceConst.IsExistProcess:
                    this.Result = this.PlugIn?.IsExistProcess(ctx, ReqDTO);
                    break;
                case BPMReqServiceConst.CanApprovalProcess:
                    this.Result = this.PlugIn?.CanApprovalProcess(ctx, ReqDTO);
                    break;
                case BPMReqServiceConst.BPMCreateProcess:
                    pParam.OperationName = "提交BPM流程";
                    pParam.PermId = "fw_scrm_createprocess";
                    pParam.UserId = ctx.UserId;
                    if (ctx.IsTempToken)
                    {  
                        //如果是通过bpm系统或oa系统的代办任务打开的，就不再验证权限了
                        permSrv?.CheckPermission(ctx, pParam);
                    }
                    this.Result = this.PlugIn?.CreateProcess(ctx, ReqDTO);
                    break;
                case BPMReqServiceConst.BPMApproveProcess :
                    pParam.OperationName = "提交BPM流程";
                    pParam.PermId = "fw_scrm_createprocess";
                    pParam.UserId = ctx.UserId;
                    if (ctx.IsTempToken)
                    {
                        //如果是通过bpm系统或oa系统的代办任务打开的，就不再验证权限了
                        permSrv?.CheckPermission(ctx, pParam);
                    }
                    this.Result = this.PlugIn?.ApproveProcess(ctx, ReqDTO);
                    break;
                case BPMReqServiceConst.ShowBPMProcess :
                    pParam.OperationName = "查看BPM流程";
                    pParam.PermId = "scrm_showprocess";
                    pParam.UserId = ctx.UserId;
                    if (ctx.IsTempToken)
                    {
                        //如果是通过bpm系统或oa系统的代办任务打开的，就不再验证权限了
                        permSrv?.CheckPermission(ctx, pParam);
                    }
                    this.Result = this.PlugIn?.ShowProcess(ctx, ReqDTO);
                    break;
                case BPMReqServiceConst.ShowAuditList:
                    pParam.OperationName = "查看BPM审批日志";
                    pParam.UserId = ctx.UserId;
                    this.Result = this.PlugIn?.ShowAuditList(ctx, ReqDTO);
                    break;
                case BPMReqServiceConst.ShowChartProcess:
                    pParam.OperationName = "查看BPM流程图";
                    pParam.UserId = ctx.UserId;
                    this.Result = this.PlugIn?.ShowChartProcess(ctx, ReqDTO);
                    break;
                default:
                    throw new Exception("未知的请求类型 {0}".Fmt(reqPara.ReqType));
            }
            if(this.Result ==null )
            {
                this.Result = Container.GetService<BPMFlowDTOResponse>()?.OperationResult;
            }
            return this.Result;
        }






    }


}
