using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.BPM;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface.BPM;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.BPM
{






    /// <summary>
    /// BPM系统服务请求插件
    /// </summary>
    public abstract class AbstractBPMOperationServicePlugin : IBPMOperationServicePlugIn
    {
        protected const string FinishAudit = "FW.FinishAudit";
        protected const string RejectFlow = "FW.RejectFlow";


        /// <summary>
        /// 获取流程监听事件信息：用于BPM流程设计中选择节点的监听事件
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="reqPara"></param>
        /// <returns></returns>
        public virtual IOperationResult GetBPMProcessEvent(UserContext ctx, BPMFlowDTO reqPara)
        {
            var events = new List<BPMProcessEventInfo>();

            BPMProcessEventInfo m = new BPMProcessEventInfo();
            m.ServiceId = FinishAudit;
            m.ServiceCaption = "流程审批结束";
            m.ServiceIdDesc = "流程审批结束：反写单据上的审批状态";

            events.Add(m);

            var reject = new BPMProcessEventInfo();
            reject.ServiceId = RejectFlow;
            reject.ServiceCaption = "流程驳回";
            reject.ServiceIdDesc = "流程驳回：反写单据上的审批状态为待提交状态";

            events.Add(reject);

            IOperationResult result = new OperationResult();
            result.IsSuccess = true;
            result.SrvData = Newtonsoft.Json.JsonConvert.SerializeObject(events);

            return result;
        }



        /// <summary>
        /// 获取业务对象简要信息：用于BPM流程设计时选择对应的业务单据
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="reqPara"></param>
        /// <returns></returns>
        public virtual IOperationResult GetBPMTemplate(UserContext ctx, BPMFlowDTO reqPara)
        {
            IOperationResult result = new OperationResult();
            var tmpls = new List<BPMBizObjectInfo>();
            string strSql = $@"select distinct t0.fbillformid,t0.fname
                                                ,t2.fname fmodulename
				                                ,t2.fmoduleid
				                                ,t2.forder as fmdlorder
				                                ,t1.fgroupid
				                                ,t1.forder as fgrporder
				                                ,t0.fmenuid
				                                ,t0.forder
                                from T_SYS_MENUITEM t0 
                                inner join T_SYS_MENUGROUP t1 on t0.fgroupid=t1.fgroupid 
                                inner join T_SYS_BIZMODULE t2 on t1.fmoduleid=t2.fmoduleid ";
            if (!reqPara.ModelId.IsNullOrEmptyOrWhiteSpace())
            {
                strSql += $@" where t2.fmoduleid = '" + reqPara.ModelId + "' ";
            }
            strSql += $@" order by t2.forder, t2.fmoduleid, t1.forder,t1.fgroupid,t0.forder,t0.fmenuid ";

            var svc = ctx.Container.GetService<IDBService>();
            var metaService = ctx.Container.GetService<IMetaModelService>();
            using (var reader = svc.ExecuteReader(ctx, strSql))
            {
                while (reader.Read())
                {
                    var bizObjId = reader.GetValue<string>("fbillformid");
                    if (bizObjId.IsNullOrEmptyOrWhiteSpace()) continue;
                    try
                    {
                        BPMBizObjectInfo tmpl = new BPMBizObjectInfo();
                        tmpl.ModelId = reader.GetString("fmoduleid");
                        tmpl.ModelCaption = reader.GetString("fmodulename");
                        tmpl.FormId = bizObjId;
                        tmpl.FormCaption = reader.GetString("fname");
                        tmpl.FormUrl = "shell.html";

                        tmpls.Add(tmpl);
                    }
                    catch
                    {
                        //吃掉异常，
                    }
                }
            }
            result.IsSuccess = true;
            result.SrvData = Newtonsoft.Json.JsonConvert.SerializeObject(tmpls);

            return result;
        }



        /// <summary>
        /// 获取业务对象字段信息：用于BPM流程设计中选择对应的字段或变量，比如分支流转的条件设置需要某些字段
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="reqPara"></param>
        /// <returns></returns>
        public virtual IOperationResult GetBPMTemplateFields(UserContext ctx, BPMFlowDTO reqPara)
        {
            IOperationResult result = new OperationResult();
            var flds = new List<BPMFieldInfo>();
            if (!reqPara.FormId.IsNullOrEmptyOrWhiteSpace())
            {
                HtmlForm meta = HtmlParser.LoadFormMetaFromCache(reqPara.FormId.Trim(), ctx);
                var el = meta.GetEntryFieldList(meta.HeadEntity.Id);
                foreach (var item in el)
                {
                    if (item.Visible == 0)
                    {
                        continue;
                    }
                    BPMFieldInfo fld = new BPMFieldInfo();
                    fld.FieldId = item.Id;
                    fld.FieldName = item.Caption;
                    fld.FldElementType = item.ElementType;
                    fld.FldElementTypeDesc = item.ElementType.ToTypeDesc();
                    flds.Add(fld);
                }
            }

            result.IsSuccess = true;
            result.SrvData = Newtonsoft.Json.JsonConvert.SerializeObject(flds);

            return result;
        }


        /// <summary>
        /// 获取流程实例字段（变量）值信息：字段（变量）值提供给BPM流程实例，
        /// 以便流程执行时能够依据这些做相应判断处理
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="reqPara"></param>
        /// <returns></returns>
        public virtual IOperationResult GetBPMTemplateFldVal(UserContext ctx, BPMFlowDTO reqPara)
        {
            IOperationResult result = new OperationResult();
            var flds = new List<BPMFieldValueInfo>();
            if (!reqPara.FormId.IsNullOrEmptyOrWhiteSpace() && reqPara.FieldList.Length > 0)
            {
                HtmlForm meta = HtmlParser.LoadFormMetaFromCache(reqPara.FormId.Trim(), ctx);
                DynamicObject data = GetBillData(ctx, meta, reqPara);
                if (data != null)
                {
                    ctx.Container.GetService<LoadReferenceObjectManager>()?.Load(ctx, meta.GetDynamicObjectType(ctx), data, false);
                }
                foreach (var fldId in reqPara.FieldList)
                {
                    if (data == null || fldId.IsNullOrEmptyOrWhiteSpace())
                    {
                        continue;
                    }
                    HtmlField hfld = meta.GetField(fldId);
                    if (hfld == null)
                    {
                        continue;
                    }

                    BPMFieldValueInfo fldVal = new BPMFieldValueInfo();
                    fldVal.FieldId = fldId;
                    fldVal.FieldValue = GetFieldValue(ctx, data, hfld, meta);

                    flds.Add(fldVal);
                }
            }

            result.IsSuccess = true;
            result.SrvData = Newtonsoft.Json.JsonConvert.SerializeObject(flds);

            return result;
        }


        /// <summary>
        /// 依据流程实例，获取对应单据数据
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="meta"></param>
        /// <param name="reqPara"></param>
        /// <returns></returns>
        protected DynamicObject GetBillData(UserContext ctx, HtmlForm meta, BPMFlowDTO reqPara)
        {
            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));
            DynamicObject data = dm.Select(reqPara.RealBillPKId, null) as DynamicObject;

            return data;
        }


        /// <summary>
        /// 获取字段值 TODO 依据不同类型获取
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="data"></param>
        /// <param name="hfld"></param>
        /// <param name="hform"></param>
        /// <returns></returns>
        protected string GetFieldValue(UserContext ctx, DynamicObject data, HtmlField hfld, HtmlForm hform)
        {
            switch (hfld.ElementType)
            {
                // 基础资料属性字段
                case HtmlElementType.HtmlField_BasePropertyField:
                    var mulFld = hfld as HtmlBasePropertyField;
                    var conFld = hform.GetField(mulFld.ControlFieldKey) as HtmlBaseDataField;
                    if (conFld.IsNullOrEmptyOrWhiteSpace())
                    {
                        return "";
                    }
                    var refForm = conFld.RefHtmlForm(ctx);
                    if (refForm.IsNullOrEmptyOrWhiteSpace())
                    {
                        return "";
                    }
                    var disFld = refForm.GetField(mulFld.DisplayFieldKey);
                    if (disFld.IsNullOrEmptyOrWhiteSpace())
                    {
                        return "";
                    }
                    DynamicObject refDy = data[conFld.Id + "_ref"] as DynamicObject;
                    if (refDy.IsNullOrEmptyOrWhiteSpace())
                    {
                        return "";
                    }

                    return GetFieldValue(ctx, refDy, disFld, refForm);
                //多选基础资料
                case HtmlElementType.HtmlField_MulBaseDataField:
                    return GetMulBDFldDatas(ctx, data, hfld);
                //文本字段
                case HtmlElementType.HtmlField_TextField:
                // 单据编号
                case HtmlElementType.HtmlField_BillNoField:
                // 图片字段
                case HtmlElementType.HtmlField_ImageField:
                //多语言文本字段
                case HtmlElementType.HtmlField_MultiLangTextField:
                //Json字段
                case HtmlElementType.HtmlField_JsonField:
                // 多选下拉列表字段
                case HtmlElementType.HtmlField_MulComboField:
                // 文件选择字段
                case HtmlElementType.HtmlField_FileField:
                    return data.GetValue<string>(hfld.Id, "");

                // 基础资料
                case HtmlElementType.HtmlField_BaseDataField:
                // 计量单位
                case HtmlElementType.HtmlField_UnitIdField:
                // 下拉列表字段
                case HtmlElementType.HtmlField_ComboField:
                // 创建人字段
                case HtmlElementType.HtmlField_CreaterField:
                // 最近修改人字段
                case HtmlElementType.HtmlField_ModifierField:
                // 单据状态字段
                case HtmlElementType.HtmlField_BillStatusField:
                // 单据类型字段
                case HtmlElementType.HtmlField_BillTypeField:
                // 用户字段
                case HtmlElementType.HtmlField_UserField:
                    var bdData = data[hfld.PropertyName + "_ref"] as DynamicObject;
                    if (bdData != null)
                    {
                        if (bdData.GetDataEntityType().Properties.ContainsKey("fnumber"))
                        {
                            return bdData.GetValue<string>("fnumber", "").ToString();
                        }
                        if (bdData.GetDataEntityType().Properties.ContainsKey("fenumitem"))
                        {
                            return bdData.GetValue<string>("fenumitem", "").ToString();
                        }
                        if (bdData.GetDataEntityType().Properties.ContainsKey("fname"))
                        {
                            return bdData.GetValue<string>("fname", "").ToString();
                        }
                    }
                    return "";
                // 复选框字段
                case HtmlElementType.HtmlField_CheckBoxField:
                    return data.GetValue<bool>(hfld.Id, false).ToString();

                // 整数字段、附件数、打印次数
                case HtmlElementType.HtmlField_IntegerField:
                case HtmlElementType.HtmlField_PrintCount:
                case HtmlElementType.HtmlField_AttchCount:
                case HtmlElementType.HtmlField_NotesCount:
                // 小数字段
                case HtmlElementType.HtmlField_DecimalField:
                // 数量字段
                case HtmlElementType.HtmlField_QtyField:
                // 单价字段
                case HtmlElementType.HtmlField_PriceField:
                // 金额字段
                case HtmlElementType.HtmlField_AmountField:
                // 尺寸字段
                case HtmlElementType.HtmlField_SizeField:
                    if (data[hfld.PropertyName].IsNullOrEmptyOrWhiteSpace())
                    {
                        return "0";
                    }
                    return data[hfld.PropertyName].ToString();

                // 日期(年月日)字段
                case HtmlElementType.HtmlField_DateField:
                // 长日期（年月日时分秒）字段
                case HtmlElementType.HtmlField_DateTimeField:
                // 创建日期字段
                case HtmlElementType.HtmlField_CreateDateField:
                // 最近修改日期字段
                case HtmlElementType.HtmlField_ModifyDateField:
                    return data.GetValue<string>(hfld.Id, null);

                default:
                    return data.GetValue<string>(hfld.Id, "");

            }
        }

        /// <summary>
        /// 获取多选基础资料字段的编码值
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="data"></param>
        /// <param name="hfld"></param>
        private string GetMulBDFldDatas(UserContext ctx, DynamicObject data, HtmlField hfld)
        {
            if (data == null)
            {
                return "";
            }

            HtmlMulBaseDataField bdFld = hfld as HtmlMulBaseDataField;
            if (bdFld == null)
            {
                return "";
            }
            var meta = bdFld.RefHtmlForm(ctx);
            var tableName = meta.HeadEntity.TableName;
            var noFld = meta.NumberFldKey;
            var ids = data.GetValue<string>(hfld.Id, "").Trim().Replace(",", "','");
            if (!ids.IsNullOrEmptyOrWhiteSpace())
            {
                var sql = "select {0} from {1} where {2} in ('{3}')".Fmt(noFld, tableName, meta.BillPKFldName, ids);
                if (meta.Isolate == "1" && meta.GetField("fmainorgid") != null)
                {
                    sql += " and (fmainorgid='' or fmainorgid=' ' or fmainorgid='{0}' )".Fmt(ctx.Company);
                }
                var svc = ctx.Container.GetService<IDBService>();
                var datas = svc.ExecuteDynamicObject(ctx, sql);
                var ret = (from p in datas
                           select p[0].ToString()).ToArray();

                return string.Join(";", ret);
            }

            return "";
        }





        /// <summary>
        /// 流程实例节点执行完成后的监听事件：用于BPM流程实例节点执行完成后的操作回掉事件
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="reqPara"></param>
        /// <returns></returns>
        public virtual IOperationResult OnBPMAfterExeProc(UserContext ctx, BPMFlowDTO reqPara)
        {
            IOperationResult result = new OperationResult();

            if (reqPara.ServiceId.EqualsIgnoreCase(FinishAudit))
            {
                // TODO 更新对应单据的状态
                result = WriteBackStatusWhenFinish(ctx, reqPara);
            }
            else if (reqPara.ServiceId.EqualsIgnoreCase(RejectFlow))
            {
                //驳回
                result = WriteBackStatusWhenReject(ctx, reqPara);
            }

            return result;
        }



        private IOperationResult WriteBackStatusWhenReject(UserContext ctx, BPMFlowDTO reqPara)
        {
            IOperationResult result = new OperationResult();
            HtmlForm meta = HtmlParser.LoadFormMetaFromCache(reqPara.FormId.Trim(), ctx);
            var data = GetBillData(ctx, meta, reqPara);

            if (data != null && !data["fflowinstanceid"].IsNullOrEmptyOrWhiteSpace())
            {
                if (data.DynamicObjectType.Properties.ContainsKey("fbpmauditdesc"))
                {
                    data["fbpmauditdesc"] = "BPM已驳回";
                }
                var dm = ctx.Container.GetService<IDataManager>();
                dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));
                dm.Save(data);

                UnAudit(ctx, meta, data);
                 
                return result;
            }

            return result;
        }




        private IOperationResult WriteBackStatusWhenFinish(UserContext ctx, BPMFlowDTO reqPara)
        {
            IOperationResult result = new OperationResult();
            HtmlForm meta = HtmlParser.LoadFormMetaFromCache(reqPara.FormId.Trim(), ctx);
            var data = GetBillData(ctx, meta, reqPara);

            if (data != null && !data["fflowinstanceid"].IsNullOrEmptyOrWhiteSpace())
            {
                if (data.DynamicObjectType.Properties.ContainsKey(meta.BizStatusFldKey))
                {
                    data[meta.BizStatusFldKey] = BillStatus.E.ToString();
                }

                if (data.DynamicObjectType.Properties.ContainsKey("fapprovedate"))
                {
                    data["fapprovedate"] = DateTime.Now;
                }
                if (data.DynamicObjectType.Properties.ContainsKey("fbpmauditdesc"))
                {
                    data["fbpmauditdesc"] = "BPM已审核";
                }
                var dm = ctx.Container.GetService<IDataManager>();
                dm.InitDbContext(ctx, meta.GetDynamicObjectType(ctx));
                dm.Save(data);
                 
                result.IsSuccess = true;
                result.SimpleMessage = "更新单据的流程审批信息成功！";
                return result;
            }

            return result;
        }


        /// <summary>
        /// 提交审核
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="meta"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public IOperationResult Submit(UserContext ctx, HtmlForm meta, DynamicObject data)
        {
            IOperationResult result = new OperationResult();
            if (!data.DynamicObjectType.Properties.ContainsKey(meta.BizStatusFldKey))
            {
                //如果没有状态字段，认为不需要提交审核
                result.IsSuccess = true;
                return result;
            }

            var status = data[meta.BizStatusFldKey]?.ToString();
            if (status.EqualsIgnoreCase(Enum.GetName(typeof(BillStatus), BillStatus.D))
                 || status.EqualsIgnoreCase(Enum.GetName(typeof(BillStatus), BillStatus.E)))
            {
                result.IsSuccess = true;
                return result;
            }

            var option = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
            option.Add("IgnoreCheckPermssion", "1");
            var gate = ctx.Container.GetService<IHttpServiceInvoker>();
            result = gate.InvokeBillOperation(ctx, meta.Id, new List<DynamicObject>() { data }, "Submit", option);

            return result;
        }


        /// <summary>
        /// 反审核
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="meta"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public IOperationResult UnAudit(UserContext ctx, HtmlForm meta, DynamicObject data)
        {
            IOperationResult result = new OperationResult();
            if (!data.DynamicObjectType.Properties.ContainsKey(meta.BizStatusFldKey))
            {
                //如果没有状态字段，认为不需要提交审核
                result.IsSuccess = true;
                return result;
            }

            result.IsSuccess = true;
            var option = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
            option.Add("IgnoreCheckPermssion", "1");
            option.Add("AudiByBPM", "true");
            var gate = ctx.Container.GetService<IHttpServiceInvoker>();

            var status = data[meta.BizStatusFldKey]?.ToString();
            if (status.EqualsIgnoreCase(Enum.GetName(typeof(BillStatus), BillStatus.D)))
            {
                option.Add("checkAgree", "false");
                result = gate.InvokeBillOperation(ctx, meta.Id, new List<DynamicObject>() { data }, "Audit", option);
            }
            else if (status.EqualsIgnoreCase(Enum.GetName(typeof(BillStatus), BillStatus.E)))
            {
                result = gate.InvokeBillOperation(ctx, meta.Id, new List<DynamicObject>() { data }, "UnAudit", option);
            }

            return result;
        }



        /// <summary>
        /// 流程实例节点执行前监听事件：用于BPM流程实例节点执行前的操作回掉事件
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="reqPara"></param>
        /// <returns></returns>
        public virtual IOperationResult OnBPMBeforExeProc(UserContext ctx, BPMFlowDTO reqPara)
        {
            IOperationResult result = new OperationResult();
            var enents = new List<BPMProcessEventInfo>();

            result.IsSuccess = true;
            result.SrvData = Newtonsoft.Json.JsonConvert.SerializeObject(enents);

            return result;
        }



        /// <summary>
        /// BPM流程增删改时的业务处理服务： 用于BPM流程增删改时，通知业务系统做相应的操作
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="reqPara"></param>
        /// <returns></returns>
        public virtual IOperationResult OnBPMFlowChange(UserContext ctx, BPMFlowDTO reqPara)
        {
            IOperationResult result = new OperationResult();
            result.IsSuccess = true;

            return result;
        }
        /// <summary>
        /// 判断流程实例是否存在
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="reqPara"></param>
        /// <returns></returns>
        public abstract IOperationResult IsExistProcess(UserContext ctx, BPMFlowDTO reqPara);
        /// <summary>
        /// 判断当前用户是否可以处理当前流程
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="reqPara"></param>
        /// <returns></returns>
        public abstract IOperationResult CanApprovalProcess(UserContext ctx, BPMFlowDTO reqPara);
        /// <summary>
        /// 创建流程
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="reqPara"></param>
        /// <returns></returns>
        public abstract IOperationResult CreateProcess(UserContext ctx, BPMFlowDTO reqPara);


        /// <summary>
        /// 流程审批
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="reqPara"></param>
        /// <returns></returns>
        public abstract IOperationResult ApproveProcess(UserContext ctx, BPMFlowDTO reqPara);



        public abstract IOperationResult ShowProcess(UserContext ctx, BPMFlowDTO reqPara);


        /// <summary>
        /// 删除流程
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="reqPara"></param>
        /// <returns></returns>
        public abstract IOperationResult DeleteProcess(UserContext ctx, BPMFlowDTO reqPara);
        /// <summary>
        /// 获取bpm审批日志列表
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="reqPara"></param>
        /// <returns></returns>
        public abstract IOperationResult ShowAuditList(UserContext ctx, BPMFlowDTO reqPara);
        /// <summary>
        /// 查看流程图
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="reqPara"></param>
        /// <returns></returns>
        public abstract IOperationResult ShowChartProcess(UserContext ctx, BPMFlowDTO reqPara);


    }






}
