using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using Newtonsoft.Json.Linq;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;

namespace JieNor.Framework.AppService.Validation
{
    /// <summary>
    /// 新增用户时手机号唯一性校验器
    /// </summary>
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    [ServiceMetaAttribute("validationid", HtmlElementType.HtmlValidator_UserPhoneUniqueValidation)]
    public class UserPhoneUniqueValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);
        }

        /// <summary>
        /// 校验逻辑处理单元
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();

            //检查用户编码
            this.CheckUserNumberOrPhone(userCtx, formInfo, dataEntities, result, "fnumber");

            //检查用户手机号
            this.CheckUserNumberOrPhone(userCtx, formInfo, dataEntities, result, "fphone");

            return result;
        }

        /// <summary>
        /// 检查用户编码或手机号
        /// 1、用户编码和手机号必须独立唯一。
        /// 2、用户编码不能等于其他用户的手机号。
        /// 3、用户手机号不能等于其他用户的编码。
        /// </summary>
        private void CheckUserNumberOrPhone(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, ValidationResult result, string fieldKey)
        {
            //如果不是从数据库加载的数据包，说明当前是新增保存，此时需要校验手机号的唯一性（手机号如果等于用户编码也认为已存在）
            var validationDatas = dataEntities?.Where(o => !o.DataEntityState.FromDatabase && !o[fieldKey].IsNullOrEmptyOrWhiteSpace());
            var _fieldValues = validationDatas
                ?.Select(o => Convert.ToString(o[fieldKey]).Trim())
                ?.Where(o => !o.IsNullOrEmptyOrWhiteSpace())
                ?.ToList();
            if (_fieldValues == null || !_fieldValues.Any()) return;

            var field = formInfo.GetField(fieldKey);
            var fieldValues = new List<string>();

            //校验当前批量保存的数据本身是否存在重复
            var groups = validationDatas.GroupBy(o => Convert.ToString(o[fieldKey]));
            foreach (var group in groups)
            {
                if (group.Count() > 1)
                {
                    foreach (var item in group)
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"用户【{item["fname"]}】的{field.Caption} {group.Key} 存在重复，请检查！",
                            DataEntity = item
                        });
                    }
                }
                else
                {
                    fieldValues.Add(group.Key);
                }
            }
            if (!fieldValues.Any()) return;

            var sqlText = @"select fnumber,fphone from t_sec_user where fmainorgid=@fmainorgid";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, userCtx.Company)
            };

            var tempTable = "";
            if (fieldValues.Count() > 50)
            {
                //用临时表关联查询
                tempTable = this.DBService.CreateTempTableWithDataList(this.Context, fieldValues,false);
                sqlText = $@"
                select fnumber,fphone from t_sec_user t 
                inner join {tempTable} tt on tt.fid=t.fnumber or tt.fid=t.fphone 
                where fmainorgid=@fmainorgid";
            }
            else if (fieldValues.Count() == 1)
            {
                sqlText += $" and (fnumber=@value or fphone=@value)";
                sqlParam.Add(new SqlParam("@value", DbType.String, fieldValues[0]));
            }
            else
            {
                var paramNames = new List<string>();
                for (int i = 0; i < fieldValues.Count; i++)
                {
                    paramNames.Add($"@v{i}");
                    sqlParam.Add(new SqlParam($"@v{i}", DbType.String, fieldValues[i]));
                }
                var paramJoin = string.Join(",", paramNames);
                sqlText += $" and (fnumber in({paramJoin}) or fphone in({paramJoin}))";
            }

            var dynObjs = this.DBService.ExecuteDynamicObject(this.Context, sqlText, sqlParam);

            //删除临时表
            this.DBService.DeleteTempTableByName(this.Context, tempTable, true);

            if (dynObjs == null || !dynObjs.Any()) return;

            foreach (var item in validationDatas)
            {
                var fieldValue = Convert.ToString(item[fieldKey]);

                //如果手机号等于某个已存在用户的编码 或者 编码等于某个已存在用户的手机号，都认为是已经被注册。
                //因为
                //同一个用户的手机号不能等于其他用户的编码，
                //同一个用户的编码也不能等于其他用户的手机号，
                //否则登录时会找到两个用户。
                var dynObj = dynObjs.FirstOrDefault(o =>
                    Convert.ToString(o["fnumber"]).Trim().EqualsIgnoreCase(fieldValue) ||
                    Convert.ToString(o["fphone"]).Trim().EqualsIgnoreCase(fieldValue));

                if (dynObj != null)
                {
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"用户【{item["fname"]}】的{field.Caption} {fieldValue} 已被注册！",
                        DataEntity = item
                    });
                }
            }
        }
    }
}