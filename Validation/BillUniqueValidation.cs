using JieNor.Framework.AppService.QueryBuilder;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.Interface.Validation;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics.Eventing.Reader;
using System.Linq;

namespace JieNor.Framework.AppService.Validation
{
    /// <summary>
    /// 表单字段唯一性校验器
    /// </summary>
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    [ServiceMetaAttribute("validationid", HtmlElementType.HtmlValidator_BillUniqueValidation)]
    public partial  class BillUniqueValidation : AbstractBaseValidation, IIgnoreEmptyFieldValueValidation
    {
        /// <summary>
        /// 判断重复行时，是否忽略空行
        /// </summary>
        public bool IgnoreEmptyValue { get; set; }

        private string[] ValidFieldKeys { get; set; }

        /// <summary>
        /// 始终整单时检查，实现逻辑中需要详细判断重复字段在单据结构中的位置
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }

            set
            {
                base.EntityKey = value;
            }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);

            if (validationExpr.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("唯一性校验器执行时，必须要指定参与唯一性判断的字段（例如编码或单据编号）！");
            }

            this.ValidFieldKeys = validationExpr.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Distinct().ToArray();
        }

        /// <summary>
        /// 校验逻辑处理单元
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();

            var validFlds = this.SplitValidFieldKeyInQueryGroup(userCtx, formInfo);

            result = CheckBillFldUnique(formInfo, dataEntities, option, validFlds, operationNo);

            return result;

            #region 废弃代码   20240423 性能优化
            /*  20240423 性能优化：修改算法---
             *  1、如果数据是从数据库加载且唯一性校验字段未有变化的，不需要校验
             *  2、数据行数较少时，直接用sql查询，无需创建临时表，减少建表的数据库开销

             
            var dbService = userCtx.Container.GetService<IDBService>();

            var validFldGroups = this.SplitValidFieldKeyInQueryGroup(userCtx, formInfo);

            ExtendedDataEntitySet dataEntitySet = new ExtendedDataEntitySet();
            dataEntitySet.Parse(userCtx, dataEntities, formInfo);

            foreach (var fldGroup in validFldGroups)
            {
                DataTable dtTemp = new DataTable();
                //主键
                dtTemp.Columns.Add("fid", typeof(string));
                dtTemp.Columns.Add("fguid", typeof(string));
                foreach (var fld in fldGroup.Value)
                {
                    var col = new DataColumn(fld.Id, fld.ElementType.ToDBClrType());
                    if (fld.ElementType == HtmlElementType.HtmlField_CheckBoxField)
                    {
                        col = new DataColumn(fld.Id, HtmlElementType.HtmlField_TextField.ToDBClrType());
                    }
                    col.AllowDBNull = true;
                    //只有字符串列才需要设置 MaxLength ，否则会报错：MaxLength 仅适用于字符串数据类型。您不能将 Column“XXX”的属性 MaxLength 设置为非负数字。
                    if (col.DataType == typeof(string))
                    {
                        col.MaxLength = fld.Length;
                    }
                    dtTemp.Columns.Add(col);
                }
                string allColCaption = GetAllColCaption(fldGroup);
                var currPathDataEntities = dataEntitySet.FindByEntityKey(fldGroup.Key);

                //记录下当前数据包处理的内存行，以便判断内部是否有重复
                Dictionary<DynamicObject, List<object>> dctMemRows = new Dictionary<DynamicObject, List<object>>();
                Dictionary<DynamicObject, List<DynamicObject>> dctDuplicateRowObjs = new Dictionary<DynamicObject, List<DynamicObject>>();
                Dictionary<string, DynamicObject> dctRowObjMap = new Dictionary<string, DynamicObject>(StringComparer.OrdinalIgnoreCase);
                foreach (var obj2 in currPathDataEntities)
                {
                    bool isEmpty = true;
                    foreach (var fld in fldGroup.Value)
                    {
                        if (fld.Id.EqualsIgnoreCase("fmainorgid"))
                        {
                            //企业隔离的空值校验去掉
                            continue;
                        }
                        if (!fld.GetValueEx(obj2.DataEntity).IsNullOrEmptyOrWhiteSpace())
                        {
                            isEmpty = false;
                            break;
                        }
                    }
                    if (this.IgnoreEmptyValue && isEmpty)
                    {
                        continue;
                    }
                    var guid = Guid.NewGuid().ToString();
                    dctRowObjMap[guid] = obj2.DataEntity;

                    List<object> lstRow = new List<object>();
                    //添加主键值
                    lstRow.Add(obj2.DataEntity.GetRoot()?["Id"] ?? "");
                    lstRow.Add(guid);
                    foreach (var fld in fldGroup.Value)
                    {
                        var val = fld.GetValueEx(obj2.DataEntity) ?? "";
                        if (fld.ElementType == HtmlElementType.HtmlField_CheckBoxField)
                        {
                            val = "true".EqualsIgnoreCase(val?.ToString()) ? 1 : 0;
                        }
                        lstRow.Add(val);
                    }

                    //判断当前行与前面收集的行是否有重复
                    DynamicObject existRowObj;
                    if (this.TryGetExistMemRow(dctMemRows, lstRow, out existRowObj))
                    {
                        var lstDuplicateRowObjs = new List<DynamicObject>();
                        if (!dctDuplicateRowObjs.TryGetValue(existRowObj, out lstDuplicateRowObjs))
                        {
                            lstDuplicateRowObjs = new List<DynamicObject>();
                            dctDuplicateRowObjs[existRowObj] = lstDuplicateRowObjs;
                        }
                        lstDuplicateRowObjs.Add(obj2.DataEntity);
                    }
                    else
                    {
                        dctMemRows[obj2.DataEntity] = lstRow;
                    }
                }

                dtTemp.BeginLoadData();
                foreach (var kvpMemRow in dctMemRows)
                {
                    if (dctDuplicateRowObjs.ContainsKey(kvpMemRow.Key))
                    {
                        //组装重复行信息，并且不加入到数据库重复的判断中
                        //获取相关实体的编码及行号信息
                        List<string> lstBillNoAndSeq = new List<string>();
                        lstBillNoAndSeq.Add(this.GetBillNoAndSeq(formInfo, fldGroup.Key, kvpMemRow.Key));

                        foreach (var tmpRow in dctDuplicateRowObjs[kvpMemRow.Key])
                        {
                            lstBillNoAndSeq.Add(this.GetBillNoAndSeq(formInfo, fldGroup.Key, tmpRow));
                        }

                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"【{string.Join(",", lstBillNoAndSeq)}】：{allColCaption} 出现重复！",
                            DataEntity = dataEntities[0],
                            LinkDataEntities = dctDuplicateRowObjs[kvpMemRow.Key]

                        });

                        continue;
                    }

                    dtTemp.LoadDataRow(kvpMemRow.Value.ToArray(), true);
                }
                dtTemp.EndLoadData();

                using (var tran = userCtx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
                {
                    SqlBuilderParameter sqlPara = new SqlBuilderParameter(userCtx, formInfo.Id);
                    sqlPara.IsShowForbidden = true;
                    sqlPara.NoColorSetting = true;
                    sqlPara.SrcPara.Add("ByUniqueValidation", "true");//唯一性校验的情况（比如单据编码唯一性校验），不加这个数据范围过滤

                    sqlPara.SelectedFieldKeys.AddRange(fldGroup.Value.Select(f => f.Id));
                    var queryObj = QueryService.BuilQueryObject(sqlPara);
                    var pkSelFld = QueryService.GetSelectField(userCtx, formInfo.Id, formInfo.BillPKFldName);

                    string strOn = "";
                    int i = 0;
                    foreach (var fld in fldGroup.Value)
                    {
                        if (!fld.Id.EqualsIgnoreCase("fmainorgid"))
                        {
                            string tbName = "";
                            SelectField baseSelFld = GetSelectField(this.Context, formInfo, fld);
                            if (fld is IMultTextField)
                            {
                                queryObj.SqlSelect += $",{baseSelFld.EntityTable.TableNameAs}.{baseSelFld.ReturnFldName}_txt as {baseSelFld.ReturnFldName}_fname ";
                            }
                            else if (fld is HtmlComboField)
                            {
                                tbName = "v" + i.ToString();
                                queryObj.SqlSelect += $",{tbName}.fenumitem as {baseSelFld.ReturnFldName}_fname";
                                queryObj.SqlFrom += Environment.NewLine + $" left join v_bd_enum {tbName} on {tbName}.fid "
                                  + $"={baseSelFld.EntityTable.TableNameAs}.{baseSelFld.ReturnFldName} ";
                            }
                            else if (fld is HtmlBaseDataField)
                            {
                                var baseFld = fld as HtmlBaseDataField;
                                tbName = "b" + i.ToString();
                                var metaSrv = this.Context.Container?.TryGetService<IMetaModelService>();
                                var baseMeta = metaSrv.LoadFormModel(this.Context, baseFld.RefFormId);
                                var selectKey = baseMeta.NameFldKey;
                                if (baseMeta.ElementType == 1)//引用的是单据
                                {
                                    selectKey = baseMeta.NumberFldKey;
                                }

                                queryObj.SqlSelect += $",{tbName}.{selectKey} as {baseSelFld.ReturnFldName}_fname";
                                queryObj.SqlFrom += Environment.NewLine + $" left join {baseMeta.BillHeadTableName} {tbName} "
                                    + $"on {tbName}.{baseMeta.BillPKFldName}"
                                    + $"={baseSelFld.EntityTable.TableNameAs}.{baseSelFld.ReturnFldName}  ";

                            }
                            // 辅助属性
                            else if (fld.ElementType == HtmlElementType.HtmlField_AuxPropertyField)
                            {
                                //tbName = "a" + i.ToString();
                                queryObj.SqlSelect += $",ISNULL((select fname from T_BD_AUXPROPVALUE aux where aux.fid={baseSelFld.EntityTable.TableNameAs}.{fld.FieldName}),'') as {fld.FieldName}_fname";
                                //queryObj.SqlSelect += $",{tbName}.fname as {fld.FieldName}_fname";
                                //queryObj.SqlFrom += $" left join T_BD_AUXPROPVALUE {tbName} "
                                //                    + $"on {tbName}.fid"
                                //                    + $"={baseSelFld.EntityTable.TableNameAs}.{fld.FieldName} ";
                            }
                        }

                        SelectField selFld = GetSelectField(userCtx, formInfo, fld);

                        if (strOn.IsNullOrEmptyOrWhiteSpace())
                        {
                            strOn = $" tmp.{fld.Id }={selFld.EntityTable.TableNameAs }.{selFld.FieldName} ";
                        }
                        else
                        {
                            strOn += $" and tmp.{fld.Id}={selFld.EntityTable.TableNameAs}.{selFld.FieldName} ";
                        }

                        i++;
                    }

                    // 设置列长度
                    var columnSize = new Dictionary<string, int>();
                    foreach (DataColumn column in dtTemp.Columns)
                    {
                        columnSize[column.ColumnName] = column.MaxLength;
                    }

                    var tempTableName = dbService.CreateTempTableWithDataTable(userCtx, dtTemp, columnSize: columnSize, isPhysics: false);
                    queryObj.SqlFrom += Environment.NewLine + $" inner join {tempTableName} tmp on  {strOn} ";

                    queryObj.SqlSelect += $",tmp.fid,tmp.fguid";

                    if (!queryObj.SqlWhere.IsNullOrEmptyOrWhiteSpace())
                    {
                        queryObj.SqlWhere = $" where not exists (select 1 from {tempTableName } xxx where xxx.fid={pkSelFld.EntityTable.TableNameAs }.{pkSelFld.FieldName } ) ";
                    }
                    else
                    {
                        queryObj.SqlWhere += $" and not exists (select 1 from {tempTableName} xxx where xxx.fid={pkSelFld.EntityTable.TableNameAs }.{pkSelFld.FieldName} ) ";
                    }

                    queryObj.SqlOrderBy = "";//不需要排序

                    using (var reader = dbService.ExecuteReader(userCtx, queryObj.SqlNoPage, sqlPara.DynamicParams))
                    {
                        while (reader.Read())
                        {
                            var guid = reader["fguid"] as string;
                            DynamicObject existRowObj = dctRowObjMap[guid];
                            var billNoAndSeq = this.GetBillNoAndSeq(formInfo, fldGroup.Key, existRowObj);
                            var msg = GetMessage(userCtx, formInfo, fldGroup, reader, billNoAndSeq);
                            result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = msg,
                                DataEntity = existRowObj,
                            });

                        }
                    }

                    DBService.DeleteTempTableByName(Context, tempTableName, false);

                    // 是否返回单据唯一校验的详细错误
                    var isReturnBillUniqueValidationDetailErrorMessage = false;
                    option?.TryGetVariableValue<bool>("IsReturnBillUniqueValidationDetailErrorMessage", out isReturnBillUniqueValidationDetailErrorMessage);

                    var numberField = formInfo.GetNumberField();
                    //如果是单据编码字段唯一性检查并且没有通过的，则丢出特定异常进行修复
                    var isValidBillNo = (this.ValidFieldKeys.Length == 1 && this.ValidFieldKeys.Contains(numberField?.Id ?? "#", StringComparer.OrdinalIgnoreCase))
                        || (this.ValidFieldKeys.Length == 2
                        && this.ValidFieldKeys.Contains(numberField?.Id ?? "#", StringComparer.OrdinalIgnoreCase)
                        && this.ValidFieldKeys.Contains("fmainorgid", StringComparer.OrdinalIgnoreCase));
                    if (result.Errors.Any() && isValidBillNo)
                    {
                        var errorMsg = $"{numberField?.Caption ?? "编码"}出现重复，请尝试重新保存！";
                        if (isReturnBillUniqueValidationDetailErrorMessage)
                        {
                            errorMsg = string.Join("\r\n", result.Errors.Select(s => s.ErrorMessage));
                        }

                        var logEx = userCtx.Container.GetService<Interface.Log.ILogServiceEx>();
                        logEx.Error($"{formInfo.Id}-{operationNo} {errorMsg}");

                        throw new BillNoDuplicateException(errorMsg);
                    }
                    tran.Complete();
                }
            }


            return result;

        */
            #endregion 

        }



        private static SelectField GetSelectField(UserContext userCtx, HtmlForm formInfo, HtmlField fld)
        {
            var selFld = QueryService.GetSelectField(userCtx, formInfo.Id, fld.Id);
            //辅助属性在唯一性校验时异常问题解决方案
            if (selFld.IsNullOrEmptyOrWhiteSpace() && fld is HtmlAuxPropertyField)
            {
                SelectField refFld = new SelectField();
                refFld.Id = fld.Id;
                refFld.Caption = fld.Caption;
                refFld.RefFldKey = "fname";
                selFld = QueryService.GetSelectField(userCtx, formInfo.Id, refFld.RefFldFullKey);
            }

            return selFld;
        }

        //private string GetMessage(UserContext userCtx, HtmlForm formInfo, KeyValuePair<string, IEnumerable<HtmlField>> fldGroup, IDataReader reader, string billNoAndSeq)
        //{
        //    var msg = new List<string>();
        //    foreach (var fld in fldGroup.Value)
        //    {
        //        if (fld.Id.EqualsIgnoreCase("fmainorgid"))
        //        {
        //            continue;
        //        }

        //        SelectField selFld = GetSelectField(userCtx, formInfo, fld);

        //        if (fld is IMultTextField || fld is HtmlComboField || fld is HtmlBaseDataField)//显示引用字段名称
        //        {
        //            msg.Add("{0} 为 '{1}'".Fmt(fld.Caption, reader[selFld.ReturnFldName + "_fname"]));
        //            continue;
        //        }

        //        // 辅助属性名称
        //        if (fld.ElementType == HtmlElementType.HtmlField_AuxPropertyField)
        //        {
        //            msg.Add("{0} 为 '{1}'".Fmt(fld.Caption, reader[fld.FieldName + "_fname"]));
        //            continue;
        //        }

        //        msg.Add("{0} 为 '{1}'".Fmt(fld.Caption, reader[selFld.ReturnFldName]));
        //    }
        //    var tips = "数据重复：已经存在【{0}】的 {1} ！".Fmt(string.Join(" , ", msg), formInfo.Caption);
        //    return tips;
        //}

        //private static string GetAllColCaption(KeyValuePair<string, IEnumerable<HtmlField>> fldGroup)
        //{
        //    //业务主体（企业主体）这个字段不显示
        //    var flds = fldGroup.Value.Where(f => !f.Id.EqualsIgnoreCase("fmainorgid"));
        //    return string.Join(" + ", flds.Select(f => f.Caption));
        //}

        //private bool TryGetExistMemRow(Dictionary<DynamicObject, List<object>> dctMemRows, List<object> lstRowObjs, out DynamicObject existRowObj)
        //{
        //    existRowObj = null;
        //    if (dctMemRows.Count == 0) return false;
        //    if (lstRowObjs == null || lstRowObjs.Count == 0) return false;
        //    foreach (var kvpItem in dctMemRows)
        //    {
        //        bool bEqual = true;
        //        //比较时前面2列数据忽略（主键+标识键）
        //        for (int i = 2; i < kvpItem.Value.Count; i++)
        //        {
        //            if (!object.Equals(kvpItem.Value[i], lstRowObjs[i]))
        //            {
        //                bEqual = false;
        //                break;
        //            }
        //        }

        //        if (bEqual)
        //        {
        //            existRowObj = kvpItem.Key;
        //            return true;
        //        }
        //    }

        //    return false;
        //}




        //private Dictionary<string, IEnumerable<HtmlField>> SplitValidFieldKeyInQueryGroup(UserContext userCtx, HtmlForm hForm)
        //{
        //    var lstValidFlds = new List<HtmlField>();
        //    foreach (var fldKey in this.ValidFieldKeys)
        //    {
        //        var fld = hForm.GetField(fldKey);
        //        if (fld == null)
        //        {
        //            throw new ArgumentException($"指定参与唯一性校验的字段标识不存在：{fldKey}");
        //        }
        //        lstValidFlds.Add(fld);
        //    }

        //    var fldGroups = lstValidFlds.GroupBy(f => f.Entity.QueryPathId);
        //    var headFlds = fldGroups.Where(o => o.Key.EqualsIgnoreCase("*"));

        //    Dictionary<string, IEnumerable<HtmlField>> dctValidGroups = new Dictionary<string, IEnumerable<HtmlField>>(StringComparer.OrdinalIgnoreCase);
        //    foreach (var group in fldGroups)
        //    {
        //        if (group.Key.EqualsIgnoreCase("*")) continue;

        //        var fldGroup = group.ToList();
        //        fldGroup.AddRange(headFlds.SelectMany(o => o));
        //        dctValidGroups.Add(group.Key, fldGroup.Distinct());
        //    }

        //    if (dctValidGroups.Count == 0)
        //    {
        //        dctValidGroups.Add("fbillhead", headFlds.SelectMany(o => o));
        //    }

        //    return dctValidGroups;
        //}
 
    
    
    
    }
}
