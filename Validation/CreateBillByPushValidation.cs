using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.Validation
{
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    [ServiceMetaAttribute("validationid", HtmlElementType.HtmlValidator_CreateBillByPush)]
    public class CreateBillByPushValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 校验实体
        /// </summary>
        public override string EntityKey { get { return "fbillhead"; }  set { base.EntityKey = value; } }

        /// <summary>
        /// 校验当前数据是否是关联生成
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();

            var billTypeField = formInfo.GetBillTypeField();
            if (billTypeField == null) return result;

            //没有规则字段不进行校验
            var bizRuleIdField = formInfo.GetField("fbizruleid");
            if (bizRuleIdField == null) return result;

            if(!(billTypeField.Entity is HtmlHeadEntity))
            {
                throw new BusinessException($"单据类型字段{billTypeField.Id}必须定义在单据头！");
            }

            var numberFld = formInfo.GetNumberField();

            var billTypeService = userCtx.Container.GetService<IBillTypeService>();

            foreach (var entity in dataEntities)
            {
                var billTypeId = billTypeField.DynamicProperty.GetValue<string>(entity);
                if (billTypeId.IsEmptyPrimaryKey()) continue;
                var billTypeObj = billTypeService.GetBillTypeInfor(userCtx, billTypeId);
                if (billTypeObj == null) continue;
                 
                if (billTypeObj.fcreatebypush)
                {
                    var billno = numberFld?.DynamicProperty?.GetValue<string>(entity.GetRoot()) ?? "N/A";
                    var bizRuleId = bizRuleIdField.DynamicProperty.GetValue<string>(entity);
                    if (bizRuleId.IsNullOrEmptyOrWhiteSpace())
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"{this.OperationName}失败：【{billTypeObj.fname}】的{numberFld?.Caption}【{billno}】已配置为必须关联生成，不允许手工新增！",
                            DataEntity = entity
                        });
                        continue;
                    }

                    var convertRule = this.MetaModelService.LoadConvertRule(userCtx, bizRuleId);
                    if (convertRule == null) continue;

                    var sourceFormMeta = this.MetaModelService.LoadFormModel(userCtx, convertRule.SourceFormId);
                    if (sourceFormMeta == null) continue;

                    var sourceCtlField = sourceFormMeta.GetField(convertRule.SourceControlFieldKey);
                    if (sourceCtlField == null) continue;
                    var currLinkMap = convertRule.FieldMappings.FirstOrDefault(o => o.SrcFieldId.EqualsIgnoreCase($"{sourceCtlField.EntityKey}.id"));
                    if (currLinkMap == null) continue;

                    var targetLinkField = formInfo.GetField(currLinkMap.Id);
                    if (targetLinkField == null) continue;

                    var ds = new ExtendedDataEntitySet();
                    ds.Parse(userCtx, new DynamicObject[] { entity }, formInfo);
                    var targetLinkObjs = ds.FindByEntityKey(targetLinkField.EntityKey);

                    //再判断关联生成的控制范围
                    switch (billTypeObj.fcreatebypushmode)
                    {
                        case "0":       //部分分录关联即可
                            if(! targetLinkObjs.Any(o => !targetLinkField.DynamicProperty.GetValue<string>(o.DataEntity).IsNullOrEmptyOrWhiteSpace()))
                            {
                                result.Errors.Add(new ValidationResultEntry()
                                {
                                    ErrorMessage = $"{this.OperationName}失败：【{billTypeObj.fname}】的{numberFld?.Caption}【{billno}】已配置为部分分录必须关联生成，不允许所有分录都是手工追加的！",
                                    DataEntity = entity
                                });
                            }
                            break;
                        case "1":       //全部分录关联
                            if(targetLinkObjs.Any(o => targetLinkField.DynamicProperty.GetValue<string>(o.DataEntity).IsNullOrEmptyOrWhiteSpace()))
                            {
                                result.Errors.Add(new ValidationResultEntry()
                                {
                                    ErrorMessage = $"{this.OperationName}失败：【{billTypeObj.fname}】的{numberFld?.Caption}【{billno}】已配置为所有分录必须关联生成，不允许存在手工追加的分录行！",
                                    DataEntity = entity
                                });
                            }
                            break;  
                    }
                    
                }
            }

            return result;
        }
    }
}
