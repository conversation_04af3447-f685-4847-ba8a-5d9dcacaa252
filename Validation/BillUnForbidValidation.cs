
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.Framework.AppService.Validation
{
    /// <summary>
    /// 表单反禁用操作校验器
    /// 1.如果禁用状态是“未禁用”则不允许反禁用操作
    /// </summary>
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    [ServiceMetaAttribute("validationid", HtmlElementType.HtmlValidator_BillUnForbidValidation)]
    public class BillUnForbidValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }

        /// <summary>
        /// 校验逻辑处理单元
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();

            var forbidFld = formInfo.GetField(formInfo.ForbidStatusFldKey);
            var numberFld = formInfo.GetNumberField();

            foreach (var entity in dataEntities)
            {
                bool bPassed = true;
                bPassed = bPassed &&
                    (forbidFld == null || forbidFld != null && forbidFld.DynamicProperty.GetValue<bool>(entity));

                if (!bPassed)
                {
                    var billno = numberFld?.DynamicProperty?.GetValue<string>(entity.GetRoot()) ?? "N/A";
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"{numberFld?.Caption}为【{billno}】的数据未禁用，不允许反禁用！",
                        DataEntity = entity
                    });
                }
            }

            return result;
        }
    }
}