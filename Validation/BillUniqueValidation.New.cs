using JieNor.Framework.AppService.QueryBuilder;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.Interface.Validation;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics.Eventing.Reader;
using System.Linq;

namespace JieNor.Framework.AppService.Validation
{


    /// <summary>
    /// 单据唯一性校验
    /// </summary>
    public partial class BillUniqueValidation
    {


        /// <summary>
        /// 性能优化：唯一性校验包含的字段，都是表头字段，直接查询，减少临时表的创建
        /// </summary> 
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="validFlds"></param>
        /// <returns></returns>
        private ValidationResult CheckBillFldUnique(HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option,
                                                        Tuple<string, List<HtmlField>> validFlds, string operationNo)
        {
            //如果数据来自数据库且唯一性字段的值均未有改变，则不需要检查
            if (!HaveChange(dataEntities, validFlds.Item2))
            {
                return new ValidationResult();
            }

            ValidationResult result = new ValidationResult();
            string allColCaption = GetAllColCaption(validFlds);

            var rowValues = GetRowValueInfos(formInfo, dataEntities, validFlds);
            var repeatDatas = GetRepeatDataInfos(formInfo, validFlds, rowValues);
            if (repeatDatas.Count > 0)
            {
                //当前的单据集合中存在重复
                GetRepeatMsgs(formInfo, validFlds, result, allColCaption, repeatDatas);
            }

            //检查当前单据跟数据库中其他单据是否重复
            foreach (var item in repeatDatas)
            {
                rowValues.Remove(item.Key);//前面已经提示重复的单据，不需要再做校验
            } 
            if(rowValues.Count >0)  
            {
                //数据行数较少，直接sql查询，不建临时表，减轻数据库压力
                if (rowValues.Count < 10)
                {
                    CheckRepeatBySql(formInfo, validFlds, result, rowValues);
                }
                else
                {
                    CheckRepeatByTemp(formInfo, validFlds, result, rowValues);
                }
            }

            // 是否返回单据唯一校验的详细错误
            var isReturnBillUniqueValidationDetailErrorMessage = false;
            option?.TryGetVariableValue<bool>("IsReturnBillUniqueValidationDetailErrorMessage", out isReturnBillUniqueValidationDetailErrorMessage);

            var numberField = formInfo.GetNumberField();
            //如果是单据编码字段唯一性检查并且没有通过的，则丢出特定异常进行修复
            var isValidBillNo = (this.ValidFieldKeys.Length == 1 && this.ValidFieldKeys.Contains(numberField?.Id ?? "#", StringComparer.OrdinalIgnoreCase))
                || (this.ValidFieldKeys.Length == 2
                && this.ValidFieldKeys.Contains(numberField?.Id ?? "#", StringComparer.OrdinalIgnoreCase)
                && this.ValidFieldKeys.Contains("fmainorgid", StringComparer.OrdinalIgnoreCase));
            if (result.Errors.Any() && isValidBillNo)
            {
                var errorMsg = $"{numberField?.Caption ?? "编码"}出现重复，请尝试重新保存！";
                if (isReturnBillUniqueValidationDetailErrorMessage)
                {
                    errorMsg = string.Join("\r\n", result.Errors.Select(s => s.ErrorMessage));
                }

                var logEx = this.Context.Container.GetService<Interface.Log.ILogServiceEx>();
                logEx.Error($"{formInfo.Id}-{operationNo} {errorMsg}");

                throw new BillNoDuplicateException(errorMsg);
            }

            return result;
        }


        private void CheckRepeatByTemp(HtmlForm formInfo, Tuple<string, List<HtmlField>> validFlds, ValidationResult result, List<RowValueInfo> rowValues)
        {
            DynamicObjectCollection existDatas = null;
            var strOn = BuildSqlOn(formInfo, validFlds);
            SqlBuilderParameter sqlPara = BuildSqlParam(formInfo, validFlds);
            QueryObject queryObj = BuildQueryObject(formInfo, validFlds, sqlPara);
            using (var tran = Context.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                var tempTableName = CreateTempTable(formInfo, validFlds, rowValues);
                queryObj.SqlSelect += $",tmp.fid as fcurrid ";
                queryObj.SqlFrom += Environment.NewLine + $" inner join {tempTableName} tmp on {strOn} ";
                var dbService = Context.Container.GetService<IDBService>();
                existDatas = dbService.ExecuteDynamicObject(this.Context, queryObj.SqlNoPage, sqlPara.DynamicParams);
                dbService.DeleteTempTableByName(Context, tempTableName, false);

                tran.Complete();
            }

            if (existDatas != null && existDatas.Count > 0)
            {
                var currentIds = rowValues.Select(f => f.PKId).ToList();
                var pkSelFld = QueryService.GetSelectField(Context, formInfo.Id, formInfo.BillPKFldName).ReturnFldName;
                var tipsBills = existDatas.Where(f => !currentIds.Contains(f[pkSelFld].ToString())).ToList();
                foreach (var existBill in tipsBills)
                {
                    var pkId = existBill["fcurrid"].ToString();
                    var currObj = rowValues.FirstOrDefault(f => f.PKId.EqualsIgnoreCase(pkId));
                    var msg = GetMessage(this.Context, formInfo, validFlds, existBill);
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = msg,
                        DataEntity = currObj?.RowObj.GetRoot(),
                    });
                }
            }
        }


        private string CreateTempTable(HtmlForm formInfo, Tuple<string, List<HtmlField>> validFlds, List<RowValueInfo> rowValues)
        {
            DataTable dtTemp = new DataTable();
            //主键
            dtTemp.Columns.Add("fid", typeof(string));
            dtTemp.Columns[0].MaxLength = 100;
            foreach (var fld in validFlds.Item2)
            {
                var col = new DataColumn(fld.Id, fld.ElementType.ToDBClrType());
                if (fld.ElementType == HtmlElementType.HtmlField_CheckBoxField)
                {
                    col = new DataColumn(fld.Id, HtmlElementType.HtmlField_TextField.ToDBClrType());
                }
                col.AllowDBNull = true;
                //只有字符串列才需要设置 MaxLength ，否则会报错：MaxLength 仅适用于字符串数据类型。您不能将 Column“XXX”的属性 MaxLength 设置为非负数字。
                if (col.DataType == typeof(string))
                {
                    col.MaxLength = fld.Length;
                }
                dtTemp.Columns.Add(col);
            }

            dtTemp.BeginLoadData();
            foreach (var rowValue in rowValues)
            {
                var rowDatas = new List<object>() { rowValue.PKId };
                rowDatas.AddRange(rowValue.FldValues);
                dtTemp.LoadDataRow(rowDatas.ToArray(), true);
            }
            dtTemp.EndLoadData();

            // 设置列长度
            var columnSize = new Dictionary<string, int>();
            foreach (DataColumn column in dtTemp.Columns)
            {
                columnSize[column.ColumnName] = column.MaxLength;
            }

            var dbService = Context.Container.GetService<IDBService>();
            var tempTableName = dbService.CreateTempTableWithDataTable(this.Context, dtTemp, columnSize: columnSize, isPhysics: false);

            return tempTableName;
        }



        private void CheckRepeatBySql(HtmlForm formInfo, Tuple<string, List<HtmlField>> validFlds, ValidationResult result, List<RowValueInfo> rowValues)
        {
            SqlBuilderParameter sqlPara = BuildSqlParam(formInfo, validFlds);
            QueryObject queryObj = BuildQueryObject(formInfo, validFlds, sqlPara);
            var strWheres = BuildSqlWhere(formInfo, validFlds, rowValues);
            var sql = GetSql(queryObj, strWheres);

            var dbService = Context.Container.GetService<IDBService>();
            var existDatas = dbService.ExecuteDynamicObject(this.Context, sql, sqlPara.DynamicParams);
            if (existDatas != null && existDatas.Count > 0)
            {
                var currentIds = rowValues.Select(f => f.PKId).ToList();
                var pkSelFld = QueryService.GetSelectField(Context, formInfo.Id, formInfo.BillPKFldName).ReturnFldName;
                var tipsBills = existDatas.Where(f => !currentIds.Contains(f[pkSelFld].ToString())).ToList();
                foreach (var existBill in tipsBills)
                {
                    var pkId = existBill["fcurrid"].ToString();
                    var currObj = rowValues.FirstOrDefault(f => f.PKId.EqualsIgnoreCase(pkId));
                    var msg = GetMessage(this.Context, formInfo, validFlds, existBill);
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = msg,
                        DataEntity = currObj?.RowObj.GetRoot(),
                    });
                }
            }
        }

        private List<Tuple<string, string>> BuildSqlWhere(HtmlForm formInfo, Tuple<string, List<HtmlField>> validFlds, List<RowValueInfo> rowValues)
        {
            List<Tuple<string, string>> strWheres = new List<Tuple<string, string>>();
            foreach (var item in rowValues)
            {
                string strWhere = "";
                for (int i = 0; i < validFlds.Item2.Count; i++)
                {
                    var flexKey = validFlds.Item2[i].Id;
                    var fldValue = item.FldValues[i];
                    if (validFlds.Item2[i].ElementType == HtmlElementType.HtmlField_CheckBoxField)
                    {
                        fldValue = "true".EqualsIgnoreCase(fldValue?.ToString()) ? 1 : 0;
                    }

                    if (!validFlds.Item2[i].ElementType.IsNumericFld())
                    {
                        fldValue = "'{0}'".Fmt(fldValue);
                    }
                    SelectField selFld = GetSelectField(this.Context, formInfo, validFlds.Item2[i]);
                    if (strWhere.IsNullOrEmptyOrWhiteSpace())
                    {
                        strWhere = $" {selFld.EntityTable.TableNameAs }.{selFld.FieldName} = {fldValue} ";
                    }
                    else
                    {
                        strWhere += $" and {selFld.EntityTable.TableNameAs}.{selFld.FieldName} = {fldValue} ";
                    }
                }
                strWhere ="({0})".Fmt (strWhere);
                strWheres.Add(Tuple.Create(" '{0}' as fcurrid ".Fmt(item.PKId), strWhere));
            }

            return strWheres;
        }


        /// <summary>
        /// 获取当前的单据集合中的重复项信息
        /// </summary>
        /// <param name="formInfo"></param>
        /// <param name="validFlds"></param>
        /// <param name="result"></param>
        /// <param name="allColCaption"></param>
        /// <param name="repeatInfos"></param>
        private void GetRepeatMsgs(HtmlForm formInfo, Tuple<string, List<HtmlField>> validFlds, ValidationResult result, string allColCaption,
                                    List<KeyValuePair<RowValueInfo, List<RowValueInfo>>> repeatInfos)
        {
            List<string> lstBillNoAndSeq = new List<string>();
            foreach (var repeatInfo in repeatInfos)
            {
                //获取相关实体的编码及行号信息
                foreach (var bill in repeatInfo.Value)
                {
                    lstBillNoAndSeq.Add(this.GetBillNoAndSeq(formInfo, validFlds.Item1, bill.RowObj));
                }

                result.Errors.Add(new ValidationResultEntry()
                {
                    ErrorMessage = $"【{string.Join(",", lstBillNoAndSeq)}】：{allColCaption} 出现重复！",
                    DataEntity = repeatInfo.Key.RowObj.GetRoot(),
                    LinkDataEntities = repeatInfo.Value.Select(f => f.RowObj.GetRoot()).ToList(),
                });
            }
        }

        /// <summary>
        /// 获取当前要校验数据行的重复数据信息
        /// </summary>
        /// <param name="formInfo"></param>
        /// <param name="validFlds"></param>
        /// <param name="rowValues"></param>
        /// <returns></returns>
        private List<KeyValuePair<RowValueInfo, List<RowValueInfo>>> GetRepeatDataInfos(HtmlForm formInfo, Tuple<string, List<HtmlField>> validFlds, List<RowValueInfo> rowValues)
        {
            if (rowValues.Count <= 1)
            {
                return new List<KeyValuePair<RowValueInfo, List<RowValueInfo>>>();
            }

            var result = new Dictionary<RowValueInfo, List<RowValueInfo>>();
            for (int i = 0; i < rowValues.Count - 1; i++)
            {
                var curObj = rowValues[i];
                for (int j = i + 1; j < rowValues.Count; j++)
                {
                    var nextObj = rowValues[j];
                    bool bEqual = true;
                    for (int k = 0; k < curObj.FldValues.Count; k++)
                    {
                        if (!object.Equals(curObj.FldValues[k], nextObj.FldValues[k]))
                        {
                            bEqual = false;
                            break;
                        }
                    }

                    if (bEqual)
                    {
                        if (!result.ContainsKey(curObj))
                        {
                            result[curObj] = new List<RowValueInfo>();
                        }
                        result[curObj].Add(nextObj);
                    }
                }
            }

            var repeat = result.Where(f => f.Value.Count > 0).ToList();

            return repeat;
        }


        /// <summary>
        /// 获取校验字段值
        /// </summary>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="validFlds"></param>
        /// <returns></returns>
        private List<RowValueInfo> GetRowValueInfos(HtmlForm formInfo, DynamicObject[] dataEntities, Tuple<string, List<HtmlField>> validFlds)
        {
            List<RowValueInfo> valueInfos = new List<RowValueInfo>();
            ExtendedDataEntitySet dataEntitySet = new ExtendedDataEntitySet();
            dataEntitySet.Parse(this.Context, dataEntities, formInfo);
            var allDataRows = dataEntitySet.FindByEntityKey(validFlds.Item1);
            foreach (var obj2 in allDataRows)
            {
                bool isEmpty = true;
                foreach (var fld in validFlds.Item2)
                {
                    if (fld.Id.EqualsIgnoreCase("fmainorgid"))
                    {
                        //企业隔离的空值校验去掉
                        continue;
                    }
                    if (!fld.GetValueEx(obj2.DataEntity).IsNullOrEmptyOrWhiteSpace())
                    {
                        isEmpty = false;
                        break;
                    }
                }
                if (this.IgnoreEmptyValue && isEmpty)
                {
                    continue;
                }

                var rowData = new RowValueInfo()
                {
                    PKId = obj2.DataEntity.GetRoot()?["Id"]?.ToString(),
                    RowObj = obj2.DataEntity,
                };
                foreach (var fld in validFlds.Item2)
                {
                    var val = fld.GetValueEx(obj2.DataEntity) ?? "";
                    if (fld.ElementType == HtmlElementType.HtmlField_CheckBoxField)
                    {
                        val = "true".EqualsIgnoreCase(val?.ToString()) ? 1 : 0;
                    }
                    rowData.FldValues.Add(val);
                }

                valueInfos.Add(rowData);
            }

            return valueInfos;
        }




        private string GetMessage(UserContext userCtx, HtmlForm formInfo, Tuple<string, List<HtmlField>> fldGroup, DynamicObject existBill)
        {
            var msg = new List<string>();
            foreach (var fld in fldGroup.Item2)
            {
                if (fld.Id.EqualsIgnoreCase("fmainorgid"))
                {
                    continue;
                }

                SelectField selFld = GetSelectField(userCtx, formInfo, fld);

                if (fld is IMultTextField || fld is HtmlComboField || fld is HtmlBaseDataField)//显示引用字段名称
                {
                    msg.Add("{0} 为 '{1}'".Fmt(fld.Caption, existBill[selFld.ReturnFldName + "_fname"]));
                    continue;
                }

                // 辅助属性名称
                if (fld.ElementType == HtmlElementType.HtmlField_AuxPropertyField)
                {
                    msg.Add("{0} 为 '{1}'".Fmt(fld.Caption, existBill[fld.FieldName + "_fname"]));
                    continue;
                }

                msg.Add("{0} 为 '{1}'".Fmt(fld.Caption, existBill[selFld.ReturnFldName]));
            }

            var tips = "数据重复：已经存在【{0}】的编号为【{1}】的 {2} ！".Fmt(string.Join(" , ", msg), existBill[formInfo.NumberFldKey], formInfo.Caption);

            return tips;
        }


        /// <summary>
        /// 不分页的sql
        /// </summary>
        public string GetSql(QueryObject queryObj, List<Tuple<string, string>> strWheres)
        {
            List<string> sqls = new List<string>();
            foreach (var item in strWheres)
            {
                string sql = queryObj.SqlSelect + $",{item.Item1}" + Environment.NewLine;
                sql += queryObj.SqlFrom + Environment.NewLine;
                if (queryObj.SqlWhere.IsNullOrEmptyOrWhiteSpace() == false)
                {
                    sql += queryObj.SqlWhere + $" and {item.Item2}" + Environment.NewLine;
                }
                else
                {
                    sql += $" where {item.Item2}" + Environment.NewLine;
                }
                sqls.Add(sql);
            }

            var querySql = string.Join(" union all ", sqls);

            return querySql;
        }


        private QueryObject BuildQueryObject(HtmlForm formInfo, Tuple<string, List<HtmlField>> validFlds, SqlBuilderParameter sqlPara)
        {
            int index = 0;
            var queryObj = QueryService.BuilQueryObject(sqlPara);
            foreach (var fld in validFlds.Item2)
            {
                if (!fld.Id.EqualsIgnoreCase("fmainorgid"))
                {
                    string tbName = "";
                    SelectField baseSelFld = GetSelectField(this.Context, formInfo, fld);
                    if (fld is IMultTextField)
                    {
                        queryObj.SqlSelect += $",{baseSelFld.EntityTable.TableNameAs}.{baseSelFld.ReturnFldName}_txt as {baseSelFld.ReturnFldName}_fname ";
                    }
                    else if (fld is HtmlComboField)
                    {
                        tbName = "v" + index.ToString();
                        queryObj.SqlSelect += $",{tbName}.fenumitem as {baseSelFld.ReturnFldName}_fname";
                        queryObj.SqlFrom += Environment.NewLine + $" left join v_bd_enum {tbName} on {tbName}.fid "
                          + $"={baseSelFld.EntityTable.TableNameAs}.{baseSelFld.ReturnFldName} ";
                    }
                    else if (fld is HtmlBaseDataField)
                    {
                        var baseFld = fld as HtmlBaseDataField;
                        tbName = "b" + index.ToString();
                        var metaSrv = this.Context.Container?.TryGetService<IMetaModelService>();
                        var baseMeta = metaSrv.LoadFormModel(this.Context, baseFld.RefFormId);
                        var selectKey = baseMeta.NameFldKey;
                        if (baseMeta.ElementType == 1)//引用的是单据
                        {
                            selectKey = baseMeta.NumberFldKey;
                        }

                        queryObj.SqlSelect += $",{tbName}.{selectKey} as {baseSelFld.ReturnFldName}_fname";
                        queryObj.SqlFrom += Environment.NewLine + $" left join {baseMeta.BillHeadTableName} {tbName} "
                            + $"on {tbName}.{baseMeta.BillPKFldName}"
                            + $"={baseSelFld.EntityTable.TableNameAs}.{baseSelFld.ReturnFldName}  ";

                    }
                    // 辅助属性
                    else if (fld.ElementType == HtmlElementType.HtmlField_AuxPropertyField)
                    {
                        queryObj.SqlSelect += $",ISNULL((select fname from T_BD_AUXPROPVALUE aux where aux.fid={baseSelFld.EntityTable.TableNameAs}.{fld.FieldName}),'') as {fld.FieldName}_fname";
                    }
                }

                index++;
            }
            queryObj.SqlOrderBy = "";//不需要增加排序，减少开销

            return queryObj;
        }

        private SqlBuilderParameter BuildSqlParam(HtmlForm formInfo, Tuple<string, List<HtmlField>> validFlds)
        {
            var selFldKeys = validFlds.Item2.Select(f => f.Id).ToList();
            selFldKeys.Add(formInfo.BillPKFldName);
            selFldKeys.Add(formInfo.NumberFldKey);
            selFldKeys = selFldKeys.Distinct().ToList();

            SqlBuilderParameter sqlPara = new SqlBuilderParameter(this.Context, formInfo.Id);
            sqlPara.IsShowForbidden = true;
            sqlPara.NoColorSetting = true;
            sqlPara.SelectedFieldKeys.AddRange(selFldKeys);
            sqlPara.SrcPara.Add("ByUniqueValidation", "true");//唯一性校验的情况（比如单据编码唯一性校验），不加这个数据范围过滤

            return sqlPara;
        }


        private string BuildSqlOn(HtmlForm formInfo, Tuple<string, List<HtmlField>> validFlds)
        {
            string strOn = " ";
            for (int i = 0; i < validFlds.Item2.Count; i++)
            {
                var fld = validFlds.Item2[i];
                SelectField selFld = GetSelectField(this.Context, formInfo, fld);
                if (strOn.IsNullOrEmptyOrWhiteSpace())
                {
                    strOn = $" tmp.{fld.Id }={selFld.EntityTable.TableNameAs }.{selFld.FieldName} ";
                }
                else
                {
                    strOn += $" and tmp.{fld.Id}={selFld.EntityTable.TableNameAs}.{selFld.FieldName} ";
                }
            }

            return strOn;
        }

        /// <summary>
        /// 检查数据是否有改变：
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private bool HaveChange(DynamicObject[] dataEntities, List<HtmlField> validFlds)
        {
            if (dataEntities.Any(f => f.DataEntityState.FromDatabase == false))
            {
                //新增的数据，需要做校验
                return true;
            }

            return true;//下面按快照查询是否有变更的方法有错误：字段如果没有启用审计日志，快照信息不会被更新，导致错误

            var haveChange = false;
            foreach (var dataEntity in dataEntities)
            {
                var snapShot = dataEntity.GetDataEntitySnapshot();
                if (snapShot.IsNullOrEmpty())
                {
                    continue;
                }

                //只是表头的快照                 
                foreach (var kvpItem in snapShot)
                {
                    if (kvpItem.Value.Changed && validFlds.Any(f => f.Id.EqualsIgnoreCase(kvpItem.Key)))
                    {
                        haveChange = true;
                        break;
                    }
                }

                if (haveChange)
                {
                    break;
                }
            }

            return haveChange;
        }



        private Tuple<string, List<HtmlField>> SplitValidFieldKeyInQueryGroup(UserContext userCtx, HtmlForm hForm)
        {
            var lstValidFlds = new List<HtmlField>();
            foreach (var fldKey in this.ValidFieldKeys)
            {
                var fld = hForm.GetField(fldKey);
                if (fld == null)
                {
                    throw new ArgumentException($"指定参与唯一性校验的字段标识不存在：{fldKey}");
                }
                lstValidFlds.Add(fld);
            }

            var fldGroups = lstValidFlds.GroupBy(f => f.Entity.QueryPathId);//子表体对应的表体字段的查询路径，跟子表体一致 
            var headFlds = fldGroups.Where(o => o.Key.EqualsIgnoreCase("*")).ToList();//表头字段

            Dictionary<string, List<HtmlField>> dctValidGroups = new Dictionary<string, List<HtmlField>>(StringComparer.OrdinalIgnoreCase);
            foreach (var group in fldGroups)
            {
                if (group.Key.EqualsIgnoreCase("*")) continue;

                var fldGroup = group.ToList();
                fldGroup.AddRange(headFlds.SelectMany(o => o));//表头字段
                dctValidGroups.Add(group.Key, fldGroup.Distinct().ToList());//表体字段
            }

            if (dctValidGroups.Count == 0)
            {
                dctValidGroups.Add("fbillhead", headFlds.SelectMany(o => o).ToList());
            }

            //唯一性校验时，字段的位置要在一条线上不能跨多个表体或多个子表体：字段要么都在表头，要么都在表体，要么都在子表体，要么表头字段+同一个表体字段，
            //要么表头字段+同一个表体字段+子表体字段（该子表体属于前面的表体）
            if (dctValidGroups.Count > 1)
            {
                throw new ArgumentException($"指定参与唯一性校验的字段标识错误：不能跨多个表体进行唯一性校验！");
            }

            return Tuple.Create(dctValidGroups.First().Key, dctValidGroups.First().Value);
        }



        private static string GetAllColCaption(Tuple<string, List<HtmlField>> fldGroup)
        {
            //业务主体（企业主体）这个字段不显示
            var flds = fldGroup.Item2.Where(f => !f.Id.EqualsIgnoreCase("fmainorgid"));
            return string.Join(" + ", flds.Select(f => f.Caption));
        }





        private class RowValueInfo
        {
            public string PKId { get; set; }

            public DynamicObject RowObj { get; set; }


            public List<object> FldValues = new List<object>();
        }


    }




}
