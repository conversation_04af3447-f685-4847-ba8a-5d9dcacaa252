using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.ScheduledTask
{
    /// <summary>
    /// 同步基础资料下载的计划任务
    /// </summary>
    //[InjectService]
    //[TaskSvrId("syncdownloadtask")]
    //[TaskMultiInstance()]
    //[Caption("同步基础资料下载任务")]
    public class SyncDownloadTask : AbstractScheduleWorker
    {
        /// <summary>
        /// 计划任务配置时的参数设置界面
        /// </summary>
        public override string ParamFormId => "syn_downloadtaskparam";

        /// <summary>
        /// 计划同步任务参数
        /// </summary>
        protected Dictionary<string, object> JobParameter { get; set; }
        /// <summary>
        /// 执行数据打包同步分发逻辑
        /// </summary>
        protected override async Task DoExecute()
        {
            this.JobParameter = Convert.ToString(this.TaskObject.Parameter).FromJson<Dictionary<string, object>>() ?? new Dictionary<string, object>();
            var systemProfileService = this.UserContext.Container.GetService<ISystemProfile>();

            var formIdStr = this.JobParameter?.GetValue("billFormId", "") as string;
            if (formIdStr.IsNullOrEmptyOrWhiteSpace())
            {
                formIdStr = systemProfileService.GetSystemParameter<string>(this.UserContext, "syn_downloadtaskparam", "billFormId");
            }

            if (formIdStr.IsNullOrEmptyOrWhiteSpace())
            {
                this.WriteLog("同步参数设置错误：没有配置表单Id应用参数");
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "同步参数设置错误：没有配置表单Id应用参数";
                return;
            }

            //用中英文逗号分号来分割出表单id
            var formIds = formIdStr.Split(new[] { ',', ';', '，', '；' }, StringSplitOptions.RemoveEmptyEntries);

            if (formIds == null || formIds.Length <= 0)
            {
                this.WriteLog("同步参数设置错误：表单Id配置错误");
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "同步参数设置错误：表单Id配置错误";
                return;
            }

            foreach (var formId in formIds)
            {
                HtmlForm form = this.MetaModelService.LoadFormModel(this.UserContext, formId);
                if (form == null)
                {
                    this.WriteLog($"没有找到{formId}表单模型!");
                }
                try
                {
                    await SyncDownloadData(form);
                }
                catch (BusinessException ex)
                {
                    this.WriteLog(ex.Message, form);
                }
                catch (Exception ex)
                {
                    this.WriteLog($"业务对象同步下载出现未知错误：{ex.Message}, stacktrace:{ex.StackTrace}", form);
                }
            }
        }

        private async Task SyncDownloadData(HtmlForm form)
        {
            var chainDataIds = new List<string>();
            while (true)
            {
                var result = await postData(form, "SyncQuery", new Dictionary<string, string> { { "billFormId", form.Id } });
                if (result.SrvData == null)
                {
                    break;
                }
                var jsonResult = result.SrvData is string ? JObject.Parse(result.SrvData.ToString()) : JObject.FromObject(result.SrvData);

                if (jsonResult == null)
                {
                    break;
                }

                var listData = (jsonResult["listData"] as JArray)?.Where(x => chainDataIds.Contains((string)x["chainDataId"]) == false).ToList();

                if (listData == null || listData.Count <= 0)
                {
                    break;
                }

                chainDataIds.AddRange(listData.Select(x => (string)x["chainDataId"]));

                foreach(var data in listData)
                {
                    data["billFormId"] = form.Id;
                }

                result = await postData(form, "SyncDownloadConfirm", new Dictionary<string, string> { { "billDataJson", JsonConvert.SerializeObject(listData) } });

                if (result.IsSuccess)
                {
                    this.WriteLog($"成功同步下载{listData.Count}条{form.Caption}表单数据", form);
                }
            }
        }

        private async Task<IOperationResult> postData(HtmlForm form, string operationNo,Dictionary<string,string> simpleData)
        {
            CommonListDTO listDto = new CommonListDTO()
                .SetFormId(form.Id)
                .SetOperationNo(operationNo)
                .SetTaskId(this.TaskId);

            listDto.SimpleData = simpleData;
                
            var gateway = this.UserContext.Container.GetService<IHttpServiceInvoker>();
            Dictionary<string, object> dctHeader = new Dictionary<string, object>();
            dctHeader["X-AppId"] = this.UserContext.AppId;
            dctHeader["X-CompanyId"] = this.UserContext.Company;

            //交给本地操作行为执行，目的是具体同步的代码与按钮操作代码进行复用
            var objResult = await Task.Run(() => gateway.InvokeLocal<object>(this.UserContext, listDto, Enu_HttpMethod.Post, dctHeader));
            var procResult = (objResult as DynamicDTOResponse)?.OperationResult;
            procResult.ThrowIfHasError(true, $"业务对象({form.Caption})同步下载任务执行失败！");
            return procResult;
        }
    }
}
