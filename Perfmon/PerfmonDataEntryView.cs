using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using ServiceStack;
using ServiceStack.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.Perfmon
{
    /// <summary>
    /// 系统性能监控明细
    /// </summary>
    [DataEntityType(Alias = "t_sys_perfmondata", AutoCreateSchema = false, Description = "", DisplayName = "性能监控表", FormAlias = "sys_perfmondata", IsView = false)]
    [InjectService]
    public class PerfmonDataEntryView : ClrDataEntity
    {
        /// <summary>
        /// 初始化当前数据实例
        /// </summary>
        /// <param name="request"></param>
        /// <param name="dtoReq"></param>
        public void Initialize(IRequest request, DynamicDTOWrapper dtoReq)
        {
            if (request == null)
            {
                throw new ArgumentNullException("request");
            }
            var userTicket = request.SessionAs<UserAuthTicket>();
            if (userTicket != null)
            {
                this.UserName = userTicket.DisplayName ?? userTicket.UserName;
                this.MainOrgId = userTicket.Company;
            }
            this.RequestStartTime = DateTime.Now;
            this.RequestUri = request.AbsoluteUri;

            var container = request.TryResolve<IServiceContainer>().BeginLifetimeScope(Guid.NewGuid().ToString());
            this.Id = container.GetService<ISequenceService>()?.GetSequence<string>();
            if (this.Id.IsNullOrEmptyOrWhiteSpace())
            {
                this.Id = Guid.NewGuid().ToString("n");
            }
            if (dtoReq != null)
            {
                this.OperationName = dtoReq.OperationName;
                this.OperationNo = dtoReq.OperationNo;
                this.FormId = dtoReq.FormId ?? string.Empty;

            }
        }
        /// <summary>
        /// 初始化性能实体对象
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="operationNo"></param>
        /// <param name="operationName"></param>
        public void Initialize(UserContext userCtx, string formId, string operationNo, string operationName)
        {
            if (userCtx == null)
            {
                throw new ArgumentNullException("userCtx");
            }
            this.UserName = userCtx.DisplayName ?? userCtx.UserName;
            this.MainOrgId = userCtx.Company;

            this.RequestStartTime = DateTime.Now;
            this.RequestUri = "服务端内部调用";
            this.Id = userCtx.Container.GetService<ISequenceService>()?.GetSequence<string>();
            if (this.Id.IsNullOrEmptyOrWhiteSpace())
            {
                this.Id = Guid.NewGuid().ToString("n");
            }
            this.OperationName = operationName;
            this.OperationNo = operationNo;
            this.FormId = formId;
        }

        /// <summary>
        /// 主键
        /// </summary>
        [SimpleProperty(true, Alias = "fid", DbType = System.Data.DbType.String)]
        public string Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [SimpleProperty(Alias = "fmainorgid", DbType = System.Data.DbType.String)]
        public string MainOrgId { get; set; }

        /// <summary>
        /// 业务表单
        /// </summary>
        [SimpleProperty(Alias = "fopformid", DbType = System.Data.DbType.String)]
        public string FormId { get; set; }

        /// <summary>
        /// 操作代码
        /// </summary>
        [SimpleProperty(Alias = "fopcode", DbType = System.Data.DbType.String)]
        public string OperationNo { get; set; }

        /// <summary>
        /// 操作名称
        /// </summary>
        [SimpleProperty(Alias = "fopname", DbType = System.Data.DbType.String)]
        public string OperationName { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        [SimpleProperty(Alias = "fusername", DbType = System.Data.DbType.String)]
        public string UserName { get; set; }

        /// <summary>
        /// 请求开始时间
        /// </summary>
        [SimpleProperty(Alias = "frequeststarttime", DbType = System.Data.DbType.DateTime)]
        public DateTime RequestStartTime { get; set; }

        /// <summary>
        /// 请求地址
        /// </summary>
        [SimpleProperty(Alias = "frequesturi", DbType = System.Data.DbType.String, Size = int.MaxValue)]
        public string RequestUri { get; set; }

        /// <summary>
        /// 创建操作上下文时间（包括：初始化操作模型，数据包（initHtmlForm,initDataEntity))
        /// </summary>
        [SimpleProperty(Alias = "fcreateopctxtime", DbType = System.Data.DbType.Int64)]
        public long CreateOpCtxTime { get; set; }

        /// <summary>
        /// 操作初始化平台耗时
        /// </summary>
        [SimpleProperty(Alias = "finitopctxtime", DbType = System.Data.DbType.Int64)]
        public long InitOpCtxTime { get; set; }

        /// <summary>
        /// 操作插件初始化耗时
        /// </summary>
        [SimpleProperty(Alias = "finitopplugctxtime", DbType = System.Data.DbType.Int64)]
        public long InitOpPlugCtxTime { get; set; }

        /// <summary>
        /// 操作执行总时间
        /// </summary>
        [SimpleProperty(Alias = "ftotalopexectime", DbType = System.Data.DbType.Int64)]
        public long TotalOpExecTime { get; set; }

        /// <summary>
        /// 操作执行前平台耗时
        /// </summary>
        [SimpleProperty(Alias = "fbeforeopexectime", DbType = System.Data.DbType.Int64)]
        public long BeforeOpExecTime { get; set; }
        /// <summary>
        /// 操作执行前插件耗时
        /// </summary>
        [SimpleProperty(Alias = "fbeforeopplugexectime", DbType = System.Data.DbType.Int64)]
        public long BeforeOpPlugExecTime { get; set; }

        /// <summary>
        /// 操作执行前插件耗时
        /// </summary>
        [SimpleProperty(Alias = "fbeginopplugexectime", DbType = System.Data.DbType.Int64)]
        public long BeginOpPlugExecTime { get; set; }

        /// <summary>
        /// 操作校验器执行平台耗时
        /// </summary>
        [SimpleProperty(Alias = "fopvalidateexectime", DbType = System.Data.DbType.Int64)]
        public long OpValidateExecTime { get; set; }

        /// <summary>
        /// 操作执行平台耗时
        /// </summary>
        [SimpleProperty(Alias = "fopexectime", DbType = System.Data.DbType.Int64)]
        public long OpExecTime { get; set; }

        /// <summary>
        /// 操作执行平台耗时
        /// </summary>
        [SimpleProperty(Alias = "fopserviceexectime", DbType = System.Data.DbType.Int64)]
        public long OpServiceExecTime { get; set; }

        /// <summary>
        /// 操作执行后插件耗时
        /// </summary>
        [SimpleProperty(Alias = "fendopplugexectime", DbType = System.Data.DbType.Int64)]
        public long EndOpPlugExecTime { get; set; }

        /// <summary>
        /// 操作执行后平台耗时
        /// </summary>
        [SimpleProperty(Alias = "fafteropexectime", DbType = System.Data.DbType.Int64)]
        public long AfterOpExecTime { get; set; }

        /// <summary>
        /// 操作执行后插件耗时
        /// </summary>
        [SimpleProperty(Alias = "fafteropplugexectime", DbType = System.Data.DbType.Int64)]
        public long AfterOpPlugExecTime { get; set; }

        /// <summary>
        /// 请求结束时间
        /// </summary>
        [SimpleProperty(Alias = "frequestcompletetime", DbType = System.Data.DbType.DateTime)]
        public DateTime RequestCompleteTime { get; set; }

        /// <summary>
        /// 是否执行过2次（发生sql异常重试）
        /// </summary>
        [SimpleProperty(Alias = "fisexecutetwice", DbType = System.Data.DbType.AnsiStringFixedLength)]
        public bool IsExecuteTwice { get; set; }

        /// <summary>
        /// 统计行数
        /// </summary>
        [SimpleProperty(Alias = "frows", DbType = System.Data.DbType.String)]
        public string Rows { get; set; }
        /// <summary>
        /// 统计过滤条件
        /// </summary>
        [SimpleProperty(Alias = "ffilters", DbType = System.Data.DbType.String)]
        public string Filters { get; set; }
        
    }
}
