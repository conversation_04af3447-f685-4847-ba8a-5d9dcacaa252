using JieNor.Framework.DataEntity.NetworkCtrl;
using JieNor.Framework.MetaCore.FormOp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 操作冲突检查控制的相关服务
    /// </summary>
    public interface INetworkCtrlService
    {

        #region  开启网控

        /// <summary>
        /// 开启操作冲突监控
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="networkCtrlObj"></param>
        /// <param name="para"></param>
        /// <returns></returns>
        NetworkCtrlResult BeginNetCtrl(OperationContext ctx, NetworkCtrlObject networkCtrlObj, NetWorkRunTimeParam para);

        /// <summary>
        /// 批量开启操作冲突监控（有一条失败就返回）
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="networkCtrlObj"></param>
        /// <param name="billParamList"></param>
        /// <param name="isSingleFail">有一条失败就返回</param>
        /// <returns></returns>
        List<NetworkCtrlResult> BatchBeginNetCtrl(OperationContext ctx, NetworkCtrlObject networkCtrlObj, List<NetWorkRunTimeParam> billParamList, bool isSingleFail = false);
        #endregion

        #region 释放网控

        /// <summary>
        /// 释放操作冲突监控
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="neworkCtrlResult"></param>
        /// <returns></returns>
        bool CommitNetCtrl(OperationContext ctx, NetworkCtrlResult neworkCtrlResult);

        /// <summary>
        /// 批量释放操作冲突监控
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="neworkCtrlResults"></param>
        /// <returns></returns>
        bool BatchCommitNetCtrl(OperationContext ctx, List<NetworkCtrlResult> neworkCtrlResults);

        /// <summary>
        /// 按当前用户，批量释放操作冲突监控（主要用于注销登录时，批量释放当前用户的操作冲突监控）
        /// </summary>
        /// <param name="ctx"></param>
        void CommitNetCtrlByUser(OperationContext ctx);

        #endregion

         
    }


}
 