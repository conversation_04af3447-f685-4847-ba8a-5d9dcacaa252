using JieNor.Framework.DataTransferObject.Message;
using System.Collections.Generic;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 短消息通知服务
    /// </summary>
    public interface IMNSService
    {
        /// <summary>
        /// 发送验证码服务
        /// </summary>
        /// <param name="ctx">数据上下文</param>
        /// <param name="mobilePhone">手机号</param>
        /// <param name="smsTplId">短信模型标识</param>
        /// <returns>出于安全性考虑，不将验证码进行返回</returns>
        bool SendAuthCode(UserContext ctx, string mobilePhone, string smsTplId = "");

        /// <summary>
        /// 验证用户输入的验证码是否有效
        /// </summary>
        /// <param name="ctx">数据上下文</param>
        /// <param name="mobilePhone">手机号</param>
        /// <param name="authCode">用户输入的验证码</param>
        /// <returns>验证通过返回true，否则为false</returns>
        bool IsValidAuthCode(UserContext ctx, string mobilePhone, string authCode);

        /// <summary>
        /// 发送短消息
        /// </summary>
        /// <param name="ctx">数据上下文</param>
        /// <param name="param">发送短信所需的参数</param>
        /// <returns>发送成功返回true，否则返回false</returns>
        bool SendShortMessage(UserContext ctx, SendShortMessageParam param);
    }

    /// <summary>
    /// 扩展接口
    /// </summary>
    public interface IMNSServiceEx
    {
        /// <summary>
        /// 内部申请验证码
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="mobilePhone"></param>
        /// <returns></returns>
        string ApplyAuthCode(UserContext ctx, string mobilePhone);
    }
}
