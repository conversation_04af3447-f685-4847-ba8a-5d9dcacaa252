using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.Framework.AppService.ExcleTempService
{
    /// <summary>
    /// 保存excle模板
    /// </summary>
    [InjectService]
    [FormId("excleimporttemp")]
    [OperationNo("tempsave")]
    public class ExcleTempSave : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var datas = this.GetQueryOrSimpleParam<string>("datas");
            if (datas.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            this.Result.MsgStyle = (int)Enu_MsgStyle.Dialog;
            //保存模板数据对应的表
            var metaModel = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "excleimport");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, metaModel.GetDynamicObjectType(this.Context));

            JObject dataObj = JObject.Parse(datas);
            DynamicObject dy = dm.SelectBy("fworkobject='{0}' and fmainorgid='{1}'".Fmt(dataObj["formid"].ToString(), this.Context.Company)).OfType<DynamicObject>().FirstOrDefault();
            if (dy.IsNullOrEmptyOrWhiteSpace())
            {
                dy = metaModel.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;
                dy["fworkobject"] = dataObj["formid"];
                dy["ffile"] = dataObj["ffile"];
                dy["fbeginrow"] = dataObj["fbeginrow"];
                dy["fbegincol"] = dataObj["fbegincol"];
            }
            else
            {
                dy["ffile"] = dataObj["ffile"];
                dy["fbeginrow"] = dataObj["fbeginrow"];
                dy["fbegincol"] = dataObj["fbegincol"];
            }
            List<DynamicObject> beforeEntitys = new List<DynamicObject>();
            DynamicObjectCollection entitys = dy["fentity"] as DynamicObjectCollection;
            //如果删除了表体数据，则记录并删除
            foreach (DynamicObject entity in entitys)
            {
                bool isRemove = true;
                for (int i = 0; i < dataObj["fentity"].Count(); i++)
                {
                    if (entity["Id"].ToString().EqualsIgnoreCase(dataObj["fentity"][i]["rowid"].ToString()))
                    {
                        isRemove = false;
                        break;
                    }
                }
                if (isRemove)
                {
                    beforeEntitys.Add(entity);
                }
            }
            var seqSrv = this.Container.GetService<IDataEntityPkService>();
            for (int i = 0; i < dataObj["fentity"].Count(); i++)
            {
                DynamicObject entity = (from en in entitys
                                        where (en["Id"].IsNullOrEmptyOrWhiteSpace() ? "" : en["Id"].ToString()).EqualsIgnoreCase(dataObj["fentity"][i]["rowid"].ToString())
                                        select en).FirstOrDefault();
                if (entity.IsNullOrEmptyOrWhiteSpace())
                {
                    entity = entitys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                    seqSrv.AutoSetPrimaryKey(this.Context, entity, entitys.DynamicCollectionItemPropertyType); //自动创建主键   
                    dataObj["fentity"][i]["rowid"] = entity["Id"].IsNullOrEmptyOrWhiteSpace() ? "" : entity["Id"].ToString();
                    entity["fcontent"] = dataObj["fentity"][i].ToJson();
                    entitys.Add(entity);
                }
                else
                {
                    entity["fcontent"] = dataObj["fentity"][i].ToJson();
                }

            }
            foreach (DynamicObject beforeEntity in beforeEntitys)
            {
                entitys.Remove(beforeEntity);
            }
            Dictionary<string, object> mDic = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
            mDic.Add("IgnoreCheckPermssion", "true");
            List<string> msgs = this.Gateway.InvokeBillOperation(this.Context, "excleimport", new DynamicObject[] { dy }, "save", mDic).ComplexMessage.ErrorMessages;
            if (msgs.Count > 0)
            {
                this.Result.SimpleMessage = msgs[0];
            }
            else
            {
                this.Result.SimpleMessage = "保存成功！";
            }
        }
    }
}
