//using JieNor.Framework.Interface;
//using JieNor.Framework.IoC;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;
//using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
//using JieNor.Framework.MetaCore.FormMeta;
//using JieNor.Framework.DataTransferObject;
//using JieNor.Framework.SuperOrm.DataManager;
//using JieNor.Framework.SuperOrm.DataEntity;

//namespace JieNor.Framework.AppService.ExcleTempService
//{
//    /// <summary>
//    /// 根据formid，获取excle模板列名
//    /// </summary>
//    [InjectService]
//    [FormId("excleimporttemp")]
//    [OperationNo("getcombos")]
//    public class ExcleTempGetCombos : AbstractOperationServicePlugIn
//    {
//        /// <summary>
//        /// 获取excle模板列名，并赋值给下拉列表
//        /// </summary>
//        /// <param name="e"></param>
//        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
//        {
//            base.BeforeExecuteOperationTransaction(e);
//            this.Result.MsgStyle = (int)Enu_MsgStyle.Dialog;
//            string fileId = this.GetQueryOrSimpleParam<string>("fileId");
//            string formId = this.GetQueryOrSimpleParam<string>("formId");
//            string beginRow = this.GetQueryOrSimpleParam<string>("beginRow");
//            string beginCol = this.GetQueryOrSimpleParam<string>("beginCol");
//            if (formId.IsNullOrEmptyOrWhiteSpace())
//            {
//                this.Result.SimpleMessage = "业务对象不能为空！";
//                return;
//            }
//            beginRow = beginRow.IsNullOrEmptyOrWhiteSpace() ? "0" : beginRow.ToString();
//            beginCol = beginCol.IsNullOrEmptyOrWhiteSpace() ? "0" : beginCol.ToString();
//            List<object> fieldNames = new List<object>();
//            List<object> cols = new List<object>();
//            string error = "";
//            if (!fileId.IsNullOrEmptyOrWhiteSpace())
//            {
//                var svc = this.Container.GetService<IExcleImportService>();
//                var rStream = svc.ReadStreamFromService(fileId.GetSignedFileUrl(false), ref error);
//                if (rStream.IsNullOrEmptyOrWhiteSpace())
//                {
//                    this.Result.SimpleMessage = "获取文件流失败！" + error;
//                    return;
//                }
//                cols = svc.GetExcleColNames(this.Context, rStream, beginRow, beginCol);
//                if (cols.Count == 0)
//                {
//                    this.Result.SimpleMessage = "获取模板列名失败！";
//                    return;
//                }
//            }
//            //增加序号列，相同序列号则为同一个单据
//            object fno = new
//            {
//                id = "sn",
//                name = "序号"
//            };
//            fieldNames.Add(fno);
//            var formInfo = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, formId);
//            var fieldList = formInfo.GetFieldList().OrderBy(f => f.EntityKey);
//            foreach(var field in fieldList)
//            {
//                if (Include(field))
//                {
//                    continue;
//                }
//                if (field is HtmlAuxPropertyField)
//                {
//                    List<object> names = new List<object>();
//                    var metaSupport = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "bd_auxproperty");
//                    this.Context.UpdateMdlSchema(metaSupport.Id);
//                    var dmSupport = this.Container.GetService<IDataManager>();
//                    dmSupport.InitDbContext(this.Context, metaSupport.GetDynamicObjectType(this.Context));
//                    List<DynamicObject> supports = dmSupport.SelectBy("fmainorgid='{0}'".Fmt(this.Context.Company)).OfType<DynamicObject>().ToList();
//                    if (supports.IsNullOrEmptyOrWhiteSpace() || supports.Count == 0)
//                    {
//                        continue;
//                    }
//                    foreach(DynamicObject support in supports)
//                    {
//                        string fname = support["fname"].IsNullOrEmptyOrWhiteSpace() ? "" : support["fname"].ToString();
//                        if (fname.IsNullOrEmptyOrWhiteSpace())
//                        {
//                            continue;
//                        }
//                        string fid = support["Id"].IsNullOrEmptyOrWhiteSpace() ? "" : support["Id"].ToString();
//                        object name = new
//                        {
//                            id = field.Id + ";" + fid,
//                            name = field.Group + "__" + field.Caption + "__" + fname
//                        };
//                        names.Add(name);
//                    }
//                    fieldNames.AddRange(names);
//                }
//                else
//                {
//                    object fieldName = new
//                    {
//                        id = field.Id,
//                        name = field.Group + "__" + field.Caption
//                    };
//                    fieldNames.Add(fieldName);
//                }
//            }
//            object obj = new
//            {
//                fieldName = fieldNames,
//                colName = cols
//            };
//            this.Result.SrvData = obj;
//        }
//        //判断是否可以从Excle导入
//        private static bool Include(HtmlField fld)
//        {
//            var include = fld.CanImport == 0
//                       || fld.ElementType == HtmlElementType.HtmlField_CreateDateField
//                       || fld.ElementType == HtmlElementType.HtmlField_CreaterField
//                       || fld.ElementType == HtmlElementType.HtmlField_ModifierField
//                       || fld.ElementType == HtmlElementType.HtmlField_ModifyDateField
//                       || fld.ElementType == HtmlElementType.HtmlField_AttchCount
//                       || fld.ElementType == HtmlElementType.HtmlField_BasePropertyField
//                       || fld.ElementType == HtmlElementType.HtmlField_FileField
//                       || fld.ElementType == HtmlElementType.HtmlField_PrintCount
//                       || fld.EntityKey.EqualsIgnoreCase("fchecklogentry");
//            return include;
//        }
//    }
//}
