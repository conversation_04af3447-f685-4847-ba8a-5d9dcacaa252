using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 分布式锁服务
    /// </summary>
    public interface IDistributedLockService
    {
        /// <summary>
        /// 获取锁
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        bool GetDistributedLock(UserContext userCtx, string key, string value, TimeSpan timeOut);

        bool ReleaseDistributedLock(UserContext userCtx, string key, string value);
    }
}
