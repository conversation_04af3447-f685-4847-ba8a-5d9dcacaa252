
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Integration;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.Interface.Integration;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.Integration
{

    /// <summary>
    /// 往第三方系统同步数据基类
    /// </summary>
    public abstract class AbstractSynchroTo3Sys: ISynDataTo3Sys
    {


        public AbstractSynchroTo3Sys()
        {
        }


        #region Var

        private object lockObj = new object();

        /// <summary>
        /// 同步任务标志
        /// </summary>
        protected string groupId = Guid.NewGuid().ToString();
        protected IRedisCache CacheClient
        {
            get;
            set;
        }

        /// <summary>
        /// 同步的数据类型
        /// </summary>
        public abstract  string DataType
        {
            get; 
        }
        /// <summary>
        /// 数据同步描述，如：供应商同步到k3
        /// </summary>
        public abstract string SynDirectDesc
        {
            get;
        }


        /// <summary>
        /// 同步用户令牌
        /// </summary>
        public abstract string TargetToken
        {
            get; 
        }
        
        /// <summary>
        /// webservice Url
        /// </summary>
        public abstract string TargetUrl
        {
            get; 
        }

        /// <summary>
        /// 目标系统名称
        /// </summary>
        public virtual string TargetSystem
        {
            get
            {
                return "i-scrm";
            }
        }

        /// <summary>
        /// 目标系统业务对象标识
        /// </summary>
        public abstract string TargetFormId
        {
            get;
        }

        /// <summary>
        /// 目标系统操作标识
        /// </summary>
        public virtual string TargetOperateId
        {
            get
            {
                return "Save";
            }
        }

        /// <summary>
        /// 目标系统操作名称
        /// </summary>
        public virtual string TargetOperateName
        {
            get
            {
                return "保存";
            }
        }

        /// <summary>
        /// 来源系统业务对象标识
        /// </summary>
        public abstract string SCRMFormId
        {
            get;
        }

        /// <summary>
        /// 来源系统业务对象名称
        /// </summary>
        public abstract string SCRMFormName
        {
            get;
        }

        /// <summary>
        /// 来源系统标识
        /// </summary>
        public virtual string SCRMSystem
        {
            get
            {
                return "i-scrm";
            }
        }
        
        /// <summary>
        /// 来源系统操作标识
        /// </summary>
        public virtual string SCRMOperateId
        {
            get
            {
                return "Query";
            }
        }
        
        /// <summary>
        /// 来源系统操作名称
        /// </summary>
        public virtual string SCRMOperateName
        {
            get
            {
                return "查询";
            }
        }

        /// <summary>
        /// 是否模拟测试
        /// </summary>
        public bool IsTest
        {
            get;
            set;
        }

        /// <summary>
        /// 上下文
        /// </summary>
        public UserContext Context
        {
            get;
            protected   set;
        }

        /// <summary>
        /// 同步单据表单信息
        /// </summary>
        protected HtmlForm SCRMFormMeta
        {
            get
            {
                HtmlForm bizInfor = HtmlParser.LoadFormMetaFromCache(this.SCRMFormId, this.Context);
                return bizInfor;
            }
        }

        /// <summary>
        /// 同步单据的编码字段
        /// </summary>
        protected HtmlField SCRMBillNoField
        {
            get
            {
                var fld = SCRMFormMeta.GetNumberField();
                return fld;
            }
        }



        #endregion Var

        /// <summary>
        /// 总入口：进行同步数据
        /// </summary> 
        public virtual void DoSynchroData(UserContext ctx,ref List<string> errInfo,   IEnumerable<object> ids)
        {
            try
            {
                Context = ctx;
                this.CacheClient = ctx.Container.GetService<IRedisCache>();
                groupId = DateTime.Now.ToString("yyyyMMddHHmmssfff");

                ChechPara(ctx);

                BeforeDoSynchroData(ids);

                var sourceDatas = GetSynchroData(ids);
                if (sourceDatas == null || sourceDatas.Count() == 0)
                {
                    //未找到需要同步的数据 
                    return;
                }

                AfterGetSynchroData(sourceDatas);

                var meta = HtmlParser.LoadFormMetaFromCache(this.SCRMFormId, ctx);
                var numberKey = meta.GetNumberField().PropertyName;

                foreach (var srcData in sourceDatas)
                {
                    if (IsStopSynchro())
                    {
                        break;
                    }

                    SynchroLogInfo log = CreateSynchroLog(srcData);
                    if (IsCancelSynchro(srcData) || JudgeDoing(srcData,log))
                    {
                        continue;
                    }

                    try
                    {
                        AddDoingCtrl(srcData);
                         
                        var targetData = BuildTargetBillData(srcData, log);

                        var synResult = ExecuteSynchro(targetData, log);
                        if(!log.isSuccess)
                        {
                            errInfo.Add("{0}【{1}】同步失败,具体原因请前往同步日志中查看！"
                            .Fmt(this.SCRMFormName, srcData[numberKey]?.ToString()));
                        }

                        AfterDoSynchroData(srcData, synResult, log);
                    }
                    catch (Exception ex)
                    {                        
                        errInfo.Add("{0}【{1}】同步失败,具体原因请前往同步日志中查看！"
                            .Fmt(this.SCRMFormName, srcData[numberKey]?.ToString()));
                        log.AddLogEntry("数据同步过程中出现异常，异常信息：" + ex.Message 
                            + System.Environment.NewLine + ex.StackTrace);
                        SynchroDataHelper.WriteSynchroLog(this.Context,log );
                    }
                    finally
                    {
                        CleareDoing(srcData);
                    }
                }

                FinishDoSynchroData(sourceDatas);

                DeleteStopFlag();
            }

            catch (Exception ex)
            {
                DeleteStopFlag();
                var log = CreateSynchroLog("数据同步过程中出现异常，异常信息：" + ex.Message + System.Environment.NewLine + ex.StackTrace); 
                SynchroDataHelper.WriteSynchroLog(this.Context, log);
            }
        }
        
        /// <summary>
        /// 相关参数校验
        /// </summary>
        /// <param name="ctx"></param>
        public virtual void   ChechPara(UserContext ctx)
        { 
        }
        
        /// <summary>
        /// 是否取消同步（比如检查判断第三方已经存在的，不用同步）
        /// </summary>
        /// <param name="srcData"></param>
        /// <returns></returns>
        public virtual bool IsCancelSynchro(DynamicObject srcData)
        {
            return false;
        }
        
        /// <summary>
        /// 数据同步之前的相关操作：如建相关表，如同步物料时先同步物料分组
        /// </summary> 
        public virtual void BeforeDoSynchroData(IEnumerable<object> ids)
        {
            
        }
        
        /// <summary>
        /// 获取需要同步的数据 
        /// </summary>
        public virtual IEnumerable<DynamicObject> GetSynchroData(IEnumerable<object> ids)
        {
            if (ids == null || ids.Count ()==0)
            {
                var k3Data = SynchroDataHelper.GetSCRMData(this.Context, SCRMFormId);

                return k3Data.ToArray();
            }
            else
            {
                var k3Data = SynchroDataHelper.GetSCRMData(this.Context, SCRMFormId, ids);

                return k3Data.ToArray();
            }
        }
        
        /// <summary>
        /// 获取到需要同步的数据之后的相关操作：如重新组织数据，格式转换、前置数据同步之类的
        /// </summary>
        /// <param name="sourceData"></param>
        public virtual void AfterGetSynchroData(IEnumerable<DynamicObject> sourceData)
        {

        }
        
        /// <summary>
        /// 调用scrm的接口进行同步
        /// </summary>
        /// <param name="targetData"></param> 
        ///  <param name="log">日志信息</param> 
        /// <returns></returns> 
        public abstract  string ExecuteSynchro(JObject targetData, SynchroLogInfo log);
        
        /// <summary>
        /// 构建目标系统的业务对象数据
        /// </summary>
        /// <param name="bizData"></param>
        /// <param name="log"></param>
        /// <returns></returns>
        public virtual JObject BuildTargetBillData(DynamicObject bizData, SynchroLogInfo log)
        {
            throw new NotImplementedException();
        }
        
        /// <summary>
        /// 数据同步操作完成后:记录同步日志记录
        /// </summary>
        /// <param name="sourceData">k3采购订单信息</param>
        /// <param name="synResult">同步返回的信息 </param>
        /// <param name="log">日志信息</param>
        public virtual void AfterDoSynchroData(DynamicObject sourceData, string synResult, SynchroLogInfo log)
        {
            //记录同步日志
            log.EndTime = DateTime.Now;
            SynchroDataHelper.WriteSynchroLog(this.Context, log);
        }

        /// <summary>
        /// 所有数据同步操作完成后：如关闭连接等
        /// </summary> 
        public virtual void FinishDoSynchroData(IEnumerable<DynamicObject> sourceDatas)
        {

        }


        

        #region NetControl

        
        protected string DoingKey(DynamicObject sourceData)
        {
            var key = "integration:{0}.{1}.{2}.{3}".Fmt(this.Context.Company, this.TargetSystem, this.TargetFormId, sourceData["Id"]);
            return key;
        }

        /// <summary>
        /// 判断是否有人正在同步
        /// </summary>
        /// <param name="sourceData"></param>
        /// <returns></returns>
        protected bool JudgeDoing(DynamicObject sourceData,SynchroLogInfo log)
        {
            var key = DoingKey(sourceData);
            var value = this.CacheClient.Get<string>(this.Context, key);
            if (value.IsNullOrEmptyOrWhiteSpace())
            {
                return false;
            }

            DateTime date;
            if (DateTime.TryParse(value, out date))
            {
                if (date.AddHours(1) < DateTime.Now)
                {
                    //同步超过2个小时视为失败
                    CacheClient.Remove(Context, key);
                    return false;
                }
            }

            log.Content = "有其他人正在同步该数据";
            SynchroDataHelper.WriteSynchroLog(this.Context, log);

            return true;              
        }
        
        /// <summary>
        ///添加正在同步标记
        /// </summary>
        /// <param name="srcData"></param>
        protected void AddDoingCtrl(DynamicObject srcData)
        {
            var key = DoingKey(srcData);
            this.CacheClient.Set(this.Context, key, DateTime.Now, TimeSpan.FromHours(10));
        }
        
        /// <summary>
        /// 去掉正在同步的标记
        /// </summary>
        /// <param name="srcData"></param>
        /// <returns></returns>
        protected void CleareDoing(DynamicObject srcData)
        {
            var key = DoingKey(srcData);
            CacheClient.Remove(Context, key);
        }


        #endregion NetControl

        #region WriteSynchroLog

        
         
        /// <summary>
        /// 创建同步日志
        /// </summary>
        /// <param name="srcData"></param> 
        protected SynchroLogInfo CreateSynchroLog(DynamicObject srcData)
        {
            var msg  ="同步的基础资料编码/单据编号：" + SCRMBillNoField == null ? "" : srcData.GetValue(SCRMBillNoField.Id, "");

            return CreateSynchroLog(msg);
        }

        /// <summary>
        /// 创建同步日志
        /// </summary>
        /// <param name="msg"></param> 
        protected SynchroLogInfo CreateSynchroLog(string  msg)
        {
            SynchroLogInfo log = new SynchroLogInfo();

            log.GroupId = groupId;
            log.TargetSystem = TargetSystem;
            log.SourceSystem = SCRMSystem;
            log.BeginTime = DateTime.Now;
            log.SyncType = SynDirectDesc;
            log.DirectDesc = SCRMSystem + "-->" + TargetSystem + "：" + SynDirectDesc;
            log.Content = msg;

            return log;
        }


        #endregion WriteSynchroLog

        #region Stop Synchro


        /// <summary>
        /// 是否终止后续的所有同步（具体作法：外部在数据库插入一条记录标准要终止同步，参照DeleteStopFlag()）
        /// </summary> 
        /// <returns></returns>
        public virtual bool IsStopSynchro()
        {
            return false;
        }
        
        /// <summary>
        /// 删除终止同步标记
        /// </summary>
        /// <returns></returns>
        public virtual void DeleteStopFlag()
        {
           
        }


        #endregion














    }


}
