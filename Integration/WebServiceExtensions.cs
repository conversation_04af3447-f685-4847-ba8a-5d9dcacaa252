
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.DataTransferObject.Integration;
using JieNor.Framework.DataTransferObject.Poco;
using Microsoft.CSharp;
using System;
using System.CodeDom;
using System.CodeDom.Compiler;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Security;
using System.Reflection;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Web.Services.Description;
using System.Xml;

namespace JieNor.Framework.AppService.Integration
{

    /// <summary>
    /// WebService扩展类
    /// </summary>
    public static class WebServiceExtensions
    {
        private static readonly List < WebServiceInfo> webServiceInfo = new  List<WebServiceInfo> ();




        /// <summary>
        /// 默认为5分钟
        /// </summary>
        private static readonly int HttpRequest_Timeout = 5 * 60 * 1000;

        /// <summary>
        /// 默认为10分钟
        /// </summary>
        private static readonly int HttpRequest_RWTimeout = 10 * 60 * 1000;






        static WebServiceExtensions()
        {
            var strConficFile = Path.Combine(PathUtils.GetStartupPath(), "oppein.json");
            if (File.Exists(strConficFile))
            {
                var cfg = File.ReadAllText(strConficFile, Encoding.UTF8);

                webServiceInfo = cfg?.FromJson<List<WebServiceInfo>>();
            }
            if (webServiceInfo == null)
            {
                webServiceInfo = new List<WebServiceInfo>();
            }
        }




        public static WebServiceInfo GetSSOWebServiceInfo(string companyId)
        {
            var key = "sso.user.{0}".Fmt(companyId);
            return webServiceInfo.FirstOrDefault(f => f.WebserviceId.EqualsIgnoreCase(key));
        }


        public static WebServiceInfo GetLLBPMWebServiceInfo(string companyId)
        {
            var key = "llbpm.flow.{0}".Fmt(companyId);
            return webServiceInfo.FirstOrDefault(f => f.WebserviceId.EqualsIgnoreCase(key));
        }


        public static WebServiceInfo GetK3CloudWebServiceInfo(string companyId)
        {
            var key = "k3cloud.edi.{0}".Fmt(companyId);

            var strConficFile = Path.Combine(PathUtils.GetStartupPath(), "oppein.json");
            var cfg = File.ReadAllText(strConficFile, Encoding.UTF8);

            var xx = cfg?.FromJson<List<WebServiceInfo>>();

            return xx.FirstOrDefault(f => f.WebserviceId.EqualsIgnoreCase(key));
        }




        /// <summary>
        /// 通过http通道协议动态调用WebService （.asmx 形式的）
        /// </summary>
        /// <param name="url"></param>
        /// <param name="methodName"></param>
        /// <param name="paras"></param>
        /// <returns></returns>
        public static string InvokeWebService(string url, string methodName, HttpRequestParam paras)
        {
            string fullUrl = Path.Combine(url, methodName);
            //CertificateTrust.SetCertificatePolicy();
            HttpWebRequest request = (HttpWebRequest)HttpWebRequest.Create(fullUrl);
            request.ContentType = "application/x-www-form-urlencoded";
            request.Method = "POST";
            request.Timeout = HttpRequest_Timeout;
            request.ReadWriteTimeout = HttpRequest_RWTimeout;
            //CertificateTrust.SetCertificatePolicy();
            byte[] data = Encoding.UTF8.GetBytes(paras.ToString());//参数
            request.ContentLength = data.Length;
            Stream writer = request.GetRequestStream();
            writer.Write(data, 0, data.Length);
            writer.Close();

            WebResponse response = request.GetResponse();
            StreamReader sr = new StreamReader(response.GetResponseStream(), Encoding.UTF8);
            String retXml = sr.ReadToEnd();
            sr.Close();

            XmlDocument doc = new XmlDocument();
            doc.LoadXml(retXml);

            return doc.InnerText;
        }



        /// <summary>
        /// 动态生成webservice代理类并调用相应接口
        /// </summary>
        /// <param name="url"></param>
        /// <param name="methodname"></param>
        /// <param name="args"></param>
        /// <returns></returns>
        public static object InvokeWebService(string url, string methodname, object[] args)
        {
            return InvokeWebService(url, null, methodname, args);
        }

        /// <summary>
        /// 动态生成webservice代理类并调用相应接口
        /// </summary>
        /// <param name="url"></param>
        /// <param name="classname"></param>
        /// <param name="methodname"></param>
        /// <param name="args"></param>
        /// <returns></returns>
        public static object InvokeWebService(string url, string classname, string methodname, object[] args)
        {
            string @namespace = "JieNor.Framework.DynamicWebServiceCalling";
            if (classname.IsNullOrEmptyOrWhiteSpace ())
            {
                string[] parts = url.Split('/');
                string[] pps = parts[parts.Length - 1].Split('.');
                classname = pps[0];
            }

            try
            {
                //获取WSDL  
                WebClient wc = new WebClient();
                //CertificateTrust.SetCertificatePolicy();
                Stream stream = wc.OpenRead(url + "?WSDL");
                ServiceDescription sd = ServiceDescription.Read(stream);
                ServiceDescriptionImporter sdi = new ServiceDescriptionImporter();
                sdi.AddServiceDescription(sd, "", "");
                CodeNamespace cn = new CodeNamespace(@namespace);

                //生成客户端代理类代码  
                CodeCompileUnit ccu = new CodeCompileUnit();
                ccu.Namespaces.Add(cn);
                sdi.Import(cn, ccu);
                CSharpCodeProvider csc = new CSharpCodeProvider();
                ICodeCompiler icc = csc.CreateCompiler();

                //设定编译参数  
                CompilerParameters cplist = new CompilerParameters();
                cplist.GenerateExecutable = false;
                cplist.GenerateInMemory = true;
                cplist.ReferencedAssemblies.Add("System.dll");
                cplist.ReferencedAssemblies.Add("System.XML.dll");
                cplist.ReferencedAssemblies.Add("System.Web.Services.dll");
                cplist.ReferencedAssemblies.Add("System.Data.dll");

                //编译代理类  
                CompilerResults cr = icc.CompileAssemblyFromDom(cplist, ccu);
                if (true == cr.Errors.HasErrors)
                {
                    System.Text.StringBuilder sb = new System.Text.StringBuilder();
                    foreach (System.CodeDom.Compiler.CompilerError ce in cr.Errors)
                    {
                        sb.Append(ce.ToString());
                        sb.Append(System.Environment.NewLine);
                    }
                    throw new Exception(sb.ToString());
                }

                //生成代理实例，并调用方法  
                System.Reflection.Assembly assembly = cr.CompiledAssembly;
                var all = assembly.GetTypes();               
                object obj = Activator.CreateInstance(all[0]);
                System.Reflection.MethodInfo mi = all[0].GetMethod(methodname);

                return mi.Invoke(obj, args);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.InnerException.Message, new Exception(ex.InnerException.StackTrace));
            }
        }




        



        /// <summary>
        /// HTTP/POST
        /// </summary>
        /// <param name="url">URL</param>
        /// <param name="postData">post数据</param>
        /// <param name="timeout">超时时间(秒)</param>
        /// <returns></returns>
        public static string HttpPost(string url, string postData, int timeout = 60)
        {
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            byte[] byteArray = Encoding.UTF8.GetBytes(postData);
            request.Method = "POST";
            request.Accept = "text/html, application/xhtml+xml, application/json, text/javascript, */*;";
            request.Timeout = timeout * 1000;
            request.ContentType = "application/x-www-form-urlencoded";
            request.ContentLength = byteArray.Length;
            Stream myRequestStream = request.GetRequestStream();
            myRequestStream.Write(byteArray, 0, byteArray.Length);
            myRequestStream.Close();

            HttpWebResponse response = (HttpWebResponse)request.GetResponse();
            Stream myResponseStream = response.GetResponseStream();
            StreamReader myStreamReader = new StreamReader(myResponseStream, Encoding.GetEncoding("utf-8"));
            string retString = myStreamReader.ReadToEnd();
            myStreamReader.Close();
            myResponseStream.Close();
            response.Close();
            return retString;
        }




        /// <summary>
        /// HTTP/GET
        /// </summary>
        /// <param name="url">URL</param>
        /// <returns></returns>
        public static string HttpGet(string url, int timeout = 10)
        {
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = "GET";
            request.Accept = "text/html, application/xhtml+xml, application/json, text/javascript, */*;";
            request.Timeout = timeout * 1000;
            //request.ContentType = "text/xml;charset=UTF-8";

            HttpWebResponse response = (HttpWebResponse)request.GetResponse();
            Stream myResponseStream = response.GetResponseStream();
            StreamReader myStreamReader = new StreamReader(myResponseStream, Encoding.GetEncoding("utf-8"));
            string retString = myStreamReader.ReadToEnd();
            myStreamReader.Close();
            myResponseStream.Close();
            response.Close();
            return retString;
        }




        /// <summary>
        /// 数据字典参数拼接
        /// </summary>
        /// <param name="dict"></param>
        /// <param name="toUTF8"></param>
        /// <returns></returns>
        public static string ConcatParams(Dictionary<string, object> dict, bool toUTF8 = true)
        {
            string str = "";
            foreach (var item in dict)
            {
                string key = "";
                string val = "";
                if (toUTF8)
                {
                    key = Get_UTF8(item.Key);
                    val = Get_UTF8(item.Value);
                }
                else
                {
                    key = Get_UrlEncode(item.Key);
                    val = Get_UrlEncode(item.Value);
                }

                if (string.IsNullOrWhiteSpace(str))
                {
                    str = key + "=" + val;
                }
                else
                {
                    str += "&" + key + "=" + val;
                }
            }
            return str;
        }

        /// <summary>
        /// 字符串转UTF-8
        /// </summary>
        /// <param name="unicodeString"></param>
        /// <returns></returns>
        public static string Get_UTF8(object unicodeString)
        {
            UTF8Encoding utf8 = new UTF8Encoding();
            Byte[] encodedBytes = utf8.GetBytes(Convert.ToString(unicodeString));
            String decodedString = utf8.GetString(encodedBytes);
            return decodedString;
        }

        /// <summary>
        /// 字符串转UrlEncode
        /// </summary>
        /// <param name="unicodeString"></param>
        /// <returns></returns>
        public static string Get_UrlEncode(object unicodeString)
        {
            string str = System.Web.HttpUtility.UrlEncode(Convert.ToString(unicodeString), System.Text.Encoding.UTF8);
            return str;
        }





    }







    public static class CertificateTrust
    {

        public static void SetCertificatePolicy()
        {
            ServicePointManager.Expect100Continue = true;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            ServicePointManager.ServerCertificateValidationCallback
                       += RemoteCertificateValidate;
        }

        private static bool RemoteCertificateValidate(
           object sender, X509Certificate cert,
            X509Chain chain, SslPolicyErrors error)
        { 
            return true;
        }
    }



}
