using Autofac.Features.Metadata;
using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject.Integration;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Integration;
using JieNor.Framework.Interface.Profile;
using JieNor.Framework.Interface.Session;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;


namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 从其他第三方系统同步数据到本系统
    /// </summary>
    [InjectService(SyncDataTypeConst.SynchroDataFrom3Sys )]
    public class SynchroDataFrom3Sys : AbstractOperationService
    {
        protected override string OperationName
        {
            get
            {
                return "同步";
            }
        }
        protected override string PermItem
        {
            get
            {
                return "fw_scrm_from3sys";
            }
        }


        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            return;
        }


        protected override void AfterExecute(ref DynamicObject[] dataEntities)
        {
            base.AfterExecute(ref dataEntities);

            CheckPermission();

             //系统参数里面设置的集成通道名称，category=integration, key 以 channel. 开头，如 channel.k3cloud,channel.oa 等等
            var sysPara = this.Container.GetService<ISystemProfile>();            
            var channels = sysPara?.GetProfile(this.UserCtx, "integration");
            if(channels.Count ==0)
            {
                return;
            }
            List<string> channelName = new List<string>();
            foreach (var kvpItem in channels)
            {
                if (kvpItem.Key.StartsWithIgnoreCase("sp:integration.channel." ))
                {
                    channelName.Add(kvpItem.Value);
                }
            }
            if (channelName.Count ==0)
            {
                return;
            }

            var opList = this.Container.GetService<IEnumerable<Meta<Lazy<ISynDataFrom3Sys>>>>()
                .Where(o => o.Metadata.GetString("AliasName").EqualsIgnoreCase(SyncDataTypeConst.SynchroDataFrom3Sys)
                            && o.Metadata.GetString("formid").EqualsIgnoreCase(this.OperationContext.HtmlForm.Id)
                           && channelName.Contains(o.Metadata.GetString("integrationname"), StringComparer.OrdinalIgnoreCase)).ToList ();
            
            if (opList == null || !opList.Any())
            {
                return;
            }
            
            if (opList != null)
            {
                opList.ToList().ForEach(o =>
                {
                    o.Value.Value.DoSynchroData (this.UserCtx);
                });
            }
            this.OperationContext.Result.MsgStyle = (int)Enu_MsgStyle.Dialog;
            this.OperationContext.Result.SimpleMessage = "同步成功！";
        }


        
    }
}
