
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Integration;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.Interface.Integration;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.Integration
{

    /// <summary>
    /// 从第三方系统同步数据的基类
    /// </summary>
    public abstract class AbstractSynchroFrom3Sys : ISynDataFrom3Sys
    {


        public AbstractSynchroFrom3Sys()
        {

        }


        #region Var


        private object lockObj = new object();
        protected string groupId = Guid.NewGuid().ToString();
      
        /// <summary>
        /// 每次批量处理的单据张数
        /// </summary>
        protected virtual int BatchSize
        {
            get
            {
                return 2000;
            }
        }

         
        protected IRedisCache CacheClient
        {
            get;
            set;
        }


        /// <summary>
        /// 上下文
        /// </summary>
        public UserContext Context
        {
            get;
            protected set;
        }


        /// <summary>
        /// 正在同步的标识
        /// </summary>
        protected string DoingKey
        {
            get
            {
                var key = "integration:{0}.{1}.{2}".Fmt(this.Context.Company, this.SourceSystem, this.TargetFormId);
                return key;
            }
        }

        /// <summary>
        /// 同步的数据类型
        /// </summary>
        public abstract string DataType
        {
            get;
        }

        /// <summary>
        /// 数据同步描述，如：从k3同步物料
        /// </summary>
        public abstract string SynDirectDesc
        {
            get;
        }




        /// <summary>
        /// 来源系统标识：这个必须唯一，比如从k3cloud系统同步过来，则必须指定是那个企业的k3cloud，
        /// 可以类似如下：oppein.k3cloud ; jienor.k3cloud.
        /// 同步的时候，以来源系统标识 + 数据在原系统的内码 作为本系统数据的内码
        /// </summary>
        public abstract  string SourceSystem
        {
            get;
        }





        /// <summary>
        /// 是否模拟测试
        /// </summary>
        public bool IsTest
        {
            get;
            set;
        }



        /// <summary>
        /// 目标系统名称
        /// </summary>
        public string TargetSystem
        {
            get
            {
                return "i-scrm";
            }
        }

        /// <summary>
        /// 目标对象标识
        /// </summary>
        public abstract string TargetFormId
        {
            get;
        }

        /// <summary>
        /// 目标单据表单信息
        /// </summary>
        protected HtmlForm TargetFormMeta
        {
            get
            {
                HtmlForm bizInfor = HtmlParser.LoadFormMetaFromCache(this.TargetFormId, this.Context);
                return bizInfor;
            }
        }

        /// <summary>
        /// 同步单据的编码字段
        /// </summary>
        protected HtmlField TargetBillNoField
        {
            get
            {
                var fld = TargetFormMeta.GetNumberField();
                return fld;
            }
        }



        #endregion Var


        /// <summary>
        /// 总入口：进行同步数据
        /// </summary> 
        public virtual void DoSynchroData(UserContext ctx)
        {
            try
            { 
                groupId = DateTime.Now.ToString("yyyyMMddHHmmssfff");
                Context = ctx;
              
                this.CacheClient = ctx.Container.GetService<IRedisCache>();
                (this.CacheClient as IPreInitCache)?.Init(this.Context);

                SynchroLogInfo log = CreateSynchroLog();
               
                if (!ChechPara(ctx, log) || JudgeDoing(log))
                {
                    return;
                }

                AddDoingCtrl();
                BeforeDoSynchroData();

                var filters = GetSynchroDataFilter(log);
                foreach (var filter in filters)
                {
                    if (IsStopSynchro())
                    {
                        break;
                    }

                    try
                    {
                        var sourceDatas = GetSynchroData(filter, log);
                        if (sourceDatas.IsNullOrEmptyOrWhiteSpace())
                        {
                            //未找到需要同步的数据 
                            continue;
                        }

                        sourceDatas = AfterGetSynchroData(sourceDatas, log);

                        var targetData = BuildTargetBillData(sourceDatas, log);

                        targetData = GetTargetNeedSaveData(targetData, log);

                        var synResult = ExecuteSynchro(targetData, log);                       
                    }
                    catch (Exception ex)
                    {
                       log.AddLogEntry ("数据同步过程中出现异常，异常信息：" + ex.Message + System.Environment.NewLine + ex.StackTrace);
                    }
                }

                AfterDoSynchroData(log);
                CleareDoing();
                DeleteStopFlag();
            }

            catch (Exception ex)
            {  
                CleareDoing();
                DeleteStopFlag();
                var log = CreateSynchroLog();
                log.Content = "数据同步过程中出现异常，异常信息：" + ex.Message + System.Environment.NewLine + ex.StackTrace;
                SynchroDataHelper.WriteSynchroLog(this.Context, log);
            }
        }



        /// <summary>
        /// 取数过滤条件：由于原数据可能比较多，不能一次获取所有数据，
        /// 比如物料数据，超过10万条，一次获取直接挂掉，故需要分批进行，这里通过过滤条件进行分批过滤。
        /// 具体实现方式，可以看从k3导入物料数据
        /// </summary>
        public virtual  List <string > GetSynchroDataFilter(SynchroLogInfo log)
        {
            return new List<string>() { "" };
        }



         




        /// <summary>
        /// 相关参数校验
        /// </summary>
        /// <param name="ctx"></param>
        public virtual bool    ChechPara(UserContext ctx, SynchroLogInfo log)
        { 
            if(ctx.IsNullOrEmpty  ())
            {
                log.Content ="同步的上下文信息不能为空";
                SynchroDataHelper.WriteSynchroLog(ctx, log);
                throw new Exception("同步的上下文信息不能为空");
            }

            return true;
        }



        /// <summary>
        /// 数据同步之前的相关操作：动态增加字段记录数据在第三方系统的主键id，如同步物料时先同步物料分组
        /// </summary> 
        public virtual void BeforeDoSynchroData()
        {
            //动态增加字段记录数据在第三方系统的主键id
            var svc = this.Context.Container.GetService<IRecThirdPartSysData>();            
            svc.BuildThirdPartSysField(this.Context, TargetFormId);             
        }

        /// <summary>
        /// 获取需要同步的数据 
        /// </summary>
        public abstract object GetSynchroData(string filter, SynchroLogInfo log);

        
        /// <summary>
        /// 获取到需要同步的数据之后的相关操作：如重新组织数据，格式转换、前置数据同步之类的
        /// </summary>
        /// <param name="sourceData"></param>
        public virtual object AfterGetSynchroData(object  sourceData, SynchroLogInfo log)
        {
            return sourceData;
        }
        
        /// <summary>
        /// 调用scrm的接口进行同步：默认直接保存数据到库（不调用服务端的Save方法）
        /// </summary>
        /// <param name="targetData"></param> 
        ///  <param name="log">日志信息</param> 
        /// <returns></returns> 
        public virtual string ExecuteSynchro(IEnumerable<DynamicObject> targetData, SynchroLogInfo log)
        { 
            return SaveData(TargetFormMeta,targetData,log );
        }



        /// <summary>
        /// 保存数据：默认直接保存数据到库（不调用服务端的Save方法）
        /// </summary>
        /// <param name="metaData"></param>
        /// <param name="datas"></param>
        /// <param name="log"></param>
        /// <returns></returns>
        protected string SaveData(HtmlForm metaData, IEnumerable<DynamicObject> datas, SynchroLogInfo log)
        {
            if (datas == null || datas.Count() == 0)
            {
                return "";
            }

            var prepareService = Context.Container.GetService<IPrepareSaveDataService>();
            prepareService?.PrepareDataEntity(this.Context, metaData, datas.ToArray(), OperateOption.Create());

            IDataManager dm = Context.Container.GetService<IDataManager>();
            dm.InitDbContext(Context, metaData.GetDynamicObjectType(Context));
             
            log.AddLogEntry("开始分批保存数据（每次保存{0}张单的数据）".Fmt (BatchSize));
            var grp = datas.GetListGroup<DynamicObject>(BatchSize);
            foreach (var item in grp)
            {
                dm.Save(item, null, OperateOption.InstanceBulkCopyAndNoCache);
                log.AddLogEntry("成功保存{0}条数据".Fmt(item.Count));
            }

            log.Content = "数据保存操作完成";

            return log.Content;
        }




        /// <summary>
        /// 构建目标系统的业务对象数据
        /// </summary>
        /// <param name="sourceData"></param>
        /// <param name="log"></param>
        /// <returns></returns>
        public abstract IEnumerable<DynamicObject> BuildTargetBillData(object sourceData, SynchroLogInfo   log);



        /// <summary>
        /// 筛选出需要保存到目标系统的业务对象数据（比如 目标系统已经存在，在不再同步之类的）
        /// </summary>
        /// <param name="sourceData"></param>
        /// <param name="log"></param>
        /// <returns></returns>
        public virtual  IEnumerable<DynamicObject> GetTargetNeedSaveData(IEnumerable<DynamicObject> sourceData, SynchroLogInfo log)
        {
            return sourceData;
        }


        /// <summary>
        /// 数据同步操作完成后:记录同步日志记录
        /// </summary>
        /// <param name="log">日志信息</param>
        public virtual void AfterDoSynchroData(  SynchroLogInfo log)
        {
            //记录同步日志
            log.EndTime = DateTime.Now;
            SynchroDataHelper.WriteSynchroLog(this.Context, log);             
        }





        #region NetControl



        /// <summary>
        /// 判断是否有人正在同步
        /// </summary>
        /// <param name="log"></param>
        /// <returns></returns>
        protected bool JudgeDoing(SynchroLogInfo log)
        {
            var value= this.CacheClient.Get<string>(this.Context, DoingKey);
            if(value.IsNullOrEmptyOrWhiteSpace ())
            {
                return false;
            }

            DateTime date;
            if(DateTime.TryParse (value,out date ))
            {
                if(date.AddHours(4)<DateTime.Now  )
                {
                    //同步超过2个小时视为失败
                    CacheClient.Remove(Context, DoingKey);
                    return false;
                }
            }

            log.Content = "已经有人正在进行数据同步";
            SynchroDataHelper.WriteSynchroLog(this.Context , log);

            return true;
        }
        
       

        /// <summary>
        ///添加正在同步标记
        /// </summary> 
        protected void AddDoingCtrl()
        {
            this.CacheClient.Set(this.Context,DoingKey , DateTime.Now, TimeSpan.FromHours (10));
        }
        
        /// <summary>
        /// 去掉正在同步的标记
        /// </summary> 
        /// <returns></returns>
        protected void CleareDoing()
        {
            CacheClient.Remove(Context, DoingKey);
        }


        #endregion NetControl

        #region WriteSynchroLog

        /// <summary>
        /// 创建同步日志
        /// </summary> 
        protected SynchroLogInfo CreateSynchroLog()
        {
            SynchroLogInfo log = new SynchroLogInfo();
            log.logId = Guid.NewGuid().ToString();
            log.GroupId = groupId;
            log.TargetSystem = TargetSystem;
            log.SourceSystem = SourceSystem;
            log.BeginTime = DateTime.Now;
            log.SyncType = SynDirectDesc;
            log.DirectDesc = SourceSystem + "-->i-scrm：" + SynDirectDesc; 
            return log;
        }



        #endregion WriteSynchroLog
        


        #region Stop Synchro


        /// <summary>
        /// 是否终止后续的所有同步（具体作法：外部在数据库插入一条记录标准要终止同步，参照DeleteStopFlag()）
        /// </summary> 
        /// <returns></returns>
        public virtual bool IsStopSynchro()
        {
            return false;
        }
        
        /// <summary>
        /// 删除终止同步标记
        /// </summary>
        /// <returns></returns>
        public virtual void DeleteStopFlag()
        {
           
        }


        #endregion














    }


}
