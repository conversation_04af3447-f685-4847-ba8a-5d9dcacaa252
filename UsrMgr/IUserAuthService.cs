using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.OpData;
using JieNor.Framework.SuperOrm;
using ServiceStack;
using ServiceStack.Auth;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{

    /// <summary>
    /// 用户注册、认证等等相关服务接口
    /// </summary>
    public interface IUserAuthService : ILoginService
    {

        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <param name="userName"></param>
        /// <returns></returns>
        CustomUserAuth GetUserAuthInfo(string userName);

        /// <summary>
        /// 获取操作的上下文信息
        /// </summary>
        /// <param name="sessionId"></param>
        /// <returns></returns>
        UserContext GetOperationContext(string sessionId);

        /// <summary>
        /// 批量注册用户接口：多用于用户的Excel导入及从第三方系统导入用户信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="lstUserRegistry"></param>
        /// <returns></returns>
        IOperationResult RegisterUser(UserContext ctx, IEnumerable<UserRegistry> lstUserRegistry);

        /// <summary>
        /// 检查用户是否存在（用户管理中心的用户）
        /// </summary>
        /// <param name="userName">用户编码（按编码唯一）</param>
        /// <returns></returns>
        bool IsUserExist(string userName);




        /// <summary>
        /// 重置用户密码
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="lstUsers"></param>
        /// <param name="password"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        IOperationResult ResetUserPassword(UserContext ctx, IEnumerable<string> lstUsers, string password, OperateOption option = null);


        /// <summary>
        /// 锁定账户
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="lstUsers"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        IOperationResult LockUser(UserContext ctx, IEnumerable<string> lstUsers, OperateOption option = null);


        /// <summary>
        /// 解锁账户
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="lstUsers"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        IOperationResult UnLockUser(UserContext ctx, IEnumerable<string> lstUsers, OperateOption option = null);


        /// <summary>
        /// 按用户id，获取用户的在线信息
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        CustomUserAuth GetOnlineInfo(string userId);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="loginInfo"></param>
        /// <returns></returns>
        void UpdateOnlineInfo(CustomUserAuth loginInfo);

    }



    /// <summary>
    /// 登录认证服务
    /// </summary>
    public interface ILoginService
    {

        /// <summary>
        /// 是否已经认证
        /// </summary>
        /// <param name="sessionId"></param>
        /// <returns></returns>
        bool IsAuthorized(string sessionId);

        /// <summary>
        /// 用户登陆认证
        /// </summary>
        /// <param name="psw"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        IOperationResult Login(string userName, string psw);


        /// <summary>
        /// 用户注销登录
        /// </summary>
        /// <param name="authInfo"></param> 
        /// <returns></returns>
        void Logout(Authenticate authInfo);


    }

}
