using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.MetaCore.FormOp.OpData;
using JieNor.Framework.DataTransferObject.Permission;

namespace JieNor.Framework.Interface
{


    /// <summary>
    /// 供应商、企业注册服务接口
    /// </summary>
    public interface IJNOrgRegister
    {


        /// <summary>
        /// 企业注册
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="companyInfo"></param> 
        /// <returns> 企业id</returns>
        string  RegisterCompany(UserContext ctx, RegisterSupplyDTO companyInfo );


        /// <summary>
        /// 生成官方招募链接
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="companyInfo"></param>
        /// <returns>招募链接</returns>
        string BuildOrgVisiteUrl(UserContext ctx, RegisterSupplyDTO companyInfo);
       

    }






}
