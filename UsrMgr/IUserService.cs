using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.OpData;
using JieNor.Framework.SuperOrm;
using ServiceStack;
using ServiceStack.Auth;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.Framework.Interface
{

    /// <summary>
    /// 用户相关服务接口
    /// </summary>
    public interface IUserService
    {

        /// <summary>
        /// 是否存在当前经销商配置在子经销商
        /// </summary>
        /// <returns></returns>
        DynamicObject GetMaingAgent(CompanyDCInfo company);
    }


}
