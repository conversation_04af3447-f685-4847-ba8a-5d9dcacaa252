using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.MetaCore.FormOp.OpData;
using JieNor.Framework.DataEntity.MainFw;
using Newtonsoft.Json.Linq;

namespace JieNor.Framework.Interface
{


    /// <summary>
    /// 模块视图接口:各业务系统可以实现自己的业务视图
    /// 1、平台获取所有的主控台菜单传入（有权限的菜单）
    /// 2、各业务系统依据自己的需要返回要显示的模块及菜单
    /// 比如：SCRM 会依据不同的用户身份（个人还是企业），
    ///      显示不同的业务模块及菜单
    /// </summary>
    public interface IBizModuleView
    {
        /// <summary>
        /// 清空用户对应的菜单列表
        /// </summary>
        /// <param name="ctx"></param>
        void ClearMenuCache(UserContext ctx);
        /// <summary>
        /// 返回可见的菜单视图
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="bizMdlItem">主控台菜单视图</param>
        /// <returns>可见的主控台菜单视图</returns>
        IEnumerable<BizModuleItem> FilterBizModule(UserContext ctx, IEnumerable<BizModuleItem> bizMdlItem);

        /// <summary>
        /// 根据传入的参数判断菜单是否可显示
        /// </summary>
        /// <param name="menu"></param>
        /// <param name="parameters"></param>
        /// <returns>false 可见（不过滤掉该菜单），true 不可见（过滤掉该菜单）</returns>
        bool FilterMenu(UserContext userCtx, JObject menu, Dictionary<string, object> parameters);
        /// <summary>
        /// 根据是否启用外部审批流判断菜单是否显示
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="menu"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        void FilterOuterParamsMenu(UserContext ctx, IEnumerable<JObject> menu, string formId);
    }
}
