using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 单据编号、基础资料编码的生成接口
    /// </summary>
    public interface IBillNoService
    {


        /// <summary>
        /// 构建单据编号 （基础资料编码）
        /// 1、从编码配置表获取编码的表达式
        /// 2、构建单据编号
        /// 3、设置单据编号
        /// 需注意：
        /// A、没有设置编码表达式的，默认设置为 'No.' + Seq(10)
        /// B、单据上的编码字段不为空的，不再重新编码（要重新编码，请先清空编码字段）
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="billDatas"></param>
        void SetBillNo(UserContext ctx, string formId, IEnumerable<DynamicObject> billDatas);


        /// <summary>
        /// 构建单据编号 （基础资料编码），单据编号由 常量（可选） + 单据上字段值（可多选） + 流水号 组成
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId">业务对象</param>
        /// <param name="billData">业务对象实例，如单据数据</param>
        /// <param name="express">获取编码的表达式：如 'cgrk' + FDate + Seq(4)，需要注意的是流水号放最后</param>
        /// <returns>编码表达式 'cgrk' + FDate + Seq(4) ，
        /// 则返回格式如 cgrk201606070001、cgrk201606070002 的编码</returns>
        void SetBillNo(UserContext ctx, string formId, DynamicObject billData, string express);

        /// <summary>
        /// 构建单据编号 （基础资料编码），单据编号由 常量（可选） + 单据上字段值（可多选） + 流水号 组成
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId">业务对象</param>
        /// <param name="billData">业务对象实例，如单据数据</param>
        /// <param name="express">获取编码的表达式：如 'cgrk' + FDate + Seq(4)，需要注意的是流水号放最后</param>
        /// <returns>编码表达式 'cgrk' + FDate + Seq(4) ，
        /// 则返回格式如 cgrk201606070001、cgrk201606070002 的编码</returns>
        void SetBillNo(UserContext ctx, string formId, IEnumerable<DynamicObject> billData, string express);

        /// <summary>
        /// 构建单据编号 （基础资料编码），单据编号由 常量（可选） + 单据上字段值（可多选） + 流水号 组成
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId">业务对象</param>
        /// <param name="billData">业务对象实例，如单据数据</param>
        /// <param name="express">获取编码的表达式：如 'cgrk' + FDate + Seq(4)，需要注意的是流水号放最后</param>
        /// <returns>编码表达式 'cgrk' + FDate + Seq(4) ，
        /// 则返回格式如 cgrk201606070001、cgrk201606070002 的编码</returns>
        string GetBillNo(UserContext ctx, string formId, DynamicObject billData, string express);

        /// <summary>
        /// 构建单据编号 （基础资料编码），单据编号由 常量（可选） + 单据上字段值（可多选） + 流水号 组成
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId">业务对象</param>
        /// <param name="billData">业务对象实例，如单据数据</param>
        /// <param name="express">获取编码的表达式：如 'cgrk' + FDate + Seq(4)，需要注意的是流水号放最后</param>
        /// <returns>编码表达式 'cgrk' + FDate + Seq(4) ，
        /// 则返回格式如 cgrk201606070001、cgrk201606070002 的编码</returns>
        Dictionary<string, DynamicObject> GetBillNo(UserContext ctx, string formId, IEnumerable<DynamicObject> billData, string express);

        /// <summary>
        /// 获取编码规则数据对象
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        DynamicObject GetBillNoSetting(UserContext ctx, string formId);


        /// <summary>
        /// 校正编码。接口调用场景：保存校验时编码唯一性校验不通过的时候，调用该接口重置编码 
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="billDatas"></param>
        void RevisionBillNo(UserContext ctx, string formId, IEnumerable<DynamicObject> billDatas);

        /// <summary>
        /// 校正编码。接口调用场景：保存校验时编码唯一性校验不通过的时候，调用该接口重置编码 
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="billData"></param>
        void RevisionBillNo(UserContext ctx, string formId, DynamicObject billData);

        //void ClearCache();


    }
}
