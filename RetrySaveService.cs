using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Serialization;
using Newtonsoft.Json.Linq;

namespace JieNor.Framework.AppService
{
    /// <summary>
    /// 重试保存服务实现
    /// </summary>
    [InjectService]
    public class RetrySaveService : IRetrySaveService
    {
        /// <summary>
        /// 队列缓存键
        /// </summary>
        private const string QueueCacheKey = "Retry:Keys:{0}";

        /// <summary>
        /// 缓存项缓存键格式
        /// </summary>
        private const string ItemCacheKeyFmt = "Retry:Items:{0}:{1}";

        /// <summary>
        /// 默认过期时间（3天）
        /// </summary>
        private static readonly TimeSpan ExpiresIn = new TimeSpan(3, 0, 0, 0, 0);

        /// <summary>
        /// 重试写入
        /// </summary>
        /// <param name="userCtx"></param>
        public void Retry(UserContext userCtx, IEnumerable<string> bizFormIds = null, Action<string> writeLogFunc = null)
        {
            if (bizFormIds == null || !bizFormIds.Any()) return;

            // 取出所有
            var sessionCache = userCtx.Container.GetService<ISessionCache>();

            var metaModelService = userCtx.Container.GetService<IMetaModelService>();
            var prepareSaveService = userCtx.Container.GetService<IPrepareSaveDataService>();
            var logService = userCtx.Container.GetService<ILogServiceEx>();

            foreach (var formId in bizFormIds)
            {
                var queueCacheKey = QueueCacheKey.Fmt(formId);
                var cacheKeys = sessionCache.GetAllItemsFromSet<string>(queueCacheKey);

                if (cacheKeys == null || !cacheKeys.Any())
                {
                    writeLogFunc?.Invoke($"重试写入完成：{formId} 没有需重试的数据");
                    continue;
                }

                var dm = userCtx.Container.GetService<IDataManager>();

                foreach (var cacheKey in cacheKeys)
                {
                    bool removeItem = true;
                    try
                    {
                        var cacheItem = sessionCache.Get<CacheItem>(cacheKey);
                        if (cacheItem == null
                            || cacheItem.JwtToken.IsNullOrEmptyOrWhiteSpace()
                            || cacheItem.FormId.IsNullOrEmptyOrWhiteSpace()
                            || cacheItem.BillData.IsNullOrEmptyOrWhiteSpace())
                        {
                            logService.Error($"重试写入失败({cacheKey})：找不到重试写入的数据");
                            continue;
                        }

                        var ctx = userCtx.Container.FromJwtToken(cacheItem.JwtToken); ;
                        if (ctx == null)
                        {
                            logService.Error($"重试写入失败({cacheKey})：ctx is null");
                            continue;
                        }

                        if (ctx.Company.IsNullOrEmptyOrWhiteSpace() || ctx.Company.EqualsIgnoreCase("jn.sz.saas.auth") || ctx.Company.EqualsIgnoreCase("jn.sz.saas.eis"))
                        {
                            logService.Error($"重试写入失败({cacheKey})：ctx company is {ctx.Company}");
                            continue;
                        }

                        var billData = cacheItem.BillData;
                        if (billData.IsNullOrEmptyOrWhiteSpace())
                        {
                            logService.Error($"重试写入失败({cacheKey})：billData is null");
                            continue;
                        }

                        var htmlForm = metaModelService.LoadFormModel(ctx, cacheItem.FormId);

                        var dataEntities = GetDynamicObjects(ctx, htmlForm, billData);
                        if (dataEntities == null || !dataEntities.Any())
                        {
                            logService.Error($"重试写入失败({cacheKey})：dataEntities is null");
                            continue;
                        }

                        var dt = htmlForm.GetDynamicObjectType(ctx);
                        if (htmlForm.Id.EqualsIgnoreCase("bd_record"))
                        {
                            var billFormId = Convert.ToString(dataEntities.First()["fbizformid"]);
                            dt = logService.LoadFormLogType(ctx, billFormId);
                        }
                        else if (htmlForm.Id.EqualsIgnoreCase("sys_logdata"))
                        {
                            dt = logService.LoadFormSysLogType(ctx);
                        }

                        // 判断是否使用了从库
                        if (htmlForm.FormOperations.Any(s => s.UseSlave))
                        {
                            ctx = ctx.CreateSlaveDBContext();
                        }

                        prepareSaveService?.PrepareDataEntity(ctx, htmlForm, dataEntities.ToArray(),
                            OperateOption.Create());

                        var tableName = dt.GetTableName();
                        var ids = dataEntities.Select(s => s["id"]?.ToString()).ToList();

                        // 先判断是否已在数据库中
                        string sql =
                            $"select {htmlForm.BillPKFldName} as id from {tableName} with(nolock) where {htmlForm.BillPKFldName} in ({ids.JoinEx(",", true)})";

                        var existsIds = ctx.ExecuteDynamicObject(sql, new List<SqlParam>()).Select(s => Convert.ToString(s["id"]))
                             .ToList();

                        dataEntities = dataEntities.Where(s => !existsIds.Contains(Convert.ToString(s["id"]))).ToList();
                        if (dataEntities.Any())
                        {
                            dm.InitDbContext(ctx, dt);
                            dm.Save(dataEntities);

                            //logService.Info($"重试写入成功({cacheKey})：{dataEntities.ToJson(false)}");
                            writeLogFunc?.Invoke($"重试写入成功({cacheKey})：{dataEntities.ToJson(false)}");
                        }
                        else
                        {
                            //logService.Info($"重试写入成功({cacheKey})：无");
                            writeLogFunc?.Invoke($"重试写入成功({cacheKey})：无");
                        }
                    }
                    catch (Exception ex)
                    {
                        var value = sessionCache.Get<string>(cacheKey);

                        logService.Error($"重试写入失败：{cacheKey}", ex);
                        logService.Error($"重试写入失败：{cacheKey}:{value}");

                        writeLogFunc?.Invoke($"重试写入失败：{cacheKey} {ex.Message}");

                        removeItem = false;
                    }
                    finally
                    {
                        if (removeItem)
                        {
                            sessionCache.RemoveItemFromSet(queueCacheKey, cacheKey);
                            sessionCache.Remove(cacheKey);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 加入重试队列
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="dataEntities"></param>
        public void Enqueue(UserContext userCtx, HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntities)
        {
            Enqueue(userCtx, htmlForm, dataEntities, ExpiresIn);
        }

        /// <summary>
        /// 加入重试队列
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="dataEntities"></param>
        public void Enqueue(UserContext userCtx, HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntities, TimeSpan expiresIn)
        {
            if (dataEntities == null || !dataEntities.Any()) return;

            var queueCacheKey = QueueCacheKey.Fmt(htmlForm.Id.ToLower());

            try
            {
                // 写入会话，后续处理
                var sessionCache = userCtx.Container.GetService<ISessionCache>();

                string cacheKey = ItemCacheKeyFmt.Fmt(htmlForm.Id.ToLower(), Guid.NewGuid().ToString("N"));

                sessionCache.AddItemToSet(queueCacheKey, cacheKey);

                var expireDate = DateTime.UtcNow.AddDays(HostConfigView.Jwt.ExpireTokensInDays);
                var jwtToken = JwtUtil.CreateJwtToken(userCtx, HostConfigView.Jwt.PrivateKeyXml, expireDate);

                sessionCache.Set(cacheKey, new CacheItem
                {
                    JwtToken = jwtToken,
                    FormId = htmlForm.Id,
                    BillData = dataEntities.ToJson(false)
                }, expiresIn);
            }
            catch (Exception ex)
            {
                userCtx.Container.GetService<ILogServiceEx>().Error("操作日志保存失败", ex);
            }
        }

        /// <summary>
        /// 根据数据包解析为动态对象
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="billDatas"></param>
        /// <returns></returns>
        public static List<DynamicObject> GetDynamicObjects(UserContext userCtx, HtmlForm htmlForm, string billDatas)
        {
            if (billDatas.IsNullOrEmptyOrWhiteSpace())
            {
                return null;
            }

            var uiData = billDatas;

            var dt = htmlForm.GetDynamicObjectType(userCtx);
            var billItems = JArray.Parse(uiData);
            List<DynamicObject> lstDataEntities = new List<DynamicObject>();
            var dcSerializer = userCtx.Container.GetService<IDynamicSerializer>();
            dcSerializer.Sync(dt, lstDataEntities, billItems, (propKey) =>
            {
                var el = htmlForm?.GetElement(propKey);
                if (el is HtmlField) return (el as HtmlField).DynamicProperty;
                if (el is HtmlEntryEntity) return (el as HtmlEntryEntity).DynamicProperty;

                return null;
            },
                null,
                null,
                null);

            // 填充id

            var entryList = htmlForm.EntryList;
            for (int i = 0, ilen = lstDataEntities.Count; i < ilen; i++)
            {
                var dataEntity = lstDataEntities[i];
                var billItem = billItems[i] as JObject;

                dataEntity["id"] = billItem.GetJsonValue("id", "");

                foreach (var entryEntity in entryList)
                {
                    // 暂时忽略子单据体
                    if (entryEntity is HtmlSubEntryEntity)
                    {
                        continue;
                    }

                    var entrys = dataEntity[entryEntity.PropertyName] as DynamicObjectCollection;
                    var entryItems = billItem[entryEntity.PropertyName] as JArray;

                    for (int j = 0, jLen = entrys.Count; j < jLen; j++)
                    {
                        var entry = entrys[j];
                        var entryItem = entryItems[j] as JObject;

                        entry["id"] = entryItem.GetJsonValue("id", "");
                    }
                }
            }

            return lstDataEntities;
        }


        class CacheItem
        {
            public string JwtToken { get; set; }

            public string BillData { get; set; }

            public string FormId { get; set; }
        }
    }

}
