using JieNor.Framework.SuperOrm.DataEntity;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.BizTask
{

    /// <summary>
    /// 后台任务信息
    /// </summary>
    public class ScheduleTaskObject
    {
        /// <summary>
        /// 构造函数，初始化计划任务
        /// </summary>
        /// <param name="taskId"></param>
        /// <param name="serviceId"></param>
        /// <param name="taskName"></param>
        /// <param name="taskLabel"></param>
        /// <param name="formId"></param>
        /// <param name="taskParam"></param>
        public ScheduleTaskObject(string taskId, string serviceId, string taskName, string taskLabel, string formId, object taskParam=null)
        {
            this.Id = taskId;
            this.JobId = taskId;
            this.ServiceId = serviceId;
            this.TaskName = taskName;
            this.TaskLabel = taskLabel;
            this.FormId = formId;
            this.Parameter = taskParam;
        }
        /// <summary>
        /// 关联的db任务对象
        /// </summary>
        public DynamicObject DataEntity { get; set; }
        /// <summary>
        /// 计划任务对象
        /// </summary>
        /// <param name="dataEntity"></param>
        public static implicit operator ScheduleTaskObject(DynamicObject dataEntity)
        {
            if (dataEntity == null) return null;
            
            return new ScheduleTaskObject(dataEntity["id"] as string,
                dataEntity["fpluginid"] as string,
                dataEntity["fname"] as string,
                dataEntity["ftasklabel"] as string,
                dataEntity["fworkobject"] as string,
                dataEntity["ftaskparameter"])
            {
                DataEntity = dataEntity
            };
        }

        /// <summary>
        /// 重设任务实例标识
        /// </summary>
        /// <param name="taskInstId"></param>
        public void SetJobId(string taskInstId)
        {
            this.JobId = taskInstId;
        }


        /// <summary>
        /// 重设任务名称
        /// </summary>
        /// <param name="taskName"></param>
        public void SetTaskName(string taskName)
        {
            this.TaskName = taskName;
        }

        /// <summary>
        /// 任务id,默认就是数据库计划任务主键
        /// </summary> 
        public string Id
        {
            get; internal set;
        }
        /// <summary>
        /// 任务Id,默认等于id,如果任务是支持多实例，则此属性会被引擎重写，此属性是引擎识别计算任务的一个属性，另一个属性是指分组
        /// </summary>
        public string JobId { get; internal set; }
        
        /// <summary>
        /// 任务分组
        /// </summary>
        public string JobGroup
        {
            get
            {
                return $"{this.FormId}_{this.TaskLabel}";
            }
        }

        /// <summary>
        /// 任务唯一 识别
        /// </summary>
        public string Identity
        {
            get { return $"{this.JobId}_{this.JobGroup}".Replace("#","_"); }
        }

        /// <summary>
        /// 任务名称
        /// </summary> 
        public string TaskName
        {
            get; internal set;
        }

        /// <summary>
        /// 任务关联的业务对象（用于任务分组）
        /// </summary>
        public string FormId { get; internal set; }
        
        /// <summary>
        /// 任务标签
        /// </summary>
        public string TaskLabel { get; internal set; }
                
        /// <summary>
        /// 任务标识
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return "{0} {1}".Fmt(this.Id, this.TaskName);
        }

        /// <summary>
        /// 执行对应服务的标识，计划任务处理器（TaskSvrIdAttribute标记的）
        /// </summary>
        public string ServiceId { get; internal set; }

        /// <summary>
        /// 任务参数配置数据
        /// </summary>
        public object Parameter { get; internal set; }

    }

}
