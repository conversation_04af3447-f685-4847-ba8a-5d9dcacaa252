using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.Framework.AppService.BizTask
{
    /// <summary>
    /// 计划任务：中断
    /// </summary>
    [InjectService]
    [FormId("bas_task")]
    [OperationNo("ewtermination")]
    public class TaskInterrupteButton : AbstractOperationServicePlugIn
    {
        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            base.OnPrepareOperationOption(e);
            e.OpCtlParam.IgnoreOpMessage = true;
        }

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            if (e.DataEntitys.IsNullOrEmptyOrWhiteSpace() || e.DataEntitys.ToList().Count == 0)
            {
                this.Result.SimpleMessage = "请选择一条数据进行操作！";
                return;
            }

            var task = this.Container.GetService<IJobScheduleService>();

            List<DynamicObject> setupData = e.DataEntitys.ToList();
            foreach (var item in setupData)
            {
                var taskName = item["fname"] as string;
                bool isOk = task.InterruptJob(this.Context, new string[] { item["Id"].ToString() }).GetAwaiter().GetResult();
                if (isOk)
                {
                    item["fstartstatus"] = "ew_start006";
                    this.Result.ComplexMessage.SuccessMessages.Add($"任务{taskName}本次执行已成功被中断，将等待下一次启动……！");
                }
                else
                {
                    this.Result.ComplexMessage.WarningMessages.Add($"任务{taskName}打断失败：可能任务不在运行中，请确认后重试！");
                }
            }
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(setupData);
            this.AddSetValueAction("fstartstatus", "ew_start006");
            this.Result.IsSuccess = true;

            this.AddRefreshPageAction();
        }
    }
}
