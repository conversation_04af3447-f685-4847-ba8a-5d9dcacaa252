using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.BizTask
{
    /// <summary>
    /// 任务数据上下文
    /// </summary>
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public class JobExecutionContext : ICloneable
    {
        /// <summary>
        /// 任务执行上下文 
        /// </summary>
        public JobExecutionContext()
        {
            this.Result = new OperationResult();
            this.Option = OperateOption.Create();
        }

        /// <summary>
        /// 连接上下文
        /// </summary>
        public UserContext UserContext { get; set; }

        /// <summary>
        /// 任务执行结果输出
        /// </summary>
        [InjectProperty]
        public IOperationResult Result { get; set; }

        /// <summary>
        /// 任务执行日志对象
        /// </summary>
        public DynamicObject TaskLogObject { get; set; }
        
        /// <summary>
        /// 任务对应的数据实体，当为临时任务时，此对象为null
        /// </summary>
        public ScheduleTaskObject TaskObject { get; set; }

        /// <summary>
        /// 操作参数
        /// </summary>        
        [InjectProperty]
        public OperateOption Option { get; set; }

        /// <summary>
        /// 克隆整个执行上下文
        /// </summary>
        /// <returns></returns>
        public object Clone()
        {
            JobExecutionContext newCtx = new JobExecutionContext();
            newCtx.UserContext = this.UserContext.Clone() as UserContext;
            newCtx.TaskLogObject = null;
            newCtx.TaskObject = this.TaskObject;
            newCtx.Option = this.Option;
            return newCtx;
        }
    }
}
