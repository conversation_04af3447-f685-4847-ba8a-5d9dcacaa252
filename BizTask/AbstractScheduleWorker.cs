using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Enums;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using JieNor.Framework.DataEntity.Tmpl;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore;
using JieNor.Framework.Interface.QueryBuilder;
using Newtonsoft.Json.Linq;
using Autofac.Features.Metadata;
using Quartz;
using JieNor.Framework.CustomException;

namespace JieNor.Framework.Interface.BizTask
{
    /// <summary>
    /// 任务基类：所有其他后台任务，从这个基类继承实现
    /// </summary>
    [DisallowConcurrentExecution]
    public abstract class AbstractScheduleWorker : IScheduleWorker, IScheduleTaskLogger, IJob
    {
        /// <summary>
        /// 任务执行上下文
        /// </summary>
        private JobExecutionContext JobExecutionContext { get; set; }

        /// <summary>
        /// 参数配置页面
        /// </summary>
        public virtual string ParamFormId
        {
            get
            {
                return "";
            }
        }

        /// <summary>
        /// 标准操作上下文
        /// </summary> 
        public UserContext UserContext
        {
            get
            {
                return this.JobExecutionContext?.UserContext;
            }
        }

        /// <summary>
        /// 操作选项
        /// </summary>
        public OperateOption Option
        {
            get { return this.JobExecutionContext?.Option; }
        }

        /// <summary>
        /// 执行结果
        /// </summary>
        public IOperationResult Result
        {
            get
            {
                return this.JobExecutionContext?.Result;
            }
        }

        /// <summary>
        /// 任务信息 
        /// </summary>
        public ScheduleTaskObject TaskObject
        {
            get
            {
                return this.JobExecutionContext?.TaskObject;
            }
        }

        /// <summary>
        /// 任务进度服务接口
        /// </summary>
        [InjectProperty]
        protected ITaskProgressService TaskProgressService { get; set; }

        /// <summary>
        /// 模型服务
        /// </summary>
        [InjectProperty]
        protected IMetaModelService MetaModelService { get; set; }

        /// <summary>
        /// 数据库访问服务
        /// </summary>
        [InjectProperty]
        protected IDBService DBService { get; set; }

        /// <summary>
        /// 任务标识
        /// </summary>
        protected string TaskId
        {
            get
            {
                return $"{this.TaskObject?.Identity}";
            }
        }
                
        /// <summary>
        /// 初始化任务执行上下文
        /// </summary>
        /// <param name="paramList"></param>
        /// <returns></returns>
        public async Task InitializeContext(params object[] paramList)
        {
            foreach(var obj in paramList)
            {
                if(obj is JobExecutionContext)
                {
                    this.JobExecutionContext = obj as JobExecutionContext;
                }
            }

            DBService = this.UserContext?.Container?.GetService<IDBService>();
            TaskProgressService= this.UserContext?.Container?.GetService<ITaskProgressService>();
            MetaModelService = this.UserContext?.Container?.GetService<IMetaModelService>();

            //this.TaskProgressService = this.UserContext?.Container?.GetService<ITaskProgressService>();
            this.Result.SimpleData["taskLogId"] = Convert.ToString(this.JobExecutionContext.TaskLogObject?["id"]);
            await Task.FromResult(1);
        }

        /// <summary>
        /// 执行计划任务具体业务逻辑
        /// </summary>
        public async Task<IOperationResult> Execute()
        {
            //todo:计划任务的插件模型后续遇到实际业务时再进行提炼
            //var jobCommon = this.UserContext.Container.GetService<IEnumerable<Meta<IScheduleWorker>>>()
            //        .FirstOrDefault(o => o.Metadata.GetString("AliasName").EqualsIgnoreCase("Task")
            //                && o.Metadata.GetString("TaskSvrId").EqualsIgnoreCase("fw.common.task"));
            try
            {
                await DoExecute();

                var result = await Task.FromResult(this.Result);

                this.TaskProgressService.SetTaskReturnData(this.UserContext, this.TaskId, result);

                return result;
            }
            finally
            {
                //将进度置为全部完成
                //this.SetTaskProgress(100, null);
                this.TaskProgressService?.SetTaskProgressValue(this.UserContext, this.TaskId, 100);
            }                
        }

        /// <summary>
        /// 设置任务进度
        /// </summary>
        /// <param name="progress"></param>
        /// <param name="message"></param>
        protected void SetTaskProgress(decimal progress, string message)
        {
            //this.TaskProgressService?.SetTaskProgressMessage(this.UserContext, this.TaskId, message);
            //this.TaskProgressService?.SetTaskProgressValue(this.UserContext, this.TaskId, progress);
            progress = progress < 0 ? 0 : progress;
            progress = progress >= 100 ? 99 : progress;
            this.TaskProgressService?.SetTaskProgressValueAndMessage(this.UserContext, this.TaskId, progress, message);
        }

        /// <summary>
        /// 设置任务执行结果状态（是否有错误发生）
        /// </summary>
        /// <param name="progress"></param>
        /// <param name="message"></param>
        protected void SetTaskExeResultStatus(bool  haveError)
        { 
            this.TaskProgressService?.SetTaskExeResultErrStatus(this.UserContext, this.TaskId, haveError);
        }

        /// <summary>
        /// 写任务运行时日志
        /// </summary>
        /// <param name="log"></param>
        /// <param name="form"></param>
        public void WriteLog(string log, HtmlForm form=null)
        {
            if (log.IsNullOrEmptyOrWhiteSpace()) return;

            if (this.JobExecutionContext == null)
            {
                //不支持从非计划任务执行上下文里访问此接口
                return;
            }
            if (this.JobExecutionContext.TaskLogObject == null) return;
            var logEntrys = this.JobExecutionContext.TaskLogObject["fentity"] as DynamicObjectCollection;
            var logObj = logEntrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
            logObj["ftasktime"] = DateTime.Now;
            if (logEntrys.Count < 1000)
            {
                logObj["fdescribe_e"] = $"{(form == null ? "" : (form.Caption + ":"))}{log}";
            }
            else if (logEntrys.Count == 1000)
            {
                logObj["fdescribe_e"] = $"{(form == null ? "" : (form.Caption + ":"))}本次任务消息过多，已被系统丢弃……";
            }
            else
            {
                return;
            }
            logEntrys.Add(logObj);
        }

        /// <summary>
        /// 保存任务运行时日志
        /// </summary>
        public void SaveLog()
        {
            if (this.JobExecutionContext == null)
            {
                //不支持从非计划任务执行上下文里访问此接口
                return;
            }
            if (this.JobExecutionContext.TaskLogObject == null) return;

            this.JobExecutionContext.UserContext.SaveBizData("bas_tasklog", this.JobExecutionContext.TaskLogObject);
        }

        /// <summary>
        /// 具体执行计划任务逻辑
        /// </summary>
        protected abstract Task DoExecute();


        /// <summary>
        /// 执行任务
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>        
        public async Task Execute(IJobExecutionContext context)
        {
            try
            {
                Thread.CurrentThread.IsBackground = true;
                object jobCtx;
                context.JobDetail.JobDataMap.TryGetValue("jobContext", out jobCtx);
                var jobExecuteContext = jobCtx as JobExecutionContext;
                if (jobExecuteContext == null
                    || jobExecuteContext.TaskObject == null)
                {
                    throw new BusinessException("无效的计划任务上下文！");
                }

                await InitializeContext(jobExecuteContext);
                var result = await Execute();
            }
            catch (Exception exp)
            {
                throw new JobExecutionException(exp, false);
            }
        }


    }


}
