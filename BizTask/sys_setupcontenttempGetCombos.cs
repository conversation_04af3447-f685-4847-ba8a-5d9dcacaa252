using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.BizTask
{
    /// <summary>
    /// 根据formid，获取操作码
    /// </summary>
    [InjectService]
    [FormId("sys_setupcontenttemp")]
    [OperationNo("getcombos")]
    public class sys_setupcontenttempGetCombos : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if (!this.SimpleData.ContainsKey("formId"))
            {
                return;
            }
            string formId = this.SimpleData["formId"];
            if (formId.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            List<object> opNames = new List<object>();
            List<object> fieldNames = new List<object>();
            List<object> bdModelNames = new List<object>();
            var metaModel = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, formId);
            if (formId == "bas_task")
            {
                string Sql = @"select fid,fname,fworkobject from t_bas_task ";
                DynamicObjectCollection dy = this.DBService.ExecuteDynamicObject(this.Context, Sql, null);
                if (dy.IsNullOrEmptyOrWhiteSpace())
                {
                    return;
                }
                foreach (var item in dy)
                {
                    object opName = new
                    {
                        id = item["fid"]+":"+item["fname"],
                        name = item["fname"]
                    };
                    opNames.Add(opName);
                }
            }
            else
            {
                if (metaModel.FormOperations.IsNullOrEmptyOrWhiteSpace() || metaModel.FormOperations.Count == 0)
                {
                    this.Result.SimpleMessage = "改单据没有配置对应的操作";
                    return;
                }
                foreach (HtmlOperation op in metaModel.FormOperations)
                {
                    object opName = new
                    {
                        id = op.OperationNo + ":" + op.OperationName,
                        name = op.OperationName
                    };
                    opNames.Add(opName);
                }
            }
            object mn = new
            {
                id = 0,
                name = "",
            };
            bdModelNames.Add(mn);
            foreach (HtmlField field in metaModel.GetFieldList())
            {
                if(field is HtmlBasePropertyField || field is HtmlButtonField)
                {
                    continue;
                }
                object fieldName = new
                {
                    id = field.Id,
                    name = field.Caption
                };
                fieldNames.Add(fieldName);
                if(field is HtmlBaseDataField && field.IsBillHeadField)
                {
                    var bdField = field as HtmlBaseDataField;
                    var bdMdl = bdField.RefHtmlForm(this.Context);
                    if (bdMdl.IsNullOrEmptyOrWhiteSpace())
                    {
                        continue;
                    }
                    
                    var mdlName = new
                    {
                        id = bdMdl.Id + "." + bdField.Id,
                        name = bdMdl.Caption + "." + bdField.Caption
                    };
                    bdModelNames.Add(mdlName);
                }
            }
            object obj = new
            {
                opName = opNames,
                fieldName = fieldNames,
                bdMdlName = bdModelNames
            };
            this.Result.SrvData = obj;
        }
    }
}
