using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.BizTask
{
    /// <summary>
    /// 计划任务：查询
    /// </summary>
    [InjectService]
    [FormId("bas_task")]
    [OperationNo("querydata")]
    public class TaskQueryData : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName.ToLower())
            {
                case "afterlistdata":
                    this.AfterListData(e);
                    break;
            }
        }

        /// <summary>
        /// 列表数据查询后触发的事件：可以干预列表数据
        /// </summary>
        /// <param name="e"></param>
        private void AfterListData(OnCustomServiceEventArgs e)
        {
            var listData = e.EventData as List<Dictionary<string, object>>;

            if (listData.IsNullOrEmpty()) return;

            var jobScheduleService = this.Container.GetService<IJobScheduleService>();

            var dt = this.HtmlForm.GetDynamicObjectType(this.Context);

            foreach (var item in listData)
            {
                if (item.TryGetValue("fbillhead_id", out var id))
                {
                    item.TryGetValue("fpluginid", out var fpluginid);
                    item.TryGetValue("fname", out var fname);
                    item.TryGetValue("ftasklabel", out var ftasklabel);
                    item.TryGetValue("fworkobject", out var fworkobject);
                    item.TryGetValue("ftaskparameter", out var ftaskparameter);
                    item.TryGetValue("fmainorgid", out var fmainorgid);

                    // 非本企业的计划任务，根据当前的调度队列判断是否调度
                    if (!(fmainorgid?.ToString()).EqualsIgnoreCase(this.Context.Company))
                    {
                        var taskObject = (DynamicObject)dt.CreateInstance();
                        taskObject["id"] = id;
                        taskObject["fpluginid"] = fpluginid;
                        taskObject["fname"] = fname;
                        taskObject["ftasklabel"] = ftasklabel;
                        taskObject["fworkobject"] = fworkobject;
                        taskObject["ftaskparameter"] = ftaskparameter;

                        var status = jobScheduleService.CheckExists(this.Context, taskObject).Result;
                        if (status)
                        {
                            item["fstartstatus"] = "ew_start003";
                            item["fstartstatus_fenumitem"] = "调度中";
                        }

                    }
                }
            }
        }
    }
}
