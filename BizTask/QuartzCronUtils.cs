//using JieNor.Framework.Interface;
//using JieNor.Framework.IoC;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;
//using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
//using JieNor.Framework.SuperOrm.DataEntity;
//using JieNor.Framework.Interface.BizTask;
//using JieNor.Framework.CustomException;

//namespace JieNor.Framework.AppService.BizTask
//{
//    /// <summary>
//    /// 生成Cron表达式
//    /// </summary>
//    [InjectService]
//    [FormId("bas_executepara")]
//    [OperationNo("execute")]
//    public class QuartzCronUtils: AbstractOperationServicePlugIn
//    {
//        /// <summary>
//        /// 校验 秒，分钟 的值是否正确
//        /// </summary>
//        /// <param name="value"></param>
//        /// <param name="dSym">分割符号</param>
//        /// <param name="msg">提示信息</param>
//        /// <param name="begin">开始时间</param>
//        /// <param name="end">结束时间</param>
//        /// <param name="resultDSym">value为空返回的字符</param>
//        /// <returns></returns>
//        private string ValValueIsRight(string value,string msg,int begin,int end,string resultDSym)
//        {
//            try
//            {
//                if (value.IsNullOrEmptyOrWhiteSpace())
//                {
//                    return resultDSym;
//                }
//                char dSym= CheckDSym(value);
//                string[] second = value.Split(dSym);
//                foreach (var item in second)
//                {
//                    int secondValue = Convert.ToInt32(item);
//                    if (secondValue < begin || secondValue > end)
//                    {
//                        throw new BusinessException("选择{0}，时间只能是 {1} - {2} ".Fmt(msg, begin,end));
//                    }
//                }
//            }
//            catch (Exception ex)
//            {
//                throw new BusinessException("输入了非法字符！");
//            }

//            return value;
//        }

//        /// <summary>
//        /// 获取分隔符号
//        /// </summary>
//        /// <param name="value"></param>
//        /// <returns>返回分隔符号</returns>
//        private char CheckDSym(string value)
//        {
//            char dSym = ' ';
//            if (value.Contains(","))
//            {
//                dSym = ',';
//            }

//            if (value.Contains("-"))
//            {
//                dSym = '-';
//            }

//            if (value.Contains("/"))
//            {
//                dSym = '/';
//            }

//            return dSym;
//        }

//        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
//        {
//            base.AfterExecuteOperationTransaction(e);
//            var fsecond = this.GetQueryOrSimpleParam<string>("fsecond");//秒
//            var fminute = this.GetQueryOrSimpleParam<string>("fminute");//分
//            var fhour = this.GetQueryOrSimpleParam<string>("fhour");//小时
//            var fdays = this.GetQueryOrSimpleParam<string>("fdays");//号
//            var fmonth = this.GetQueryOrSimpleParam<string>("fmonth");//月
//            var fweek = this.GetQueryOrSimpleParam<string>("fweek");//周
//            var fyear = this.GetQueryOrSimpleParam<string>("fyear");//年

//            if (!fdays.IsNullOrEmptyOrWhiteSpace() && !fweek.IsNullOrEmptyOrWhiteSpace())
//            {
//                //提示
//                this.Result.SimpleMessage = "日 和 星期 不能一起使用！";
//                return;
//            }

//            string Cron = "";//表达式

//            string second= ValValueIsRight(fsecond, "秒",0,59,"*");
//            Cron += "{0} ".Fmt(second);
            
//            string minute= ValValueIsRight(fminute,"分",0,59, "*");
//            Cron += "{0} ".Fmt(minute);

//            string hour = ValValueIsRight(fhour, "小时", 0, 23, "*");
//            Cron += "{0} ".Fmt(hour);

//            string days = ValValueIsRight(fdays, "日", 1, 31, "*");
//            Cron += "{0} ".Fmt(days);

//            string month = ValValueIsRight(fmonth, "月", 1, 11, "*");
//            Cron += "{0} ".Fmt(month);

            

//            string week = ValValueIsRight(fweek, "周", 1, 7, "?");
//            if (days=="*" && !week.IsNullOrEmptyOrWhiteSpace())
//            {
//                string[] CronSp= Cron.Split(' ');
//                CronSp[3] = "?";
//                Cron = string.Join(" ", CronSp);
//            }
//            //int newWeek= Convert.ToInt32(week);
//            //if (newWeek==7)
//            //{
//            //    newWeek = 6;
//            //}
//            //else if(newWeek == )
//            //{

//            //}
//            Cron += "{0} ".Fmt(week);

//            if (!fyear.IsNullOrEmptyOrWhiteSpace())
//            {
//                string year = ValValueIsRight(fyear, "年", 0, 9999,"");
//                Cron += "{0} ".Fmt(year);
//            }

//            string daw = "";
//            if (fdays.IsNullOrEmptyOrWhiteSpace())
//            {
//                int weekInt = Convert.ToInt32(week);

//                if (weekInt == 1)
//                {
//                    daw = "周" + (7).ToString();
//                }
//                else
//                {
//                    daw = "周" + (weekInt - 1).ToString();
//                }
//            }
//            else
//            {
//                daw = days+"号";
//            }
//            string translateCron = "{0}年，{1}月，{2}，{3}时，{4}分钟，{5}秒 执行".Fmt(fyear, month, daw, hour, minute, second);
//            var task = this.Container.GetService<ITaskService>();
//            List<string> list = task.GetTaskeFireTime(this.Context, Cron, 20);
//            Dictionary<string, object> dic = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
//            dic.Add("cron", Cron);
//            dic.Add("tasktime", list);
//            dic.Add("translatecron", translateCron);
//            this.Result.SrvData = dic;
//        }

//    }

    

//}
