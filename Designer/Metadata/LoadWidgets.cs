using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.Meta.Designer;
using ServiceStack;

namespace JieNor.Framework.AppService.Designer.Metadata
{
    /// <summary>
    /// 获取所有设计器可用元素
    /// </summary>
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("loadwidgets")]
    public class LoadWidgets : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            e.Cancel = true;

            var allDesignerElementTypes = AssemblyUtils.AssemblyList
                .SelectMany(t => t.ExportedTypes)
                .Where(t => t.FirstAttribute<HtmlDesignerElementAttribute>() != null);
            var allDesignerElMetadata = allDesignerElementTypes
                .Select(t => t.FirstAttribute<HtmlDesignerElementAttribute>().GetMetadata(t))
                .OrderBy(o => o.GetInt("elementType"));

            this.Result.SrvData = allDesignerElMetadata;
            this.Result.IsSuccess = true;
        }
    }
}
