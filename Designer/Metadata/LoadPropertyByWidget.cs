using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using ServiceStack;
using JieNor.Framework.Meta.Designer;

namespace JieNor.Framework.AppService.Designer.Metadata
{
    /// <summary>
    /// 获取指定类型设计元素关联的属性
    /// </summary>
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("loadpropertybywidget")]
    public class LoadPropertyByWidget : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            e.Cancel = true;

            var elType = this.GetQueryOrSimpleParam<int>("elementType");
            var elDesignerType = AssemblyUtils.AssemblyList
                .SelectMany(t => t.ExportedTypes)
                .Where(t => t.FirstAttribute<HtmlDesignerElementAttribute>() != null)
                .FirstOrDefault(t =>
                {
                    var metaAttrs = t.FirstAttribute<HtmlDesignerElementAttribute>().GetMetadata(t);
                    var elementType = metaAttrs.GetInt("elementType");
                    //如果是普通元素对象，取对应元素的属性，如果是表单则取htmlForm的属性
                    return elementType == elType || elType < 50 && elementType == 0;
                });

            if (elDesignerType != null)
            {
                this.Result.SrvData = elDesignerType.GetProperties()
                    .Where(p => p.FirstAttribute<HtmlDesignerElementAttribute>() != null)
                    .Select(p => p.FirstAttribute<HtmlDesignerElementAttribute>().GetMetadata(p))
                    .OrderBy(o => o.GetInt("order"));
            }
            this.Result.IsSuccess = elDesignerType!=null;
        }
    }
}
