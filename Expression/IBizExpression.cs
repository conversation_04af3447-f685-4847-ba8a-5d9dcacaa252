using JieNor.Framework.Enums;
using JieNor.Framework.SuperOrm;
using Microsoft.Scripting.Hosting;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{


    public interface IBizExpression
    {


        /// <summary>
        /// 获取或者设置表达式的标题，可为空。
        /// 用来给此表达式一个简短的业务描述
        /// </summary> 
        LocaleValue Title
        {
            get;
            set;
        }



        /// <summary>
        /// 获取或者设置表达式的真正表达式文本。类似于：
        /// [BillNo.Amount] > 200
        /// </summary> 
        string ExpressionText
        {
            get;
            set;
        }

        /// <summary>
        /// 获取表达式的显示文本。类似于：
        /// [单据头.金额] > 200
        /// </summary>
        string DisplayText
        {
            get;
        }


        /// <summary>
        /// 表达式的代码类型。默认为自动检测
        /// </summary> 
        ExpressionKind CodeKind
        {
            get;
            set;
        }

        /// <summary>
        /// 在编译后会调用此方法缓存编译结果。
        /// </summary>
        CompiledCode CompiledCode
        {
            get;
            set;
        }

        /// <summary>
        /// 设置字段映射。用来将表达式中的字段翻译为显示文本或者真正的目标脚本
        /// <para>
        /// 此方法支持Fluent编程，亦即你可以写出如下的代码：
        /// exp.MapField("name", "名称").MapField("age", "年龄")等等
        /// </para>
        /// </summary>
        /// <param name="fieldName">字段名</param>
        /// <param name="displayText">字段的显示名称</param>
        /// <returns>基于Fluent编程的快速接口</returns>
        IBizExpression MapField(string fieldName, string displayText);


        /// <summary>
        /// 查找出当前表达式包含的字段的集合。
        /// <para>
        /// 【小提示】如果仅仅希望对一个单表达式做查找字段的动作。
        /// 您也可以通过BOSExpression的静态方法：LookupVariables
        /// 来达到同样的效果。
        /// </para>
        /// </summary>
        /// <returns>字段集合</returns>
        Collection<string> LookupVariables();

        /// <summary>
        /// 查找出给定的表达式包含的字段的集合
        /// </summary>
        /// <param name="exp">待查找的表达式</param>
        /// <returns>字段集合</returns>
        Collection<string> LookupVariables(string exp);


    }


}
