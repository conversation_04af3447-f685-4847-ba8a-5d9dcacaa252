namespace JieNor.Framework.Interface
{
    public interface IBizExpressionEvaluator
    {

        /// <summary>
        /// 初始化
        /// </summary> 
        void Initialize();

        /// <summary>
        /// 计算一条表达式，并返回计算的结果
        /// <para>
        /// 注意：此表达式必须是带有返回值得单条表达式项，否则会抛出异常
        /// </para>
        /// </summary>
        /// <param name="expression">待执行的表达式</param>
        /// <param name="context">表达式执行所需的上下文</param>
        /// <returns>表达式计算结果</returns>
        object Eval(IBizExpression expression, IBizExpressionContext context);

        /// <summary>
        /// 计算块状语法
        /// </summary>
        /// <param name="expression"></param>
        /// <param name="context"></param>
        /// <param name="exprReplace">是否校正表达式</param>
        /// <returns></returns>
        object EvalScript(IBizExpression expression, IBizExpressionContext context, bool exprReplace = true);


        /// <summary>
        /// 计算表达式条件
        /// </summary>
        /// <param name="expression"></param>
        /// <param name="context"></param>
        /// <param name="exprReplace">是否校正表达式</param>
        /// <returns></returns>
        bool CheckCondition(string expression, IBizExpressionContext context, bool exprReplace = true);

        /// <summary>
        /// 验证表达式语法
        /// </summary>
        /// <param name="expression">待验证的表达式</param>
        /// <param name="errorMessage">错误信息</param>
        /// <returns>验证结果</returns>
        bool CheckGrammer(IBizExpression expression, out string errorMessage);
        /// <summary>
        /// 验证表达式语法，和少民讨论增加该接口
        /// </summary>
        /// <param name="expression">待验证的表达式</param>
        /// <returns>null:语法校验通过;no null:有语法错误</returns>
        Microsoft.Scripting.SyntaxErrorException CheckGrammer(IBizExpression expression);

        /// <summary>
        /// 获取某个表达式中，出现的名称成员的结果。
        /// </summary>
        /// <param name="expression">要检查的表达式对象。</param>
        /// <returns>返回所有的名称成员，注意，返回的成员不能保证完全是字段，外界需要自行去除。</returns>
        string[] GetNameExpression(IBizExpression expression);

        ///// <summary>
        ///// 获取某个表达式中，出现的名称成员的方法调用的结果。
        ///// </summary>
        ///// <param name="script">要检查的表达式文本。</param>
        ///// <returns>返回所有的名称成员，注意，返回的成员不能保证完全是字段，外界需要自行去除。</returns>
        //Dictionary<string, List<SourceCodeIndex>> GetFunctions(string script);

    }


}
