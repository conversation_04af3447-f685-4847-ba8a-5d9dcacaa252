using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 表达式工厂类
    /// </summary>
    public interface IExpressionFactory
    {
        /// <summary>
        /// 计算表达式
        /// </summary>
        /// <param name="exp"></param>
        /// <param name="ctx"></param>
        /// <returns></returns>
        object Eval(IBizExpression exp, IBizExpressionContext ctx);

        /// <summary>
        /// 计算表达式
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="exp"></param>
        /// <param name="ctx"></param>
        /// <returns></returns>
        T Eval<T>(IBizExpression exp, IBizExpressionContext ctx);

        IBizExpressionEvaluator CreateInstance();

        IBizExpressionEvaluator DefaultEvaluator { get; }
    }
}
