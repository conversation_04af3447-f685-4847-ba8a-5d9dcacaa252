using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.OpData;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 状态变换操作
    /// </summary>
    public abstract class AbstractSetStatus : AbstractOperationService
    {
        /// <summary>
        /// 操作后状态值
        /// </summary>
        protected abstract string StatusValue
        {
            get;
        }

        /// <summary>
        /// 状态字段标识
        /// </summary>
        protected virtual string StatusFieldKey
        {
            get
            {
                return this.OperationContext.HtmlForm?.BizStatusFldKey;
            }
        }

        /// <summary>
        /// 服务初始化
        /// </summary>
        protected override void InitializeService()
        {
            base.InitializeService();
            this.ServiceControlOption.SupportIdemotency = true;
        }
        protected override void InitializeOperationDataEntities(ref DynamicObject[] dataEntities)
        {
            base.InitializeOperationDataEntities(ref dataEntities);

            var prepareService = this.Container.GetService<IPrepareSaveDataService>();
            prepareService?.PrepareDataEntity(this.UserCtx, this.OperationContext.HtmlForm, dataEntities, this.OperationContext.Option);
        }

        /// <summary>
        /// 操作后将状态变化行为返回前端
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void AfterExecute(ref DynamicObject[] dataEntities)
        {
            if (this.OperationContext is BillOperationContext && dataEntities.Length == 1)
            {
                var statusField = this.OperationContext.HtmlForm?.GetField(this.StatusFieldKey);
                if (statusField != null)
                {
                    var dataEntitySet = new ExtendedDataEntitySet();
                    dataEntitySet.Parse(this.OperationContext, dataEntities, this.OperationContext.HtmlForm);
                    var entryRowObjs = dataEntitySet.FindByEntityKey(statusField.EntityKey);
                    foreach (var dataRow in entryRowObjs)
                    {
                        var row = dataRow.RowIndex;
                        if (statusField.Entity is HtmlHeadEntity)
                        {
                            row = -1;
                        }
                        var statusValue = statusField.DynamicProperty.GetValue(dataRow.DataEntity);
                        this.OperationContext.SetValue(this.StatusFieldKey, statusValue, row.ToString());
                    }
                }
            }

            base.AfterExecute(ref dataEntities);
        }

        /// <summary>
        /// 创建详细审计日志对象
        /// </summary>
        /// <param name="htmlForm"></param>
        /// <param name="dataEntity"></param>
        /// <param name="logCategory"></param>
        /// <returns></returns>
        protected override BillAuditDetailObject CreateBillAuditLog(HtmlForm htmlForm, DynamicObject dataEntity, long logCategory)
        {
            var opName = this.GetActualOperationName(htmlForm, dataEntity);
            BillAuditDetailObject auditObj = new BillAuditDetailObject();
            auditObj.Summary = $"本次{opName}引起的详细数据变化如下：";

            var snapShot = dataEntity.GetDataEntitySnapshot();
            if (snapShot.IsNullOrEmpty()) return auditObj;

            var allTraceFieldGroups = htmlForm.GetFieldList().Where(o => o.DisableBT == false)
                .GroupBy(o=>o.EntityKey);

            var dataEntitySet = new ExtendedDataEntitySet();
            dataEntitySet.Parse(this.OperationContext, new DynamicObject[] { dataEntity }, htmlForm);

            foreach (var group in allTraceFieldGroups)
            {
                var dataEntityObjs = dataEntitySet.FindByEntityKey(group.Key);
                //只是表头的快照
                foreach (var kvpItem in snapShot)
                {
                    if (!kvpItem.Value.Changed) continue;
                    var field = group.FirstOrDefault(o => o.PropertyName.EqualsIgnoreCase(kvpItem.Key));
                    if (field.IsNullOrEmpty()) continue;

                    string oldDisplayValue = null, newDisplayValue = null;
                    
                    auditObj.Items.Add(new BillAuditDetailRow()
                    {
                        Id= field.Id,
                        Name=field.Caption,
                        OldValue=Convert.ToString(kvpItem.Value.InitialValue),
                        OldDisplayValue=oldDisplayValue,
                        NewValue=Convert.ToString(kvpItem.Value.LatestValue),
                        NewDisplayValue=newDisplayValue,
                    });
                }
                //遍历表体 获取快照 添加审计日志
                var entrykey = group.Key;
                if (entrykey.EqualsIgnoreCase("fbillhead")) continue;
                //按照实体为准
                if (!dataEntity.DynamicObjectType.Properties.Contains(entrykey)) continue;

                var entrydatas = dataEntity[entrykey] as DynamicObjectCollection;
                foreach (var entry in entrydatas)
                {
                    var row = entry.DynamicObjectType.Properties.Contains("fseq") ? Convert.ToInt32(entry?["fseq"]) : 0;
                    //获取明细快照
                    var entrysnapShots = entry.GetDataEntitySnapshot();
                    if (entrysnapShots.IsNullOrEmpty()) continue;
                    foreach (var entrysnapShot in entrysnapShots)
                    {
                        if (!entrysnapShot.Value.Changed) continue;
                        var field = group.FirstOrDefault(o => o.PropertyName.EqualsIgnoreCase(entrysnapShot.Key));
                        if (field.IsNullOrEmpty()) continue;
                        string oldDisplayValue = null, newDisplayValue = null;

                        auditObj.Items.Add(new BillAuditDetailRow()
                        {
                            Id = field.Id,
                            Row = row,
                            Name = field.Caption,
                            OldValue = Convert.ToString(entrysnapShot.Value.InitialValue),
                            OldDisplayValue = oldDisplayValue,
                            NewValue = Convert.ToString(entrysnapShot.Value.LatestValue),
                            NewDisplayValue = newDisplayValue,
                        });
                    }
                }
            }
            return auditObj;
        }
    }

    /// <summary>
    /// 重构设置状态基类实现方式
    /// </summary>
    public abstract class AbstractSetStatusEx : AbstractOperationService
    {
        
    }
}
