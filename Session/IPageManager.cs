using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.Session
{
    /// <summary>
    /// 页面管理器
    /// </summary>
    public interface IPageManager
    {
        /// <summary>
        /// 获取服务端页面模型
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="pageId"></param>
        /// <returns></returns>
        HtmlServerPage GetPage(UserContext userCtx, string pageId);

        /// <summary>
        /// 新增或更新一个页面对象
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="page"></param>
        /// <returns></returns>
        void AddOrUpdatePage(UserContext userCtx, HtmlServerPage page);

        /// <summary>
        /// 销毁页面对象
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="pageId"></param>
        void Dispose(UserContext userCtx, string pageId);

        /// <summary>
        /// 销毁当前session的所有页面对象
        /// </summary>
        /// <param name="userCtx"></param>
        void DisposeAllBySession(UserContext userCtx);
    }
}
