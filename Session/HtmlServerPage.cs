using JieNor.Framework.MetaCore.FormMeta;
using ServiceStack;
using System;

namespace JieNor.Framework.Interface.Session
{
    /// <summary>
    /// 服务端页面对象
    /// </summary>
    public class HtmlServerPage : ISupportAutoDispose
    {
        public const string IndexPageId = "a45a9361-3cb0-4a25-a63e-06375ffc0610";

        /// <summary>
        /// 构造函数
        /// </summary>
        private HtmlServerPage()
        {
            this.ExpiryIn = new TimeSpan(24, 0, 0);
            this.PkId = "";
        }

        /// <summary>
        /// 创建页面实例
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="hForm"></param>
        /// <param name="domainType"></param>
        /// <param name="openStyle"></param>
        /// <param name="status"></param>
        /// <param name="parentPageId"></param>
        /// <param name="pkId"></param>
        /// <returns></returns>
        public static HtmlServerPage CreatePageInstance(UserContext userCtx, HtmlForm hForm, Enu_DomainType domainType, Enu_OpenStyle openStyle, Enu_BillStatus status, string parentPageId, string pkId = "")
        {
            HtmlServerPage pageInst = new HtmlServerPage();
            pageInst.Id = userCtx.TranId;

            pageInst.PageId = Guid.NewGuid().ToString("n");
            pageInst.ParentPageId = parentPageId;
            pageInst.FormId = hForm.Id;
            pageInst.ViewOpenMode = hForm.ViewOpenMode;
            pageInst.DomainType = domainType;
            pageInst.OpenStyle = openStyle;
            pageInst.SessionId = userCtx.Id;
            pageInst.Status = status;
            pageInst.PkId = pkId;

            if (userCtx.IsTempToken)
            {
                pageInst.SessionId = Guid.NewGuid().ToString("n");
            }
            return pageInst;
        }

        /// <summary>
        /// 过期时间
        /// </summary>
        public TimeSpan? ExpiryIn
        {
            get; set;
        }

        /// <summary>
        /// 标识
        /// </summary>
        public string Id
        {
            get; set;
        }

        /// <summary>
        /// 最后激活时间
        /// </summary>
        public DateTime LastActiveTime
        {
            get; set;
        }

        /// <summary>
        /// 当前页面实例标识
        /// </summary>
        public string PageId { get; set; }

        /// <summary>
        /// 父级页面实例标识
        /// </summary>
        public string ParentPageId { get; set; }

        /// <summary>
        /// 页面状态
        /// </summary>
        public Enu_BillStatus Status { get; set; }

        /// <summary>
        /// 修改时的主键令牌
        /// </summary>
        public string PkId { get; set; }

        /// <summary>
        /// 页面标识
        /// </summary>
        public string FormId { get; set; }

        /// <summary>
        /// 增加session标识，方便批量释放页面实例
        /// </summary>
        public string SessionId { get; set; }

        /// <summary>
        /// 领域类别标识
        /// </summary>
        public Enu_DomainType DomainType { get; set; }

        /// <summary>
        /// 打开模式
        /// </summary>
        public Enu_OpenStyle OpenStyle { get; set; }

        /// <summary>
        /// 视图打开模式：-1-所有类型视图都可重复打开，0-所有类型视图不可重复打开，1-查询视图不可重复打开，2-修改视图不可重复打开，4-新增视图不可重复打开
        /// </summary>
        public int ViewOpenMode { get; set; }

        /// <summary>
        /// 支持持久化
        /// </summary>
        public bool SupportPersist
        {
            get
            {
                return true;
            }
        }

        /// <summary>
        /// 资源释放
        /// </summary>
        public void Dispose()
        {

        }
    }

    /// <summary>
    /// 服务端页面模型扩展方法 
    /// </summary>
    public static class HtmlServerPageExtensions
    {

        /// <summary>
        /// 取得当前页面的session对象
        /// </summary>
        /// <param name="htmlPage"></param>
        /// <returns></returns>
        public static PageSharedStorage GetPageSession(this HtmlServerPage htmlPage)
        {
            AssertUtils.AreNotNull(HostContext.Container);
            var sessionMgr = HostContext.Container.Resolve<IGlobalObjectManager<PageSharedStorage>>();
            if (sessionMgr != null)
            {
                return sessionMgr.ApplyGlobalObject(htmlPage.PageId, "__sessionobject__", true);
            }
            return null;
        }

        /// <summary>
        /// 取得父级页面的session对象
        /// </summary>
        /// <param name="htmlPage"></param>
        /// <returns></returns>
        public static PageSharedStorage GetParentPageSession(this HtmlServerPage htmlPage)
        {
            AssertUtils.AreNotNull(HostContext.Container);
            var sessionMgr = HostContext.Container.Resolve<IGlobalObjectManager<PageSharedStorage>>();
            if (sessionMgr != null)
            {
                return sessionMgr.ApplyGlobalObject(htmlPage.ParentPageId, "__sessionobject__", true);
            }
            return null;
        }

        /// <summary>
        /// 销毁页面session
        /// </summary>
        /// <param name="htmlPage"></param>
        public static void DisposePageSession(this HtmlServerPage htmlPage)
        {
            AssertUtils.AreNotNull(HostContext.Container);
            var sessionMgr = HostContext.Container.Resolve<IGlobalObjectManager<PageSharedStorage>>();
            if (sessionMgr != null)
            {
                PageSharedStorage sessionObj;
                sessionMgr.DisposeGlobalObject(null, htmlPage.PageId, "__sessionobject__", out sessionObj);
            }
        }
    }
}
