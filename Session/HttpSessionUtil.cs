using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface.Cache;
using ServiceStack;
using ServiceStack.Web;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace JieNor.Framework.Interface.Session
{
    /// <summary>
    /// 会话工具类
    /// </summary>
    public static class HttpSessionUtil
    {
        /// <summary>
        /// 生成tokenid
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public static string MakeTokenId(this UserAuthTicket session)
        {
            var container = HostContext.TryResolve<IServiceContainer>().BeginLifetimeScope(Guid.NewGuid().ToString());
            RandomCodeImgParam para = new RandomCodeImgParam();
            para.Count = 1;
            para.Length = 16;
            if (session == null)
            {
                para.Length = 32;
            }
            para.AddLowerLetter = true;
            para.AddUpperLetter = true;

            var svc =  container.GetService<IRandomCode>();
            var rdc = svc.CreateRandomCode(para);
            var tokenId = $"{session.Company}.{session.UserName}".HashString() + rdc[0];
             
            return tokenId;
        }



        /// <summary>
        /// 获取会话标识
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public static string GetXSessionId(this IRequest req)
        {
            var sessionId = "";
            var userToken = req.GetXSessionUserToken();
            if (!userToken.IsNullOrEmptyOrWhiteSpace())
            {
                sessionId = req.GetItemOrCookie(userToken);
            }
            if (sessionId.IsNullOrEmptyOrWhiteSpace())
            {
                sessionId = Guid.NewGuid().ToString("N");
            }
            return sessionId;
        }



        ///// <summary>
        ///// 获取会话缓存标识
        ///// </summary>
        ///// <param name="req"></param>
        ///// <returns></returns>
        //public static string GetXSessionCacheKey(this IRequest req)
        //{
        //    var sid = req.GetXSessionId();
        //    return  req.GetXSessionCacheKey(sid);
        //}

        ///// <summary>
        ///// 获取会话缓存标识
        ///// </summary>
        ///// <param name="req"></param>
        ///// <param name="sessionId"></param>
        ///// <returns></returns>
        //public static string GetXSessionCacheKey(this IRequest req, string sessionId)
        //{
        //    var cid = SessionFeature.GetSessionKey(sessionId);
        //    return cid ?? "#";
        //}

        /// <summary>
        /// 获取当前会话
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public static UserAuthTicket GetXSession(this IRequest req)
        {
            var container = req.TryResolve<IServiceContainer>().BeginLifetimeScope(Guid.NewGuid().ToString());
            var sessionKey = req.GetXSessionId();
            var sessionCache = container.GetService<ISessionCache>();

            return sessionCache?.GetXSession(sessionKey);
        }


        /// <summary>
        /// 获取特定sessin会话（如果出错，会自动重试获取）
        /// </summary>
        /// <param name="sessionCache"></param>
        /// <param name="sessionKey"></param>
        /// <returns></returns>
        public static UserAuthTicket GetXSession(this ISessionCache sessionCache, string sessionKey)
        {
            if (sessionKey == null)
            {
                sessionKey = "";
            }

            //由于会话信息包比较大，在从缓存中获取并反系列化的过程可能超时或出现错误，这里增加重试机制
            int index = 0;
            UserAuthTicket result = null;
            while (index < 10)
            {
                var ok = TryGetSession(sessionCache, sessionKey, ref result);
                if (ok)
                {
                    break;
                }

                index++;
            }

            if (result == null)
            {
                result = new UserAuthTicket();
            }

            return result;
        }

        private static bool TryGetSession(ISessionCache sessionCache, string sessionKey, ref UserAuthTicket result)
        {
            try
            {
                result = sessionCache?.GetTicket(sessionKey);
            }
            catch (Exception ex)
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// 会话id
        /// </summary>
        /// <param name="req"></param>
        /// <param name="sessionId"></param>
        /// <returns></returns>
        public static UserAuthTicket GetXSession(this IRequest req, string sessionId)
        {
            var container = req.TryResolve<IServiceContainer>().BeginLifetimeScope(Guid.NewGuid().ToString());
            //var sessionKey = req.GetXSessionId(sessionId);
            var sessionCache = container.GetService<ISessionCache>();

            return sessionCache?.GetXSession(sessionId);
        }

        /// <summary>
        /// 从当前请求上下文中创建用户上下文
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public static UserContext CreateUserContext(this IRequest request)
        {
            UserContext userCtx = new UserContext();
            var session = SessionFeature.GetOrCreateSession<UserAuthTicket>(null, request, request.Response);
            if (session == null || session.IsAuthenticated == false)
            {
                session = request.GetXSession();
            }
            session.AppId = request.GetHeader("X-AppId");
            session.Device = request.GetHeader("X-Device");
            userCtx.SetUserSession(session);
            userCtx.Container = request.TryResolve<IServiceContainer>().BeginLifetimeScope(Guid.NewGuid().ToString());
            return userCtx;
        }



        static ConcurrentDictionary<string, DateTime> _cacheExpiryTime = new ConcurrentDictionary<string, DateTime>();

        /// <summary>
        /// 延长会话过期时间
        /// </summary>
        /// <param name="req"></param>
        /// <param name="tsExtendTime"></param>
        public static void SlideSessionExpiryTime(this IRequest req, TimeSpan? tsExtendTime)
        {
            if (tsExtendTime.HasValue == false)
            {
                return;
            }

            var sessionKey = req.GetXSessionId();
            if (sessionKey.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }

            if (_cacheExpiryTime.ContainsKey(sessionKey))
            {
                if (_cacheExpiryTime[sessionKey].AddMinutes(5) > DateTime.Now)
                {
                    return;
                }
            }

            var container = req.TryResolve<IServiceContainer>().BeginLifetimeScope(Guid.NewGuid().ToString());
            var sessionCache = container.GetService<ISessionCache>();
            var session = sessionCache?.GetXSession(sessionKey);
            if (session == null || session.IsAuthenticated == false)
            {
                return;
            }

            _cacheExpiryTime[sessionKey] = DateTime.Now;

            TimeSpan tsExpiry = tsExtendTime.Value;
            if (tsExtendTime.Value.Minutes < HostConfigView.Auth.SessionExpiry)
            {
                tsExpiry = TimeSpan.FromMinutes(HostConfigView.Auth.SessionExpiry);
            }

            req.SaveXSession(session, tsExpiry);

            int index = 0;
            while (index < 3)
            {
                try
                {
                    var tokenId = req.GetXSessionUserToken(session.Company, session.UserName);
                    req.Response.SetCookie(tokenId, session.Id, DateTime.Now + tsExpiry, "/");
                    //没出错，直接退出
                    break;
                }
                catch (Exception ex)
                {
                    index++;
                }
            }

        }

        /// <summary>
        /// 获取用户会话标识
        /// </summary>
        /// <param name="req"></param>
        /// <param name="companyId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public static string GetXSessionUserToken(this IRequest req, string companyId = null, string userName = null)
        {
            var userToken = req.GetHeader("X-TokenId");
            if (userToken.IsNullOrEmptyOrWhiteSpace())
            {
                var referer = req?.UrlReferrer?.Query?.TrimStart('?');
                if (referer.IsNullOrEmptyOrWhiteSpace()) return userToken;

                var queryItems = referer.Split('&');
                foreach (var kvpItem in queryItems)
                {
                    var paraItems = kvpItem.Split('=');
                    if (paraItems.Length > 1 && paraItems[0].Trim().ToLower() == "token")
                    {
                        return paraItems[1].Trim();
                    }
                }
            }
            //if (userToken.IsNullOrEmptyOrWhiteSpace()
            //    && !companyId.IsNullOrEmptyOrWhiteSpace()
            //    && !userName.IsNullOrEmptyOrWhiteSpace())
            //{
            //    userToken = $"{companyId}.{userName}".HashString();
            //}
            return userToken;
        }

        /// <summary>
        /// 保存会话信息至redis缓存
        /// </summary>
        /// <param name="req"></param>
        /// <param name="session"></param>
        /// <param name="tsExpiry"></param>
        public static void SaveXSession(this IRequest req, UserAuthTicket session, TimeSpan tsExpiry)
        {
            //保存session至指定缓存中
            if (req == null || session == null)
            {
                return;
            }

            if (session.Id.IsNullOrEmptyOrWhiteSpace() || session.IsAuthenticated == false)
            {
                return;
            }

            // 来自导购小程序，不缓存
            if (session.Device.EqualsIgnoreCase("qywxminiprogram"))
            {
                return;
            }

            if (tsExpiry == TimeSpan.Zero)
            {
                tsExpiry = TimeSpan.FromMinutes(HostConfigView.Auth.SessionExpiry);
            }

            var container = req.TryResolve<IServiceContainer>().BeginLifetimeScope(Guid.NewGuid().ToString());
            var sessionKey = session.Id;// req.GetXSessionCacheKey(session.Id);
            var sessionCache = container.GetService<ISessionCache>();
            session.LastModified = DateTime.UtcNow;

            int index = 0;
            while (index <= 10)
            {
                index++;

                try
                {
                    //检查缓存中的用户信息，是否和当前的用户是否是同一个
                    var oldSession = sessionCache.GetTicket(sessionKey);
                    if (oldSession != null && !oldSession.UserId.EqualsIgnoreCase(session.UserId))
                    {
                        var sb = new StringBuilder();
                        var msg = "新用户信息：{0} {1} {2} ；旧用户信息：{3} {4} {5}".Fmt(session.UserId, session.UserName, session.DisplayName,
                                                                                            oldSession?.UserId, oldSession?.UserName, oldSession?.DisplayName);
                        sb.AppendLine(msg);
                        // 记录当前Cookie、UserAgent及Session数据，便于后续排查
                        sb.AppendLine("SessionKey：" + sessionKey);
                        sb.AppendLine("Header：" + HttpUtility.UrlDecode(req.Headers.ToString()));
                        sb.AppendLine("Cookie：" + req.Cookies.ToJson());
                        sb.AppendLine("AbsoluteUri：" + req.AbsoluteUri);
                        sb.AppendLine("UrlReferrer：" + req.UrlReferrer);
                        sb.AppendLine("UserAgent：" + req.UserAgent);
                        sb.AppendLine("oldSession：" + oldSession?.ToJson());
                        sb.AppendLine("newSession：" + session.ToJson());
                        sb.AppendLine("移除SessionKey：" + sessionKey);
                        sb.ToString().WriteLogToDebugFile("session错乱");

                        //移除redis 对应的key 使其再次登录
                        sessionCache.RemoveTicket(sessionKey);

                        throw HttpError.Unauthorized("操作失败，请重新登录");

                    }
                    //session.Companys = session.Companys.Where(f => f.CompanyId.EqualsIgnoreCase(session.Company)).ToList();
                    sessionCache?.SetTicket(req,sessionKey, session, tsExpiry);

                    break;
                }
                catch (HttpError ex)
                {
                    throw ex;
                }
                catch (Exception ex)
                {
                    // ignored
                }
            }

        }



    }
}
