using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataEntity.BillType;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 单据类型服务接口定义
    /// </summary>
    public interface IBillTypeService
    {
        /// <summary>
        /// 获取业务单据的默认单据类型Id。
        /// 优先取企业自定义的默认单据类型,取不到企业自己的，则取系统预设的默认单据类型
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">业务表单</param>
        /// <returns>默认单据类型Id</returns>
        string GetDefaultBillTypeId(UserContext userCtx, HtmlForm htmlForm);



        /// <summary>
        /// 根据单据类型Id获取参数设置信息
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">业务表单</param>
        /// <param name="billTypeId">单据类型Id</param>
        /// <returns>参数设置表单对应的动态实体对象</returns>
        DynamicObject GetBillTypeParamSet(UserContext userCtx, HtmlForm htmlForm, string billTypeId);



        /// <summary>
        /// 根据单据类型Id获取参数设置信息
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">业务表单</param>
        /// <param name="billTypeIds">单据类型Id集合</param>
        /// <returns>参数设置表单对应的动态实体对象</returns>
        Dictionary<string, DynamicObject> GetBillTypeParamSet(UserContext userCtx, HtmlForm htmlForm, HashSet<string> billTypeIds);



        /// <summary>
        /// 根据单据类型Id获取单据类型动态实体对象（单据类型的完整数据包）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="billTypeId">单据类型Id</param>
        /// <returns>单据类型动态实体对象</returns>
        DynamicObject GetBillTypeById(UserContext userCtx, string billTypeId);


        /// <summary>
        /// 创建单据类型UI数据包，只打包前端需要用到的数据
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="billTypeObj"></param>
        /// <returns></returns>
        JObject CreateBillTypeUIDataObject(UserContext userCtx, DynamicObject billTypeObj);


        /// <summary>
        /// 预置单据类型参数：从单据类型的 fparamset 字段中复制生成，企业注册时预置
        /// </summary>
        /// <param name="userCtx"></param>
        void PresetBillTypeParam(UserContext userCtx);



        /// <summary>
        /// 获取单据类型中某个自定义参数的值
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="userCtx"></param>
        /// <param name="formId">业务表单标识</param>
        /// <param name="billTypeId">单据类型Id</param>
        /// <param name="paramKey">自定义参数标识</param>
        /// <param name="defValue">默认值</param>
        /// <returns></returns>
        T GetBillTypeParam<T>(UserContext userCtx, string formId, string billTypeId, string paramKey, T defValue = default(T));


        DynamicObject GetBillTypeByBizObject(UserContext userCtx, string bizObject, string fid);



        ///// <summary>
        ///// 获取单据类型优先取自建的
        ///// </summary>
        ///// <param name="userCtx"></param>
        ///// <param name="bizObject"></param>
        ///// <param name="fids"></param>
        ///// <returns></returns>
        //List<DynamicObject> GetBillTypesByBizObject(UserContext userCtx, string bizObject, List<string> fids);



        /// <summary>
        /// 根据单据类型Id获取单据类型的相关信息：优先取本组织下的参数，本组织下没有参数时，取总部组织的参数，还取不到的，取系统预设的
        /// </summary>
        /// <param name="userCtx">用户上下文</param> 
        /// <param name="billTypeId">单据类型Id</param>
        /// <returns>参数设置表单对应的动态实体对象</returns>
        BillTypeInfo GetBillTypeInfor(UserContext userCtx,   string billTypeId);



        /// <summary>
        /// 获取业务表单的所有单据类型的相关信息(注意是所有的单据类型，包括禁用的)
        /// </summary>
        /// <param name="userCtx">用户上下文</param> 
        /// <param name="htmlForm">业务表单</param>
        /// <returns>参数设置表单对应的动态实体对象</returns>
        List< BillTypeInfo> GetBillTypeInfors(UserContext userCtx, HtmlForm htmlForm);


        /// <summary>
        /// 获取业务表单的所有单据类型的相关信息(注意是所有的单据类型，包括禁用的)
        /// </summary>
        /// <param name="userCtx">用户上下文</param> 
        /// <param name="formId">业务表单id</param>
        /// <returns>参数设置表单对应的动态实体对象</returns>
        List<BillTypeInfo> GetBillTypeInfors(UserContext userCtx, string  formId);


        /// <summary>
        /// 获取单据类型Id的映射关系：预设的单据类型，用户修改后会变成私有的，在单据保存等判断中，需要把预设的修改私有的，这里返回映射关系
        /// </summary>
        /// <param name="userCtx"></param> 
        /// <param name="billTypeIds"></param>
        /// <returns>key ----- 原单据类型Id，value----对应的私有单据类型Id </returns>
        Dictionary<string ,string > GetBillTypeIdMapInfo(UserContext userCtx,  List<string> billTypeIds);
         



        }
}