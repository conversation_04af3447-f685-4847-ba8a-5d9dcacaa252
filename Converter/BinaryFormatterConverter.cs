using JieNor.Framework.Interface.Converter;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.Converter
{
    [InjectService]
    public class BinaryFormatterConverter : IBinaryFormatterConverter
    {
        public object Deserialize(string base64String)
        {
            byte[] bytes = System.Convert.FromBase64String(base64String);
            return Deserialize(bytes);
        }

        public object Deserialize(byte[] bytes)
        {
            using (MemoryStream stream = new MemoryStream(bytes))
            {
                BinaryFormatter bFormat = new BinaryFormatter();
                return bFormat.Deserialize(stream);
            }
        }

        public T Deserialize<T>(string base64String) where T : ISerializable
        {
            return (T)Deserialize(base64String);
        }

        public T Deserialize<T>(byte[] bytes) where T : ISerializable
        {
            return (T)Deserialize(bytes);
        }

        public byte[] Serialize(object obj)
        {
            if (obj == null)
            {
                return null;
            }
            using (MemoryStream stream = new MemoryStream())
            {
                BinaryFormatter bFormat = new BinaryFormatter();
                bFormat.Serialize(stream, obj);
                return stream.ToArray();
            }
        }

        public string SerializeToBase64String(object obj)
        {
            var bytes = Serialize(obj);
            if (bytes == null)
            {
                return string.Empty;
            }
            return System.Convert.ToBase64String(bytes);
        }
    }
}
