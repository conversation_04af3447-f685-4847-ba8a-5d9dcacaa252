using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 下拉列表数据服务
    /// </summary>
    public interface IComboDataService
    {
        /// <summary>
        /// 获取某个表单模型里面所有下拉列表的下拉选项数据
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="excludeCity"></param>
        Dictionary<string, List<BaseDataSummary>> GetFormComboDatas(UserContext ctx, string formId, bool excludeCity = false);

        /// <summary>
        /// 获取某个表单模型里面某个下拉列表的下拉选项数据
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="fldKey"></param>
        List<BaseDataSummary> GetFormComboDatas(UserContext ctx, string formId,string fldKey);

        /// <summary>
        /// 获取单据类型下拉
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="excludeCity"></param>
        /// <returns></returns>
        Dictionary<string, List<BaseDataSummary>> GetBillTypeComboDatas(UserContext ctx, string formId);
        /// <summary>
        /// 获取辅助资料信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="category"></param>
        /// <param name="filter"></param>
        List<BaseDataSummary> GetComboDatas(UserContext ctx, string category, string filter); 



        /// <summary>
        /// 清除辅助资料缓存
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="category"></param>
        void ClearCache(UserContext ctx, string category);

    }
}