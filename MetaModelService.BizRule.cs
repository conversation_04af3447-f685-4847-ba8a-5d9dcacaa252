using Autofac.Features.Metadata;
using JieNor.Framework.AppService.SystemPlugIn.Auth;
using JieNor.Framework.Comparer;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;

namespace JieNor.Framework.AppService
{
    /// <summary>
    /// 元模型管理服务：业务规则文件解析（rule.json文件）
    /// </summary> 
    public partial  class   MetaModelService 
    {  
         

        static string __formBusinessKey = "formBusiness:";
        static object lockObj = new object();

        /// <summary>
        /// 业务规则
        /// </summary>
        static ConcurrentDictionary<string, JObject> dctFormBusinessCacheDt = new ConcurrentDictionary<string, JObject>(StringComparer.OrdinalIgnoreCase);
        static ConcurrentDictionary<string, string > fileRulesCache = new ConcurrentDictionary<string, string>(StringComparer.OrdinalIgnoreCase);


        /// <summary>
        /// 加载表单业务规则
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="showPara"></param>
        /// <returns></returns>
        public JObject LoadFormBusinessRule(UserContext ctx, FormShowParameter showPara)
        {
            //规则文件里面定义的页面控制规则
            var rule = LoadBizRuleFromFile(ctx, showPara.FormId);
            var rulex = new JObject(rule);
            //加上各个业务表单自定义的动态规则
            List<UIBizCtrlRuleInfo> dyRule = GetDynamicBizCtrlRule(ctx, showPara);
            if (dyRule.Count > 0)
            {
                foreach (var ctrl in dyRule)
                {
                    AddRule(rulex, "lockRules", ctrl.lockRules);
                    AddRule(rulex, "visibleRules", ctrl.visibleRules);
                    AddRule(rulex, "calcRules", ctrl.calcRules);
                }
            }

            return rulex;
        }


        private static void AddRule(JObject rule, string key, List<UICtrlRule> exps)
        {
            JArray rx = rule.GetJsonValue<JArray>(key, new JArray());
            if (rx == null)
            {
                rx = new JArray();
            }
            foreach (var r in exps)
            {
                var jobj = new JObject();
                jobj["id"] = r.id;
                jobj["expression"] = r.expression;
                rx.Add(jobj);
            }
            rule[key] = rx;
        }

        /// <summary>
        /// 获取各个业务单据定义的动态规则
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="showPara"></param>
        /// <returns></returns>
        private static List<UIBizCtrlRuleInfo> GetDynamicBizCtrlRule(UserContext ctx, FormShowParameter showPara)
        {
            List<UIBizCtrlRuleInfo> dyRule = new List<UIBizCtrlRuleInfo>();
            var plugIn = ctx.Container.GetService<IEnumerable<Meta<IDynamicBusinessRule>>>()
                        .Where(o =>
                        {
                            var plugFormId = o.Metadata.GetString("formid").Trim();
                            if (plugFormId == "*" || plugFormId.EqualsIgnoreCase(showPara.FormId))
                            {
                                return true;
                            }
                            var formIds = plugFormId.Split('|');
                            return formIds.Any(f => f.Trim().EqualsIgnoreCase(showPara.FormId));
                        })
                        .ToList();
            if (plugIn != null && plugIn.Any())
            {
                foreach (var svc in plugIn)
                {
                    var ruleX = svc.Value.BuildBusinessRule(ctx, showPara);
                    if (ruleX != null && ruleX.Count > 0)
                    {
                        dyRule.AddRange(ruleX);
                    }
                }
            }

            return dyRule;
        }

        private static  JObject LoadBizRuleFromFile(UserContext ctx, string formId)
        {
            string cacheKey = "{0}{1}{2}".Fmt(__CachePrefix, __formBusinessKey, formId);
            if (dctFormBusinessCacheDt.ContainsKey(cacheKey))
            {
                return dctFormBusinessCacheDt[cacheKey];
            }

            if(fileRulesCache.Count ==0)
            {
                lock (lockObj)
                {
                    var allFiles = PathUtils.SearchWebFiles("/mdl", "*.rule.json".Fmt(formId));
                    foreach (var item in allFiles)
                    {
                        var fileName = Path.GetFileName(item);
                        if (fileRulesCache.ContainsKey(fileName))
                        {
                            FileUtil.WriteTempLog("rule.json", "规则文件重复：" + item);
                            continue;
                        }
                        fileRulesCache.TryAdd(fileName, item);
                    }
                }
            }
             
           JObject businessRule = null;
            var mainConvertFile = "";
            if (fileRulesCache.ContainsKey("{0}.rule.json".Fmt(formId)))
            {
                mainConvertFile = fileRulesCache["{0}.rule.json".Fmt(formId)];
            }

            if (mainConvertFile.IsNullOrEmptyOrWhiteSpace())
            {
                //如果没有定义菜单文件默认加载所属领域的公共菜单
                var formMeta = HtmlParser.LoadFormMetaFromCache(formId, ctx);
                if (!formMeta.BaseModel.IsNullOrEmptyOrWhiteSpace() && formMeta.ElementType == HtmlElementType.HtmlForm_BaseForm
                    && fileRulesCache.ContainsKey("bd.rule.json"))
                {
                    mainConvertFile = fileRulesCache["bd.rule.json"];
                }
                else if (!formMeta.BaseModel.IsNullOrEmptyOrWhiteSpace() && formMeta.ElementType == HtmlElementType.HtmlForm_BillForm
                    && fileRulesCache.ContainsKey("bill.rule.json"))
                {
                    mainConvertFile = fileRulesCache["bill.rule.json"];
                }
            }

            string strWebRootPath = PathUtils.GetStartupPath();

            var currBusinessRuleData = ReadJsonObjectFromFile(mainConvertFile);
            if (currBusinessRuleData == null)
            {
                return new JObject { };
            }

            var businessRuleData = currBusinessRuleData;

            Stack<JObject> stkAllMenuBars = new Stack<JObject>();
            stkAllMenuBars.Push(currBusinessRuleData);

            while (true)
            {
                JToken baseValue;
                if (businessRuleData.TryGetValue("base", out baseValue))
                {
                    var parentMenuFile = Path.Combine(strWebRootPath, baseValue.ToString().Trim().TrimStart('/'));
                    businessRuleData = ReadJsonObjectFromFile(parentMenuFile);
                }

                if (businessRuleData == null) break;

                stkAllMenuBars.Push(businessRuleData);
            }
            var rootMenuObj = stkAllMenuBars.Pop();

            while (stkAllMenuBars.Count > 0)
            {
                var menuObj = stkAllMenuBars.Pop();

                rootMenuObj = rootMenuObj.Merge(menuObj, (srcObj, dstObj, propKey) =>
                {
                    switch (propKey.ToLower())
                    {
                        case "lockrules":
                            return srcObj["id"].ToString().EqualsIgnoreCase(dstObj["id"].ToString());
                        case "visiblerules":
                            return srcObj["id"].ToString().EqualsIgnoreCase(dstObj["id"].ToString());
                    }
                    return false;
                });
            }

            businessRule = rootMenuObj;
            if (businessRule != null && !dctFormBusinessCacheDt.ContainsKey(cacheKey))
            {
                dctFormBusinessCacheDt[cacheKey] = businessRule;
            }

            return businessRule ?? new JObject();
        }







    }
}
