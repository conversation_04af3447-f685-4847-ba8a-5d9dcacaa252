using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.Consts;
using System.Data;
using Newtonsoft.Json.Linq;

namespace JieNor.Framework.AppService.EwService
{
    [InjectService("Tasktmpl")]
    public class OpenTmpl : AbstractOperationService
    {
        protected override string OperationName => null;

        protected override string PermItem => null;

        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var metaTemp = this.Container.GetService<IMetaModelService>().LoadFormModel(this.UserCtx, "sys_setupcontenttemp");
            var metaModel = this.Container.GetService<IMetaModelService>().LoadFormModel(this.UserCtx, "sys_setupcontent");
            DynamicObject dyTemp = metaTemp.GetDynamicObjectType(this.UserCtx).CreateInstance() as DynamicObject;
            dyTemp["fworkobject"] = this.OperationContext.HtmlForm.Id;
            DynamicObjectCollection entitys = dyTemp["fentity"] as DynamicObjectCollection;
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserCtx, metaModel.GetDynamicObjectType(this.UserCtx));

            string strSql = @"SELECT a.fid,fformid,fnumber,fname,fworkobject,fentryid,fcontent FROM T_SYS_SetUpContent a
                             INNER JOIN T_SYS_SetUpContentEntry b ON b.fid = a.fid
                             WHERE fworkobject=@formid ";
            List<SqlParam> paras = new List<SqlParam>()
                {
                    new SqlParam("@formid",DbType.String,this.OperationContext.HtmlForm.Id)
                };
            DynamicObjectCollection dyList = this.DBService.ExecuteDynamicObject(this.UserCtx, strSql, paras);
                if (!dyList.IsNullOrEmptyOrWhiteSpace() && dyList.Count > 0)
                {
                    foreach (DynamicObject dy in dyList)
                    {
                        if (dy["fcontent"].IsNullOrEmptyOrWhiteSpace())
                        {
                            continue;
                        }
                        DynamicObject entity = entitys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                        JObject jObj = JObject.Parse(dy["fcontent"].ToString());
                        entity["fopname"] = jObj["opName"]["id"].ToString() + ":" + jObj["opName"]["fname"].ToString();
                        entity["fop"] = jObj["opName"]["fname"].ToString();
                        entity["fcontent"] = jObj["content"].ToString();
                        entity["frowid"] = dy["fentryid"];
                        entity["fentityformid"] = jObj["fentityformid"];
                        entitys.Add(entity);
                    }
                }
            string parentId = this.OperationContext.PageId.IsNullOrEmptyOrWhiteSpace() ? Guid.NewGuid().ToString() : this.OperationContext.PageId;
            var action = this.UserCtx.ShowSpecialForm(metaTemp, dyTemp, false, parentId,
                Enu_OpenStyle.Modal,
                Enu_DomainType.Dynamic,
                null,
                null,
                "");
            this.OperationContext.Result.HtmlActions.Add(action);

        }
    }
}
