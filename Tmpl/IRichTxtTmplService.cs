using JieNor.Framework.DataEntity.Tmpl;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 解析自定义配置模板
    /// </summary>
    public interface IRichTxtTmplService
    {
        /// <summary>
        /// 获取多个单据实体的解析内容
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId">单据模型id</param>
        /// <param name="billDatas">单据内容</param>
        /// <returns></returns>
        List<RichTxtTmplInfo> GetRichTxtTmplList(UserContext ctx, string formId, IEnumerable<DynamicObject> billDatas, string enFormId = "", int dataType = 1);
        /// <summary>
        /// 获取单个单据实体的解析内容
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId">单据模型id</param>
        /// <param name="billData">单据内容</param>
        /// <returns></returns>
        RichTxtTmplInfo GetRichTxtTmpl(UserContext ctx, string formId, DynamicObject billData);
        /// <summary>
        /// 获取某个操作的解析内容
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="operationNo">操作码</param>
        /// <param name="info">单据实体的解析内容</param>
        /// <param name="operationName">操作名称</param>
        /// <returns></returns>
        RichTxt GetRichTxt(UserContext ctx, string formId, string operationNo, RichTxtTmplInfo info, string operationName = "");
        /// <summary>
        /// 获取某个操作的解析内容
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="operationNo">操作码</param>
        /// <param name="info">单据实体的解析内容</param>
        /// <param name="operationName">操作名称</param>
        /// <returns></returns>
        RichTxt GetRichTxt(UserContext ctx, string operationNo, RichTxtTmplInfo info, string operationName = "");
    }
}
