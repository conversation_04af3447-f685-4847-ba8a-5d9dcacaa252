using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json.Linq;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject.Message;
using JieNor.Framework.MetaCore;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface.BizWarn;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.Framework.AppService.ServiceInst
{
    /// <summary>
    /// 发消息服务
    /// </summary>
    [InjectService("sendmsg")]
    [ServiceMetaAttribute("name", "发消息服务")]
    [ServiceMetaAttribute("serviceid", HtmlElementType.HtmlBizService_SendMessage)]
    public class SendMessageService : AbstractBaseService
    {
        /// <summary>
        /// 参数配置页面
        /// </summary>
        public override string ParamFormId { get { return "sys_msgsereditor"; } }

        /// <summary>
        /// 服务参数
        /// </summary>
        public string ServicePara { get; set; }

        /// <summary>
        /// 服务初始化通知
        /// </summary>
        /// <param name="servicePara">服务参数</param>
        protected override void OnServiceInitialized(string servicePara)
        {
            this.ServicePara = servicePara;
        }

        /// <summary>
        /// 服务执行逻辑
        /// </summary>
        /// <param name="dataEntities">数据包</param>
        public override void ExecuteService(ref DynamicObject[] dataEntities)
        {
            //数据包是否有数据
            if (dataEntities == null || !dataEntities.Any()) return;

            //服务参数
            if (this.ServicePara.IsNullOrEmptyOrWhiteSpace()) return;
            JObject joServicePara = JObject.Parse(this.ServicePara);
            if (joServicePara == null) return;

            //服务前置条件
            string strCondition = Convert.ToString(joServicePara["condition"]);

            //服务配置
            var jsonSerConfig = Convert.ToString(joServicePara["serConfig"]);
            if (jsonSerConfig.IsNullOrEmptyOrWhiteSpace()) return;
            var joConfig = JObject.Parse(jsonSerConfig);
            if (joConfig == null) return;
            var bizFormId = joConfig["fbizobject"]?["id"]?.ToString();
            var param = new SendMessageParam();
            param.MsgType = joConfig["fmsgtype"]?["id"]?.ToString();
            param.MsgTitle = joConfig["fmsgtitle"]?.ToString();
            param.MsgContent = joConfig["fmsgcontent"]?.ToString();
            param.MsgLinkUrl = joConfig["flinkurl"]?.ToString();
            param.SendObjStaff = joConfig["fsendobjyg"]?["id"]?.ToString();
            param.SendObjRole = joConfig["fsendobjjs"]?["id"]?.ToString();
            param.SendObjVar = joConfig["fsendobjbl"]?["id"]?.ToString();
            if (bizFormId.IsNullOrEmptyOrWhiteSpace() 
                || param.MsgTitle.IsNullOrEmptyOrWhiteSpace()
                || (param.SendObjStaff.IsNullOrEmptyOrWhiteSpace()
                    && param.SendObjRole.IsNullOrEmptyOrWhiteSpace()
                    && param.SendObjVar.IsNullOrEmptyOrWhiteSpace()))
            {
                return;
            }

            //业务对象模型
            var bizForm = this.MetaModelService.LoadFormModel(this.Context, bizFormId);
            if (bizForm == null) return;

            //表达式引擎
            var bizExpCtx = this.Container.GetService<IBizExpressionContext>();
            bizExpCtx.Context = this.Context;
            bizExpCtx.HtmlForm = bizForm;

            //BizDynamicDataRow dcRow = new BizDynamicDataRow(bizForm);
            //bizExpCtx.BindSetField(new TrySetValueHandler(dcRow.TrySetMember));
            //bizExpCtx.BindGetField(new TryGetValueHandler(dcRow.TryGetMember));
            //var evaluator = this.Container.GetService<IBizExpressionEvaluator>();

            var bizPkIds = this.QueryBizObjPkIdsByCondition(bizForm, dataEntities, strCondition);

            var bizWarnService = this.Container.GetService<IBusinessWarnService>();
            foreach (var dataEntitie in dataEntities)
            {
                ////如果存在前置条件
                //if (!strCondition.IsNullOrEmptyOrWhiteSpace())
                //{
                //    bizExpCtx.BizData = dataEntitie;
                //    dcRow.ActiveDataObject = dataEntitie;
                //    //执行表达式条件，如果条件不成立，则终止后续逻辑
                //    var checkResult = evaluator.CheckCondition(strCondition, bizExpCtx);
                //    if (!checkResult) continue;
                //}

                if (!bizPkIds.Contains(Convert.ToString(dataEntitie["id"]))) continue;
                try
                {
                    var result = bizWarnService.SendMessage(this.Context, bizForm, dataEntitie, param);
                    result?.ThrowIfHasError(true, $"发送消息失败！");
                }
                catch (Exception ex)
                {
                    var logEx = this.Container.GetService<ILogServiceEx>();
                    logEx?.Error($"生成操作消息时出现未知错误：{ex.Message}，业务表单：{bizForm.Caption} - {bizForm.Id}，stacktrace：{ex.StackTrace}");
                }
            }
        }

        /// <summary>
        /// 根据当前数据包主键Id以及过滤条件，获取符合条件的数据包主键Id
        /// </summary>
        /// <param name="bizForm"></param>
        /// <param name="dataEntities"></param>
        /// <param name="condition"></param>
        /// <returns></returns>
        private List<string> QueryBizObjPkIdsByCondition(HtmlForm bizForm, DynamicObject[] dataEntities, string condition)
        {
            var currentPkids = dataEntities.Select(o => o["id"] as string).ToList();
            if (condition.IsNullOrEmptyOrWhiteSpace())
            {
                return currentPkids;
            }
            var bizPkIds = new List<string>();
            try
            {
                var filterList = condition.FromJson<List<FilterRowObject>>();
                if (filterList == null) return currentPkids;
                filterList = filterList.Where(o => !o.Id.IsNullOrEmptyOrWhiteSpace() && !o.Operator.IsNullOrEmptyOrWhiteSpace()).ToList();
                if (filterList.Count <= 0) return currentPkids;

                SqlBuilderParameter queryParam = new SqlBuilderParameter(this.Context, bizForm.Id);
                queryParam.PageIndex = -1;
                queryParam.PageCount = -1;
                queryParam.SelectedFieldKeys.Add(bizForm.BillPKFldName);
                queryParam.SetFilter(filterList);
                if (currentPkids.Count == 1)
                {
                    queryParam.FilterString = $"fid='{currentPkids[0]}'";
                }
                else
                {
                    queryParam.FilterString = $"fid in('{string.Join("','", currentPkids)}')";
                }
                var queryObj = QueryService.BuilQueryObject(queryParam);
                var sqlText = $@"{queryObj.SqlSelect} {queryObj.SqlFrom} {queryObj.SqlWhere}";

                using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, queryParam.DynamicParams))
                {
                    while (reader.Read())
                    {
                        var pkId = Convert.ToString(reader["fbillhead_id"]);
                        if (!pkId.IsNullOrEmptyOrWhiteSpace() && !bizPkIds.Contains(pkId))
                        {
                            bizPkIds.Add(pkId);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var logEx = this.Container.GetService<ILogServiceEx>();
                logEx.Error(ex);
            }

            return bizPkIds;
        }
    }
}