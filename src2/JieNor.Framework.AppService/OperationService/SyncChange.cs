using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using JieNor.Framework.SuperOrm.Serialization;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.CustomException;
using JieNor.Framework.IoC;
using JieNor.Framework.Consts;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 协同变更
    /// </summary>
    [InjectService("SyncChange")]
    public class SyncChange : AbstractOperationService
    {
        protected override string OperationName
        {
            get
            {
                return "协同变更";
            }
        }

        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_SyncChange;
            }
        }


        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var changeData = this.GetQueryOrSimpleParam<string>("changeData");

            if (string.IsNullOrWhiteSpace(changeData))
            {
                throw new BusinessException("参数changeData不能为空!");
            }

            var changeReason = this.GetQueryOrSimpleParam<string>("changeReason");
            if (string.IsNullOrWhiteSpace(changeReason))
            {
                throw new BusinessException("变更理由不能为空!");
            }

            var changeArray = JArray.Parse(changeData);
            var unPackUpdateLocalIdService = this.Container.GetService<IUnPackUpdateLocalIdService>();
            var htmlForm = this.OperationContext.HtmlForm;

            changeArray = unPackUpdateLocalIdService.BindData(this.UserCtx, htmlForm, changeArray);

            if (changeArray == null || changeArray.Count <= 0)
            {
                throw new BusinessException("参数changeData解析失败!");
            }

            //触发协同变更数据解析后事件：委托给业务插件处理，可以在业务插件中可以覆盖协同变更数据
            OnCustomServiceEventArgs e = new OnCustomServiceEventArgs()
            {
                EventName = "onAfterParseSyncChangeData",
                EventData = Tuple.Create(this.OperationContext.OperationNo, changeArray)
            };
            this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", e);

            var idList = new List<string>();
            var changeArrayTmp = changeArray;
            changeArray = new JArray();

            foreach (var changeItem in changeArrayTmp)
            {
                var changeObject = changeItem as JObject;
                var idProperty = changeObject.Properties().FirstOrDefault(x => string.Equals("id", x.Name, StringComparison.OrdinalIgnoreCase));

                if (idProperty == null)
                {
                    continue;
                }

                var id = (string)(idProperty.Value);
                if (string.IsNullOrWhiteSpace(id))
                {
                    continue;
                }

                idList.Add(id);
                changeArray.Add(changeObject);
                changeObject.Remove("fpublishcid");
                changeObject.Remove("fpublishcid_pid");
                changeObject.Remove("fdataorigin");
            }

            if (idList.Count <= 0)
            {
                throw new BusinessException("没有解析出本地数据");
            }

            var dt = htmlForm.GetDynamicObjectType(this.UserCtx);
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserCtx, dt);
            var dynamicObjects = dm.Select(idList).OfType<DynamicObject>().ToList();

            if (dynamicObjects == null || dynamicObjects.Count < 0)
            {
                throw new BusinessException("解析出错或本地数据已删除");
            }

            var option = new Dictionary<string, object>
            {
                { "originalOpNo","SyncChange"}
            };
            var result = this.Gateway.InvokeBillOperation(this.UserCtx, htmlForm.Id, dynamicObjects, "change", option);
            result?.ThrowIfHasError(true, $"保存快照失败！");

            var dcSerializer = this.Container.GetService<IDynamicSerializer>();
            dcSerializer.Sync(dt, dynamicObjects, changeArray, (propKey) =>
            {
                var el = htmlForm?.GetElement(propKey);
                if (el is HtmlField) return (el as HtmlField).DynamicProperty;
                if (el is HtmlEntryEntity) return (el as HtmlEntryEntity).DynamicProperty;

                return null;
            },
            null,
            null,
            null);

            //触发协同变更数据保存前事件：委托给业务插件处理，可以在业务插件中可以覆盖协同变更数据
            OnCustomServiceEventArgs ea = new OnCustomServiceEventArgs()
            {
                EventName = "onBeforeSaveSyncChangeData",
                EventData = Tuple.Create(this.OperationContext.OperationNo, dynamicObjects)
            };
            this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", ea);

            var unitConvertService = this.Container.GetService<IUnitConvertService>();
            unitConvertService.ConvertByBasQty(this.UserCtx, htmlForm, dynamicObjects, OperateOption.Create());

            result = this.Gateway.InvokeBillOperation(this.UserCtx, htmlForm.Id, dynamicObjects, "save", option);
            result?.ThrowIfHasError(true, "保存变更后数据失败!");
            result = this.Gateway.InvokeBillOperation(this.UserCtx, htmlForm.Id, dynamicObjects, "submitchange", option);
            result?.ThrowIfHasError(true, "提交变更后数据失败!");
            option.Add("execOpinion", changeReason);
            result = this.Gateway.InvokeBillOperation(this.UserCtx, htmlForm.Id, dynamicObjects, "AuditFlow", option);
            result?.ThrowIfHasError(true, "审核变更后数据失败!");
        }
    }
}
