using System;
using System.Collections.Generic;
using JieNor.Framework.Consts;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.Enums;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.Interface.FormService;
using System.Linq;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 禁用 操作
    /// </summary>
    [InjectService("Forbid")]
    public class Forbid : AbstractSetStatus
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName
        {
            get
            {
                return "禁用";
            }
        }

        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_Forbid;
            }
        }

        /// <summary>
        /// 是否自动保存数据
        /// </summary>
        protected override bool AutoSaveData
        {
            get
            {
                return true;
            }
        }

        /// <summary>
        /// 状态值
        /// </summary>
        protected override string StatusValue
        {
            get
            {
                return "Y";
            }
        }

        /// <summary>
        /// 状态字段标识
        /// </summary>
        protected override string StatusFieldKey
        {
            get
            {
                return this.OperationContext.HtmlForm?.ForbidStatusFldKey;
            }
        }

        /// <summary>
        /// 操作执行过程
        /// </summary>
        /// <param name="dataEntities">数据包</param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null) return;

            foreach (var dataEntity in dataEntities)
            {
                //禁用状态
                if (dataEntity.DynamicObjectType.Properties.Contains("fforbidstatus"))
                {
                    dataEntity.SetValue("fforbidstatus", "1");
                }

                //禁用人ID
                if (dataEntity.DynamicObjectType.Properties.Contains("fforbidid"))
                {
                    dataEntity.SetValue("fforbidid", this.OperationContext.UserContext.UserId);
                }

                //禁用时间
                if (dataEntity.DynamicObjectType.Properties.Contains("fforbiddate"))
                {
                    dataEntity.SetValue("fforbiddate", DateTime.Now);
                }
            }

            this.OperationContext.Result.IsSuccess = true;
        }

        /// <summary>
        /// 处理禁用操作通用校验规则
        /// </summary>
        /// <returns></returns>
        protected override List<IDataValidRule> PrepareValidationRules()
        {
            var rules = base.PrepareValidationRules();

            var errorMessage = string.Empty;
            rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return ValiStatus(newData, oldData, out errorMessage);
            }).WithMessage("{0}", (dyObj, key) => errorMessage));

            var mustRule = this.Container.GetValidRuleService(HtmlElementType.HtmlValidator_MustInput);
            if (mustRule != null)
            {
                mustRule.Initialize(this.OperationContext.UserContext, "Forbid");
                rules.Add(mustRule);
            }

            return rules;
        }

        protected override void PrepareBusinessService(List<FormServiceDesc> lstOpServices)
        {
            base.PrepareBusinessService(lstOpServices);
            var formServiceLoaders = this.Container.GetService<IEnumerable<IFormServiceLoader>>();
            if (formServiceLoaders != null && formServiceLoaders.Any())
            {
                foreach (var formServiceLoader in formServiceLoaders)
                {
                    var syncServiceInst = formServiceLoader.CreateSyncService(this.UserCtx, this.OperationContext.HtmlForm, Enu_SyncTimePoint.SyncWhenForbid, this.OperationContext.Option);
                    if (syncServiceInst != null)
                    {
                        lstOpServices.AddRange(syncServiceInst);
                    }
                }
            }
        }

        /// <summary>
        /// 状态校验
        /// </summary>
        /// <param name="newData"></param>
        /// <param name="oldData"></param>
        /// <param name="errorMessage"></param>
        /// <returns></returns>
        private bool ValiStatus(DynamicObject newData, DynamicObject oldData, out string errorMessage)
        {
            bool bPassed = true;
            HtmlForm formInfo = this.OperationContext.HtmlForm;
            var forbidFld = formInfo.GetField(formInfo.ForbidStatusFldKey);
            var statusFld = formInfo.GetField(formInfo.BizStatusFldKey);
            var statusVal = statusFld?.DynamicProperty.GetValue<string>(newData);
            var numberFld = formInfo.GetField(formInfo.NumberFldKey);
            var numberVal = numberFld.DynamicProperty.GetValue<string>(newData);

            errorMessage = $"{numberFld.Caption}为【{numberVal}】的{formInfo.Caption} 不是创建/重新审核状态 或 已禁用，不允许禁用！";

            //未禁用
            bPassed = bPassed &&
                (forbidFld == null || forbidFld != null && !forbidFld.DynamicProperty.GetValue<bool>(newData));

            //状态=创建/重新审核
            bPassed = bPassed &&
                (statusFld == null || statusFld != null
                    && (statusVal.EqualsIgnoreCase(BillStatus.B.ToString()) || statusVal.EqualsIgnoreCase(BillStatus.C.ToString())));

            return bPassed;
        }
    }
}