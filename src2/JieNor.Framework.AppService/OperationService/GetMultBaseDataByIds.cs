using JieNor.Framework.Consts;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 根据基础资料ID获取基础资料信息，多个ID之间用逗号隔开，比如：abc,acd,bed
    /// </summary>
    [InjectService("getmultbasedatabyids")]
    public class GetMultBaseDataByIds : AbstractOperationService
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName
        {
            get
            {
                return "";
            }
        }

        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_View;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var strIds = this.GetQueryOrSimpleParam<string>("ids");
            if (strIds.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentException("基础资料ID为空！");
            }

            string[] ids = strIds.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (ids.Length <= 0)
            {
                throw new ArgumentException("基础资料ID为空！");
            }

            var fieldKey = this.GetQueryOrSimpleParam<string>("fieldKey");
            var baseField = this.OperationContext.HtmlForm?.GetField(fieldKey ?? "") as HtmlMulBaseDataField;
            if (baseField == null)
            {
                throw new BusinessException("不明确的基础资料字段标识，无法查找明细信息！");
            }

            HtmlForm refFieldForm = baseField.RefHtmlForm(this.OperationContext.UserContext);
            var nameField = refFieldForm.GetNameField();
            var numberField = refFieldForm.GetNumberField();

            List<string> returnIds = new List<string>();
            List<string> returnNames = new List<string>();
            List<string> returnNumbers = new List<string>();

            var dm = this.GetDataManager();
            dm.InitDbContext(this.UserCtx, refFieldForm.GetDynamicObjectType(this.OperationContext.UserContext));
            
            var baseDataList = dm.Select(ids);
            if (baseDataList != null)
            {
                foreach (DynamicObject baseData in baseDataList)
                {
                    returnIds.Add(baseData["id"] as string ?? "");
                    returnNames.Add(nameField?.DynamicProperty.GetValue(baseData) as string ?? "");
                    returnNumbers.Add(numberField?.DynamicProperty.GetValue(baseData) as string ?? "");
                }
            }

            this.OperationContext.Result.SrvData = new
            {
                id = string.Join(",", returnIds),
                name = string.Join(",", returnNames),
                number = string.Join(",", returnNumbers)
            };
        }
    }
}