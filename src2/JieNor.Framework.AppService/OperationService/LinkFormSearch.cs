using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
using JieNor.Framework.Consts;
using JieNor.Framework.Utils;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.Interface.QueryBuilder;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 上下表单联查
    /// </summary>
    [InjectService("LinkFormSearch")]
    public class LinkFormSearch : AbstractOperationService
    {
        protected override string OperationName
        {
            get
            {
                return "联查查询";
            }
        }

        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_View;
            }
        }

        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            var htmlForm = this.OperationContext.HtmlForm;
            var metaService = this.Container.GetService<IMetaModelService>();

            var results = new List<Dictionary<string, object>>();

            dealPreForm(metaService, htmlForm, dataEntities, results);

            dealNextForm(metaService, htmlForm, dataEntities, results);

            var dealLinkFormEventData = new OnCustomServiceEventArgs()
            {
                EventName = "dealLinkForm",
                DataEntities = dataEntities,
                EventData = new Dictionary<string, object>
                {
                    { "linkFormDatas",results},
                    { "userContext",this.UserCtx},
                }
            };

            this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", dealLinkFormEventData);

            results = filterByVisible(results);

            numericalStatement(results);

            this.OperationContext.Result.SrvData = results;
        }

        private List<Dictionary<string, object>> filterByVisible(List<Dictionary<string, object>> datas)
        {
            if (datas == null || datas.Count <= 0)
            {
                return datas;
            }

            if ((this.UserCtx.ClientType & (long)Enu_ClientType.Mobile) == (long)Enu_ClientType.Mobile)
            {
                datas = datas.Where(x =>
                  {
                      if (false == x.Keys.Contains("visible"))
                      {
                          return false;
                      }
                      return Convert.ToInt32(x["visible"]) > 0;
                  }).ToList();
            }

            return datas;
        }

        private void numericalStatement(List<Dictionary<string, object>> datas)
        {
            if (datas == null || datas.Count <= 0)
            {
                return;
            }

            var sqls = new List<string>();
            var sqlParams = new List<SqlParam>();
            foreach (var data in datas)
            {
                var formId = Convert.ToString(data["formId"]);
                var filterString = Convert.ToString(data["filterString"]);
                var flag = Convert.ToString(data["flag"]);
                var para = new SqlBuilderParameter(this.UserCtx, formId);
                para.PageIndex = -1;
                para.PageCount = -1;
                para.SrcFormId = this.OperationContext.HtmlForm.Id;
                para.SelectedFieldKeys.Add("fbillhead_id");

                if (!filterString.IsNullOrEmptyOrWhiteSpace())
                {
                    para.FilterString = string.IsNullOrWhiteSpace(para.FilterString) ? " ( {0} ) ".Fmt(filterString) : para.FilterString.JoinFilterString(filterString);
                }

                var queryObject = QueryService.BuilQueryObject(para);

                var sqlText = $@"
select '{formId}' as fbillformid,'{flag}'as fflag,count(distinct fbillhead_id) as fqty from (
{queryObject.SqlSelect} {queryObject.SqlFrom} {queryObject.SqlWhere}
) t";

                sqls.Add(sqlText);

                foreach (var param in para.DynamicParams)
                {
                    if (sqlParams.Any(x => x.Name.EqualsIgnoreCase(param.Name)))
                    {
                        continue;
                    }
                    sqlParams.Add(param);
                }
            }

            var sql = string.Join(" union all ", sqls);
            var dbService = this.Container.GetService<IDBService>();
            var dbResults = new List<Dictionary<string, object>>();

            using (var dataReader = dbService.ExecuteReader(this.UserCtx, sql, sqlParams))
            {
                while (dataReader.Read())
                {
                    dbResults.Add(new Dictionary<string, object>
                    {
                        { "formId",Convert.ToString(dataReader["fbillformid"])},
                        { "count",Convert.ToInt32(dataReader["fqty"])},
                        { "flag",Convert.ToString(dataReader["fflag"])}
                    });
                }
            }

            foreach(var data in datas)
            {
                var flag = Convert.ToString(data["flag"]);
                var formId = Convert.ToString(data["formId"]);
                var dbResult = dbResults.FirstOrDefault(x => Convert.ToString(x["formId"]) == formId && flag == Convert.ToString(x["flag"]));
                data["count"] = dbResult == null ? 0 : Convert.ToInt32(dbResult["count"]);
            }
        }


        private void dealPreForm(IMetaModelService metaService, HtmlForm htmlForm, DynamicObject[] dataEntities, List<Dictionary<string, object>> results)
        {
            var preConvertRules = metaService.LoadConvertRuleByTargetFormId(this.UserCtx, htmlForm.Id);
            if (preConvertRules == null || false == preConvertRules.Any())
            {
                return;
            }

            var preGroups = preConvertRules.GroupBy(x => x.SourceFormId);
            ExtendedDataEntitySet dataEntitySet = new ExtendedDataEntitySet();
            dataEntitySet.Parse(this.UserCtx, dataEntities, htmlForm);

            foreach (var preGroup in preGroups)
            {
                var preForm = metaService.LoadFormModel(this.UserCtx, preGroup.Key);
                var preInfos = dealForm(preGroup, preForm, htmlForm);
                if (preInfos == null || preInfos.Count <= 0)
                {
                    continue;
                }

                var visible = Convert.ToInt32(preInfos.First()["visible"]);
                var preInfoGroups = preInfos.GroupBy(x => x["sourceFieldKey"]);
                var filterStringList = new List<string>();

                foreach (var preInfoGroup in preInfoGroups)
                {
                    var keyInfo = preInfoGroup.FirstOrDefault()?["sourceFieldKeyType"];
                    if (string.IsNullOrWhiteSpace(keyInfo))
                    {
                        continue;
                    }

                    if (keyInfo.EqualsIgnoreCase("id"))
                    {
                        keyInfo = "fid";
                    }
                    else if (keyInfo.EqualsIgnoreCase(preForm.NumberFldKey))
                    {
                        keyInfo = preForm.NumberFldKey;
                    }
                    else
                    {
                        keyInfo = preForm.GetEntity(keyInfo).PkFieldName;
                    }


                    var field = htmlForm.GetField(preInfoGroup.Key);
                    var formFields = preInfoGroup.Select(x => x["formFieldKey"])
                                                 .Where(x => false == string.IsNullOrWhiteSpace(x))
                                                 .Distinct()
                                                 .Select(x => htmlForm.GetField(x))
                                                 .ToList();

                    var sourceEntities = dataEntitySet.FindByEntityKey(field.EntityKey);
                    var valueInfos = sourceEntities.Where(x=> 
                    {
                        if (formFields == null || formFields.Count <= 0)
                        {
                            if (keyInfo.EqualsIgnoreCase(preForm.NumberFldKey))
                            {
                                return false;
                            }
                            return true;
                        }

                        foreach(var formField in formFields)
                        {
                            var formId = formField.DynamicProperty.GetValue<string>(x.DataEntity);
                            if (formId.EqualsIgnoreCase(preForm.Id))
                            {
                                return true;
                            }
                        }
                        return false;
                    })
                    .Select(x => field.DynamicProperty.GetValue<string>(x.DataEntity))
                    .Where(x => false == string.IsNullOrWhiteSpace(x))
                    .Distinct()
                    .ToList();

                    if (valueInfos == null || valueInfos.Count <= 0)
                    {
                        continue;
                    }

                    var filterStringBuilder = new StringBuilder();

                    if (valueInfos.Count == 1)
                    {
                        filterStringBuilder.AppendFormat(" {0} = '{1}' ", keyInfo, valueInfos[0]);
                    }
                    else
                    {
                        filterStringBuilder.AppendFormat(" {0} in ( ", keyInfo);
                        filterStringBuilder.Append(string.Join(",", valueInfos.Select(x => $"'{x}'")));
                        filterStringBuilder.Append(" ) ");
                    }

                    filterStringList.Add(filterStringBuilder.ToString());
                }

                addResultItem(filterStringList, preForm, "preForm", results, visible);
            }
        }

        private void dealNextForm(IMetaModelService metaService, HtmlForm htmlForm, DynamicObject[] dataEntities, List<Dictionary<string, object>> results)
        {
            var nextConvertRules = metaService.LoadConvertRuleByFormId(this.UserCtx, htmlForm.Id);
            if (nextConvertRules == null || false == nextConvertRules.Any())
            {
                return;
            }

            var nextGroups = nextConvertRules.GroupBy(x => x.TargetFormId);
            ExtendedDataEntitySet dataEntitySet = new ExtendedDataEntitySet();
            dataEntitySet.Parse(this.UserCtx, dataEntities, htmlForm);

            foreach (var nextGroup in nextGroups)
            {
                var nextForm = metaService.LoadFormModel(this.UserCtx, nextGroup.Key);
                var nextInfos = dealForm(nextGroup, htmlForm, nextForm);
                if (nextInfos == null || nextInfos.Count <= 0)
                {
                    continue;
                }

                var visible = Convert.ToInt32(nextInfos.First()["visible"]);
                var nextInfoGroups = nextInfos.GroupBy(x => x["sourceFieldKeyType"]);
                var filterStringList = new List<string>();

                foreach(var nextInfoGroup in nextInfoGroups)
                {
                    DynamicObject[] sourceEntities = null;
                    var propKey = string.Empty;
                    if (nextInfoGroup.Key.EqualsIgnoreCase("id"))
                    {
                        sourceEntities = dataEntities;
                        propKey = "id";
                    }
                    else if (nextInfoGroup.Key.EqualsIgnoreCase(htmlForm.NumberFldKey))
                    {
                        sourceEntities = dataEntities;
                        propKey = htmlForm.NumberFldKey;
                    }
                    else
                    {
                        sourceEntities = dataEntitySet.FindByEntityKey(nextInfoGroup.Key).Select(x => x.DataEntity).ToArray();
                        propKey = "id";
                    }

                    var valueInfos = sourceEntities.Select(x => Convert.ToString(x[propKey]))
                                                   .Where(x => false == string.IsNullOrWhiteSpace(x))
                                                   .Distinct()
                                                   .ToList();
                    
                    if (valueInfos == null || valueInfos.Count <= 0)
                    {
                        continue;
                    }

                    string valueExp = string.Empty;

                    if (valueInfos.Count == 1)
                    {
                        valueExp = $" = '{valueInfos[0]}' ";
                    }
                    else
                    {
                        valueExp = string.Format(" in ( {0} )", string.Join(",", valueInfos.Select(x => $"'{x}'")));
                    }

                    foreach (var nextInfo in nextInfoGroup)
                    {
                        var sourceFieldKey = nextInfo["sourceFieldKey"];
                        if (string.IsNullOrWhiteSpace(sourceFieldKey))
                        {
                            continue;
                        }

                        var formFieldKey = nextInfo["formFieldKey"];
                        if (string.IsNullOrWhiteSpace(formFieldKey))
                        {
                            if (propKey.EqualsIgnoreCase(htmlForm.NumberFldKey))
                            {
                                continue;
                            }
                            filterStringList.Add($" {sourceFieldKey} {valueExp} ");
                            continue;
                        }

                        filterStringList.Add(string.Format(" {0} = '{1}' and {2} {3}  ", formFieldKey, htmlForm.Id, sourceFieldKey, valueExp));
                    }
                }

                addResultItem(filterStringList, nextForm, "nextForm", results, visible);
            }
        }

        private void addResultItem(List<string> filterStringList, HtmlForm htmlForm, string flag, List<Dictionary<string, object>> results, int visible)
        {
            if (filterStringList == null || filterStringList.Count <= 0)
            {
                return;
            }
            var filterString = filterStringList.Count == 1 ? filterStringList[0] : 
                                                             string.Format(" ({0}) ", string.Join(" or ", filterStringList.Select(x => $" ({x}) ")));
            results.Add(new Dictionary<string, object>
            {
                { "formId",htmlForm.Id},
                { "formCaption",htmlForm.Caption},
                { "flag",flag},
                { "filterString",filterString},
                { "visible",visible}
            });
        }

        //private List<Dictionary<string, string>> dealForm(IGrouping<string, ConvertRule> nextGroup,
        //                                                  HtmlForm htmlForm,
        //                                                  HtmlForm nextForm,
        //                                                  bool getFormField)
        //{
        //    List<Dictionary<string, string>> mapResults = new List<Dictionary<string, string>>();
        //    if (nextGroup.Count() == 1)
        //    {
        //        var fieldMappings = nextGroup.First().FieldMappings;
        //        if (fieldMappings == null || fieldMappings.Count <= 0)
        //        {
        //            return mapResults;
        //        }

        //        string formFieldKey = string.Empty;

        //        if (getFormField)
        //        {
        //            var formField = findHtmlField(fieldMappings, nextForm, $"'{htmlForm.Id}'");
        //            if (formField == null)
        //            {
        //                return mapResults;
        //            }
        //            formFieldKey = formField.Id;
        //        }
                
        //        var idField = findHtmlField(fieldMappings, nextForm, "fbillhead.id");
        //        if (idField != null)
        //        {
        //            mapResults.Add(new Dictionary<string, string>
        //            {
        //                { "formFieldKey",formFieldKey},
        //                { "sourceFieldKey",idField.Id},
        //                { "sourceFieldKeyType","id"}
        //            });
        //            return mapResults;
        //        }
        //        var numberField = findHtmlField(fieldMappings, nextForm, htmlForm.NumberFldKey);
        //        if (numberField == null)
        //        {
        //            return mapResults;
        //        }
        //        mapResults.Add(new Dictionary<string, string>
        //        {
        //            { "formFieldKey",formFieldKey},
        //            { "sourceFieldKey",numberField.Id},
        //            { "sourceFieldKeyType",htmlForm.NumberFldKey}
        //        });
        //        return mapResults;
        //    }

        //    var fieldInfos = new List<List<string>>();
        //    foreach (var nextItem in nextGroup)
        //    {
        //        var fieldMappings = nextItem.FieldMappings;
        //        if (fieldMappings == null || fieldMappings.Count <= 0)
        //        {
        //            continue;
        //        }

        //        HtmlField[] formFields = null;

        //        if (getFormField)
        //        {
        //            formFields = findHtmlFields(fieldMappings, nextForm, $"'{htmlForm.Id}'");
        //            if (formFields == null || formFields.Length <= 0)
        //            {
        //                continue;
        //            }
        //        }
                
        //        var itemInfo = new List<string>();

        //        var idFields = findHtmlFields(fieldMappings, nextForm, "fbillhead.id");
        //        if (idFields != null && idFields.Length > 0)
        //        {
        //            foreach(var idField in idFields)
        //            {
        //                if (formFields == null)
        //                {
        //                    itemInfo.Add($"|||{idField.Id}|||id");
        //                    continue;
        //                }
        //                foreach(var formField in formFields)
        //                {
        //                    itemInfo.Add($"{formField.Id}|||{idField.Id}|||id");
        //                }
        //            }
        //        }

        //        var numberFields = findHtmlFields(fieldMappings, nextForm, htmlForm.NumberFldKey);
        //        if (numberFields != null && numberFields.Length > 0)
        //        {
        //            foreach (var numberField in numberFields)
        //            {
        //                if (formFields == null)
        //                {
        //                    itemInfo.Add($"|||{numberField.Id}|||{htmlForm.NumberFldKey}");
        //                    continue;
        //                }
        //                foreach (var formField in formFields)
        //                {
        //                    itemInfo.Add($"{formField.Id}|||{numberField.Id}|||{htmlForm.NumberFldKey}");
        //                }
        //            }
        //        }

        //        if (itemInfo == null || itemInfo.Count <= 0)
        //        {
        //            continue;
        //        }

        //        fieldInfos.Add(itemInfo.Distinct().ToList());
        //    }

        //    var intersectResults = intersectList(fieldInfos);

        //    if (intersectResults == null || intersectResults.Length <= 0)
        //    {
        //        return mapResults;
        //    }

        //    foreach (var intersectResult in intersectResults)
        //    {
        //        var intersects = intersectResult.Split(new[] { "|||" }, StringSplitOptions.None);
        //        mapResults.Add(new Dictionary<string, string>
        //        {
        //            { "formFieldKey",intersects[0]},
        //            { "sourceFieldKey",intersects[1]},
        //            { "sourceFieldKeyType",intersects[2]}
        //        });
        //    }
        //    return mapResults;
        //}


        private List<Dictionary<string, string>> dealForm(IGrouping<string, ConvertRule> convertGroups,
                                                          HtmlForm preForm,
                                                          HtmlForm nextForm)
        {
            List<Dictionary<string, string>> mapResults = new List<Dictionary<string, string>>();
            var visible = 0;

            if (convertGroups.Count() == 1)
            {
                var convertRule = convertGroups.First();
                var fieldMappings = convertRule.FieldMappings;
                if (fieldMappings == null || fieldMappings.Count <= 0)
                {
                    return mapResults;
                }

                visible = convertRule.VisibleEx;
                var formFields = findHtmlFields(fieldMappings, nextForm, $"'{preForm.Id}'");
                var idFields = findHtmlFields(fieldMappings, nextForm, "fbillhead.id");
                var numberFields = findHtmlFields(fieldMappings, nextForm, preForm.NumberFldKey);
                var entryKeyList = new List<string>();

                foreach (var idField in idFields)
                {
                    var entityKey = getEntityKey(idField);
                    if (entryKeyList.Contains(entityKey))
                    {
                        continue;
                    }

                    var formField = formFields.FirstOrDefault(x => getEntityKey(x).EqualsIgnoreCase(entityKey));
                    var formFieldKey = formField == null ? string.Empty : formField.Id;
                    mapResults.Add(new Dictionary<string, string>
                    {
                        { "formFieldKey",formFieldKey},
                        { "sourceFieldKey",idField.Id},
                        { "sourceFieldKeyType","id"},
                        { "visible",visible.ToString()}
                    });
                    entryKeyList.Add(entityKey);
                }
                
                foreach(var numberField in numberFields)
                {
                    var entityKey = getEntityKey(numberField);
                    if (entryKeyList.Contains(entityKey))
                    {
                        continue;
                    }

                    var formField = formFields.FirstOrDefault(x => getEntityKey(x).EqualsIgnoreCase(entityKey));
                    //源单编号字段必须要有源单类型字段一起匹配才能准确匹配
                    if (formField == null)
                    {
                        continue;
                    }

                    var formFieldKey = formField == null ? string.Empty : formField.Id;
                    mapResults.Add(new Dictionary<string, string>
                    {
                        { "formFieldKey",formFieldKey},
                        { "sourceFieldKey",numberField.Id},
                        { "sourceFieldKeyType",preForm.NumberFldKey},
                        { "visible",visible.ToString()}
                    });
                    entryKeyList.Add(entityKey);
                }

                var activeEntityKey = string.IsNullOrWhiteSpace(convertRule.ActiveEntityKey) ? "fbillhead" : convertRule.ActiveEntityKey;
                if (entryKeyList.Contains(activeEntityKey) || activeEntityKey.EqualsIgnoreCase("fbillhead") || preForm.GetEntity(activeEntityKey) == null)
                {
                    return mapResults;
                }

                var entryIdFields = findHtmlFields(fieldMappings, nextForm, $"{activeEntityKey}.id");

                if (entryIdFields == null || entryIdFields.Length <= 0)
                {
                    return mapResults;
                }

                foreach (var entryIdField in entryIdFields)
                {
                    var formField = formFields.FirstOrDefault(x => getEntityKey(x).EqualsIgnoreCase(activeEntityKey));
                    var formFieldKey = formField == null ? string.Empty : formField.Id;
                    mapResults.Add(new Dictionary<string, string>
                    {
                        { "formFieldKey",formFieldKey},
                        { "sourceFieldKey",entryIdField.Id},
                        { "sourceFieldKeyType",$"{activeEntityKey}"},
                        { "visible",visible.ToString()}
                    });
                }

                return mapResults;
            }

            var fieldInfos = new List<List<string>>();
            foreach (var convertRule in convertGroups)
            {
                var fieldMappings = convertRule.FieldMappings;
                if (fieldMappings == null || fieldMappings.Count <= 0)
                {
                    continue;
                }

                visible = visible | convertRule.VisibleEx;

                HtmlField[] formFields = findHtmlFields(fieldMappings, nextForm, $"'{preForm.Id}'");

                var itemInfo = new List<string>();
                var idFields = findHtmlFields(fieldMappings, nextForm, "fbillhead.id");
                foreach (var idField in idFields)
                {
                    var entityKey = getEntityKey(idField);
                    var curFormFields = formFields.Where(x => getEntityKey(x).EqualsIgnoreCase(entityKey)).ToArray();
                    if (curFormFields == null || curFormFields.Length <= 0)
                    {
                        itemInfo.Add($"|||{idField.Id}|||id");
                        continue;
                    }
                    foreach (var formField in curFormFields)
                    {
                        itemInfo.Add($"{formField.Id}|||{idField.Id}|||id");
                    }
                }

                var numberFields = findHtmlFields(fieldMappings, nextForm, preForm.NumberFldKey);
                foreach (var numberField in numberFields)
                {
                    var entityKey = getEntityKey(numberField);
                    var curFormFields = formFields.Where(x => getEntityKey(x).EqualsIgnoreCase(entityKey)).ToArray();
                    if (curFormFields == null || curFormFields.Length <= 0)
                    {
                        continue;
                    }
                    foreach (var formField in curFormFields)
                    {
                        itemInfo.Add($"{formField.Id}|||{numberField.Id}|||{preForm.NumberFldKey}");
                    }
                }

                var activeEntityKey = string.IsNullOrWhiteSpace(convertRule.ActiveEntityKey) ? "fbillhead" : convertRule.ActiveEntityKey;
                if (false == activeEntityKey.EqualsIgnoreCase("fbillhead") && preForm.GetEntity(activeEntityKey) != null)
                {
                    var entryIdFields = findHtmlFields(fieldMappings, nextForm, $"{activeEntityKey}.id");

                    foreach (var entryIdField in entryIdFields)
                    {
                        var curFormFields = formFields.Where(x => getEntityKey(x).EqualsIgnoreCase(activeEntityKey)).ToArray();
                        if (curFormFields == null || curFormFields.Length <= 0)
                        {
                            itemInfo.Add($"|||{entryIdField.Id}|||{activeEntityKey}");
                            continue;
                        }
                        foreach (var formField in curFormFields)
                        {
                            itemInfo.Add($"{formField.Id}|||{entryIdField.Id}|||{activeEntityKey}");
                        }
                    }
                }

                if (itemInfo == null || itemInfo.Count <= 0)
                {
                    continue;
                }

                fieldInfos.Add(itemInfo.Distinct().ToList());
            }

            var intersectResults = intersectList(fieldInfos);

            if (intersectResults == null || intersectResults.Length <= 0)
            {
                return mapResults;
            }

            foreach (var intersectResult in intersectResults)
            {
                var intersects = intersectResult.Split(new[] { "|||" }, StringSplitOptions.None);
                mapResults.Add(new Dictionary<string, string>
                {
                    { "formFieldKey",intersects[0]},
                    { "sourceFieldKey",intersects[1]},
                    { "sourceFieldKeyType",intersects[2]},
                    { "visible",visible.ToString()}
                });
            }
            return mapResults;
        }

        private string getEntityKey(HtmlField filed)
        {
            return string.IsNullOrWhiteSpace(filed.EntityKey) ? "fbillhead" : filed.EntityKey;
        }

        private string[] intersectList(List<List<string>> fieldIdList)
        {
            if (fieldIdList == null || fieldIdList.Count <= 0)
            {
                return null;
            }

            //求交集
            var intersect = fieldIdList.First();
            var records = new List<string>();
            foreach (var fieldIds in fieldIdList)
            {
                records.AddRange(fieldIds);
                intersect = intersect.Intersect(fieldIds).ToList();
                if (intersect == null || intersect.Count <= 0)
                {
                    break;
                }
            }

            //如果有交集，返回交集的第一个元素
            if (intersect != null && intersect.Count > 0)
            {
                var fieldId = intersect.First();
                return new[] { fieldId };
            }

            var groups = records.GroupBy(x => x).OrderByDescending(x => x.Count()).ToList();

            var ids = new List<string>();
            foreach (var fieldIds in fieldIdList)
            {
                foreach (var group in groups)
                {
                    if (fieldIds.Contains(group.Key))
                    {
                        if (false == ids.Contains(group.Key))
                        {
                            ids.Add(group.Key);
                        }
                        break;
                    }
                }
            }

            return ids.ToArray();
        }

        private HtmlField[] findHtmlFields(List<FieldMapObject> fieldMapping, HtmlForm htmlForm, string mapKey)
        {
            var fields = new List<HtmlField>();
            foreach (var fieldMap in fieldMapping)
            {
                if (string.Equals(fieldMap.SrcFieldId, mapKey, StringComparison.OrdinalIgnoreCase))
                {
                    var field = htmlForm.GetField(fieldMap.Id);
                    if (field != null)
                    {
                        fields.Add(field);
                    }
                }
            }
            return fields.Distinct(x => x.Id).ToArray();
        }
    }
}
