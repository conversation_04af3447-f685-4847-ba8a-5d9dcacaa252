using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Serialization;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.DataEntity.BillType;
using JieNor.Framework.CustomException;

namespace JieNor.Framework.AppService
{
    /// <summary>
    /// 单据类型服务定义
    /// </summary>
    [InjectService]
    public partial class BillTypeService : IBillTypeService, IReceivedPubMessage, IOnceWarmUpService
    {

        /// <summary>
        /// 获取业务单据的默认单据类型Id。
        /// 优先取企业自定义的默认单据类型,取不到企业自己的，则取系统预设的默认单据类型
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">业务表单</param>
        /// <returns>默认单据类型Id</returns>
        public string GetDefaultBillTypeId(UserContext userCtx, HtmlForm htmlForm)
        {
            GetAllBillTypeInfos(userCtx);

            var billTypes = _billTypeInfos.Where(f => !f.fforbidstatus && f.fbizobject.EqualsIgnoreCase(htmlForm.Id)
                                                && (f.fmainorgid.IsNullOrEmptyOrWhiteSpace() || f.fmainorgid == "0" || f.fmainorgid == userCtx.Company))?.ToList();
            if (billTypes == null || billTypes.Count == 0)
            {
                return string.Empty;
            }

            //优先取企业自定义的默认单据类型
            BillTypeInfo billType = billTypes.FirstOrDefault(o => o.fmainorgid.EqualsIgnoreCase(userCtx.Company) && o.fisdefault);
            //取不到企业自己的，则取系统预设的默认单据类型
            if (billType == null)
            {
                billType = billTypes.FirstOrDefault(f => f.fisdefault);
                if (billType != null)//如果有系统预设，判断这个预设的是否已经被企业自己修改并且设置为非默认的了 
                {
                    var privte = billTypes.FirstOrDefault(o => billType.fid.EqualsIgnoreCase(o.fprimitiveid));
                    if (privte != null)//预设的已经被企业自己修改并且设置为非默认的了
                    {
                        billType = privte;
                    }
                }
            }

            if (billType != null)
            {
                return billType.fid;
            }

            if (billTypes.Count == 1)//如果只有这么一个单据类型，那就是这个了
            {
                return billTypes[0].fid;
            }

            return string.Empty;
        }


        /// <summary>
        /// 根据单据类型Id获取单据类型的自定义参数设置信息：优先取本组织下的参数，本组织下没有参数时，取总部组织的参数，还取不到的，取系统预设的
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">业务表单</param>
        /// <param name="billTypeId">单据类型Id</param>
        /// <returns>参数设置表单对应的动态实体对象</returns>
        public DynamicObject GetBillTypeParamSet(UserContext userCtx, HtmlForm htmlForm, string billTypeId)
        {
            var billTypeInfo = GetBillTypeInfor(userCtx, billTypeId);

            return billTypeInfo.BillTypeParaSet.fparamset;
        }



        /// <summary>
        /// 根据单据类型Id获取单据类型的相关信息：优先取本组织下的参数，本组织下没有参数时，取总部组织的参数，还取不到的，取系统预设的
        /// </summary>
        /// <param name="userCtx">用户上下文</param> 
        /// <param name="billTypeId">单据类型Id</param>
        /// <returns>参数设置表单对应的动态实体对象</returns>
        public BillTypeInfo GetBillTypeInfor(UserContext userCtx, string billTypeId)
        {
            if (billTypeId.IsNullOrEmptyOrWhiteSpace())
            {
                return null;
            }

            GetAllBillTypeInfos(userCtx);

            var billTypes = _billTypeInfos.Where(f => (f.fid.EqualsIgnoreCase(billTypeId) || f.fprimitiveid.EqualsIgnoreCase(billTypeId))
                                                && (f.fmainorgid.IsNullOrEmptyOrWhiteSpace() || f.fmainorgid == "0"
                                                || f.fmainorgid == userCtx.Company || f.fmainorgid == userCtx.TopCompanyId)).ToList();

            //优先取本组织下的参数 
            var ret = billTypes.FirstOrDefault(f => f.fmainorgid == userCtx.Company);
            if (ret != null)
            {
                return CoppyBillTypeInfo(userCtx, ret);
            }

            //本组织下没有参数时，取总部组织的参数
            ret = billTypes.FirstOrDefault(f => f.fmainorgid == userCtx.TopCompanyId);
            if (ret != null)
            {
                return CoppyBillTypeInfo(userCtx, ret);
            }

            //都取不到的，取默认的
            ret = billTypes.FirstOrDefault(f => f.fmainorgid == "0" || f.fmainorgid.IsNullOrEmptyOrWhiteSpace());
            if (ret != null)
            {
                return CoppyBillTypeInfo(userCtx, ret);
            }

            if (billTypes != null && billTypes.Count >0)
            {
                return CoppyBillTypeInfo(userCtx, billTypes[0]);
            }

            return null;
        }





        /// <summary>
        /// 获取业务表单的所有单据类型的相关信息(注意是所有的单据类型，包括禁用的)
        /// </summary>
        /// <param name="userCtx">用户上下文</param> 
        /// <param name="htmlForm">业务表单</param>
        /// <returns>参数设置表单对应的动态实体对象</returns>
        public List<BillTypeInfo> GetBillTypeInfors(UserContext userCtx, HtmlForm htmlForm)
        {
            return GetBillTypeInfors(userCtx, htmlForm.Id);
        }


        /// <summary>
        /// 获取业务表单的所有单据类型的相关信息(注意是所有的单据类型，包括禁用的)
        /// </summary>
        /// <param name="userCtx">用户上下文</param> 
        /// <param name="formId">业务表单id</param>
        /// <returns>参数设置表单对应的动态实体对象</returns>
        public List<BillTypeInfo> GetBillTypeInfors(UserContext userCtx, string formId)
        {
            var result= new List<BillTypeInfo>();
            if (formId.IsNullOrEmptyOrWhiteSpace())
            {
                return result;
            }

            GetAllBillTypeInfos(userCtx);

            var billTypes = _billTypeInfos.Where(f => f.fbizobject.EqualsIgnoreCase(formId)
                                                && (f.fmainorgid.IsNullOrEmptyOrWhiteSpace() || f.fmainorgid == "0"
                                                || f.fmainorgid == userCtx.Company || f.fmainorgid == userCtx.TopCompanyId)).ToList();

            //优先取本组织下的参数 
            result = billTypes.Where(f => f.fmainorgid == userCtx.Company).ToList ();
             
            //总部定义的或者系统预设的单据类型，如果该类型没有被企业修改过的，一起返回
            var defBillTypes = billTypes.Where(f => f.fmainorgid.IsNullOrEmptyOrWhiteSpace() || f.fmainorgid == "0")?.ToList();
            if (defBillTypes != null)
            {
                foreach (var item in defBillTypes)
                {
                    if(result.Any (f=>f.fprimitiveid.EqualsIgnoreCase (item.fid)))
                    {
                        continue;
                    }

                    result.Add(item);
                }
            }

            //自定义参数信息没有的，创建默认的自定义参数值信息
            foreach (var billTypeInfo in result)
            {
                if(billTypeInfo.BillTypeParaSet.fparamset !=null || !billTypeInfo.HaveParaSetForm)
                {
                    continue;
                }

                billTypeInfo.BillTypeParaSet.fparamset = GetDefCustomParaSet(userCtx, billTypeInfo);
            }

            return result;
        }






        private BillTypeInfo CoppyBillTypeInfo(UserContext ctx, BillTypeInfo billTypeInfo)
        {
            if (billTypeInfo.BillTypeParaSet.fparamset != null)
            {
                return billTypeInfo;
            }

            if (!billTypeInfo.HaveParaSetForm)
            {
                return billTypeInfo;
            }

            //构建自定义参数的默认值信息
            var defCustParaSet = GetDefCustomParaSet(ctx, billTypeInfo);

            var newBillTypeInfo = new BillTypeInfo()
            {
                BillTypeParaSet = new BilltypeParaSet() { fbilltypeid = billTypeInfo.fid, fmainorgid = billTypeInfo.fmainorgid, fparamsetX = defCustParaSet.ToJson(), fparamset = defCustParaSet },
                fbizobject = billTypeInfo.fbizobject,
                fcreatebypush = billTypeInfo.fcreatebypush,
                fcreatebypushmode = billTypeInfo.fcreatebypushmode,
                fisdefault = billTypeInfo.fisdefault,
                fispreset = billTypeInfo.fispreset,
                fmainorgid = billTypeInfo.fmainorgid,
                fid = billTypeInfo.fid,
                fforbidstatus = billTypeInfo.fforbidstatus,
                fname = billTypeInfo.fname,
                fnumber = billTypeInfo.fnumber,
                fparamset = billTypeInfo.fparamset,
                fparamsetX = billTypeInfo.fparamsetX,
                fprimitiveid = billTypeInfo.fprimitiveid,
                fstatus = billTypeInfo.fstatus,
                FieldCtrlInfo = billTypeInfo.FieldCtrlInfo,
            };

            return newBillTypeInfo;
        }


        private static  DynamicObject GetDefCustomParaSet(UserContext ctx, BillTypeInfo billTypeInfo)
        {
            //有自定义的参数信息，需要构建自定义参数的默认值信息
            var paraSetFormId = "{0}_param".Fmt(billTypeInfo.fbizobject);
            var mdlForm = HtmlParser.LoadFormMetaFromCache(paraSetFormId, ctx);
            if (mdlForm == null)
            {
                return null;
            }

            //如果有对应表单参数模型，则需要构建对应表单自定义参数的默认值信息 
            var key = @"{0}:{1}".Fmt(billTypeInfo.fbizobject, ctx.Company);//自定义的参数的默认值，跟组织有关
            if (!_defBillTypeParaSet.ContainsKey(key))
            {
                var paramObj = mdlForm.GetDynamicObjectType(ctx).CreateInstance() as DynamicObject;
                IDefaultValueCalculator defaultValueCalculator = ctx.Container.GetService<IDefaultValueCalculator>();
                defaultValueCalculator.Execute(ctx, mdlForm, new[] { paramObj });

                _defBillTypeParaSet[key] = paramObj;
            }

            return _defBillTypeParaSet[key];
        }

        /// <summary>
        /// 根据单据类型Id获取参数设置信息
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">业务表单</param>
        /// <param name="billTypeIds">单据类型Id集合</param>
        /// <returns>参数设置表单对应的动态实体对象</returns>
        public Dictionary<string, DynamicObject> GetBillTypeParamSet(UserContext userCtx, HtmlForm htmlForm, HashSet<string> billTypeIds)
        {
            var returnObjs = new Dictionary<string, DynamicObject>();
            foreach (var billTypeid in billTypeIds)
            {
                returnObjs[billTypeid] = GetBillTypeParamSet(userCtx, htmlForm, billTypeid);
            }

            return returnObjs;
        }


        /// <summary>
        /// 获取单据类型中某个自定义参数的值
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="userCtx"></param>
        /// <param name="formId">业务表单标识</param>
        /// <param name="billTypeId">单据类型Id</param>
        /// <param name="paramKey">自定义参数标识</param>
        /// <param name="defValue">默认值</param>
        /// <returns></returns>
        /// <returns></returns>
        public T GetBillTypeParam<T>(UserContext userCtx, string formId, string billTypeId, string paramKey, T defValue = default(T))
        {
            var billTypeInfo = GetBillTypeInfor(userCtx, billTypeId);
            var billTypeParamObj= billTypeInfo.BillTypeParaSet.fparamset;

            if (billTypeParamObj == null)
            {
                return defValue;
            }

            var value = billTypeParamObj.GetValue<T>(paramKey, defValue);

            return value;
        }



        /// <summary>
        /// 根据单据类型Id获取单据类型参数动态实体对象
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="billTypeIds">单据类型Id集合</param>
        /// <returns>单据类型参数动态实体对象</returns>
        public List<DynamicObject> GetBillTypeParam(UserContext userCtx, HashSet<string> billTypeIds)
        {
            if (billTypeIds == null || !billTypeIds.Any()) return null;

            List<DynamicObject> returnObjs = null;

            var htmlForm = userCtx.Container.GetService<MetaModelService>()?.LoadFormModel(userCtx, "bd_billtypeparam");
            var _dmBillTypePara = userCtx.GetDefaultDataManager(htmlForm);

            var dt = htmlForm.GetDynamicObjectType(userCtx);
            var where = $"(fmainorgid=' ' or fmainorgid='0' or fmainorgid=@fmainorgid or fmainorgid=@TopCompanyId) and fbilltypeid in({string.Join(",", billTypeIds.Select((x, i) => $"@fbilltypeid{i}"))})";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("TopCompanyId", System.Data.DbType.String, userCtx.TopCompanyId)
            };
            sqlParam.AddRange(billTypeIds.Select((x, i) => new SqlParam($"@fbilltypeid{i}", System.Data.DbType.String, x)));
            var dataReader = userCtx.GetPkIdDataReader(htmlForm, where, sqlParam);

            var dynObjs = _dmBillTypePara.SelectBy(dataReader).OfType<DynamicObject>();
            if (dynObjs != null && dynObjs.Count() > 0)
            {
                returnObjs = new List<DynamicObject>();
                foreach (var item in billTypeIds)
                {
                    DynamicObject dynObj = null;
                    //如果企业有自定义的单据类型参数，则以企业自定义的为准
                    dynObj = dynObjs.FirstOrDefault(o => Convert.ToString(o["fbilltypeid"]).EqualsIgnoreCase(item) && Convert.ToString(o["fmainorgid"]).EqualsIgnoreCase(userCtx.Company));

                    //如果企业没有自定义，则尝试取总部预设的
                    if (dynObj == null)
                    {
                        dynObj = dynObjs.FirstOrDefault(o => Convert.ToString(o["fbilltypeid"]).EqualsIgnoreCase(item) && Convert.ToString(o["fmainorgid"]).EqualsIgnoreCase(userCtx.TopCompanyId));
                    }

                    //如果都取不到，取系统预设的
                    if (dynObj == null)
                    {
                        dynObj = dynObjs.FirstOrDefault(o => Convert.ToString(o["fbilltypeid"]).EqualsIgnoreCase(item) &&
                        (o["fmainorgid"].IsNullOrEmptyOrWhiteSpace()
                            || Convert.ToString(o["fmainorgid"]).EqualsIgnoreCase("0")));
                    }

                    if (dynObj != null)
                    {
                        returnObjs.Add(dynObj);
                    }
                }
            }

            if (returnObjs == null)
            {
                returnObjs = new List<DynamicObject>();
            }
            string defJson = string.Empty;

            foreach (var item in billTypeIds)
            {
                var existsObj = returnObjs.FirstOrDefault(x => Convert.ToString(x["fbilltypeid"]) == item);
                if (existsObj == null || existsObj["fparamset"].IsNullOrEmptyOrWhiteSpace())
                {
                    if (defJson == string.Empty)
                    {
                        var formMeta = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "ydj_order_param");
                        var paramObj = formMeta.GetDynamicObjectType(userCtx).CreateInstance() as DynamicObject;
                        IDefaultValueCalculator defaultValueCalculator = userCtx.Container.GetService<IDefaultValueCalculator>();
                        defaultValueCalculator.Execute(userCtx, formMeta, new[] { paramObj });

                        defJson = paramObj.ToJson();
                    }

                    DynamicObject defObj = dt.CreateInstance() as DynamicObject;
                    defObj["fbilltypeid"] = item;
                    defObj["fparamset"] = defJson;

                    returnObjs.Add(defObj);
                }
            }

            return returnObjs;
        }


        /// <summary>
        /// 根据单据类型Id获取单据类型动态实体对象（单据类型完整数据包）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="billTypeId">单据类型Id</param>
        /// <returns>单据类型动态实体对象</returns>
        public DynamicObject GetBillTypeById(UserContext userCtx, string billTypeId)
        {
            if (billTypeId.IsNullOrEmptyOrWhiteSpace()) return null;

            var metaService = userCtx.Container.GetService<MetaModelService>();

            var billTypeForm = metaService?.LoadFormModel(userCtx, "bd_billtype");
            var _dmBillType = userCtx.GetDefaultDataManager(billTypeForm);

            var billTypeObj = _dmBillType.Select(billTypeId) as DynamicObject;

            return billTypeObj;
        }


        /// <summary>
        /// 创建单据类型UI数据包
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="billTypeObj"></param>
        /// <returns></returns>
        public JObject CreateBillTypeUIDataObject(UserContext userCtx, DynamicObject billTypeObj)
        {
            var jobj = new JObject();
            jobj["id"] = Convert.ToString(billTypeObj["id"]);
            jobj["fnumber"] = Convert.ToString(billTypeObj["fnumber"]);
            jobj["fname"] = Convert.ToString(billTypeObj["fname"]);

            var jarray = new JArray();
            jobj["fentity"] = jarray;

            var entrys = billTypeObj["fentity"] as DynamicObjectCollection;
            foreach (var entry in entrys)
            {
                var jentry = new JObject();
                jentry["ffieldid"] = new JObject
                {
                    ["id"] = Convert.ToString(entry["ffieldid"]),
                    ["name"] = "" //Convert.ToString(entry["ffieldid_txt"])
                };
                jentry["flock"] = new JObject
                {
                    ["id"] = Convert.ToString(entry["flock"]),
                    ["name"] = "" //Convert.ToString(entry["flock_txt"])
                };
                jentry["fmustinput"] = new JObject
                {
                    ["id"] = Convert.ToString(entry["fmustinput"]),
                    ["fnumber"] = "",
                    ["fname"] = ""
                };
                jarray.Add(jentry);
            }

            return jobj;
        }


        /// <summary>
        /// 预置单据类型参数
        /// </summary>
        /// <param name="userCtx"></param>
        public void PresetBillTypeParam(UserContext userCtx)
        {
            try
            {
                var metaService = userCtx.Container.GetService<IMetaModelService>();
                var billTypeParamForm = metaService.LoadFormModel(userCtx, "bd_billtypeparam");
                var _dmBillTypePara = userCtx.GetDefaultDataManager(billTypeParamForm);

                //先找出没有预置过参数的单据类型
                var strSql = @"
                select fid,fparamset from t_bd_billtype 
                where fmainorgid='0' and fispreset='1' 
                    and fid not in(select fbilltypeid from t_bd_billtypeparam where fmainorgid=@fmainorgid)";

                var sqlParam = new List<SqlParam>
                {
                    new SqlParam("fmainorgid", System.Data.DbType.String, userCtx.TopCompanyId)
                };

                List<DynamicObject> billTypeParamList = new List<DynamicObject>();

                var dbService = userCtx.Container.GetService<IDBService>();
                using (var dataReader = dbService.ExecuteReader(userCtx, strSql, sqlParam))
                {
                    while (dataReader.Read())
                    {
                        var billTypeId = dataReader.GetString("fid");
                        var paramSet = dataReader.GetString("fparamset");
                        if (paramSet.IsNullOrEmptyOrWhiteSpace()) continue;

                        var billTypeParam = _dmBillTypePara.DataEntityType.CreateInstance() as DynamicObject;
                        billTypeParam["fbilltypeid"] = billTypeId;
                        billTypeParam["fparamset"] = paramSet;
                        billTypeParamList.Add(billTypeParam);
                    }
                }

                if (billTypeParamList.Count > 0)
                {
                    var prepareService = userCtx.Container.GetService<PrepareSaveDataService>();
                    prepareService.PrepareDataEntity(userCtx, billTypeParamForm, billTypeParamList.ToArray(), OperateOption.Create());

                    _dmBillTypePara.Save(billTypeParamList);

                    //通知其他负载均衡站点更新缓存
                    var pubSubService = userCtx.Container.GetService<IPubSubService>();
                    pubSubService.PublishMessage<string>(DataTransferObject.Const.PubSubChannel.BillTypeChange, userCtx.Company);
                }
            }
            catch (Exception ex)
            {
                var logServiceEx = userCtx.Container.GetService<ILogServiceEx>();
                logServiceEx?.Error("预置单据类型参数时出错", ex);
            }
        }



        public DynamicObject GetBillTypeByBizObject(UserContext userCtx, string bizObject, string fid)
        {
            if (fid.IsNullOrEmptyOrWhiteSpace())
            {
                return null;
            }

            GetAllBillTypeInfos(userCtx);

            var billTypes = _billTypeInfos.Where(f => f.fid.EqualsIgnoreCase(fid)
                                                && (f.fmainorgid.IsNullOrEmptyOrWhiteSpace() || f.fmainorgid == "0"
                                                || f.fmainorgid == userCtx.Company || f.fmainorgid == userCtx.TopCompanyId)).ToList();


            var list = userCtx.LoadBizDataByFilter("bd_billtype", $"fbizobject = '{bizObject}' and ( fid='{fid}' or fprimitiveid= '{fid}' )");
            var obj = list.FirstOrDefault(f => Convert.ToString(f["fmainorgid"]).EqualsIgnoreCase(userCtx.Company));
            return obj == null ? list.FirstOrDefault() : obj;
        }


        //public List<DynamicObject> GetBillTypesByBizObject(UserContext userCtx, string bizObject, List<string> fids)
        //{
        //    List<DynamicObject> result = new List<DynamicObject>();
        //    //var list = userCtx.LoadBizDataByFilter("bd_billtype", $"fbizobject = '{bizObject}' and ( fid in ('{string.Join("','", fids)}')  or fprimitiveid in ('{string.Join("','", fids)}') )");
        //    //按需查字段
        //    var sqlText = $@"
        //    select fid id,fmainorgid,fprimitiveid from t_bd_billtype with(nolock) where fid in ('{string.Join("','", fids)}') and fbizobject='{bizObject}' 
        //    union all
        //    select fid id,fmainorgid,fprimitiveid from t_bd_billtype with(nolock) where fmainorgid = '{userCtx.Company}' and fprimitiveid in ('{string.Join("','", fids)}') and fbizobject='{bizObject}' ";
        //    var dbService = userCtx.Container.GetService<IDBService>();
        //    var list = dbService.ExecuteDynamicObject(userCtx, sqlText);

        //    if (list != null && list.Count() > 0)
        //    {

        //        foreach (var dynObj in list)
        //        {
        //            //优先取本企业的
        //            if (Convert.ToString(dynObj["fmainorgid"]).EqualsIgnoreCase(userCtx.Company))
        //            {
        //                result.Add(dynObj);
        //            }
        //            else
        //            {
        //                //否则取总部预置的
        //                if (Convert.ToString(dynObj["fmainorgid"]).EqualsIgnoreCase("0"))
        //                {
        //                    result.Add(dynObj);
        //                }
        //            }
        //        }
        //    }
        //    return result;
        //}


        /// <summary>
        /// 获取单据类型Id的映射关系：预设的单据类型，用户修改后会变成私有的，在单据保存等判断中，需要把预设的修改私有的，这里返回映射关系
        /// </summary>
        /// <param name="userCtx"></param> 
        /// <param name="billTypeIds"></param>
        /// <returns>key ----- 原单据类型Id，value----对应的私有单据类型Id </returns>
        public Dictionary<string, string> GetBillTypeIdMapInfo(UserContext userCtx, List<string> billTypeIds)
        {
            if (billTypeIds==null || billTypeIds.Count ==0)
            {
                return null;
            }

            GetAllBillTypeInfos(userCtx);

            var ret = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            foreach (var billTypeId in billTypeIds)
            {
                var billTypes = _billTypeInfos.Where(f => (f.fid.EqualsIgnoreCase(billTypeId) || f.fprimitiveid.EqualsIgnoreCase(billTypeId))
                                                && (f.fmainorgid.IsNullOrEmptyOrWhiteSpace() || f.fmainorgid == "0"
                                                || f.fmainorgid == userCtx.Company || f.fmainorgid == userCtx.TopCompanyId)).ToList();

                //优先取本组织下的参数 
                var billTypeX = billTypes.FirstOrDefault(f => f.fmainorgid == userCtx.Company);
                if (billTypeX != null)
                {
                    ret[billTypeId] = billTypeX.fid;
                    continue;
                }

                //本组织下没有参数时，取总部组织的参数
                billTypeX = billTypes.FirstOrDefault(f => f.fmainorgid == userCtx.TopCompanyId);
                if (billTypeX != null)
                {
                    ret[billTypeId] = billTypeX.fid;
                    continue;
                }

                //都取不到的，取默认的
                billTypeX = billTypes.FirstOrDefault(f => f.fmainorgid == "0" || f.fmainorgid.IsNullOrEmptyOrWhiteSpace());
                if (billTypeX != null)
                {
                    ret[billTypeId] = billTypeX.fid;
                    continue;
                }
            }

            return ret ;
        }




    }
}