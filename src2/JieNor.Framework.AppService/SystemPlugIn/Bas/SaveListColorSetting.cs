using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormModel.List;

namespace JieNor.Framework.AppService.SystemPlugIn.Bas
{

    /// <summary>
    ///  保存列表颜色设置信息
    /// </summary>
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("SaveListColorSetting")]
    public class SaveListColorSetting : AbstractOperationServicePlugIn
    {

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            var formId = this.GetQueryOrSimpleParam<string>("formId", "");
            if(formId.IsNullOrEmptyOrWhiteSpace () )
            {
                throw new BusinessException("参数异常，请指定要设置的业务对象标识");
            }

            var setting = this.GetQueryOrSimpleParam<string>("setting", "");
            if (!setting.IsNullOrEmptyOrWhiteSpace())
            {
                //默认是列表
                var category = "listcolor";
                var entityKey = this.GetQueryOrSimpleParam<string>("entityKey", "");
                if (!entityKey.IsNullOrEmptyOrWhiteSpace())
                {
                    category = $"{entityKey}color";
                }

                var option = setting.FromJson<List<ListColorSetting>>();
                  var svc = this.Container.GetService<IListQuryProfile>();
                svc.SaveListCellColor(this.Context, formId, option, category);

                this.Result.IsSuccess = true;
                this.Result.ComplexMessage.SuccessMessages.Add("保存成功！");
            }

        }

    }
}
