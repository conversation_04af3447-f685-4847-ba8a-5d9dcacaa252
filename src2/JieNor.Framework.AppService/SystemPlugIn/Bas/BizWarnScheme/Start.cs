using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.Framework.AppService.SystemPlugIn.Bas.BizWarnScheme
{
    /// <summary>
    /// 业务预警方案：启用
    /// </summary>
    [InjectService]
    [FormId("bas_bizwarnscheme")]
    [OperationNo("bwcstart")]
    public class Start : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return newData["fispreset"].Equals("1") && !this.Context.Company.Equals(this.Context.TopCompanyId);
            }).WithMessage(@"业务预警操作预设数据只允许总部人员启用/停用！"));
        }
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                this.Result.SimpleMessage = "请选择一条数据进行操作！";
                return;
            }

            var taskForm = this.MetaModelService.LoadFormModel(this.Context, "bas_task");
            var taskType = taskForm.GetDynamicObjectType(this.Context);
            List<DynamicObject> taskDynObjs = new List<DynamicObject>();

            foreach (var dataEntity in e.DataEntitys)
            {
                var taskDynObj = taskType.CreateInstance() as DynamicObject;
                taskDynObj["ftranid"] = dataEntity["ftranid"];
                taskDynObj["fname"] = dataEntity["fname"] + " - 预警任务";
                taskDynObj["fpluginid"] = "businesswarntask";
                taskDynObj["fworkobject"] = dataEntity["fbizformid"];
                taskDynObj["fcondition"] = dataEntity["fcondition"];
                taskDynObj["fexecuteplan"] = dataEntity["fhertz"];
                taskDynObj["fsysmessage"] = true;
                taskDynObj["fstartstatus"] = "ew_start001";
                taskDynObj["ftasklabel"] = "业务预警任务";
                taskDynObj["fispreset"] = dataEntity["fispreset"];//如果业务预警预设，那么自动将任务预设
                taskDynObjs.Add(taskDynObj);

                dataEntity["fstartstatus"] = "bwc_start002";
            }

            var result = this.Gateway.InvokeBillOperation(this.Context, taskForm.Id, taskDynObjs, "save", new Dictionary<string, object>());
            result?.ThrowIfHasError(true, $"生成关联的计划任务失败！");

            result = this.Gateway.InvokeBillOperation(this.Context, taskForm.Id, taskDynObjs, "ewtest", new Dictionary<string, object>());
            result?.ThrowIfHasError(true, $"执行关联的计划任务失败！");

            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(e.DataEntitys);

            this.AddSetValueAction("fstartstatus", "bwc_start002");
            this.Result.IsSuccess = true;
        }
    }
}