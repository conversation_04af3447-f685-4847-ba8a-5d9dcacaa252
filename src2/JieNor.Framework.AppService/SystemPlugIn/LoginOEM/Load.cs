using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.SystemPlugIn.LoginOEM
{
    /// <summary>
    /// oem自定义页面保存动作处理
    /// </summary>
    [InjectService]
    [FormId("sys_oemloginpage")]
    [OperationNo("load")]
    public class Load : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 操作执行完成事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {            
            var dctParaVal = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            dctParaVal["logo"] = @"../fw/images/oem/myhome-logo.png";
            dctParaVal["backgroundImage"] = @"../fw/images/oem/myhome-default-bg.jpg";
            dctParaVal["systemDesc"] = "最专业的家居信息化服务商";
            dctParaVal["companyName"] = "易到家官网";
            dctParaVal["companyUrl"] = "http://www.91myhome.com/";
            dctParaVal["hotLineTitle"] = "客服热线：";
            dctParaVal["hotLineTelphone"] = "0755-2665 5994";
            var systemProfile = this.Container.GetService<ISystemProfile>();
            var systemParaVal = systemProfile.GetProfile(this.Context, "fw", "oem_login_param")?
                .FromJson<Dictionary<string, string>>() ?? new Dictionary<string, string>();
            if (systemParaVal.IsEmpty())
            {
                systemParaVal = dctParaVal;
            }

            //兼容历史数据
            var hotLineTitle = "";
            systemParaVal.TryGetValue("hotLineTitle", out hotLineTitle);
            if (hotLineTitle.IsNullOrEmptyOrWhiteSpace())
            {
                systemParaVal["hotLineTitle"] = "客服热线：";
            }

            this.Result.SrvData = new
            {
                Data = systemParaVal
            };
        }
    }
}
