using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.Utils;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Interface.Log;

namespace JieNor.Framework.AppService.SystemPlugIn.Auth
{
    /// <summary>
    /// 设置登录方式
    /// </summary>
    [InjectService]
    [FormId("sec_user")]
    [OperationNo("SetLoginWay")]
    public class SetLoginWay : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 准备操作选项时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            base.OnPrepareOperationOption(e);

            e.OpCtlParam.IgnoreOpMessage = true;
        }

        /// <summary>
        /// 执行操作事务后事件，通知插件对象执行其它事务无关的业务逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length < 1)
            {
                throw new BusinessException($"请选择一个用户后再进行设置！");
            }

            var wxPcAppId = this.GetAppConfig("fw:weixin.pc.appid");
            var wxPcAppSecret = this.GetAppConfig("fw:weixin.pc.appsecret");
            if (wxPcAppId.IsNullOrEmptyOrWhiteSpace() || wxPcAppSecret.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"当前系统未配置微信登录，暂无无法使用该功能！");
            }

            var onlyWechat = this.GetQueryOrSimpleParam<bool>("onlyWechat", false);
            var userNames = e.DataEntitys.Select(o => o["fnumber"] as string).Distinct().ToList();

            //跨站更新ac站点用户信息
            var request = new CommonFormDTO()
            {
                FormId = "auth_user",
                OperationNo = "SetLoginWay",
            };
            request.SimpleData.Add("onlyWechat", onlyWechat.ToString());
            request.SimpleData.Add("userName", string.Join(",", userNames));
            
            var gateway = this.Container.GetService<IHttpServiceInvoker>();
            var targetSEP = TargetSEP.AuthService;
            if (!targetSEP.Headers.ContainsKey("X-AppId")) targetSEP.Headers.Add("X-AppId", this.Context.Product);

            var acResponse = gateway.Invoke(this.Context, targetSEP, request) as DynamicDTOResponse;
            var acResult = acResponse?.OperationResult;

            this.Result.MergeResult(acResult);

            //更新本地用户信息
            this.UpdateLocalUserInfo(e.DataEntitys, acResult, onlyWechat);
        }

        /// <summary>
        /// 更新本地用户信息
        /// </summary>
        /// <param name="dataEntitys"></param>
        /// <param name="acResult"></param>
        /// <param name="onlyWechat"></param>
        private void UpdateLocalUserInfo(DynamicObject[] dataEntitys, IOperationResult acResult, bool onlyWechat)
        {
            if (acResult == null || !acResult.IsSuccess) return;

            var acSrvData = acResult?.SrvData?.ToString()?.FromJson<Dictionary<string, List<string>>>();
            var succeedUsers = new List<string>();
            acSrvData.TryGetValue("succeedUsers", out succeedUsers);
            if (succeedUsers == null || succeedUsers.Count < 1) return;

            var waitSaves = new List<DynamicObject>();
            foreach (var number in succeedUsers)
            {
                var localUser = dataEntitys.FirstOrDefault(o => Convert.ToString(o["fnumber"]).EqualsIgnoreCase(number));
                if (localUser != null)
                {
                    localUser["fonlywechat"] = onlyWechat;
                    waitSaves.Add(localUser);
                }
            }

            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));

            //更新当前企业下的本地用户信息
            if (waitSaves.Count > 0)
            {
                dm.Save(waitSaves);
            }

            //更新其他企业下的本地同名用户信息
            this.UpdateOtherCompanyLocalUserInfo(dm, succeedUsers, onlyWechat);
        }

        /// <summary>
        /// 更新其他企业下的本地同名用户信息（其他企业下用户名相同的数据）
        /// 该类数据实时性要求不高，采用异步更新
        /// </summary>
        /// <param name="dm"></param>
        /// <param name="succeedUsers"></param>
        /// <param name="onlyWechat"></param>
        private async Task UpdateOtherCompanyLocalUserInfo(IDataManager dm, List<string> succeedUsers, bool onlyWechat)
        {
            await Task.Run(() =>
            {
                try
                {
                    var where = "";
                    var sqlParam = new List<SqlParam>();
                    if (succeedUsers.Count == 1)
                    {
                        where = "fnumber=@userName";
                        sqlParam.Add(new SqlParam("@userName", System.Data.DbType.String, succeedUsers[0]));
                    }
                    else
                    {
                        StringBuilder sbWhere = new StringBuilder();
                        for (int i = 0; i < succeedUsers.Count; i++)
                        {
                            var paramName = "@n" + i;
                            sbWhere.Append(paramName + ",");
                            sqlParam.Add(new SqlParam(paramName, System.Data.DbType.String, succeedUsers[i]));
                        }
                        where = $"fnumber in({sbWhere.ToString().TrimEnd(',')})";
                    }

                    var sqlText = $"select fid from {this.HtmlForm.BillHeadTableName} where {where}";
                    var userIds = this.DBService.ExecuteDynamicObject(this.Context, sqlText, sqlParam)
                        ?.Select(o => o["fid"]);
                    var localUsers = dm.Select(userIds)?.OfType<DynamicObject>();
                    if (localUsers == null || localUsers.Count() < 1) return;

                    foreach (var localUser in localUsers)
                    {
                        localUser["fonlywechat"] = onlyWechat;
                    }
                    dm.Save(localUsers);
                }
                catch (Exception ex)
                {
                    var logEx = this.Container.GetService<ILogServiceEx>();
                    logEx.Error("更新其他企业下的本地同名用户登录方式时出错", ex);
                }
            });
        }
    }
}
