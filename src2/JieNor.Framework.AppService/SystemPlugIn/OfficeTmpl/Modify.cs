using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json.Linq;

using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FileServer;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.Framework.AppService.SystemPlugIn.OfficeTmpl
{
    /// <summary>
    /// 打印模板修改
    /// </summary>
    [InjectService]
    [FormId("bas_officetmpl")]
    [OperationNo("modify")]
    public class Modify : AbstractOperationServicePlugIn
    {

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length == 0)
            {
                return;
            }

            var bizFormId = e.DataEntitys?[0]["fsrcformid"]?.ToString();
            var bizMeta = HtmlParser.LoadFormMetaFromCache(bizFormId, this.Context);
            var ens = bizMeta?.EntryList.Where(f => f is HtmlEntryEntity && !(f is HtmlSubEntryEntity)).ToList();
            if (ens?.Count > 0)
            {
                var beAdd = new List<HtmlEntryEntity>();
                var enFilter = e.DataEntitys[0]["fentity"] as DynamicObjectCollection;
                foreach (var item in ens)
                {
                    if (enFilter.Any(f => item.Id.ToString().EqualsIgnoreCase(f["ffilterentrykey"]?.ToString())))
                    {
                        continue;
                    }
                    beAdd.Add(item);
                }

                if (beAdd.Count > 0)
                {
                    foreach (var item in beAdd)
                    {
                        var addRow = enFilter.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                        addRow["ffilterentrykey"] = item.Id;
                        addRow["ffilterentrydesc"] = item.Caption;
                        addRow["ffilterentrydesc"] = item.Caption;
                        addRow["fenpapercount"] = 1;

                        enFilter.Add(addRow);
                    }
                }
            }
        }



    }
}