using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.SystemPlugIn.UserEdit
{
    /// <summary>
    /// 查看我的企业接口插件
    /// </summary>
    [InjectService]
    [FormId("sec_user")]
    [OperationNo("GetMyProfile")]
    public class GetMyProfile : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 获取当前用户资料信息
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            var userData = dm.Select(this.Context.UserId) as DynamicObject;
            if (userData == null)
            {
                throw new BusinessException("本地用户不存在，请尝试注销后重新登录来恢复！");
            }
            var uiConverter = this.Container.GetService<IUiDataConverter>();
            var billData = uiConverter.CreateUIDataObject(this.Context, this.HtmlForm, userData);

            #region 获取部门和关联员工（打包数据增加fdeptname，fstaffname返回）
            // 用这个会showForm var resp = this.Gateway.InvokeBillOperation(this.Context, "sec_user", null, "view", dctOption);
            string deptName = string.Empty;
            string staffName = string.Empty;
            //追加部门信息进用户数据包里
            var baseObjProvider = this.Container.GetService<IBaseFormProvider>();
            var deptInfo = baseObjProvider?.GetMyDepartment(this.Context);
            if (deptInfo != null) deptName = deptInfo.Name;
            // 关联员工
            var staffInfo = baseObjProvider?.GetMyStaff(this.Context);
            if (staffInfo != null) staffName = staffInfo.Name;
            var uiData = billData.GetJsonValue<JObject>("uidata");
            uiData["fstaffid"] = staffName;
            uiData["fdeptname"] = deptName;
            uiData["fuserid"] = this.Context.UserId;
            #endregion

            //加载当前登录用户关联的AC用户信息
            this.LoadCurrentAuthUser(uiData);

            this.Result.SrvData = new
            {
                Data = uiData
            };
        }

        /// <summary>
        /// 加载当前登录用户关联的AC用户信息
        /// </summary>
        /// <param name="uiData"></param>
        private void LoadCurrentAuthUser(JObject uiData)
        {
            //微信登录：只有后台配置了微信登录才显示
            var wxPcAppId = this.GetAppConfig("fw:weixin.pc.appid");
            if (wxPcAppId.IsNullOrEmptyOrWhiteSpace()) return;

            var request = new CommonFormDTO()
            {
                FormId = "auth_user",
                OperationNo = "fetch",
            };
            request.SimpleData.Add("userName", this.Context.UserName);
            request.SimpleData.Add("productId", this.Context.Product);
            var gateway = this.Container.GetService<IHttpServiceInvoker>();

            var targetSEP = TargetSEP.AuthService;
            if (!targetSEP.Headers.ContainsKey("X-AppId")) targetSEP.Headers.Add("X-AppId", this.Context.Product);

            var result = gateway.Invoke(this.Context, targetSEP, request) as DynamicDTOResponse;
            var acUser = result?.OperationResult?.SrvData?.ToString().FromJson<CustomUserAuth>();

            uiData["fwxunionid"] = acUser?.WxUnionId ?? "";
            uiData["fwxopenid"] = acUser?.WxOpenId ?? "";
            uiData["fwxpcopenid"] = acUser?.WxPcOpenId ?? "";
        }
    }
}
