using JieNor.Framework.DataTransferObject.Const;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.SystemPlugIn.Version
{
    /// <summary>
    /// 发版管理参数：保存
    /// </summary>
    [InjectService]
    [FormId("sys_versionparam")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 执行操作事务后事件
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            var paramObj = e.DataEntitys[0];
            var proVersionNo = Convert.ToString(paramObj?["fproversionno"] ?? "");//产品版本号
            var proVersionDate = Convert.ToString(paramObj?["fproversiondate"] ?? "");//产品发版日期
            proVersionDate = Convert.ToDateTime(proVersionDate).ToString("yyyy-MM-dd HH:mm");//格式化日期
            var siteVersionNo = Convert.ToString(paramObj?["fsiteversionno"] ?? "");//站点默认版本号
            if (proVersionNo.IsNullOrEmptyOrWhiteSpace() || proVersionDate.IsNullOrEmptyOrWhiteSpace() || siteVersionNo.IsNullOrEmptyOrWhiteSpace())
            {
                //任一配置为空则不更新
                return;
            }

            var msgStr = $"fw.versionno={proVersionNo},fw.versiondate={proVersionDate},ms.site.version={siteVersionNo}";
            //发送消息，通知其他负载均衡站点更新Host.config 
            var pubSubService = this.Container.GetService<IPubSubService>();
            pubSubService.PublishMessage<string>(PubSubChannel.UpdateSystemVersion, msgStr);
        }
    }
}
