using System;
using System.Linq;
using System.Collections.Generic;
using Newtonsoft.Json;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.DataTransferObject.BPM;
using JieNor.Framework.AppService.OperationService.ApprovalFlow;

namespace JieNor.Framework.AppService.SystemPlugIn.BPM.FlowProxy
{
    /// <summary>
    /// 流程代理：加载流程
    /// </summary>
    [InjectService]
    [FormId("bpm_flowproxy")]
    [OperationNo("LoadFlow")]
    public class LoadFlow : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var userNumber = this.GetQueryOrSimpleParam<string>("userNumber");
            if (userNumber.IsNullOrEmptyOrWhiteSpace()) return;

            var sqlText = @"select fv.fdetailid,fv.fflowname,fv.fvername,fv.fentryid,fv.fdetails from t_bpm_businessapproval bp 
            inner join t_bpm_businessflow bf on bf.fid=bp.fid 
            inner join t_bpm_flowversion fv on fv.fentryid=bf.fentryid 
            where bp.fmainorgid=@fmainorgid and fv.fismain='1' and fverstatus='flowver_sta02'";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("fmainorgid",System.Data.DbType.String, this.Context.Company)
            };

            var flowList = new List<Dictionary<string, string>>();
            using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, sqlParam))
            {
                while (reader.Read())
                {
                    Dictionary<string, string> flow = new Dictionary<string, string>();
                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        var objValue = reader.GetString(i);
                        var colName = reader.GetName(i);
                        flow[colName] = objValue.IsNullOrEmptyOrWhiteSpace() ? "" : objValue;
                    }
                    flowList.Add(flow);
                }
            }

            var data = new List<Dictionary<string, string>>();
            if (flowList.Any())
            {
                var flowService = this.Container.GetService<IApprovalFlowService>();

                foreach (var flow in flowList)
                {
                    var jsonStr = flow["fdetails"] as string;
                    if (jsonStr.IsNullOrEmptyOrWhiteSpace()) continue;

                    //流程设计图信息
                    BPMDesignDrawingModel details = null;
                    try
                    {
                        details = JsonConvert.DeserializeObject<BPMDesignDrawingModel>(jsonStr);
                    }
                    catch { }
                    var sections = details?.sections;
                    if (sections == null || sections.Count <= 0) continue;

                    //解析流程设计图中的参与人
                    var exists = false;
                    foreach (var node in sections)
                    {
                        if(flowService.CheckUserIsNodeParticipant(this.Context, node, userNumber))
                        {
                            exists = true;
                            break;
                        }
                    }
                    if (exists)
                    {
                        data.Add(new Dictionary<string, string>
                        {
                            { "fflowid", flow["fentryid"] },
                            { "fverid", flow["fdetailid"] },
                            { "fflowname", flow["fflowname"] },
                            { "fvername", flow["fvername"] }
                        });
                    }
                }
            }

            this.Result.SrvData = data;
            this.Result.IsSuccess = true;
        }
    }
}