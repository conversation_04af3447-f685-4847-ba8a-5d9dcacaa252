using System;
using System.Linq;
using System.Text.RegularExpressions;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System.Collections.Generic;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.DataTransferObject;

namespace JieNor.Framework.AppService.SystemPlugIn.Sys.BillConvertRule
{
    /// <summary>
    /// 单据转换规则：获取指定表单的字段
    /// </summary>
    [InjectService]
    [FormId("sys_billconvertrule")]
    [OperationNo("queryfield")]
    public class RelatedInfoQueryField : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName.ToLower())
            {
                case "onfilterqueryfield":
                    var eventData = e.EventData as Tuple<string, string>;
                    if (eventData == null) return;
                    var bizField = this.HtmlForm.GetField(eventData.Item1) as HtmlFieldModelField;
                    if (bizField == null) return;
                    var formModel = this.MetaModelService.LoadFormModel(this.Context, eventData.Item2);
                    if (formModel == null) return;
                    e.Cancel = true;
                    e.Result = this.GetBizObjectFields(formModel, bizField);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 获取指定表单的字段列表
        /// </summary>
        /// <param name="bizForm"></param>
        /// <param name="bizField"></param>
        /// <returns></returns>
        private List<Dictionary<string, object>> GetBizObjectFields(HtmlForm bizForm, HtmlField bizField)
        {
            //返回给前端的字段信息列表
            List<Dictionary<string, object>> fieldGroups = new List<Dictionary<string, object>>();

            //获取所有的单据体信息
            if (bizField.Id.EqualsIgnoreCase("factiveentitykey")
                || bizField.Id.EqualsIgnoreCase("ftargetentitykey"))
            {
                var items = new List<Dictionary<string, string>>();
                items.Add(new Dictionary<string, string>
                {
                    { "id", bizForm.HeadEntity.Id },
                    { "name", bizForm.HeadEntity.Caption }
                });

                var entrys = bizForm.EntryList.OrderBy(o => o.ElementType);
                foreach (var entry in entrys)
                {
                    items.Add(new Dictionary<string, string>
                    {
                        { "id", entry.Id },
                        { "name", entry.Caption }
                    });
                }
                fieldGroups.Add(new Dictionary<string, object>
                {
                    { "group", bizForm.Caption },
                    { "fields", items }
                });
                return fieldGroups;
            }

            List<HtmlField> htmlFieldList = bizForm.GetFieldList().ToList();
            if (htmlFieldList != null)
            {
                //始终都要排除的字段
                List<string> excludeFields = new List<string>()
                {
                    "fmainorgid", "fbizruleid", "fname_py", "fname_py2", "fflowinstanceid",
                    "ftranid", "ffromtranid", "froottranid", "fnextprocnode", "fpublishcid",
                    "fdataorigin", "fparenttranid", "ftoptranid", "fchaindataid", "ffromchaindataid",
                    "fprimitiveid"
                };

                //根据字段的 group 分组
                IEnumerable<IGrouping<string, HtmlField>> groups = htmlFieldList
                    .Where(t => !excludeFields.Contains(t.Id.ToLower()))
                    .GroupBy(t => t.Group, t => t);
                foreach (IGrouping<string, HtmlField> group in groups)
                {
                    List<Dictionary<string, string>> fields = new List<Dictionary<string, string>>();
                    List<HtmlField> htmlFields = group.OrderBy(t => t.TabIndex).ToList();
                    foreach (var htmlField in htmlFields)
                    {
                        //添加一个单据内码字段（主键ID）
                        if (htmlField.Id.EqualsIgnoreCase(bizForm.NumberFldKey))
                        {
                            fields.Add(new Dictionary<string, string>
                            {
                                { "id", "fbillhead.id" },
                                { "name", $"{bizForm.Caption}内码" }
                            });
                        }

                        //添加一个明细内码字段（明细主键ID）
                        if (!htmlField.IsBillHeadField)
                        {
                            var entryPkid = $"{htmlField.EntityKey}.id";
                            if(fields.FirstOrDefault(o => o["id"].EqualsIgnoreCase(entryPkid)) == null)
                            {
                                fields.Add(new Dictionary<string, string>
                                {
                                    { "id", entryPkid },
                                    { "name", $"{htmlField.Entity.Caption}内码" }
                                });
                            }
                        }

                        fields.Add(new Dictionary<string, string>
                        {
                            { "id", htmlField.Id },
                            { "name", htmlField.Caption }
                        });
                    }

                    fieldGroups.Add(new Dictionary<string, object>
                    {
                        { "group", group.Key },
                        { "fields", fields }
                    });
                }
            }

            return fieldGroups;
        }
    }
}