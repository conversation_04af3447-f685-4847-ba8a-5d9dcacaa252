using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Serialization;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.SystemPlugIn.SI.OperationLog
{
    /// <summary>
    /// 销售合同：查询数据
    /// </summary>
    [InjectService]
    [FormId("si_operationlog")]
    [OperationNo("QueryData")]
    public class QueryData : AbstractOperationServicePlugIn
    {

        private DynamicObject _customFilterObj;
        private HtmlForm _customFilterForm;

        /// <summary>
        /// 获取自定搜索页面
        /// </summary>
        protected DynamicObject CustomFilterObject
        {
            get
            {
                if (_customFilterObj == null)
                {
                    var customFilter = this.GetQueryOrSimpleParam<string>("__customFilter__", "");
                    JArray jObjs = new JArray();
                    if (!customFilter.IsNullOrEmptyOrWhiteSpace())
                    {
                        var jObjCustomFilter = JObject.Parse(customFilter);
                        jObjs.Add(jObjCustomFilter);
                    }

                    if (this.CustomFilterFormMeta.IsNullOrEmpty()) return null;
                    var dtCustomFilterForm = this.CustomFilterFormMeta.GetDynamicObjectType(this.Context);
                    var defCalulator = this.Container.GetService<IDefaultValueCalculator>();

                    if (jObjs.Any())
                    {
                        List<DynamicObject> lstBillObjs = new List<DynamicObject>();
                        var dcSerializer = this.Container.GetService<IDynamicSerializer>();
                        dcSerializer.Sync(dtCustomFilterForm, lstBillObjs, jObjs, (propKey) =>
                        {
                            var el = this.OperationContext.HtmlForm?.GetElement(propKey);
                            if (el is HtmlField) return (el as HtmlField).DynamicProperty;
                            if (el is HtmlEntryEntity) return (el as HtmlEntryEntity).DynamicProperty;

                            return null;
                        },
                         null,
                         null,
                         null);

                        _customFilterObj = lstBillObjs.FirstOrDefault();
                    }

                    if (_customFilterObj == null)
                    {
                        _customFilterObj = dtCustomFilterForm.CreateInstance() as DynamicObject;
                        defCalulator.Execute(this.Context, this.CustomFilterFormMeta, new DynamicObject[] { _customFilterObj });
                    }

                    this.Container.GetService<LoadReferenceObjectManager>()?.Load(this.Context, _customFilterObj.DynamicObjectType, _customFilterObj, false);

                }
                return _customFilterObj;
            }
        }

        /// <summary>
        /// 自定义过滤页面模型
        /// </summary>
        protected HtmlForm CustomFilterFormMeta
        {
            get
            {
                if (_customFilterForm.IsNullOrEmpty())
                {
                    var customFilterForm = this.HtmlForm.CustomFilterForm;
                    if (customFilterForm.IsNullOrEmptyOrWhiteSpace()) return null;

                    _customFilterForm = this.MetaModelService.LoadFormModel(this.Context, customFilterForm);
                }
                return _customFilterForm;
            }
        }
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);
            switch (e.EventName)
            {
                case OnCustomServiceEventArgs.PrepareQueryBuilderParameter:
                    var para = e.EventData as SqlBuilderParameter;
                    prepareQueryBuilderParameter(para);
                    break;
            }
        }

        private void prepareQueryBuilderParameter(SqlBuilderParameter para)
        {
            StringBuilder sb = new StringBuilder();
            DateTime? dtStart;
            DateTime? dtEnd;
            this.TryGetAcceptDateDate(out dtStart, out dtEnd);

            var fmyobjectid= Convert.ToString(this.CustomFilterObject["fmyobjectid"]);
            if (!fmyobjectid.IsNullOrEmptyOrWhiteSpace())
            {
                sb.Append(" and fmyobjectid=@fmyobjectid ");
                para.AddParameter(new SqlParam("@fmyobjectid", System.Data.DbType.String, fmyobjectid));
            }

            var fnumbers = Convert.ToString(this.CustomFilterObject["fnumbers"]);
            if (!fnumbers.IsNullOrEmptyOrWhiteSpace())
            {
                sb.Append(" and (fsuccessnumbers=@fnumbers or ffailnumbers=@fnumbers)");
                para.AddParameter(new SqlParam("@fnumbers", System.Data.DbType.String, fnumbers));
            }

            var fdescription= Convert.ToString(this.CustomFilterObject["fdescription"]);
            if (!fdescription.IsNullOrEmptyOrWhiteSpace())
            {
                sb.Append($" and fdescription like '%{fdescription}%'");
                para.AddParameter(new SqlParam("@fdescription", System.Data.DbType.String, fdescription));
            }

            if (dtStart != null)
            {
                sb.Append(" and fcreatedate>=@fdatefrom ");
                para.AddParameter(new SqlParam("@fdatefrom", System.Data.DbType.DateTime, dtStart.Value));
            }

            if (dtEnd != null)
            {
                sb.Append(" and fcreatedate < @fdateto ");
                para.AddParameter(new SqlParam("@fdateto", System.Data.DbType.DateTime, dtEnd.Value));
            }

            var filterString = sb.ToString().TrimStart();
            if (filterString.StartsWith("and", StringComparison.OrdinalIgnoreCase))
            {
                filterString = filterString.Substring(3);
            }
            else if (filterString.StartsWith("or", StringComparison.OrdinalIgnoreCase))
            {
                filterString = filterString.Substring(2);
            }
            para.FilterString = para.FilterString.JoinFilterString(filterString);
        }

        /// <summary>
        /// 获得当前受理期间信息
        /// </summary>
        /// <param name="dtStart"></param>
        /// <param name="dtEnd"></param>
        protected void TryGetAcceptDateDate(out DateTime? dtStart, out DateTime? dtEnd)
        {
            dtStart = (DateTime?)this.CustomFilterObject["fdatefrom"];
            if (dtStart.HasValue)
            {
                dtStart = dtStart.Value.DayBegin();
            }
            dtEnd = (DateTime?)this.CustomFilterObject["fdateto"];
            if (dtEnd.HasValue)
            {
                dtEnd = dtEnd.Value.DayEnd();
            }
        }
    }
}
