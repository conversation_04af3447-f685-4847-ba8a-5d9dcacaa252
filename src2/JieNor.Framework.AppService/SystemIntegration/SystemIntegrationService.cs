using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface.SystemIntegration;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.Framework.AppService.SystemIntegration
{
    /// <summary>
    /// 系统集成服务实现
    /// </summary>
    [InjectService]
    public class SystemIntegrationService : ISystemIntegrationService
    {
        /// <summary>
        /// 创建数据同步结果实体
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="dataSyncResultHtmlForm">数据同步结果表单</param>
        /// <param name="syncHtmlForm">同步业务对象表单</param>
        /// <param name="syncDataEntity">同步业务对象实体</param>
        /// <param name="extAppId">外部应用id</param>
        /// <param name="option">操作额外选项</param>
        /// <returns></returns>
        public DynamicObject CreateDataSyncResult(UserContext userCtx, HtmlForm dataSyncResultHtmlForm, HtmlForm syncHtmlForm, DynamicObject syncDataEntity,
            string extAppId, OperateOption option = null)
        {
            var slaveCtx = userCtx.CreateSlaveDBContext();

            var synLogObj = (DynamicObject)dataSyncResultHtmlForm.GetDynamicObjectType(slaveCtx).CreateInstance();
            synLogObj["fextappid"] = extAppId;
            synLogObj["fmyobjectid"] = syncHtmlForm.Id;

            synLogObj["fsyncstatus"] = "1";     //待处理
            if (syncDataEntity == null)
            {
                synLogObj["fsynctime"] = DateTime.Now;
            }
            else
            {
                synLogObj["fsynctime"] = syncHtmlForm.GetField("fmodifydate")?.DynamicProperty?.GetValue<DateTime?>(syncDataEntity) ?? DateTime.Now;
                synLogObj["fbillid"] = syncDataEntity["id"];
                synLogObj["fbillnumber"] = syncHtmlForm.GetNumberField()?.GetDisplayValue(slaveCtx, syncHtmlForm, syncDataEntity, option) ?? "N/A";
            }

            return synLogObj;
        }

        /// <summary>
        /// 保存数据同步结果到数据库
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="dataSyncResultHtmlForm">数据同步结果表单</param>
        /// <param name="dataEntities">数据同步结果实体列表</param>
        public void SaveDataSyncResult(UserContext userCtx, HtmlForm dataSyncResultHtmlForm, DynamicObject[] dataEntities, OperateOption option = null)
        {
            SaveDataSyncResultAsync(userCtx, dataSyncResultHtmlForm, dataEntities, option);
        }

        /// <summary>
        /// 保存数据同步结果到数据库
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="dataSyncResultHtmlForm">数据同步结果表单</param>
        /// <param name="dataEntities">数据同步结果实体列表</param>
        public async Task SaveDataSyncResultAsync(UserContext userCtx, HtmlForm dataSyncResultHtmlForm, DynamicObject[] dataEntities, OperateOption option = null)
        {
            if (dataEntities.IsNullOrEmpty()) return;

            var task = new Task(() =>
            {
                var slaveCtx = userCtx.CreateSlaveDBContext();

                var prepareService = slaveCtx.Container.GetService<IPrepareSaveDataService>();
                prepareService.PrepareDataEntity(slaveCtx, dataSyncResultHtmlForm, dataEntities, option);

                var dm = slaveCtx.Container.GetService<IDataManager>();
                dm.InitDbContext(slaveCtx, dataSyncResultHtmlForm.GetDynamicObjectType(slaveCtx));
                dm.Save(dataEntities);
            });

            ThreadWorker.QuequeTask(task, result =>
            {
                result.HandleError();
            });

            await Task.CompletedTask;
        }

        /// <summary>
        /// 写数据同步结果日志明细
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="dataSyncResult">数据同步结果实体</param>
        /// <param name="logContent">日志</param>
        /// <param name="isError">是否出错</param>
        public void WriteDataSyncResult(UserContext userCtx, DynamicObject dataSyncResult, string logContent, bool isError = false)
        {
            if (dataSyncResult == null) return;
            var logEntryObjs = dataSyncResult["fentity"] as DynamicObjectCollection;
            if (logEntryObjs == null) return;
            if (isError)
            {
                dataSyncResult["fsyncstatus"] = "3";
            }
            var logEntryObj = (DynamicObject)logEntryObjs.DynamicCollectionItemPropertyType.CreateInstance();
            logEntryObjs.Add(logEntryObj);
            //logEntryObj["Id"] = Guid.NewGuid().ToString();
            logEntryObj["flogtime"] = DateTime.Now;
            logEntryObj["flogsource"] = "1";
            logEntryObj["flogcontent"] = logContent;
        }

        /// <summary>
        /// 获取外部应用产品参数
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="thirdSysId"></param>
        /// <returns></returns>
        public DynamicObject GetExternalAppObject(UserContext userCtx, string thirdSysId, OperateOption option = null)
        {
            return userCtx.LoadBizDataById("sys_externalapp", thirdSysId);
        }

        /// <summary>
        /// 创建操作日志实体
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">操作日志表单</param>
        /// <param name="opApi">操作接口</param>
        /// <param name="opName">操作名称</param>
        /// <param name="opFormId">操作业务对象</param>
        /// <param name="opLogSource">操作日志来源，1：我方；2：对方</param>
        /// <param name="description">描述</param>
        /// <returns></returns>
        public DynamicObject CreateOperationLog(UserContext userCtx, HtmlForm htmlForm, string opApi, string opName, string opFormId, string opLogSource = "1", string description = null)
        {
            var slaveCtx = userCtx.CreateSlaveDBContext();

            var opLogObj = (DynamicObject)htmlForm.GetDynamicObjectType(slaveCtx).CreateInstance();

            opLogObj["fopstatus"] = "1";     //待处理
            opLogObj["foptime"] = DateTime.Now;
            opLogObj["fopapi"] = opApi;
            opLogObj["fopname"] = opName;
            opLogObj["fmyobjectid"] = opFormId;
            opLogObj["foplogsource"] = opLogSource;
            opLogObj["fdescription"] = description;
            opLogObj["fmainorgid"] = slaveCtx.TopCompanyId;  // 记录到总部

            return opLogObj;
        }

        /// <summary>
        /// 写操作日志明细
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="opLog">操作日志实体</param>
        /// <param name="logContent">日志</param>
        /// <param name="isError">是否出错</param>
        public void WriteOperationLog(UserContext userCtx, DynamicObject opLog, string logContent, bool isError = false)
        {
            if (opLog == null) return;
            var logEntryObjs = opLog["fentity"] as DynamicObjectCollection;
            if (logEntryObjs == null) return;
            if (isError)
            {
                opLog["fopstatus"] = "3";
            }
            var logEntryObj = (DynamicObject)logEntryObjs.DynamicCollectionItemPropertyType.CreateInstance();
            logEntryObjs.Add(logEntryObj);
            logEntryObj["flogtime"] = DateTime.Now;
            logEntryObj["flogcontent"] = logContent;
        }

        /// <summary>
        /// 保存操作日志到数据库
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">操作日志表单</param>
        /// <param name="dataEntities">操作日志实体列表</param>
        /// <param name="option">操作额外选项</param>
        public void SaveOperationLog(UserContext userCtx, HtmlForm htmlForm, DynamicObject[] dataEntities, OperateOption option = null)
        {
            SaveOperationLogAsync(userCtx, htmlForm, dataEntities, option);
        }

        /// <summary>
        /// 保存操作日志到数据库
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">操作日志表单</param>
        /// <param name="dataEntities">操作日志实体列表</param>
        /// <param name="option">操作额外选项</param>
        public async Task SaveOperationLogAsync(UserContext userCtx, HtmlForm htmlForm, DynamicObject[] dataEntities, OperateOption option = null)
        {
            if (dataEntities.IsNullOrEmpty()) return;

            var task = new Task(() =>
            {
                var slaveCtx = userCtx.CreateSlaveDBContext();

                try
                {
                    // 限制字段长度
                    foreach (var dataEntity in dataEntities)
                    {
                        var fieldIds = new[] { "fsuccessnumbers", "ffailnumbers", "fdescription" };
                        foreach (var fieldId in fieldIds)
                        {
                            var field = htmlForm.GetField(fieldId);
                            var len = field.Length;
                            string value = Convert.ToString(dataEntity[field.PropertyName]);
                            if (value.Length > len)
                            {
                                value = value.Substring(0, len);
                            }

                            dataEntity[field.PropertyName] = value;
                        }
                    }

                    var prepareService = slaveCtx.Container.GetService<IPrepareSaveDataService>();
                    prepareService.PrepareDataEntity(slaveCtx, htmlForm, dataEntities, option);

                    var dm = slaveCtx.Container.GetService<IDataManager>();
                    dm.InitDbContext(slaveCtx, htmlForm.GetDynamicObjectType(slaveCtx));
                    dm.Save(dataEntities);
                }
                catch (Exception e)
                {
                    var logService = slaveCtx.Container.GetService<ILogServiceEx>();
                    logService.Error("保存集成操作日志失败", e);

                    // 加入重试队列
                    slaveCtx.Container.GetService<IRetrySaveService>().Enqueue(slaveCtx, htmlForm, dataEntities, TimeSpan.FromDays(30));
                    slaveCtx.Container.GetService<ILogService>().WriteLogToFile(dataEntities?.ToJson(), "retry/" + (htmlForm.Id?.ToLower() ?? "unknown"));
                }
            });

            ThreadWorker.QuequeTask(task, result =>
            {
                result.HandleError(); 
            });

            await Task.CompletedTask;
        }
    }
}
