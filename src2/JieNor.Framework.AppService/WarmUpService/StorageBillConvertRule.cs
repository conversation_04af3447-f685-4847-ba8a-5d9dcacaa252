//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.IO;
//using System.Threading;
//using System.Threading.Tasks;
//using JieNor.Framework.Interface;
//using JieNor.Framework.IoC;
//using JieNor.Framework.MetaCore.FormMeta;
//using JieNor.Framework.SuperOrm.DataEntity;
//using JieNor.Framework.Interface.Log;
//using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
//using JieNor.Framework.SuperOrm.DataManager;
//using JieNor.Framework.SuperOrm;

//namespace JieNor.Framework.AppService.WarmUpService
//{
//    /// <summary>
//    /// 存储单据转换规则信息至数据库中
//    /// </summary>
//    [InjectService]
//    public class StorageBillConvertRule : IWarmUpService
//    {
//        /// <summary>
//        /// 是否已运行过一次
//        /// </summary>
//        private bool IsRunOnce { get; set; }

//        /// <summary>
//        /// 日志服务
//        /// </summary>
//        [InjectProperty]
//        private ILogServiceEx LogEx { get; set; }

//        /// <summary>
//        /// 表单模型服务
//        /// </summary>
//        [InjectProperty]
//        private MetaModelService MetaModelService { get; set; }

//        /// <summary>
//        /// 单据转换规则模型
//        /// </summary>
//        private HtmlForm BillCvtRuleForm { get; set; }

//        /// <summary>
//        /// 执行热身服务逻辑
//        /// </summary>
//        /// <param name="userCtx"></param>
//        public void Execute(UserContext userCtx)
//        {
//            //该热身服务只执行一次
//            if (this.IsRunOnce) return;
//            this.IsRunOnce = true;

//            this.LogEx.Info("热身服务开始自动更新单据转换规则信息至数据库。");

//            try
//            {
//                this.BillCvtRuleForm = this.MetaModelService.LoadFormModel(userCtx, "sys_billconvertrule");

//                var dm = userCtx.Container.GetService<IDataManager>();
//                dm.InitDbContext(userCtx, this.BillCvtRuleForm.GetDynamicObjectType(userCtx));

//                var ruleDynObjs = new List<DynamicObject>();
//                var dicFilePath = new Dictionary<string, List<string>>();

//                var filePaths = PathUtils.SearchWebFiles("/mdl", "*.cvt.json")?.ToList() ?? new List<string>();
//                foreach (var filePath in filePaths)
//                {
//                    var fileName = Path.GetFileName(filePath);
//                    var ruleId = fileName?.Replace(".cvt.json", "");
//                    if (ruleId.IsNullOrEmptyOrWhiteSpace()) continue;

//                    if (!dicFilePath.ContainsKey(ruleId))
//                    {
//                        dicFilePath[ruleId] = new List<string>();
//                    }
//                    dicFilePath[ruleId].Add(filePath);
//                }

//                var repetFiles = dicFilePath.Where(o => o.Value.Count > 1).ToList();
//                if (repetFiles.Count > 0)
//                {
//                    var sbRepetInfo = new StringBuilder("系统中存在重复的单据转换规则文件：").AppendLine();
//                    foreach (var item in repetFiles)
//                    {
//                        sbRepetInfo.Append($"单据转换规则Id：{item.Key}").AppendLine();
//                        foreach (var path in item.Value)
//                        {
//                            sbRepetInfo.Append(path).AppendLine();
//                        }
//                    }
//                    this.LogEx.Error(sbRepetInfo);
//                }

//                var deleteRuleIds = new List<string>();
//                var tempRuleIds = new List<string>();
//                foreach (var ruleId in dicFilePath.Keys)
//                {
//                    //此处始终加载系统预置的单据转换规则（排除企业自定义的规则文件）
//                    var rule = this.MetaModelService.LoadConvertRule(userCtx, ruleId, false);
//                    if (rule == null) continue;

//                    //规则Id中如果存在有效的企业标识，则说明是用户自定义的规则文件，此类规则文件不需要处理
//                    var ruleCompanyId = "";
//                    var strRuleIds = ruleId.Split('.');
//                    if (strRuleIds.Length > 0)
//                    {
//                        ruleCompanyId = strRuleIds[strRuleIds.Length - 1].Trim();
//                    }
//                    long intCompanyId = 0;
//                    long.TryParse(ruleCompanyId, out intCompanyId);
//                    if (ruleCompanyId.Length == 18 && intCompanyId > 0) continue;

//                    if (!rule.Id.EqualsIgnoreCase(ruleId))
//                    {
//                        this.LogEx.Error($"单据转换规则文件名与规则Id不一致，规则文件名：{ruleId}，规则Id：{rule.Id}");
//                        continue;
//                    }
//                    if (tempRuleIds.Contains(rule.Id))
//                    {
//                        this.LogEx.Error($"单据转换规则Id重复，规则Id：{rule.Id}");
//                        continue;
//                    }
//                    tempRuleIds.Add(rule.Id);

//                    //记录需要删除的规则
//                    if (!rule.Visible || rule.SourceFormId.IsNullOrEmptyOrWhiteSpace() || rule.TargetFormId.IsNullOrEmptyOrWhiteSpace())
//                    {
//                        deleteRuleIds.Add(rule.Id);
//                        continue;
//                    }
//                    HtmlForm sourceForm = null;
//                    try
//                    {
//                        sourceForm = this.MetaModelService.LoadFormModel(userCtx, rule.SourceFormId);
//                    }
//                    catch (Exception ex)
//                    {
//                        this.LogEx.Error($"加载单据转换规则源单模型出错，规则Id：{rule.Id}，源表单Id：{rule.SourceFormId}", ex);
//                    }
//                    HtmlForm targetForm = null;
//                    try
//                    {
//                        targetForm = this.MetaModelService.LoadFormModel(userCtx, rule.TargetFormId);
//                    }
//                    catch (Exception ex)
//                    {
//                        this.LogEx.Error($"加载单据转换规则目标单模型出错，规则Id：{rule.Id}，目标表单Id：{rule.TargetFormId}", ex);
//                    }
//                    if (sourceForm == null || targetForm == null)
//                    {
//                        deleteRuleIds.Add(rule.Id);
//                        continue;
//                    }
//                    var ruleDynObj = this.BuildBillConvertRuleDynObj(userCtx, dm, rule, sourceForm, targetForm);
//                    if (ruleDynObj != null)
//                    {
//                        ruleDynObjs.Add(ruleDynObj);
//                    }
//                }

//                if (ruleDynObjs.Count > 0)
//                {
//                    var preService = userCtx.Container.GetService<IPrepareSaveDataService>();
//                    preService.PrepareDataEntity(userCtx, this.BillCvtRuleForm, ruleDynObjs.ToArray(), OperateOption.Create());

//                    dm.Save(ruleDynObjs);
//                }

//                //删除无用规则信息
//                if (deleteRuleIds.Count > 0)
//                {
//                    dm.Delete(deleteRuleIds);
//                }
//            }
//            catch (Exception ex)
//            {
//                this.LogEx.Error($"更新单据转换规则信息失败", ex);
//            }
//        }

//        /// <summary>
//        /// 更新单据转换规则信息
//        /// </summary>
//        /// <param name="userCtx"></param>
//        /// <param name="dm"></param>
//        /// <param name="rule"></param>
//        /// <param name="sourceForm"></param>
//        /// <param name="targetForm"></param>
//        /// <returns></returns>
//        private DynamicObject BuildBillConvertRuleDynObj(UserContext userCtx, IDataManager dm, ConvertRule rule, HtmlForm sourceForm, HtmlForm targetForm)
//        {
//            DynamicObject ruleDynObj = null;

//            try
//            {
//                //查询是否已经存在
//                var reader = userCtx.GetPkIdDataReader(this.BillCvtRuleForm, 
//                    $"fid=@fid and fmainorgid='0'",
//                    new List<SqlParam>
//                    {
//                        new SqlParam("@fid", System.Data.DbType.String, rule.Id)
//                    });
//                ruleDynObj = dm.SelectBy(reader).OfType<DynamicObject>()?.FirstOrDefault();
//                if (ruleDynObj == null)
//                {
//                    //不存在则新增
//                    ruleDynObj = new DynamicObject(this.BillCvtRuleForm.GetDynamicObjectType(userCtx));
//                    ruleDynObj["id"] = rule.Id;
//                    ruleDynObj["fnumber"] = rule.Id;
//                }
//                ruleDynObj["fmainorgid"] = "0";
//                ruleDynObj["fispreset"] = "1";
//                ruleDynObj["fdescription"] = "系统预置的单据转换规则";
//                ruleDynObj["fname"] = rule.Name;
//                ruleDynObj["fsourceformid"] = rule.SourceFormId;
//                ruleDynObj["ftargetformid"] = rule.TargetFormId;
//                ruleDynObj["fconvertway"] = rule.ConvertWay;
//                ruleDynObj["ffilterstring"] = rule.FilterString;
//                ruleDynObj["fmessage"] = rule.Message;

//                HtmlEntity activeEntity = null;
//                var activeEntityKey = rule.ActiveEntityKey;
//                if (activeEntityKey.IsNullOrEmptyOrWhiteSpace()) activeEntityKey = "fbillhead";
//                if (activeEntityKey.EqualsIgnoreCase("fbillhead"))
//                {
//                    activeEntity = sourceForm.HeadEntity;
//                }
//                else
//                {
//                    activeEntity = sourceForm.GetEntryEntity(activeEntityKey);
//                }
//                ruleDynObj["factiveentitykey"] = activeEntityKey;
//                ruleDynObj["factiveentitykey_txt"] = activeEntity?.Caption ?? "";

//                ruleDynObj["fcontrolfieldkey"] = rule.ControlFieldKey;
//                ruleDynObj["fcontrolfieldkey_txt"] = targetForm.GetField(rule.ControlFieldKey)?.Caption ?? "";

//                ruleDynObj["fsourcecontrolfieldkey"] = rule.SourceControlFieldKey;
//                ruleDynObj["fsourcecontrolfieldkey_txt"] = sourceForm.GetField(rule.SourceControlFieldKey)?.Caption ?? "";

//                ruleDynObj["frelationfieldkey"] = rule.RelationFieldKey;
//                ruleDynObj["frelationfieldkey_txt"] = targetForm.GetField(rule.RelationFieldKey)?.Caption ?? "";

//                ruleDynObj["frealtionformidfieldkey"] = rule.RealtionFormIdFieldKey;
//                ruleDynObj["frealtionformidfieldkey_txt"] = targetForm.GetField(rule.RealtionFormIdFieldKey)?.Caption ?? "";

//                var seqService = userCtx.Container.GetService<ISequenceService>();

//                #region 单据类型映射
//                var billTypeMapEntity = this.BillCvtRuleForm.GetEntryEntity("fbilltypemap");
//                var billTypeMaps = billTypeMapEntity.DynamicProperty.GetValue<DynamicObjectCollection>(ruleDynObj);
//                billTypeMaps.Clear();
//                foreach (var item in rule.BillTypeMappings)
//                {
//                    if (!item.SourceBillTypeId.IsNullOrEmptyOrWhiteSpace() && !item.TargetBillTypeId.IsNullOrEmptyOrWhiteSpace())
//                    {
//                        var billTypeMap = new DynamicObject(billTypeMapEntity.DynamicObjectType);
//                        billTypeMap["id"] = seqService.GetSequence<string>();
//                        billTypeMap["fseq"] = billTypeMaps.Count + 1;
//                        billTypeMap["fsourcebilltypeid"] = item.SourceBillTypeId;
//                        billTypeMap["ftargetbilltypeid"] = item.TargetBillTypeId;
//                        billTypeMaps.Add(billTypeMap);
//                    }
//                }
//                #endregion

//                #region 字段映射
//                var fieldMapEntity = this.BillCvtRuleForm.GetEntryEntity("ffieldmap");
//                var fieldMaps = fieldMapEntity.DynamicProperty.GetValue<DynamicObjectCollection>(ruleDynObj);
//                fieldMaps.Clear();
//                var fieldMapCount = 5;
//                foreach (var item in rule.FieldMappings)
//                {
//                    if (!item.SrcFieldId.IsNullOrEmptyOrWhiteSpace() && !item.Id.IsNullOrEmptyOrWhiteSpace())
//                    {
//                        var targetField = targetForm.GetField(item.Id);
//                        if (targetField == null) continue;

//                        var fieldMap = new DynamicObject(fieldMapEntity.DynamicObjectType);
//                        fieldMap["id"] = seqService.GetSequence<string>();
//                        fieldMap["fseq"] = fieldMaps.Count + 1;
//                        fieldMap["ftargetfieldid"] = item.Id;
//                        fieldMap["ftargetfieldid_txt"] = targetField?.Caption ?? "";
//                        if (item.MapType == (int)Enu_FieldMapType.Expression)
//                        {
//                            fieldMap["fsrcfieldexpr"] = item.SrcFieldId;
//                            fieldMap["fsrcfieldexpr_txt"] = item.SrcFieldId;
//                        }
//                        else
//                        {
//                            var _txt = sourceForm.GetField(item.SrcFieldId)?.Caption ?? "";
//                            if (item.SrcFieldId.EqualsIgnoreCase("fbillhead.id"))
//                            {
//                                _txt = $"{sourceForm.Caption}内码";
//                            }
//                            else
//                            {
//                                if (item.SrcFieldId.EndsWithIgnoreCase(".id"))
//                                {
//                                    var entityKey = item.SrcFieldId.Replace(".id", "");
//                                    var entity = sourceForm.GetEntryEntity(entityKey);
//                                    if (entity != null)
//                                    {
//                                        _txt = $"{entity.Caption}内码";
//                                    }
//                                }
//                            }
//                            fieldMap["fsrcfieldid"] = item.SrcFieldId;
//                            fieldMap["fsrcfieldid_txt"] = _txt;
//                        }
//                        fieldMap["fmaptype"] = item.MapType;
//                        fieldMap["fmapgroup"] = item.MapActionWhenGrouping;
//                        fieldMap["forder"] = item.Order <= 0 ? fieldMapCount : item.Order;
//                        fieldMap["fallowfilter"] = item.AllowFilter;
//                        fieldMaps.Add(fieldMap);

//                        fieldMapCount += 5;
//                    }
//                }
//                #endregion

//                #region 来源数据分单规则
//                var billGroupEntity = this.BillCvtRuleForm.GetEntryEntity("fbillgroup");
//                var billGroups = billGroupEntity.DynamicProperty.GetValue<DynamicObjectCollection>(ruleDynObj);
//                billGroups.Clear();
//                var billGroupCount = 5;
//                foreach (var item in rule.BillGroups)
//                {
//                    if (!item.Id.IsNullOrEmptyOrWhiteSpace())
//                    {
//                        var field = sourceForm.GetField(item.Id);
//                        if (field == null) continue;

//                        var billGroup = new DynamicObject(billGroupEntity.DynamicObjectType);
//                        billGroup["id"] = seqService.GetSequence<string>();
//                        billGroup["fseq"] = billGroups.Count + 1;
//                        billGroup["fgroupfieldid"] = item.Id;
//                        billGroup["fgroupfieldid_txt"] = field?.Caption ?? "";
//                        billGroup["forder"] = item.Order <= 0 ? billGroupCount : item.Order;
//                        billGroups.Add(billGroup);

//                        billGroupCount += 5;
//                    }
//                }
//                #endregion

//                #region 来源数据按单分组规则
//                var fieldGroupEntity = this.BillCvtRuleForm.GetEntryEntity("ffieldgroup");
//                var fieldGroups = fieldGroupEntity.DynamicProperty.GetValue<DynamicObjectCollection>(ruleDynObj);
//                fieldGroups.Clear();
//                var fieldGroupCount = 5;
//                foreach (var item in rule.FieldGroups)
//                {
//                    if (!item.Id.IsNullOrEmptyOrWhiteSpace())
//                    {
//                        var field = sourceForm.GetField(item.Id);
//                        if (field == null) continue;

//                        HtmlEntity targetEntity = null;
//                        var targetEntityKey = item.TargetEntityKey;
//                        if (!targetEntityKey.IsNullOrEmptyOrWhiteSpace())
//                        {
//                            if (targetEntityKey.EqualsIgnoreCase("fbillhead"))
//                            {
//                                targetEntity = targetForm.HeadEntity;
//                            }
//                            else
//                            {
//                                targetEntity = targetForm.GetEntryEntity(targetEntityKey);
//                            }
//                        }
//                        if (targetEntity == null) targetEntityKey = "";

//                        var fieldGroup = new DynamicObject(fieldGroupEntity.DynamicObjectType);
//                        fieldGroup["id"] = seqService.GetSequence<string>();
//                        fieldGroup["fseq"] = fieldGroups.Count + 1;
//                        fieldGroup["fgroupfieldid"] = item.Id;
//                        fieldGroup["fgroupfieldid_txt"] = field?.Caption ?? "";
//                        fieldGroup["ftargetentitykey"] = targetEntityKey;
//                        fieldGroup["ftargetentitykey_txt"] = targetEntity?.Caption ?? "";
//                        fieldGroup["forder"] = item.Order <= 0 ? fieldGroupCount : item.Order;
//                        fieldGroups.Add(fieldGroup);

//                        fieldGroupCount += 5;
//                    }
//                }
//                #endregion
//            }
//            catch (Exception ex)
//            {
//                this.LogEx.Error($"创建单据转换规则动态对象出错，规则Id：{rule.Id}", ex);
//            }

//            return ruleDynObj;
//        }
//    }
//}