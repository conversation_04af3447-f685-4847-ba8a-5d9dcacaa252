using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;


namespace JieNor.Framework.AppService.Validation
{
    /// <summary>
    /// 基础资料删除操作校验器
    /// 检查基础资料是否是预设数据，如果是预设的数据，不允许删除
    /// </summary>
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    [ServiceMetaAttribute("validationid", HtmlElementType.HtmlValidator_BDPrepareValidation)]
    public class BDPrepareValidation : AbstractBaseValidation
    {


        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }

            set
            {
                base.EntityKey = value;
            }
        }


        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }

        /// <summary>
        /// 校验逻辑处理单元
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            var preFld = formInfo.GetField("fispreset");
            if (formInfo.ElementType != HtmlElementType.HtmlForm_BaseForm || preFld == null)
            {
                return result;
            }
            var noFld = formInfo.GetField(formInfo.NumberFldKey);
            foreach (var entity in dataEntities)
            {
                var val = preFld.DynamicProperty.GetValue<bool>(entity);
                if (!val)
                {
                    continue;
                }

                var err = string.Format("编码为【{0}】的 {1}，是系统预设数据，不允许删除！",
                                        noFld?.DynamicProperty?.GetValue<string>(entity),
                                        formInfo.Caption);
                result.Errors.Add(new ValidationResultEntry()
                {
                    ErrorMessage = err,
                    DataEntity = entity
                });
            }

            return result;
        }

 



    }
}