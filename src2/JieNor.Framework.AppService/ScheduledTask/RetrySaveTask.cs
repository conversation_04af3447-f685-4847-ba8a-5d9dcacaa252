using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;

namespace JieNor.Framework.AppService.ScheduledTask
{
    /// <summary>
    /// 重试保存任务
    /// </summary>
    [InjectService]
    [TaskSvrId("RetrySaveTask")]
    [Caption("重试保存任务")]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public class RetrySaveTask : AbstractScheduleWorker
    {
        /// <summary>
        /// 计划任务配置时的参数设置界面
        /// </summary>
        public override string ParamFormId => "sys_retrysavetaskparam";

        /// <summary>
        /// 计划同步任务参数
        /// </summary>
        protected Dictionary<string, object> JobParameter { get; set; }

        protected IRetrySaveService RetrySaveService { get; set; }

        protected override async Task DoExecute()
        {
            this.JobParameter = Convert.ToString(this.TaskObject.Parameter).FromJson<Dictionary<string, object>>() ?? new Dictionary<string, object>();

            this.RetrySaveService = this.UserContext.Container.GetService<IRetrySaveService>();

            var bizFormIds = this.JobParameter.GetString("fbizformlist")?.SplitKey();

            var slaveCtx = this.UserContext.CreateSlaveDBContext();
            this.RetrySaveService.Retry(slaveCtx, bizFormIds, log => this.WriteLog(log));

            this.WriteLog("重试保存完成");
        }
    }
}
