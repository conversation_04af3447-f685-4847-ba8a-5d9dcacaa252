using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.BLA;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.Framework.AppService.ConvertAction
{
    [InjectService]
    public class InitializeAction : BaseConvertAction
    {
        /// <summary>
        /// 输出属性：转换规则
        /// </summary>
        [OutputProperty]
        public ConvertRule ConvertRule { get; private set; }

        /// <summary>
        /// 源单模型
        /// </summary>
        [OutputProperty]
        public HtmlForm SourceHtmlForm { get; set; }

        /// <summary>
        /// 目标单模型
        /// </summary>
        [OutputProperty]
        public HtmlForm TargetHtmlForm { get; set; }

        /// <summary>
        /// 单据转换服务
        /// </summary>
        [InjectProperty]
        protected IConvertService ConvertService { get; set; }

        public override void Execute()
        {
            this.ConvertRule = this.MetaModelService.LoadConvertRule(this.UserContext, this.BillConvertContext.RuleId);
            if (this.BillConvertContext.SourceFormId.IsNullOrEmptyOrWhiteSpace())
            {
                this.BillConvertContext.SourceFormId = this.ConvertRule.SourceFormId;
            }
            if (this.BillConvertContext.TargetFormId.IsNullOrEmptyOrWhiteSpace())
            {
                this.BillConvertContext.TargetFormId = this.ConvertRule.TargetFormId;
            }

            var e = new InitializeServiceEventArgs()
            {
                SourceFormId = this.BillConvertContext.SourceFormId,
                TargetFormId = this.BillConvertContext.TargetFormId,
                UserCtx=this.UserContext,
                Option=this.BillConvertContext.Option,
                Rule = this.ConvertRule
            };
            this.PlugInProxy?.InvokePlugInMethod("InitializeService", e);

            if (e.Rule != null)
            {
                this.ConvertRule = e.Rule;
            }

            this.SourceHtmlForm = this.MetaModelService.LoadFormModel(this.UserContext, this.ConvertRule.SourceFormId);
            this.TargetHtmlForm = this.MetaModelService.LoadFormModel(this.UserContext, this.ConvertRule.TargetFormId);

            this.PreProccessFieldMappings();

            var dm = this.UserContext.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserContext, this.SourceHtmlForm.GetDynamicObjectType(this.UserContext));
            dm.InitDbContext(this.UserContext, this.TargetHtmlForm.GetDynamicObjectType(this.UserContext));

            //自动添加交易号的关联携带
            //自动设置控制字段的映射方式，覆盖模板配置（前提是只要配置了控制字段:ControlFieldKey!=''）
            this.AutoCreateFieldMappings();


            //对单据转换模板进行合法性检查
            this.ConvertService?.ValidAndThrowError(this.UserContext, this.ConvertRule);
        }

        /// <summary>
        /// 预处理字段映射关系
        /// </summary>
        private void PreProccessFieldMappings()
        {
            //排除掉基础资料属性字段的映射
            var mapRemoves = new List<FieldMapObject>();
            foreach (var map in this.ConvertRule.FieldMappings)
            {
                var srcField = this.SourceHtmlForm.GetField(map.SrcFieldId);
                var field = this.SourceHtmlForm.GetField(map.Id);
                if (srcField is HtmlBasePropertyField || field is HtmlBasePropertyField)
                {
                    mapRemoves.Add(map);
                }
            }
            foreach (var map in mapRemoves)
            {
                this.ConvertRule.FieldMappings.Remove(map);
            }
        }

        private void AutoCreateFieldMappings()
        {
            AutoCreateFieldMappings(this.TargetHtmlForm.TranFldKey, this.SourceHtmlForm.TranFldKey);
            AutoCreateFieldMappings(this.TargetHtmlForm.TopTranFldKey, this.SourceHtmlForm.TopTranFldKey);
            AutoCreateFieldMappings(this.TargetHtmlForm.ParentTranFldKey, this.SourceHtmlForm.ParentTranFldKey);
            AutoCreateFieldMappings(this.TargetHtmlForm.DataOriginFldKey, this.SourceHtmlForm.DataOriginFldKey);
            //处理辅助属性扩展自动映射下推
            AutoCreateFieldMappings_AttrExt();
        }

        private void AutoCreateFieldMappings_AttrExt()
        {
            var AttrFields = this.TargetHtmlForm.GetFieldList()?.Where(f =>
            {
                if (f == null || !(f is HtmlAuxPropertyField)) return false;
                var Field = f as HtmlAuxPropertyField;
                return !Field.ControlFieldKey.IsNullOrEmptyOrWhiteSpace();
            }).ToList();
            if (AttrFields == null || AttrFields.Count() <= 0) return;
            foreach (var AttrField in AttrFields)
            {
                var attrField_ext = this.TargetHtmlForm.GetField(AttrField.FieldName + "_e");
                AutoCreateFieldMappings(attrField_ext.FieldName, attrField_ext.FieldName);
            }
        }


        private void AutoCreateFieldMappings(string targetTranFldKey, string sourceTranFldKey)
        {
            if (this.ConvertRule == null) return;

            //自动增加一些内置字段的转换与携带
            //单据头的tranId映射，未配置时自动添加
            var tranFldMap = this.ConvertRule.FieldMappings.FirstOrDefault(o => o.Id.EqualsIgnoreCase(targetTranFldKey));

            if (tranFldMap == null)
            {
                this.ConvertRule.FieldMappings.Add(new FieldMapObject()
                {
                    Id = targetTranFldKey,
                    MapType = (int)Enu_FieldMapType.Default,
                    SrcFieldId = sourceTranFldKey
                });
            }

            Dictionary<string, List<string>> dctEntityKeyMap = new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase);

            foreach (var fldMapObj in this.ConvertRule.FieldMappings)
            {
                if (fldMapObj.MapType != (int)Enu_FieldMapType.Default) continue;

                var targetFldObj = this.TargetHtmlForm.GetField(fldMapObj.Id);
                var sourceFldObj = this.SourceHtmlForm.GetField(fldMapObj.SrcFieldId);
                if (targetFldObj == null || sourceFldObj == null) continue;

                if (targetFldObj.Entity is HtmlHeadEntity) continue;

                List<string> lstMapKeys = new List<string>();
                if (!dctEntityKeyMap.TryGetValue(targetFldObj.EntityKey, out lstMapKeys))
                {
                    lstMapKeys = new List<string>();
                    dctEntityKeyMap[targetFldObj.EntityKey] = lstMapKeys;
                }

                if (!lstMapKeys.Contains(sourceFldObj.EntityKey, StringComparer.OrdinalIgnoreCase))
                {
                    lstMapKeys.Add(sourceFldObj.EntityKey);
                }
            }

            foreach (var kvpItem in dctEntityKeyMap)
            {
                var entityTranId = $"{kvpItem.Key}_{targetTranFldKey}";
                tranFldMap = this.ConvertRule.FieldMappings.FirstOrDefault(o => o.Id.EqualsIgnoreCase(entityTranId));
                if (tranFldMap != null) continue;

                if (kvpItem.Value.Count > 1)
                {
                    foreach (var item in kvpItem.Value.ToArray())
                    {
                        if (item.EqualsIgnoreCase("fbillhead")) kvpItem.Value.Remove(item);
                    }
                }

                if (kvpItem.Value.Count > 1)
                {
                    throw new BusinessException($"表体字段出现映射了多个源单分录体({string.Join(",", kvpItem.Value)})的情况，请明确配置当前分录体的ftranid字段映射来源。");
                }
                if (kvpItem.Value.Count > 0 && !kvpItem.Value[0].IsNullOrEmptyOrWhiteSpace())
                {
                    var srcTranFldKey = sourceTranFldKey;
                    if (!kvpItem.Value[0].EqualsIgnoreCase("fbillhead"))
                    {
                        srcTranFldKey = $"{kvpItem.Value.First()}_{sourceTranFldKey}";
                    }
                    this.ConvertRule.FieldMappings.Add(new FieldMapObject()
                    {
                        Id = entityTranId,
                        MapType = (int)Enu_FieldMapType.Default,
                        SrcFieldId = srcTranFldKey
                    });
                }
            }
        }
    }
}
