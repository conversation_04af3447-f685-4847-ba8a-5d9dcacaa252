using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.BLA;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using System;
using System.Collections.Generic;
using System.Data;

namespace JieNor.Framework.AppService.ConvertAction
{
    /// <summary>
    /// 获取源单数据的取数逻辑单元
    /// </summary>
    [InjectService]
    public class GetSourceDataAction : BaseConvertAction
    {
        /// <summary>
        /// 单据转换规则
        /// </summary>
        [InputProperty, OutputProperty]
        public ConvertRule ConvertRule { get; set; }

        /// <summary>
        /// 输出的源单数据对象
        /// </summary>
        [OutputProperty]
        public Dictionary<HtmlEntity, IEnumerable<Dictionary<string, object>>> SourceDataEntities { get; set; }

        /// <summary>
        /// 源单模型
        /// </summary>
        [InputProperty]
        public HtmlForm SourceHtmlForm { get; set; }

        /// <summary>
        /// 目标单模型
        /// </summary>
        [InputProperty]
        public HtmlForm TargetHtmlForm { get; set; }
        
        /// <summary>
        /// 源单查询对象
        /// </summary>
        [InputProperty]
        public IEnumerable<ConvertSourceQueryObject> SourceQueryObjects { get; set; }
        
        /// <summary>
        /// 执行单据转换取数逻辑
        /// </summary>
        public override void Execute()
        {
            Dictionary<HtmlEntity, IEnumerable<Dictionary<string, object>>> dctAllSourceObjs = new Dictionary<HtmlEntity, IEnumerable<Dictionary<string, object>>>();

            
            int flag = 0;
            foreach (var sourceQuery in this.SourceQueryObjects)
            {
                var be = new BeforeGetSourceBillDataEventArgs()
                {
                    QueryObject = sourceQuery.QueryObject,
                    ActiveQueryEntity = sourceQuery.ActiveQueryEntity,
                    Rule = this.ConvertRule,
                    SourceFormId = this.SourceHtmlForm.Id,
                    TargetFormId = this.TargetHtmlForm.Id,
                    Cancel = false
                };
                this.PlugInProxy.InvokePrimitive(o =>
                {
                    o.BeforeGetSourceBillData(be);
                });

                List<Dictionary<string, object>> lstQueryResult = new List<Dictionary<string, object>>();
                IDataReader dataReader = null;
                if (be.Cancel)
                {
                    dataReader = be.DataReader;
                    if (be.DataReader == null)
                    {
                        throw new BusinessException("必须提供有效的数据读取器！");
                    }
                }
                else
                {
                    dataReader = this.DBService.ExecuteReader(this.UserContext, sourceQuery.QueryObject.SqlNoPage, sourceQuery.ParamList);
                }
                using (dataReader)
                {
                    while (dataReader.Read())
                    {
                        Dictionary<string, object> dctRowObj = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);
                        for (int i = 0; i < dataReader.FieldCount; i++)
                        {
                            var name = dataReader.GetName(i);
                            var value = dataReader.GetValue(i);
                            dctRowObj[name] = value;
                        }

                        lstQueryResult.Add(dctRowObj);
                    }
                }

                var ae = new AfterGetSourceBillDataEventArgs()
                {                    
                    ActiveQueryEntity= sourceQuery.ActiveQueryEntity,
                    SourceDataEntities = lstQueryResult,
                    Rule = this.ConvertRule,
                    SourceFormId = this.SourceHtmlForm.Id,
                    TargetFormId = this.TargetHtmlForm.Id
                };
                this.PlugInProxy.InvokePrimitive(o =>
                {
                    o.AfterGetSourceBillData(ae);
                });

                dctAllSourceObjs[sourceQuery.ActiveQueryEntity] = ae.SourceDataEntities;

                if (lstQueryResult.Count > 0) flag++;
            }

            //没有找到符合条件的数据
            if (flag == 0)
            {
                throw new BusinessException(this.ConvertRule.Message.IsNullOrEmptyOrWhiteSpace() ? "根据源单过滤条件找不到符合的源单数据，请检查！" : this.ConvertRule.Message);
            }

            this.SourceDataEntities = dctAllSourceObjs;
        }


    }
    
}
