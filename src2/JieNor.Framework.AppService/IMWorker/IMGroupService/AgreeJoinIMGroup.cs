using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.IM;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using JieNor.Framework.Consts;


namespace JieNor.Framework.AppService.IMWorker.IMGroupService
{





    /// <summary>
    /// 允许、拒绝别人的邀请
    /// </summary>
    [InjectService]
    [FormId(HtmlFormIdConst.Html_IM_IMGroup)]
    [OperationNo("AgreeJoinIMGroup")]
    public class AgreeJoinIMGroup : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {

            var msgId = this.GetQueryOrSimpleParam<string>("msgId");
            bool  agree = this.GetQueryOrSimpleParam<string>("agree").EqualsIgnoreCase ("true");
            var msgPiple = this.Container.GetService<IChatServiceProxy>();
            msgPiple.AgreeJoinIMGroup(this.Context, msgId, agree);
            this.Result.IsSuccess = true;
        }

    }


     






}
