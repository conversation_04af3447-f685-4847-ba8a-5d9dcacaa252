using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.IoC;
using Newtonsoft.Json.Linq;
using JieNor.Framework.Interface.Log;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 抽象操作服务基类
    /// </summary>
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public abstract class AbstractBaseService : IBaseService
    {
        /// <summary>
        /// 服务前置条件
        /// </summary>
        protected string Precondition { get; set; }

        /// <summary>
        /// 服务参数字符串
        /// </summary>
        protected string ServiceParamString { get; set; }

        /// <summary>
        /// 动态参数对象
        /// </summary>
        protected Dictionary<string, string> QueryStringParam { get; private set; }

        /// <summary>
        /// 简单数据对象
        /// </summary>
        protected Dictionary<string, string> SimpleData { get; private set; }

        /// <summary>
        /// 网关服务
        /// </summary>
        [InjectProperty]
        protected IHttpServiceInvoker Gateway { get; private set; }

        /// <summary>
        /// 业务对象业务模型
        /// </summary>
        public HtmlForm HtmlForm { get; private set; }

        /// <summary>
        /// 上下文
        /// </summary>
        public UserContext Context { get; private set; }

        /// <summary>
        /// 当前IoC服务容器
        /// </summary>
        protected IServiceContainer Container { get; set; }

        /// <summary>
        /// 其它控制参数
        /// </summary>
        protected OperateOption Option { get; set; }

        /// <summary>
        /// 操作结果信息
        /// </summary>
        protected IOperationResult Result { get; set; }

        /// <summary>
        /// 日志服务
        /// </summary>
        protected ILogService Logger { get; private set; }

        /// <summary>
        /// 本地文本日志器
        /// </summary>
        private ILogServiceEx LocalLogger { get; set; }

        /// <summary>
        /// 缓存服务
        /// </summary>
        protected IRedisCache CacheClient { get; private set; }
         
        /// <summary>
        /// 操作上下文关联的选中数据
        /// </summary>
        protected IEnumerable<SelectedRow> SelectedRows { get; set; }

        /// <summary>
        /// orm数据读写引擎
        /// </summary>
        protected IDataManager GetDataManager()
        {
            if (this.Context == null) return null;
            var dm = this.Container?.GetService<IDataManager>();
            dm.Option = this.Option;
            return dm;
        }

        /// <summary>
        /// 模型服务引擎
        /// </summary>
        protected IMetaModelService MetaModelService { get; private set; }

        /// <summary>
        /// 任务处理服务
        /// </summary>
        protected ITaskProgressService TaskProgressService { get; private set; }

        /// <summary>
        /// 操作代码
        /// </summary>
        public string OperationNo { get { return __operCtx?.OperationNo; } }

        /// <summary>
        /// 操作名称
        /// </summary>
        public string OperationName { get { return __operCtx?.OperationName ?? __operCtx?.HtmlOperation?.OperationName; } }

        /// <summary>
        /// 操作对象
        /// </summary>
        protected HtmlOperation HtmlOperation { get { return __operCtx?.HtmlOperation; } }

        /// <summary>
        /// 插件代理
        /// </summary>
        protected PlugInProxy<IOperationServicePlugIn> PlugInProxy { get; set; }

        /// <summary>
        /// 当前页面pageid
        /// </summary>
        protected string CurrentPageId
        {
            get
            {
                return __operCtx?.PageId;
            }
        }


        /// <summary>
        /// 当前操作对应的异步任务id
        /// </summary>
        protected string TaskId
        {
            get
            {
                return this.GetQueryOrSimpleParam<string>("__taskId__");
            }
        }

        /// <summary>
        /// 数据库服务引擎
        /// </summary>
        protected IDBService DBService { get; private set; }

        /// <summary>
        /// 当前操作上下文
        /// </summary>
        private OperationContext __operCtx { get; set; }


        /// <summary>
        /// 当前页面数据共享区
        /// </summary>
        protected dynamic PageSession
        {
            get
            {
                return __operCtx?.GetPageSession();
            }
        }

        /// <summary>
        /// 父级页面共享区
        /// </summary>
        protected dynamic ParentPageSession
        {
            get
            {
                return __operCtx?.GetParentPageSession();
            }
        }

        /// <summary>
        /// 参数配置form标识
        /// </summary>
        public virtual string ParamFormId
        {
            get
            {
                return "";
            }
        }
        /// <summary>
        /// 事务模式
        /// </summary>
        public virtual int TransactionMode
        {
            get
            {
                return 0;
            }
        }

        /// <summary>
        /// 服务执行
        /// </summary>
        /// <param name="dataEntities"></param>
        public abstract void ExecuteService(ref DynamicObject[] dataEntities);


        /// <summary>
        /// 服务初始化通知
        /// </summary>
        protected virtual void OnServiceInitialized(string servicePara)
        {

        }

        /// <summary>
        /// 初始化过程
        /// </summary>
        /// <param name="operCtx"></param>
        /// <param name="servicePara"></param>
        public void InitializeService(OperationContext operCtx, string servicePara)
        {
            __operCtx = operCtx;
            this.HtmlForm = operCtx.HtmlForm;
            this.Result = operCtx.Result;
            this.Option = operCtx.Option;
            this.Context = operCtx.UserContext;
            this.Container = operCtx.Container;
            this.QueryStringParam = operCtx.QueryStringParam;
            this.SimpleData = operCtx.SimpleData;
            this.Logger = operCtx.Container.GetService<ILogService>();
            this.LocalLogger = operCtx.Container.GetService<ILogServiceEx>();
            this.CacheClient = operCtx.Container.GetService<IRedisCache>();
            (this.CacheClient as IPreInitCache).Init(this.Context);
            this.Gateway = operCtx.Container.GetService<IHttpServiceInvoker>();
            this.DBService = operCtx.Container?.GetService<IDBService>();
            this.MetaModelService = operCtx.Container?.GetService<IMetaModelService>();
            this.TaskProgressService = operCtx.Container?.GetService<ITaskProgressService>();
            this.PlugInProxy = this.Option.GetVariableValue("_plugInProxy_", this.PlugInProxy);
            this.SelectedRows = operCtx.SelectedRows;

            if (!servicePara.IsNullOrEmptyOrWhiteSpace())
            {
                var srvPara = JObject.Parse(servicePara ?? "{}");
                this.Precondition = srvPara.GetJsonValue("condition", "");
                this.ServiceParamString = srvPara.GetJsonValue("serConfig", "");
            }
            this.OnServiceInitialized(servicePara);
        }

        /// <summary>
        /// 从当前上下文里获取url参数或简单数据参数
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="def"></param>
        /// <returns></returns>
        public T GetQueryOrSimpleParam<T>(string key, T def = default(T))
        {
            object objValue = null;
            objValue = this.QueryStringParam.GetValue(key, null);

            if (objValue.IsNullOrEmpty())
            {
                objValue = this.SimpleData.GetValue(key, null);
            }

            if (objValue.IsNullOrEmpty())
            {
                T value = def;
                this.Option?.TryGetVariableValue<T>(key, out value);
                objValue = value;
            }

            if (objValue.IsNullOrEmpty())
            {
                var formPara = this.PageSession?.FormParameter;
                if (formPara is FormParameter)
                {
                    objValue = (formPara as FormParameter).CustomParameter?.GetValue(key, null);
                }
            }

            if (objValue.IsNullOrEmpty()) return def;

            if (typeof(T).IsEnum)
            {
                return objValue.ToEnum<T>();
            }

            return (T)Convert.ChangeType(objValue, typeof(T));
        }

        /// <summary>
        /// 获取页面会话区变量
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="defValue"></param>
        /// <returns></returns>
        protected virtual T GetSessionValue<T>(string key, T defValue = default(T))
        {
            object objValue = null;
            dynamic pageSession = this.PageSession;
            var formPara = pageSession?.FormParameter;
            if (formPara is FormParameter)
            {
                objValue = (formPara as FormParameter).CustomParameter?.GetValue(key, null);
            }
            if (objValue == null) return defValue;

            return (T)Convert.ChangeType(objValue, typeof(T));
        }

        /// <summary>
        /// 创建客户端赋值指令
        /// </summary>
        /// <param name="id">字段标识</param>
        /// <param name="value">字段值</param>
        /// <param name="row">行号</param>
        /// <param name="pageId">页面标识（不传为当前页面）</param>
        protected void AddSetValueAction(string id, object value, string row = null, string pageId = null)
        {
            this.__operCtx?.SetValue(id, value, row, pageId);
        }

        /// <summary>
        /// 提供写日志接口，如果是在计划任务上下文里，提供写入日志对象的可能
        /// </summary>
        /// <param name="message"></param>
        protected void WriteLog(string message)
        {
            this.LocalLogger?.Info(message);
        }

    }
}
