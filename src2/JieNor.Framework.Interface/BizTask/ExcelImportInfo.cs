using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.BizTask
{


    public class ExcelImortResult
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="row"></param>
        /// <param name="col"></param>
        /// <param name="level">0:提示 1：警告 2：错误</param>
        /// <param name="msg"></param>
        public ExcelImortResult(int row,int col,int level,string msg)
        {
            Row = row;
            Col = col;
            Level = level;
            Msg = msg;
        }

        public int Row;
        public int Col;
        public string Msg;

        /// <summary>
        /// 0:提示
        /// 1：警告
        /// 2：错误
        /// </summary>
        public int Level;

        public override string ToString()
        {
            return Msg; 
        }
    }

    public class ExcelImortDataInfo
    {
        public ExcelImortDataInfo(int beginRow, DynamicObject billData, HtmlEntity activeEntity)
        {
            BeginRow = beginRow;
            NewBillData = billData;
            this.ActiveEntity = activeEntity;
        }

        public HtmlEntity ActiveEntity { get; private set; }

        public int BeginRow;
        public int EndRow;

        public List<ExcelImortResult> Msg =new List<ExcelImortResult>();

        /// <summary>
        /// 由Excel表生成的新的业务单据
        /// </summary>
        public DynamicObject NewBillData;

        /// <summary>
        /// 对应的在系统中存在的业务单据（按整单匹配字段匹配到的系统单据）
        /// </summary>
        public DynamicObject ExistsBillData;

        /// <summary>
        /// 系列号：excel分单依据
        /// </summary>
        public string SN;
          

    }


}
