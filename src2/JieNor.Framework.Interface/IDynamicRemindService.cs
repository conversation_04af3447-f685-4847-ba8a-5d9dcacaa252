using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 动态提醒服务
    /// </summary>
    public interface IDynamicRemindService
    {
        /// <summary>
        /// 推送动态提醒消息
        /// </summary>
        /// <param name="useContext"></param>
        /// <param name="dataEntity"></param>
        void PushDynamicRemind(UserContext useContext, DynamicObject dataEntity);

        /// <summary>
        /// 创建推送动态提示消息的数据结构类型
        /// </summary>
        /// <returns></returns>
        DynamicObjectType CreateDynamicObjectType();

        /// <summary>
        /// 创建推送动态提示消息实体
        /// </summary>
        /// <returns></returns>
        DynamicObject CreateDynamicObject();
    }
}
