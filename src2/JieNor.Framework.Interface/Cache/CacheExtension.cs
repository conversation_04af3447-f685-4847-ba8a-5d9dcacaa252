using JieNor.Framework;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using ServiceStack.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace System
{
    /// <summary>
    /// 提供一套带上下文的缓存操作接口
    /// </summary>
    public static class RedisCacheExtension
    {
        /// <summary>
        /// 设置键有效期
        /// </summary>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="key"></param>
        /// <param name="tsExpireIn"></param>
        public static void SetTimeToLive(this IRedisCache redisCache, UserContext userCtx, string key, TimeSpan tsExpireIn)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            redisCache.SetTimeToLive(key, tsExpireIn);
        }

        /// <summary>
        /// 设置键有效期
        /// </summary>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="key"></param>
        /// <param name="dtExpireAt"></param>
        /// <returns></returns>
        public static void SetTimeToLive(this IRedisCache redisCache, UserContext userCtx, string key, DateTime dtExpireAt)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            redisCache.SetTimeToLive(key, dtExpireAt);
        }


        /// <summary>
        /// 设置某个对象至缓存
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public static bool Set<T>(this IRedisCache redisCache, UserContext userCtx, string key, T value)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            return redisCache.Set(key, value);
        }

        public static bool Set<T>(this IBillNoCache redisCache, UserContext userCtx, string key, T value)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            return redisCache.Set(key, value);
        }

        /// <summary>
        /// 设置某个对象至缓存，并应用有效期
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="expiryIn"></param>
        /// <returns></returns>
        public static bool Set<T>(this IRedisCache redisCache, UserContext userCtx, string key, T value, TimeSpan expiryIn)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            return redisCache.Set(key, value, expiryIn);
        }

        /// <summary>
        /// 从缓存获取一个对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        public static T Get<T>(this IRedisCache redisCache, UserContext userCtx, string key)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            return redisCache.Get<T>(key);
        }

        /// <summary>
        /// 从缓存获取一组对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="keys"></param>
        /// <returns></returns>
        public static IDictionary<string, T> GetAll<T>(this IRedisCache redisCache, UserContext userCtx, IEnumerable<string> keys)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            return redisCache.GetAll<T>(keys);
        }

        /// <summary>
        /// 设置一组数据至缓存
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="allValues"></param>
        public static void SetAll<T>(this IRedisCache redisCache, UserContext userCtx, IDictionary<string, T> allValues)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            redisCache.SetAll<T>(allValues);
        }

        /// <summary>
        /// 清空指定标识的缓存
        /// </summary>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        public static bool Remove(this IRedisCache redisCache, UserContext userCtx, string key)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            return redisCache.Remove(key);
        }

        /// <summary>
        /// 清空所有缓存
        /// </summary>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public static void RemoveAll(this IRedisCache redisCache, UserContext userCtx)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            redisCache.RemoveAll();
        }

        /// <summary>
        /// 清空所有缓存
        /// </summary>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="keys"></param>
        /// <returns></returns>
        public static void RemoveAll(this IRedisCache redisCache, UserContext userCtx, IEnumerable<string> keys)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            redisCache.RemoveAll(keys);
        }

        /// <summary>
        /// 移除符合指定样式的所有key
        /// </summary>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="keyPattern"></param>
        /// <param name="type"></param>
        public static void RemoveAllByKeyPattern(this IRedisCache redisCache, UserContext userCtx, string keyPattern, Enu_SearchType type)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            redisCache.RemoveAllByKeyPattern(keyPattern, type);
        }

        /// <summary>
        /// 计数接口
        /// </summary>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="key"></param>
        /// <param name="amount"></param>
        /// <returns></returns>
        public static long Increment(this IRedisCache redisCache, UserContext userCtx, string key, uint amount)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            return redisCache.Increment(key, amount);
        }

        /// <summary>
        /// 向某个队列中加入某个对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="listId"></param>
        /// <param name="value"></param>
        public static void EnqueueItemToList<T>(this IRedisCache redisCache, UserContext userCtx, string listId, T value)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            redisCache.EnqueueItemToList<T>(listId, value);
        }

        /// <summary>
        /// 从某个队列中弹出某个对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="listId"></param>
        /// <returns></returns>
        public static T DequeueItemFromList<T>(this IRedisCache redisCache, UserContext userCtx, string listId)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            return redisCache.DequeueItemFromList<T>(listId);
        }

        /// <summary>
        /// 推送一个对象到某个堆栈
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="listId"></param>
        /// <param name="value"></param>
        public static void PushItemToList<T>(this IRedisCache redisCache, UserContext userCtx, string listId, T value)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            redisCache.PushItemToList<T>(listId, value);
        }

        /// <summary>
        /// 从某个堆栈弹出一个对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="listId"></param>
        /// <returns></returns>
        public static T PopItemFromList<T>(this IRedisCache redisCache, UserContext userCtx, string listId)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            return redisCache.PopItemFromList<T>(listId);
        }

        /// <summary>
        /// 获取某个缓存内容存活期还有多久
        /// </summary>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        public static TimeSpan? GetTimeToLive(this IRedisCache redisCache, UserContext userCtx, string key)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            return redisCache.GetTimeToLive(key);
        }

        /// <summary>
        /// 获取redis缓存状态
        /// </summary>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public static Dictionary<string, object> GetStatusDescription(this IRedisCache redisCache, UserContext userCtx)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            return redisCache.GetStatusDescription();
        }


        /// <summary>
        /// 获取某集合里的所有对象
        /// </summary>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="listId"></param>
        /// <returns></returns>
        public static IEnumerable<string> GetAllItemsFromSet(this IRedisCache redisCache, UserContext userCtx, string listId)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            return redisCache.GetAllItemsFromSet(listId);
        }

        /// <summary>
        /// 对指定集合做运算（差集）
        /// </summary>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="fromListId"></param>
        /// <param name="withListIds"></param>
        /// <returns></returns>
        public static IEnumerable<string> GetDifferencesFromSet(this IRedisCache redisCache, UserContext userCtx, string fromListId, params string[] withListIds)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            return redisCache.GetDifferencesFromSet(fromListId, withListIds);
        }

        /// <summary>
        /// 从集合里移除指定项
        /// </summary>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="listId"></param>
        /// <param name="key"></param>
        public static void RemoveItemFromSet(this IRedisCache redisCache, UserContext userCtx, string listId, string key)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            redisCache.RemoveItemFromSet(listId, key);
        }

        /// <summary>
        /// 向集合里添加指定项
        /// </summary>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="listId"></param>
        /// <param name="key"></param>
        public static void AddItemToSet(this IRedisCache redisCache, UserContext userCtx, string listId, string key)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            redisCache.AddItemToSet(listId, key);
        }

        /// <summary>
        /// 批量向集合里添加指定项
        /// </summary>
        /// <param name="redisCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="listId"></param>
        /// <param name="items"></param>
        public static void AddRangeToSet(this IRedisCache redisCache, UserContext userCtx, string listId, List<string> items)
        {
            (redisCache as IPreInitCache)?.Init(userCtx);
            redisCache.AddRangeToSet(listId, items);
        }
    }

    /// <summary>
    /// 内存缓存接口的扩展方法
    /// </summary>
    public static class MemoryCacheExtension
    {
        /// <summary>
        /// 设置指定键的缓存数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="memCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public static bool Set<T>(this IMemoryCache memCache, UserContext userCtx, string key, T value)
        {
            (memCache as IPreInitCache)?.Init(userCtx);
            return memCache.Set(key, value);
        }

        /// <summary>
        /// 设置指定键的缓存数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="memCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="expiryIn"></param>
        /// <returns></returns>
        public static bool Set<T>(this IMemoryCache memCache, UserContext userCtx, string key, T value, TimeSpan expiryIn)
        {
            (memCache as IPreInitCache)?.Init(userCtx);
            return memCache.Set(key, value, expiryIn);
        }

        /// <summary>
        /// 获取指定键的缓存数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="memCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        public static T Get<T>(this IMemoryCache memCache, UserContext userCtx, string key)
        {
            (memCache as IPreInitCache)?.Init(userCtx);
            return memCache.Get<T>(key);
        }

        /// <summary>
        /// 获取多个键对应的缓存数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="memCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="keys"></param>
        /// <returns></returns>
        public static IDictionary<string, T> GetAll<T>(this IMemoryCache memCache, UserContext userCtx, IEnumerable<string> keys)
        {
            (memCache as IPreInitCache)?.Init(userCtx);
            return memCache.GetAll<T>(keys);
        }

        /// <summary>
        /// 设置多个键对应的缓存数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="memCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="allValues"></param>
        public static void SetAll<T>(this IMemoryCache memCache, UserContext userCtx, IDictionary<string, T> allValues)
        {
            (memCache as IPreInitCache)?.Init(userCtx);
            memCache.SetAll(allValues);
        }

        /// <summary>
        /// 清空指定标识的缓存
        /// </summary>
        /// <param name="memCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        public static bool Remove(this IMemoryCache memCache, UserContext userCtx, string key)
        {
            (memCache as IPreInitCache)?.Init(userCtx);
            return memCache.Remove(key);
        }

        /// <summary>
        /// 清空所有缓存
        /// </summary>
        /// <param name="memCache"></param>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public static void RemoveAll(this IMemoryCache memCache, UserContext userCtx)
        {
            (memCache as IPreInitCache)?.Init(userCtx);
            memCache.RemoveAll();
        }

        /// <summary>
        /// 清空所有缓存
        /// </summary>
        /// <param name="memCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="keys"></param>
        /// <returns></returns>
        public static void RemoveAll(this IMemoryCache memCache, UserContext userCtx, IEnumerable<string> keys)
        {
            (memCache as IPreInitCache)?.Init(userCtx);
            memCache.RemoveAll(keys);
        }

        /// <summary>
        /// 移除符合指定样式的所有key
        /// </summary>
        /// <param name="memCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="keyPattern"></param>
        /// <param name="type"></param>
        public static void RemoveAllByKeyPattern(this IMemoryCache memCache, UserContext userCtx, string keyPattern, Enu_SearchType type)
        {
            (memCache as IPreInitCache)?.Init(userCtx);
            memCache.RemoveAllByKeyPattern(keyPattern, type);
        }

        /// <summary>
        /// 计数接口
        /// </summary>
        /// <param name="memCache"></param>
        /// <param name="userCtx"></param>
        /// <param name="key"></param>
        /// <param name="amount"></param>
        /// <returns></returns>
        public static long Increment(this IMemoryCache memCache, UserContext userCtx, string key, uint amount)
        {
            (memCache as IPreInitCache)?.Init(userCtx);
            return memCache.Increment(key, amount);
        }
         

    }

}
