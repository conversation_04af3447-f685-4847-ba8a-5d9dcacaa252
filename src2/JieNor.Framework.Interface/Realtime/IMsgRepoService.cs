//using JieNor.Framework.Enums;
//using System;
//using System.Collections.Generic;

//namespace JieNor.Framework.Interface.Realtime
//{
//    /// <summary>
//    /// 系统消息服务总线
//    /// </summary>
//    public interface IMsgRepoService
//    {

//        /// <summary>
//        /// 获取指定用户未阅读消息，默认取当前用户
//        /// </summary>
//        /// <param name="ctx"></param>
//        /// <param name="userId"></param>
//        /// <returns></returns>
//        IEnumerable<IRealtimeMessage> GetUnreadMessages(UserContext ctx, string userId = null);

//        /// <summary>
//        /// 保存消息
//        /// </summary>
//        /// <param name="ctx"></param>
//        /// <param name="messages"></param>
//        void SaveMessage(UserContext ctx, IEnumerable<IRealtimeMessage> messages);

//        /// <summary>
//        /// 批量设置消息为已读标记
//        /// </summary>
//        /// <param name="ctx"></param>
//        /// <param name="messages"></param>
//        /// <param name="messageStatus">消息状态，请参考<see cref="IMEnums.Enu_MessageStatus">消息状态枚举定义</see>/></param>
//        /// <param name="isAppend">是否追加状态，否则重置状态</param>
//        void SetMessageStatus(UserContext ctx, IEnumerable<IRealtimeMessage> messages, int messageStatus, bool isAppend = false);


//        /// <summary>
//        /// 获取当前用户指定状态的消息
//        /// </summary>
//        /// <param name="ctx"></param>
//        /// <param name="filter">过滤条件</param>
//        /// <returns></returns>
//        IEnumerable<IRealtimeMessage> GetMessage(UserContext ctx, string filter);

//        /// <summary>
//        /// 根据指定发送者获取消息
//        /// </summary>
//        /// <param name="ctx"></param>
//        /// <param name="sender"></param>
//        /// <param name="fromDate"></param>
//        /// <param name="toDate"></param>
//        /// <returns></returns>
//        IEnumerable<IRealtimeMessage> GetMessageBySender(UserContext ctx, string sender, DateTime? fromDate = null, DateTime? toDate = null);

//        /// <summary>
//        /// 根据指定接收者获取消息
//        /// </summary>
//        /// <param name="ctx"></param>
//        /// <param name="receiver"></param>
//        /// <param name="fromDate"></param>
//        /// <param name="toDate"></param>
//        /// <returns></returns>
//        IEnumerable<IRealtimeMessage> GetMessageByReceiver(UserContext ctx, string receiver, DateTime? fromDate = null, DateTime? toDate = null);


//    }
//}
