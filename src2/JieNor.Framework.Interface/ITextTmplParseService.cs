using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 文本模版解析服务接口定义
    /// </summary>
    public interface ITextTmplParseService
    {
        /// <summary>
        /// 解析文本模版中的字段占位符，比如：你有一个销售合同 {fbillno} 已收款 {freceivable}。
        /// 已知输入：文本模板，模板中变量以“{”大括号标明，服务接口支持传入模板以及上下文表单数据包来完成模板变量的解析与替换
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="bizForm">业务表单</param>
        /// <param name="dataEntity">业务对象数据包</param>
        /// <param name="template">模版字符串</param>
        /// <returns>解析后的文本</returns>
        string ParseFieldPlaceholder(UserContext userCtx, HtmlForm bizForm, DynamicObject dataEntity, string template);
    }
}