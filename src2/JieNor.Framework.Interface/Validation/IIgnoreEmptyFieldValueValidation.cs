using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.Validation
{
    /// <summary>
    /// 忽略字段为空值的重复性校验
    /// </summary>
    public interface IIgnoreEmptyFieldValueValidation
    {
        /// <summary>
        /// 判断重复行时，是否忽略空行
        /// </summary>
        bool IgnoreEmptyValue { get; set; }
    }
}
