using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.WarmUpService
{



    /// <summary>
    /// 订阅消息
    /// </summary>
    [InjectService]
    public class SubMessageService : IOnceWarmUpService
    {

        public void Execute(UserContext ctx)
        {
            //var container = ctx.Container;
            //var svc = container.GetService<IPubSubService>();
            //if (svc == null)
            //{
            //    return ;
            //}

            //var svcs = container.GetService<IEnumerable<IReceivedPubMessage>>().ToList();
            //foreach (var item in svcs)
            //{
            //    svc.SubMessage(item.ChannelName);
            //}
        }


    }




}
