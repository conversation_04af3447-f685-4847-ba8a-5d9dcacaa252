using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Datacenter;
using JieNor.Framework.IoC;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataManager;
using ServiceStack;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using JieNor.Framework.Interface.Log;

namespace JieNor.Framework.AppService.WarmUpService
{
    /// <summary>
    /// 热身服务：预构建操作日志列表的SQL视图
    /// </summary>
    [InjectService]
    public class PreBuildRecordListView : IOnceWarmUpService
    {
        /// <summary>
        /// 是否已运行过一次
        /// </summary>
        private bool IsRunOnce { get; set; }

        /// <summary>
        /// 是否构建日志表结构
        /// </summary>
        private bool IsBuildLogTableStruct { get; set; }

        /// <summary>
        /// 不需要处理的表单
        /// </summary>
        private string[] noNeedLogFormIds = new string[]
        {
            "bd_record", "bd_recordlist", "bd_recordarchive", "sys_bizobject", "bas_task",
            "si_datasyncresult", "bas_tasklog", "si_operationlog"
        };

        /// <summary>
        /// 执行热身服务逻辑
        /// </summary>
        /// <param name="ctx"></param>
        public void Execute(UserContext ctx)
        {
            this.IsBuildLogTableStruct = ctx.Meta?.GetValue("IsBuildLogTableStruct")?.EqualsIgnoreCase("true") ?? false;

            //是否是手工调用
            var isManualBuild = ctx.Meta?.ContainsKey("IsBuildLogTableStruct") ?? false;

            //该热身服务只执行一次
            if (this.IsRunOnce && !isManualBuild) return;
            this.IsRunOnce = true;

            this.BuildRecordListView(ctx);
        }

        /// <summary>
        /// 构建操作日志列表的SQL视图
        /// </summary>
        private void BuildRecordListView(UserContext ctx)
        {
            var logTableNames = this.LoadBizFormLogTableNames(ctx);
            if (!logTableNames.Any()) return;

            //检查日志表是否存在数据库中，如果不存在则不需要处理
            var sqlText = $@"select [name] from sysobjects where xtype='u'";
            var sqlParam = new List<SqlParam>();
            var dbService = ctx.Container.GetService<IDBService>();
            var sbLogSql = new StringBuilder();

            using (var tran = ctx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                var tempTable = "";
                if (logTableNames.Count > 50)
                {
                    //用临时表关联查询
                    tempTable = dbService.CreateTempTableWithDataList(ctx, logTableNames);
                    sqlText = $@"
                select [name] from sysobjects t 
                inner join {tempTable} tt on tt.fid=t.[name] 
                where t.xtype='u'";
                }
                else if (logTableNames.Count == 1)
                {
                    sqlParam.Add(new SqlParam("@name", System.Data.DbType.String, logTableNames[0]));
                    sqlText += $" and [name]=@name";
                }
                else
                {
                    var paramNames = new List<string>();
                    for (int i = 0; i < logTableNames.Count; i++)
                    {
                        sqlParam.Add(new SqlParam($"@name{i}", System.Data.DbType.String, logTableNames[i]));
                        paramNames.Add($"@name{i}");
                    }
                    sqlText += $" and [name] in({string.Join(",", paramNames)})";
                }
                 
                using (var reader = dbService.ExecuteReader(ctx, sqlText, sqlParam))
                {
                    while (reader.Read())
                    {
                        var logTableName = reader.GetValueToString("name")?.Trim()?.ToLowerInvariant() ?? "";
                        if (logTableName.IsNullOrEmptyOrWhiteSpace()) continue;

                        var logSql = $"select fid,fformid,fmainorgid,fbizformid,fbizobjid,fbizobjno,ftype,fcontent,foperator,fopdate,fopcode,fopname,fip,fisdevops,fdatasourceterminal from {logTableName}";

                        sbLogSql.AppendLine(logSql).AppendLine("union all");
                    }
                }

                tran.Complete();

                //删除临时表
                dbService.DeleteTempTableByName(ctx, tempTable, true);
            }

            if (sbLogSql.Length < 1) return;

            var strLogSql = sbLogSql.ToString();
            strLogSql = strLogSql.Substring(0, strLogSql.LastIndexOf("union all") - 1);

            //创建视图的Sql脚本，不存在则创建，存在则修改。
            //不采用删除后重建的原因：
            //1、避免重建失败时导致上一次构建成功的视图被删除的问题，尽量保证大部分操作日志可查看。
            //2、避免在删除视图的那一瞬间，有用户正在查询操作日志，导致视图不存在的错误。
            var viewSql = $@"/*dialect*/
            if exists (select 1 from sysobjects where name = 'v_bd_recordlist' and xtype = 'v')
            begin
                execute('alter view v_bd_recordlist
                as
                    {strLogSql}
                ')
            end
            else
            begin
                execute('create view v_bd_recordlist
                as
                    {strLogSql}
                ')
            end";

            var dbServiceEx = ctx.Container.GetService<IDBServiceEx>();
            dbServiceEx.Execute(ctx, viewSql);
        }

        /// <summary>
        /// 加载业务表单的操作日志数据库表名称
        /// </summary>
        private List<string> LoadBizFormLogTableNames(UserContext ctx)
        {
            ILogServiceEx logServiceEx = null;
            IDataManager recordDm = null;
            if (this.IsBuildLogTableStruct)
            {
                logServiceEx = ctx.Container.GetService<ILogServiceEx>();
                recordDm = ctx.Container.GetService<IDataManager>();
                recordDm.Option = ctx.Container.GetService<OperateOption>();
                recordDm.Option.SetVariableValue("forceRecreate", true);
                recordDm.Option.SetVariableValue("autoUpdateScheme", true);
            }

            var logTableNames = new List<string>();

            //扫描系统中的模型文件
            var mdlFiles = PathUtils.SearchWebFiles(HostConfigView.Site.DefaultMdlDir, "*.mdl.html", true);
            foreach (var mdlFile in mdlFiles)
            {
                var formId = Path.GetFileName(mdlFile).MdlFormId().Trim();
                if (formId.IsNullOrEmptyOrWhiteSpace())
                {
                    //模型表单标识不规范
                    continue;
                }

                if (noNeedLogFormIds.Contains(formId, StringComparer.OrdinalIgnoreCase))
                {
                    continue;
                }

                HtmlForm formMeta = null;
                try
                {
                    formMeta = HtmlParser.LoadFormMetaFromCache(formId, ctx);
                }
                catch { }

                if (formMeta == null
                    //|| formMeta.HeadEntity.TableName.IsNullOrEmptyOrWhiteSpace()//参数配置页面TableName为空故此判断注释
                    || formMeta.HeadEntity.TableName.StartsWithIgnoreCase("v_"))
                {
                    continue;
                }
                if (formMeta.ElementType != HtmlElementType.HtmlForm_ParameterForm
                        && formMeta.HeadEntity.TableName.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                //只处理基础资料和业务单据，因为只有这两类模型才会记录操作日志
                //参数配置页面也需记录日志
                if (formMeta.ElementType == HtmlElementType.HtmlForm_BaseForm
                    || formMeta.ElementType == HtmlElementType.HtmlForm_BillForm
                    || formMeta.ElementType == HtmlElementType.HtmlForm_ParameterForm)
                {
                    var billHeadTableName = "";
                    if (formMeta.ElementType == HtmlElementType.HtmlForm_ParameterForm)
                    {
                        //参数配置页面无表，用id生成的日志表
                        billHeadTableName = formMeta.Id;
                    }
                    else
                    {
                        billHeadTableName = formMeta.HeadEntity.TableName.Trim().ToLower();
                    }
                    logTableNames.Add($"{billHeadTableName}_lg");

                    //构建日志表结构
                    try
                    {
                        if (this.IsBuildLogTableStruct)
                        {
                            var bizLogDt = logServiceEx.LoadFormLogType(ctx, formMeta.Id);
                            recordDm.InitDbContext(ctx, bizLogDt);
                            bizLogDt = null;
                            //降低CPU占用
                            //Thread.Sleep(50);
                        }
                    }
                    catch { }
                }
            }

            return logTableNames.Distinct().ToList();
        }
    }
}
