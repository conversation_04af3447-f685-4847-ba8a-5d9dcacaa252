using ICSharpCode.SharpZipLib.Zip;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OBS;
using OBS.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;

namespace JieNor.Framework.FileWebAPI.Helpers
{
    /// <summary>
    /// 华为云临时URl
    /// </summary>
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("hwtempurl")]
    public class HWTempUrl : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            //Access Key Id
            string Ak = this.GetAppConfig<string>("fw.huaweioss.ak");
            //Secret Access Key
            string Sk = this.GetAppConfig<string>("fw.huaweioss.sk");
            //Endpoint 
            string Endpoint = this.GetAppConfig<string>("fw.huaweioss.endpoint");
            //桶
            string BucketName = this.GetAppConfig<string>("fw.huaweioss.bucketname");

            var objectkey = this.GetQueryOrSimpleParam("objectkey", "");
            var filename = this.GetQueryOrSimpleParam("filename", "");

            if (objectkey.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.ComplexMessage.ErrorMessages.Add("请求参数不正确 ！");
                this.Result.IsSuccess = false;
                return;
            }

            ObsConfig config = new ObsConfig();
            config.Endpoint = Endpoint;
            ObsClient client = new ObsClient(Ak, Sk, config);

            // URL有效期，3600秒
            long exipreSeconds = 3600;
            //文档 https://support.huaweicloud.com/sdk-dotnet-devg-obs/obs_25_0701.html
            CreateTemporarySignatureRequest request = new CreateTemporarySignatureRequest();
            request.BucketName = BucketName;
            request.ObjectKey = objectkey;
            request.Method = HttpVerb.PUT;
            request.Expires = exipreSeconds;
            request.Headers.Add("Content-Type", GetContentType(filename));
            //临时URL对象
            CreateTemporarySignatureResponse response = client.CreateTemporarySignature(request);

            this.Result.SrvData = new { SignUrl = response.SignUrl, ActualSignedRequestHeaders = GetContentType(filename) };
            this.Result.IsSuccess = true;
        }

        private string GetContentType(string filename)
        {
            if (filename.Length < 4) return "";
            filename = filename.Substring(filename.IndexOf(".") + 1);
            string type = "";
            switch (filename.ToLower())
            {
                case "jpeg":
                    type = "image/jpeg";
                    break;
                case "jpg":
                    type = "image/jpeg";
                    break;
                case "png":
                    type = "image/png";
                    break;
                case "svg":
                    type = "image/svg+xml";
                    break;
                case "zip":
                    type = "application/zip";
                    break;
                case "xls":
                    type = "application/x-excel";
                    break;
                case "pdf":
                    type = "application/pdf";
                    break;
                case "doc":
                case "docx":
                    type = "application/msword";
                    break;
                default:
                    type = "text/html";
                    break;
            }
            return type;
        }
    }
}