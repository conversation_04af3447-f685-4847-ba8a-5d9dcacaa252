using ICSharpCode.SharpZipLib.Zip;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OBS;
using OBS.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;

namespace JieNor.Framework.FileWebAPI.Helpers
{
    /// <summary>
    /// 华为云文件下载
    /// </summary>
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("hwdownload")]
    public class HWCloudDownloadFile : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            //Access Key Id
            string Ak = this.GetAppConfig<string>("fw.huaweioss.ak");
            //Secret Access Key
            string Sk = this.GetAppConfig<string>("fw.huaweioss.sk");
            //Endpoint 
            string Endpoint = this.GetAppConfig<string>("fw.huaweioss.endpoint");
            //桶
            string BucketName = this.GetAppConfig<string>("derucci-public");

            ObsConfig config = new ObsConfig();
            config.Endpoint = Endpoint;
            ObsClient client = new ObsClient(Ak, Sk, config);
            var objAry = this.GetQueryOrSimpleParam<string>("objs");
            var objs = JArray.Parse(objAry);
            try
            {
                var zipFileName = Guid.NewGuid().ToString("N");
                var zipPath = PathUtils.GetStartupPath() + "\\export\\{0}.zip".Fmt(zipFileName);
                var downLoadPath = this.GetCurrentAppServer().ToString() + "export/{0}.zip".Fmt(zipFileName);
                FileStream zipFile = File.Create(zipPath);

                using (ZipOutputStream zipStream = new ZipOutputStream(zipFile))
                {
                    foreach (var obj in objs)
                    {
                        var fileName = obj["fileName"]?.ToString();
                        var fileUrl = obj["fileUrl"]?.ToString();
                        var fileId = obj["fileId"]?.ToString();
                        if (fileName.IsNullOrEmptyOrWhiteSpace() || fileUrl.IsNullOrEmptyOrWhiteSpace())
                        {
                            continue;
                        }

                        GetObjectRequest request = new GetObjectRequest()
                        {
                            BucketName = BucketName,
                            ObjectKey = fileId,
                        };
                        using (GetObjectResponse response = client.GetObject(request))
                        {
                            //保存路径
                            string dest = fileUrl + "\\" + fileName;
                            if (!File.Exists(dest))
                            {
                                // 将对象的数据流写入文件中
                                response.WriteResponseStreamToFile(dest);
                            }
                        }
                    }
                }
                this.Result.SimpleData["localUrl"] = downLoadPath;
            }
            catch (Exception ex)
            {
                this.Result.ComplexMessage.ErrorMessages.Add("文件下载失败：" + ex.ToString());
                this.Result.IsSuccess = false;
            }
        }

        /// <summary>
        /// 得到临时图片地址
        /// </summary>
        /// <param name="BucketName"></param>
        /// <param name="ObjectKey"></param>
        /// <param name="client"></param>
        private string GetImgUrl(string BucketName, string ObjectKey, ObsClient client)
        {
            if (!isExistsObjctKey(BucketName, ObjectKey, client)) return "文件不存在";
            // URL有效期，3600秒
            long exipreSeconds = 3600;

            CreateTemporarySignatureRequest request = new CreateTemporarySignatureRequest();
            request.BucketName = BucketName;
            request.ObjectKey = ObjectKey;
            request.Method = HttpVerb.GET;
            request.Expires = exipreSeconds;
            CreateTemporarySignatureResponse response = client.CreateTemporarySignature(request);
            return response.SignUrl;

        }

        /// <summary>
        /// 文件是否存在
        /// </summary>
        /// <param name="BucketName"></param>
        /// <param name="ObjectKey"></param>
        /// <param name="client"></param>
        /// <returns></returns>
        private bool isExistsObjctKey(string BucketName, string ObjectKey, ObsClient client)
        {
            // 判断指定的对象是否存在
            try
            {
                HeadObjectRequest request = new HeadObjectRequest()
                {
                    BucketName = BucketName,
                    ObjectKey = ObjectKey
                };
                bool response = client.HeadObject(request);
                return true;
            }
            catch (ObsException ex)
            {
                return false;
            }
        }

    }
}