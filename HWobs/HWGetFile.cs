using ICSharpCode.SharpZipLib.Zip;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OBS;
using OBS.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace JieNor.Framework.FileWebAPI.Helpers
{
    /// <summary>
    /// 华为云得到文件地址
    /// </summary>
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("hwgetfile")]
    public class HWGetFile : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            //Access Key Id
            string Ak = this.GetAppConfig<string>("fw.huaweioss.ak");
            //Secret Access Key
            string Sk = this.GetAppConfig<string>("fw.huaweioss.sk");
            //Endpoint 
            string Endpoint = this.GetAppConfig<string>("fw.huaweioss.endpoint");
            //桶
            string BucketName = this.GetAppConfig<string>("fw.huaweioss.bucketname");

            var objectkeys = this.GetQueryOrSimpleParam<string>("objectkeys");

            List<object> list = new List<object>();
            if (objectkeys.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.SrvData = list;
                this.Result.IsSuccess = false;
                return;
            }

            ObsConfig config = new ObsConfig();
            config.Endpoint = Endpoint;
            ObsClient client = new ObsClient(Ak, Sk, config);

            // URL有效期，3600秒
            long exipreSeconds = 3600;
            foreach (var item in objectkeys.Split(','))
            {
                var key = Convert.ToString(item);
                CreateTemporarySignatureRequest request = new CreateTemporarySignatureRequest();
                request.BucketName = BucketName;
                request.ObjectKey = key;
                request.Method = HttpVerb.GET;
                request.Expires = exipreSeconds;

                CreateTemporarySignatureResponse response = client.CreateTemporarySignature(request);
                list.Add(new { fileId = key, url = response.SignUrl });

            }
            this.Result.SrvData = list;
            this.Result.IsSuccess = true;
        }
    }
}