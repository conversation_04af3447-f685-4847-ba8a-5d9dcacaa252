using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.QueryBuilder
{
    public interface IFuzzySqlBuilder
    {

        List<Dictionary<string, object>> GetQueryData(UserContext ctx, SqlBuilderParameter param);
         
    }
}
