using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.QueryBuilder
{
    /// <summary>
    /// 列表sql构建器
    /// </summary>
    public interface IListSqlBuilder
    {
        /// <summary>
        /// 获取模糊查询数据
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        List<Dictionary<string, object>> GetFuzzyQueryData(UserContext ctx, SqlBuilderParameter param);

        /// <summary>
        /// 获取列表数据隔离条件
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        List<FilterRowObject> GetListAccessControlFilter(UserContext userCtx, string formId);

        /// <summary>
        /// 获得查询对象
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        QueryObject GetQueryObject(UserContext userCtx, SqlBuilderParameter param);

        /// <summary>
        /// 获取查询数据结果
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        List<Dictionary<string, object>> GetQueryData(UserContext ctx, SqlBuilderParameter param);

        /// <summary>
        /// 获取查询数据结果
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="param"></param>
        /// <param name="queryObject"></param>
        /// <returns></returns>
        List<Dictionary<string, object>> GetQueryData(UserContext ctx, SqlBuilderParameter param, QueryObject queryObject);

        /// <summary>
        /// 获取列表数据描述
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        ListDesc GetListDesc(UserContext ctx, SqlBuilderParameter param);

        /// <summary>
        /// 获取列表数据描述
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        ListDesc GetListDesc(UserContext ctx, SqlBuilderParameter param, QueryObject queryObject);

        /// <summary>
        /// 获取查询数据对象
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="param"></param>
        /// <returns></returns>
        DynamicObjectCollection GetQueryDataEntity(UserContext ctx, SqlBuilderParameter param);
    }
}
