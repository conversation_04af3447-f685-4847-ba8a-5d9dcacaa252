using JieNor.Framework.DataTransferObject;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.QueryBuilder
{
    /// <summary>
    /// 字段脚本代理
    /// </summary>
    [InjectService]
    public class FieldMetaSqlProxy : IFieldMetaSqlProxy
    {
        /// <summary>
        /// 补齐特定模型字段的sql自动化脚本
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="hForm"></param>
        /// <param name="htmlField"></param>
        /// <returns></returns>
        public IEnumerable<string> GetFieldMetaSql(UserContext ctx, HtmlForm hForm, HtmlField htmlField)
        {
            List<string> lstSql = new List<string>();
            switch (htmlField.ElementType)
            {
                case HtmlElementType.HtmlField_BaseDataEntryField:
                    return this.GetFieldMetaSql(ctx, hForm, htmlField as HtmlBaseDataEntryField);

                case HtmlElementType.HtmlField_MulClassTypeField:
                    return this.GetFieldMetaSql(ctx, hForm, htmlField as HtmlMulClassTypeField);
            }

            return lstSql;
        }

        /// <summary>
        /// 获取字段关联的建表脚本
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="htmlField"></param>
        /// <returns></returns>
        protected IEnumerable<string> GetFieldMetaSql(UserContext ctx, HtmlForm htmlForm, HtmlBaseDataEntryField htmlField)
        {
            List<string> lstMetaSql = new List<string>();
            if (htmlField==null || htmlField.RefDynamicObjectType == null || htmlForm == null) return lstMetaSql;

            //如果配置了视图名称，则以配置的视图为准，解决整个系统只能有一个基础资料分录模型的问题
            var viewName = htmlField.DataViewName;
            if (viewName.IsNullOrEmptyOrWhiteSpace()) viewName = htmlField.RefDynamicObjectType.GetTableName();
            if (viewName.IsNullOrEmptyOrWhiteSpace()) return lstMetaSql;

            var ctlField = htmlForm.GetField(htmlField.ControlFieldKey) as HtmlBaseDataField;
            if (ctlField == null)
            {
                return lstMetaSql;
            }
            var controlFieldForm = HtmlParser.LoadFormMetaFromCache(ctlField.RefFormId, ctx);
            if (controlFieldForm == null) return lstMetaSql;

            var lookupEntity = controlFieldForm.GetEntryEntity(htmlField.LookupEntityKey);
            if (lookupEntity == null) return lstMetaSql;

            var lookupNumberField = controlFieldForm.GetField(htmlField.LookupNumberFieldKey);
            var lookupNameField = controlFieldForm.GetField(htmlField.LookupNameFieldKey);

            var numberField = controlFieldForm.GetNumberField();
            var nameField = controlFieldForm.GetNameField();


            SqlBuilderParameter sqlPara = new SqlBuilderParameter(ctx, controlFieldForm.Id);
            sqlPara.PageCount = -1;
            sqlPara.PageIndex = -1;
            sqlPara.IsDistinct = true;
            sqlPara.UseInneJoin = true;
            sqlPara.QueryUserFieldOnly = true;
            sqlPara.NoIsolation = true;
            sqlPara.NoColorSetting = true;
            sqlPara.IsShowForbidden = true;

            sqlPara.AddSelectField($"{lookupEntity.PkFieldName} as fid")
                .AddSelectField($"{lookupNumberField.FieldName} as fnumber")
                .AddSelectField($"{lookupNameField.FieldName} as fname")
                .AddSelectField($"'' as fname_py")
                .AddSelectField($"'' as fname_py2")
                .AddSelectField($"'{controlFieldForm.Id}' as fformid");

            HtmlEntity headEntity = controlFieldForm.HeadEntity;

            sqlPara.AddSelectField($"{headEntity.PkFieldName} as fpid")
                    .AddSelectField($"{numberField.FieldName} as fpnumber")
                    .AddSelectField($"{nameField.FieldName} as fpname")
                    .AddSelectField($"fmainorgid")
                    .AddSelectField($"fmainorgid_txt");

            var queryObj = QueryService.BuilQueryObject(sqlPara);

            if (queryObj.Sql.Length > 0)
            {
                var strCreateViewSql = $@"
    CREATE VIEW {viewName}
    AS
    {queryObj.Sql}
";
                lstMetaSql.Add($@"
IF NOT EXISTS(SELECT 1 FROM SYSOBJECTS WHERE NAME = '{viewName}' AND XTYPE = 'V')
BEGIN
    EXECUTE('{strCreateViewSql.Replace("'", "''")}')
END
");
            }
            return lstMetaSql;

        }


        /// <summary>
        /// 辅助生成自动构建库脚本
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="htmlField"></param>
        /// <returns></returns>
        protected IEnumerable<string> GetFieldMetaSql(UserContext ctx, HtmlForm htmlForm, HtmlMulClassTypeField htmlField)
        {
            List<string> lstMetaSql = new List<string>();
            if (htmlField == null) return lstMetaSql;
            if (htmlField.DataSource.IsNullOrEmpty()) return lstMetaSql;
            if (htmlField.DataViewName.IsNullOrEmptyOrWhiteSpace()) return lstMetaSql;

            string strViewSql = "";

            foreach (var item in htmlField.DataSource)
            {
                var linkFormMeta = HtmlParser.LoadFormMetaFromCache(item.FormId, ctx);
                SqlBuilderParameter sqlPara = new SqlBuilderParameter(ctx, item.FormId);
                sqlPara.PageCount = -1;
                sqlPara.PageIndex = -1;
                sqlPara.IsDistinct = true;
                sqlPara.UseInneJoin = true;
                sqlPara.NoIsolation = true;
                sqlPara.QueryUserFieldOnly = true;
                sqlPara.NoColorSetting = true;
                sqlPara.IsShowForbidden = true;

                sqlPara.AddSelectField($"{linkFormMeta.NumberFldKey} as fnumber")
                    .AddSelectField($"{linkFormMeta.NameFldKey} as fname")
                    .AddSelectField($"'' as fname_py")
                    .AddSelectField($"'' as fname_py2")
                    .AddSelectField($"{linkFormMeta.HeadEntity.Id}.fid as fid")
                    .AddSelectField($"'{item.FormId}' as ftypeformid")
                    .AddSelectField($"'bd_mulclasstypebasedata' as fformid");

                var descField = linkFormMeta.GetField("fdescription");
                if(descField!=null && descField.Entity is HtmlHeadEntity)
                {
                    sqlPara.AddSelectField(descField.Id);
                }
                var mainOrgField = linkFormMeta.GetField("fmainorgid");
                if(mainOrgField!=null && mainOrgField.Entity is HtmlHeadEntity)
                {
                    sqlPara.AddSelectField(mainOrgField.Id);
                    sqlPara.AddSelectField(mainOrgField.Id + "_txt");
                }

                if (!item.Filter.IsNullOrEmptyOrWhiteSpace())
                {
                    sqlPara.FilterString = " ( {0} ) ".Fmt(item.Filter);
                }
                var queryObj = QueryService.BuilQueryObject(sqlPara);
                if (strViewSql.IsNullOrEmptyOrWhiteSpace())
                {
                    strViewSql += queryObj.Sql;
                }
                else
                {
                    strViewSql += "\r\n union all \r\n" + queryObj.Sql;
                }
            }

            if (!strViewSql.IsNullOrEmptyOrWhiteSpace())
            {
                var strCreateViewSql = $@"
    CREATE VIEW {htmlField.DataViewName}
    AS
    {strViewSql}
";
                lstMetaSql.Add($@"
IF NOT EXISTS(SELECT 1 FROM SYSOBJECTS WHERE NAME = '{htmlField.DataViewName}' AND XTYPE = 'V')
BEGIN
    EXECUTE('{strCreateViewSql.Replace("'","''")}')
END
");
            }


            return lstMetaSql;
        }
    }
}
