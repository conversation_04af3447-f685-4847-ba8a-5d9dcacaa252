using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.IoC;
using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;
using JieNor.Framework.DataTransferObject.Const;

namespace JieNor.Framework.AppService.QueryBuilder
{
    /// <summary>
    /// 下拉列表数据服务
    /// </summary>
    [InjectService]
    public class ComboDataService : IComboDataService, IReceivedPubMessage
    {
        /// <summary>
        /// 获取某个表单模型里面所有下拉列表的下拉选项数据
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="excludeCity"></param>
        public Dictionary<string, List<BaseDataSummary>> GetFormComboDatas(UserContext ctx, string formId, bool excludeCity)
        {
            var htmlForm = HtmlParser.LoadFormMetaFromCache(formId, ctx);
            var flds = htmlForm.GetFieldList().Where(f => f is HtmlComboField).ToList();

            //不要合并从数据库查询，有设置特殊过滤条件时会产生错误数据
            Dictionary<string, List<BaseDataSummary>> returnData = new Dictionary<string, List<BaseDataSummary>>(StringComparer.OrdinalIgnoreCase);
            foreach (HtmlComboField item in flds)
            {
                //排除省市区下拉框，因为前端并不是用下拉框控件来展示省市区
                if (item.CategoryFilter.IsNullOrEmptyOrWhiteSpace()
                    || item.CategoryFilter.EqualsIgnoreCase("省")
                    || item.CategoryFilter.EqualsIgnoreCase("市")
                    || item.CategoryFilter.EqualsIgnoreCase("区"))
                {
                    continue;
                }

                var data = QueryComboData(ctx, item.CategoryFilter, item.Filter);
                returnData.Add(item.Id, data);
            }

            this.LoadBillTypeData(ctx, htmlForm, returnData);

            return returnData;
        }

        public Dictionary<string, List<BaseDataSummary>> GetBillTypeComboDatas(UserContext ctx, string formId)
        {
            var htmlForm = HtmlParser.LoadFormMetaFromCache(formId, ctx);

            //不要合并从数据库查询，有设置特殊过滤条件时会产生错误数据
            Dictionary<string, List<BaseDataSummary>> returnData = new Dictionary<string, List<BaseDataSummary>>(StringComparer.OrdinalIgnoreCase);

            this.LoadBillTypeData(ctx, htmlForm, returnData);

            return returnData;
        }


        /// <summary>
        /// 获取某个表单模型里面某个下拉列表的下拉选项数据
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="fldKey"></param>
        public List<BaseDataSummary> GetFormComboDatas(UserContext ctx, string formId, string fldKey)
        {
            var htmlForm = HtmlParser.LoadFormMetaFromCache(formId, ctx);
            var fld = htmlForm.GetField(fldKey) as HtmlComboField;
            var data = QueryComboData(ctx, fld.CategoryFilter, fld.Filter);
            return data;
        }



        /// <summary>
        /// 获取辅助资料信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="category"></param>
        /// <param name="filter"></param>
        public List<BaseDataSummary> GetComboDatas(UserContext ctx, string category, string filter)
        {
            var data = QueryComboData(ctx, category, filter);
            return data;
        }




        /// <summary>
        /// 清除辅助资料缓存
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="category"></param>
        public void ClearCache(UserContext ctx, string category)
        {
            //发送消息，通知其他负载均衡站点更新相关配置信息
            //改用Redis缓存辅助资料，无需通知其他站点 By：liren  2024-03-13
            //var pubSubService = ctx.Container.GetService<IPubSubService>();
            //pubSubService.PublishMessage(PubSubChannel.ComboData, new List<string> { ctx.Company, category });

            if (ctx.IsTopOrg)
            {
                var allOrgs = this.GetAllCompanys();
                foreach (var item in allOrgs)
                {
                    try
                    {
                        var orgCtx = this.CreateContextByCompanyInfo(item.Value);
                        var cache = orgCtx.Container.GetService<IRedisCache>();
                        var cacheKey = CacheKeyConst.CacheKey_Combo_Field_All.Fmt(orgCtx.Company, category);
                        cache.RemoveAllByKeyPattern(orgCtx, cacheKey, Enu_SearchType.Contains);
                    }
                    catch (Exception ex)
                    {
                    }
                }
            }
            else
            {
                try
                {
                    var cache = ctx.Container.GetService<IRedisCache>();
                    var cacheKey = CacheKeyConst.CacheKey_Combo_Field_All.Fmt(ctx.Company, category);
                    cache.RemoveAllByKeyPattern(ctx, cacheKey, Enu_SearchType.Contains);
                }
                catch (Exception ex)
                {
                }
            }

            //var allOrgs = this.GetAllCompanys();
            //foreach (var item in allOrgs)
            //{
            //    try
            //    {
            //        var orgCtx = this.CreateContextByCompanyInfo(item.Value);
            //        var cache = orgCtx.Container.GetService<IMemoryCache>();
            //        var cacheKey = CacheKeyConst.CacheKey_Combo_Field_All.Fmt(ctx.Company,category);
            //        cache.RemoveAllByKeyPattern(orgCtx, cacheKey, Enu_SearchType.Contains);
            //    }
            //    catch (Exception ex)
            //    { 
            //    }
            //}
        }




        /// <summary>
        /// 加载单据类型数据源
        /// </summary>
        /// <param name="comboData"></param>
        private void LoadBillTypeData(UserContext ctx, HtmlForm htmlForm, Dictionary<string, List<BaseDataSummary>> comboData)
        {
            var billTypeField = htmlForm.GetFieldList()?.FirstOrDefault(o => o is HtmlBillTypeField);
            if (billTypeField == null) return;

            var svc = ctx.Container.GetService<IBillTypeService>();
            var billTypeInfos = svc.GetBillTypeInfors(ctx, htmlForm);
             
            var billTypeData = billTypeInfos.Select(o => new BaseDataSummary
            {
                Id = o.fid  ,
                Number = o.fnumber   ,
                Name = o.fname  ,
                // 兼容字符和布尔值
                Disable =o.fforbidstatus  ,
                IsPreset =o.fispreset ,  
            }).ToList();

            comboData.Add(billTypeField.Id.ToLower(), billTypeData);
        }

        private List<BaseDataSummary> QueryComboData(UserContext ctx, string category, string filter)
        {
            var cache = ctx.Container.GetService<IRedisCache>();
            var cacheKey = CacheKeyConst.CacheKey_Combo_Field.Fmt(ctx.Company, category, filter);
            List<BaseDataSummary> enumData = null;
            try
            {
                enumData = cache.Get<List<BaseDataSummary>>(ctx, cacheKey);
            }
            catch (Exception e)
            {
                // ignored
            }
            if (enumData == null || enumData.Count == 0)
            {
                enumData = new List<BaseDataSummary>();
                SqlBuilderParameter para = BuildQueryPara(ctx);
                para.SetFilter(GetFilter(category));

                if (!filter.IsNullOrEmptyOrWhiteSpace())
                {
                    para.FilterString = " ( {0} ) ".Fmt(filter);
                }

                var querySvc = ctx.Container.GetService<IListSqlBuilder>();
                var _dicEnumData = querySvc.GetFuzzyQueryData(ctx, para);
                foreach (var item in _dicEnumData)
                {
                    enumData.Add(new BaseDataSummary()
                    {
                        Id = item["fbillhead_id"].ToString(),
                        Number = item["fenumitem"].ToString(),
                        Name = item["fenumitem"].ToString(),
                        Disable = Convert.ToString(item["fforbidstatus"]).EqualsIgnoreCase("1") ? true : false,
                        IsPreset = Convert.ToString(item["fissys"]).EqualsIgnoreCase("1") ? true : false
                    });
                }

                cache.Set(ctx, cacheKey, enumData, TimeSpan.FromDays(7));
            }

            return enumData;
        }

        private SqlBuilderParameter BuildQueryPara(UserContext ctx)
        {
            HtmlForm htmlForm = HtmlParser.LoadFormMetaFromCache("bd_enum", ctx);
            SqlBuilderParameter para = new SqlBuilderParameter(ctx, "bd_enum");
            para.PageCount = 10000;
            para.PageIndex = 1;

            //主键
            var pkId = htmlForm.GetField(htmlForm.BillPKFldName);
            if (pkId != null)
            {
                para.SelectedFieldKeys.Add(htmlForm.BillPKFldName);
            }
            var noFld = htmlForm.GetField("fcategory");
            if (noFld != null)
            {
                para.SelectedFieldKeys.Add("fcategory");
            }
            var nameFld = htmlForm.GetField("fenumitem");
            if (nameFld != null)
            {
                para.SelectedFieldKeys.Add("fenumitem");
            }
            var statusFld = htmlForm.GetField("fforbidstatus");
            if (statusFld != null)
            {
                para.SelectedFieldKeys.Add("fforbidstatus");
            }
            var orderFld = htmlForm.GetField("forder");
            if (orderFld != null)
            {
                para.SelectedFieldKeys.Add("forder");
            }
            para.SelectedFieldKeys.Add("fissys");
            para.WhereString.Clear();
            para.OrderByString = "fforbidstatus asc,forder asc";

            return para;
        }


        /// <summary>
        /// 获取过滤条件
        /// </summary> 
        /// <returns></returns>
        private List<FilterRowObject> GetFilter(string category)
        {
            List<FilterRowObject> lst = new List<FilterRowObject>();
            if (!category.IsNullOrEmptyOrWhiteSpace())
            {
                FilterRowObject filter = new FilterRowObject();
                filter.Id = "FCategory";
                filter.Logic = "And";
                filter.Operator = "=";
                filter.Value = category;
                filter.RowIndex = 1;
                lst.Add(filter);
            }
            return lst;
        }

        #region 订阅清缓存

        public string ChannelName => PubSubChannel.ComboData;


        public void OnReceivedMessage(string msg)
        {
            var items = msg.FromJson<List<string>>();

            if (items.Count < 2)
            {
                return;
            }

            var companyId = items[0];
            var category = items[1];

            var ctx = "".CreateDBContextByCompanyId(companyId);
            if (ctx.IsTopOrg)
            {
                var allOrgs = this.GetAllCompanys();
                foreach (var item in allOrgs)
                {
                    try
                    {
                        var orgCtx = this.CreateContextByCompanyInfo(item.Value);
                        var cache = orgCtx.Container.GetService<IRedisCache>();
                        var cacheKey = CacheKeyConst.CacheKey_Combo_Field_All.Fmt(ctx.Company, category);
                        cache.RemoveAllByKeyPattern(orgCtx, cacheKey, Enu_SearchType.Contains);
                    }
                    catch (Exception ex)
                    {
                    }
                }
            }
            else
            {
                try
                {
                    var cache = ctx.Container.GetService<IRedisCache>();
                    var cacheKey = CacheKeyConst.CacheKey_Combo_Field_All.Fmt(ctx.Company, category);
                    cache.RemoveAllByKeyPattern(ctx, cacheKey, Enu_SearchType.Contains);
                }
                catch (Exception ex)
                {
                }
            }
        }

        #endregion
    }
}