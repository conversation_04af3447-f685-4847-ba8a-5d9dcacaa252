using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using Newtonsoft.Json.Linq;
using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace JieNor.Framework.Interface.QueryBuilder
{
    /// <summary>
    /// 查询服务
    /// </summary>
    [Serializable]
    public class QueryService
    {
        private static Regex _regexUserId = new Regex(@"@currentUserId\b", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static Regex _regexUserName = new Regex(@"@currentUserName\b", RegexOptions.Compiled | RegexOptions.IgnoreCase);


        /// <summary>
        /// 缓存模型可选的字段列表
        /// </summary>
        static ConcurrentDictionary<string, QueryMetaInfo> htmlFormQueryInfos = new ConcurrentDictionary<string, QueryMetaInfo>();


        /// <summary>
        /// 获得表单查询元模型
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="hForm"></param>
        /// <returns></returns>
        public static QueryMetaInfo GetHtmlFormQueryMetaInfo(UserContext userCtx, HtmlForm hForm)
        {
            AnalysisHtmlFormFld(userCtx, hForm);

            return htmlFormQueryInfos[hForm.FormCacheId];
        }

        /// <summary>
        /// 清除缓存
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formId"></param>
        public static void ClearQueryMetaInfo(UserContext userCtx, string formId)
        {
            if (formId.IsNullOrEmptyOrWhiteSpace()) return;
            QueryMetaInfo info = null;
            foreach (var kvpItem in htmlFormQueryInfos.ToArray())
            {
                if (kvpItem.Value.HtmlForm?.Id.EqualsIgnoreCase(formId) == true)
                {
                    htmlFormQueryInfos.TryRemove(kvpItem.Key, out info);
                }
            }
        }

        /// <summary>
        /// 获取指定表单模型的查询字段对象
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="fldKey"></param>
        /// <returns></returns>
        public static SelectField GetSelectField(UserContext ctx, string formId, string fldKey)
        {
            var hform = HtmlParser.LoadFormMetaFromCache(formId, ctx);
            var queryInfo = GetHtmlFormQueryMetaInfo(ctx, hform);

            return queryInfo.GetSelectField(fldKey);
        }

        /// <summary>
        /// 获取指定实体查询对象
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="entityKey"></param>
        /// <returns></returns>
        public static EntityTable GetEntityTable(UserContext ctx, string formId, string entityKey)
        {
            var hform = HtmlParser.LoadFormMetaFromCache(formId, ctx);
            var queryInfo = GetHtmlFormQueryMetaInfo(ctx, hform);

            return queryInfo.GetEntityTable(entityKey);
        }

        /// <summary>
        /// 构建查询对象
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        public static QueryObject BuilQueryObject(SqlBuilderParameter para)
        {
            lock (para)
            {
                QueryObject queryObj = new QueryObject();

                queryObj.PageCount = para.PageCount;
                queryObj.PageIndex = para.PageIndex;

                if (para.SrcPara == null)
                {
                    para.SrcPara = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
                }

                //自定义查询参数：（系统）当前用户名、用户Id、所属企业Id等
                //不能加一些查询中可能用不到的参数，这样有可能触发sqlserver的参数嗅探规则，导致查询剧慢
                //AppendSysParams(para);

                //增加数据范围的过滤（数据权限）
                AppendDataRowAclFilter(para);

                //加上业务插件里面实现 IDataQueryRule 的过滤条件
                AppendDataQueryRuleFilter(para);

                AnalysisHtmlFormFld(para.Ctx, para.HtmlForm);

                AnalysisSelectFld(para, queryObj);

                BuildSelectSql(para, queryObj);

                //构建查询条件，并输出
                List<string> lstFilterFields = new List<string>();
                BuildWhereSql(para, queryObj, out lstFilterFields);

                BuildFromSql(para, queryObj, lstFilterFields);
                BuildStaticFromSql(para, queryObj, lstFilterFields);


                BuildOrderBySql(para, queryObj);

                BuildGroupBySql(para, queryObj);

                BuildSumAmountSql(para, queryObj);//加上字段统计查询

                return queryObj;
            }
        }


        /// <summary>
        /// 自定义查询参数：（系统）当前用户名、用户Id、所属企业Id等
        /// </summary>
        /// <param name="para"></param>
        private static void AppendSysParams(SqlBuilderParameter para)
        {
            if (para != null && para.DynamicParams != null)
            {
                //para.AddParameter(new SqlParam("currentUserName", System.Data.DbType.String, para.Ctx.UserName));
                //para.AddParameter(new SqlParam("currentUserId", System.Data.DbType.String, para.Ctx.UserId));
                //para.AddParameter(new SqlParam("currentCompanyId", System.Data.DbType.String, para.Ctx.Company));
            }
        }


        /// <summary>
        /// 构建 group by 语句
        /// </summary>
        private static void BuildGroupBySql(SqlBuilderParameter para, QueryObject queryObj)
        {

        }

        /// <summary>
        /// 构建 order by 语句
        /// </summary>
        private static void BuildOrderBySql(SqlBuilderParameter para, QueryObject queryObj)
        {
            List<string> orderBy = new List<string>();
            var flds = (para.OrderByString ?? "").Split(',');
            var queryInfo = GetHtmlFormQueryMetaInfo(para.Ctx, para.HtmlForm);
            foreach (var fld in flds)
            {
                if (fld.IsNullOrEmptyOrWhiteSpace()) continue;

                var order = "";
                var str = fld.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                if (str.Length > 1)
                {
                    order = str[1];
                }

                var x = queryInfo.GetSelectField(str[0]);
                if (x != null && queryObj.SelectFlds.FirstOrDefault(o => o.EntityKey?.EqualsIgnoreCase(x.EntityKey) == true) != null)
                {
                    orderBy.Add(string.Format("{0} {1}", x.SelectFldName, order));
                }
            }

            if (orderBy.Count > 0)
            {
                queryObj.SqlOrderBy = string.Format("Order By {0} ", string.Join(",", orderBy));
            }

            if (queryObj.SqlOrderBy.IsNullOrEmptyOrWhiteSpace())
                SetDefaultOrderBy(para, queryObj);
        }

        /// <summary>
        /// 默认的排序：单据默认按编码降序排，基础资料默认按编码升序排
        /// </summary>
        private static void SetDefaultOrderBy(SqlBuilderParameter para, QueryObject queryObj)
        {
            //默认按创建日期降序
            if (!para.HtmlForm.CreateDateFldKey.IsNullOrEmptyOrWhiteSpace())
            {
                var fld = queryObj.SelectFlds.FirstOrDefault(f => f.Id.EqualsIgnoreCase(para.HtmlForm.CreateDateFldKey));
                if (fld != null)
                {
                    queryObj.SqlOrderBy = string.Format("Order By {0} Desc", fld.SelectFldName);
                }
            }

            if (queryObj.SqlOrderBy.IsNullOrEmptyOrWhiteSpace() == true)
            {
                var fld = queryObj.SelectFlds.FirstOrDefault(f => f.Id.EqualsIgnoreCase(para.HtmlForm.NumberFldKey));
                if (fld != null)
                {
                    if (para.HtmlForm.ElementType == HtmlElementType.HtmlForm_BillForm)
                    {
                        queryObj.SqlOrderBy = string.Format("Order By {0} Desc", fld.SelectFldName);
                    }
                    else
                    {
                        queryObj.SqlOrderBy = string.Format("Order By {0} ", fld.SelectFldName);
                    }
                }
            }
            if (queryObj.SqlOrderBy.IsNullOrEmptyOrWhiteSpace() == true)
            {
                var fld = queryObj.SelectFlds.FirstOrDefault(f => f.Id.EqualsIgnoreCase(para.HtmlForm.NameFldKey));
                if (fld != null)
                {
                    queryObj.SqlOrderBy = string.Format("Order By {0} ", fld.SelectFldName);
                }
            }

            if (queryObj.SqlOrderBy.IsNullOrEmptyOrWhiteSpace() == true)
            {
                //报表式列表，默认按 fseq 升序
                if (para.HtmlForm.ElementType == HtmlElementType.HtmlForm_ListReportForm)
                {
                    var seqField = queryObj.SelectFlds.FirstOrDefault(f => f.Id.EqualsIgnoreCase("fseq"));
                    if (seqField != null)
                    {
                        queryObj.SqlOrderBy = string.Format("Order By {0} ", seqField.SelectFldName);
                    }
                    else
                    {
                        seqField = queryObj.SelectFlds.FirstOrDefault();
                        queryObj.SqlOrderBy = string.Format("Order By {0} ", seqField.SelectFldName);
                    }
                }
                else
                {
                    var fld = queryObj.SelectFlds.FirstOrDefault(f => f.Id.EqualsIgnoreCase(para.HtmlForm.BillPKFldName));
                    if (fld != null)
                    {
                        queryObj.SqlOrderBy = string.Format("Order By {0} ", fld.SelectFldName);
                    }
                }
            }
        }

        /// <summary>
        /// 构建 where 语句 
        /// </summary>
        private static void BuildWhereSql(SqlBuilderParameter para, QueryObject queryObj, out List<string> lstFieldKeys)
        {
            lstFieldKeys = new List<string>();

            QueryMetaInfo queryInfo = GetHtmlFormQueryMetaInfo(para.Ctx, para.HtmlForm);

            if (queryInfo.AllQueryFields.IsEmpty()) queryInfo = new QueryMetaInfo(para.HtmlForm, queryObj.SelectFlds);

            // 是否来源于角色授权
            var fromAssignright = (para.SrcPara?.GetValue("fromFormId") ?? "").EqualsIgnoreCase("sec_assignright");

            // 是否来源于主控菜单
            var fromMainFw = !(para.SrcPara?.GetValue("mainFwMenuId") ?? "").IsNullOrEmptyOrWhiteSpace();

            StringBuilder sbFilter = new StringBuilder();
            var mainorgfld = para.HtmlForm.GetField("fmainorgid");
            if (para.HtmlForm.Id.EqualsIgnoreCase("bd_mulclasstypebasedata"))
            {
                //数据隔离方案里面的部门、员工数据要按组织隔离
                sbFilter.AppendFormat(" fmainorgid=@currentCompanyId ");
                para.AddParameter(new SqlParam("currentCompanyId", System.Data.DbType.String, para.Ctx.Company));
            }
            else if (para.HtmlForm.Id.EqualsIgnoreCase("bas_organization") && para.Ctx.IsTopOrg)//
            {
                sbFilter.AppendFormat(" ftopcompanyid=@toporgid ");
                para.AddParameter(new SqlParam("toporgid", System.Data.DbType.String, para.Ctx.TopCompanyId));
            }
            else if (mainorgfld != null && !para.NoIsolation && "1".EqualsIgnoreCase(para.HtmlForm.Isolate))
            {
                para.AddParameter(new SqlParam("currentCompanyId", System.Data.DbType.String, para.Ctx.Company));
                //不要写fmainorgid='0'，=‘0’通常是业务预置的跨组织共享数据，不希望被前端读取到。
                //以上注释说不要写 fmainorgid='0' 是因为当时预置的数据在前端可以修改，导致有问题，现在平台已经有通用的锁定性规则控制（预置数据的 fispreset='1'的数据在页面上都已被锁定）
                //所以要求预置数据必须设置 fispreset 字段为 1
                if (para.HtmlForm.NotShowShareData)
                {
                    sbFilter.AppendFormat(" fmainorgid=@currentCompanyId ");
                }
                else
                {
                    para.SrcPara.TryGetValue("selectDataType", out string value);
                    if (value.EqualsIgnoreCase("parentorg"))
                    {
                        para.AddParameter(new SqlParam("parentCompanyId", System.Data.DbType.String, para.Ctx.ParentCompanyId));
                        sbFilter.AppendFormat(" ( fmainorgid='0' or fmainorgid=@parentCompanyId ) ");
                    }
                    else {
                       
                        sbFilter.AppendFormat(" ( fmainorgid='0' or fmainorgid=@currentCompanyId ) ");
                    }
                 
                }
            }

            // 总部视角下要显示经销商的管理员用户
            else if (para.Ctx.IsTopOrg && para.FormId.EqualsIgnoreCase("sec_user") && (fromAssignright || fromMainFw))
            {
                sbFilter.Append(" 1=1 ");
            }
            else if (mainorgfld != null && !para.NoIsolation && "0".EqualsIgnoreCase(para.HtmlForm.Isolate))//不隔离：取本企业的及总部企业的
            {
                //如果业务数据查询规则中有明确指定不按企业隔离，则忽略企业过滤条件
                if ((para.SrcPara?.GetValue("IgnoreCompanyIsolation") ?? "").EqualsIgnoreCase("true"))
                {
                    sbFilter.Append(" 1=1 ");
                }
                else
                {
                    // 父级企业过滤条件
                    var parentCompanyIdFilter = "";
                    if (!para.Ctx.ParentCompanyId.IsNullOrEmptyOrWhiteSpace())
                    {
                        parentCompanyIdFilter = $" or fmainorgid=@parentCompanyId";
                        para.AddParameter(new SqlParam("@parentCompanyId", System.Data.DbType.String, para.Ctx.ParentCompanyId));
                    }

                    para.AddParameter(new SqlParam("currentCompanyId", System.Data.DbType.String, para.Ctx.Company));
                    para.AddParameter(new SqlParam("@toporgid", System.Data.DbType.String, para.Ctx.TopCompanyId));
                    if (para.HtmlForm.NotShowShareData)
                    {
                        sbFilter.AppendFormat($"(fmainorgid=@currentCompanyId or fmainorgid=@toporgid {parentCompanyIdFilter})");
                    }
                    else
                    {
                        sbFilter.AppendFormat($"(fmainorgid='0' or fmainorgid=@currentCompanyId or fmainorgid=@toporgid {parentCompanyIdFilter})");
                    }
                }
            }
            else
            {
                sbFilter.Append(" 1=1 ");
            }

            if (!para.FilterString.IsNullOrEmptyOrWhiteSpace())
                sbFilter.Append($" and {para.FilterString}  ");

            //辅助资料不需要加禁用过滤条件，因为辅助资料下拉框需要显示已禁用的选项，只是要控制不让用户选择即可
            if (para.HtmlForm.Id.EqualsIgnoreCase("bd_enum") == false)
            {
                //判断是否要增加禁用过滤条件：如果前端没有明确对禁用状态作过滤，则自动追加禁用数据不显示的过滤条件
                var forbidStatusFld = para.HtmlForm.GetField(para.HtmlForm.ForbidStatusFldKey);
                if (forbidStatusFld == null)
                {
                    forbidStatusFld = para.HtmlForm.GetField(para.HtmlForm.CancelStatusFldKey);
                }
                if (forbidStatusFld != null && para.IsShowForbidden == false)
                {
                    var forbidStatusFilterObj = para.WhereString?.FirstOrDefault(x => x.Id.EqualsIgnoreCase($"{forbidStatusFld.Id}_txt"));
                    if (forbidStatusFilterObj == null)
                    {
                        para.WhereString.Add(new FilterRowObject()
                        {
                            Id = $"{forbidStatusFld.Id}_txt",
                            Operator = "=##0",
                            Value = ""
                        });
                    }
                }
            }


            if (para.WhereString?.Any() == true)
            {
                StringBuilder strWhereSql = new StringBuilder();
                foreach (var filterRow in para.WhereString)
                {
                    if (filterRow.Id.IsNullOrEmptyOrWhiteSpace())
                    {
                        continue;
                    }

                    var selField = queryInfo.GetSelectField(filterRow.Id);
                    if (selField == null)
                    {
                        continue;
                    }

                    string filter = filterRow.ToFilterString(para.Ctx, selField, para.DynamicParams, para.HtmlForm);

                    if (string.IsNullOrEmpty(filter) == false)
                    {
                        var memo = filterRow.Memo.IsNullOrEmptyOrWhiteSpace() ? "" : Environment.NewLine + filterRow.Memo + Environment.NewLine;
                        strWhereSql.AppendLine().AppendFormat("{0} {1} {2} ",
                                string.Equals("or", filterRow.Logic, StringComparison.CurrentCultureIgnoreCase) ? "or" : "and",
                                memo,
                                filter);
                    }
                }

                if (!strWhereSql.IsNullOrEmptyOrWhiteSpace())
                {
                    sbFilter.Append(strWhereSql.ToString());
                }
            }

            //如果存在条件，才拼接 Where
            if (sbFilter.Length > 0)
            {
                //翻译sql条件
                List<string> fldKeys = null;
                var sqlParser = para.Ctx?.Container?.GetService<ISqlStringParser>();

                var strFilter = "";
                try
                {
                    strFilter = sqlParser?.GetRealSqlFilter(para.Ctx, para.HtmlForm, sbFilter.ToString(), out fldKeys);
                }
                catch (Exception ex)
                {
                    var logService = para.Ctx?.Container?.GetService<Log.ILogServiceEx>();
                    logService?.Error($"翻译sql条件出错，WhereString：{para.WhereString?.ToJson()}，SqlWhereString：{sbFilter}", ex);
                    throw;
                }
                lstFieldKeys = new List<string>(fldKeys.Distinct());
                foreach (var item in fldKeys)
                {
                    if (item.IndexOf('.') > -1)
                    {
                        var arr = item.Split('.');
                        lstFieldKeys.Add(arr[0]);
                    }
                }
                queryObj.SqlWhere = string.Format("Where {0} ", strFilter);
            }

            //不确定到底有没有业务在如过滤方案或者js插件里直接拼参数，因此在这里统一拦截处理
            if (_regexUserId.IsMatch(queryObj.SqlWhere))
            {
                para.AddParameter(new SqlParam("currentUserId", System.Data.DbType.String, para.Ctx.UserId));
            }
            if (_regexUserName.IsMatch(queryObj.SqlWhere))
            {
                para.AddParameter(new SqlParam("currentUserName", System.Data.DbType.String, para.Ctx.UserName));
            }

        }

        /// <summary>
        /// 构建 from 语句
        /// </summary>
        private static void BuildFromSql(SqlBuilderParameter para, QueryObject queryObj, List<string> lstFilterFields)
        {
            List<string> sbSqlFrom = new List<string>();

            var head = queryObj.SelectFlds.FirstOrDefault(f => f.EntityKey.EqualsIgnoreCase(para.HtmlForm.HeadEntity.Id));
            if (head != null)
            {
                //主表
                if (para.HtmlForm.ElementType == HtmlElementType.HtmlForm_ListReportForm || para.HtmlForm.ElementType == HtmlElementType.HtmlForm_ReportForm)
                {
                    sbSqlFrom.Add(string.Format("From {0} AS {1} {2}", para.Ctx.Meta["rptTempTableName"], head.EntityTable.TableNameAs, GetWithSql(para)));
                }
                else
                {
                    sbSqlFrom.Add(head.EntityTable.FromTableAs(para.ReadDirty));
                }
            }

            var lstAllQueryFlds = queryObj.SelectFlds.ToList();
            QueryMetaInfo queryInfo = GetHtmlFormQueryMetaInfo(para.Ctx, para.HtmlForm);
            foreach (var filterFieldKey in lstFilterFields)
            {
                var selField = queryInfo.GetSelectField(filterFieldKey);
                if (selField != null)
                {
                    lstAllQueryFlds.Add(selField);
                }
            }

            string sumExpression = string.Empty;//计算列
            string join = "";
            List<string> fromKey = new List<string>();
            foreach (var entry in para.HtmlForm.EntryList)
            {
                var fld = lstAllQueryFlds.FirstOrDefault(f => f.IsRefFld == false && f.EntityKey.EqualsIgnoreCase(entry.Id));
                if (fld != null)
                {
                    //明细表
                    join = para.UseInneJoin
                        ? fld.EntityTable.InnerJoinTableAs(fld.ParentTable.PkFldName, fld.ParentTable.FullPkFldName, para.ReadDirty)
                        : fld.EntityTable.LeftJoinTableAs(fld.ParentTable.PkFldName, fld.ParentTable.FullPkFldName, para.ReadDirty);
                    if (!fromKey.Contains(join))
                    {
                        sbSqlFrom.Add(join);
                        fromKey.Add(join);
                    }
                }

                if (entry.SubEntryList != null && entry.SubEntryList.Count > 0)
                {
                    foreach (var subEn in entry.SubEntryList)
                    {
                        fld = lstAllQueryFlds.FirstOrDefault(f => f.EntityKey.EqualsIgnoreCase(subEn.Id));
                        if (fld != null)
                        {
                            //父级表和主表的关联(列表查询可能只匹配了子表，所以这里查询下父级表有无关联)
                            join = para.UseInneJoin
                               ? fld.ParentTable.InnerJoinTableAs(fld.ParentTable.ParentTable?.PkFldName, fld.ParentTable.ParentTable?.FullPkFldName, para.ReadDirty)
                               : fld.ParentTable.LeftJoinTableAs(fld.ParentTable.ParentTable?.PkFldName, fld.ParentTable.ParentTable?.FullPkFldName, para.ReadDirty);
                            if (!fromKey.Contains(join))
                            {
                                sbSqlFrom.Add(join);
                                fromKey.Add(join);
                            }

                            //子表
                            join = para.UseInneJoin
                                ? fld.EntityTable.InnerJoinTableAs(fld.ParentTable.PkFldName, fld.ParentTable.FullPkFldName, para.ReadDirty)
                                : fld.EntityTable.LeftJoinTableAs(fld.ParentTable.PkFldName, fld.ParentTable.FullPkFldName, para.ReadDirty);
                            if (!fromKey.Contains(join))
                            {
                                sbSqlFrom.Add(join);
                                fromKey.Add(join);
                            } 
                        }
                    }
                }
            }


            //关联基础资料表或拆分表
            foreach (var item in lstAllQueryFlds)
            {
                if (item.IsSuffixTable)
                {
                    //关联拆分表取数
                    join = para.UseInneJoin
                        ? item.SuffixTable.InnerJoinTableAs(item.EntityTable.PkFldName, item.EntityTable.TableNameAs + "." + item.EntityTable.PkFldName, para.ReadDirty)
                        : item.SuffixTable.LeftJoinTableAs(item.EntityTable.PkFldName, item.EntityTable.TableNameAs + "." + item.EntityTable.PkFldName, para.ReadDirty);
                    if (item.SuffixTable.FilterString.IsNullOrEmptyOrWhiteSpace() == false)
                    {
                        join += " And " + item.SuffixTable.FilterString.Replace(TableNameConst.TempTableNameAs, item.SuffixTable.TableNameAs);
                    }
                    if (!fromKey.Contains(join))
                    {
                        sbSqlFrom.Add(join);
                        fromKey.Add(join);
                    }
                }

                //引用字段
                if (item.IsRefFld)
                {
                    if (item.IsSuffixTable)
                    {
                        //引用表要关联到拆分表
                        join = para.UseInneJoin
                            ? item.RefTable.InnerJoinTableAs(item.RefTable.PkFldName, item.SuffixTable.TableNameAs + "." + item.FieldName, para.ReadDirty)
                            : item.RefTable.LeftJoinTableAs(item.RefTable.PkFldName, item.SuffixTable.TableNameAs + "." + item.FieldName, para.ReadDirty);
                        if (item.RefTable.FilterString.IsNullOrEmptyOrWhiteSpace() == false)
                        {
                            join += " And " + item.RefTable.FilterString.Replace(TableNameConst.TempTableNameAs, item.RefTable.TableNameAs);
                        }
                        if (!fromKey.Contains(join))
                        {
                            sbSqlFrom.Add(join);
                            fromKey.Add(join);
                        }
                    }
                    else
                    {
                        join = para.UseInneJoin
                            ? item.RefTable.InnerJoinTableAs(item.RefTable.PkFldName, item.EntityTable.TableNameAs + "." + item.FieldName, para.ReadDirty)
                            : item.RefTable.LeftJoinTableAs(item.RefTable.PkFldName, item.EntityTable.TableNameAs + "." + item.FieldName, para.ReadDirty);
                        if (item.RefTable.FilterString.IsNullOrEmptyOrWhiteSpace() == false)
                        {
                            join += " And " + item.RefTable.FilterString.Replace(TableNameConst.TempTableNameAs, item.RefTable.TableNameAs);
                        }
                        if (!fromKey.Contains(join))
                        {
                            sbSqlFrom.Add(join);
                            fromKey.Add(join);
                        }
                    }
                    if (item.RefPropertyTable != null)
                    {
                        join = para.UseInneJoin
                            ? item.RefPropertyTable.InnerJoinTableAs(item.RefPropertyTable.PkFldName, item.RefTable.TableNameAs + "." + item.RefFieldName, para.ReadDirty)
                            : item.RefPropertyTable.LeftJoinTableAs(item.RefPropertyTable.PkFldName, item.RefTable.TableNameAs + "." + item.RefFieldName, para.ReadDirty);
                        if (!fromKey.Contains(join))
                        {
                            sbSqlFrom.Add(join);
                            fromKey.Add(join);
                        }
                    }
                }
            }
            queryObj.SqlFrom = string.Join(Environment.NewLine, sbSqlFrom.Distinct());
        }

        /// <summary>
        /// 构建统计分析时的 from 语句
        /// </summary>
        /// <param name="para"></param>
        /// <param name="queryObj"></param>
        /// <param name="lstFilterFields"></param>
        private static void BuildStaticFromSql(SqlBuilderParameter para, QueryObject queryObj, List<string> lstFilterFields)
        {
            StringBuilder sbBillCountSqlFrom = new StringBuilder();
            StringBuilder sbAllCountSqlFrom = new StringBuilder();

            var head = queryObj.SelectFlds.FirstOrDefault(f => f.EntityKey.EqualsIgnoreCase(para.HtmlForm.HeadEntity.Id));
            if (head != null)
            {
                //主表
                if (para.HtmlForm.ElementType == HtmlElementType.HtmlForm_ListReportForm || para.HtmlForm.ElementType == HtmlElementType.HtmlForm_ReportForm)
                {
                    sbBillCountSqlFrom.AppendLine(string.Format("From {0} AS {1} {2}", para.Ctx.Meta["rptTempTableName"], head.EntityTable.TableNameAs, GetWithSql(para)));
                    sbAllCountSqlFrom.AppendLine(string.Format("From {0} AS {1} {2}", para.Ctx.Meta["rptTempTableName"], head.EntityTable.TableNameAs, GetWithSql(para)));
                }
                else
                {
                    sbBillCountSqlFrom.AppendLine(head.EntityTable.FromTableAs(para.ReadDirty));
                    sbAllCountSqlFrom.AppendLine(head.EntityTable.FromTableAs(para.ReadDirty));
                }
            }

            var selectFlds = new List<SelectField>();
            var lstAllQueryFlds = new List<SelectField>();
            QueryMetaInfo queryInfo = GetHtmlFormQueryMetaInfo(para.Ctx, para.HtmlForm);
            foreach (var filterFieldKey in lstFilterFields)
            {
                var selField = queryInfo.GetSelectField(filterFieldKey);
                if (selField != null)
                {
                    selectFlds.Add(selField);
                    lstAllQueryFlds.Add(selField);
                }
                else
                {
                    //filterFieldKey 已经是变成了 表别名.表字段的形式，queryInfo找不到，要之前分析好的queryObj获取
                    selField = queryObj.SelectFlds.FirstOrDefault(f => filterFieldKey.Trim().EndsWithIgnoreCase(f.SelectFldName.Trim()));
                    if (selField != null)
                    {
                        selectFlds.Add(selField);
                        lstAllQueryFlds.Add(selField);
                    }
                }
            }

            //如果 select 的字段包含明细表的字段，也要关联明细表
            foreach (var fieldKey in para.SelectedFieldKeys)
            {
                var selField = queryInfo.GetSelectField(fieldKey);
                if (selField != null && !selectFlds.Contains(selField))
                {
                    selectFlds.Add(selField);
                }
            }

            string sumExpression = string.Empty;//计算列
            string join = "";
            List<string> fromKey = new List<string>();
            foreach (var entry in para.HtmlForm.EntryList)
            {
                var fld = selectFlds.FirstOrDefault(f => f.EntityKey.EqualsIgnoreCase(entry.Id));
                if (fld != null)
                {
                    //明细表
                    join = para.UseInneJoin
                        ? fld.EntityTable.InnerJoinTableAs(fld.ParentTable.PkFldName, fld.ParentTable.FullPkFldName, para.ReadDirty)
                        : fld.EntityTable.LeftJoinTableAs(fld.ParentTable.PkFldName, fld.ParentTable.FullPkFldName, para.ReadDirty);
                    if (!fromKey.Contains(join))
                    {
                        sbBillCountSqlFrom.AppendLine(join);
                        sbAllCountSqlFrom.AppendLine(join);
                        fromKey.Add(join);
                    }
                }

                if (entry.SubEntryList != null && entry.SubEntryList.Count > 0)
                {
                    foreach (var subEn in entry.SubEntryList)
                    {
                        fld = selectFlds.FirstOrDefault(f => f.EntityKey.EqualsIgnoreCase(subEn.Id));
                        if (fld != null)
                        {
                            //父级表和主表的关联(列表查询可能只匹配了子表，所以这里查询下父级表有无关联)
                            join = para.UseInneJoin
                               ? fld.ParentTable.InnerJoinTableAs(fld.ParentTable.ParentTable?.PkFldName, fld.ParentTable.ParentTable?.FullPkFldName, para.ReadDirty)
                               : fld.ParentTable.LeftJoinTableAs(fld.ParentTable.ParentTable?.PkFldName, fld.ParentTable.ParentTable?.FullPkFldName, para.ReadDirty);
                            if (!fromKey.Contains(join))
                            {
                                sbBillCountSqlFrom.AppendLine(join);
                                sbAllCountSqlFrom.AppendLine(join);
                                fromKey.Add(join);
                            }

                            //子表
                            join = para.UseInneJoin
                                ? fld.EntityTable.InnerJoinTableAs(fld.ParentTable.PkFldName, fld.ParentTable.FullPkFldName, para.ReadDirty)
                                : fld.EntityTable.LeftJoinTableAs(fld.ParentTable.PkFldName, fld.ParentTable.FullPkFldName, para.ReadDirty);
                            if (!fromKey.Contains(join))
                            {
                                sbBillCountSqlFrom.AppendLine(join);
                                sbAllCountSqlFrom.AppendLine(join);
                                fromKey.Add(join);
                            }
                        }
                    }
                }
            }


            //关联基础资料表或拆分表
            foreach (var item in lstAllQueryFlds)
            {
                if (item.IsSuffixTable)
                {
                    //关联拆分表取数
                    join = para.UseInneJoin
                        ? item.SuffixTable.InnerJoinTableAs(item.EntityTable.PkFldName, item.EntityTable.TableNameAs + "." + item.EntityTable.PkFldName, para.ReadDirty)
                        : item.SuffixTable.LeftJoinTableAs(item.EntityTable.PkFldName, item.EntityTable.TableNameAs + "." + item.EntityTable.PkFldName, para.ReadDirty);
                    if (item.SuffixTable.FilterString.IsNullOrEmptyOrWhiteSpace() == false)
                    {
                        join += " And " + item.SuffixTable.FilterString.Replace(TableNameConst.TempTableNameAs, item.SuffixTable.TableNameAs);
                    }
                    if (!fromKey.Contains(join))
                    {
                        sbBillCountSqlFrom.AppendLine(join);
                        sbAllCountSqlFrom.AppendLine(join);
                        fromKey.Add(join);
                    }
                }

                //引用字段
                if (item.IsRefFld)
                {
                    if (item.IsSuffixTable)
                    {
                        //引用表要关联到拆分表
                        join = para.UseInneJoin
                            ? item.RefTable.InnerJoinTableAs(item.RefTable.PkFldName, item.SuffixTable.TableNameAs + "." + item.FieldName, para.ReadDirty)
                            : item.RefTable.LeftJoinTableAs(item.RefTable.PkFldName, item.SuffixTable.TableNameAs + "." + item.FieldName, para.ReadDirty);
                        if (item.RefTable.FilterString.IsNullOrEmptyOrWhiteSpace() == false)
                        {
                            join += " And " + item.RefTable.FilterString.Replace(TableNameConst.TempTableNameAs, item.RefTable.TableNameAs);
                        }
                        if (!fromKey.Contains(join))
                        {
                            sbBillCountSqlFrom.AppendLine(join);
                            sbAllCountSqlFrom.AppendLine(join);
                            fromKey.Add(join);
                        }
                    }
                    else
                    {
                        join = para.UseInneJoin
                            ? item.RefTable.InnerJoinTableAs(item.RefTable.PkFldName, item.EntityTable.TableNameAs + "." + item.FieldName, para.ReadDirty)
                            : item.RefTable.LeftJoinTableAs(item.RefTable.PkFldName, item.EntityTable.TableNameAs + "." + item.FieldName, para.ReadDirty);
                        if (item.RefTable.FilterString.IsNullOrEmptyOrWhiteSpace() == false)
                        {
                            join += " And " + item.RefTable.FilterString.Replace(TableNameConst.TempTableNameAs, item.RefTable.TableNameAs);
                        }
                        if (!fromKey.Contains(join))
                        {
                            sbBillCountSqlFrom.AppendLine(join);
                            sbAllCountSqlFrom.AppendLine(join);
                            fromKey.Add(join);
                        }
                    }
                    if (item.RefPropertyTable != null)
                    {
                        join = para.UseInneJoin
                            ? item.RefPropertyTable.InnerJoinTableAs(item.RefPropertyTable.PkFldName, item.RefTable.TableNameAs + "." + item.RefFieldName, para.ReadDirty)
                            : item.RefPropertyTable.LeftJoinTableAs(item.RefPropertyTable.PkFldName, item.RefTable.TableNameAs + "." + item.RefFieldName, para.ReadDirty);
                        if (!fromKey.Contains(join))
                        {
                            sbBillCountSqlFrom.AppendLine(join);
                            sbAllCountSqlFrom.AppendLine(join);
                            fromKey.Add(join);
                        }
                    }
                }
            }
            queryObj.AllCountSqlFrom = sbAllCountSqlFrom.ToString();
            queryObj.BillCountSqlFrom = sbBillCountSqlFrom.ToString();
        }

        private static string GetWithSql(SqlBuilderParameter para)
        {
            var key = new List<string>();
            if(para.ReadDirty)
            {
                key.Add(" nolock ");
            }

            if(!para.WithIndex.IsNullOrEmptyOrWhiteSpace())
            {
                key.Add(" index = {0} ".Fmt(para.WithIndex));
            }

            if(!key.Any ())
            {
                return "";
            }

            return "with({0})".Fmt(key.JoinEx (" ",false));
             
        }

        /// <summary>
        /// 构建 select 语句
        /// </summary>
        private static void BuildSelectSql(SqlBuilderParameter para, QueryObject queryObj)
        {
            var queryInfo = GetHtmlFormQueryMetaInfo(para.Ctx, para.HtmlForm);
            queryObj.SqlSelect = "Select ";
            if (para.IsDistinct)
            {
                queryObj.SqlSelect += " distinct ";
            }
            if (para.TopCount > 0)
            {
                queryObj.SqlSelect += $" top {para.TopCount} ";
            }
            List<string> sql = new List<string>();
            List<string> sqlNoAlias = new List<string>();
            foreach (var item in queryObj.QueryFlds)
            {
                var selFldString = item.GetSelectString(queryInfo).Trim();
                if (selFldString.IsNullOrEmptyOrWhiteSpace()) continue;

                var selFldNoAlias = item.GetSelectString(queryInfo, true).Trim();

                if (sql.Contains(Environment.NewLine + selFldString, StringComparer.OrdinalIgnoreCase)
                        || sql.Contains(selFldString, StringComparer.OrdinalIgnoreCase)) continue;

                if (sql.Count > 0 && sql.Count % 5 == 0)
                {
                    sql.Add(Environment.NewLine + selFldString);
                }
                else
                {
                    sql.Add(selFldString);
                }
                sqlNoAlias.Add(selFldNoAlias);
            }
            queryObj.SqlField = string.Join(",", sqlNoAlias);
            queryObj.SqlSelect += string.Join(",", sql);
        }


        /// <summary>
        /// 处理前端传递的查询字段
        /// </summary>
        private static void AnalysisSelectFld(SqlBuilderParameter para, QueryObject queryObj)
        {
            var queryInfo = GetHtmlFormQueryMetaInfo(para.Ctx, para.HtmlForm);
            if (para.SelectedFieldKeys.Count == 0)
            {
                queryInfo.AllQueryFields.ToList().ForEach(f => para.SelectedFieldKeys.Add(f.IsRefFld ? f.RefFldFullKey : f.Id));
            }

            if (!string.IsNullOrWhiteSpace(para.SumExprString))
            {
                para.SelectedFieldKeys.Add(para.SumExprString);
            }
              
            var logMsg= DealSelectedFldKey(para,queryObj);

            List<SelectField> selFlds = new List<SelectField>();

            queryObj.QueryFlds = QueryField.CreateQueryItems(para.Ctx, para.SelectedFieldKeys.Distinct().ToArray());
            foreach (var queryItem in queryObj.QueryFlds)
            {
                var linkSelFlds = queryItem.GetSelectField(queryInfo);
                if (linkSelFlds != null)
                {
                    selFlds.AddRange(linkSelFlds);
                }
            }

            var mustQueryFldKeys = new List<string>
            {
            };

            //是否只查询用户指定列？
            if (!para.QueryUserFieldOnly)
            {
                #region 针对实体分析需要强制添加到查询对象中的字段列表

                SetHeadPKFlds(para, queryInfo, queryObj, selFlds, ref mustQueryFldKeys);

                SetEntryPKFlds(para, queryInfo, queryObj, selFlds, ref mustQueryFldKeys);

                #endregion
            }

            //强制增加颜色的区分字段（列表的着色用）
            if (!para.NoColorSetting)
            {
                SetColorFlds(para, queryInfo, selFlds, queryObj, ref mustQueryFldKeys);
            }

            var mustQueryFlds = QueryField.CreateQueryItems(para.Ctx, mustQueryFldKeys.ToArray());
            foreach (var mustQueryFld in mustQueryFlds)
            {
                var existFld = queryObj.QueryFlds
                    .FirstOrDefault(o => o.GetSelectAliasName(queryInfo).EqualsIgnoreCase(mustQueryFld.GetSelectAliasName(queryInfo)));
                if (existFld == null)
                {
                    queryObj.QueryFlds.Add(mustQueryFld);
                }
            }

            //查询字段的位置要在一条线上不能跨多个表体或多个子表体：字段要么都在表头，要么都在表体，要么都在子表体，要么表头字段+同一个表体字段，
            //要么表头字段+同一个表体字段+子表体字段（该子表体属于前面的表体） 
            var allEns = selFlds.Where(f => !f.EntityKey.EqualsIgnoreCase("fbillhead")).Select(f => f.EntityKey).Distinct().ToList();
            if (allEns.Count > 1)
            {
                var entitys = para.HtmlForm.EntryList.Where(f => allEns.Contains(f.Id, StringComparer.OrdinalIgnoreCase)).ToList();
                var grpPaths = entitys.GroupBy(f => f.QueryPathId).ToList();//子表体对应的表体字段的查询路径，跟子表体一致                 
                if (grpPaths.Count > 1)
                {
                    //跨了多个表体或各个子表体的，记录日志以便排查
                    logMsg += Environment.NewLine + "          前面自动去掉部分字段后，还有查询结果为笛卡尔集的情况！".Fmt(para.HtmlForm.Caption, para.HtmlForm.Id, para.Ctx.UserName,
                                para.Ctx.Companys.FirstOrDefault(f => f.CompanyId == para.Ctx.Company)?.CompanyName);
                    
                } 
            }

            if (!logMsg.IsNullOrEmptyOrWhiteSpace())
            {
                logMsg.WriteLogToDebugFile("列表查询字段跨多个表体");
            }

            queryObj.SelectFlds = selFlds.OrderBy(f => f.EntityKey).ToList();
        }

        /// <summary>
        /// 处理选中的字段：跨了多个表体或各个子表体的,只取其中的一个表体，其他平行的表体去掉，避免查询结果为笛卡尔乘积
        /// </summary>
        /// <param name="para"></param>
        private static string  DealSelectedFldKey(SqlBuilderParameter para, QueryObject queryObj)
        { 
            var allFlds = new Dictionary<string, HtmlField>();
            foreach (var item in para.SelectedFieldKeys)
            {
                var arr = item.Split('.');
                var fld = para.HtmlForm.GetField(arr[0]);
                if (fld != null)
                {
                    allFlds[item] = fld;
                }
            }

            queryObj.IsIncludeEntity = allFlds.Values.Any(f => f.Entity is HtmlEntryEntity || f.Entity is HtmlSubEntryEntity);

            var selEns = new List<string>() { "fbillhead" };
            var allEns = new List<string>();
            allEns.AddRange(allFlds.Select(f => f.Value.EntityKey).Distinct().ToList());
            allEns = allEns.Where(f => f.IsNullOrEmptyOrWhiteSpace() == false && !f.EqualsIgnoreCase("fbillhead")).Distinct().ToList();
            if (allEns.Count > 1)
            {
                var entitys = para.HtmlForm.EntryList.Where(f => allEns.Contains(f.Id, StringComparer.OrdinalIgnoreCase)).ToList();
                var grpPaths = entitys.GroupBy(f => f.QueryPathId.ToLowerInvariant ()).ToList();//子表体对应的表体字段的查询路径，跟子表体一致                
                //跨了多个表体或各个子表体的,只取其中的一个表体，其他平行的表体去掉，避免查询结果为笛卡尔乘积
                var first = grpPaths.FirstOrDefault();
                if (grpPaths.Count > 1)
                {
                    //优先取id为 fentry 的
                    first = grpPaths.FirstOrDefault(f => f.Key.EqualsIgnoreCase("fentry") || f.ToList().Any(x => x.Id.EqualsIgnoreCase("fentry")));
                    if (first == null)
                    {
                        first = grpPaths.FirstOrDefault();
                    }
                }

                selEns.AddRange(first.ToList().Select(f => f.Id));
            }
            else
            {
                selEns.AddRange(allEns);
            }

            var beRemoveKeys = new List<string>();
            foreach (var item in allFlds)
            {
                if (!selEns.Contains(item.Value.EntityKey,StringComparer.OrdinalIgnoreCase))
                {
                    beRemoveKeys.Add(item.Key);
                }
            }

            var msg = "";
            if (beRemoveKeys.Count > 0)
            {
                var ret = para.SelectedFieldKeys.Except(beRemoveKeys).ToList();
                para.SelectedFieldKeys.Clear();
                para.SelectedFieldKeys.AddRange(ret);

                msg = "对应表单：{0}-{1} ，操作人：{2} {3}，操作组织：{4} ，为避免出现笛卡尔集数据，自动去掉字段： {5}".Fmt(para.HtmlForm.Caption, para.HtmlForm.Id, para.Ctx.UserName, para.Ctx.DisplayName,
                                  para.Ctx.Companys.FirstOrDefault(f => f.CompanyId == para.Ctx.Company)?.CompanyName,
                                  beRemoveKeys.JoinEx (" , ",false)); 
            }

            return msg;
        }

        /// <summary>
        /// 强制增加颜色的区分字段（列表的着色用）
        /// </summary>
        /// <param name="para"></param>
        /// <param name="queryInfo">模型里面包含的字段列表</param>
        /// <param name="selFlds">选中的字段列表</param>
        /// <param name="queryObj"></param>
        /// <param name="mustQueryFldKeys"></param>
        private static void SetColorFlds(SqlBuilderParameter para, QueryMetaInfo queryInfo,
                                        List<SelectField> selFlds, QueryObject queryObj,
                                        ref List<string> mustQueryFldKeys)
        {
            //列表颜色设置里面涉及的状态字段
            var enKeys = (from en in selFlds
                          select en.EntityKey).Distinct().ToList();
            var svc = para.Ctx.Container.GetService<IListQuryProfile>();
            var colorSetting = svc.GetListCellColor(para.Ctx, para.FormId);
            var valColorSettiong = new List<ListColorSetting>();
            foreach (var item in colorSetting)
            {
                var fld = queryInfo.GetSelectField(item.FldKey);
                if (fld == null)
                {
                    continue;
                }

                //颜色设置涉及的字段所在的位置，如果都不在所选字段涉及的表头/表体，则忽略该字段
                if (!enKeys.Any(f => f.EqualsIgnoreCase(fld.EntityKey)))
                {
                    continue;
                }
                valColorSettiong.Add(item);
                queryObj.ColorFlds.Add(fld);

                if (!selFlds.Any(f => f.FullId.EqualsIgnoreCase(fld.FullId)))
                {
                    selFlds.Add(fld);
                    mustQueryFldKeys.Add(fld.Id);
                }
            }
            queryObj.ColoSettings = valColorSettiong.OrderBy(f => f.Priority).ToList();

            if (queryObj.RowBGColorFlds == null)
            {
                queryObj.RowBGColorFlds = new Dictionary<string, List<SelectField>>();
            }
            var rowColorSetting = queryInfo.HtmlForm.RowBGColorSetting;
            if (rowColorSetting != null)
            {
                var bizExpr = para.Ctx.Container.GetService<IBizExpression>();
                var evaluator = para.Ctx.Container.GetService<IBizExpressionEvaluator>();
                foreach (var item in rowColorSetting)
                {
                    if (item.ExprKeys == null || item.ExprKeys.Length == 0)
                    {
                        bizExpr.ExpressionText = item.ConditionExp;
                        item.ExprKeys = evaluator.GetNameExpression(bizExpr);
                    }

                    var rowBGFlds = new List<SelectField>();
                    foreach (var fldKey in item.ExprKeys)
                    {
                        var fx = queryInfo.GetSelectField(fldKey);
                        if (fx == null)
                        {
                            continue;
                        }

                        rowBGFlds.Add(fx);

                        //行颜色设置涉及的字段所在的位置，如果都不在所选字段涉及的表头/表体，则忽略该字段
                        if (!enKeys.Any(f => f.EqualsIgnoreCase(fx.EntityKey)))
                        {
                            continue;
                        }

                        if (!selFlds.Any(f => f.FullId.EqualsIgnoreCase(fx.FullId)))
                        {
                            selFlds.Add(fx);
                            mustQueryFldKeys.Add(fx.Id);
                        }
                    }
                    queryObj.RowBGColorFlds[item.Id] = rowBGFlds;
                }
            }

        }

        /// <summary>
        /// 强制查询出表头的几个字段：主表的主键Id，FormId，单据编码，基础资料名称字段
        /// </summary>
        /// <param name="para"></param>
        /// <param name="queryInfo"></param>
        /// <param name="queryObj"></param>
        /// <param name="selFlds">选中的字段列表</param>
        /// <param name="mustQueryFldKeys">模型里面包含的字段列表</param>
        /// <returns></returns>
        private static void SetHeadPKFlds(SqlBuilderParameter para,
            QueryMetaInfo queryInfo,
            QueryObject queryObj,
            List<SelectField> selFlds,
            ref List<string> mustQueryFldKeys)
        {
            //强制传递表头的几个字段：主表的主键Id，FormId，单据编码，基础资料名称字段
            var fld = queryInfo.GetSelectField($"{para.HtmlForm.HeadEntity.Id}.{para.HtmlForm.BillFormIdFldKey}");
            if (fld != null)
            {
                selFlds.Add(fld);
                mustQueryFldKeys.Add($"{ para.HtmlForm.HeadEntity.Id}.{ para.HtmlForm.BillFormIdFldKey}");
            }
            fld = queryInfo.GetSelectField($"{para.HtmlForm.HeadEntity.Id}.{para.HtmlForm.BillPKFldName}");
            if (fld != null)
            {
                selFlds.Add(fld);
                queryObj.SelectPkField = fld;
                mustQueryFldKeys.Add($"{para.HtmlForm.HeadEntity.Id}.{para.HtmlForm.BillPKFldName}");
            }
            fld = queryInfo.GetSelectField($"{para.HtmlForm.HeadEntity.Id}.{para.HtmlForm.NumberFldKey}");
            if (fld != null)
            {
                selFlds.Add(fld);
                mustQueryFldKeys.Add($"{para.HtmlForm.HeadEntity.Id}.{para.HtmlForm.NumberFldKey}");
            }
            fld = queryInfo.GetSelectField($"{para.HtmlForm.HeadEntity.Id}.{para.HtmlForm.NameFldKey}");
            if (fld != null)
            {
                selFlds.Add(fld);
                mustQueryFldKeys.Add($"{para.HtmlForm.HeadEntity.Id}.{para.HtmlForm.NameFldKey}");
            }
        }


        /// <summary>
        /// 如果选择的字段在表体，则强制传递表体的主键及序号字段
        /// </summary>
        /// <param name="para"></param>
        /// <param name="queryInfo"></param>
        /// <param name="queryObj"></param>
        /// <param name="selFlds">选中的字段列表</param>
        /// <param name="mustQueryFldKeys"></param>
        /// <returns></returns>
        private static void SetEntryPKFlds(SqlBuilderParameter para,
            QueryMetaInfo queryInfo,
            QueryObject queryObj,
            List<SelectField> selFlds,
            ref List<string> mustQueryFldKeys)
        {
            var lstSelEntityKeys = (from en in selFlds
                                    select en.EntityKey).Distinct().ToList();
            //如果选择的字段在表体，则强制传递表体的主键及序号字段
            foreach (var item in para.HtmlForm.EntryList)
            {
                if (!lstSelEntityKeys.Contains(item.Id, StringComparer.OrdinalIgnoreCase)) continue;
                if (selFlds.Any(f => f.EntityKey.EqualsIgnoreCase(item.Id)))
                {
                    var fld = queryInfo.GetSelectField($"{item.Id}.{item.PkFieldName}");
                    if (fld != null)
                    {
                        selFlds.Add(fld);
                        mustQueryFldKeys.Add($"{item.Id}.{item.PkFieldName}");
                        // continue;
                    }

                    if (item.SeqDynamicProperty != null)
                    {
                        fld = queryInfo.GetSelectField($"{item.Id}.{item.SeqDynamicProperty.Name}");
                        if (fld != null)
                        {
                            selFlds.Add(fld);
                            mustQueryFldKeys.Add($"{item.Id}.{item.SeqDynamicProperty.Name}");
                            continue;
                        }
                    }

                    //如果是树形单据体强制添加内置字段查询对象
                    if (item is HtmlTreeEntryEntity)
                    {
                        var treeEntity = item as HtmlTreeEntryEntity;
                        //父行内码字段
                        if (treeEntity.ParentIdDynamicProperty != null)
                        {
                            fld = queryInfo.GetSelectField($"{item.Id}.{treeEntity.ParentIdDynamicProperty.Name}");
                            if (fld != null)
                            {
                                selFlds.Add(fld);
                                mustQueryFldKeys.Add($"{item.Id}.{treeEntity.ParentIdDynamicProperty.Name}");
                                continue;
                            }
                        }
                        //是否叶子字段
                        if (treeEntity.IsLeafDynamicProperty != null)
                        {
                            fld = queryInfo.GetSelectField($"{item.Id}.{treeEntity.IsLeafDynamicProperty.Name}");
                            if (fld != null)
                            {
                                selFlds.Add(fld);
                                mustQueryFldKeys.Add($"{item.Id}.{treeEntity.IsLeafDynamicProperty.Name}");
                                continue;
                            }
                        }
                        //层级字段
                        if (treeEntity.LevelDynamicProperty != null)
                        {
                            fld = queryInfo.GetSelectField($"{item.Id}.{treeEntity.LevelDynamicProperty.Name}");
                            if (fld != null)
                            {
                                selFlds.Add(fld);
                                mustQueryFldKeys.Add($"{item.Id}.{treeEntity.LevelDynamicProperty.Name}");
                                continue;
                            }
                        }
                        //展开状态字段
                        if (treeEntity.IsExpandedDynamicProperty != null)
                        {
                            fld = queryInfo.GetSelectField($"{item.Id}.{treeEntity.IsExpandedDynamicProperty.Name}");
                            if (fld != null)
                            {
                                selFlds.Add(fld);
                                mustQueryFldKeys.Add($"{item.Id}.{treeEntity.IsExpandedDynamicProperty.Name}");
                                continue;
                            }
                        }
                    }
                }

            }
        }

        /// <summary>
        /// 分析并缓存模型信息
        /// </summary>
        private static void AnalysisHtmlFormFld(UserContext ctx, HtmlForm hForm)
        {
            var dt = hForm.GetDynamicObjectType(ctx);
            if (htmlFormQueryInfos.ContainsKey(hForm.FormCacheId) && dt != null)
            {
                var queryInfo = htmlFormQueryInfos[hForm.FormCacheId];
                if (!htmlFormQueryInfos[hForm.FormCacheId].IsNullOrEmpty() && !hForm.BillHeadTableName.IsNullOrEmpty() && htmlFormQueryInfos[hForm.FormCacheId].HtmlForm.BillHeadTableName != hForm.BillHeadTableName)
                {
                    //避免报表ListReportFormController用户会话过期后，直接请求后临时表对应不上的问题
                    List<SelectField> newflds = hForm.ToListSelectField(ctx).OrderBy(f => f.ListTabIndex).ToList();
                    queryInfo = new QueryMetaInfo(hForm, newflds);
                    htmlFormQueryInfos[hForm.FormCacheId] = queryInfo;
                }
                var hasValidCols = queryInfo.AllQueryFields.FirstOrDefault(o => o.RefTable != null && o.RefTable.TableName.IsNullOrEmptyOrWhiteSpace() == true) != null;
                if (hasValidCols == false)
                    return;
            }
            //构建可选择的字段信息,构建实体表别名 
            List<SelectField> flds = hForm.ToListSelectField(ctx).OrderBy(f => f.ListTabIndex).ToList();
            htmlFormQueryInfos[hForm.FormCacheId] = new QueryMetaInfo(hForm, flds);
            //htmlFormFields.TryAdd(formId, flds);
        }

        private static void BuildSumAmountSql(SqlBuilderParameter para, QueryObject queryObj)
        {
            if (!string.IsNullOrWhiteSpace(para.SumExprString))
            {
                queryObj.SqlSumExpr = string.Format("select sum({0}) from ({1} {2} {3}) sumx",
                    para.SumExprString,
                    queryObj.SqlSelect,
                    queryObj.SqlFrom,
                    queryObj.SqlWhere);
            }
        }




        #region 数据授权



        /// <summary>
        /// 数据范围的过滤（数据权限）
        /// </summary>
        /// <param name="para"></param>
        private static void AppendDataRowAclFilter(SqlBuilderParameter para)
        {
            if ((para.SrcPara != null && para.SrcPara.ContainsKey("ByUniqueValidation")) || !para.EnableDataRowACL)
            {
                //唯一性校验的情况（比如单据编码唯一性校验），不加这个数据范围过滤
                return;
            }
            var svc = para.Ctx.Container.GetService<IPermissionService>();
            var rulePara = new DataQueryRuleParaInfo()
            {
                FormId = para.FormId,
                SrcFormId = para.SrcFormId,
                SrcPara = para.SrcPara,
                SrcFldId = para.SrcFldId,
            };
            var filterRowObjs = svc.GetDataRowACLFilter(para.Ctx, rulePara);

            if (filterRowObjs.IsNullOrEmpty())
            {
                return;
            }
            filterRowObjs.First().Memo = "/*角色授权中定义的数据范围*/";
            para.SetFilter(filterRowObjs);
            //para.AppendFilterString(filter);
        }


        /// <summary>
        /// 数据查询规则：主要应用在各业务系统自定义的数据隔离（数据授权）功能
        /// </summary>
        /// <param name="para"></param>
        private static void AppendDataQueryRuleFilter(SqlBuilderParameter para)
        {
            if ((para.SrcPara != null && para.SrcPara.ContainsKey("ByUniqueValidation")) || !para.EnableDataQueryRule)
            {
                //唯一性校验的情况（比如单据编码唯一性校验），不加这个数据范围过滤
                return;
            }

            var svc = para.Ctx.Container.GetService<IPermissionService>();
            var rulePara = new DataQueryRuleParaInfo()
            {
                FormId = para.FormId,
                SrcFormId = para.SrcFormId,
                SrcPara = para.SrcPara,
                SrcFldId = para.SrcFldId,
            };
            var filterRowObjs = svc.GetDataQueryRuleFilter(para.Ctx, rulePara);

            if (filterRowObjs.IsNullOrEmpty())
            {
                return;
            }
            var memo = filterRowObjs.First().Memo;
            if (memo.IsNullOrEmptyOrWhiteSpace())
            {
                memo = "/*业务接口IDataQueryRule中定义的数据范围*/";
            }
            filterRowObjs.First().Memo = memo;
            para.SetFilter(filterRowObjs);
        }



        #endregion 数据授权




    }


}
