using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.Framework.Interface.SystemIntegration
{
    /// <summary>
    /// 系统集成服务接口定义
    /// </summary>
    public interface ISystemIntegrationService
    {
        /// <summary>
        /// 创建数据同步结果实体
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="dataSyncResultHtmlForm">数据同步结果表单</param>
        /// <param name="syncHtmlForm">同步业务对象表单</param>
        /// <param name="syncDataEntity">同步业务对象实体</param>
        /// <param name="extAppId">外部应用id</param>
        /// <param name="option">操作额外选项</param>
        /// <returns></returns>
        DynamicObject CreateDataSyncResult(UserContext userCtx, HtmlForm dataSyncResultHtmlForm, HtmlForm syncHtmlForm, DynamicObject syncDataEntity,
            string extAppId, OperateOption option = null);

        /// <summary>
        /// 保存数据同步结果到数据库
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="dataSyncResultHtmlForm">数据同步结果表单</param>
        /// <param name="dataEntities">数据同步结果实体列表</param>
        /// <param name="option">操作额外选项</param>
        void SaveDataSyncResult(UserContext userCtx, HtmlForm dataSyncResultHtmlForm, DynamicObject[] dataEntities, OperateOption option = null);

        /// <summary>
        /// 保存数据同步结果到数据库
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="dataSyncResultHtmlForm">数据同步结果表单</param>
        /// <param name="dataEntities">数据同步结果实体列表</param>
        /// <param name="option">操作额外选项</param>
        Task SaveDataSyncResultAsync(UserContext userCtx, HtmlForm dataSyncResultHtmlForm, DynamicObject[] dataEntities, OperateOption option = null);

        /// <summary>
        /// 写数据同步结果日志明细
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="dataSyncResult">数据同步结果实体</param>
        /// <param name="logContent">日志</param>
        /// <param name="isError">是否出错</param>
        void WriteDataSyncResult(UserContext userCtx, DynamicObject dataSyncResult, string logContent, bool isError = false);

        /// <summary>
        /// 获取外部应用实体
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="thirdSysId"></param>
        /// <param name="option">操作额外选项</param>
        /// <returns></returns>
        DynamicObject GetExternalAppObject(UserContext userCtx, string thirdSysId, OperateOption option = null);

        /// <summary>
        /// 创建操作日志实体
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">操作日志表单</param>
        /// <param name="opApi">操作接口</param>
        /// <param name="opName">操作名称</param>
        /// <param name="opFormId">操作业务对象</param>
        /// <param name="opLogSource">操作日志来源，1：我方；2：对方</param>
        /// <param name="description">描述</param>
        /// <returns></returns>
        DynamicObject CreateOperationLog(UserContext userCtx, HtmlForm htmlForm, string opApi, string opName, string opFormId, string opLogSource = "1", string description = null);

        /// <summary>
        /// 写操作日志明细
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="opLog">操作日志实体</param>
        /// <param name="logContent">日志</param>
        /// <param name="isError">是否出错</param>
        void WriteOperationLog(UserContext userCtx, DynamicObject opLog, string logContent, bool isError = false);

        /// <summary>
        /// 保存操作日志到数据库
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">操作日志表单</param>
        /// <param name="dataEntities">操作日志实体列表</param>
        /// <param name="option">操作额外选项</param>
        void SaveOperationLog(UserContext userCtx, HtmlForm htmlForm, DynamicObject[] dataEntities, OperateOption option = null);

        /// <summary>
        /// 保存操作日志到数据库
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">操作日志表单</param>
        /// <param name="dataEntities">操作日志实体列表</param>
        /// <param name="option">操作额外选项</param>
        Task SaveOperationLogAsync(UserContext userCtx, HtmlForm htmlForm, DynamicObject[] dataEntities, OperateOption option = null);
    }
}
