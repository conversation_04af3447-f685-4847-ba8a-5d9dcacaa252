using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn.SystemIntegration;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.Framework.Interface.SystemIntegration
{
    /// <summary>
    /// 同步数据至第三方系统的基类插件
    /// </summary>
    public abstract class AbstractSyncDataTo3SysPlugIn : ISyncDataTo3SysPlugIn
    {
        /// <summary>
        /// 上下文
        /// </summary>
        protected UserContext UserContext { get; set; }

        /// <summary>
        /// 表单对象
        /// </summary>
        protected HtmlForm HtmlForm { get; set; }

        /// <summary>
        /// 当前同步对象映射配置
        /// </summary>
        protected DynamicObject BillMappingObject { get; set; }

        /// <summary>
        /// 外部应用产品信息
        /// </summary>
        protected TargetServer TargetServer { get; set; }

        /// <summary>
        /// 网关
        /// </summary>
        [InjectProperty]
        protected IHttpServiceInvoker Gateway { get; set; }

        /// <summary>
        /// 初始化插件上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="hForm"></param>
        /// <param name="billMapObject"></param>
        /// <param name="targetServer"></param>
        /// <param name="objInst"></param>
        public void InitializePlugIn(UserContext userCtx, HtmlForm hForm, DynamicObject billMapObject, TargetServer targetServer, params object[] objInst)
        {
            this.UserContext = userCtx;
            this.HtmlForm = hForm;
            this.BillMappingObject = billMapObject;
            this.TargetServer = targetServer;
        }

        /// <summary>
        /// 打包目标单据后事件
        /// </summary>
        /// <param name="e"></param>
        public virtual void AfterPackTargetBill(AfterPackTargetBillEventArgs e)
        {

        }

        /// <summary>
        /// 同步操作后事件
        /// </summary>
        /// <param name="e"></param>
        public virtual void AfterSendDataToTarget(AfterSendDataToTargetEventArgs e)
        {

        }

        /// <summary>
        /// 打包目标单据前事件
        /// </summary>
        /// <param name="e"></param>
        public virtual void BeforePackTargetBill(BeforePackTargetBillEventArgs e)
        {

        }

        /// <summary>
        /// 同步操作前事件
        /// </summary>
        /// <param name="e"></param>
        public virtual void BeforeSendDataToTarget(BeforeSendDataToTargetEventArgs e)
        {

        }

        /// <summary>
        /// 构建映射信息后事件
        /// </summary>
        /// <param name="e"></param>
        public virtual void AfterBuildBillMapping(AfterBuildBillMappingEventArgs e)
        {

        }
    }
}
