
SET BUILD=Debug
REM SET BUILD=Release
REM COPY ..\lib\%BUILD%\JieNor.Framework.dll ..\src\JieNor.AMS.YDJ.Store.Web\bin\
REM TortoiseProc.exe /command:update /path:"..\..\..\..\..\..\code\work\fw\lib\%BUILD%" /closeonend:1
echo "����ɾ����ǰlib\debug�Լ�website\binĿ¼�����"
rd ..\lib\%BUILD% /s /q;
rd ..\src\JieNor.AMS.YDJ.Store.Web\bin /s /q;

REM TortoiseProc.exe /command:update /path:"..\..\..\..\..\..\code\work\fw\lib\dist" /closeonend:1
XCOPY ..\..\..\..\..\..\code\work\fw\lib\%BUILD% ..\lib\%BUILD%\ /y /s /e

msbuild ..\src\JieNor.AMS.YDJ.Store.Build.sln

XCOPY ..\lib\%BUILD% ..\src\JieNor.AMS.YDJ.Store.Web\bin\ /y /s /e