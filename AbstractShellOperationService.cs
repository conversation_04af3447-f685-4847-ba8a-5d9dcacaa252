using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 抽象外壳服务基类
    /// </summary>
    public abstract class AbstractShellOperationService : IShellOperationService
    {
        /// <summary>
        /// 连接上下文
        /// </summary>
        protected UserContext Context { get; set; }

        /// <summary>
        /// 外壳服务
        /// </summary>
        protected IShellService ShellService { get; set; }

        /// <summary>
        /// 元数据模型服务
        /// </summary>
        protected IMetaModelService MetaModelService { get; set; }

        /// <summary>
        /// 操作结果
        /// </summary>
        protected IOperationResult Result { get; set; }

        /// <summary>
        /// 执行外壳服务逻辑
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="actionPara"></param>
        public void Execute(UserContext userCtx, string actionPara)
        {
            this.Context = userCtx;

            this.ShellService = userCtx.Container.GetService<IShellService>();
            this.Result = userCtx.Container.GetService<DynamicDTOResponse>()?.OperationResult;
            this.MetaModelService = userCtx.Container.GetService<IMetaModelService>();

            this.InitializeService(actionPara);
            this.ExecuteService();
        }

        /// <summary>
        /// 执行服务接口
        /// </summary>
        protected abstract void ExecuteService();

        /// <summary>
        /// 初始化服务参数接口
        /// </summary>
        /// <param name="actionPara"></param>
        protected virtual void InitializeService(string actionPara)
        {

        }
    }
}
