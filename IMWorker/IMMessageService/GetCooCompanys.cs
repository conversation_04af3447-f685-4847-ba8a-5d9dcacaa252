using JieNor.Framework.Interface;
using JieNor.Framework.Interface.IM;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.IMWorker.IMMessageService
{
    /// <summary>
    /// 协同企业服务
    /// </summary>
    [InjectService]
    public class GetCooCompanys : ICooCompanyService
    {
        /// <summary>
        /// 获取所有协同企业
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="objCompId"></param>
        /// <returns></returns>
        public List<Dictionary<string, object>> GetAllCooCompanys(UserContext userCtx, string objCompId = null)
        {
            if (userCtx == null || userCtx.Company.IsNullOrEmptyOrWhiteSpace()) return null;

            var meta = userCtx.Container.GetService<MetaModelService>()?.LoadFormModel(userCtx, "coo_company");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, meta.GetDynamicObjectType(userCtx));

            var allSql = @"select fcompanyId,fname from T_COO_COMPANY where fmycompanyId='{0}' and fmainorgid='{0}' and fcoostatus=N'已协同'".Fmt(objCompId ?? userCtx.Company);
            List<Dictionary<string, object>> cooCompanys = new List<Dictionary<string, object>>();
            System.Collections.ArrayList arrComp = new System.Collections.ArrayList();
            var svc = userCtx.Container.GetService<IDBService>();
            using (var reader = svc.ExecuteReader(userCtx, allSql))
            {
                while (reader.Read())
                {
                    var companyId = reader.GetValue<string>("fcompanyId");
                    if (!arrComp.Contains(companyId))
                    {
                        cooCompanys.Add(new Dictionary<string, object>() { { "id", companyId }, { "name", reader.GetValue<string>("fname") } });
                        arrComp.Add(companyId);
                    }
                }
            }
            return cooCompanys;
        }
        
    }
}
