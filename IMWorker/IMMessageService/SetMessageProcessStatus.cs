using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.IM;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;


namespace JieNor.Framework.AppService.IMWorker.IMMessageService
{

    /// <summary>
    /// 设置消息的处理状态
    /// </summary> 
    [InjectService]
    [FormId(HtmlFormIdConst.Html_IM_IMUser)]
    [OperationNo("SetMessageProcessStatus")]
    public class SetMessageProcessStatus : AbstractOperationServicePlugIn
    {

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var msgId = this.GetQueryOrSimpleParam<string>("msgId");
            var st = this.GetQueryOrSimpleParam<string>("status");
            IMMsgHandleStatus status =  IMMsgHandleStatus.None ;
            Enum.TryParse<IMMsgHandleStatus>(st, out status);
            var msgPiple = this.Container.GetService<IMessageServiceProxy>();
            msgPiple.SetMessageProcessStatus(this.Context, msgId, status);
            this.Result.IsSuccess = true;
        }


    }


}
