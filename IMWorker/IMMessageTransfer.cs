using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.Interface.IM;
using JieNor.Framework.IoC;
using System;
using System.Collections.Concurrent;
using System.Threading;

namespace JieNor.Framework.AppService.IMWorker
{
    /// <summary>
    /// 消息处理器：只处理需要转发的消息
    /// </summary>
    [InjectService]
    [MessageChannel("fw.redirect")]
    public class IMMessageTransfer : IIMMessageHandler
    {




        /// <summary>
        /// 待处理的消息队列
        /// </summary>
        static ConcurrentQueue<RedirectorIMMessage> WaitFor = new ConcurrentQueue<RedirectorIMMessage>();

        /// <summary>
        /// 正在处理的消息队列
        /// </summary>
        static ConcurrentStack<RedirectorIMMessage> Processing = new ConcurrentStack<RedirectorIMMessage>();


        /// <summary>
        /// 转发出错,需要重发的消息队列
        /// </summary>
        static ConcurrentQueue<RedirectorIMMessage> ReapetSend = new ConcurrentQueue<RedirectorIMMessage>();


        static IMMessageTransfer()
        {
            //Task task = new Task(() =>
            //{
            //    Execute();
            //}
            //    );

            //task.Start();
        }


        /// <summary>
        /// 消息处理器优先级
        /// </summary>
        public int Priority
        {
            get
            {
                return 1;
            }
        }

        /// <summary>
        /// 处理消息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="message"></param>
        /// <param name="result"></param>
        public void ProcessMessage(UserContext userCtx, IMMessage message, MessageHandlerResult result)
        {
            //TODO 判断消息目的地与WebBootstrapper.CurrAppServer是否一致，不一致则调用SignalR客户端代理，进行转发消息。
            if (true)
            {
                RedirectorIMMessage msg = new RedirectorIMMessage(message);
                WaitFor.Enqueue(msg);
            }
        }




        /// <summary>
        /// 消息转发
        /// </summary>
        //[PerfMonitor]
        private static void Execute()
        {
            while (true)
            {
                if (WaitFor.Count > 10000)
                {
                    // TODO 如果正在处理的消息超过1万条：租塞严重，发邮件给管理员？？？？
                    ;
                }

                if (Processing.Count > 10000)
                {
                    // TODO 如果正在处理的消息超过1万条：租塞严重，发邮件给管理员？？？？
                    ;
                }

                if (ReapetSend.Count > 10000)
                {
                    // TODO 如果等待重发的消息超过1万条：租塞严重，发邮件给管理员？？？？
                    ;
                }

                RedirectorIMMessage msg;
                if (WaitFor.TryDequeue(out msg))
                {
                    RedirectorMessage(msg);
                }

                while (ReapetSend.TryDequeue(out msg))
                {
                    if (msg.IsTime)
                    {
                        RedirectorMessage(msg);
                        Thread.Sleep(50);
                    }
                }

                Thread.Sleep(500);
            }
        }


        /// <summary>
        /// 转发消息
        /// </summary>
        /// <param name="msg"></param>
        private static void RedirectorMessage(RedirectorIMMessage msg)
        {
            try
            {
                Processing.Push(msg);

                // TODO 加上目标url
                //IIMServiceProxy svc = ServiceStackHost.Instance.Resolve<IIMServiceProxy>();
                //svc.SendMessage(msg.Message.ToJson());
            }
            catch (Exception ex)
            {
                // TODO 记录日志

                msg.PreProcTime = DateTime.Now;
                msg.AddWaitTime();
                ReapetSend.Enqueue(msg);
            }
            finally
            {
                Processing.TryPop(out msg);
            }
        }

        public void InitializeOption(MessageHandlerOption option)
        {
        }
    }
}











internal class RedirectorIMMessage
{

    /// <summary>
    /// 最长等待时间：2天
    /// </summary>
    static int maxWaitTime = 60 * 48;

    /// <summary>
    /// 等待时长（分钟）
    /// </summary>
    int waitTime = 0;


    public IMMessage Message
    {
        get;
        set;
    }

    int addStep = 1;

    /// <summary>
    /// 增加重发的等待时间
    /// </summary> 
    public void AddWaitTime()
    {
        if (waitTime >= maxWaitTime)
        {
            return;
        }

        waitTime += addStep;

        addStep *= 2;
    }



    /// <summary>
    /// 最近一次处理时间
    /// </summary>
    public DateTime PreProcTime
    {
        get;
        set;
    }



    public RedirectorIMMessage(IMMessage msg)
    {
        Message = msg;
    }


    /// <summary>
    /// 是否到重发的时间了
    /// </summary>
    public bool IsTime
    {
        get
        {
            return (DateTime.Now - PreProcTime).TotalMinutes >= waitTime;
        }
    }

}

