using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.IM;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using JieNor.Framework.Consts;

namespace JieNor.Framework.AppService.IMWorker.IMUserService
{


    /// <summary>
    /// 设置用户的在线状态，并通知客户端更新用户的状态
    /// </summary>
    [InjectService]
    [FormId(HtmlFormIdConst.Html_IM_IMUser)]
    [OperationNo("SetIMUserStatus")]
    public class SetIMUserStatus : AbstractOperationServicePlugIn
    {

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var userKey = this.GetQueryOrSimpleParam<string>("userKey");
            var st = this.GetQueryOrSimpleParam<string>("status");
            IMUserStatus status = IMUserStatus.Active;
            Enum.TryParse<IMUserStatus>(st,out status);
            var msgPiple = this.Container.GetService<IUserServiceProxy>();
            msgPiple.SetIMUserStatus(this.Context, userKey, status);
            this.Result.IsSuccess = true;

        }








    }


}
