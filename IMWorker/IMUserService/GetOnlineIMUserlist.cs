using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.IM;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using JieNor.Framework.Consts;

namespace JieNor.Framework.AppService.IMWorker.IMUserService
{
    /// <summary>
    /// 获取在线用户列表
    /// </summary>
    [InjectService]
    [FormId(HtmlFormIdConst.Html_IM_IMUser)]
    [OperationNo("GetOnlineIMUserlist")]
    public class GetOnlineIMUserlist : AbstractOperationServicePlugIn
    {

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var msgPiple = this.Container.GetService<IUserServiceProxy>();
            this.Result.SrvData = msgPiple.GetOnlineIMUserlist(this.Context);
            this.Result.IsSuccess = true;

        }


    }





}
