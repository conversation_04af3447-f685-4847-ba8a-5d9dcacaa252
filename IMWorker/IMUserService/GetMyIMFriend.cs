using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.IM;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using JieNor.Framework.Consts;

namespace JieNor.Framework.AppService.IMWorker.IMUserService
{
    /// <summary>
    /// 获取好友列表
    /// </summary>
    [InjectService]
    [FormId(HtmlFormIdConst.Html_IM_IMUser)]
    [OperationNo("GetMyIMFriend")]
    public class GetMyIMFriend : AbstractOperationServicePlugIn
    {

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var userId = this.GetQueryOrSimpleParam<string>("chatUserId", "");
            var msgPiple = this.Container.GetService<IChatServiceProxy>();
            this.Result.SrvData = msgPiple.GetMyIMFriend(this.Context,userId);
            this.Result.IsSuccess = true;

        }


    }





}
