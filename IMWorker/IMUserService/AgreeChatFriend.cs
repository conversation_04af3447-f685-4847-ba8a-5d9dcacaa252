using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.IM;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using JieNor.Framework.Consts;

namespace JieNor.Framework.AppService.IMWorker.IMUserService
{
    /// <summary>
    /// 同意好友申请
    /// </summary>
    [InjectService]
    [FormId(HtmlFormIdConst.Html_IM_IMUser)]
    [OperationNo("AgreeChatFriend")]
    public class AgreeChatFriend : AbstractOperationServicePlugIn
    {

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var msg = this.GetQueryOrSimpleParam<string>("msg", "");
            var agree = this.GetQueryOrSimpleParam<string>("agree", "false").EqualsIgnoreCase("true");
            var msgPiple = this.Container.GetService<IChatServiceProxy>();
            msgPiple.AgreeChatFriend(this.Context, msg, agree);
            this.Result.IsSuccess = true;

        }


    }





}
