using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.IM;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;


namespace JieNor.Framework.AppService.IMWorker.IMUserService
{
    /// <summary>
    /// 更改用户头像
    /// </summary>
    [InjectService]
    [FormId(HtmlFormIdConst.Html_IM_IMUser)]
    [OperationNo("ChangeIMUserGravatar")]
    public class ChangeIMUserGravatar : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var userKey = this.GetQueryOrSimpleParam<string>("userKey");
            var url = this.GetQueryOrSimpleParam<string>("url");
            var msgPiple = this.Container.GetService<IUserServiceProxy>();
            msgPiple.ChangeIMUserGravatar(this.Context, userKey, url);
            this.Result.IsSuccess = true;

        }


    }





}
