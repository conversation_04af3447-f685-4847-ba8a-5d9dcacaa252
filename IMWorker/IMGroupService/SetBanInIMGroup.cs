using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.IM;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;



namespace JieNor.Framework.AppService.IMWorker.IMGroupService
{


    /// <summary>
    /// 是否禁止某些神仙在群里发言
    /// </summary>
    [InjectService]
    [FormId(HtmlFormIdConst.Html_IM_IMGroup)]
    [OperationNo("SetBanInIMGroup")]
    public class SetBanInIMGroup : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var userKey = this.GetQueryOrSimpleParam<string>("userKey");
            var groupKey = this.GetQueryOrSimpleParam<string>("groupKey");
            var reason = this.GetQueryOrSimpleParam<string>("reason");
            var isBan = this.GetQueryOrSimpleParam<string>("isBan").EqualsIgnoreCase ("true");
            var msgPiple = this.Container.GetService<IChatServiceProxy>();
            msgPiple.SetBanInIMGroup(this.Context, groupKey, userKey, isBan, reason);
            this.Result.IsSuccess = true;
        }
    }



}
