using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.IM;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;



namespace JieNor.Framework.AppService.IMWorker.IMGroupService
{


    /// <summary>
    /// 将某人踢出聊天群组
    /// </summary>
    [InjectService]
    [FormId(HtmlFormIdConst.Html_IM_IMGroup)]
    [OperationNo("KickFromIMGroup")]
    public class KickFromIMGroup : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var beRemoveUserKey = this.GetQueryOrSimpleParam<string>("beRemoveUserKey");
            var groupKey = this.GetQueryOrSimpleParam<string>("groupKey");
            var reason = this.GetQueryOrSimpleParam<string>("reason");
            var msgPiple = this.Container.GetService<IChatServiceProxy>();
            msgPiple.KickFromIMGroup(this.Context, beRemoveUserKey, groupKey, reason);
            this.Result.IsSuccess = true;
        }
    }



}
