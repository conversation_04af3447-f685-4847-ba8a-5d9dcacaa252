using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.IM;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;



namespace JieNor.Framework.AppService.IMWorker.IMGroupService
{

    /// <summary>
    /// 创建聊天群组
    /// </summary>
    [InjectService]
    [FormId(HtmlFormIdConst.Html_IM_IMGroup)]
    [OperationNo("CreatIMGroup")]
    public class CreatIMGroup : AbstractOperationServicePlugIn
    {

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {

            var jsonGroup = this.GetQueryOrSimpleParam<string>("jsonGroup");
            var msgPiple = this.Container.GetService<IChatServiceProxy>();
            msgPiple.CreatIMGroup(this.Context, jsonGroup);
            this.Result.IsSuccess = true;

        }

    }



}
