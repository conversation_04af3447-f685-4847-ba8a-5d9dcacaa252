using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.IM;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using JieNor.Framework.Consts;


namespace JieNor.Framework.AppService.IMWorker.IMGroupService
{





    /// <summary>
    /// 允许、拒绝别人的邀请
    /// </summary>
    [InjectService]
    [FormId(HtmlFormIdConst.Html_IM_IMGroup)]
    [OperationNo("GetIMCategory")]
    public class GetIMCategory : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var chatUserId = this.GetQueryOrSimpleParam<string>("chatUserId", "");
            var msgPiple = this.Container.GetService<IChatServiceProxy>();
            this.Result.SrvData = msgPiple.GetIMCategory(this.Context, chatUserId);
            this.Result.IsSuccess = true;
        }

    }


     






}
