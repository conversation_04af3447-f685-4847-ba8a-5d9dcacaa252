using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.IM;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;



namespace JieNor.Framework.AppService.IMWorker.IMGroupService
{

    /// <summary>
    /// 设置聊天群的锁定状态
    /// </summary>
    [InjectService]
    [FormId(HtmlFormIdConst.Html_IM_IMGroup)]
    [OperationNo("SetIMGroupLockStatus")]
    public class SetIMGroupLockStatus : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var reason = this.GetQueryOrSimpleParam<string>("reason");
            var groupKey = this.GetQueryOrSimpleParam<string>("groupKey");
            var isLock = this.GetQueryOrSimpleParam<string>("isLock").EqualsIgnoreCase("true");
            var msgPiple = this.Container.GetService<IChatServiceProxy>();
            msgPiple.SetIMGroupLockStatus(this.Context, groupKey, isLock, reason);
            this.Result.IsSuccess = true;
        }
    }



}
