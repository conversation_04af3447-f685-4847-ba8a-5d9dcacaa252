using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.IM;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;



namespace JieNor.Framework.AppService.IMWorker.IMGroupService
{

    /// <summary>
    /// 设置群成员的群管理员属性
    /// </summary>
    [InjectService]
    [FormId(HtmlFormIdConst.Html_IM_IMGroup)]
    [OperationNo("SetIMGroupAdmin")]
    public class SetIMGroupAdmin : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var userKey = this.GetQueryOrSimpleParam<string>("userKey");
            var groupKey = this.GetQueryOrSimpleParam<string>("groupKey"); 
            var isAdmin = this.GetQueryOrSimpleParam<string>("isAdmin").EqualsIgnoreCase("true");
            var msgPiple = this.Container.GetService<IChatServiceProxy>();
            msgPiple.SetIMGroupAdmin(this.Context, groupKey, userKey, isAdmin);
            this.Result.IsSuccess = true;
        }
    }



}
