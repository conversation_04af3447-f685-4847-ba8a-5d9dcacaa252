using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject.IM;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.IM;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;



namespace JieNor.Framework.AppService.IMWorker.IMGroupService
{

    /// <summary>
    /// 申请加入聊天群组
    /// </summary>
    [InjectService]
    [FormId(HtmlFormIdConst.Html_IM_IMGroup)]
    [OperationNo("JoinToIMGroup")]
    public class JoinToIMGroup : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var grpKey = this.GetQueryOrSimpleParam<string>("grpKey");
            var msgPiple = this.Container.GetService<IChatServiceProxy>();
            msgPiple.JoinToIMGroup(this.Context, grpKey);
            this.Result.IsSuccess = true;
        }
    }



}
