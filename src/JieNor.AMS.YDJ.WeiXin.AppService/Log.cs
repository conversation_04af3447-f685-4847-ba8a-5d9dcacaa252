using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.WeiXin.AppService
{
    public class Log
    {

        private static StringBuilder LogTemp = new StringBuilder();
        /// <summary>
        /// 写日志
        /// </summary>
        /// <param name="Str">需要写的内容</param>
        public static void WriteLog(object Str1)
        {
            string WriteStr = DateTime.Now.ToString("HH:mm:ss") + "\t";
            try
            {
                string Str = Str1.ToString();
                string path = AppDomain.CurrentDomain.BaseDirectory + @"Log";
                WriteStr += Str;
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
                using (StreamWriter streamWriter = new StreamWriter(Path.Combine(path, DateTime.Now.ToString("yyyy-MM-dd") + ".log"), true, Encoding.UTF8))
                {
                    if (LogTemp != null)
                    {
                        streamWriter.Write(LogTemp.ToString());
                    }
                    streamWriter.WriteLine(WriteStr);
                    streamWriter.Flush();
                    streamWriter.Close();
                }
                LogTemp.Clear();
            }
            catch (Exception)
            {
                LogTemp.AppendLine(WriteStr);
            }
        }
    }
}
