using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Reflection;
using JieNor.Framework.IoC;

namespace JieNor.AMS.YDJ.WeiXin.AppService
{
    [InjectService]
    public class DynamicRegisterWeiXinHandle : IDynamicRegisterController
    {
        public Assembly[] GetControllerAssembly()
        {
            return new Assembly[] { this.GetType().Assembly };
        }
    }
}
