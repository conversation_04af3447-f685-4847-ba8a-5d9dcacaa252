using System;
using JieNor.AMS.YDJ.Consumer.API.DTO;
using JieNor.AMS.YDJ.Consumer.API.Model;
using JieNor.AMS.YDJ.Consumer.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface.Cache;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.Auth
{
    /// <summary>
    /// 微信小程序：发送短信验证码接口
    /// </summary>
    public class CreateLoginTokenController : AuthController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(CreateLoginTokenDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            if (!this.ValidateParam(dto, resp)) return resp;

            var customer = this.Context.LoadBizDataById("ydj_customer", dto.CustomerId);
            if (customer != null)
            {
                string token = this.CreateToken(customer);
                resp.Data = token;
                resp.Success = true;
                resp.Message = "生成成功！";
            }
            else
            {
                resp.Success = false;
                resp.Message = "生成失败：客户不存在或已删除！";
            }
            return resp;
        }


        /// <summary>
        /// 校验参数
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        /// <returns></returns>
        private bool ValidateParam(CreateLoginTokenDTO dto, BaseResponse<object> resp)
        {
            if (dto.CustomerId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = $"发送失败：客户id {nameof(dto.CustomerId)} 不能为空！";
                return false;
            }

            if (dto.CustomerCode.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = $"发送失败：商户编码 {nameof(dto.CustomerCode)} 不能为空！";
                return false;
            }

            return true;
        }
    }
}