using System;
using System.Collections.Generic;
using JieNor.AMS.YDJ.Consumer.API.DTO.User;
using JieNor.AMS.YDJ.Consumer.API.Model.ENT.Coupon;
using JieNor.AMS.YDJ.Consumer.API.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Utils;
using System.Data;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.ENT.Coupon
{
    /// <summary>
    /// 消费者小程序：我的优惠券列表接口
    /// </summary>
    public class MyCouponListController : BaseController
    {
        public object Any(MyCouponListDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseListPageData<MyCouponListModel>>
            {
                Data = new BaseListPageData<MyCouponListModel>()
            };

            if (!ValidateParam(dto, resp))
            {
                return resp;
            }

            //设置响应数据包
            this.SetResponseData(dto, resp);

            return resp;
        }

        /// <summary>
        /// 校验参数
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        /// <returns></returns>
        private bool ValidateParam(MyCouponListDTO dto, BaseResponse<BaseListPageData<MyCouponListModel>> resp)
        {
            if (dto.Tab.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = $"页签 {nameof(dto.Tab)} 不能为空！";
                return false;
            }

            return true;
        }

        /// <summary>
        /// 设置响应数据包
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        private void SetResponseData(MyCouponListDTO dto, BaseResponse<BaseListPageData<MyCouponListModel>> resp)
        {
            resp.Message = "取数成功！";
            resp.Success = true;

            var sqlBuilder = BuildSqlBuilder(this.Context, dto.Tab, dto.PageSize, dto.PageIndex);

            resp.Data.PageSize = dto.PageSize;
            resp.Data.TotalRecord = GetCount(sqlBuilder);
            resp.Data.List = new List<MyCouponListModel>();

            using (var reader = this.DBService.ExecuteReader(this.Context, sqlBuilder.DataSql, sqlBuilder.SqlParams))
            {
                while (reader.Read())
                {
                    var model = new MyCouponListModel
                    {
                        Id = reader.GetValueToString("fid"),
                        Name = reader.GetValueToString("fcouponname"),
                        Amount = Math.Round(reader.GetValueToDecimal("famount"), 2),
                        ExpiredEndDate = reader.GetDateTimeEx("fexpiredenddate"),
                        ExpiredStartDate = reader.GetDateTimeEx("fexpiredstartdate"),
                        UseTime = reader.GetValueToDateTime("fusetime")
                    };

                    string useCondition = string.Empty;
                    string useDeptType = reader.GetValueToString("fusedepttype");
                    switch (useDeptType?.ToLowerInvariant())
                    {
                        // 所有门店
                        case "usedepttype_01":
                            useCondition += "所有门店，";
                            break;
                        // 指定门店
                        case "usedepttype_02":
                            useCondition += $"限{reader.GetValueToString("fusedepttype_deptids_txt")}，";
                            break;
                        default:
                            throw new BusinessException($"未实现适用门店{useDeptType}的取数逻辑！");
                    }
                    string productScope = reader.GetValueToString("fproductscope");
                    switch (productScope?.ToLowerInvariant())
                    {
                        // 所有商品：全场所有商品可用。
                        case "productscope_01":
                            useCondition += "全场所有商品可用";
                            break;
                        // 指定商品：指定商品可用。
                        case "productscope_02":
                            useCondition += $"指定商品可用";
                            break;
                        // 指定品牌：指定XX品牌可用。
                        // 指定品牌和系列：指定XX品牌XX系列可用。
                        case "productscope_03":
                            string brandTxt = reader.GetValueToString("fproductscope_brandids_txt");
                            string seriesTxt = reader.GetValueToString("fproductscope_seriesids_txt");
                            if (seriesTxt.IsNullOrEmptyOrWhiteSpace())
                            {
                                useCondition += $"指定{brandTxt}品牌可用";
                            }
                            else
                            {
                                useCondition += $"指定{brandTxt}品牌{seriesTxt}系列可用";
                            }
                            break;
                        // 高级筛选：指定商品可用。
                        case "productscope_04":
                            useCondition += $"指定商品可用";
                            break;
                        default:
                            throw new BusinessException($"未实现适用商品{productScope}的取数逻辑！");
                    }

                    model.UseCondition = useCondition;

                    resp.Data.List.Add(model);
                }
            }

        }

        public static SqlBuilder BuildSqlBuilder(UserContext userCtx, string tab, int pageSize, int pageIndex)
        {
            SqlBuilder sqlBuilder = new SqlBuilder();

            sqlBuilder.SqlParams.Add(new SqlParam("@fmainorgid", DbType.String, userCtx.Company));
            sqlBuilder.SqlParams.Add(new SqlParam("@fcustomerid", DbType.String, userCtx.UserId));

            string orderBy, sql;

            switch (tab?.ToLower())
            {
                /*
                 * 待使用：导购赠送的优惠券，且在有效期内，剩余未使用的
                 * 1. 【业务类型】=发券
                 * 2. 【待核券余额】>0
                 * 3. 有效期内
                 *
                 * 【金额】=待核券余额
                 * 排序：按导购赠送的时间，最新赠送在顶部。
                 */
                case "using":
                    {
                        orderBy = "a.fcreatedate desc";
                        sql = $@"
select 
    row_number() over(order by {orderBy}) rownum,
    a.fid,
    a.fexpiredstartdate,
    a.fexpiredenddate,
    a.fmodifydate as fusetime,
    b.fname as fcouponname,
    b.fusedepttype,
    b.fusedepttype_deptids_txt,
    b.fproductscope,
    b.fproductscope_brandids_txt,
    b.fproductscope_seriesids_txt,
    a.funverifyamount as famount
from t_ydj_couponrecord a left join t_ydj_coupon b on a.fcouponid=b.fid
where a.fmainorgid=@fmainorgid 
and a.fcustomerid=@fcustomerid
and a.foperatetype=@foperatetype 
and a.funverifyamount>0
and ((a.fexpiredstartdate is null and a.fexpiredenddate is null) or @date between a.fexpiredstartdate and a.fexpiredenddate)";

                        sqlBuilder.DataSql = $@"select top {pageSize} * from ({sql}) as t  where rownum > {pageSize * (pageIndex - 1)}";
                        sqlBuilder.CountSql = $@"select COUNT(1) total_count from ({sql}) as t";

                        sqlBuilder.SqlParams.Add(new SqlParam("@foperatetype", DbType.String, "operatetype_01"));
                        sqlBuilder.SqlParams.Add(new SqlParam("@date", DbType.DateTime, BeiJingTime.Now.Date));
                    }
                    break;
                /*
                 * 已使用：赠送的优惠券已使用的那部分优惠券。
                 * 1. 【业务类型】=发券或作废
                 * 2. 【累计核券金额】>0
                 * 
                 * 【金额】=累计核券金额
                 * 排序：按使用时间，最新使用在顶部。
                 */
                case "used":
                    {
                        orderBy = "a.fmodifydate desc";
                        sql = $@"
select 
    row_number() over(order by {orderBy}) rownum,
    a.fid,
    a.fexpiredstartdate,
    a.fexpiredenddate,
    a.fmodifydate as fusetime,
    b.fname as fcouponname,
    b.fusedepttype,
    b.fusedepttype_deptids_txt,
    b.fproductscope,
    b.fproductscope_brandids_txt,
    b.fproductscope_seriesids_txt,
    a.fsumverifyamount as famount
from t_ydj_couponrecord a left join t_ydj_coupon b on a.fcouponid=b.fid
where a.fmainorgid=@fmainorgid 
and a.fcustomerid=@fcustomerid
and a.foperatetype in (@sendtype, @canceltype)
and a.fsumverifyamount>0";

                        sqlBuilder.DataSql = $@"select top {pageSize} * from ({sql}) as t  where rownum > {pageSize * (pageIndex - 1)}";
                        sqlBuilder.CountSql = $@"select COUNT(1) total_count from ({sql}) as t";

                        sqlBuilder.SqlParams.Add(new SqlParam("@sendtype", DbType.String, "operatetype_01"));
                        sqlBuilder.SqlParams.Add(new SqlParam("@canceltype", DbType.String, "operatetype_03"));
                    }
                    break;
                /*
                 * 已失效：赠送的优惠券未使用但已过有效期，或者赠送的优惠券未使用被商家强制作废。
                 * 1. 【业务类型】=发券且过期
                 * 2. 【业务类型】=作废
                 * 
                 * 【金额】=待核券余额
                 * 排序：按失效的时间，最新失效在顶部。
                 */
                case "cancel":
                    {
                        orderBy = "a.fcanceldate desc";
                        sql = $@"
select 
    row_number() over(order by {orderBy}) rownum,
    a.fid,
    a.fexpiredstartdate,
    a.fexpiredenddate,
    a.fmodifydate as fusetime,
    b.fname as fcouponname,
    b.fusedepttype,
    b.fusedepttype_deptids_txt,
    b.fproductscope,
    b.fproductscope_brandids_txt,
    b.fproductscope_seriesids_txt,
    a.funverifyamount as famount
from t_ydj_couponrecord a left join t_ydj_coupon b on a.fcouponid=b.fid
where a.fmainorgid=@fmainorgid 
and a.fcustomerid=@fcustomerid
and 
(
    (a.foperatetype=@sendtype and (@date > a.fexpiredenddate))
    or (a.foperatetype=@canceltype)
)";

                        sqlBuilder.DataSql = $@"select top {pageSize} * from ({sql}) as t  where rownum > {pageSize * (pageIndex - 1)}";
                        sqlBuilder.CountSql = $@"select COUNT(1) total_count from ({sql}) as t";

                        sqlBuilder.SqlParams.Add(new SqlParam("@sendtype", DbType.String, "operatetype_01"));
                        sqlBuilder.SqlParams.Add(new SqlParam("@canceltype", DbType.String, "operatetype_03"));
                        sqlBuilder.SqlParams.Add(new SqlParam("@date", DbType.DateTime, BeiJingTime.Now.Date));
                    }
                    break;
                default:
                    throw new BusinessException($"未实现页签{tab}的取数逻辑！");
            }

            return sqlBuilder;
        }

        /// <summary>
        /// 获取客户可用优惠券数量（待使用）
        /// </summary> 
        /// <returns></returns>
        private int GetCount(SqlBuilder sqlBuilder)
        {
            int count = 0;

            using (var reader = this.DBService.ExecuteReader(this.Context, sqlBuilder.CountSql, sqlBuilder.SqlParams))
            {
                if (reader.Read())
                {
                    count = reader.GetValueToInt("total_count");
                }
            }

            return count;
        }
    }
}
