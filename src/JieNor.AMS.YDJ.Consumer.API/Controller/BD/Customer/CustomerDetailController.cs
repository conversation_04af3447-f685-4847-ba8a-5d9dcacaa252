using System;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.Consumer.API.Utils;
using JieNor.AMS.YDJ.Consumer.API.Model.BD.Customer.MemberInformation;
using JieNor.AMS.YDJ.Consumer.API.Model;
using JieNor.AMS.YDJ.Consumer.API.DTO.BD.Customer;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.BD.Customer
{
    /// <summary>
    /// 会员信息详情取数接口
    /// </summary>
    public class CustomerDetailModel : BaseController
    {
        /// <summary>
        /// 客户表单模型
        /// </summary>
        protected HtmlForm CustomerForm { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(CustomerDetailDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<MemberInformationDetailModel>();

            //根据唯一标识获取数据
            this.CustomerForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_customer");
            var customerObj = this.CustomerForm.GetBizDataById(this.Context, this.Context.UserId, true);

            //设置响应数据包
            this.SetResponseData(customerObj, resp);

            return resp;
        }

        /// <summary>
        /// 设置响应数据包
        /// </summary>
        /// <param name="customer"></param>
        /// <param name="resp"></param>
        private void SetResponseData(DynamicObject customer, BaseResponse<MemberInformationDetailModel> resp)
        {
            if (customer == null)
            {
                resp.Message = "客户不存在或已被删除！";
                resp.Success = false;
                return;
            }

            resp.Message = "取数成功！";
            resp.Success = true;

            resp.Data.Id = Convert.ToString(customer["id"]);
            resp.Data.Number = Convert.ToString(customer["fnumber"]);
            resp.Data.Name = Convert.ToString(customer["fname"]);
            resp.Data.Phone = Convert.ToString(customer["fphone"]).Trim();
            resp.Data.Province = new ComboDataModel(customer["fprovince_ref"] as DynamicObject);
            resp.Data.City = new ComboDataModel(customer["fcity_ref"] as DynamicObject);
            resp.Data.Region = new ComboDataModel(customer["fregion_ref"] as DynamicObject);
            resp.Data.Address = Convert.ToString(customer["faddress"]).Trim();
            resp.Data.CreateDate = Convert.ToDateTime(customer["fcreatedate"]);
            resp.Data.Gender = new ComboDataModel(customer["fgender_ref"] as DynamicObject);
            resp.Data.Images = Convert.ToString(customer["fheadimgurl"]);
            resp.Data.Email = Convert.ToString(customer["femail"]).Trim();
            resp.Data.Birthdate = customer["fbirthdate"] == null ? "" : Convert.ToString(customer["fbirthdate"]);
        }
    }
}
