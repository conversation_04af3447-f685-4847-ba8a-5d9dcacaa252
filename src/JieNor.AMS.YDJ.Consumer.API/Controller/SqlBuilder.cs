using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Consumer.API.Controller
{
    public class SqlBuilder
    {
        /// <summary>
        /// 数据sql
        /// </summary>
        public string DataSql { get; set; } = string.Empty;

        /// <summary>
        /// 记录数sql
        /// </summary>
        public string CountSql { get; set; } = string.Empty;

        /// <summary>
        /// 参数
        /// </summary>
        public List<SqlParam> SqlParams { get; set; } = new List<SqlParam>();
    }
}
