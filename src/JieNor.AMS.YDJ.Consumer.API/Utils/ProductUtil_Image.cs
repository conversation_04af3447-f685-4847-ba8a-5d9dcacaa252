using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Consumer.API.Model;

namespace JieNor.AMS.YDJ.Consumer.API.Utils
{
    /// <summary>
    /// 商品图片帮助类：定义商品图片相关的通用逻辑
    /// </summary>
    public partial class ProductUtil
    {
        /// <summary>
        /// 获取商品图片
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productObj">商品数据包</param>
        /// <param name="auxPropVals">辅助属性值列表</param>
        /// <param name="customDesc">定制说明</param>
        /// <returns>商品图片列表</returns>
        public static List<BaseImageModel> GetImages(
            UserContext userCtx,
            DynamicObject productObj,
            List<Dictionary<string, string>> auxPropVals = null,
            string customDesc = "")
        {
            var auxPropWhere = GetAuxPropWhere(auxPropVals);
            return GetImages(userCtx, productObj, auxPropWhere, customDesc, null);
        }

        /// <summary>
        /// 获取商品图片
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productObj">商品数据包</param>
        /// <param name="auxPropValId">商品辅助属性值集ID</param>
        /// <param name="customDesc">定制说明</param>
        /// <returns>商品图片列表</returns>
        public static List<BaseImageModel> GetImages(
            UserContext userCtx,
            DynamicObject productObj,
            string auxPropValId = "",
            string customDesc = "")
        {
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@auxPropValId", System.Data.DbType.String, auxPropValId),
            };
            var auxPropWhere = $" and te.fattrinfo=@auxPropValId";
            return GetImages(userCtx, productObj, auxPropWhere, customDesc, sqlParam);
        }

        /// <summary>
        /// 获取商品图片
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="auxPropVals">辅助属性值列表</param>
        /// <param name="customDesc">定制说明</param>
        /// <returns>商品图片列表</returns>
        public static List<BaseImageModel> GetImages(
            UserContext userCtx,
            string productId,
            List<Dictionary<string, string>> auxPropVals = null,
            string customDesc = "")
        {
            var auxPropWhere = GetAuxPropWhere(auxPropVals);
            var productObj = userCtx.LoadBizDataById("ydj_product", productId);
            return GetImages(userCtx, productObj, auxPropWhere, customDesc, null);
        }

        /// <summary>
        /// 获取商品图片
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="auxPropValId">商品辅助属性值集ID</param>
        /// <param name="customDesc">定制说明</param>
        /// <returns>商品图片列表</returns>
        public static List<BaseImageModel> GetImages(
            UserContext userCtx,
            string productId,
            string auxPropValId = "",
            string customDesc = "")
        {
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@auxPropValId", System.Data.DbType.String, auxPropValId),
            };
            var auxPropWhere = $" and te.fattrinfo=@auxPropValId";
            var productObj = userCtx.LoadBizDataById("ydj_product", productId);
            return GetImages(userCtx, productObj, auxPropWhere, customDesc, sqlParam);
        }

        /// <summary>
        /// 获取商品图片
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productObj">商品数据包</param>
        /// <param name="auxPropWhere">辅助属性值过滤条件</param>
        /// <param name="customDesc">定制说明</param>
        /// <param name="paramList">参数列表</param>
        /// <returns>商品图片列表</returns>
        private static List<BaseImageModel> GetImages(
            UserContext userCtx,
            DynamicObject productObj,
            string auxPropWhere,
            string customDesc = "",
            List<SqlParam> paramList = null)
        {
            customDesc = (customDesc ?? "").Trim();

            var images = new List<BaseImageModel>();

            var productId = Convert.ToString(productObj?["id"]);
            if (productId.IsNullOrEmptyOrWhiteSpace()) return images;

            var profileService = userCtx.Container.GetService<ISystemProfile>();

            //只取主图
            var onlyProductImg = profileService.GetSystemParameter(userCtx, "bas_storesysparam", "fonlyproductimg", false);
            if (onlyProductImg)
            {
                return GetProductImages(userCtx, productObj);
            }

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("@fmaterialid", System.Data.DbType.String, productId)
            };
            if (paramList != null)
            {
                sqlParam.AddRange(paramList);
            }

            var sqlWhere = $@"
            te.fmainorgid=@fmainorgid and te.fproductid=@fmaterialid {auxPropWhere}";

            var sqlText = $@"
            select top 1 fimage,fimage_txt from t_ydj_commoditygallery te with(nolock) 
            where {sqlWhere}";

            var image = "";
            var imageTxt = "";

            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    image = reader.GetValueToString("fimage");
                    imageTxt = reader.GetValueToString("fimage_txt");
                }
            }

            if (!image.IsNullOrEmptyOrWhiteSpace())
            {
                images = ImageFieldUtil.ParseImages(image, imageTxt, true);
            }

            //先取图库再取主图（图库没有取到的情况下再取商品主图）
            var enableProductImg = profileService.GetSystemParameter(userCtx, "bas_storesysparam", "fenableproductimg", false);
            if (!images.Any() && enableProductImg)
            {
                images = GetProductImages(userCtx, productObj);
            }

            return images;
        }

        /// <summary>
        /// 获取商品主图
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productObj">商品ID</param>
        /// <returns>商品主图列表</returns>
        private static List<BaseImageModel> GetProductImages(UserContext userCtx, DynamicObject productObj)
        {
            var images = new List<BaseImageModel>();
            if (userCtx == null || productObj == null) return images;

            var image = Convert.ToString(productObj["fimage"]);
            var imageTxt = Convert.ToString(productObj["fimage_txt"]);

            images = ImageFieldUtil.ParseImages(image, imageTxt, true);

            return images;
        }
    }
}