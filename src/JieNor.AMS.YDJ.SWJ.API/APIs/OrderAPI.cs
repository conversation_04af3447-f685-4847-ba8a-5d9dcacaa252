using System;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using System.Threading;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.SWJ.API.Request.SWJ;
using JieNor.AMS.YDJ.SWJ.API.Response.SWJ;
using JieNor.Framework;
using JieNor.Framework.Interface;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.SWJ.API.APIs
{
    /// <summary>
    /// 订单相关接口
    /// </summary>
    public class OrderAPI
    {
        public UserContext agentCtx { get; set; }

        public ILogService loger { get; set; }
        public OrderAPI(UserContext _agentCtx)
        {
            agentCtx = _agentCtx;
        }
        /// <summary>
        /// 获取销售单信息
        /// </summary>
        /// <param name="client"></param>
        /// <param name="platformOrderId">订单的通用ID（必填）</param>
        /// <param name="platformOrderCode">订单编号</param>
        /// <returns></returns>
        public ShopOrderInfoResponse GetShopOrderInfo(SWJClient client, string platformOrderId, string platformOrderCode = null)
        {
            string accessToken = client.GetAccessToken();
            //正式环境需要使用这个配置
            string url = $"/common/api/v2/derucci/getAimesPlatformOrderDetailAndUploadOss?sysCode=external&access_token={accessToken}";

            var request = new ShopOrderInfoRequest
            {
                platformOrderCode = platformOrderCode,
                platformOrderId = platformOrderId
            };

            var resp = JsonClient.InvokeThirdByJson<ShopOrderInfoRequest, ShopOrderInfoResponse>(client.OpenServer, url, request);
            while (!resp.success)
            {
                for (int i = 0; i < 3; i++)
                {
                    Thread.Sleep(1000);
                    accessToken = client.GetAccessToken(true);
                    if (client.OpenGatWayServer.Headers.ContainsKey("Access-token"))
                    {
                        client.OpenGatWayServer.Headers.Remove("Access-token");
                        client.OpenGatWayServer.Headers.Add("Access-token", accessToken);
                    }
                    //Task.Delay(1000);
                    resp = JsonClient.InvokeThirdByJson<ShopOrderInfoRequest, ShopOrderInfoResponse>(client.OpenServer, url, request);
                    if (!resp.success)
                    {
                        if (i == 2)
                        {
                            return resp;
                        }
                        continue;
                    }
                    else break;
                }
            }
            if (resp.success)
            {
                //把另一个接口的数据合并进来
                var orderDetailResult = GetOrderDetail(client, platformOrderId, platformOrderCode);
                if (orderDetailResult.success)
                {
                    resp.result.basicMaterial = orderDetailResult.data?.basicMaterial;
                    resp.result.material = orderDetailResult.data?.material;
                    resp.result.schemePreviewImage = orderDetailResult.data?.schemePreviewImage;
                    resp.result.unitCategoryName = orderDetailResult.data?.unitCategoryName;
                }
                else
                {

                    loger.WriteLogToFile("【GetOrderDetail】：时间：{0}，操作：【三维家接口，获取工厂单方案内容接口失败】，内容:{1}".Fmt(
                         DateTime.Now.ToString("HH:mm:ss"), "orderDetailResult：" + JsonConvert.SerializeObject(orderDetailResult)),
                        "SWJLogFile");
                    return new ShopOrderInfoResponse()
                    {
                        success = false,
                        errorMessage = orderDetailResult.msg,
                        errorCode = orderDetailResult.code
                    };
                }
                //把另一个接口的数据合并进来
                var afterOrderDetailResult = GetAfterSaleOrder(client, platformOrderId, platformOrderCode);
                if (afterOrderDetailResult.success)
                {
                    resp.result.schemeContent = afterOrderDetailResult.data?.schemeContent;
                }
                else
                {
                    loger.WriteLogToFile("【GetAfterSaleOrder】：时间：{0}，操作：【三维家接口，获取工厂单方案内容接口失败】，内容:{1}".Fmt(
                         DateTime.Now.ToString("HH:mm:ss"), "afterOrderDetailResult：" + JsonConvert.SerializeObject(afterOrderDetailResult)),
                        "SWJLogFile");
                    return new ShopOrderInfoResponse()
                    {
                        success = false,
                        errorMessage = afterOrderDetailResult.msg,
                        errorCode = afterOrderDetailResult.code
                    };
                }
            }
            return resp;
        }

        /// <summary>
        /// 回传单号给三维家
        /// </summary>
        /// <param name="client"></param>
        /// <param name="orderId"></param>
        /// <param name="orderCode"></param>
        /// <param name="contractNo"></param>
        /// <returns></returns>
        public SWJResponse updateOrderContractNo(SWJClient client, string orderId, string orderCode = "", string contractNo = "")
        {
            try
            {
                string accessToken = client.GetAccessToken();
                //正式环境需要使用这个配置
                string url = $"/common/api/v2/aimesOrder/upShopOrderContractNo?sysCode=external&access_token={accessToken}";

                var request = new UpdateShopOrderInfoRequest
                {
                    orderId = orderId,
                    orderCode = orderCode,
                    contractNo = contractNo
                };

                var resp = JsonClient.InvokeThirdByJson<UpdateShopOrderInfoRequest, SWJResponse>(client.OpenServer, url, request);

                return resp;
            }
            catch (Exception ex)
            {
                return new SWJResponse()
                {
                    errorMessage = ex.Message + "；StackTrace：" + ex.StackTrace
                };
            }
        }

        public AfterSaleInfoResult GetAfterSaleOrder(SWJClient client, string platformOrderId, string platformOrderCode = null)
        {
            try
            {
                var context =
                loger = agentCtx.Container.GetService<ILogService>();
                string accessToken = client.GetAccessToken();
                //正式环境需要使用这个配置
                string url = $"/api/v1/aimes/factoryOrderOp/getFactoryOrderScheme";

                var request = new ShopOrderInfoRequest
                {
                    factoryOrderId = platformOrderId
                };
                if (!client.OpenGatWayServer.Headers.ContainsKey("Access-token"))
                    client.OpenGatWayServer.Headers.Add("Access-token", accessToken);
                var resp = JsonClient.InvokeThirdByJson<ShopOrderInfoRequest, AfterSaleInfoResult>(client.OpenGatWayServer, url, request);
                while (!resp.success)
                {
                    for (int i = 0; i < 3; i++)
                    {
                        Thread.Sleep(1000);
                        accessToken = client.GetAccessToken(true);
                        if (client.OpenGatWayServer.Headers.ContainsKey("Access-token"))
                        {
                            client.OpenGatWayServer.Headers.Remove("Access-token");
                            client.OpenGatWayServer.Headers.Add("Access-token", accessToken);
                        }
                        //Task.Delay(1000);
                        resp = JsonClient.InvokeThirdByJson<ShopOrderInfoRequest, AfterSaleInfoResult>(client.OpenGatWayServer, url, request);
                        if (!resp.success)
                        {
                            if (i == 2)
                            {
                                return resp;
                            }
                            continue;
                        }
                        else break;
                    }
                }

                loger.WriteLogToFile("【GetAfterSaleOrder】：时间：{0}，操作：【三维家接口，获取工厂单方案内容接口成功】，内容:{1}".Fmt(DateTime.Now.ToString("HH:mm:ss"), "platformOrderId:" + platformOrderId + "；resp：" + JsonConvert.SerializeObject(resp)), "SWJLogFile");
                return resp;
            }
            catch (Exception ex)
            {
                loger.WriteLogToFile("【GetAfterSaleOrder】：时间：{0}，操作：【三维家接口，获取工厂单方案内容接口失败】，内容:{1}".Fmt(
                     DateTime.Now.ToString("HH:mm:ss"), "platformOrderId:" + platformOrderId + "；result：" + JsonConvert.SerializeObject(ex)),
                    "SWJLogFile");
                //额外获取的失败了不影响主流程，这里做日志记录异常
                return new AfterSaleInfoResult() { success = false };
            }
        }


        /// <summary>
        /// 三维家的标准接口
        /// </summary>
        /// <param name="client"></param>
        /// <param name="platformOrderId"></param>
        /// <param name="platformOrderCode"></param>
        /// <returns></returns>
        public OrderDetailResult GetOrderDetail(SWJClient client, string orderID, string orderCode = null)
        {
            try
            {
                loger = agentCtx.Container.GetService<ILogService>();
                string accessToken = client.GetAccessToken();
                //正式环境需要使用这个配置
                string url = $"api/v1/aimes/factoryOrderOp/getFactoryOrderInfo";

                var request = new UpdateShopOrderInfoRequest
                {
                    orderID = orderID,
                    orderCode = orderCode
                };
                if (!client.OpenGatWayServer.Headers.ContainsKey("Access-token"))
                    client.OpenGatWayServer.Headers.Add("Access-token", accessToken);
                var resp = JsonClient.InvokeThirdByJson<UpdateShopOrderInfoRequest, OrderDetailResult>(client.OpenGatWayServer, url, request);
                while (!resp.success)
                {
                    for (int i = 0; i < 3; i++)
                    {
                        Thread.Sleep(1000);
                        accessToken = client.GetAccessToken(true);
                        if (client.OpenGatWayServer.Headers.ContainsKey("Access-token"))
                        {
                            client.OpenGatWayServer.Headers.Remove("Access-token");
                            client.OpenGatWayServer.Headers.Add("Access-token", accessToken);
                        }
                        //Task.Delay(1000);
                        resp = JsonClient.InvokeThirdByJson<UpdateShopOrderInfoRequest, OrderDetailResult>(client.OpenGatWayServer, url, request);
                        if (!resp.success)
                        {
                            if (i == 2)
                            {
                                return resp;
                            }
                            continue;
                        }
                        else break;
                    }
                }

                loger.WriteLogToFile("【GetOrderDetail】：时间：{0}，操作：【三维家接口，获取工厂单详细信息接口成功】，内容:{1}".Fmt(DateTime.Now.ToString("HH:mm:ss"), "orderID:" + orderID + "；resp：" + JsonConvert.SerializeObject(resp)), "SWJLogFile");
                return resp;
            }
            catch (Exception ex)
            {
                loger.WriteLogToFile("【GetOrderDetail】：时间：{0}，操作：【三维家接口，获取工厂单详细信息接口失败】，内容:{1}".Fmt(
                     DateTime.Now.ToString("HH:mm:ss"), "orderID:" + orderID + "；result：" + JsonConvert.SerializeObject(ex)),
                    "SWJLogFile");
                //额外获取的失败了不影响主流程，这里做日志记录异常
                return new OrderDetailResult() { success = false };
            }
        }
    }
}
