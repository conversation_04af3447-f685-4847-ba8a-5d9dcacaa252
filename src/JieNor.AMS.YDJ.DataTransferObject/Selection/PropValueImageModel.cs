using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.DataTransferObject
{
    /// <summary>
    /// 属性值图片模型
    /// </summary>
    public class PropValueImageModel
    {
        /// <summary>
        /// 图片哈希值
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 图片文件名
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 原图地址
        /// </summary>
        public string Url { get; set; } = string.Empty;

        /// <summary>
        /// 缩略图地址
        /// </summary>
        public string ThumbUrl { get; set; } = string.Empty;
    }
}
