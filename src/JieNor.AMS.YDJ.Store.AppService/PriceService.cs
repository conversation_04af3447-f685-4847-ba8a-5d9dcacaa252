using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.AMS.YDJ.Core;
using System.Diagnostics;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.SuperOrm.DataManager;
using System.Collections.Concurrent;

namespace JieNor.AMS.YDJ.Store.AppService
{
    /// <summary>
    /// 商品取价服务
    /// </summary>
    [InjectService]
    public class PriceService : IPriceService
    {
        /// <summary>
        /// 用于过滤指定辅助属性的SQL条件模板
        /// </summary>
        const string containAttrInfoSql = @" 
        and 
        (
	        case 
                when pe.fattrinfo='' then 1 
		        when 
                (
			        (select count(1) from ({0})t)={1} 
			        and
			        (
				        select count(1) from t_bd_auxpropvalue av with(nolock)
				        inner join t_bd_auxpropvalueentry ave with(nolock) on av.fid=ave.fid
				        where av.fid=pe.fattrinfo 
			        )={1}
		        ) then 1 
		        else 0 
	        end 
        )=1
        ";

        /// <summary>
        /// 用于过滤指定辅助属性组合值集的SQL条件模板
        /// 改动最小：在原基础上不再根据属性值内码匹配而是根据属性值名称匹配。
        /// </summary>
        const string containAuxPropSql = @"
        select 1 as c from t_bd_auxpropvalueentry ave with(nolock)
        inner join t_sel_prop ap with(nolock) on ap.fid=ave.fauxpropid
        where ap.{2}=@fauxpropid{0}{1} and ave.fvaluename=@fvaluename{0}{1} and ave.fid=pe.fattrinfo
        ";

        /// <summary>
        /// 用于过滤空辅助属性的SQL条件模板
        /// </summary>
        const string noContainAttrInfoSql = " and pe.fattrinfo='' ";

        /// <summary>
        /// union all SQL串
        /// </summary>
        const string unionAllSql = @" union all ";

        /// <summary>
        /// 价格类型与取价标记位的映射表，价格类型为 Key，取价标记位为 Value
        /// </summary>
        static Dictionary<string, byte> priceTypeFlagMap = new Dictionary<string, byte>
        {
            // 销售价
            { "salPrice", 1 },

            // 经销价（已废弃）
            { "definedPrice", 2 },

            // 采购价格
            { "purPrice", 4 }
        };

        /// <summary>
        /// 检查指定的价格类型是否与指定的取件标记位匹配
        /// </summary>
        /// <param name="typeName">价格类型</param>
        /// <param name="priceFlag">取价标记位</param>
        /// <returns></returns>
        public bool MatchPriceType(string typeName, byte priceFlag)
        {
            return (priceFlag & priceTypeFlagMap[typeName]) > 0;
        }
        public string pricerule { get; set; } = "0";
        /// <summary>
        /// 清除价格缓存（已废弃）
        /// </summary>
        public void ClearPriceCache(UserContext userContext, DynamicObject[] dataEntities, string productIdkey)
        {
            ////处理操作后
            //if (dataEntities == null || dataEntities.Length < 1) return;

            //#region 更新缓存方便取价服务

            ////自动填充企业主体字段值
            //var productIds = dataEntities.SelectMany(x => (x["fentry"] as DynamicObjectCollection).Select(y => Convert.ToString(y[productIdkey]))).Where(x => string.IsNullOrWhiteSpace(x) == false).Distinct().ToList();

            //ClearPriceCache(userContext, productIds);
            //#endregion
        }
        /// <summary>
        /// 新增、更新 定制品零售系数
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="dataEntities"></param>
        public void CreateOrAlterPriceFactor(UserContext userContext, DynamicObject PriceFactorObj, List<string> productids)
        {
            var metaModelService = userContext.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(userContext, "ydj_factorprice");
            if (!productids.Any()) return;

            //var productForm = metaModelService.LoadFormModel(userContext, "ydj_product");
            //var dm = userContext.Container.GetService<IDataManager>();
            //dm.InitDbContext(userContext, productForm.GetDynamicObjectType(userContext));
            //var productObj = dm.Select(productids).OfType<DynamicObject>().ToList();
            var productObj = userContext.LoadBizBillHeadDataById("ydj_product",productids,"fmainorgid");

            //只有总部商品 才能新增或者更新 定制品零售系数
            productids = productObj.Where(o => Convert.ToString(o["fmainorgid"]).EqualsIgnoreCase(userContext.TopCompanyId)).Select(o => Convert.ToString(o["id"])).ToList();
            foreach (var productid in productids)
            {
                var FactorDatas = GetPriceFactor(userContext, productid, "");
                var FactorData = FactorDatas.Where(o => Convert.ToString(o["fproductid"]).EqualsIgnoreCase(productid))?.FirstOrDefault();

                var entrys = PriceFactorObj["fentry"] as DynamicObjectCollection;
                var FactorMx = entrys.Where(o => Convert.ToString(o["fproductid"]).EqualsIgnoreCase(productid))?.FirstOrDefault();
                if (FactorMx.IsNullOrEmptyOrWhiteSpace())
                {
                    //如果 定制品零售系数 没有此商品 则走新增逻辑
                    FactorMx = new DynamicObject(htmlForm.GetEntryEntity("fentry").DynamicObjectType);
                    FactorMx["fproductid"] = productid;
                    entrys.Add(FactorMx);
                }
                if (!FactorData.IsNullOrEmptyOrWhiteSpace())
                {
                    //如果 定制品零售系数 有此商品 则走更新逻辑 
                    FactorMx["fsalprice"] = Convert.ToDecimal(FactorData["fsalprice"]);
                    FactorMx["fpurprice"] = Convert.ToDecimal(FactorData["fpurprice"]);
                    FactorMx["factor"] = Convert.ToDecimal(FactorData["factor"]);
                }
            }
        }


        public List<Dictionary<string, object>> GetPriceFactor(UserContext userContext, string productid, string clientId)
        {
            var sql = $@"/*dialect*/
                                select  fproductid =@fproductid, 
                                fsalprice = (
                                --优先取有效价格，有效价格取不到再去取最近的失效价格
                                select top 1 fsalprice from (
                                --有效期内取得到时：取top 1价格
                                select top 1 t1.fsalprice,'1' as Isable from   t_ydj_price t
                                left join t_ydj_priceentry t1 on t.fid = t1.fid
                                where t.fforbidstatus='0'  --已禁用的筛选掉 
                                and t1.fproductid = @fproductid --销售价目 单据体 商品
                                and datediff(d,t1.fstartdate,@forderdate)>-1 and datediff(d,@forderdate,t1.fexpiredate)>-1 
                                and (t.fmainorgid = @fmainorgid or t.fmainorgid = @ftoporgid )--销售价目 企业标识
                                and t1.fconfirmstatus = 2
                                order by datediff(d,t1.fstartdate,@forderdate)
                                union
                                --有效期内取不到时：取最近【生效日期】内的价格
                                select top 1 t1.fsalprice,'0' as Isable from   t_ydj_price t
                                left join t_ydj_priceentry t1 on t.fid = t1.fid
                                where t.fforbidstatus='0'  --已禁用的筛选掉 
                                and t1.fproductid = @fproductid --销售价目 单据体 商品
                                and (datediff(d,t1.fstartdate,@forderdate) < 0 or datediff(d,@forderdate,t1.fexpiredate) < 0 ) 
                                and (t.fmainorgid = @fmainorgid or t.fmainorgid = @ftoporgid )--销售价目 企业标识
                                and t1.fconfirmstatus = 2
                                order by datediff(d,t1.fstartdate,@forderdate)
                                )
                                 a order by a.Isable desc
                                ),
							   fpurprice = (
                               --优先取有效价格，有效价格取不到再去取最近的失效价格
                                select top 1 fpurprice from (
                                select top 1 t3.fpurprice fpurprice,'1' as Isable from
                                t_ydj_purchaseprice t2 --on t.fproductid = t2.fproductid--采购价目表
                                left join t_ydj_purchasepriceentry t3 on t2.fid = t3.fid
                                where t2.fforbidstatus='0' --已禁用的筛选掉 
                                and t3.fproductid_e = @fproductid --采购价目 单据体 商品
                                and datediff(d,t3.fstartdate_e,@forderdate)>-1 and datediff(d,@forderdate,t3.fexpiredate_e)>-1 
                                and (t2.fmainorgid = @fmainorgid or t2.fmainorgid = @ftoporgid ) --采购价目 企业标识
                                and t3.fconfirmstatus = 2
                                order by datediff(d,t3.fstartdate_e,@forderdate)
                                union
                                select top 1 t3.fpurprice fpurprice,'0' as Isable from
                                t_ydj_purchaseprice t2 --on t.fproductid = t2.fproductid--采购价目表
                                left join t_ydj_purchasepriceentry t3 on t2.fid = t3.fid
                                where t2.fforbidstatus='0' --已禁用的筛选掉 
                                and t3.fproductid_e = @fproductid --采购价目 单据体 商品
                                and (datediff(d,t3.fstartdate_e,@forderdate) < 0 or datediff(d,@forderdate,t3.fexpiredate_e) < 0 ) 
                                and (t2.fmainorgid = @fmainorgid or t2.fmainorgid = @ftoporgid ) --采购价目 企业标识
                                and t3.fconfirmstatus = 2
                                order by datediff(d,t3.fstartdate_e,@forderdate)
                                )
                                a order by a.Isable desc
                                )";
            var sqlParam = new List<SqlParam>
                {
                    new SqlParam("ftoporgid", System.Data.DbType.String, userContext.TopCompanyId),
                    new SqlParam("fmainorgid", System.Data.DbType.String,userContext.Company),
                    new SqlParam("fproductid", System.Data.DbType.String, productid),
                    new SqlParam("forderdate", System.Data.DbType.DateTime, DateTime.Now),
                };
            List<Dictionary<string, object>> datas = new List<Dictionary<string, object>>();
            //统一零售价：取总部组织下《销售价目》已确认且生效日期内的【统一零售价】，如果《销售价目》没有生效日期内的价格，取已确认且最近【生效日期】内的【统一零售价】
            //采购价：取总部组织下《采购价目》已确认且生效日期内的【采购价】，如果《采购价目》没有生效日期内的价格，取已确认且最近【生效日期】内的【采购价】
            //零售系数：取总部组织下【统一零售价】/【采购价】；如果【统一零售价】、【采购价】两个字段的值任意一个为0，或者两个都为0，则【零售系数】为0，且能手工编辑
            var factor = 0M;
            using (var dataReader = userContext.ExecuteReader(sql.ToString(), sqlParam))
            {
                while (dataReader.Read())
                {
                    Dictionary<string, object> data = new Dictionary<string, object>();
                    data["fproductid"] = Convert.ToString(dataReader["fproductid"]);
                    decimal fsalprice = dataReader["fsalprice"] is DBNull ? 0M : Convert.ToDecimal(dataReader["fsalprice"]);
                    decimal fpurprice = dataReader["fpurprice"] is DBNull ? 0M : Convert.ToDecimal(dataReader["fpurprice"]);

                    if (fsalprice > 0M && fpurprice > 0M)
                    {
                        factor = fsalprice / fpurprice;
                    }
                    data["fsalprice"] = fsalprice;
                    data["fpurprice"] = fpurprice;
                    data["factor"] = factor;
                    data["clientId"] = clientId;
                    datas.Add(data);
                }
            }

            return datas;
        }

        /// <summary>
        /// 清除价格缓存（已废弃）
        /// </summary>
        public void ClearPriceCache(UserContext userContext, IEnumerable<string> productIds)
        {
            //#region 更新缓存方便取价服务

            //if (productIds == null || productIds.Any() == false)
            //{
            //    return;
            //}
            //var gateway = userContext.Container.GetService<IHttpServiceInvoker>();
            //gateway.InvokeBillOperation(userContext,
            //                    "ydj_price",
            //                     null,
            //                    "clearpricecache",
            //                    new Dictionary<string, object>
            //                    {
            //                        { "productIds",string.Join(",",productIds)}
            //                    }
            //                 );
            //#endregion
        }

        /// <summary>
        /// 二级分销取价通用方法
        /// </summary>
        /// <param name="pricerule">二级分销合同价格计算规则：'0':'二级分销合同按总部出厂价计算','1':'二级分销合同按总部零售价计算'</param>
        /// <param name="salprice">销售价目商品统一零售价</param>
        /// <param name="purPrice">总部采购价目</param> 
        /// <param name="purOrderEntry">采购订单明细</param>
        /// <param name="currentAgentInfo">二级分销经销商信息</param>
        public decimal CalulateSubmitOnePirce(string pricerule,decimal salprice, decimal purPrice,DynamicObject purOrderEntry,DynamicObjectCollection currentAgentInfo) 
        {
            decimal price = 0M;
            decimal hqprice = 0M;

            if (pricerule.EqualsIgnoreCase("0"))
            {                
                hqprice = purPrice;
                if (!Convert.ToBoolean(purOrderEntry["funstdtype"]) && purOrderEntry["fattrinfo"].IsNullOrEmptyOrWhiteSpace() && salprice==0M)
                {
                    price = purPrice * matchResellRatio(Convert.ToString((purOrderEntry?["fmaterialid_ref"] as DynamicObject)["fseriesid"]), currentAgentInfo);
                }
                else
                {
                    price = salprice;
                }
            }
            else
            {
                //还是以分销价目优先，取不到的话再取采购订单总部零售价为准
                if (salprice > 0)
                {
                    price = salprice;
                }
                else
                {
                    hqprice = Convert.ToDecimal(purOrderEntry?["fhqretailprice"] ?? 0); ;
                }
            }
            //取不到价就以总部采购价*系数
            if (price == 0)
            {
                //0212 之前标准品才算系数，现在改成所有商品都以采购订单上的【总部零售价】* 系数，以此为基准。
                price = hqprice * matchResellRatio(Convert.ToString((purOrderEntry?["fmaterialid_ref"] as DynamicObject)["fseriesid"]), currentAgentInfo);
            }
            return price;
        }

        /// <summary>
        /// 获取一级经销商定义的商品的二级分销价
        /// </summary>
        /// <param name="userCtx">一级经销商企业用户上下文</param>
        /// <param name="productInfos">商品信息</param>
        /// <returns>分销价信息</returns>
        public List<JObject> GetResellerPrice(UserContext userCtx, JArray productInfos)
        {
            var _productInfos = productInfos?.Where(x => !x["productId"].IsNullOrEmptyOrWhiteSpace())?.ToList();
            if (_productInfos == null || _productInfos.Count <= 0) return null;

            // 销售价（一级经销商定义的报价类型为“二级经销报价”的商品销售价）
            byte priceFlag = 1;

            #region SQL语句

            const string tableSql = @"
            select * from 
            (
                select top 1 @fclientid{0} as fclientid,pe.fproductid,pe.funitid,pe.fstartdate,pe.fsalprice 
                from t_ydj_priceentry pe with(nolock) 
                inner join t_ydj_price p with(nolock) on p.fid=pe.fid 
                inner join t_bd_material m with(nolock) on pe.fproductid=m.fid 
                left join t_ydj_customerentry ce with(nolock) on ce.fid=p.fid 
                where m.fmainorgid in(@fmainorgid,@ftoporgid) and p.fmainorgid=@fmainorgid and m.{{0}}=@fproductid{0} 
                --已禁用的筛选掉
                and p.fforbidstatus='0'
                and pe.fconfirmstatus='2' and p.ftype='quote_type_04' and (m.fsalunitid=pe.funitid or pe.funitid='') 
                and (case when pe.fstartdate is null then 1 else case when datediff(d,pe.fstartdate,@fbizdate{0})>-1 then 1 else 0 end end)=1 
                and (case when pe.fexpiredate is null then 1 else case when datediff(d,@fbizdate{0},pe.fexpiredate)>-1 then 1 else 0 end end)=1 
                and 
                (
                    case 
                        when(p.flimit='') then 1 
                        when(p.flimit='limit_01' and ce.fcustomerid=@fcustomerid{0}) then 1 
                        else 0 
                    end
                )=1 
                {1} 
            )t";

            #endregion

            // 所有商品ID
            var productIds = new List<string>();

            var sqls = new List<string>();
            var sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, userCtx.Company),
                new SqlParam("@ftoporgid", DbType.String, userCtx.TopCompanyId)
            };

            for (var i = 0; i < _productInfos.Count; i++)
            {
                var productInfo = _productInfos[i];
                var clientId = Convert.ToString(productInfo["clientId"]);
                var productId = Convert.ToString(productInfo["productId"]);
                var customerId = Convert.ToString(productInfo["customerId"]);

                // 如果没有前端传送业务时间，则业务时间设置为当前时间
                DateTime.TryParse(Convert.ToString(productInfo["bizDate"]), out var bizDate);
                if (bizDate == default(DateTime)) bizDate = DateTime.Now;

                productIds.Add(productId);

                var attrInfoBuilder = new StringBuilder();

                sqlParams.AddRange(new[]
                {
                    new SqlParam(string.Format("@fclientid{0}",i), DbType.String, clientId),
                    new SqlParam(string.Format("@fproductid{0}",i), DbType.String, productId),
                    new SqlParam(string.Format("@fcustomerid{0}",i), DbType.String, customerId),
                    new SqlParam(string.Format("@fbizdate{0}",i), DbType.DateTime, bizDate)
                });

                var attrInfoId = productInfo["attrInfo"].Type == JTokenType.Object ? Convert.ToString(productInfo["attrInfo"]?["id"]) : "";
                var auxPropInfos = new List<KeyValuePair<string, string>>();
                var entities = productInfo["attrInfo"].Type == JTokenType.Object ? productInfo["attrInfo"]?["entities"] as JArray : null;
                if (entities != null && entities.Count > 0)
                {
                    var propValueIds = new List<string>();
                    entities.ForEach(e =>
                    {
                        propValueIds.Add((string)e["valueId"]);
                    });
                    var propValueDatas = userCtx.LoadBizBillHeadDataById("sel_propvalue", propValueIds, "fname,fid,fnumber,fnosuitcreate")
                    ?.OfType<DynamicObject>()
                    ?.ToList();

                    foreach (var entity in entities)
                    {
                        var auxPropId = (string)entity["auxPropId"];
                        var valueId = (string)entity["valueId"];
                        var propValueName = propValueDatas.Where(o => Convert.ToString(o["fid"]).EqualsIgnoreCase(valueId)).Select(o => Convert.ToString(o["fname"]))?.FirstOrDefault();
                        if (!string.IsNullOrEmpty(auxPropId)
                            && !auxPropInfos.Any(x => x.Key == auxPropId))
                        {
                            if (propValueName.IsNullOrEmptyOrWhiteSpace()) continue;
                            auxPropInfos.Add(new KeyValuePair<string, string>(auxPropId, propValueName));
                        }
                    }
                }
                //接口返回的fattrinfo内码 不能直接用需要转成名称组合去匹配价格
                if ((!string.IsNullOrWhiteSpace(attrInfoId)) && (entities.IsNullOrEmptyOrWhiteSpace() || entities?.Count() == 0))
                {
                    //attrinfoBuilder.AppendFormat(" and pe.fattrinfo=@fattrinfo{0}", i);
                    //sqlParams.Add(new SqlParam(string.Format("@fattrinfo{0}", i), DbType.String, attrInfoId));
                    //辅助属性组合值键值对
                    Dictionary<string, string> auxPropKv = null;
                    var setObj = userCtx.LoadBizDataById("bd_auxpropvalueset", attrInfoId);
                    var setEntrys = (setObj?["fentity"] as DynamicObjectCollection)
                        ?.OrderBy(o => Convert.ToInt32(o["fdisplayseq"]))
                        ?.ToList();
                    if (setEntrys != null && setEntrys.Any())
                    {
                        auxPropKv = new Dictionary<string, string>();
                        foreach (var item in setEntrys)
                        {
                            var auxPropId = Convert.ToString(item["fauxpropid"]);
                            var valueId = Convert.ToString(item["fvalueid"]);
                            var valueName = Convert.ToString(item["fvaluename"]);

                            if (!string.IsNullOrEmpty(auxPropId)
                                && !auxPropInfos.Any(x => x.Key == auxPropId))
                            {
                                if (valueName.IsNullOrEmptyOrWhiteSpace()) continue;
                                auxPropInfos.Add(new KeyValuePair<string, string>(auxPropId, valueName));
                            }
                        }
                    }
                }
                if (auxPropInfos.Count <= 0)
                {
                    // 根据空辅助属性匹配价格
                    attrInfoBuilder.Append(noContainAttrInfoSql);
                }
                else
                {
                    // 根据辅助属性组合值匹配价格
                    List<string> auxPropSqlList = new List<string>();
                    foreach (var kv in auxPropInfos)
                    {
                        sqlParams.AddRange(new[]
                        {
                                //商品明细超过 11行 ，且1、11 行的辅助属性个数超过10个时 就能重现，产生两个同参数名的fauxpropid110；
                                //第1组的第10个参数为 @fauxpropid110
                                //第11组的第1个参数为 @fauxpropid110
                                new SqlParam(string.Format("@fauxpropid{0}{1}", i*10, auxPropSqlList.Count), DbType.String, kv.Key),
                                new SqlParam(string.Format("@fvaluename{0}{1}", i*10, auxPropSqlList.Count), DbType.String, kv.Value)
                            });
                        auxPropSqlList.Add(string.Format(containAuxPropSql, i * 10, auxPropSqlList.Count, "fid"));
                    }
                    attrInfoBuilder.AppendFormat(containAttrInfoSql, string.Join(unionAllSql, auxPropSqlList), auxPropInfos.Count);
                }

                var tblSql = string.Format(tableSql, i, attrInfoBuilder);
                sqls.Add(string.Format(tblSql, "fid"));
            }

            var priceList = new JArray();
            var sql = string.Join(unionAllSql, sqls);
            var dbService = userCtx.Container.GetService<IDBService>();
            using (var dataReader = dbService.ExecuteReader(userCtx, sql, sqlParams))
            {
                while (dataReader.Read())
                {
                    priceList.Add(new JObject
                    {
                        { "fclientid",dataReader.GetValueToString("fclientid") },
                        { "fproductid",dataReader.GetValueToString("fproductid") },
                        { "funitid",dataReader.GetValueToString("funitid") },
                        { "fstartdate",dataReader.GetValueToDateTime("fstartdate") },
                        { "fsalprice", dataReader.GetValueToDecimal("fsalprice") }
                    });
                }
            }

            // 商品单位列表
            var productUnits = this.LoadProductUnits(userCtx, productIds);

            // 根据单位换算价格
            this.ConvertProductPrice(_productInfos, priceList, productUnits, priceFlag);

            var resultDatas = priceList
            .GroupBy(x => Convert.ToString(x["fclientid"]))
            .Select(x =>
            {
                // 按生效开始日期降序排序取最新的价格
                var first = x.OrderByDescending(y => (DateTime)y["fstartdate"]).FirstOrDefault();

                return new JObject
                {
                    { "clientId", x.Key },
                    { "fsalprice", first["fsalprice"] }
                };
            })
            .ToList();

            return resultDatas;
        }

        ///// <summary>
        ///// 本地取价
        ///// </summary>
        ///// <param name="userCtx">用户上下文</param>
        ///// <param name="priceFlag">价格标志</param>
        ///// <param name="productInfos">商品信息</param>
        ///// <param name="htmlFormId">表单标识</param>
        ///// <returns></returns>
        //public List<JToken> GetLocalPrice(UserContext userCtx, byte priceFlag, JArray productInfos, OperateOption option = null)
        //{
        //    return GetPrice(userCtx, priceFlag, productInfos, option);
        //}

        /// <summary>
        /// 获取商品的经销价（销售价目表中的经销价）（已废弃）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="productInfos">商品信息</param>
        /// <returns>经销价信息</returns>
        public List<JObject> GetSellPrice(UserContext userCtx, JArray productInfos)
        {
            var _productInfos = productInfos?.Where(x => !x["productId"].IsNullOrEmptyOrWhiteSpace())?.ToList();
            if (_productInfos == null || _productInfos.Count <= 0) return null;

            // 经销价
            byte priceFlag = 2;

            #region SQL语句

            const string tableSql = @"
            select * from 
            (
                select top 1 @fclientid{0} as fclientid,pe.fproductid,pe.funitid,
                pe.fstartdate,pe.fexpiredate,pe.fdefinedprice 
                from t_ydj_priceentry pe with(nolock) 
                inner join t_ydj_price p with(nolock) on p.fid=pe.fid 
                inner join t_bd_material m with(nolock) on pe.fproductid=m.fid 
                left join t_ydj_customerentry ce with(nolock) on ce.fid=p.fid 
                where m.fmainorgid in(@fmainorgid,@ftoporgid) and p.fmainorgid=@fmainorgid and m.{{0}}=@fproductid{0} 
                --已禁用的筛选掉
                and p.fforbidstatus='0'
                and pe.fconfirmstatus='2' and p.ftype='quote_type_03' and pe.funstdtype=@funstdtype{0} 
                and (m.fsalunitid=pe.funitid or pe.funitid='') 
                and (case when pe.fstartdate is null then 1 else case when datediff(d,pe.fstartdate,@fbizdate{0})>-1 then 1 else 0 end end)=1 
                and (case when pe.fexpiredate is null then 1 else case when datediff(d,@fbizdate{0},pe.fexpiredate)>-1 then 1 else 0 end end)=1 
                and 
                (
                    case 
                        when(p.flimit='') then 1 
                        when(p.flimit='limit_01' and ce.fcustomerid=@fcustomerid{0}) then 1 
                        when(p.flimit='limit_02' and ce.fcustype=(select top 1 fcustomertype from t_ydj_customer c with(nolock) where c.fid=@fcustomerid{0} and c.fmainorgid in (@fmainorgid,@ftoporgid))) then 1 
                        else 0 
                    end
                )=1 
                {1} 
            )t";

            #endregion

            // 所有商品ID
            var productIds = new List<string>();

            var sqls = new List<string>();
            var sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, userCtx.Company),
                new SqlParam("@ftoporgid", DbType.String, userCtx.TopCompanyId)
            };

            for (var i = 0; i < _productInfos.Count; i++)
            {
                var productInfo = _productInfos[i];
                var clientId = Convert.ToString(productInfo["clientId"]);
                var productId = Convert.ToString(productInfo["productId"]);
                var unStdType = Convert.ToBoolean(productInfo["unStdType"]);
                var customerId = Convert.ToString(productInfo["customerId"]);

                // 如果没有前端传送业务时间，则业务时间设置为当前时间
                DateTime.TryParse(Convert.ToString(productInfo["bizDate"]), out var bizDate);
                if (bizDate == default(DateTime)) bizDate = DateTime.Now;

                productIds.Add(productId);

                var attrInfoBuilder = new StringBuilder();

                sqlParams.AddRange(new[]
                {
                    new SqlParam(string.Format("@fclientid{0}",i), DbType.String, clientId),
                    new SqlParam(string.Format("@fproductid{0}",i), DbType.String, productId),
                    new SqlParam(string.Format("@funstdtype{0}",i), DbType.String, unStdType ? "1" : "0"),
                    new SqlParam(string.Format("@fcustomerid{0}",i), DbType.String, customerId),
                    new SqlParam(string.Format("@fbizdate{0}",i), DbType.DateTime, bizDate)
                });

                var attrInfoId = productInfo["attrInfo"].Type == JTokenType.Object ? Convert.ToString(productInfo["attrInfo"]?["id"]) : "";
                if (!attrInfoId.IsNullOrEmptyOrWhiteSpace())
                {
                    // 根据辅助属性组合值ID匹配价格
                    attrInfoBuilder.AppendFormat(" and pe.fattrinfo=@fattrinfo{0}", i);
                    sqlParams.Add(new SqlParam(string.Format("@fattrinfo{0}", i), DbType.String, attrInfoId));
                }
                else
                {
                    var auxPropInfos = new List<KeyValuePair<string, string>>();
                    var entities = productInfo["attrInfo"].Type == JTokenType.Object ? productInfo["attrInfo"]?["entities"] as JArray : null;
                    if (entities != null && entities.Count > 0)
                    {
                        foreach (var entity in entities)
                        {
                            var auxPropId = (string)entity["auxPropId"];
                            var valueId = (string)entity["valueId"];
                            if (!auxPropId.IsNullOrEmptyOrWhiteSpace() && !auxPropInfos.Any(x => x.Key == auxPropId))
                            {
                                auxPropInfos.Add(new KeyValuePair<string, string>(auxPropId, valueId));
                            }
                        }
                    }
                    if (auxPropInfos.Count <= 0)
                    {
                        // 根据空辅助属性匹配价格
                        attrInfoBuilder.Append(noContainAttrInfoSql);
                    }
                    else
                    {
                        // 根据辅助属性组合值匹配价格
                        List<string> auxPropSqlList = new List<string>();
                        foreach (var kv in auxPropInfos)
                        {
                            sqlParams.AddRange(new[]
                            {
                                 //商品明细超过 11行 ，且1、11 行的辅助属性个数超过10个时 就能重现，产生两个同参数名的fauxpropid110；
                                //第1组的第10个参数为 @fauxpropid110
                                //第11组的第1个参数为 @fauxpropid110
                                new SqlParam(string.Format("@fauxpropid{0}{1}", i*10, auxPropSqlList.Count), DbType.String, kv.Key),
                                new SqlParam(string.Format("@fvaluename{0}{1}", i*10, auxPropSqlList.Count), DbType.String, kv.Value)
                            });
                            auxPropSqlList.Add(string.Format(containAuxPropSql, i * 10, auxPropSqlList.Count, "fid"));
                        }
                        attrInfoBuilder.AppendFormat(containAttrInfoSql, string.Join(unionAllSql, auxPropSqlList), auxPropInfos.Count);
                    }
                }

                var tblSql = string.Format(tableSql, i, attrInfoBuilder);
                sqls.Add(string.Format(tblSql, "fid"));
            }

            var priceList = new JArray();
            var sql = string.Join(unionAllSql, sqls);
            var dbService = userCtx.Container.GetService<IDBService>();
            using (var dataReader = dbService.ExecuteReader(userCtx, sql, sqlParams))
            {
                while (dataReader.Read())
                {
                    priceList.Add(new JObject
                    {
                        { "fclientid",dataReader.GetValueToString("fclientid") },
                        { "fproductid",dataReader.GetValueToString("fproductid") },
                        { "funitid",dataReader.GetValueToString("funitid") },
                        { "fstartdate",dataReader.GetValueToDateTime("fstartdate") },
                        { "fexpiredate",dataReader.GetValueToDateTime("fexpiredate") },
                        { "fdefinedprice", dataReader.GetValueToDecimal("fdefinedprice") },
                    });
                }
            }

            // 商品单位列表
            var productUnits = this.LoadProductUnits(userCtx, productIds);

            // 根据单位换算价格
            this.ConvertProductPrice(_productInfos, priceList, productUnits, priceFlag);

            var resultDatas = priceList
            .GroupBy(x => Convert.ToString(x["fclientid"]))
            .Select(x =>
            {
                // 按生效日期降序排序取第一个
                var mw = x.OrderByDescending(y => (DateTime)y["fstartdate"]).ToList();
                var first = mw.FirstOrDefault();
                var resultData = new JObject
                {
                    { "success", true },
                    { "count", mw.Count },
                    { "definedPrice", first["fdefinedprice"] },
                    {
                        "matchPrices", new JArray
                        {
                            mw.Select(y => new JObject
                            {
                                { "startDate", y["fstartdate"] },
                                { "expireDate", y["fexpiredate"] },
                                { "definedPrice", y["fdefinedprice"] }
                            })
                        }
                    }
                };

                var clientId = x.Key;
                var productInfo = _productInfos.First(y => (string)y["clientId"] == clientId) as JObject;
                resultData.Add("clientId", clientId);

                return resultData;
            })
            .ToList();

            return resultDatas;
        }

        /// <summary>
        /// 获取《商品促销》的【销售价】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productInfos"></param>
        /// <param name="specialPriceDate"></param>
        /// <returns></returns>
        public List<JObject> GetSpecialPrices(UserContext userCtx, IEnumerable<JToken> productInfos, DateTime specialPriceDate)
        {
            var specialPrices = new List<JObject>();
            if (productInfos == null || !productInfos.Any())
            {
                return specialPrices;
            }

            var productIds = productInfos.Select(s => s.GetJsonValue<string>("productId")).ToList();

            string sql = $@"
select t.fid, t.fnumber, t.fname, t.fcreatedate, t.fstartdate, t.fenddate, te.fmaterialid, te.fattrinfo, te.funitid, te.fprice, te.flowestprice
from t_ydj_prdpromotion t with(nolock) 
inner join t_ydj_prdpromotionproduct te with(nolock) on t.fid=te.fid
where t.fmainorgid=@fmainorgid and t.fforbidstatus='0' and t.fpublishstatus='1' and t.fruletype='ruletype_02' 
and @fdate>=t.fstartdate and @fdate<=t.fenddate
and te.fmaterialid in ({productIds.JoinEx(",", true)})";

            SqlBuilderParameter param = new SqlBuilderParameter(userCtx, "ydj_productpromotion");
            param.AddSelectField("fnumber");
            param.IsDistinct = true;
            param.ReadDirty = true;

            var queryObject = QueryService.BuilQueryObject(param);
            var pkSql = $"select distinct t0.fid {Environment.NewLine} {queryObject.SqlFrom} {queryObject.SqlWhere}";

            sql += $" and t.fid in ({pkSql}) ";

            List<SqlParam> sqlParams = new List<SqlParam>();
            sqlParams.AddRange(param.DynamicParams);
            sqlParams.Add(new SqlParam("@fdate", DbType.DateTime, specialPriceDate));
            if (!sqlParams.Any(s => s.Name.EqualsIgnoreCase("@fmainorgid") || s.Name.EqualsIgnoreCase("fmainorgid")))
            {
                sqlParams.Add(new SqlParam("@fmainorgid", DbType.String, userCtx.Company));
            }

            var promotionPrices = new JArray();
            using (var dataReader = userCtx.ExecuteReader(sql, sqlParams))
            {
                while (dataReader.Read())
                {
                    promotionPrices.Add(new JObject
                    {
                        { "fpromotionid",dataReader.GetValueToString("fid")},
                        { "fproductid",dataReader.GetValueToString("fmaterialid")},
                        { "funitid",dataReader.GetValueToString("funitid")},
                        { "fcreatedate",dataReader.GetValueToDateTime("fcreatedate")},
                        { "fstartdate",dataReader.GetValueToDateTime("fstartdate")},
                        { "fenddate",dataReader.GetValueToDateTime("fenddate")},
                        { "fsalprice",dataReader.GetValueToDecimal("fprice")},
                        { "flowestprice",dataReader.GetValueToDecimal("flowestprice")},
                        { "fattrinfo", dataReader.GetValueToString("fattrinfo").Trim() },
                    });
                }
            }

            var auxPropValueIds = promotionPrices.Select(s => Convert.ToString(s["fattrinfo"]))
                .Where(s => !s.IsNullOrEmptyOrWhiteSpace()).ToList();
            var auxPropValues = userCtx.LoadBizDataById("bd_auxpropvalueset", auxPropValueIds);

            var sortAuxPropValues = new Dictionary<string, string>();
            foreach (var auxPropValue in auxPropValues)
            {
                var entrys = auxPropValue["fentity"] as DynamicObjectCollection;

                string value = entrys.OrderBy(s => Convert.ToString(s["fauxpropid"]))
                    .Select(s => $"{s["fauxpropid"]}:{s["fvalueid"]}").JoinEx(";", false);

                sortAuxPropValues[value] = Convert.ToString(auxPropValue["id"]);
            }

            foreach (var productInfo in productInfos)
            {
                var productId = productInfo.GetJsonValue<string>("productId");
                var entities = productInfo["attrInfo"].Type == JTokenType.Object ? productInfo["attrInfo"]["entities"] as JArray : null;
                var attrInfoId = productInfo["attrInfo"].Type == JTokenType.Object ? (string)productInfo["attrInfo"]["id"] : null;

                var matchPrices = new List<JObject>();

                // 有辅助属性
                if (!attrInfoId.IsNullOrEmptyOrWhiteSpace() || entities != null)
                {
                    if (attrInfoId.IsNullOrEmptyOrWhiteSpace())
                    {
                        string value = entities.OrderBy(s => (string)s["auxPropId"])
                            .Select(s => $"{s["auxPropId"]}:{s["valueId"]}").JoinEx(";", false);
                        sortAuxPropValues.TryGetValue(value, out attrInfoId);
                    }

                    if (!attrInfoId.IsNullOrEmptyOrWhiteSpace())
                    {
                        matchPrices.AddRange(promotionPrices
                            .Where(s =>
                                Convert.ToString(s["fproductid"]).EqualsIgnoreCase(productId)
                                && Convert.ToString(s["fattrinfo"]).EqualsIgnoreCase(attrInfoId))
                            .OrderByDescending(s => Convert.ToDateTime(s["fcreatedate"]))
                            .Select(s => (JObject)s)
                        );
                    }
                }

                matchPrices.AddRange(promotionPrices
                    .Where(s =>
                        Convert.ToString(s["fproductid"]).EqualsIgnoreCase(productId)
                        && Convert.ToString(s["fattrinfo"]).IsNullOrEmptyOrWhiteSpace()
                    )
                    .OrderByDescending(s => Convert.ToDateTime(s["fcreatedate"]))
                    .Select(s => (JObject)s)
                );

                if (matchPrices.Count == 0)
                {
                    continue;
                }

                var specialPrice = new JObject();
                specialPrices.Add(specialPrice);

                var first = matchPrices.First();

                specialPrice["clientId"] = productInfo["clientId"];
                specialPrice["productId"] = productInfo["productId"];

                specialPrice["promotionId"] = first["fpromotionid"];
                specialPrice["promotionRule"] = "商品特价";
                specialPrice["promotionSalPrice"] = first["fsalprice"];
                specialPrice["promotionLowestPrice"] = first["flowestprice"];
            }

            return specialPrices;
        }

        /// <summary>
        /// 获取商品价格
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="priceFlag">取价标记位</param>
        /// <param name="productInfos">取价参数列表，包括商品、辅助属性 等等</param>
        /// <returns>商品价格信息</returns>
        public List<JToken> GetPrice(UserContext userCtx, byte priceFlag, JArray productInfos, OperateOption option = null)
        {
            var srvData = new List<JToken>();
            var _productInfos = productInfos
                ?.Where(o => !o["productId"].IsNullOrEmptyOrWhiteSpace())
                ?.ToList();

            if (_productInfos == null || !_productInfos.Any()) return srvData;

            //先走选配计价再走定价方案 提供给 pc、小程序一起调用。
            this.LoadSelectionPrice(userCtx, _productInfos, srvData, priceFlag, option);

            // 后端实际的取价策略是否是总部取价
            var isFatchHqPrice = true;

            var productInfo = productInfos?.FirstOrDefault();
            //如果前端录入了总部零售价，则以前端录入的总部零售价去算经销定价系数。
            var currentHqprice = Convert.ToDecimal(productInfo?["currentHqprice"] ?? 0M);
            // 销售价
            if (MatchPriceType("salPrice", priceFlag))
            {
                // 是否取总部价格（如果是明确取总部零售价或者当前经销商未启用经销价时，则取总部定义的价目表价格，否则取经销商自己定义的价目表价格）
                var isHqPrice = _productInfos.Any(o => !o["isHqPrice"].IsNullOrEmptyOrWhiteSpace() && Convert.ToBoolean(o["isHqPrice"]));
                var enableSellPrice = userCtx.IsEnableSellPrice();
                isFatchHqPrice = isHqPrice || !enableSellPrice;
                ProductUtil.WritePriceLog(userCtx, $"_productInfos：{JsonConvert.SerializeObject(_productInfos)}；priceFlag：{priceFlag}； true, option：{JsonConvert.SerializeObject(option)}, isFatchHqPrice：{isFatchHqPrice}", 1);
                if (isFatchHqPrice)
                {
                    var priceInfos = this.MatchPrice(userCtx, _productInfos, priceFlag, true, option: option);
                    if (priceInfos != null && priceInfos.Count > 0)
                    {
                        if (srvData.Count == 0 || srvData?.FirstOrDefault().GetJsonValue<bool>("success") == false)
                        {
                            srvData.AddRange(priceInfos);
                        }
                    }
                    if (currentHqprice > 0)
                    {
                        if (srvData == null || srvData.Count == 0)
                        {
                            srvData.Add(new JObject
                            {
                                   { "clientId", Convert.ToString(_productInfos[0]["clientId"]) },
                                   { "success", true },
                                   { "salPrice", currentHqprice }
                            });
                        }
                        else
                        {
                            foreach (var item in srvData)
                            {
                                item["salPrice"] = currentHqprice;
                            }
                        }
                    }
                }
                else
                {
                    this.LoadProductPriceByPriceScheme(userCtx, priceFlag, _productInfos, srvData, option);
                }
                //判断是否二级提交一级做变更的场景
                bool isresellorder = productInfos.Select(o => Convert.ToBoolean(o?["isresellorder"] ?? false)).FirstOrDefault();
                if (isresellorder)
                {
                    foreach (var item in srvData)
                    {
                        if (currentHqprice > 0)
                        {
                            //如果二级提交一级做变更的场景，且修改了总部零售价返回零售价的场景时；给个标识给前端 零售价无需再赋值。
                            item["changeprice_resellorder"] = "1";
                        }
                    }
                }
                ProductUtil.WritePriceLog(userCtx, $"srvData：{JsonConvert.SerializeObject(srvData)}；", 2);
            }

            // 采购价
            else
            {
                var priceInfos = this.MatchPrice(userCtx, _productInfos, priceFlag, false, option: option);
                if (priceInfos != null && priceInfos.Count > 0)
                {
                    srvData.AddRange(priceInfos);
                }
            }

            // 给没有匹配到价格的商品返回一个不成功的标记位
            productInfos.Where(x => !srvData.Any(y => (string)x["clientId"] == (string)y["clientId"])).ForEach(x =>
            {
                srvData.Add(new JObject
                {
                    { "clientId", x["clientId"] },
                    { "success", false }
                });
            });

            // 返回本次的取价策略，便于调用方做后续处理
            foreach (var item in srvData)
            {
                item["isFatchHqPrice"] = isFatchHqPrice;
            }

            //处理非标商品的销售价：如果商品是非标，则销售价直接返回0
            this.ProcNonStandardProductSalPrice(userCtx, priceFlag, productInfos, srvData);

            if (option != null && option.TryGetVariableValue<bool>("fetchSpecialPrice", out var fetchSpecialPrice) &&
                  fetchSpecialPrice)
            {
                option.TryGetVariableValue<DateTime>("specialPriceDate", out var specialPriceDate);
                SetSpecialPrice(userCtx, productInfos, srvData, specialPriceDate);
            }

            if (priceFlag != 4)
            {
                //如果是二级分销提交一级的合同，变更后再去新增行走的取价应该还是取二级的价格
                SetResellOrderPrice(userCtx, productInfos, srvData);
            }

            return srvData;
        }

        private void LoadSelectionPrice(UserContext userCtx, IEnumerable<JToken> productInfos, List<JToken> srvDataList, byte priceFlag, OperateOption option = null)
        {
            var priceService = userCtx.Container.GetService<IPriceService>();
            if (productInfos == null || productInfos.Count() == 0) return;

            var propListKv = this.BuildPropEntityListKv(userCtx, productInfos);
            if (propListKv == null || !propListKv.Any()) return;
            //先将总部价取出来存着 后面如果选配计价算的价格为0 需要从这里取
            var hqPriceInfos = priceService.MatchPrice(userCtx, productInfos.ToList(), 1, true, false);

            var hqPriceInfos_New = priceService.MatchPrice(userCtx, productInfos.ToList(), 1, true, true);

            var _priceInfos = priceService.MatchPrice(userCtx, productInfos.ToList(), 1, false, false);
            if (priceService.MatchPriceType("salPrice", priceFlag))
            {
                // 是否取总部价格（如果是明确取总部零售价或者当前经销商未启用经销价时，则取总部定义的价目表价格，否则取经销商自己定义的价目表价格）
                var isHqPrice = productInfos.Any(o => !o["isHqPrice"].IsNullOrEmptyOrWhiteSpace() && Convert.ToBoolean(o["isHqPrice"]));
                var enableSellPrice = userCtx.IsEnableSellPrice();
                //取零售价（并非总部零售价）且启用了经销价逻辑 
                //if (!isHqPrice)
                //{
                foreach (var productInfo in productInfos)
                {
                    //取不到再去取总部价
                    var priceInfo = _priceInfos?.FirstOrDefault(o =>
                    Convert.ToString(o["clientId"]).EqualsIgnoreCase(Convert.ToString(productInfo["clientId"])));

                    var Hqprice = hqPriceInfos.FirstOrDefault().GetJsonValue<decimal>("salPrice");
                    //即便匹配到了经销价 但是总部零售价还是以总部价走一遍选配计价。    
                    if (isHqPrice)
                    {
                        if (srvDataList.Count == 0 || srvDataList?.FirstOrDefault().GetJsonValue<bool>("success") == false)
                        {
                            srvDataList.AddRange(hqPriceInfos);
                        }
                    }

                    if (priceInfo == null || Convert.ToDecimal(priceInfo?["salPrice"] ?? 0) <= 0)
                    {
                        //如果不勾选【无经销价商品价格默认显示为0】才去取总部价计算选配计价公式 再去走定价方案
                        var profileService = userCtx.Container.GetService<ISystemProfile>();
                        var noDealPrice = !userCtx.IsTopOrg && profileService.GetSystemParameter(userCtx, "bas_storesysparam", "fnodealprice", false);
                        //启用经销价且勾选了【无经销价商品价格默认显示为0】 价格才为0，否则取再取总部价计算选配计价
                        if (!(noDealPrice && enableSellPrice))
                        {
                            if (srvDataList.Count == 0 || srvDataList?.FirstOrDefault().GetJsonValue<bool>("success") == false)
                            {
                                srvDataList.AddRange(hqPriceInfos);
                            }
                        }
                    }
                }
                //}
            }
            //只处理价目表取价成功的数据
            srvDataList = srvDataList.Where(o =>
            {
                //是否不需要执行选配计价公式逻辑
                var notNeed = o.GetJsonValue<bool>("fispresetprop")
                        && !o.GetJsonValue<string>("fattrinfo").IsNullOrEmptyOrWhiteSpace();
                if (notNeed)
                {
                    return false;
                }
                return true;
            }).ToList();
            if (!srvDataList.Any()) return;

            //选配计价服务
            var formulaService = userCtx.Container.GetService<ISelectionPriceService>();

            foreach (var item in srvDataList)
            {
                var clientId = item.GetJsonValue<string>("clientId");
                if (clientId.IsNullOrEmptyOrWhiteSpace()) continue;

                var productInfo = productInfos.FirstOrDefault(o => Convert.ToString(o["clientId"]).EqualsIgnoreCase(clientId));
                var productId = productInfo?.GetJsonValue<string>("productId");
                if (productId.IsNullOrEmptyOrWhiteSpace()) continue;

                var propList = propListKv.GetValue(clientId);
                if (propList == null || !propList.Any()) continue;
                bool isHqPrice = (bool)(productInfo?.GetJsonValue<bool>("isHqPrice"));
                //采购价
                var purPrice = item.GetJsonValue<decimal>("purPrice");
                if (purPrice > 0)
                {
                    purPrice = formulaService.GetProductPrice_New(userCtx, productId, purPrice, propList, isHqPrice, option);
                    item["purPrice"] = purPrice;
                }

                //销售价：特价不走选配计价
                var salPrice = item.GetJsonValue<decimal>("salPrice");
                var promotionId = item.GetJsonValue<string>("promotionId");
                bool fisoutspot = (bool)(productInfo?.GetJsonValue<bool>("fisoutspot"));
                if (salPrice > 0 && promotionId.IsNullOrEmptyOrWhiteSpace())
                {
                    salPrice = formulaService.GetProductPrice_New(userCtx, productId, salPrice, propList, isHqPrice, option);
                    if (salPrice == 0M)
                    {
                        if (fisoutspot)
                        {
                            var Hqprice = hqPriceInfos_New?.FirstOrDefault(o =>
                                            Convert.ToString(o["clientId"]).EqualsIgnoreCase(Convert.ToString(clientId))).GetJsonValue<decimal>("salPrice");
                            //如果走完选配计价后价格后价格为0，需要再走补偿逻辑取价 (其实就是综合价目表hqPriceInfos_New的取值) 
                            if (Hqprice != null && Hqprice > 0)
                            {
                                salPrice = Convert.ToDecimal(Hqprice);
                            }
                        }


                        //后面逻辑无须再取总部价了
                        option.SetVariableValue("NoSelectionPrice", true);
                        if (option != null && option.TryGetVariableValue<string>("PriceMsg", out var PriceMsg) && !PriceMsg.IsNullOrEmptyOrWhiteSpace())
                        {
                            item["PriceMsg"] = PriceMsg;
                        }
                    }
                    item["salPrice"] = salPrice;
                }
            }
        }

        private Dictionary<string, List<PropEntity>> BuildPropEntityListKv(UserContext userCtx, IEnumerable<JToken> productInfos)
        {
            var propListKv = new Dictionary<string, List<PropEntity>>();
            var propIds = new List<string>();
            var propValueIds = new List<string>();

            //解析商品辅助属性信息
            foreach (var productInfo in productInfos)
            {
                var productId = productInfo.GetJsonValue<string>("productId");
                var clientId = productInfo.GetJsonValue<string>("clientId");
                if (productId.IsNullOrEmptyOrWhiteSpace() || clientId.IsNullOrEmptyOrWhiteSpace()) continue;

                var propList = new List<PropEntity>();
                propListKv[clientId] = propList;

                var attrInfo = productInfo["attrInfo"] as JObject;
                var attrInfoId = Convert.ToString(attrInfo?["id"]);
                var attrInfoEntrys = attrInfo?["entities"] as JArray;
                if (attrInfoEntrys == null || !attrInfoEntrys.Any()) continue;

                foreach (var attrInfoEntry in attrInfoEntrys)
                {
                    var propId = Convert.ToString(attrInfoEntry?["auxPropId"]);
                    var propValueId = Convert.ToString(attrInfoEntry?["valueId"]);

                    propIds.Add(propId);
                    propValueIds.Add(propValueId);

                    propList.Add(new PropEntity
                    {
                        PropId = propId,
                        ValueId = propValueId
                    });
                }
            }

            propIds = propIds.Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            propValueIds = propValueIds.Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();

            //批量加载属性和属性值数据包
            List<DynamicObject> propDatas = null;
            List<DynamicObject> propValueDatas = null;
            if (propIds.Any())
            {
                propDatas = userCtx.LoadBizBillHeadDataById("sel_prop", propIds, "fname,fnumber,fdatatype")
                    ?.OfType<DynamicObject>()
                    ?.ToList();
            }
            if (propValueIds.Any())
            {
                propValueDatas = userCtx.LoadBizBillHeadDataById("sel_propvalue", propValueIds, "fname,fnumber,fnosuitcreate")
                    ?.OfType<DynamicObject>()
                    ?.ToList();
            }

            //填充属性信息
            foreach (var item in propListKv)
            {
                var propList = item.Value;
                foreach (var prop in propList)
                {
                    var propData = propDatas?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(prop.PropId));
                    var propValueData = propValueDatas?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(prop.ValueId));
                    if (propData != null)
                    {
                        prop.PropName = Convert.ToString(propData["fname"]);
                        prop.PropNumber = Convert.ToString(propData["fnumber"]);
                        prop.PropValueDataType = (PropValueDataTypeEnum)Convert.ToInt32(propData["fdatatype"]);
                    }
                    if (propValueData != null)
                    {
                        prop.ValueName = Convert.ToString(propValueData["fname"]);
                        prop.ValueNumber = Convert.ToString(propValueData["fnumber"]);
                        prop.IsNosuitCreate = Convert.ToString(propValueData["fnosuitcreate"]) == "1";
                    }
                }
            }

            return propListKv;
        }

        public void SetResellOrderPrice(UserContext userCtx, JArray productInfos, List<JToken> srvData)
        {
            // 是否取总部价格
            var isHqPrice = productInfos.Any(o => !o["isHqPrice"].IsNullOrEmptyOrWhiteSpace() && Convert.ToBoolean(o["isHqPrice"]));
            //0902 调整：二级提交至一级的合同做变更的时候总部零售价取值一级组织的总部零售价
            if (isHqPrice) return;

            bool isresellorder = productInfos.Select(o => Convert.ToBoolean(o?["isresellorder"] ?? false)).FirstOrDefault();
            if (!isresellorder) return;

            var ClientId = srvData.Where(s =>
            {
                var priceObj = (JObject)s;
                var success = priceObj.GetJsonValue<bool>("success");

                return success;
            }).Select(s => (string)s["clientId"]);

            if (!ClientId.Any()) return;

            // 批量加载一级经销商定义的商品二级分销价格
            var productPrices = GetResellerPrice(userCtx, productInfos);
            //批量加载商品总部采购价目
            var hqPurPrices = GetPrice(userCtx, 4, productInfos);
            //取二级采购订单的总部零售价，因为二级提交一级的分销合同变更新增明细没上游，所以零售价需要重新获取
            var metaModelService = userCtx.Container.GetService<IMetaModelService>(); 
            var htmlForm = metaModelService.LoadFormModel(userCtx, "ydj_purchaseorder");
            DynamicObject purOrderEntry = new DynamicObject(htmlForm.GetEntryEntity("fentity").DynamicObjectType); ;
            foreach (var productInfo in productInfos)
            {
                productInfo["isHqPrice"] = true;
            }
            var option = OperateOption.Create();
            //以前端总部零售价传参 获取总部零售价，以此为基准计算零售价
            var hqPriceInfos = GetPrice(userCtx, 1, productInfos, option);
            // var hqPriceInfos = MatchPrice(userCtx, productInfos.ToList(), 1, true, false);
            purOrderEntry["fhqretailprice"] = hqPriceInfos?.FirstOrDefault().GetJsonValue<decimal>("salPrice") ?? 0M;
            
            //所有商品ID
            var productIds = productInfos
                ?.Select(o => Convert.ToString(o["productId"]))
                ?.Distinct()
                ?.ToList();

            DynamicObjectCollection productObjs = userCtx.LoadBizBillHeadDataById("ydj_product", productIds, "fmainorgid,fseriesid");
            var fsourcenumber = productInfos?.Select(o => Convert.ToString(o["fsourcenumber"])).FirstOrDefault();
            var currAgentSql = $@"
                select t1.fisreseller,t1.fisnotmgrinv,t1.fsalemanid,t1.fresellratio,
				t2.fseriesid,t2.fresellratio fresellratio_e,t1.fpricerule
				from t_bas_agent t1 with(nolock)
				left join t_agent_ratioinfo t2  with(nolock) on t1.fid=t2.fid
                left join t_ydj_purchaseorder as pur with(nolock) on pur.fbillno = '{fsourcenumber}'
                where t1.fid=pur.fmainorgid ";

            var currentAgentInfo = userCtx.ExecuteDynamicObject(currAgentSql, null);
            var currentAgent = currentAgentInfo.FirstOrDefault();

            //todo 根据经销商参数【二级分销合同价格计算规则】，如果是二级分销合同按总部出厂价计算,则走原逻辑取取《采购价目》的【统一零售价】；如果是二级分销合同按总部零售价计算,则取《销售价目》的【统一零售价】     
            this.pricerule = Convert.ToString(currentAgent?["fpricerule"] ?? "0");

            var SourceOrdersql = $@"
            select odmx.fhqprice,odmx.fprice,purmx.fmaterialid
            from t_ydj_purchaseorder as pur with(nolock)
            inner join t_ydj_poorderentry as purmx  with(nolock) on pur.fid = purmx.fid
            inner join t_ydj_orderentry as odmx with(nolock) on odmx.fentryid = purmx.fsourceentryid
            where fbillno ='{fsourcenumber}' and fmaterialid in ({productIds.JoinEx(",", true)}) ";
            //获取二级采购订单上级销售合同的总部零售价
            var SoreceOrderObj = userCtx.ExecuteDynamicObject(SourceOrdersql, null);

            foreach (var productInfo in productInfos)
            {
                // 商品统一零售价
                var productPrice = productPrices?.FirstOrDefault(o =>
                    Convert.ToString(o["clientId"]).EqualsIgnoreCase(Convert.ToString(productInfo.GetJsonValue<string>("clientId"))));

                // 总部采购价目
                var hqPurPrice = hqPurPrices?.FirstOrDefault(o =>
                    Convert.ToString(o["clientId"]).EqualsIgnoreCase(Convert.ToString(productInfo.GetJsonValue<string>("clientId"))));

                var funstdtype = Convert.ToBoolean(productInfo["unStdType"]);
                var entities = productInfo["attrInfo"].Type == JTokenType.Object ? productInfo["attrInfo"]["entities"] as JArray : null;
                var attrInfoId = productInfo["attrInfo"].Type == JTokenType.Object ? (string)productInfo["attrInfo"]["id"] : null;
                var productId = (string)productInfo["productId"];
                var productObj = productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(productId));
                purOrderEntry["fmaterialid_ref"] = productObj;

                //生成的一级分销销售合同【标准商品】即（没勾选非标，辅助属性为空）的对应的零售价等于总部采购价目*二级分销档案的【分销价格系数】，其他的暂不处理，按之前逻辑走
                decimal price = 0;
                //if (!funstdtype && (entities == null || entities?.Count() == 0) && productPrice.IsNullOrEmpty())
                //{
                //    price = Convert.ToDecimal(hqPurPrice["purPrice"]) * matchResellRatio(Convert.ToString(productObj["fseriesid"]), currentAgentInfo);
                //}
                //else
                //{
                //    price = Convert.ToDecimal(productPrice?["fsalprice"] ?? 0);
                //}

                price = this.CalulateSubmitOnePirce(this.pricerule, Convert.ToDecimal(productPrice?["fsalprice"] ?? 0), Convert.ToDecimal(hqPurPrice["purPrice"]), purOrderEntry, currentAgentInfo);
                var salData = srvData.FirstOrDefault(s =>
                            s.GetJsonValue<string>("clientId")
                            .EqualsIgnoreCase(productInfo.GetJsonValue<string>("clientId")));
                if (salData == null)
                {
                    continue;
                }
                //如果是总部零售价
                if (isHqPrice)
                {
                    price = SoreceOrderObj.Where(o => Convert.ToString(o["fmaterialid"]).EqualsIgnoreCase(productId)).Select(o => Convert.ToDecimal(o?["fhqprice"] ?? 0M))?.FirstOrDefault() ?? 0M;
                }
                salData["salPrice"] = price;

            }
        }

        /// <summary>
        /// 根据系列及二级分销商信息匹配销售系数
        /// </summary>
        /// <param name="fseriesid">系列id</param>
        /// <param name="currAgentInfo">二级分销商信息</param>
        /// <returns></returns>
        public decimal matchResellRatio(string fseriesid, DynamicObjectCollection currAgentInfo)
        {
            decimal resellRatio = 1;

            if (currAgentInfo == null || !currAgentInfo.Any())
            {
                return resellRatio;
            }
            //拿系列匹配分销价格系数明细配置，匹配到则取对应系数，匹配不到取表头的系数计算
            var matchRatio = currAgentInfo.FirstOrDefault(x => !x["fseriesid"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(x["fseriesid"]) == fseriesid);
            if (matchRatio == null)
            {
                var ratio = Convert.ToDecimal(currAgentInfo.First()["fresellratio"]);
                return ratio <= 0 ? 1 : ratio;
            }
            return Convert.ToDecimal(matchRatio["fresellratio_e"]);
        }

        /// <summary>
        /// 设置促销活动：特价
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productInfos"></param>
        /// <param name="srvData"></param>
        /// <param name="specialPriceDate"></param>
        public void SetSpecialPrice(UserContext userCtx, IEnumerable<JToken> productInfos, List<JToken> srvData, DateTime specialPriceDate)
        {
            // 需要获取特价的clientId
            var matchSpecialPriceClientId = srvData.Where(s =>
                {
                    var priceObj = (JObject)s;

                    var success = priceObj.GetJsonValue<bool>("success");
                    var promotionProp = priceObj.Property("promotionId");

                    return success && promotionProp == null;
                }).Select(s => (string)s["clientId"]);

            if (!matchSpecialPriceClientId.Any()) return;

            // 需要获取特价的商品
            var matchSpecialPriceProductInfos =
                productInfos.Where(s => matchSpecialPriceClientId.Contains((string)s["clientId"]));
            if (!matchSpecialPriceProductInfos.Any()) return;

            if (specialPriceDate == default(DateTime) || specialPriceDate == DateTime.MinValue)
            {
                specialPriceDate = DateTime.Now;
            }

            var specialPrices = GetSpecialPrices(userCtx, productInfos, specialPriceDate);

            foreach (var specialPrice in specialPrices)
            {
                var salData = srvData.FirstOrDefault(s =>
                    s.GetJsonValue<string>("clientId")
                        .EqualsIgnoreCase(specialPrice.GetJsonValue<string>("clientId")));
                if (salData == null)
                {
                    continue;
                }

                // 商品特价
                salData["promotionId"] = specialPrice["promotionId"];
                salData["promotionRule"] = specialPrice["promotionRule"];
                salData["promotionSalPrice"] = specialPrice["promotionSalPrice"];
                salData["promotionLowestPrice"] = specialPrice["promotionLowestPrice"];
            }
        }

        /// <summary>
        /// 处理非标商品的销售价：如果商品是非标，则销售价直接返回0
        /// </summary>
        private void ProcNonStandardProductSalPrice(UserContext userCtx, byte priceFlag, JArray productInfos, List<JToken> srvData)
        {
            //如果不是取销售价，则不用处理
            if (!MatchPriceType("salPrice", priceFlag)) return;

            //如果是库存查询，即便是非标，销售价正常返回。
            var isfrominventory = productInfos.Any(o => !o["isfrominventory"].IsNullOrEmptyOrWhiteSpace() && Convert.ToBoolean(o["isfrominventory"]));
            if (isfrominventory) return;

            var _productInfos = productInfos.Where(o => !Convert.ToString(o["productId"]).IsNullOrEmptyOrWhiteSpace()).ToList();

            var productIds = _productInfos
                .Select(o => Convert.ToString(o["productId"]))
                .Distinct()
                .ToList();
            if (!productIds.Any()) return;

            //非标商品
            var productObjs = userCtx.LoadBizBillHeadDataById("ydj_product", productIds, "funstdtype")
                ?.Where(o => Convert.ToString(o["funstdtype"]) == "1")
                ?.ToList();
            if (productObjs == null || !productObjs.Any()) return;

            foreach (var item in srvData)
            {
                if (!Convert.ToBoolean(item["success"])) continue;

                var clientId = Convert.ToString(item["clientId"]);
                var productId = Convert.ToString(_productInfos.FirstOrDefault(o => Convert.ToString(o["clientId"]).EqualsIgnoreCase(clientId))?["productId"]);
                if (productId.IsNullOrEmptyOrWhiteSpace()) continue;

                //如果当前取价商品是非标商品，则销售价直接为0
                var productObj = productObjs.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(productId));
                if (productObj != null)
                {
                    item["salPrice"] = 0;
                }
            }
        }

        private void SetOutPrice(JObject data, byte priceFlag)
        {
            if (data == null)
            {
                return;
            }

            DeletePrice(data, priceFlag);

            var matchPrices = data["matchPrices"] as JArray;

            if (matchPrices == null || matchPrices.Count <= 0)
            {
                return;
            }

            foreach (var matchPrice in matchPrices)
            {
                DeletePrice((JObject)matchPrice, priceFlag);
            }
        }

        private void DeletePrice(JObject data, byte priceFlag)
        {
            foreach (var kv in priceTypeFlagMap)
            {
                if ((priceFlag & kv.Value) == 0)
                {
                    var prop = data.Property(kv.Key);
                    if (prop != null)
                    {
                        data.Remove(kv.Key);
                    }
                }
            }
        }

        /// <summary>
        /// 匹配价目表中的价格
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="productInfos">取价参数列表，包括商品、辅助属性 等等</param>
        /// <param name="priceFlag">取价标记位</param>
        /// <param name="isFatchHqPrice">是否获取总部价格</param>
        /// <param name="isFatchAuxProp">是否获取（价目表的辅助属性、商品的允许选配）字段</param>
        /// <returns>商品价格信息</returns>
        public List<JObject> MatchPrice(UserContext userCtx, List<JToken> productInfos, byte priceFlag, bool isFatchHqPrice, bool isFatchAuxProp = false, OperateOption option = null)
        {
            if (productInfos == null || !productInfos.Any()) return new List<JObject>();

            JArray ls = new JArray();
            List<JObject> allPrices = new List<JObject>();

            //所有商品ID
            var productIds = productInfos
                ?.Select(o => Convert.ToString(o["productId"]))
                ?.Distinct()
                ?.ToList();

            //商品单位列表
            var productUnits = this.LoadProductUnits(userCtx, productIds);
            if (productInfos.Count == 0)
            {
                return allPrices;
            }

            #region sql语句

            // 取销售价目表
            var tableSql = @"
            select * from 
            (
                select top 1 @fclientid{0} as fclientid,pe.fproductid,pe.funitid,
                pe.fstartdate,0 as fpurprice,pe.fsalprice " + (isFatchAuxProp ? ",pe.fattrinfo,m.fispresetprop" : "") + @" 
                --优先取可用的价格，如果价格都不在有效期内则取最新的一条价目。60026
                ,case when (pe.fstartdate is null or pe.fexpiredate is null) then 1 else case when (datediff(d,pe.fstartdate,@fbizdate0)>-1 and datediff(d,@fbizdate0,pe.fexpiredate)>-1) then 1 else 0 end end 'enable'
                from t_ydj_priceentry pe with(nolock)
                inner join t_ydj_price p with(nolock) on p.fid=pe.fid
                inner join t_bd_material m with(nolock) on pe.fproductid=m.fid {2}
                left join t_ydj_customerentry ce with(nolock) on ce.fid=p.fid
                where m.fmainorgid in (@fmainorgid,@ftoporgid" + (userCtx.IsSecondOrg ? ",@foneorgid" : string.Empty) + @")
                and p.fforbidstatus='0'
                and m.{{0}}=@fproductid{0} and pe.fconfirmstatus='2' {3}
                and (m.fsalunitid=pe.funitid or pe.funitid='') 
                --and (case when pe.fstartdate is null then 1 else case when datediff(d,pe.fstartdate,@fbizdate{0})>-1 then 1 else 0 end end)=1 
                --and (case when pe.fexpiredate is null then 1 else case when datediff(d,@fbizdate{0},pe.fexpiredate)>-1 then 1 else 0 end end)=1 
                and 
                (
                    case 
                        when(p.flimit='') then 1 
                        when(p.flimit='limit_01' and ce.fcustomerid=@fcustomerid{0}) then 1 
                        else 0 
                    end
                )=1
                {1}
                order by enable desc,pe.fstartdate desc
            )t";

            // 取采购价目表
            if (MatchPriceType("purPrice", priceFlag))
            {
                //是否直接取最新采购价，忽略供应商取最新总部价目
                bool supplierNotFilter = productInfos != null && productInfos.Any(x => !x["supplierNotFilter"].IsNullOrEmptyOrWhiteSpace() && Convert.ToBoolean(x["supplierNotFilter"]));
                //分销商是否取总部采购价（部分分销系数业务场景需要）
                bool isReHqPurPrice = productInfos != null && productInfos.Any(x => !x["isReHqPurPrice"].IsNullOrEmptyOrWhiteSpace() && Convert.ToBoolean(x["isReHqPurPrice"]));
                if (!isReHqPurPrice && !userCtx.IsSecondOrg)
                {
                    isReHqPurPrice = true;
                }
                tableSql = @"
                select * from 
                (
                    select top 1 @fclientid{0} as fclientid,pe.fproductid_e fproductid,pe.funitid_e funitid,
                    pe.fstartdate_e as fstartdate,pe.fpurprice,0 as fsalprice " + (isFatchAuxProp ? ",pe.fattrinfo,m.fispresetprop" : "") + @" 
                    from t_ydj_purchasepriceentry pe with(nolock)
                    inner join t_ydj_purchaseprice p with(nolock) on p.fid=pe.fid 
                    inner join t_bd_material m with(nolock) on pe.fproductid_e=m.fid 
                    where " + (isReHqPurPrice ? "p.fmainorgid in(@fmainorgid,@ftoporgid)" : "p.fmainorgid=@fmainorgid") +
                    @" and m.fmainorgid in (@fmainorgid,@ftoporgid" + (userCtx.IsSecondOrg ? ",@foneorgid" : string.Empty) + @")
                    and p.fforbidstatus='0' and m.fispresetprop='0' and m.funstdtype='0' --定制和非标，不允许取到价格
                    and m.{{0}}=@fproductid{0} and pe.fconfirmstatus='2' " +
                    (supplierNotFilter ? "" : "and (p.fsupplierid=@fsupplierid{0} or p.fsupplierid='') ") + @"
                    and (m.fpurunitid=pe.funitid_e or pe.funitid_e='') 
                    and (case when pe.fstartdate_e is null then 1 else case when datediff(d,pe.fstartdate_e,@fbizdate{0})>-1 then 1 else 0 end end)=1 
                    and (case when pe.fexpiredate_e is null then 1 else case when datediff(d,@fbizdate{0},pe.fexpiredate_e)>-1 then 1 else 0 end end)=1 
                    {1}
                    order by pe.fstartdate_e desc
                )t";
            }

            #endregion

            // 只有销售价目表才需要加载商品的企业ID
            DynamicObjectCollection productObjs = null;
            if (productIds != null && productIds.Any() && !MatchPriceType("purPrice", priceFlag))
            {
                productObjs = userCtx.LoadBizBillHeadDataById("ydj_product", productIds, "fmainorgid");
            }

            var sqls = new List<string>();
            var sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, userCtx.Company),
                new SqlParam("@ftoporgid", DbType.String, userCtx.TopCompanyId)
            };
            if (userCtx.IsSecondOrg)
            {
                sqlParams.Add(new SqlParam("@foneorgid", DbType.String, userCtx.ParentCompanyId));
            }

            for (var i = 0; i < productInfos.Count; i++)
            {
                var productInfo = productInfos[i];
                var productId = (string)productInfo["productId"];
                var client = ((string)productInfo["clientId"]) ?? string.Empty;
                var supplierId = ((string)productInfo["supplierId"]) ?? string.Empty;
                var customerId = ((string)productInfo["customerId"]) ?? string.Empty;
                var bizDate = string.IsNullOrWhiteSpace((string)productInfo["bizDate"]) ? DateTime.Now : Convert.ToDateTime((string)productInfo["bizDate"]);
                var auxPropInfos = new List<KeyValuePair<string, string>>();
                var entities = productInfo["attrInfo"].Type == JTokenType.Object ? productInfo["attrInfo"]["entities"] as JArray : null;
                var attrInfoId = productInfo["attrInfo"].Type == JTokenType.Object ? (string)productInfo["attrInfo"]["id"] : null;
                var attrinfoBuilder = new StringBuilder();

                // 只有总部商品才需要按照 isFatchHqPrice 参数来动态取销售价，如果是自建商品则保留原来的取价条件
                var productObj = productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(productId));
                var productOrgId = Convert.ToString(productObj?["fmainorgid"]);
                var isTopProduct = !productOrgId.IsNullOrEmptyOrWhiteSpace() && productOrgId.EqualsIgnoreCase(userCtx.TopCompanyId);
                var priceOrgWhere = " and p.fmainorgid=m.fmainorgid";
                var typeWhere = " and p.ftype='quote_type_01'";
                if (isTopProduct)
                {
                    priceOrgWhere = " and p.fmainorgid='" + (isFatchHqPrice ? userCtx.TopCompanyId : userCtx.Company) + "'";
                    typeWhere = " and p.ftype='" + (isFatchHqPrice ? "quote_type_01" : "quote_type_03") + "'";
                }

                //isFatchHqPrice传false表示走经销商自己的经销价，取销售价目 的时候才注释 不加条件日期条件，取经销价或者二级分销价的时候还是要加上条件。
                if (!isFatchHqPrice)
                {
                    typeWhere += $@" and (case when pe.fstartdate is null then 1 else case when datediff(d,pe.fstartdate,'{bizDate}')>-1 then 1 else 0 end end)=1 
                                and (case when pe.fexpiredate is null then 1 else case when datediff(d,'{bizDate}',pe.fexpiredate)>-1 then 1 else 0 end end)=1 ";
                }

                if (entities != null && entities.Count > 0)
                {
                    var propValueIds = new List<string>();
                    entities.ForEach(e =>
                    {
                        propValueIds.Add((string)e["valueId"]);
                    });
                    var propValueDatas = userCtx.LoadBizBillHeadDataById("sel_propvalue", propValueIds, "fname,fid,fnumber,fnosuitcreate")
                    ?.OfType<DynamicObject>()
                    ?.ToList();

                    foreach (var entity in entities)
                    {
                        var auxPropId = (string)entity["auxPropId"];
                        var valueId = (string)entity["valueId"];
                        var propValueName = propValueDatas.Where(o => Convert.ToString(o["fid"]).EqualsIgnoreCase(valueId)).Select(o => Convert.ToString(o["fname"]))?.FirstOrDefault();
                        if (!string.IsNullOrEmpty(auxPropId)
                            && !auxPropInfos.Any(x => x.Key == auxPropId))
                        {
                            if (propValueName.IsNullOrEmptyOrWhiteSpace()) continue;
                            auxPropInfos.Add(new KeyValuePair<string, string>(auxPropId, propValueName));
                        }
                    }
                }

                //如果没有前端传送业务时间，则业务时间设置为当前时间
                if (bizDate == default(DateTime))
                {
                    bizDate = DateTime.Now;
                }

                sqlParams.AddRange(new[]
                {
                    new SqlParam(string.Format("@fproductid{0}",i),DbType.String,productId),
                    new SqlParam(string.Format("@fclientid{0}",i),DbType.String,client),
                    new SqlParam(string.Format("@fbizdate{0}",i), DbType.DateTime, bizDate)
                });

                if (MatchPriceType("salPrice", priceFlag))
                {
                    sqlParams.Add(new SqlParam(string.Format("@fcustomerid{0}", i), DbType.String, customerId));
                }
                if (MatchPriceType("purPrice", priceFlag))
                {
                    sqlParams.Add(new SqlParam(string.Format("@fsupplierid{0}", i), DbType.String, supplierId));
                }
                //接口返回的fattrinfo内码 不能直接用需要转成名称组合去匹配价格
                if ((!string.IsNullOrWhiteSpace(attrInfoId)) && (entities.IsNullOrEmptyOrWhiteSpace() || entities?.Count() == 0))
                {
                    //attrinfoBuilder.AppendFormat(" and pe.fattrinfo=@fattrinfo{0}", i);
                    //sqlParams.Add(new SqlParam(string.Format("@fattrinfo{0}", i), DbType.String, attrInfoId));
                    //辅助属性组合值键值对
                    Dictionary<string, string> auxPropKv = null;
                    var setObj = userCtx.LoadBizDataById("bd_auxpropvalueset", attrInfoId);
                    var setEntrys = (setObj?["fentity"] as DynamicObjectCollection)
                        ?.OrderBy(o => Convert.ToInt32(o["fdisplayseq"]))
                        ?.ToList();
                    if (setEntrys != null && setEntrys.Any())
                    {
                        auxPropKv = new Dictionary<string, string>();
                        foreach (var item in setEntrys)
                        {
                            var auxPropId = Convert.ToString(item["fauxpropid"]);
                            var valueId = Convert.ToString(item["fvalueid"]);
                            var valueName = Convert.ToString(item["fvaluename"]);

                            if (!string.IsNullOrEmpty(auxPropId)
                                && !auxPropInfos.Any(x => x.Key == auxPropId))
                            {
                                if (valueName.IsNullOrEmptyOrWhiteSpace()) continue;
                                auxPropInfos.Add(new KeyValuePair<string, string>(auxPropId, valueName));
                            }
                        }
                    }
                }

                //都通过辅助属性组合名称的形式
                if (auxPropInfos.Count <= 0)
                {
                    attrinfoBuilder.Append(noContainAttrInfoSql);
                }
                else
                {
                    List<string> auxPropSqlList = new List<string>();
                    foreach (var kv in auxPropInfos)
                    {
                        sqlParams.AddRange(new[]
                        {
                            //商品明细超过 11行 ，且1、11 行的辅助属性个数超过10个时 就能重现，产生两个同参数名的fauxpropid110；
                            //第1组的第10个参数为 @fauxpropid110
                            //第11组的第1个参数为 @fauxpropid110
                            new SqlParam(string.Format("@fauxpropid{0}{1}",i*10,auxPropSqlList.Count),DbType.String,kv.Key),
                            new SqlParam(string.Format("@fvaluename{0}{1}",i*10,auxPropSqlList.Count),DbType.String,kv.Value)
                        });
                        auxPropSqlList.Add(string.Format(containAuxPropSql, i * 10, auxPropSqlList.Count, "fid"));
                    }
                    attrinfoBuilder.AppendFormat(containAttrInfoSql, string.Join(unionAllSql, auxPropSqlList), auxPropInfos.Count);
                }


                var tblSql = "";
                if (MatchPriceType("purPrice", priceFlag))
                {
                    tblSql = string.Format(tableSql, i, attrinfoBuilder.ToString());
                }
                else
                {
                    tblSql = string.Format(tableSql, i, attrinfoBuilder.ToString(), priceOrgWhere, typeWhere);
                }
                sqls.Add(string.Format(tblSql, "fid"));
            }

            if (sqls.Any())
            {

                var sql = string.Join(unionAllSql, sqls);
                IDBService dbService = userCtx.Container.GetService<IDBService>();
                using (var dataReader = dbService.ExecuteReader(userCtx, sql, sqlParams))
                {
                    while (dataReader.Read())
                    {
                        ls.Add(new JObject
                    {
                        { "fclientid",dataReader.GetValueToString("fclientid")},
                        { "fproductid",dataReader.GetValueToString("fproductid")},
                        { "funitid",dataReader.GetValueToString("funitid")},
                        { "fstartdate",dataReader.GetValueToDateTime("fstartdate")},
                        { "fpurprice",dataReader.GetValueToDecimal("fpurprice")},
                        { "fsalprice",dataReader.GetValueToDecimal("fsalprice")},
                        { "fattrinfo", isFatchAuxProp ? dataReader.GetValueToString("fattrinfo").Trim() : "" },
                        { "fispresetprop", isFatchAuxProp ? dataReader.GetValueToString("fispresetprop") == "1" : false },
                        { "fdefinedprice",0 }
                    });
                    }
                }

                //根据单位换算价格
                this.ConvertProductPrice(productInfos, ls, productUnits, priceFlag);
            }

            var prices = ls
                .GroupBy(x => Convert.ToString(x["fclientid"]))
                .Select(x =>
                {
                    // 按生效日期降序排序取第一个
                    var mw = x.OrderByDescending(y => (DateTime)y["fstartdate"]).ToList();
                    var first = mw.FirstOrDefault();

                    var resultData = new JObject
                    {
                        { "success", true },
                        { "count", mw.Count },
                        { "productId", first["fproductid"] },
                        { "purPrice", first["fpurprice"] },
                        { "salPrice", first["fsalprice"] },
                        { "definedPrice", first["fdefinedprice"] },
                        {
                            "matchPrices", new JArray
                            {
                                mw.Select(y => new JObject
                                {
                                    { "startDate", y["fstartdate"] },
                                    { "purPrice", y["fpurprice"] },
                                    { "salPrice", y["fsalprice"] },
                                    { "definedPrice", y["fdefinedprice"] }
                                })
                            }
                        }
                    };

                    if (isFatchAuxProp)
                    {
                        resultData["fattrinfo"] = first["fattrinfo"];
                        resultData["fispresetprop"] = first["fispresetprop"];
                    }

                    resultData.Add("clientId", x.Key);

                    SetOutPrice(resultData, priceFlag);

                    return resultData;

                }).ToList();

            //取总部零售价时，如果取不到价格 再去如果《综合价目表》中【总部零售价】字段值为0，
            //则根据《销售合同》商品行的【商品编码】匹配总部视角下《定制品零售系数》带出【零售系数】，通过《综合价目表》的【采购单价（折前）】*《定制品零售系数》的【零售系数】=《销售合同》的【总部零售价】
            if (isFatchHqPrice && isFatchAuxProp)
            {
                GetHqpriceByPricesynthesize(productInfos, prices, userCtx);
            }

            allPrices.AddRange(prices);

            return allPrices;
        }

        /// <summary>
        /// 综合价目表 取值 
        /// </summary>
        /// <param name="productInfos"></param>
        /// <param name="ls"></param>
        /// <param name="userCtx"></param>
        //根据商品行的【商品编码】+【辅助属性】+【定制说明】匹配当前组织下的《综合价目表》，带出《综合价目表》中的【统一零售价】作为商品行的【总部零售价】 
        public void PricesynThe(List<JToken> productInfos, List<JObject> ls, UserContext userCtx)
        {
            var sqls = new List<string>();
            var sqlParams = new List<SqlParam>
                {
                    new SqlParam("@fmainorgid", DbType.String, userCtx.Company),
                    new SqlParam("@ftoporgid", DbType.String, userCtx.TopCompanyId)
                };
            // 取综合价目表
            var tableSql = @"
            select * from 
            (
                select top 1 @fclientid{0} as fclientid,pe.fmaterialid as fproductid,pe.funifysaleprice,pe.fpurfacprice,pe.fattrinfo,m.fispresetprop
                from t_ydj_pricesynthesize as pe with(nolock)  
                inner join t_bd_material m with(nolock) on pe.fmaterialid=m.fid 
                where  m.{{0}}=@fproductid{0} and pe.fmainorgid = @fmainorgid
                {1} 
            )t";

            var containAttrInfoSql = @" 
            and 
            (
	            case  
		            when 
                    (
			            (select count(1) from ({0})t)={1} 
			            and
			            (
				            select count(1) from t_bd_auxpropvalue av with(nolock)
				            inner join t_bd_auxpropvalueentry ave with(nolock) on av.fid=ave.fid
				            where av.fid=pe.fattrinfo 
			            )={1}
		            ) then 1 
		            else 0 
	            end 
            )=1
            ";
            for (var i = 0; i < productInfos.Count; i++)
            {
                var productInfo = productInfos[i];
                var productId = (string)productInfo["productId"];
                var client = ((string)productInfo["clientId"]) ?? string.Empty;
                var auxPropInfos = new List<KeyValuePair<string, string>>();
                var entities = productInfo["attrInfo"].Type == JTokenType.Object ? productInfo["attrInfo"]["entities"] as JArray : null;
                var attrInfoId = productInfo["attrInfo"].Type == JTokenType.Object ? (string)productInfo["attrInfo"]["id"] : null;
                var attrinfoBuilder = new StringBuilder();

                if (entities != null && entities.Count > 0)
                {
                    var propValueIds = new List<string>();
                    entities.ForEach(e =>
                    {
                        propValueIds.Add((string)e["valueId"]);
                    });
                    var propValueDatas = userCtx.LoadBizBillHeadDataById("sel_propvalue", propValueIds, "fname,fid,fnumber,fnosuitcreate")
                    ?.OfType<DynamicObject>()
                    ?.ToList();

                    foreach (var entity in entities)
                    {
                        var auxPropId = (string)entity["auxPropId"];
                        var valueId = (string)entity["valueId"];
                        var propValueName = propValueDatas.Where(o => Convert.ToString(o["fid"]).EqualsIgnoreCase(valueId)).Select(o => Convert.ToString(o["fname"]))?.FirstOrDefault();
                        if (!string.IsNullOrEmpty(auxPropId)
                            && !auxPropInfos.Any(x => x.Key == auxPropId))
                        {
                            if (propValueName.IsNullOrEmptyOrWhiteSpace()) continue;
                            auxPropInfos.Add(new KeyValuePair<string, string>(auxPropId, propValueName));
                        }
                    }
                }

                sqlParams.AddRange(new[]
                {
                    new SqlParam(string.Format("@fproductid{0}",i),DbType.String,productId),
                    new SqlParam(string.Format("@fclientid{0}",i),DbType.String,client)
                });

                if (!string.IsNullOrWhiteSpace(attrInfoId))
                {
                    attrinfoBuilder.AppendFormat(" and pe.fattrinfo=@fattrinfo{0}", i);
                    sqlParams.Add(new SqlParam(string.Format("@fattrinfo{0}", i), DbType.String, attrInfoId));
                }
                else
                {
                    if (auxPropInfos.Count <= 0)
                    {
                        attrinfoBuilder.Append(noContainAttrInfoSql);
                    }
                    else
                    {
                        List<string> auxPropSqlList = new List<string>();
                        foreach (var kv in auxPropInfos)
                        {
                            sqlParams.AddRange(new[]
                            {
                                new SqlParam(string.Format("@fauxpropid{0}{1}",i,auxPropSqlList.Count),DbType.String,kv.Key),
                                new SqlParam(string.Format("@fvaluename{0}{1}",i,auxPropSqlList.Count),DbType.String,kv.Value)
                            });
                            auxPropSqlList.Add(string.Format(containAuxPropSql, i, auxPropSqlList.Count, "fid"));
                        }
                        attrinfoBuilder.AppendFormat(containAttrInfoSql, string.Join(unionAllSql, auxPropSqlList), auxPropInfos.Count);
                    }
                }
                var tblSql = string.Format(tableSql, i, attrinfoBuilder.ToString());
                sqls.Add(string.Format(tblSql, "fid"));
            }
            if (sqls.Any())
            {
                var sql = string.Join(unionAllSql, sqls);
                IDBService dbService = userCtx.Container.GetService<IDBService>();
                using (var dataReader = dbService.ExecuteReader(userCtx, sql, sqlParams))
                {
                    while (dataReader.Read())
                    {
                        decimal funifysaleprice = dataReader["funifysaleprice"] is DBNull ? 0M : Convert.ToDecimal(dataReader["funifysaleprice"]);
                        decimal fpurfacprice = dataReader["fpurfacprice"] is DBNull ? 0M : Convert.ToDecimal(dataReader["fpurfacprice"]);
                        var fclientid = Convert.ToString(dataReader["fclientid"]);
                        ls.ForEach(o =>
                        {
                            if (Convert.ToString(o["clientId"]).EqualsIgnoreCase(fclientid)) 
                            {
                                o["funifysaleprice"] = funifysaleprice;
                                o["fpurfacprice"] = fpurfacprice;
                                o["fattrinfo"] = dataReader.GetValueToString("fattrinfo").Trim();
                            }   
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 取总部零售价时，如果取不到价格 再去从《综合价目表》中获取价格
        /// 根据《销售合同》商品行的【商品编码】匹配总部视角下《定制品零售系数》带出【零售系数】，通过《综合价目表》的【采购单价（折前）】*《定制品零售系数》的【零售系数】=《销售合同》的【总部零售价】
        /// </summary>
        /// <param name="ls"></param>
        private void GetHqpriceByPricesynthesize(List<JToken> productInfos, List<JObject> ls, UserContext userCtx)
        {
            //先 根据商品行的【商品编码】+【辅助属性】+【定制说明】匹配当前组织下的《综合价目表》，带出《综合价目表》中的【统一零售价】作为商品行的【总部零售价】 
            this.PricesynThe(productInfos, ls, userCtx);

            //获取综合价目表统一零售价小于等于0 的商品
            var NoHqPriceProids = ls
                .Where(o => Convert.ToDecimal(o["funifysaleprice"] ?? 0) <= 0M).ToList();
            //.Select(o => Convert.ToString(o?["productId"] ?? "")).ToList();

            var productids = NoHqPriceProids.Select(o => Convert.ToString(o?["productId"] ?? "")).ToList();
            var attrinfoLst = NoHqPriceProids.Select(o => Convert.ToString(o?["fattrinfo"] ?? "")).ToList();
            ////if (!NoHqPriceProids.Any()) return;

            //再从综合价目表中取【采购单价（折前）】 
            var sql = $@"select top 1 actormx.factor,actormx.fproductid,pricesyn.fpurfacprice from t_ydj_factorprice as actor with (nolock)
                            inner join t_ydj_factorpriceentry as actormx with (nolock) on actor.fid = actormx.fid 
                            inner join t_ydj_pricesynthesize as pricesyn with(nolock) on pricesyn.fmaterialid = actormx.fproductid 
                            where actormx.fproductid in ({productids.JoinEx(",", true)}) and pricesyn.fattrinfo in ({attrinfoLst.JoinEx(",", true)}) 
                            and pricesyn.fmainorgid ='{userCtx.Company}'";

            JArray datas = new JArray();
            using (var dataReader = userCtx.ExecuteReader(sql.ToString(), null))
            {
                while (dataReader.Read())
                {
                    JObject data = new JObject();
                    data["fproductid"] = Convert.ToString(dataReader["fproductid"]);
                    //前面已经按辅助属性匹配查了采购折前
                    decimal fpurfacprice = ls.Where(o => Convert.ToString(o["productId"]).EqualsIgnoreCase(Convert.ToString(dataReader["fproductid"])))
                                        .Select(o => Convert.ToDecimal(o?["fpurfacprice"] ?? 0)).FirstOrDefault();
                    decimal factor = dataReader["factor"] is DBNull ? 0M : Convert.ToDecimal(dataReader["factor"]);
                    //decimal fpurfacprice = dataReader["fpurfacprice"] is DBNull ? 0M : Convert.ToDecimal(dataReader["fpurfacprice"]);
                    decimal fhqprice_new = factor * fpurfacprice;

                    data["factor"] = factor;
                    data["fpurfacprice"] = fpurfacprice;
                    data["fhqprice_new"] = fhqprice_new;
                    datas.Add(data);
                }
            }

            ls.ForEach(o =>
            {
                var clientId = Convert.ToString(o["clientId"]);
                var productInfo = productInfos.FirstOrDefault(x => Convert.ToString(x["clientId"]).EqualsIgnoreCase(clientId));
                bool fisoutspot = (bool)(productInfo?.GetJsonValue<bool>("fisoutspot"));

                //出现货 才走新逻辑
                if (fisoutspot)
                {
                    var salPrice = Convert.ToDecimal(o["salPrice"]);
                    //统一零售价能取到则不需要往下走。
                    //if (salPrice > 0M) return;
                    //如果综合价目表 统一零售价 为0 则取综合价目表中的 【零售系数】* 【采购单价（折前）】 的新总部零售价
                    if (Convert.ToDecimal(o["funifysaleprice"] ?? 0) <= 0M)
                    {
                        var fproductid = Convert.ToString(o?["productId"]);
                        var fhqprice_new = datas.Where(z => Convert.ToString(z["fproductid"]).EqualsIgnoreCase(fproductid))
                                                .Select(z => Convert.ToDecimal(z?["fhqprice_new"]))?.FirstOrDefault();

                        o["salPrice"] = fhqprice_new;

                        JArray matchPrices = o?["matchPrices"] as JArray;
                        foreach (JObject x in matchPrices)
                        {
                            x["salPrice"] = fhqprice_new;
                        }

                    }
                    //否则直接取综合价目表的价格
                    else
                    {
                        o["salPrice"] = Convert.ToDecimal(o["funifysaleprice"] ?? 0);
                        JArray matchPrices = o?["matchPrices"] as JArray;
                        foreach (JObject x in matchPrices)
                        {
                            x["salPrice"] = Convert.ToDecimal(o["funifysaleprice"] ?? 0);
                        }
                    }
                }
            });
        }

        /// <summary>
        /// 根据单位换算价格
        /// </summary>
        /// <param name="productInfos"></param>
        /// <param name="priceList"></param>
        /// <param name="productUnits"></param>
        /// <param name="priceFlag"></param>
        private void ConvertProductPrice(List<JToken> productInfos, JArray priceList, DynamicObjectCollection productUnits, byte priceFlag)
        {
            if (productUnits == null || !productUnits.Any()) return;

            foreach (var item in priceList)
            {
                var clientId = Convert.ToString(item["fclientid"]);
                var productId = Convert.ToString(item["fproductid"]);
                var unitId = Convert.ToString(item["funitid"]);

                //调用方传递的商品信息
                var productInfo = productInfos.FirstOrDefault(o =>
                    Convert.ToString(o["clientId"]).EqualsIgnoreCase(Convert.ToString(item["fclientid"])));
                if (productInfo == null) continue;

                var _clientId = Convert.ToString(productInfo["clientId"]);
                var _productId = Convert.ToString(productInfo["productId"]);
                var _unitId = Convert.ToString(productInfo["unitId"]);

                //单位为空，说明当前忽略换算
                if (_unitId.IsNullOrEmptyOrWhiteSpace()) continue;

                //单位如果相同，说明当前本身就是按照（销售 或 采购）单位进行查价的，则无需换算
                if (_unitId.EqualsIgnoreCase(unitId)) continue;

                //商品在价目表中的价格，一般是按照销售单位或者采购单位设置销售价或者采购价
                var cvtUnitFieldKey = "";
                var price = 0M;
                if (MatchPriceType("purPrice", priceFlag))
                {
                    cvtUnitFieldKey = "fpurunitid";
                    price = Convert.ToDecimal(item["fpurprice"]);
                }
                else if (MatchPriceType("salPrice", priceFlag))
                {
                    cvtUnitFieldKey = "fsalunitid";
                    price = Convert.ToDecimal(item["fsalprice"]);
                }
                else if (MatchPriceType("definedPrice", priceFlag))
                {
                    cvtUnitFieldKey = "fsalunitid";
                    price = Convert.ToDecimal(item["fdefinedprice"]);
                }
                if (cvtUnitFieldKey.IsNullOrEmptyOrWhiteSpace()) continue;

                /*
                 * 数据推演：
                 * 
                 * 商品单位信息
                 * 基本单位：片
                 * 销售单位：平米
                 * 
                 * 商品单位明细信息
                 * 1平米 = 2.46片
                 * 1包 = 9片
                 * 
                 * 假设销售价目表定价1平米 = 100
                 * 那么1片的价格就是 = 100 / 2.46 = 40.65040650406504
                 * 而1包又 = 9片，那么1包的价格就是 = 40.65040650406504 * 9 = 365.8536585365853
                 * 
                 * 最终四舍五入后保留两位小数的价格就是 = 365.85
                 */

                //换算后的价格
                var convertedPrice = 0M;

                //以价目表的价格为基准，根据销售单位或采购单位的换算率来换算价格
                var productUnit = productUnits.FirstOrDefault(o => Convert.ToString(o["fproductid"]).EqualsIgnoreCase(_productId));
                if (productUnit != null && !_unitId.IsNullOrEmptyOrWhiteSpace())
                {
                    var cvtUnitId = Convert.ToString(productUnit[cvtUnitFieldKey]);

                    //当前商品的（销售 或 采购）单位
                    var cvtUnit = productUnits.FirstOrDefault(o =>
                        Convert.ToString(o["fproductid"]).EqualsIgnoreCase(_productId)
                            && Convert.ToString(o["funitid"]).EqualsIgnoreCase(cvtUnitId));
                    if (cvtUnit != null)
                    {
                        var cvtRate = Convert.ToDecimal(cvtUnit["fcvtrate"]); //单位换算率
                        var precision = Convert.ToDecimal(cvtUnit["fprecision"]); //单位精度
                        if (cvtRate == 0)
                        {
                            throw new BusinessException($"商品【{cvtUnit["fproductname"]}】的单位【{cvtUnit["funitname"]}】换算率为0，无法执行价格换算，请检查商品单位的换算率！");
                        }
                        //先换算出（销售 或 采购）价格
                        //换算后的价格 = 价目表的价格 / 当前商品的（销售 或 采购）单位的换算率
                        convertedPrice = price / cvtRate;
                    }

                    var baseUnitId = Convert.ToString(productUnit["fbaseunitid"]);
                    if (!_unitId.EqualsIgnoreCase(baseUnitId))
                    {
                        //如果当前取价的单位不是基本单位，则取当前单位在商品中的换算率
                        var bizUnit = productUnits.FirstOrDefault(o =>
                            Convert.ToString(o["fproductid"]).EqualsIgnoreCase(_productId)
                                && Convert.ToString(o["funitid"]).EqualsIgnoreCase(_unitId));
                        if (bizUnit != null)
                        {
                            var cvtRate = Convert.ToDecimal(bizUnit["fcvtrate"]); //单位换算率
                            var precision = Convert.ToDecimal(bizUnit["fprecision"]); //单位精度
                            if (cvtRate == 0)
                            {
                                throw new BusinessException($"商品【{bizUnit["fproductname"]}】的单位【{bizUnit["funitname"]}】换算率为0，无法执行价格换算，请检查商品单位的换算率！");
                            }
                            convertedPrice = convertedPrice * cvtRate;
                        }
                    }

                    //默认四舍五入保留两位小数
                    convertedPrice = Math.Round(convertedPrice, 2);
                }

                if (MatchPriceType("purPrice", priceFlag))
                {
                    item["fpurprice"] = convertedPrice;
                }
                else if (MatchPriceType("salPrice", priceFlag))
                {
                    // 判断折率
                    var salPrice = Convert.ToDecimal(item["fsalprice"]);
                    item["fsalprice"] = convertedPrice;

                    // 扩展：增加判断特价促销
                    var flowestprice = item.GetJsonValue<decimal>("flowestprice");
                    if (flowestprice > 0)
                    {
                        var cvtRate = convertedPrice / salPrice;
                        item["flowestprice"] = flowestprice * cvtRate;
                    }
                }
                else if (MatchPriceType("definedPrice", priceFlag))
                {
                    item["fdefinedprice"] = convertedPrice;
                }
            }
        }

        /// <summary>
        /// 加载商品单位列表
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productIds"></param>
        /// <returns></returns>
        private DynamicObjectCollection LoadProductUnits(UserContext userCtx, List<string> productIds)
        {
            if (productIds == null || !productIds.Any()) return null;

            var sqlParam = new List<SqlParam>();

            var paramNames = new List<string>();
            for (int i = 0; i < productIds.Count; i++)
            {
                paramNames.Add($"@id{i}");
                sqlParam.Add(new SqlParam($"@id{i}", DbType.String, productIds[i]));
            }

            var sqlText = $@"
            select mu.fid fproductid,m.fname fproductname,mu.funitid,u.fname funitname,
            mu.fcvtrate,mu.fprecision,m.funitid fbaseunitid,m.fpurunitid,m.fsalunitid 
            from t_bd_material m with(nolock) 
            inner join t_bd_materialunit mu with(nolock) on mu.fid=m.fid 
			inner join t_ydj_unit u with(nolock) on u.fid=mu.funitid 
            where ";

            if (paramNames.Count == 1)
            {
                sqlText += $" m.fid={paramNames.First()}";
            }
            else
            {
                sqlText += $" m.fid in({string.Join(",", paramNames)})";
            }

            var dbService = userCtx.Container.GetService<IDBService>();
            var productUnits = dbService.ExecuteDynamicObject(userCtx, sqlText, sqlParam);

            return productUnits;
        }

        /// <summary>
        /// 获取商品非标价格
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="productInfos">取价参数列表，包括商品、辅助属性 等等</param>
        /// <returns>商品价格信息</returns>
        public List<JToken> GetUnstdTypePrice(UserContext userCtx, JArray productInfos)
        {
            var srvData = new List<JToken>();

            var _productInfos = productInfos
                ?.Where(o => !o["productId"].IsNullOrEmptyOrWhiteSpace())
                ?.ToList();

            if (_productInfos == null || !_productInfos.Any()) return srvData;

            // 销售价标记位
            byte priceFlag = 1;

            // 需要按照定价方案优先级计算价格的商品
            var tempProductInfos = new List<JToken>();

            // 加载经销商的所有定价方案
            var priceSchemes = this.LoadAllPriceSchemes(userCtx);

            // 如果定价对象中只定义了【商品】定价对象，则直接获取经销商的价目表价格
            if (this.IsOnlyProductPriceObj(priceSchemes))
            {
                // 取经销商自己的价目表
                var _priceInfos = this.MatchPrice(userCtx, _productInfos, priceFlag, false);

                // 如果经销商自己没有定义价目表或者没有匹配到价目表，则继续取总部的价格
                // 找出未取到价格的取价参数，根据慕思中台返回的价格，再结合经销商自己的定价百分比计算出最终的零售价
                foreach (var _productInfo in _productInfos)
                {
                    var priceInfo = _priceInfos?.FirstOrDefault(o =>
                        Convert.ToString(o["clientId"]).EqualsIgnoreCase(Convert.ToString(_productInfo["clientId"])));
                    if (priceInfo == null || Convert.ToDecimal(priceInfo?["salPrice"] ?? 0) <= 0)
                    {
                        tempProductInfos.Add(_productInfo);
                    }
                    else
                    {
                        srvData.Add(new JObject
                        {
                            ["clientId"] = priceInfo["clientId"],
                            ["salPrice"] = priceInfo["salPrice"]
                        });
                    }
                }
            }
            else
            {
                tempProductInfos.AddRange(_productInfos);
            }

            if (!tempProductInfos.Any()) return srvData;

            var profileService = userCtx.Container.GetService<ISystemProfile>();
            //该经销商在《销售管理参数》勾选上 【无经销价商品默认价格显示为0（前提是当前经销商启用经销价）】, 所带出来的价格就默认为0, 不会在取总部零售价了
            var noDealPrice = !userCtx.IsTopOrg && profileService.GetSystemParameter(userCtx, "bas_storesysparam", "fnodealprice", false);
            if (noDealPrice)
            {
                // 给没有匹配到价格的商品返回一个不成功的标记位
                productInfos.Where(x => !srvData.Any(y => (string)x["clientId"] == (string)y["clientId"])).ForEach(x =>
                {
                    srvData.Add(new JObject
                    {
                        { "clientId", x["clientId"] },
                        { "success", false }
                    });
                });
                return srvData;
            }

            // 根据慕思中台返回的价格，再结合经销商自己的定价百分比计算出最终的零售价

            // 批量加载商品的系列
            var productIds = tempProductInfos
                .Select(o => Convert.ToString(o["productId"]))
                .Distinct()
                .ToList();
            var productObjs = userCtx.LoadBizBillHeadDataById("ydj_product", productIds, "fseriesid");

            // 批量加载商品的所有上级商品类别
            var productServer = userCtx.Container.GetService<IProductService>();
            var productCategoryIdKv = productServer.LoadProductParentCategoryIds(userCtx, productIds);

            // 加载经销商的所有定价方案明细
            var priceSchemeDetails = this.LoadAllPriceSchemeDetails(userCtx);

            foreach (var productInfo in tempProductInfos)
            {
                // 查找取价参数
                var clientId = Convert.ToString(productInfo["clientId"]);
                var productId = Convert.ToString(productInfo["productId"]);
                var bizDate = Convert.ToDateTime(productInfo["bizDate"]);

                // 总部零售价（慕思中台返回的价格）
                var hqPrice = Convert.ToDecimal(productInfo["hqPrice"]);

                // 最终的零售价
                var salPrice = this.CalcPriceByPriceScheme(productObjs, productCategoryIdKv,
                    priceSchemes, priceSchemeDetails, productId, bizDate, hqPrice);

                srvData.Add(new JObject
                {
                    ["clientId"] = clientId,
                    ["salPrice"] = salPrice
                });
            }

            return srvData;
        }

        /// <summary>
        /// 根据定价方案计算商品零售价
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="priceFlag">取价标记位</param>
        /// <param name="productInfos">取价参数列表，包括商品、辅助属性 等等</param>
        /// <param name="srvData">商品价格信息</param>
        public void LoadProductPriceByPriceScheme(
            UserContext userCtx,
            byte priceFlag,
            List<JToken> productInfos,
            List<JToken> srvData, OperateOption option = null)
        {
            //销售价
            if (MatchPriceType("salPrice", priceFlag))
            {
                // 是否取总部价格
                var isHqPrice = productInfos.Any(o => !o["isHqPrice"].IsNullOrEmptyOrWhiteSpace() && Convert.ToBoolean(o["isHqPrice"]));
                var enableSellPrice = userCtx.IsEnableSellPrice();
                //取总部零售价 或者 取零售价时不启用经销价也不计算
                if (isHqPrice || !enableSellPrice) return;
            }

            // 需要按照定价方案优先级计算价格的商品
            var tempProductInfos = new List<JToken>();

            // 加载经销商的所有定价方案
            var priceSchemes = this.LoadAllPriceSchemes(userCtx);
            // 取经销商自己的价目表
            var _priceInfos = MatchPrice(userCtx, productInfos, priceFlag, false, true);
            //如果匹配到经销商自己的价目表
            foreach (var productInfo in productInfos)
            {
                var priceInfo = _priceInfos?.FirstOrDefault(o =>
                    Convert.ToString(o["clientId"]).EqualsIgnoreCase(Convert.ToString(productInfo["clientId"])));
                if (priceInfo == null || Convert.ToDecimal(priceInfo?["salPrice"] ?? 0) <= 0)
                {
                    tempProductInfos.Add(productInfo);
                }
                else
                {
                    // 标记该零售价来源于经销商自己
                    priceInfo["fromAgent"] = true;
                    srvData.Add(priceInfo);
                }
            }

            //// 如果定价对象中只定义了【商品】定价对象，则直接获取经销商的价目表价格
            //if (this.IsOnlyProductPriceObj(priceSchemes))
            //{
            //    // 如果经销商自己没有定义价目表或者没有匹配到价目表，则继续取总部的价格
            //    // 找出未取到价格的取价参数，再重新取一次总部的价格，再结合经销商自己的定价百分比计算出最终的零售价
            //    foreach (var productInfo in productInfos)
            //    {
            //        var priceInfo = _priceInfos?.FirstOrDefault(o =>
            //            Convert.ToString(o["clientId"]).EqualsIgnoreCase(Convert.ToString(productInfo["clientId"])));
            //        if (priceInfo == null || Convert.ToDecimal(priceInfo?["salPrice"] ?? 0) <= 0)
            //        {
            //            tempProductInfos.Add(productInfo);
            //        }
            //        else
            //        {
            //            // 标记该零售价来源于经销商自己
            //            priceInfo["fromAgent"] = true;
            //            srvData.Add(priceInfo);
            //        }
            //    }
            //}
            //else
            //{
            //    tempProductInfos.AddRange(productInfos);
            //}

            if (!tempProductInfos.Any()) return;

            var profileService = userCtx.Container.GetService<ISystemProfile>();
            //该经销商在《销售管理参数》勾选上 【无经销价商品默认价格显示为0（前提是当前经销商启用经销价）】, 所带出来的价格就默认为0, 不会在取总部零售价了
            var noDealPrice = !userCtx.IsTopOrg && profileService.GetSystemParameter(userCtx, "bas_storesysparam", "fnodealprice", false);
            if (noDealPrice) return;

            // 总部零售价
            var priceInfos = new List<JObject>();

            // 将没有取到经销商自己价目表的商品，再重新取一次总部的价格
            var hqPriceInfos = this.MatchPrice(userCtx, tempProductInfos, priceFlag, true);

            // 需要按照当前系统日期取价的商品信息
            var currentDateProductInfos = new List<JToken>();
            foreach (var tempProductInfo in tempProductInfos)
            {
                var hqPriceInfo = hqPriceInfos?.FirstOrDefault(o =>
                    Convert.ToString(o["clientId"]).EqualsIgnoreCase(Convert.ToString(tempProductInfo["clientId"])));
                if (hqPriceInfo == null || Convert.ToDecimal(hqPriceInfo?["salPrice"] ?? 0) <= 0)
                {
                    // 如果取不到总部的价格，则按当前系统日期再取一次总部价，其它取价条件不变
                    var jsonStr = tempProductInfo.ToJson();
                    var currentDateProductInfo = JObject.Parse(jsonStr);
                    currentDateProductInfo["bizDate"] = DateTime.Now.ToString("yyyy-MM-dd");
                    currentDateProductInfos.Add(currentDateProductInfo);
                }
                else
                {
                    priceInfos.Add(hqPriceInfo);
                }
            }

            // 按当前系统日期再取一次总部价
            if (currentDateProductInfos.Any())
            {
                hqPriceInfos = this.MatchPrice(userCtx, currentDateProductInfos, priceFlag, true);
                if (hqPriceInfos != null && hqPriceInfos.Any())
                {
                    priceInfos.AddRange(hqPriceInfos);
                }
            }

            if (priceInfos == null || !priceInfos.Any()) return;

            // 根据总部零售价再结合经销商自己的定价百分比计算出最终的零售价

            // 批量加载商品的系列、商品类别
            var productIds = priceInfos
                .Select(priceInfo =>
                {
                    var clientId = Convert.ToString(priceInfo["clientId"]);
                    var productInfo = tempProductInfos.FirstOrDefault(o => Convert.ToString(o["clientId"]).EqualsIgnoreCase(clientId));
                    var productId = Convert.ToString(productInfo["productId"]);
                    return productId;
                })
                .Distinct()
                .ToList();
            var productObjs = userCtx.LoadBizBillHeadDataById("ydj_product", productIds, "fseriesid");

            // 批量加载商品的所有上级商品类别
            var productServer = userCtx.Container.GetService<IProductService>();
            var productCategoryIdKv = productServer.LoadProductParentCategoryIds(userCtx, productIds);

            // 加载经销商的所有定价方案明细
            var priceSchemeDetails = this.LoadAllPriceSchemeDetails(userCtx);

            foreach (var priceInfo in priceInfos)
            {
                // 查找取价参数
                var clientId = Convert.ToString(priceInfo["clientId"]);
                var productInfo = tempProductInfos.FirstOrDefault(o => Convert.ToString(o["clientId"]).EqualsIgnoreCase(clientId));
                var productId = Convert.ToString(productInfo["productId"]);
                var bizDate = Convert.ToDateTime(productInfo["bizDate"]);

                // 查找商品信息
                var productObj = productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(productId));
                var categoryIds = productCategoryIdKv.GetValue(productId) ?? new List<string>();
                var seriesId = Convert.ToString(productObj?["fseriesid"]);

                // 总部零售价（总部价目表的价格）原逻辑：出厂价 =》通过经销定价方案计算零售价 =》通过选配计价公式计算 最终价格
                //var hqPrice = Convert.ToDecimal(priceInfo["salPrice"]);

                //选配计价公式处理后的价格
                var PriceAfter = (decimal)(srvData.Where(o => Convert.ToString(o["clientId"]).EqualsIgnoreCase(clientId) && Convert.ToBoolean(o["success"]))?.Select(o => Convert.ToDecimal(o["salPrice"])).FirstOrDefault());
                //to do 零售价在取的时候因为取不到当前价格所以需要将总部零售价获取再计算，hqPrice应该拿30774而不是26760

                // 总部零售价（选配计价公式计算后）现逻辑：出厂价 =》通过选配计价公式计算零售价 =》通过经销定价方案计算 最终价格
                var hqPrice = PriceAfter;
                if (PriceAfter == 0)
                {
                    //选配计价匹配不到无须再取总部价
                    if (!(option != null && option.TryGetVariableValue<bool>("NoSelectionPrice", out var NoSelectionPrice) && NoSelectionPrice))
                    {
                        hqPrice = Convert.ToDecimal(priceInfo["salPrice"]);
                    }
                }
                //如果前端录入了总部零售价，则以前端录入的总部零售价去算经销定价系数。
                var currentHqprice = Convert.ToDecimal(productInfo?["currentHqprice"] ?? 0M);
                if (currentHqprice > 0)
                {
                    hqPrice = currentHqprice;
                }

                // 最终的零售价
                var salPrice = this.CalcPriceByPriceScheme(productObjs, productCategoryIdKv,
                    priceSchemes, priceSchemeDetails, productId, bizDate, hqPrice);

                //   // 如果afterCalculate传了才走定价方案
                //if ((option != null && option.TryGetVariableValue<bool>("afterCalculate", out var afterCalculate) && afterCalculate))
                //{ 
                //    priceInfo["salPrice"] = salPrice;
                //}
                priceInfo["salPrice"] = salPrice;
                if (salPrice != hqPrice)
                {
                    // 标记该零售价来源于经销商自己（总部零售价+经销商自己的定价方案计算出来的统一零售价）
                    priceInfo["fromAgent"] = true;
                }
                if (srvData.Count > 0 && srvData.Any(o => Convert.ToString(o["clientId"]).EqualsIgnoreCase(clientId) && Convert.ToBoolean(o["success"])))
                {
                    srvData.ForEach(o =>
                    {
                        if (Convert.ToString(o["clientId"]).EqualsIgnoreCase(clientId))
                        {
                            o["salPrice"] = salPrice;
                        }
                    });
                }

                if (srvData.Count == 0 || srvData?.FirstOrDefault().GetJsonValue<bool>("success") == false || !srvData.Any(o => Convert.ToString(o["clientId"]).EqualsIgnoreCase(clientId)))
                {
                    srvData.Add(priceInfo);
                }
            }
        }

        /// <summary>
        /// 根据定价方案计算商品零售价
        /// </summary>
        /// <param name="productObjs">商品信息</param>
        /// <param name="productCategoryIdKv">商品类别</param>
        /// <param name="priceSchemes">经销定价方案</param>
        /// <param name="priceSchemeDetails">经销定价方案明细</param>
        /// <param name="productId">商品ID</param>
        /// <param name="bizDate">业务日期</param>
        /// <param name="hqPrice">总部零售价</param>
        /// <returns>零售价</returns>
        private decimal CalcPriceByPriceScheme(
            DynamicObjectCollection productObjs,
            Dictionary<string, List<string>> productCategoryIdKv,
            List<PriceScheme> priceSchemes,
            List<PriceSchemeDetail> priceSchemeDetails,
            string productId,
            DateTime bizDate,
            decimal hqPrice)
        {
            // 零售价默认等于总部零售价
            var salPrice = hqPrice;

            // 查找商品信息
            var productObj = productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(productId));
            var categoryIds = productCategoryIdKv.GetValue(productId) ?? new List<string>();
            var seriesId = Convert.ToString(productObj?["fseriesid"]);

            // 根据《经销定价方案》优先级先找到满足条件的商品的《经销定价方案明细》
            PriceSchemeDetail priceSchemeDetail = null;

            foreach (var priceScheme in priceSchemes)
            {
                // 排除商品定价对象
                if (priceScheme.PriceObj.EqualsIgnoreCase(PriceObjConsts.Product)) continue;

                // 特别说明 1 : 《经销定价方案明细》有那种什么规则都不配置的, 直接是填了【定价百分比】, 
                // 此时根据优先级如果匹配到该《经销定价方案明细》商品也不需要匹配了因为也没规则, 
                // 直接获取对应的【定价百分比】来计算。
                priceSchemeDetail = priceSchemeDetails?.FirstOrDefault(o =>
                    o.PriceSchemeId.EqualsIgnoreCase(priceScheme.Id)
                    && o.SeriesId.IsNullOrEmptyOrWhiteSpace()
                    && o.CategoryId.IsNullOrEmptyOrWhiteSpace()
                    && bizDate >= o.StartDate
                    && bizDate <= o.ExpireDate
                    && o.Percentum > 0);

                if (priceSchemeDetail != null) break;

                var priceObjIds = this.ParserPriceObjIds(priceScheme);
                if (this.IsSeries(priceObjIds))
                {
                    priceSchemeDetail = priceSchemeDetails?.FirstOrDefault(o =>
                        o.PriceSchemeId.EqualsIgnoreCase(priceScheme.Id)
                        && o.SeriesId.EqualsIgnoreCase(seriesId)
                        && o.CategoryId.IsNullOrEmptyOrWhiteSpace()
                        && bizDate >= o.StartDate
                        && bizDate <= o.ExpireDate
                        && o.Percentum > 0);
                }
                else if (this.IsCategory(priceObjIds))
                {
                    priceSchemeDetail = priceSchemeDetails?.FirstOrDefault(o =>
                        o.PriceSchemeId.EqualsIgnoreCase(priceScheme.Id)
                        && categoryIds.Contains(o.CategoryId, StringComparer.OrdinalIgnoreCase)
                        && o.SeriesId.IsNullOrEmptyOrWhiteSpace()
                        && bizDate >= o.StartDate
                        && bizDate <= o.ExpireDate
                        && o.Percentum > 0);
                }
                else if (this.IsSeriesAndCategory(priceObjIds))
                {
                    priceSchemeDetail = priceSchemeDetails?.FirstOrDefault(o =>
                        o.PriceSchemeId.EqualsIgnoreCase(priceScheme.Id)
                        && o.SeriesId.EqualsIgnoreCase(seriesId)
                        && categoryIds.Contains(o.CategoryId, StringComparer.OrdinalIgnoreCase)
                        && bizDate >= o.StartDate
                        && bizDate <= o.ExpireDate
                        && o.Percentum > 0);
                }

                // 只要找到了符合条件的方案明细，则不再继续找其它的方案明细
                if (priceSchemeDetail != null) break;
            }

            if (priceSchemeDetail != null)
            {
                // 零售价 = 总部零售价 * 定价百分比 / 100
                salPrice = salPrice * priceSchemeDetail.Percentum / 100;
            }

            return salPrice;
        }

        /// <summary>
        /// 清除定价方案缓存
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        public void ClearPriceSchemeCache(UserContext userCtx)
        {
            var cache = userCtx.Container.GetService<IRedisCache>();
            var cacheKey = PriceCacheKeyConsts.PriceScheme.Fmt(userCtx.Company);
            cache.Remove(userCtx, cacheKey);
        }

        /// <summary>
        /// 清除定价方案明细缓存
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        public void ClearPriceSchemeDetailCache(UserContext userCtx)
        {
            var cache = userCtx.Container.GetService<IRedisCache>();
            var cacheKey = PriceCacheKeyConsts.PriceSchemeDetail.Fmt(userCtx.Company);
            cache.Remove(userCtx, cacheKey);
        }

        /// <summary>
        /// 加载经销商的所有定价方案，且已按优先级排序
        /// </summary>
        private List<PriceScheme> LoadAllPriceSchemes(UserContext userCtx)
        {
            // 先取缓存
            var cache = userCtx.Container.GetService<IRedisCache>();
            var cacheKey = PriceCacheKeyConsts.PriceScheme.Fmt(userCtx.Company);
            var priceSchemes = cache.Get<List<PriceScheme>>(userCtx, cacheKey);
            if (priceSchemes != null && priceSchemes.Any()) return priceSchemes;

            var sqlText = @"
            select fid,fpriority,fpriceobj from t_bas_pricescheme with(nolock) 
            where fmainorgid=@fmainorgid and fforbidstatus='0' 
            order by fpriority desc";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, userCtx.Company)
            };

            priceSchemes = new List<PriceScheme>();

            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, sqlText, sqlParam))
            {
                while (reader.Read())
                {
                    priceSchemes.Add(new PriceScheme
                    {
                        Id = reader.GetValueToString("fid"),
                        PriceObj = reader.GetValueToString("fpriceobj"),
                        Priority = reader.GetValueToInt("fpriority")
                    });
                }
            }

            // 缓存起来
            cache.Set(cacheKey, priceSchemes);

            return priceSchemes;
        }

        /// <summary>
        /// 加载经销商的所有定价方案明细
        /// </summary>
        private List<PriceSchemeDetail> LoadAllPriceSchemeDetails(UserContext userCtx)
        {
            // 先取缓存
            var cache = userCtx.Container.GetService<IRedisCache>();
            var cacheKey = PriceCacheKeyConsts.PriceSchemeDetail.Fmt(userCtx.Company);
            var priceSchemeDetails = cache.Get<List<PriceSchemeDetail>>(userCtx, cacheKey);
            if (priceSchemeDetails != null && priceSchemeDetails.Any()) return priceSchemeDetails;

            // 缓存没有再取数据库
            var sqlText = @"
            select fpriceschemeid,fseriesid,fcategoryid,fpercentum,fstartdate,fexpiredate from t_bas_priceschemedetail with(nolock) 
            where fmainorgid=@fmainorgid and fforbidstatus='0'";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, userCtx.Company)
            };

            priceSchemeDetails = new List<PriceSchemeDetail>();

            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, sqlText, sqlParam))
            {
                while (reader.Read())
                {
                    priceSchemeDetails.Add(new PriceSchemeDetail
                    {
                        PriceSchemeId = reader.GetValueToString("fpriceschemeid"),
                        SeriesId = reader.GetValueToString("fseriesid"),
                        CategoryId = reader.GetValueToString("fcategoryid"),
                        Percentum = reader.GetValueToInt("fpercentum"),
                        StartDate = reader.GetValueToDateTime("fstartdate"),
                        ExpireDate = reader.GetValueToDateTime("fexpiredate")
                    });
                }
            }

            // 缓存起来
            cache.Set(cacheKey, priceSchemeDetails);

            return priceSchemeDetails;
        }

        /// <summary>
        /// 从指定的定价方案中解析出定价对象
        /// </summary>
        /// <param name="priceScheme">定价方案</param>
        /// <returns>定价对象ID集合</returns>
        private List<string> ParserPriceObjIds(PriceScheme priceScheme)
        {
            var priceObjIds = priceScheme.PriceObj
                ?.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                ?.ToList()
                ?? new List<string>();
            return priceObjIds;
        }

        /// <summary>
        /// 检查指定的定价方案集合中是否只有商品定价对象
        /// </summary>
        /// <param name="priceSchemes">定价方案集合</param>
        /// <returns></returns>
        private bool IsOnlyProductPriceObj(List<PriceScheme> priceSchemes)
        {
            return priceSchemes?.Any(o => o.PriceObj.EqualsIgnoreCase(PriceObjConsts.Product)) ?? false;
        }

        /// <summary>
        /// 检查定价对象是否是：系列
        /// </summary>
        /// <param name="priceObjIds">定价对象ID集合</param>
        /// <returns></returns>
        private bool IsSeries(List<string> priceObjIds)
        {
            return
                priceObjIds.Count == 1 &&
                priceObjIds.Contains(PriceObjConsts.Series);
        }

        /// <summary>
        /// 检查定价对象是否是：商品类型
        /// </summary>
        /// <param name="priceObjIds">定价对象ID集合</param>
        /// <returns></returns>
        private bool IsCategory(List<string> priceObjIds)
        {
            return
                priceObjIds.Count == 1 &&
                priceObjIds.Contains(PriceObjConsts.Category);
        }

        /// <summary>
        /// 检查定价对象是否是：系列+商品类别
        /// </summary>
        /// <param name="priceObjIds">定价对象ID集合</param>
        /// <returns></returns>
        private bool IsSeriesAndCategory(List<string> priceObjIds)
        {
            return
                priceObjIds.Count == 2 &&
                priceObjIds.Contains(PriceObjConsts.Series) &&
                priceObjIds.Contains(PriceObjConsts.Category);
        }

        #region  获取二级分销采购价目
        public List<JToken> GetSecondOrgProductPrice(UserContext userCtx, string priceRule, JArray productInfos, string customerId,string temptable)
        {
            var (productIds, productMap) = GetMaterialDict(userCtx,productInfos, temptable);
            var ratioData = LoadRatioData(userCtx);
            var priceData = LoadPriceDataInParallel(userCtx, productIds, customerId, priceRule, temptable);
            var result = ProcessPrices(userCtx,priceRule, productInfos, productMap, priceData, ratioData);
            return result;

        }


        //获取商品相关字段信息
        private (List<string> productIds, Dictionary<string, DynamicObject> productMap) GetMaterialDict(UserContext userCtx, JArray productInfos, string temptable)
        {
            var productIds = productInfos.Select(p => $"'{Convert.ToString(p["productId"])}'").ToList();
            if (!productIds.Any()) return (new List<string>(), new Dictionary<string, DynamicObject>());
            string productsql = $"select tab1.fid, fseriesid, fmainorgid  from T_BD_MATERIAL tab1 with(nolock)  ";

            if (!temptable.IsNullOrEmptyOrWhiteSpace())
            {
                productsql = $"{productsql} inner join {temptable} tab2 with(nolock) on tab1.fid=tab2.fid";
            }
            else {
                productsql = $"{productsql} where fid in ({string.Join(",", productIds)})";
            }
            var products = userCtx.ExecuteDynamicObject(productsql, null);
            var productMap = products.ToDictionary(
                p => Convert.ToString(p["fid"]),
                p => p,
                StringComparer.OrdinalIgnoreCase);
            return (productIds, productMap);
        }

        //获取经销商各系列的比例
        private RatioInfo LoadRatioData(UserContext userCtx)
        {
            var ratioInfo = new RatioInfo();
            var currAgentSql = $@"
                select t1.fisreseller,t1.fisnotmgrinv,t1.fsalemanid,t1.fresellratio,
				t2.fseriesid,t2.fresellratio fresellratio_e,t1.fpricerule
				from t_bas_agent t1 with(nolock)
				left join t_agent_ratioinfo t2  with(nolock) on t1.fid=t2.fid
                where t1.fid='{userCtx.Company}'";
            var agentData = userCtx.ExecuteDynamicObject(currAgentSql, null);
            if (agentData == null || !agentData.Any())
            {
                ratioInfo.DefaultRatio = 1m;
                return ratioInfo;
            }
            var firstRecord = agentData.First();
            try
            {
                ratioInfo.DefaultRatio = Convert.ToDecimal(firstRecord["fresellratio"]);
                // 处理无效的默认比例值
                if (ratioInfo.DefaultRatio <= 0) ratioInfo.DefaultRatio = 1m;
            }
            catch
            {
                ratioInfo.DefaultRatio = 1m;
            }
            // 
            foreach (var record in agentData)
            {
                var seriesId = Convert.ToString(record["fseriesid"]);
                if (string.IsNullOrWhiteSpace(seriesId)) continue;
                try
                {
                    if (!ratioInfo.DetailRatios.ContainsKey(seriesId)) {
                        var ratio = Convert.ToDecimal(record["fresellratio_e"]);
                        ratioInfo.DetailRatios.Add(seriesId, ratio);
                    }
                }   
                catch
                {
                    // 忽略无效的比例值
                }
            }

            return ratioInfo;
        }

        //获取给价目的价格
        private PriceData LoadPriceDataInParallel(UserContext userCtx,List<string> productIds, string customerId, string priceRule,string temptable)
        {
            var priceData = new PriceData();
             
            Parallel.Invoke(
                () => priceData.SalePrices = LoadSalePrices(userCtx,productIds, customerId, temptable),
                () => priceData.PurchasePrices = LoadPurchasePrices(userCtx,productIds, temptable),
                () => priceData.SynthesizePrices = LoadSynthesizePrices(userCtx, productIds, priceRule, temptable)
            );

            return priceData;
        }
        #region 获取价目数据
        //销售价目以及分销价目
        private Dictionary<string, List<DynamicObject>> LoadSalePrices(UserContext userCtx,List<string> productIds, string customerId,string temptable)
        {
          
            string sql = $@"/*dialect*/SELECT 
                                p.fnumber,
                                p.fformid,
                                pe.fproductid,
                                pe.fconfirmstatus,
                                pe.funitid,
                                pe.funstdtype,
                                pe.fqty,
                                pe.fstartdate,
                                pe.fexpiredate,
                                pe.fsalprice AS fprice,
                                CASE 
                                    WHEN pe.fstartdate IS NULL OR pe.fexpiredate IS NULL THEN 1
                                    WHEN GETDATE() BETWEEN pe.fstartdate AND pe.fexpiredate THEN 1
                                    ELSE 0 
                                END AS enable,
                                ce.fcustomerid
                            FROM {temptable} temp
                            INNER JOIN t_bd_material m WITH(NOLOCK) ON temp.fid = m.fid
                            INNER JOIN t_ydj_priceentry pe WITH(NOLOCK) ON 
                                pe.fproductid = m.fid AND 
                                pe.fconfirmstatus = '2' AND 
                                pe.fattrinfo = ''
                            INNER JOIN t_ydj_price p WITH(NOLOCK) ON 
                                p.fid = pe.fid AND
                                p.fmainorgid IN ('{userCtx.ParentCompanyId}','{userCtx.TopCompanyId}') AND
                                p.FFormId IN ('ydj_reprice','ydj_price') AND
                                p.fforbidstatus = '0' AND
                                p.ftype IN ('quote_type_01','quote_type_04')
                            OUTER APPLY (
                                SELECT TOP 1 ce.fcustomerid
                                FROM t_ydj_customerentry ce WITH(NOLOCK)
                                WHERE ce.fid = p.fid
                                AND
                                (
                                    (p.flimit = '' AND ce.fcustomerid IS NULL) OR
                                    (p.flimit = 'limit_01' AND ce.fcustomerid = '{customerId}')
                                )
                            ) ce
                            WHERE (m.fsalunitid = pe.funitid OR pe.funitid = '')
                            ORDER BY 
                                p.FFormId,
                                enable DESC,
                                ce.fcustomerid DESC,
                                pe.fstartdate DESC;
                            ";
              var data = userCtx.ExecuteDynamicObject(sql, null);
            return GroupPriceData(data, "fproductid");
        }

        //采购价目
        private Dictionary<string, List<DynamicObject>> LoadPurchasePrices(UserContext userCtx,List<string> productIds,string temptable)
        {
            string sql = $@" /*dialect*/select   p.fnumber,
				p.fformid,
                pe.fproductid_e  as fproductid,
                pe.fstartdate_e as fstartdate,
                pe.fexpiredate_e as fexpiredate,
                pe.fconfirmstatus,
                pe.funitid_e as  funitid,
                '' as funstdtype,
                '1' as fqty,
                pe.fpurprice as fprice
                    from t_ydj_purchasepriceentry pe with(nolock)
                    inner join t_ydj_purchaseprice p with(nolock) on p.fid=pe.fid 
                     inner join {temptable} temp on temp.fid= pe.fproductid_e
                    inner join t_bd_material m with(nolock) on temp.fid=m.fid 
                     where p.fmainorgid in ('{userCtx.ParentCompanyId}','{userCtx.TopCompanyId}')
                    and p.fforbidstatus='0' 
                    and pe.fconfirmstatus='2' 
                    and (m.fpurunitid=pe.funitid_e or pe.funitid_e='') 
                    and (case when pe.fstartdate_e is null then 1 else case when datediff(d,pe.fstartdate_e,GETDATE())>-1 then 1 else 0 end end)=1 
                    and (case when pe.fexpiredate_e is null then 1 else case when datediff(d,GETDATE(),pe.fexpiredate_e)>-1 then 1 else 0 end end)=1 
                    order by pe.fstartdate_e desc";
            var data = userCtx.ExecuteDynamicObject(sql, null);
            return GroupPriceData(data, "fproductid");
        }

        //综合价目
        private Dictionary<string, DynamicObject> LoadSynthesizePrices(UserContext userCtx, List<string> productIds,string priceRule,string temptable)
        {
            string priceFeild = "fpurfacprice";
            if (priceRule == "1") {
                priceFeild = "funifysaleprice";
            }
            //综合价目表
            string sql = $@" /*dialect*/select '' as fnumber, 
                '2' as fconfirmstatus,
                '' as  funitid,
                '' as funstdtype,
                '1' as fqty, 
                fformid, 
                fmaterialid as fproductid,
                 getdate() as fstartdate,
                '2099-01-01 00:00:00' AS fexpiredate, 
                fattrinfo,
                {priceFeild}  as fprice
                from t_ydj_pricesynthesize tab1
                inner join {temptable} temp on temp.fid=tab1.fmaterialid 
                where tab1.fattrinfo='' and  tab1.fmainorgid='{userCtx.ParentCompanyId}'
                AND tab1.fpurfacprice>0";
            var data = userCtx.ExecuteDynamicObject(sql, null);
            return data?
           .GroupBy(x => Convert.ToString(x["fproductid"])) // 按产品ID分组
           .ToDictionary(
               g => g.Key,
               g => g .FirstOrDefault(), // 每组取第一条
               StringComparer.OrdinalIgnoreCase
           ) ?? new Dictionary<string, DynamicObject>();
        }

        private Dictionary<string, List<DynamicObject>> GroupPriceData(IEnumerable<DynamicObject> data, string keyField)
        {
            return data
                .GroupBy(x => Convert.ToString(x[keyField]))
                .ToDictionary(
                    g => g.Key,
                    g => g.OrderBy(x=>x["fformid"]).ThenBy(x=>x["fstartdate"]).ThenBy(x=>x["fexpiredate"]).ToList(),
                    StringComparer.OrdinalIgnoreCase);
        }

        #endregion
        private List<JToken> ProcessPrices(UserContext userCtx,string priceRule, JArray productInfos,
        Dictionary<string, DynamicObject> productMap, PriceData priceData, RatioInfo ratioData)
        {
            var result = new ConcurrentBag<JToken>();
          
            Parallel.ForEach(productInfos.Cast<JToken>(), item =>
            {
                var productId = Convert.ToString(item["productId"]);
                if (!productMap.TryGetValue(productId, out var product)) return;

                var seriesId = Convert.ToString(product["fseriesid"]);
                var ratio = GetResellRatio(seriesId, ratioData);

                var priceFound = priceRule == "0"
                    ? ProcessPriceRule0(userCtx, productId, product, priceData, ratio)
                    : ProcessPriceRule1(productId, product, priceData, ratio);

                if (priceFound != null) result.Add(priceFound);
            });

            return result.ToList();
        }

        private JToken ProcessPriceRule0(UserContext userCtx,string productId, dynamic product,
            PriceData priceData, decimal ratio)
        {
            // 实现价格规则0的处理逻辑
            var price = FindValidPrice(priceData.SalePrices, productId, "ydj_reprice")
                ?? FindPurchasePrice(priceData.PurchasePrices,productId, product, userCtx.TopCompanyId)
                ?? FindSynthesizePrice(priceData.SynthesizePrices,productId );

            return CreatePriceItem(price, ratio);
        }

        private JToken ProcessPriceRule1(string productId, dynamic product,
            PriceData priceData, decimal ratio)
        {
            // 实现价格规则1的处理逻辑
            var price = FindValidPrice(priceData.SalePrices, productId, "ydj_reprice") ?? FindValidPrice(priceData.SalePrices, productId, "ydj_price")
                ?? FindSynthesizePrice(priceData.SynthesizePrices,productId );

            return CreatePriceItem(price, ratio);
        }

        #region 辅助方法
        private DynamicObject FindValidPrice(Dictionary<string, List<DynamicObject>> prices,
            string productId, string formId)
        {
            if (prices.TryGetValue(productId, out var priceList))
            {
                return priceList.OrderByDescending(x => x["enable"]).ThenByDescending(x=>x["fcustomerid"]).ThenByDescending(x => x["fstartdate"])
                      .FirstOrDefault(p =>
                    Convert.ToString(p["FFormId"]) == formId &&
                    Convert.ToDecimal(p["fprice"]) > 0);
            }
            return null;
        }
        private DynamicObject FindPurchasePrice(Dictionary<string, List<DynamicObject>> purchasePrices,string productId, DynamicObject product,string topCompanyId)
        {
            if (purchasePrices == null) return null;

            try
            {
                // 1. 判断采购价表单类型
                string formId = "ydj_selfpurchaseprice";
                if (product != null &&
                    Convert.ToString(product["fmainorgid"]) == topCompanyId)
                {
                    formId = "ydj_purchaseprice";
                }

                // 2. 查找匹配的采购价记录
                if (purchasePrices.TryGetValue(productId, out var priceList))
                {
                    var validPrice = priceList
                        .Where(p =>
                            Convert.ToString(p["FFormId"]) == formId &&
                            Convert.ToDecimal(p["fprice"]) > 0)
                        .OrderByDescending(p=>Convert.ToDateTime(p["fstartdate"])).FirstOrDefault();

                    return validPrice;
                }
            }
            catch (Exception ex)
            {
                
            }

            return null;
        }
     
        private DynamicObject FindSynthesizePrice(Dictionary<string, DynamicObject> synthesizePrices, string productId)
        {
            if (synthesizePrices == null) return null;

            try
            {
                // 1. 查找综合价记录
                if (synthesizePrices.TryGetValue(productId, out var price))
                {
                    // 2. 验证价格有效性
                    var priceValue = Convert.ToDecimal(price["fprice"]);
                    if (priceValue > 0)
                    {
                        return price;
                    }
                }
            }
            catch (Exception ex)
            {
               
            }

            return null;
        }
      
        
        private decimal GetResellRatio(string seriesId, RatioInfo ratioData)
        {
            if (ratioData.DetailRatios.TryGetValue(seriesId, out var ratio))
                return ratio;

            return ratioData.DefaultRatio > 0 ? ratioData.DefaultRatio : 1m;
        }

      
        private JToken CreatePriceItem(DynamicObject priceSource, decimal ratio)
        {
            if (priceSource == null) return null;
            if (priceSource["fformid"].ToString().EqualsIgnoreCase("ydj_reprice")) { ratio=1m;};
            try
            {
                return new JObject(
                    new JProperty("fsourcebillno", priceSource["fnumber"].ToString()),
                    new JProperty("fsourceformid", priceSource["fformid"].ToString()),
                    new JProperty("fproductid", priceSource["fproductid"].ToString()),
                    new JProperty("fstartdate", priceSource["fstartdate"].ToString()),
                    new JProperty("fexpiredate", priceSource["fexpiredate"].ToString()),
                    new JProperty("funstdtype", priceSource["funstdtype"].ToString()),
                    new JProperty("funitid", priceSource["funitid"].ToString()),
                    new JProperty("fqty", priceSource["fqty"].ToString()),
                    new JProperty("fconfirmstatus", priceSource["fconfirmstatus"].ToString()),
                    new JProperty("fprice", Convert.ToDecimal(priceSource["fprice"]) * ratio)
                );
            }
            catch (Exception ex)
            {
                return null;
            }
        }


        private bool IsValidPrice(PriceItem price)
        {
            // 实现具体验证逻辑
            return price?.Price > 0;
        }
        #endregion 

        #endregion
    }

    public class PriceData
    {
        public Dictionary<string, List<DynamicObject>> SalePrices { get; set; }
        public Dictionary<string, List<DynamicObject>> PurchasePrices { get; set; }
        public Dictionary<string, DynamicObject> SynthesizePrices { get; set; }
    }
    public class RatioInfo
    {
        public decimal DefaultRatio { get; set; } = 1m;
        public Dictionary<string, decimal> DetailRatios { get; }
            = new Dictionary<string, decimal>(StringComparer.OrdinalIgnoreCase);
    }

    public class PriceItem
    {
        public string Number { get; set; }
        public string FormId { get; set; }
        public string ProductId { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? ExpireDate { get; set; }
        public decimal Price { get; set; }

        public JToken ToJToken(decimal ratio) => new JObject(
            new JProperty("fsourcebillno", Number),
            new JProperty("fsourceformid", FormId),
            new JProperty("fproductid", ProductId),
            new JProperty("fstartdate", StartDate?.ToString("yyyy-MM-dd HH:mm:ss.fff")),
            new JProperty("fexpiredate", ExpireDate?.ToString("yyyy-MM-dd HH:mm:ss.fff")),
            new JProperty("fprice", Price * ratio)
        );
    }
    /// <summary>
    /// 经销定价方案信息
    /// </summary>
    public class PriceScheme
    {
        /// <summary>
        /// 定价方案ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 定价对象
        /// </summary>
        public string PriceObj { get; set; }
    }

    /// <summary>
    /// 经销定价方案明细信息
    /// </summary>
    public class PriceSchemeDetail
    {
        /// <summary>
        /// 定价方案ID
        /// </summary>
        public string PriceSchemeId { get; set; }

        /// <summary>
        /// 系列ID
        /// </summary>
        public string SeriesId { get; set; }

        /// <summary>
        /// 商品类别ID
        /// </summary>
        public string CategoryId { get; set; }

        /// <summary>
        /// 百分比
        /// </summary>
        public int Percentum { get; set; }

        /// <summary>
        /// 生效日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 失效日期
        /// </summary>
        public DateTime ExpireDate { get; set; }
    }

    /// <summary>
    /// 经销定价对象常量
    /// </summary>
    public class PriceObjConsts
    {
        /// <summary>
        /// 商品
        /// </summary>
        public const string Product = "priceobj_01";

        /// <summary>
        /// 系列
        /// </summary>
        public const string Series = "priceobj_02";

        /// <summary>
        /// 商品类别
        /// </summary>
        public const string Category = "priceobj_03";
    }

    /// <summary>
    /// 价格缓存键常量
    /// </summary>
    public class PriceCacheKeyConsts
    {
        /// <summary>
        /// 价格方案
        /// </summary>
        public const string PriceScheme = "PriceScheme:{0}";

        /// <summary>
        /// 价格方案明细
        /// </summary>
        public const string PriceSchemeDetail = "PriceSchemeDetail:{0}";
    }
}
