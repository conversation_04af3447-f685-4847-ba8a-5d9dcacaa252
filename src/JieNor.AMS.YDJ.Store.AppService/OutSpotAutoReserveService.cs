//using JieNor.AMS.YDJ.Core.Interface;
//using JieNor.Framework.IoC;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;
//using JieNor.Framework.MetaCore.FormMeta;
//using JieNor.Framework.SuperOrm.DataEntity;
//using JieNor.Framework.CustomException;
//using JieNor.Framework;
//using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
//using JieNor.Framework.Interface;
//using JieNor.Framework.MetaCore;
//using Newtonsoft.Json.Linq;
//using Newtonsoft.Json;
//using JieNor.Framework.SuperOrm;
//using JieNor.Framework.MetaCore.FormOp.FormService;

//namespace JieNor.AMS.YDJ.Store.AppService
//{
//    /// <summary>
//    /// 出现货自动预留服务
//    /// </summary>
//    [InjectService]
//    public class OutSpotAutoReserveService : IOutSpotAutoReserveService
//    {
//        /// <summary>
//        /// 自动预留
//        /// </summary>
//        /// <param name="userContext">用户上下文</param>
//        /// <param name="dataEntities">源单数据</param>
//        /// <param name="htmlForm">源单表单</param>
//        /// <param name="entryKey">源单明细id</param>
//        /// <param name="customerId">源单客户字段id</param>
//        /// <param name="reserveDateToId">源单预留日期字段id</param>
//        public void AutoReserve(UserContext userContext, DynamicObject[] dataEntities, HtmlForm htmlForm, string entryKey, string customerId, string reserveDateToId)
//        {
//            if (dataEntities == null || dataEntities.Length <= 0)
//            {
//                return;
//            }

//            HtmlEntryEntity sourceHtmlEntry;
//            HtmlField customerField;
//            HtmlField reserveDateToField;

//            checkAutoReserveArgs(userContext, htmlForm, entryKey, customerId, reserveDateToId, out sourceHtmlEntry, out customerField, out reserveDateToField);

//            var fentries = dataEntities.SelectMany(x => x[entryKey] as DynamicObjectCollection).Where(x => Convert.ToBoolean(x["fisoutspot"])).ToList();

//            if (fentries == null || fentries.Count <= 0)
//            {
//                return;
//            }

//            var profileService = userContext.Container.GetService<ISystemProfile>();
//            var foutspotautores = profileService.GetSystemParameter(userContext, "bas_storesysparam", "foutspotautores", false);

//            if (false == foutspotautores)
//            {
//                return;
//            }

//            var metaModelService = userContext.Container.GetService<IMetaModelService>();
//            var billMenus = metaModelService.GetBillMenu(userContext, htmlForm.Id, "", Enu_BillStatus.New);

//            var reserveInventoryMenu = billMenus?.FirstOrDefault(x => Convert.ToString(x["id"]) == "tbReserveInventory") as JObject;
//            var paramString = (string)reserveInventoryMenu?["param"];

//            var param = JObject.Parse($"{{{paramString ?? string.Empty}}}");

//            var formParam = param?["dialog"]?["formParam"] as JObject;

//            if (formParam == null || formParam.Count <= 0)
//            {
//                throw new BusinessException("预留操作未配置源单明细映射字段，请检查配置！");
//            }

//            JsonSerializer serializer = new JsonSerializer();

//            var cvtRule = serializer.Deserialize<ConvertRule>(new JTokenReader(formParam));

//            if (cvtRule == null
//                || cvtRule.ActiveEntityKey.IsNullOrEmptyOrWhiteSpace()
//                || cvtRule.FieldMappings == null
//                || cvtRule.FieldMappings.Count <= 0)
//            {
//                throw new BusinessException("预留操作未配置源单明细映射字段，请检查配置！");
//            }

//            var targetForm = metaModelService.LoadFormModel(userContext, "stk_reservedialog");
//            var targetHtmlEntry = targetForm.GetEntryEntity("fentry");
//            var groupEntities = fentries.GroupBy(x => x.Parent);
//            var targetEntities = new List<DynamicObject>();

//            foreach (var groupEntity in groupEntities)
//            {
//                targetEntities.Add(convertData(userContext, groupEntity, cvtRule, htmlForm, sourceHtmlEntry, targetForm, targetHtmlEntry, customerField, reserveDateToField));
//            }

//            var unitConvertService = userContext.Container.GetService<IUnitConvertService>();
//            unitConvertService.ConvertByBasQty(userContext, targetForm, targetEntities, OperateOption.Create());
//            var fields = targetHtmlEntry.GetFieldList(targetForm.GetFieldList().ToList()).Where(x => (x is HtmlBasePropertyField) == false).ToList();

//            foreach (var targetEntity in targetEntities)
//            {
//                var fsourcenumber = Convert.ToString(targetEntity["fsourcenumber"]);
//                var dataEntity = dataEntities.FirstOrDefault(x => Convert.ToString(x[htmlForm.NumberFldKey]) == fsourcenumber);
//                if (dataEntity == null)
//                {
//                    continue;
//                }

//                var targetEntries = targetHtmlEntry.DynamicProperty.GetValue<DynamicObjectCollection>(targetEntity);
//                var demandEntry = new List<Dictionary<string, object>>();

//                foreach (var targetEntry in targetEntries)
//                {
//                    Dictionary<string, object> item = new Dictionary<string, object>();
//                    foreach (var field in fields)
//                    {
//                        item[field.Id] = targetEntry[field.PropertyName];
//                    }
//                    demandEntry.Add(item);
//                }

//                var gateWay = userContext.Container.GetService<IHttpServiceInvoker>();
//                var result = gateWay.InvokeBillOperation(userContext, htmlForm.Id, new[] { dataEntity }, "Reserveinventory", new Dictionary<string, object>
//                {
//                    { "demandEntry",Newtonsoft.Json.JsonConvert.SerializeObject(demandEntry)},
//                    { "reserveObjectId", Convert.ToString(targetEntity["freserveobjectid"])},
//                    { "reserveObjectType",Convert.ToString(targetEntity["freserveobjecttype"])},
//                    { "description",""}
//                });

//                result?.ThrowIfHasError(true, $"{htmlForm.Caption}自动预留失败!");
//            }
//        }

//        /// <summary>
//        /// 出现货自动预留字段映射转换方法
//        /// </summary>
//        /// <param name="userContext"></param>
//        /// <param name="groupEntity"></param>
//        /// <param name="cvtRule"></param>
//        /// <param name="sourceForm"></param>
//        /// <param name="sourceEntry"></param>
//        /// <param name="targetForm"></param>
//        /// <param name="targetHtmlEntry"></param>
//        /// <param name="customerField"></param>
//        /// <param name="reserveDateToField"></param>
//        /// <returns></returns>
//        private DynamicObject convertData(UserContext userContext,
//                                          IGrouping<object, DynamicObject> groupEntity,
//                                          ConvertRule cvtRule,
//                                          HtmlForm sourceForm,
//                                          HtmlEntryEntity sourceEntry,
//                                          HtmlForm targetForm,
//                                          HtmlEntryEntity targetHtmlEntry,
//                                          HtmlField customerField,
//                                          HtmlField reserveDateToField)
//        {
//            var exprEngine = userContext.Container.GetService<IBizExpressionEvaluator>();
//            var exprCtx = userContext.Container.GetService<IBizExpressionContext>();
//            var expression = userContext.Container.GetService<IBizExpression>();

//            var sourceEntity = groupEntity.Key as DynamicObject;
//            var fsourcebillno = Convert.ToString(sourceEntity[sourceForm.NumberFldKey]);
//            var fsourceinterid = Convert.ToString(sourceEntity["id"]);
//            var targetEntity = targetForm.GetDynamicObjectType(userContext).CreateInstance() as DynamicObject;
//            var targetEntries = targetHtmlEntry.DynamicProperty?.GetValue<DynamicObjectCollection>(targetEntity);

//            BizDynamicDataRow dcRow = new BizDynamicDataRow(sourceForm);
//            exprCtx.BizData = sourceEntity;
//            exprCtx.BindSetField(new TrySetValueHandler(dcRow.TrySetMember));
//            exprCtx.BindGetField(new TryGetValueHandler(dcRow.TryGetMember));

//            var profileService = userContext.Container.GetService<ISystemProfile>();

//            //可预留到仓库仓位
//            var resCarryStoreHouse = profileService.GetSystemParameter(userContext, "bas_storesysparam", "frescarrystorehouse", true);

//            var activeEntrys = sourceEntry.DynamicProperty?.GetValue<DynamicObjectCollection>(sourceEntity);
//            foreach (var activeEntry in groupEntity)
//            {
//                var entry = new DynamicObject(targetHtmlEntry.DynamicObjectType);
//                targetEntries.Add(entry);

//                foreach (var map in cvtRule.FieldMappings)
//                {
//                    if (map.Id.IsNullOrEmptyOrWhiteSpace() || map.SrcFieldId.IsNullOrEmptyOrWhiteSpace()) continue;
//                    var field = targetForm.GetField(map.Id);
//                    if (field == null) continue;

//                    //仓库、仓位 的携带逻辑按照参数来控制
//                    if (map.Id.EqualsIgnoreCase("fstorehouseid") || map.Id.EqualsIgnoreCase("fstorelocationid"))
//                    {
//                        //如果不需要字段带出仓库、仓位，则无需处理映射逻辑
//                        if (!resCarryStoreHouse) continue;
//                    }

//                    object srcFieldVal = null;
//                    switch (map.MapType)
//                    {
//                        case (int)Enu_FieldMapType.Expression:
//                            dcRow.ActiveDataObject = activeEntry;
//                            expression.ExpressionText = map.SrcFieldId;
//                            var vars = exprEngine.GetNameExpression(expression);
//                            string strExprTxt = map.SrcFieldId;
//                            foreach (var varName in vars)
//                            {
//                                strExprTxt = strExprTxt.Replace(varName, varName.Replace(".", "_"));
//                            }
//                            expression.ExpressionText = strExprTxt;
//                            srcFieldVal = exprEngine.Eval(expression, exprCtx);
//                            break;
//                        default:
//                            var srcField = sourceForm.GetField(map.SrcFieldId);
//                            if (srcField == null) continue;
//                            var currentEntry = activeEntry;
//                            if (srcField.IsBillHeadField) { currentEntry = sourceEntity; }
//                            srcFieldVal = srcField.DynamicProperty.GetValue(currentEntry);
//                            break;
//                    }
//                    field.DynamicProperty?.SetValue(entry, srcFieldVal);
//                }

//                var mustFieldIds = new[] { "fmaterialid", "funitid", "fbizunitid", "fstockstatus" };
//                var mustFields = targetForm.GetFieldList()
//                                           .Where(x => mustFieldIds.Contains(x.Id))
//                                           .ToList();

//                foreach (var mustField in mustFields)
//                {
//                    var value = Convert.ToString(mustField.DynamicProperty.GetValue(entry));
//                    if (false == string.IsNullOrWhiteSpace(value))
//                    {
//                        continue;
//                    }

//                    //检查是否已配置了必录字段的映射
//                    var map = cvtRule.FieldMappings.FirstOrDefault(x => x.Id.EqualsIgnoreCase(mustField.Id));
//                    if (map == null)
//                    {
//                        throw new BusinessException($"出现货自动预留失败,没有配置{mustField.Caption}的字段映射!");
//                    }

//                    var srcField = sourceForm.GetField(map.SrcFieldId);
//                    if (srcField == null)
//                    {
//                        throw new BusinessException($"出现货自动预留失败,没有配置错误，{sourceForm.Caption}没有{map.SrcFieldId}字段!");
//                    }

//                    throw new BusinessException($"出现货自动预留失败,{sourceForm.Caption}[{fsourcebillno}]的{srcField.Caption}不能为空!");
//                }

//                entry["fsourceentryid"] = Convert.ToString(activeEntry["id"]);
//                var qtyField = sourceForm.GetField("fqty");
//                if (qtyField == null)
//                {
//                    throw new BusinessException($"出现货自动预留失败,{sourceForm.Caption}没有{qtyField.Caption}字段!");
//                }
//                var qty = Convert.ToDecimal(qtyField.DynamicProperty.GetValue(activeEntry));
//                if (qty <= 0)
//                {
//                    throw new BusinessException($"出现货自动预留失败,{sourceForm.Caption}[{fsourcebillno}]的{qtyField.Caption}不能小于等于0!");
//                }
//                entry["fqty"] = activeEntry["fqty"];
//                var reserveDateToValue = reserveDateToField.DynamicProperty.GetValue(reserveDateToField.IsBillHeadField ? sourceEntity : activeEntry);
//                var dateValue = reserveDateToValue == null ? DateTime.MinValue : Convert.ToDateTime(reserveDateToValue);
//                if (dateValue == DateTime.MinValue || dateValue == DateTime.MaxValue)
//                {
//                    throw new BusinessException($"出现货自动预留失败,{sourceForm.Caption}[{fsourcebillno}]的{reserveDateToField.Caption}不能为空值!");
//                }
//                entry["freservedateto"] = reserveDateToField.DynamicProperty.GetValue(reserveDateToField.IsBillHeadField ? sourceEntity : activeEntry);
//            }

//            targetEntity["freserveobjecttype"] = "ydj_customer";
//            var customerValue = customerField.DynamicProperty.GetValue(sourceEntity);
//            if (customerValue == null)
//            {
//                throw new BusinessException($"出现货自动预留失败,{sourceForm.Caption}[{fsourcebillno}]的{customerField.Caption}不能为空值!");
//            }
//            targetEntity["freserveobjectid"] = customerValue;
//            targetEntity["fsourcetype"] = sourceForm.Id;
//            targetEntity["fsourcenumber"] = fsourcebillno;

//            return targetEntity;
//        }

//        /// <summary>
//        /// 检查自动预留接口参数
//        /// </summary>
//        /// <param name="userContext"></param>
//        /// <param name="htmlForm"></param>
//        /// <param name="entryKey"></param>
//        /// <param name="customerId"></param>
//        /// <param name="reserveDateToId"></param>
//        private void checkAutoReserveArgs(UserContext userContext,
//                                          HtmlForm htmlForm,
//                                          string entryKey,
//                                          string customerId,
//                                          string reserveDateToId,
//                                          out HtmlEntryEntity entryEntity,
//                                          out HtmlField customerField,
//                                          out HtmlField reserveDateToField)
//        {
//            if (userContext == null)
//            {
//                throw new BusinessException("出现货自动预留失败,当前用户上下文不能为空!");
//            }
//            if (htmlForm == null)
//            {
//                throw new BusinessException("出现货自动预留失败,当前表单不能为空");
//            }
//            if (string.IsNullOrWhiteSpace(entryKey))
//            {
//                throw new BusinessException("出现货自动预留失败,当前明细id不能为空");
//            }
//            entryEntity = htmlForm.GetEntryEntity(entryKey);
//            if (entryEntity == null)
//            {
//                throw new BusinessException($"出现货自动预留失败,{htmlForm.Caption}不存在{entryKey}明细!");
//            }
//            if (string.IsNullOrWhiteSpace(customerId))
//            {
//                throw new BusinessException("出现货自动预留失败,客户字段不能为空!");
//            }
//            customerField = htmlForm.GetField(customerId);
//            if (customerField == null)
//            {
//                throw new BusinessException($"出现货自动预留失败,{htmlForm.Caption}不存在{customerId}字段!");
//            }
//            if (false == customerField.IsBillHeadField)
//            {
//                throw new BusinessException($"出现货自动预留失败,{customerField.Caption}不是{htmlForm.Caption}的表头字段!");
//            }
//            if (string.IsNullOrWhiteSpace(reserveDateToId))
//            {
//                throw new BusinessException("出现货自动预留失败,预留日期字段不能为空!");
//            }
//            reserveDateToField = htmlForm.GetField(reserveDateToId);
//            if (reserveDateToField == null)
//            {
//                throw new BusinessException($"出现货自动预留失败,{htmlForm.Caption}不存在{reserveDateToField}字段!");
//            }
//            if (false == reserveDateToField.IsBillHeadField && false == reserveDateToField.EntityKey.EqualsIgnoreCase(entryKey))
//            {
//                throw new BusinessException($"出现货自动预留失败,{reserveDateToField.Caption}既不是{htmlForm.Caption}的表头字段也不是{entryEntity.Caption}的字段!");
//            }
//        }
//    }
//}
