using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.Core.Metadata;
using JieNor.AMS.YDJ.Store.AppService.Clients.Ewc.AdvancedAPIs;
using JieNor.Framework.DataTransferObject.BPM;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.BizState;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.OperationService
{
    /// <summary>
    /// 发送企业微信消息
    /// </summary>
    [InjectService("SendQywxMessage")]
    public class SendQywxMessage : AbstractOperationService
    {
        ///// <summary>
        ///// 操作名称
        ///// </summary>
        //protected override string OperationName { get { return ""; } }

        ///// <summary>
        ///// 权限项
        ///// </summary>
        //protected override string PermItem { get { return ""; } }

        /// <summary>
        /// 业务表单
        /// </summary>
        protected HtmlForm HtmlForm => this.OperationContext.HtmlForm;

        /// <summary>
        /// 操作执行过程
        /// </summary>
        /// <param name="dataEntities">数据包</param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var msgtype = this.OperationContext.Option.GetVariableValue("msgtype", "text");
            var useridsStr = this.OperationContext.Option.GetVariableValue("userids", "");
            var msgtitle = this.OperationContext.Option.GetVariableValue("msgtitle", "");
            var msgcontent = this.OperationContext.Option.GetVariableValue("msgcontent", "");
            var msglinkurl=this.OperationContext.Option.GetVariableValue("msglinkurl", "");

            var userids = useridsStr.Split(new string[] {","}, StringSplitOptions.RemoveEmptyEntries);

            switch (msgtype.ToLowerInvariant())
            {
                case "miniprogram_notice":
                    MassApi.EwcSendMiniProgramMsgByUserIdsAsync(this.UserCtx, msgtitle, msgcontent, null, msglinkurl, userids);
                    break;
                case "text":
                    MassApi.EwcSendTextMsgByUserIdsAsync(this.UserCtx, $"【{msgtitle }】\n{msgcontent}", userids);
                    break;
                default:
                    throw new BusinessException($"不支持msgtype【{msgtype}】类型消息通知！");

            }

            this.OperationContext.Result.IsSuccess = true;
        }
    }
}
