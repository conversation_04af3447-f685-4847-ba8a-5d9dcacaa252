using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.Core.Metadata;

namespace JieNor.AMS.YDJ.Store.AppService.OperationService
{
    /// <summary>
    /// 获取文件详情
    /// </summary>
    [InjectService("GetFileDetail")]
    public class GetFileDetail : AbstractOperationService
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName { get { return ""; } }

        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem { get { return ""; } }

        /// <summary>
        /// 业务表单
        /// </summary>
        protected HtmlForm HtmlForm => this.OperationContext.HtmlForm;

        /// <summary>
        /// 操作执行过程
        /// </summary>
        /// <param name="dataEntities">数据包</param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                throw new BusinessException($"当前数据未保存！");
            }
            var dataEntitie = dataEntities.First();

            var fieldKey = this.GetQueryOrSimpleParam<string>("fieldKey");
            var fileId = this.GetQueryOrSimpleParam<string>("fileId");
            if (fieldKey.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentException("参数 fieldKey 为空，请检查！");
            }
            if (fileId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentException("参数 fileId 为空，请检查！");
            }
            var field = this.HtmlForm.GetField(fieldKey) as HtmlSynergyFileField;
            if (field == null)
            {
                throw new BusinessException($"字段 {fieldKey} 在模型中不存在，请检查！");
            }

            List<string> lstNonvisibleFields = new List<string>();
            if (this.HtmlForm.EnableRAC)
            {
                var permService = this.Container.GetService<IPermissionService>();
                lstNonvisibleFields = permService.GetInvisibleFieldByFormId(this.UserCtx, this.HtmlForm.Id)?.ToList() ?? new List<string>();
            }
            var havePerm = false;
            if (!lstNonvisibleFields.Contains(field.Id, StringComparer.OrdinalIgnoreCase))
            {
                havePerm = true;
            }
            if (!havePerm)
            {
                throw new BusinessException($"您没有【{field.Caption}】字段的查看权限！");
            }

            var cooCompanyId = string.Empty;
            var cooProductId = string.Empty;

            //尝试从客户或供应商中获取协同信息
            var controlField = this.HtmlForm.GetField(field.ControlFieldKey) as HtmlBaseDataField;
            var controlFieldVal = controlField?.DynamicProperty?.GetValue<string>(dataEntitie);
            if (!controlFieldVal.IsNullOrEmptyOrWhiteSpace())
            {
                var refForm = controlField.RefHtmlForm(this.UserCtx);
                if (refForm != null)
                {
                    var dm = this.GetDataManager();
                    dm.InitDbContext(this.UserCtx, refForm.GetDynamicObjectType(this.UserCtx));
                    switch (refForm.Id.ToLower())
                    {
                        case "ydj_customer":
                        case "ydj_supplier":
                            var synObj = dm.Select(controlFieldVal) as DynamicObject;
                            if (synObj != null)
                            {
                                cooCompanyId = synObj["fcoocompanyid"] as string;
                                cooProductId = synObj["fcooproductid"] as string;
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
            if (cooCompanyId.IsNullOrEmptyOrWhiteSpace() || cooProductId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"协同关系不正确，无法获取对方文件信息！");
            }

            //向对方发送协同请求
            var response = this.Gateway.Invoke(
                this.UserCtx,
                new TargetSEP(cooCompanyId, cooProductId),
                new CommonBillDTO()
                {
                    FormId = this.HtmlForm.Id,
                    OperationNo = "GetSynFileDetail",
                    BillData = "",
                    ExecInAsync = false,
                    AsyncMode = (int)Enu_AsyncMode.Background,
                    SimpleData = new Dictionary<string, string> { { "fileId", fileId } }
                }.SetOptionFlag((long)Enu_OpFlags.TPSRequest)
            ) as CommonBillDTOResponse;
            response?.OperationResult?.ThrowIfHasError(true, $"发送协同失败，对方系统未返回任何响应！");

            var srvDataStr = response?.OperationResult?.SrvData as string;
            var srvData = srvDataStr.FromJson<List<Dictionary<string, string>>>();

            this.OperationContext.Result.IsSuccess = true;
            this.OperationContext.Result.SrvData = srvData;
        }
    }
}
