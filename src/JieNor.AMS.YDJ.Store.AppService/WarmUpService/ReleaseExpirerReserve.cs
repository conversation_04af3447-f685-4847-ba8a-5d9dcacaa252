//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;
//using JieNor.Framework.Interface;
//using JieNor.Framework.IoC;
//using JieNor.Framework.Interface.Log;
//using JieNor.AMS.YDJ.Core.Interface.StockReserve;
//using JieNor.Framework;

//namespace JieNor.AMS.YDJ.Store.AppService.WarmUpService
//{
//    /// <summary>
//    /// 通过热身服务释放过期预留单
//    /// </summary>
//    [InjectService]
//    public class ReleaseExpirerReserve : IWarmUpService
//    {
//        /// <summary>
//        /// 日志服务
//        /// </summary>
//        [InjectProperty]
//        private ILogServiceEx LogEx { get; set; }

//        /// <summary>
//        /// 执行热身服务逻辑
//        /// </summary>
//        /// <param name="userCtx"></param>
//        public void Execute(UserContext userCtx)
//        {
//            if (!CheckEnableTask())
//            {
//                return;
//            }
             
//            var reserveService = userCtx.Container.GetService<IReserveReleaseService>();

//            var allBills = reserveService.GetExpirerReserveBills(userCtx);
//            if (allBills == null || allBills.Count == 0)
//            { 
//                return;
//            }

//            var grpOrgs = allBills.GroupBy(f => f["fmainorgid"].ToString()).ToList();
//            foreach (var item in grpOrgs)
//            {
//                //注意，实现类里面有全局变量，每个组织的相关参数不一样，要一个个组织的创建服务
//                var ctx = userCtx.CreateAgentDBContext(item.Key);
//                var svc = ctx.Container.GetService<IReserveReleaseService>();
//                var result = svc.ReleaseExpirer(ctx, item.ToList() ); 
//            }

//            this.LogEx.Info("过期预留单通过【热身服务】释放完毕。");
//        }


//        /// <summary>
//        /// 验证是否开启计划任务
//        /// </summary>
//        private bool CheckEnableTask()
//        {
//            var value = "".GetAppConfig(HostConfigKeys.MS.Site_EnableTask);
//            // 配置为空时，表示开启
//            var enableTask = value.IsNullOrEmptyOrWhiteSpace() || value.EqualsIgnoreCase("true");

//            return enableTask;
//        }
//    }
//}