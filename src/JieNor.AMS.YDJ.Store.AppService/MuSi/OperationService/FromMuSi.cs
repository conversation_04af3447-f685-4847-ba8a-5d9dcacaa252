using System;
using System.Collections.Generic;
using System.Diagnostics.Eventing.Reader;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Transactions;
using Autofac.Features.Metadata;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.Store.AppService.Clients;
using JieNor.AMS.YDJ.Store.AppService.Clients.Ewc;
using JieNor.AMS.YDJ.Store.AppService.MuSi.DataTransferObject;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.MetaCore;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface.SystemIntegration;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.OperationService
{
    /// <summary>
    /// 从慕思同步
    /// </summary>
    [InjectService("FromMuSi")]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public class FromMuSi : AbstractOperationService
    {
        /// <summary>
        /// 系统集成插件代理
        /// </summary>
        protected List<ISyncDataFromMuSiPlugIn> PlugIns { get; set; }

        #region 服务

        /// <summary>
        /// 日志服务
        /// </summary>
        [InjectProperty]
        protected ILogServiceEx Logger { get; set; }

        /// <summary>
        /// 系统集成服务
        /// </summary> 
        [InjectProperty]
        protected ISystemIntegrationService SystemIntegrationService { get; set; }

        /// <summary>
        /// 慕思服务
        /// </summary>
        [InjectProperty]
        protected IMuSiService MuSiService { get; set; }

        /// <summary>
        /// 慕思服务
        /// </summary>
        [InjectProperty]
        protected IComboDataService ComboDataService { get; set; }

        [InjectProperty]
        protected ISequenceService SequenceService { get; set; }

        #endregion

        /// <summary>
        /// 外部应用动态对象
        /// </summary>
        protected DynamicObject ExtAppObj { get; set; }

        /// <summary>
        /// 映射
        /// </summary>
        protected DynamicObject FieldMapObj { get; set; }

        /// <summary>
        /// 服务前置条件
        /// </summary>
        protected string Condition { get; set; }

        /// <summary>
        /// 操作日志表单模型
        /// </summary>
        protected HtmlForm OpLogForm { get; set; }

        /// <summary>
        /// 操作日志
        /// </summary>

        protected DynamicObject OpLogObj { get; set; }

        /// <summary>
        /// 表单模型
        /// </summary>
        protected HtmlForm HtmlForm => this.OperationContext.HtmlForm;

        /// <summary>
        /// 操作选项
        /// </summary>
        protected OperateOption Option => this.OperationContext.Option;

        /// <summary>
        /// 用户上下文
        /// </summary>
        protected UserContext Context => this.UserCtx;

        /// <summary>
        /// 操作结果
        /// </summary>
        protected IOperationResult Result => this.OperationContext.Result;

        protected MuSiMerchantClient Client { get; set; }

        #region 缓存数据

        /// <summary>
        /// 实体主键字段
        /// </summary>
        private Dictionary<string, List<Tuple<HtmlField, DynamicObject>>> dicEntityPkFields =
            new Dictionary<string, List<Tuple<HtmlField, DynamicObject>>>(StringComparer.OrdinalIgnoreCase);

        /// <summary>
        /// 辅助资料缓存
        /// </summary>
        private Dictionary<string, List<BaseDataSummary>> dicComboDatas = new Dictionary<string, List<BaseDataSummary>>(StringComparer.OrdinalIgnoreCase);

        /// <summary>
        /// 基础资料缓存
        /// dicBaseDatas[formId][fnumber]
        /// </summary>
        private Dictionary<string, Dictionary<string, BaseDataSummary>> dicBaseDatas = new Dictionary<string, Dictionary<string, BaseDataSummary>>(StringComparer.OrdinalIgnoreCase);

        #endregion

        /// <summary>
        /// 扩展参数
        /// </summary>
        protected JObject ExtendParams { get; set; }

        protected override void InitializeService()
        {
            base.InitializeService();

            //this.Option.SetIgnoreOpLogFlag();

            this.ResetSysAdminUserContext();

            //不启用并发控制
            this.ServiceControlOption.EnableDistributedLock = false;

            this.SetTaskProgress(1, $"正在初始化数据同步服务，请稍等...");

            var fieldMapObj = this.GetQueryOrSimpleParam<DynamicObject>("__fieldMapObj__");
            if (fieldMapObj == null)
            {
                string servicePara = this.Option.GetVariableValue("servicePara", "");
                //服务参数
                if (servicePara.IsNullOrEmptyOrWhiteSpace()) return;
                var jsonPara = JObject.Parse(servicePara);
                if (jsonPara == null) return;

                //服务配置
                var jsonSerConfig = Convert.ToString(jsonPara["serConfig"]);
                if (jsonSerConfig.IsNullOrEmptyOrWhiteSpace()) return;
                var joConfig = JObject.Parse(jsonSerConfig);

                //服务前置条件
                this.Condition = jsonPara.GetJsonValue("condition", "");

                var extAppId = joConfig["extAppId"]?.ToString();
                if (extAppId.IsNullOrEmptyOrWhiteSpace())
                {
                    extAppId = this.GetQueryOrSimpleParam<string>("extAppId");
                }

                string billMapId = joConfig["billMapId"]?.ToString();
                if (billMapId.IsNullOrEmptyOrWhiteSpace())
                {
                    billMapId = this.GetQueryOrSimpleParam<string>("billMapId");
                }

                this.FieldMapObj = fieldMapObj = this.Context.LoadBizDataById("si_musibizobjmap", billMapId);
                this.ExtAppObj = this.MuSiService.GetExternalAppObject(this.Context, extAppId);
            }
            else
            {
                this.Condition = Convert.ToString(fieldMapObj["ffilterstring"]);
                this.FieldMapObj = fieldMapObj;
                this.ExtAppObj = this.MuSiService.GetExternalAppObject(this.Context, Convert.ToString(fieldMapObj["fextappid"]));
            }

            this.OpLogForm = this.MetaModelService.LoadFormModel(this.Context, "si_operationlog");

            try
            {
                var fextendparam = Convert.ToString(this.FieldMapObj["fextendparam"]);
                if (fextendparam.IsNullOrEmptyOrWhiteSpace()) return;

                this.ExtendParams = JObject.Parse(fextendparam);
            }
            catch
            {
                this.ExtendParams = new JObject();
            }
        }

        /// <summary>
        /// 重置为系统管理员
        /// </summary>
        private void ResetSysAdminUserContext()
        {
            UserAuthTicket session = new UserAuthTicket();

            // 用系统预设的管理员身份操作
            session.UserId = "sysadmin";
            session.DisplayName = "系统管理员";
            session.UserName = "系统管理员";

            session.Product = this.Context.Product;
            session.Company = this.Context.Company;
            session.BizOrgId = this.Context.Company;
            session.TopCompanyId = this.Context.TopCompanyId;
            session.ParentCompanyId = this.Context.ParentCompanyId;
            session.Companys = this.Context.Companys.ToList();
            session.Id = this.Context.Id;

            this.OperationContext.UserContext.SetUserSession(session);
        }

        protected override void BeforeExecute(ref DynamicObject[] dataEntities)
        {
            base.BeforeExecute(ref dataEntities);

            this.OpCtlParam.DisableTransaction = true;
        }

        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            this.Result.MsgStyle = (int)Enu_MsgStyle.Dialog;  //弹窗提示

            var isLocalData = this.Option.TryGetVariableValue("__FromLocalData__", out object _);

            PreSync(this.FieldMapObj, ref dataEntities);

            dataEntities = null;

            this.Result.IsSuccess = true;
            this.Result.Title = "系统提示 - 来自慕思中台";
            this.Result.MsgStyle = (int)Enu_MsgStyle.Dialog;

            var progressMsg = $"正在解析慕思系统服务地址，请稍等...";
            this.SetTaskProgress(5, progressMsg);

            this.Client = new MuSiMerchantClient(this.Context, this.ExtAppObj);

            // 初始化实体主键字段
            InitEntityPkFields(this.FieldMapObj);

            // 初始化集成操作日志
            this.InitOpLog(this.FieldMapObj, isLocalData);

            // 数据同步状态设置
            this.WriteOpLog(progressMsg);

            // 初始化系统集成插件
            this.PlugIns = this.Container.GetService<IEnumerable<Meta<Lazy<ISyncDataFromMuSiPlugIn>>>>()
                .Where(o => o.Metadata.GetString(ServiceMetaKeyConsts.ThirdSystemId).EqualsIgnoreCase("MuSi")
                            && (o.Metadata.GetString(ServiceMetaKeyConsts.FormId).EqualsIgnoreCase(this.HtmlForm.Id)
                                || Regex.IsMatch(this.HtmlForm.Id, $"^{o.Metadata.GetString(ServiceMetaKeyConsts.FormId)}$"))
                            && (o.Metadata.GetString(ServiceMetaKeyConsts.OperationNo).EqualsIgnoreCase("syncfrommusi")))
                .Select(o => o.Value.Value)
                .ToList();

            // 触发插件初始化事件
            this.PlugIns.OfType<AbstractSyncDataFromMuSiPlugIn>()
                .ToList()
                .ForEach(o => o.InitializePlugIn(this.OperationContext, this.FieldMapObj,
                    this.OpLogObj, this.SystemIntegrationService)
                );

            if (isLocalData)
            {
                // 从本地数据同步到当前系统
                dataEntities = this.FromLocalSyncToCurrentSystem(this.FieldMapObj)?.ToArray();
            }
            else
            {
                // 从慕思系统同步到当前系统
                dataEntities = this.FromMuSiSystemSyncToCurrentSystem(this.FieldMapObj)?.ToArray();
            }

            this.SaveOpLog();
            this.SetTaskProgress(100, "任务执行完成，详细信息请看任务执行日志!");

            if (this.Result.IsSuccess && dataEntities.IsNullOrEmpty())
            {
                this.Result.SimpleMessage = "没有数据可同步！";
            }
        }

        /// <summary>
        /// 前置同步
        /// </summary>
        private void PreSync(DynamicObject fieldMapObj, ref DynamicObject[] dataEntities)
        {
            var presyncFormIds = (this.ExtendParams?["__PreSyncFormIds__"] as JArray)?.Select(s => s.ToString());
            if (presyncFormIds.IsNullOrEmpty()) return;

            string extAppId = Convert.ToString(this.ExtAppObj["id"]);
            var waitSecondsPerRequest = this.ExtendParams.GetJsonValue("__WaitSecondsPerRequest__", 30);

            foreach (var formId in presyncFormIds.Distinct())
            {
                var htmlForm = this.MetaModelService.LoadFormModel(this.Context, formId);

                var fieldMapObjs = this.Container.GetService<IMuSiBizObjMapService>().GetBizObjMaps(this.Context,
                    htmlForm, extAppId, Enu_MuSiSyncDir.MuSiToCurrent, Enu_MuSiSyncTimePoint.SyncManual);

                foreach (var item in fieldMapObjs)
                {
                    var result = this.Gateway.InvokeBillOperation(this.Context, htmlForm.Id, new DynamicObject[0], "frommusi", new Dictionary<string, object> { { "__fieldMapObj__", item }, { "IgnoreCheckPermssion", true } });

                    this.Result.MergeResult(result);

                    // 等待 waitSecondsPerRequest 秒
                    Thread.Sleep(waitSecondsPerRequest * 1000);
                }

            }
        }

        /// <summary>
        /// 初始化实体主键字段
        /// </summary>
        /// <param name="fieldMapObj"></param>
        private void InitEntityPkFields(DynamicObject fieldMapObj)
        {
            var fieldEntrys = fieldMapObj["ffieldentry"] as DynamicObjectCollection;
            if (fieldEntrys.IsNullOrEmpty())
            {
                return;
            }

            var pkFieldEntrys = fieldEntrys.Where(s => Convert.ToBoolean(s["fispk"]));

            foreach (var pkFieldEntry in pkFieldEntrys)
            {
                var fmyfieldid = Convert.ToString(pkFieldEntry["fmyfieldid"]);
                if (fmyfieldid.IsNullOrEmptyOrWhiteSpace()) continue;

                // 添加主键
                var fld =
                    fmyfieldid.EqualsIgnoreCase("id") ?
                        new HtmlField("id") { FieldName = "fid", PropertyName = "id", EntityKey = "fbillhead" } :
                        this.HtmlForm.GetField(fmyfieldid);
                if (fld == null) continue;

                if (!dicEntityPkFields.TryGetValue(fld.EntityKey, out var item))
                {
                    item = new List<Tuple<HtmlField, DynamicObject>>();
                    dicEntityPkFields[fld.EntityKey] = item;
                }

                item.Add(Tuple.Create(fld, pkFieldEntry));
            }
        }

        /// <summary>
        /// 从慕思系统同步到当前系统
        /// </summary>
        /// <param name="fieldMapObj"></param>
        private IEnumerable<DynamicObject> FromMuSiSystemSyncToCurrentSystem(DynamicObject fieldMapObj)
        {
            var apiNo = Convert.ToString(fieldMapObj["fapino"]); //接口编码
            var apiName = Convert.ToString(fieldMapObj["fname"]); //接口编码
            if (apiNo.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("慕思业务对象映射配置错误：“接口编码”字段值为空！");
            }

            List<DynamicObject> allDataEntities = new List<DynamicObject>();

            var evaluator = this.Container.GetService<IBizExpressionEvaluator>();
            var bizExpCtx = this.Container.GetService<IBizExpressionContext>();
            bizExpCtx.HtmlForm = this.HtmlForm;
            bizExpCtx.Context = this.Context;

            JsonDynamicDataRow dcRow = new JsonDynamicDataRow();
            bizExpCtx.BindSetField(new TrySetValueHandler(dcRow.TrySetMember));
            bizExpCtx.BindGetField(new TryGetValueHandler(dcRow.TryGetMember));

            var maxSize = Convert.ToInt32(fieldMapObj["fmaxsize"]);
            var batchSize = Convert.ToInt32(fieldMapObj["fbatchsize"]);
            var loopCount = maxSize <= 0 ? int.MaxValue : Convert.ToInt32(Math.Ceiling(maxSize * 1.0M / batchSize));
            var waitSecondsPerRequest = this.ExtendParams.GetJsonValue("__WaitSecondsPerRequest__", 30);

            // 是否回调全部
            var isCallBackAll = this.ExtendParams.GetJsonValue("__IsCallbackAll__", false);

            List<string> allNumbers = new List<string>();
            List<string> successNumbers = new List<string>();

            var numberFld = this.HtmlForm.GetNumberField();
            var tranFld = this.HtmlForm.GetField(HtmlForm.TranFldKey);

            try
            {
                for (int count = 1; count <= loopCount ; count++)
                {
                    this.WriteOpLog($"第{count}轮同步开始...");

                    var result = SendToMuSi(fieldMapObj);

                    this.Result.MergeResult(result);

                    if (!result.IsSuccess)
                    {
                        break;
                    }

                    var data = result.SrvData as JArray;
                    JArray lstValidObjs = new JArray();

                    //如果存在前置条件
                    if (!this.Condition.IsNullOrEmptyOrWhiteSpace())
                    {
                        foreach (JObject item in data)
                        {
                            //执行表达式条件，如果条件不成立，则不执行后续操作
                            dcRow.ActiveDataObject = item;
                            var checkResult = evaluator.CheckCondition(this.Condition, bizExpCtx);
                            if (!checkResult)
                            {
                                continue;
                            }

                            lstValidObjs.Add(item);
                        }
                    }
                    else
                    {
                        lstValidObjs = data;
                    }

                    // 没有数据：结束
                    if ((isCallBackAll && data.IsNullOrEmpty()) || (!isCallBackAll && lstValidObjs.IsNullOrEmpty()))
                    {
                        this.WriteOpLog("没有数据可同步...");
                        break;
                    }

                    var mapItems = DataMapping(fieldMapObj, lstValidObjs);
                    var dataEntities = mapItems.Select(s => s.DataEntity).ToList();
                    // 去重
                    dataEntities = DistinctDataEntities(dataEntities);

                    // 添加编码
                    if (numberFld != null)
                    {
                        allNumbers.AddRange(dataEntities.Select(s => Convert.ToString(s[numberFld.PropertyName])));
                    }
                    else if (tranFld != null)
                    {
                        allNumbers.AddRange(dataEntities.Select(s => Convert.ToString(s[tranFld.PropertyName])));
                    }

                    if (!dataEntities.IsNullOrEmpty())
                    {
                        using (var scope =
                            this.UserCtx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
                        {
                            Save(ref dataEntities, fieldMapObj, data);
                            scope.Complete();
                        }
                    }

                    DoAfterExecute(ref dataEntities);

                    allDataEntities.AddRange(dataEntities);

                    if (!dataEntities.IsNullOrEmpty())
                    {
                        if (numberFld != null)
                        {
                            successNumbers.AddRange(dataEntities.Select(s => Convert.ToString(s[numberFld.PropertyName])));
                        }
                        else if (tranFld != null)
                        {
                            successNumbers.AddRange(dataEntities.Select(s => Convert.ToString(s[tranFld.PropertyName])));
                        }
                    }

                    string dataType = this.Result.SimpleData["data_type"];

                    // 保存到中间表
                    SaveToPullDataTable(mapItems, dataType, dataEntities);

                    // 是否回调全部
                    if (isCallBackAll)
                    {
                        CallBack(data, dataType);
                    }
                    // 否则只返回有效数据
                    else
                    {
                        CallBack(lstValidObjs, dataType);
                    }

                    this.WriteOpLog($"第{count}轮同步结束...");

                    this.WriteOpLog($"每次请求间要等待{waitSecondsPerRequest}秒...");
                    //this.SaveOpLog();
                    Thread.Sleep(waitSecondsPerRequest * 1000);
                }
            }
            catch (Exception ex)
            {
                this.OpLogObj["fdescription"] = $"操作异常：{ex.Message}";
                this.WriteOpLog($"操作异常：{ex.Message} \r\n {ex.StackTrace}", true);

                this.Logger.Error("操作失败！", ex);

                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = $"操作异常：{ex.Message}";

                return null;
            }
            finally
            {
                // 等待若干秒，避免有相同接口配置时，下一个配置无法拉取数据
                Thread.Sleep(waitSecondsPerRequest * 1000);

                successNumbers = successNumbers
                    .Where(s => !s.IsNullOrEmptyOrWhiteSpace())
                    .Distinct().ToList();

                var failNumbers = allNumbers.Except(successNumbers)
                    .Where(s => !s.IsNullOrEmptyOrWhiteSpace())
                     .Distinct().ToList();

                this.OpLogObj["fsuccessnumbers"] = string.Join(",", successNumbers);
                this.OpLogObj["ffailnumbers"] = string.Join(",", failNumbers);
            }

            this.WriteOpLog("操作完成");
            this.OpLogObj["fopstatus"] = "2";

            return allDataEntities;
        }

        /// <summary>
        /// 从本地数据同步到当前系统
        /// </summary>
        /// <param name="fieldMapObj"></param>
        private IEnumerable<DynamicObject> FromLocalSyncToCurrentSystem(DynamicObject fieldMapObj)
        {
            var apiNo = $"/local/{this.HtmlForm.Id}"; //Convert.ToString(fieldMapObj["fapino"]); //接口编码
            var apiName = $"本地：{fieldMapObj["fname"]}"; //Convert.ToString(fieldMapObj["fname"]); //接口编码
            if (apiNo.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("慕思业务对象映射配置错误：“接口编码”字段值为空！");
            }

            List<DynamicObject> allDataEntities = new List<DynamicObject>();

            var evaluator = this.Container.GetService<IBizExpressionEvaluator>();
            var bizExpCtx = this.Container.GetService<IBizExpressionContext>();
            bizExpCtx.HtmlForm = this.HtmlForm;
            bizExpCtx.Context = this.Context;

            JsonDynamicDataRow dcRow = new JsonDynamicDataRow();
            bizExpCtx.BindSetField(new TrySetValueHandler(dcRow.TrySetMember));
            bizExpCtx.BindGetField(new TryGetValueHandler(dcRow.TryGetMember));

            List<string> allNumbers = new List<string>();
            List<string> successNumbers = new List<string>();

            var numberFld = this.HtmlForm.GetNumberField();
            var tranFld = this.HtmlForm.GetField(HtmlForm.TranFldKey);

            try
            {
                this.Option.TryGetVariableValue("__LocalData__", out JArray data);
                if (data.IsNullOrEmpty())
                {
                    return null;
                }

                this.WriteOpLog($"本地数据包：{data.ToJson()}");

                JArray lstValidObjs = new JArray();

                //如果存在前置条件
                if (!this.Condition.IsNullOrEmptyOrWhiteSpace())
                {
                    foreach (JObject item in data)
                    {
                        //执行表达式条件，如果条件不成立，则不执行后续操作
                        dcRow.ActiveDataObject = item;
                        var checkResult = evaluator.CheckCondition(this.Condition, bizExpCtx);
                        if (!checkResult)
                        {
                            continue;
                        }

                        lstValidObjs.Add(item);
                    }
                }
                else
                {
                    lstValidObjs = data;
                }

                // 没有数据：结束
                if (lstValidObjs.IsNullOrEmpty())
                {
                    this.WriteOpLog("没有数据可同步...");
                    return null;
                }

                var mapItems = DataMapping(fieldMapObj, lstValidObjs);
                var dataEntities = mapItems.Select(s => s.DataEntity).ToList();
                // 去重
                dataEntities = DistinctDataEntities(dataEntities);

                // 添加编码
                if (numberFld != null)
                {
                    allNumbers.AddRange(dataEntities.Select(s => Convert.ToString(s[numberFld.PropertyName])));
                }
                else if (tranFld != null)
                {
                    allNumbers.AddRange(dataEntities.Select(s => Convert.ToString(s[tranFld.PropertyName])));
                }

                if (!dataEntities.IsNullOrEmpty())
                {
                    using (var scope =
                        this.UserCtx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
                    {
                        Save(ref dataEntities, fieldMapObj, data);
                        scope.Complete();
                    }
                }

                DoAfterExecute(ref dataEntities);

                allDataEntities.AddRange(dataEntities);

                if (!dataEntities.IsNullOrEmpty())
                {
                    if (numberFld != null)
                    {
                        successNumbers.AddRange(dataEntities.Select(s => Convert.ToString(s[numberFld.PropertyName])));
                    }
                    else if (tranFld != null)
                    {
                        successNumbers.AddRange(dataEntities.Select(s => Convert.ToString(s[tranFld.PropertyName])));
                    }
                }

                successNumbers = successNumbers
                    .Where(s => !s.IsNullOrEmptyOrWhiteSpace())
                    .Distinct().ToList();

                var failNumbers = allNumbers.Except(successNumbers)
                    .Where(s => !s.IsNullOrEmptyOrWhiteSpace())
                    .Distinct().ToList();

                this.OpLogObj["fsuccessnumbers"] = string.Join(",", successNumbers);
                this.OpLogObj["ffailnumbers"] = string.Join(",", failNumbers);

                this.WriteOpLog("操作完成");
                this.OpLogObj["fopstatus"] = "2";

                this.Result.IsSuccess = true;
                this.Result.SimpleMessage = "操作成功";
                this.Result.SrvData = GetSuccessExternalIds(mapItems, dataEntities);
            }
            catch (Exception ex)
            {
                this.OpLogObj["fdescription"] = $"操作异常：{ex.Message}";
                this.WriteOpLog($"操作异常：{ex.Message} \r\n {ex.StackTrace}", true);

                this.Logger.Error("操作失败！", ex);

                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = $"操作异常：{ex.Message}";

                return null;
            }

            return allDataEntities;
        }

        /// <summary>
        /// 执行标准执行后方法
        /// </summary>
        /// <param name="dataEntities"></param>
        private void DoAfterExecute(ref List<DynamicObject> dataEntities)
        {
            this.WriteOpLog("调用标准执行后事件...");
            var asea = new AfterExecuteEventArgs
            {
                DataEntitys = dataEntities,
                Result = this.Result
            };
            this.PlugIns.ForEach(o => o.AfterExecute(asea));

            // 可干预数据包
            dataEntities = asea.DataEntitys.ToList();
        }

        /// <summary>
        /// 发送请求到慕思
        /// </summary>
        private IOperationResult SendToMuSi(DynamicObject fieldMapObj)
        {
            var synctype = Convert.ToString(fieldMapObj["fsynctype"]);//我方同步方式
            IOperationResult result = new OperationResult();
            result.IsSuccess = true;

            this.WriteOpLog("初始化请求数据包...");

            var requestBody = InitRequestBody(fieldMapObj);

            string url = Convert.ToString(fieldMapObj["fapino"]);

            JArray data = null;
            try
            {
                this.WriteOpLog($"请求Url：{this.Client.TargetServer.Host}{url}");
                this.WriteOpLog($"请求Header：{this.Client.TargetServer.Headers.ToJson()}");
                this.WriteOpLog($"请求数据包：{requestBody.ToJson()}");
                var type = Enu_HttpMethod.Post;
                if (synctype.EqualsIgnoreCase("1")) type = Enu_HttpMethod.Get;

                var resp = this.Client.Send<FromMuSiSyncResponse>(url, requestBody, type, contentType: Enu_ContentType.FormUrlEncoded);

                this.WriteOpLog($"响应数据包：{resp.ToJson()}");
                this.Logger.Info("请求Url：" + this.Client.TargetServer.Host + url);
                this.Logger.Info("响应数据包：" + resp.ToJson());

                if (!resp.Success)
                {
                    this.WriteOpLog($"向慕思系统发送请求错误：{resp.Msg}", true);

                    result.IsSuccess = false;
                    result.SimpleMessage = $"向慕思系统发送请求错误：{resp.Msg}";
                    return result;
                }
                //var json = "[{\"taxpayer_phone\":\"***********\",\"customer_type\":\"Z001\",\"registration_authority\":\"河南\",\"business_term\":\"长期\",\"business_license_type\":\"有限责任公司\",\"currency_code\":\"CNY\",\"legal_person_id\":\"1743446174989291522\",\"bank\":\"中国邮政储蓄银行股份有限公司北京昌平区龙域中路支行\",\"update_time\":\"2024-01-25 22:13:39\",\"person_liable_phone\":\"***********\",\"e_mail\":\"<EMAIL>\",\"credit_code\":\"91610136MAB0QXTC05\",\"bill_type\":\"1\",\"id\":\"1743446174989291521\",\"if_enabled\":0,\"pay_taxes_type\":\"0\",\"bank_account\":\"****************\",\"legal_person_contact\":\"***********\",\"bank_code\":\"************\",\"create_user_id\":\"1509351629139808257\",\"create_time\":\"2024-01-08 14:07:43\",\"shop_status\":0,\"register_date\":\"2021-02-23 00:00:00\",\"overseas_flag\":0,\"registered_capital\":\"25636\",\"person_liable_name\":\"咯咯\",\"update_user_name\":\"韦芳丹\",\"status_explain\":\"有效\",\"payment_condition\":\"Z013\",\"declaration_form_id\":\"1743445503086956546\",\"license_number\":\"1013140\",\"boss_name\":\"陈玉娣\",\"business_scope\":\"河南\",\"first_administrative_region_code\":\"\",\"country_code\":\"CN\",\"bank_account_name\":\"河南威泽佳环保科技有限公司\",\"business_license_name\":\"河南威裕佳环保科技有限公司\",\"business_license_address\":\"陕西省西安市浐猿生态区百柳路62号\",\"tax_type\":\"1\",\"legal_person_name\":\"夏燕\",\"customer_pricing\":\"1\",\"boss_id\":\"1399283431103447042\",\"reconciliation_account\":\"********\",\"certificates_rec_address\":\"河南\",\"shipment_type\":\"01\"}]";
                //data = JArray.Parse(json);
                data = resp.Data.List;

                // 保存data_type，用于后续的回调
                result.SimpleData["data_type"] = resp.Data.Data_Type;
            }
            catch (Exception ex)
            {
                this.Logger.Error("向慕思系统发送请求异常", ex);
                this.WriteOpLog($"向慕思系统发送请求异常：{ex.Message} \t\n {ex.StackTrace}", true);

                result.IsSuccess = false;
                result.SimpleMessage = $"向慕思系统发送请求异常：{ex.Message}";
                return result;
            }

            result.SrvData = data;

            return result;
        }

        /// <summary>
        /// 保存到拉取数据中间表
        /// </summary>
        private void SaveToPullDataTable(List<MapItem> mapItems, string dataType, IEnumerable<DynamicObject> successBizObjs)
        {
            var metaModelService = this.Context.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "ms_pulldata");

            var dt = htmlForm.GetDynamicObjectType(this.Context);
            var dm = this.Context.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, dt);

            List<DynamicObject> dynObjs = new List<DynamicObject>();

            var numberFld = this.HtmlForm.GetNumberField();

            foreach (var item in mapItems)
            {
                var dynObj = (DynamicObject)dt.CreateInstance();
                string id = Convert.ToString(item.DataEntity["id"]);
                string json = item.Data.ToJson();
                dynObj["fjson"] = json;
                dynObj["fmd5"] = SecurityUtil.HashString(json);
                dynObj["fdatatype"] = dataType;
                dynObj["fcreatedate"] = BeiJingTime.Now;
                dynObj["ftranid"] = item.Data.GetJsonValue("id", " ");
                dynObj["fbizformid"] = this.HtmlForm.Id;
                dynObj["fbizobjno"] = numberFld != null ? item.DataEntity[numberFld.PropertyName] : " ";
                dynObj["fbizobjid"] = item.DataEntity["id"];

                var successBizObj = successBizObjs.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(id));
                dynObj["fopstatus"] = successBizObj != null ? "1" : "0";

                dynObjs.Add(dynObj);
            }

            var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
            prepareSaveDataService.PrepareDataEntity(this.Context, htmlForm, dynObjs.ToArray(), this.Option);

            this.Context.SaveBizData(htmlForm.Id, dynObjs);
        }

        /// <summary>
        /// 获取操作成功的外部id
        /// </summary>
        /// <param name="mapItems"></param>
        /// <param name="dataType"></param>
        /// <param name="successBizObjs"></param>
        /// <returns></returns>
        private List<string> GetSuccessExternalIds(List<MapItem> mapItems, IEnumerable<DynamicObject> successBizObjs)
        {
            List<string> successExternalIds = new List<string>();

            foreach (var item in mapItems)
            {
                var id = Convert.ToString(item.DataEntity["id"]);

                var successBizObj = successBizObjs.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(id));
                if (successBizObj != null)
                {
                    successExternalIds.Add(item.Data.GetJsonValue("id", " "));
                }
            }

            return successExternalIds;
        }

        /// <summary>
        /// 初始化请求Body
        /// </summary> 
        /// <param name="fieldMapObj"></param>
        protected Dictionary<string, string> InitRequestBody(DynamicObject fieldMapObj)
        {
            var requestBody = new Dictionary<string, string>();

            var paramEntrys = fieldMapObj["fparamentry"] as DynamicObjectCollection;
            if (paramEntrys.IsNullOrEmpty()) return requestBody;

            foreach (var paramEntry in paramEntrys)
            {
                var paramId = Convert.ToString(paramEntry["fparamid"]);
                var paramSrc = Convert.ToString(paramEntry["fparamsrc"]);
                var paramValue = Convert.ToString(paramEntry["fparamvalue"]);
                var paramType = Convert.ToString(paramEntry["fparamtype"]);
                var paramFormat = Convert.ToString(paramEntry["fparamformat"]);

                if (paramSrc.EqualsIgnoreCase("currbegindate"))
                {
                    paramFormat = string.IsNullOrWhiteSpace(paramFormat) ? "yyyy-MM-dd 00:00:00" : paramFormat;
                    requestBody[paramId] = DateTime.Now.ToString(paramFormat);

                    continue;
                }
                if (paramSrc.EqualsIgnoreCase("currenddate"))
                {
                    paramFormat = string.IsNullOrWhiteSpace(paramFormat) ? "yyyy-MM-dd 23:59:59" : paramFormat;
                    requestBody[paramId] = DateTime.Now.ToString(paramFormat);
                    continue;
                }
                if (paramSrc.EqualsIgnoreCase("currbefore"))
                {
                    int.TryParse(paramValue, out var intDays);
                    if (intDays > 0) intDays = intDays * -1;
                    paramFormat = string.IsNullOrWhiteSpace(paramFormat) ? "yyyy-MM-dd 00:00:00" : paramFormat;
                    requestBody[paramId] = DateTime.Now.AddDays(intDays).ToString(paramFormat);
                    continue;
                }
                if (paramSrc.EqualsIgnoreCase("currafter"))
                {
                    int.TryParse(paramValue, out var intDays);
                    paramFormat = string.IsNullOrWhiteSpace(paramFormat) ? "yyyy-MM-dd 23:59:59" : paramFormat;
                    requestBody[paramId] = DateTime.Now.AddDays(intDays).ToString(paramFormat);
                    continue;
                }

                if (paramSrc.EqualsIgnoreCase("batchsize"))
                {
                    requestBody[paramId] = Convert.ToString(fieldMapObj["fbatchsize"]);
                    continue;
                }

                switch (paramType)
                {
                    case "int":
                        int.TryParse(paramValue, out var intValue);
                        requestBody[paramId] = intValue.ToString();
                        break;
                    case "decimal":
                        decimal.TryParse(paramValue, out var decimalValue);
                        requestBody[paramId] = decimalValue.ToString();
                        break;
                    case "double":
                        double.TryParse(paramValue, out var doubleValue);
                        requestBody[paramId] = doubleValue.ToString();
                        break;
                    case "float":
                        float.TryParse(paramValue, out var floatValue);
                        requestBody[paramId] = floatValue.ToString();
                        break;
                    case "datetime":
                        string dateTimeValue = string.Empty;
                        if (DateTime.TryParse(paramValue, out var dateTime))
                        {
                            paramFormat = string.IsNullOrWhiteSpace(paramFormat) ? "yyyy-MM-dd" : paramFormat;
                            dateTimeValue = dateTime.ToString(paramFormat);
                        }
                        requestBody[paramId] = dateTimeValue;
                        break;
                    default:
                        requestBody[paramId] = paramValue;
                        break;
                }
            }

            return requestBody;
        }

        /// <summary>
        /// 获取或创建业务对象实体=>来源数据包映射对
        /// </summary>
        /// <param name="sourceBillDatas"></param>
        /// <returns></returns>
        private List<MapItem> GetOrCreateDataEntitySourceBillDataMap(JArray sourceBillDatas)
        {
            if (sourceBillDatas.IsNullOrEmpty())
            {
                return new List<MapItem>();
            }

            // 获取单据头的主键字段
            dicEntityPkFields.TryGetValue(this.HtmlForm.HeadEntity.Id, out var pkFields);

            var dt = this.HtmlForm.GetDynamicObjectType(this.Context);

            // 没有配置主键字段，直接按数据包返回空实体
            if (pkFields.IsNullOrEmpty())
            {
                return sourceBillDatas.Select(s => new MapItem
                {
                    DataEntity = dt.CreateInstance() as DynamicObject,
                    Data = s as JObject
                }).ToList();
            }

            // 获取已存在实体
            List<string> sqls = new List<string>();
            for (int i = 0; i < sourceBillDatas.Count; i++)
            {
                var sourceBillData = (JObject)sourceBillDatas[i];

                string sql = $"select {this.HtmlForm.BillPKFldName}, {i} as fseq from {this.HtmlForm.BillHeadTableName} with(nolock) where 1=1 ";

                if (this.HtmlForm.GetField("fmainorgid") != null)
                {
                    sql += $" and fmainorgid='{this.Context.Company}' ";
                }

                foreach (var pkField in pkFields)
                {
#warning 后缀要扩展基础资料/辅助资料的情况
                    sql += $" and {pkField.Item1.FieldName}='{sourceBillData.GetJsonValue(Convert.ToString(pkField.Item2["fextfieldid"]), "")}'";
                }

                sqls.Add(sql);
            }

            string batchSql = string.Join(" union ", sqls);
            // 获取 数据包行号=>实体主键 映射对
            Dictionary<int, string> dicSeqPk = new Dictionary<int, string>();
            using (var reader = this.DBService.ExecuteReader(this.Context, batchSql))
            {
                while (reader.Read())
                {
                    dicSeqPk[Convert.ToInt32(reader[1])] = Convert.ToString(reader[0]);
                }
            }

            // 获取实体数据包
            var dataEntities = this.Context.LoadBizDataById(this.HtmlForm.Id, dicSeqPk.Values);

            // 创建 实体=>数据包 映射对
            List<MapItem> result = new List<MapItem>();
            for (int i = 0; i < sourceBillDatas.Count; i++)
            {
                var sourceBillData = (JObject)sourceBillDatas[i];
                dicSeqPk.TryGetValue(i, out var pk);

                var dataEntity = dataEntities.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(pk));
                if (dataEntity == null)
                {
                    dataEntity = dt.CreateInstance() as DynamicObject;
                    dataEntity["id"] = SequenceService.GetSequence<string>();

                    result.Add(new MapItem
                    {
                        DataEntity = dataEntity,
                        Data = sourceBillData
                    });
                }
                else
                {
                    var item = result.FirstOrDefault(s => Convert.ToString(s.DataEntity["id"]).EqualsIgnoreCase(pk));
                    if (item == null)
                    {
                        item = new MapItem();
                        result.Add(item);
                    }

                    item.DataEntity = dataEntity;
                    item.Data = sourceBillData;
                }
            }

            return result;
        }

        /// <summary>
        /// 数据映射
        /// </summary>
        /// <param name="fieldMapObj"></param>
        /// <param name="data"></param>
        protected List<MapItem> DataMapping(DynamicObject fieldMapObj, JArray data)
        {
            if (data.IsNullOrEmpty()) return new List<MapItem>();

            var lstDataEntitySourceBillDataMap = GetOrCreateDataEntitySourceBillDataMap(data);

            var fieldEntries = fieldMapObj["ffieldentry"] as DynamicObjectCollection;
            // 按实体分组
            var groups = new Dictionary<string, List<Tuple<DynamicObject, HtmlField>>>(StringComparer.OrdinalIgnoreCase);
            foreach (var fieldEntry in fieldEntries)
            {
                string fmyfieldid = Convert.ToString(fieldEntry["fmyfieldid"]);
                if (fmyfieldid.IsNullOrEmptyOrWhiteSpace()) continue;

                // 添加主键
                var fld =
                    fmyfieldid.EqualsIgnoreCase("id") ?
                        new HtmlField("id") { FieldName = "fid", PropertyName = "id", EntityKey = "fbillhead" } :
                        this.HtmlForm.GetField(fmyfieldid);
                if (fld == null) continue;

                var entityKey = fld.EntityKey.ToLower();

                if (!groups.TryGetValue(entityKey, out var group))
                {
                    group = new List<Tuple<DynamicObject, HtmlField>>();
                    groups[entityKey] = group;
                }

                group.Add(Tuple.Create(fieldEntry, fld));
            }

            // 额外增加单据体映射处理
            foreach (var entryEntity in this.HtmlForm.EntryList)
            {
                var entryEntityKey = entryEntity.Id.ToLower();
                if (!groups.ContainsKey(entryEntityKey))
                {
                    groups.Add(entryEntityKey, new List<Tuple<DynamicObject, HtmlField>>());
                }
            }

            // 按实体分组（单据头/单据体）处理映射
            foreach (var group in groups)
            {
                var entityKey = group.Key;
                var items = group.Value;

                foreach (var dataEntitySourceBillDataMap in lstDataEntitySourceBillDataMap)
                {
                    var dataEntity = dataEntitySourceBillDataMap.DataEntity;
                    var sourceBillData = dataEntitySourceBillDataMap.Data;

                    // 单据头
                    if (entityKey.EqualsIgnoreCase(this.HtmlForm.HeadEntity.Id))
                    {
                        foreach (var item2 in items)
                        {
                            var fieldEntry = item2.Item1;
                            var fld = item2.Item2;

                            FieldMapping(dataEntity, dataEntity, fieldEntry, fld, sourceBillData);
                        }
                    }
                    // 单据体
                    else
                    {
                        var entryEntity = this.HtmlForm.GetEntryEntity(entityKey);

                        //TODO:临时处理，直接交给业务插件自行处理
                        var ea = new EntryFieldMappingEventArgs(dataEntity, sourceBillData, this.HtmlForm, entryEntity, this.Option);
                        this.PlugIns.ForEach(o => o.EntryFieldMapping(ea));
                    }
                }
            }

            return lstDataEntitySourceBillDataMap;
        }

        /// <summary>
        /// 去重
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private List<DynamicObject> DistinctDataEntities(List<DynamicObject> dataEntities)
        {
            List<DynamicObject> result = new List<DynamicObject>();

            // 过滤相同主键的数据
            HashSet<string> keys = new HashSet<string>();
            dicEntityPkFields.TryGetValue(this.HtmlForm.HeadEntity.Id, out var pkFields);
            if (!pkFields.IsNullOrEmpty())
            {
                for (int i = dataEntities.Count - 1; i >= 0; i--)
                {
                    var dataEntity = dataEntities[i];

                    string key = pkFields.Select(s => dataEntity[s.Item1.PropertyName]?.ToString() ?? "").ToJson()
                        .HashString();

                    if (!keys.Contains(key))
                    {
                        result.Insert(0, dataEntity);
                        keys.Add(key);
                    }
                }
            }
            else
            {
                result = dataEntities;
            }

            return result;
        }

        /// <summary>
        /// 字段映射
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="entity"></param>
        /// <param name="fieldEntry"></param>
        /// <param name="fld"></param>
        /// <param name="sourceBillData">来源数据包</param>
        private void FieldMapping(DynamicObject dataEntity, DynamicObject entity, DynamicObject fieldEntry, HtmlField fld, JObject sourceBillData)
        {
            string fextfieldid = Convert.ToString(fieldEntry["fextfieldid"]).Trim();
            string fextconstval = Convert.ToString(fieldEntry["fextconstval"]).Trim();
            string fextfieldexpression = Convert.ToString(fieldEntry["fextfieldexpression"]).Trim();

            var ea = new BeforeFieldMappingEventArgs(dataEntity, entity, fieldEntry, this.Option)
            {
                ExternalData = sourceBillData
            };
            this.PlugIns.ForEach(o => o.BeforeFieldMapping(ea));

            if (ea.Cancel)
            {
                entity[fld.PropertyName] = ea.Result;
                return;
            }

            // 常量
            if (!fextconstval.IsNullOrEmptyOrWhiteSpace())
            {
                entity[fld.PropertyName] = ConvertByHtmlField(fextconstval, fld);
                return;
            }

            // 表达式
            if (!fextfieldexpression.IsNullOrEmptyOrWhiteSpace())
            {
                if (!fextfieldid.IsNullOrEmptyOrWhiteSpace())
                {
                    sourceBillData.TryGetValue(fextfieldid, out var token);

                    fextfieldexpression = fextfieldexpression.Replace("$val$", token?.ToString());
                }

                var exprObj = this.Container.GetService<IExpressionFactory>();
                if (exprObj == null) return;

                IBizExpressionContext exprCtx = this.Container.GetService<IBizExpressionContext>();
                exprCtx.Context = this.UserCtx;

                IBizExpression exprItem = this.Container.GetService<IBizExpression>();
                exprItem.ExpressionText = fextfieldexpression;

                object val = exprObj.Eval(exprItem, exprCtx);

                entity[fld.PropertyName] = ConvertByHtmlField(val?.ToString(), fld);

                return;
            }

            // 字段映射
            if (!fextfieldid.IsNullOrEmptyOrWhiteSpace())
            {
                sourceBillData.TryGetValue(fextfieldid, out var token);

                entity[fld.PropertyName] = ConvertByHtmlField(token?.ToString(), fld);
                return;
            }
        }

        /// <summary>
        /// 根据字段类型转换
        /// </summary>
        /// <param name="value"></param>
        /// <param name="fld"></param>
        /// <returns></returns>
        private object ConvertByHtmlField(string value, HtmlField fld)
        {
            if (value.IsNullOrEmptyOrWhiteSpace()) return null;

            // 简单选择字段
            if (fld is HtmlSimpleSelectField)
            {
                var simpleSelectFld = (HtmlSimpleSelectField)fld;

                var items = simpleSelectFld.GetDropdownItems();

                var item = items.FirstOrDefault(s => s.Value.EqualsIgnoreCase(value) || s.Key.EqualsIgnoreCase(value));

                return item.Key;
            }

            // 辅助资料（下拉列表字段）
            if (fld is HtmlComboField)
            {
                var comboFld = (HtmlComboField)fld;

                string key = $"{comboFld.CategoryFilter}@{comboFld.Filter}";
                if (!dicComboDatas.TryGetValue(key, out var enumDatas))
                {
                    enumDatas = this.ComboDataService.GetComboDatas(this.UserCtx, comboFld.CategoryFilter, comboFld.Filter);
                    dicComboDatas[key] = enumDatas;
                }
                //返回的省市区改为模糊匹配
                var enumData = enumDatas.FirstOrDefault(s => s.Name.IndexOf(value) > -1 || s.Id.IndexOf(value) > -1);

                return enumData?.Id;
            }

            // 多选基础资料
            if (fld is HtmlMulBaseDataField)
            {
                var baseDataFld = (HtmlMulBaseDataField)fld;
                var refForm = this.MetaModelService.LoadFormModel(this.Context, baseDataFld.RefFormId);
                // 获取编码获取
                var nos = value.Split(new[] { "," }, StringSplitOptions.RemoveEmptyEntries);

                var dynObjs = this.Context.LoadBizDataByNo(refForm.Id, refForm.GetNumberField().FieldName, nos);

                return string.Join(",", dynObjs?.Select(s => Convert.ToString(s["id"])) ?? new string[] { });
            }

            // 基础资料
            if (fld is HtmlBaseDataField)
            {
                var baseDataFld = (HtmlBaseDataField)fld;
                var refForm = this.MetaModelService.LoadFormModel(this.Context, baseDataFld.RefFormId);
                // 获取编码获取
                var dynObj = this.Context.LoadBizDataByNo(refForm.Id, refForm.GetNumberField().FieldName, new[] { value }).FirstOrDefault();

                return dynObj?["id"];
            }

            if (fld is HtmlDateTimeField)
            {
                return Convert.ToDateTime(value);
            }

            if (fld is HtmlIntegerField)
            {
                int.TryParse(value, out int intValue);
                return intValue;
            }

            if (fld is HtmlDecimalField)
            {
                decimal.TryParse(value, out decimal decimalValue);
                return decimalValue;
            }

            if (fld is HtmlCheckBoxField)
            {
                if (value.EqualsIgnoreCase("0") || value.EqualsIgnoreCase("false"))
                {
                    return false;
                }

                if (value.EqualsIgnoreCase("1") || value.EqualsIgnoreCase("true"))
                {
                    return true;
                }

                throw new InvalidCastException("该字符串未被识别为有效的布尔值。");
            }

            // 辅助属性处理
            if (fld is HtmlAuxPropertyField)
            {
                return null;
            }

            if (fld is HtmlFileField)
            {
                return null;
            }

            return value;
        }

        /// <summary>
        /// 保存至数据库
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="fieldMapObj"></param>
        /// <param name="data"></param>
        protected void Save(ref List<DynamicObject> dataEntities, DynamicObject fieldMapObj, JArray data)
        {
            this.WriteOpLog("调用标准保存前事件...");
            var bsea = new BeforeSaveEventArgs
            {
                DataEntitys = dataEntities,
                Result = this.Result
            };
            this.PlugIns.ForEach(o => o.BeforeSave(bsea));

            if (bsea.Cancel)
            {
                this.WriteOpLog("调用标准保存前事件，取消标准保存操作...");
                return;
            }

            // 可干预数据包
            dataEntities = bsea.DataEntitys.ToList();

            if (dataEntities.IsNullOrEmpty())
            {
                this.WriteOpLog("没有数据需要执行保存...");
                return;
            }

            this.WriteOpLog("调用标准保存操作...");

            var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, dataEntities, "save", new Dictionary<string, object>
            {
                // 不刷编码
                { "NotRefreshNumber", true }, 
                // 返回单据唯一性校验的详细错误消息
                { "IsReturnBillUniqueValidationDetailErrorMessage", true },
                 { "IgnoreCheckPermssion", true }
            });

            // 全部成功或部分成功：过滤成功的
            if (result.SimpleData.TryGetValue("pkids", out string strPkids) && !strPkids.IsNullOrEmptyOrWhiteSpace())
            {
                var pkids = strPkids.FromJson<List<string>>();

                dataEntities = dataEntities.Where(s => pkids.Contains(Convert.ToString(s["id"]))).ToList();
                //WriteLog(dataEntities); 
            }
            // 全部失败：清空
            else
            {
                dataEntities = new List<DynamicObject>();
            }

            this.WriteOpLog("调用标准保存后事件...");
            var asea = new AfterSaveEventArgs
            {
                DataEntitys = dataEntities,
                Result = this.Result
            };
            this.PlugIns.ForEach(o => o.AfterSave(asea));

            // 可干预数据包
            dataEntities = asea.DataEntitys.ToList();

            // 避免因保存后事件报错，导致记录了错误结果
            this.WriteOpLog("保存结果：" + result.ToString());
            this.Result.MergeResult(result);
        }

        private void WriteLog(List<DynamicObject> dataEntities) 
        {
            if (!dataEntities.Any()) return;
            foreach (var dataEntitie in dataEntities) 
            {
                this.Logger.SyncBatchWriteLog(this.Context, new LogEntry[] {
                new LogEntry
                {
                    BillIds = dataEntitie["id"] as string,
                    BillNos = dataEntitie["fnumber"] as string,
                    BillFormId = this.HtmlForm.Id,
                    OpName = "主数据拉取",
                    OpCode = "syncfrommusi",
                    Content = "执行了主数据拉取操作",
                    DebugData = "执行了主数据拉取操作",
                    Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                    Level = Enu_LogLevel.Info.ToString(),
                    LogType = Enu_LogType.RecordType_03
                }
            });
            } 
        }

        protected void CallBack(JArray array, string dataType)
        {
            try
            {
                if (array.IsNullOrEmpty())
                {
                    this.WriteOpLog("无需回调...");
                    //this.SaveOpLog();
                    return;
                }

                var externalIds = array.Select(s => s.GetJsonValue("id", ""));

                var requestData = new Dictionary<string, string>();
                requestData["dataType"] = dataType;
                requestData["version"] = BeiJingTime.Now.TimestampFromBeiJingTime().ToString();
                requestData["systemCode"] = "200";

                requestData["dataIds"] = string.Join(",", externalIds);

                string url = "/datasync/asynchronous/notice";

                this.WriteOpLog($"回调Url：{this.Client.TargetServer.Host}{url}");
                this.WriteOpLog($"回调Header：{this.Client.TargetServer.Headers.ToJson()}");
                this.WriteOpLog($"回调请求参数：{requestData.ToJson()}");

                var resp = this.Client.Send<JObject>(url, requestData, contentType: Enu_ContentType.FormUrlEncoded);

                this.WriteOpLog($"回调响应数据：{resp.ToJson()}");
                this.Logger.Info("请求Url：" + this.Client.TargetServer.Host + url);
                this.Logger.Info("回调响应数据：" + resp.ToJson());
                //this.SaveOpLog();
            }
            catch (Exception ex)
            {
                this.Logger.Error("回调失败", ex);
                this.WriteOpLog("回调失败：" + ex.Message + "\t" + ex.StackTrace);
            }
        }

        /// <summary>
        /// 初始化集成操作日志
        /// </summary>
        /// <param name="fieldMapObj"></param>
        private void InitOpLog(DynamicObject fieldMapObj, bool isLocalData)
        {
            var apiNo = isLocalData ? $"/local/{this.HtmlForm.Id}" : Convert.ToString(fieldMapObj["fapino"]); //接口编码
            var apiName = isLocalData ? $"本地接口:{fieldMapObj["fname"]}" : $"外部接口:{fieldMapObj["fname"]}"; //接口编码

            this.OpLogObj = this.SystemIntegrationService.CreateOperationLog(this.Context, this.OpLogForm, apiNo, apiName, this.HtmlForm.Id, "2", "");
        }

        /// <summary>
        /// 写操作日志
        /// </summary>
        /// <param name="logContent"></param>
        /// <param name="isError">是否出错</param>
        private void WriteOpLog(string logContent, bool isError = false)
        {
            if (OpLogObj == null) return;

            this.SystemIntegrationService.WriteOperationLog(this.Context, this.OpLogObj, logContent, isError);
        }

        /// <summary>
        /// 保存同步日志
        /// </summary>
        protected void SaveOpLog()
        {
            if (this.OpLogObj == null) return;

            SystemIntegrationService.SaveOperationLog(this.Context, this.OpLogForm, new[] { this.OpLogObj }, this.Option);

        }

        /// <summary>
        /// 设置进度条信息
        /// </summary>
        /// <param name="value"></param>
        /// <param name="message"></param>
        private void SetTaskProgress(decimal value = 0, string message = "")
        {
            if (value >= 0)
            {
                this.TaskProgressService.SetTaskProgressValue(this.Context, this.TaskId, value);
            }
            this.TaskProgressService.SetTaskProgressMessage(this.Context, this.TaskId, message);
        }

        public class MapItem
        {
            public DynamicObject DataEntity { get; set; }

            public JObject Data { get; set; }
        }
    }
}
