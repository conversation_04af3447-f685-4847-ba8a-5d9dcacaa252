using System.Collections.Generic;
using JieNor.Framework.Consts;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.OperationService
{
    /// <summary>
    /// 慕思通用回调操作，目前主要用于接收慕思系统回调通知操作
    /// </summary>
    [InjectService("MSCallback")]
    public class MSCallback : AbstractOperationService
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName { get { return "慕思通用回调"; } }

        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem { get { return PermConst.PermssionItem_View; } }

        /// <summary>
        /// 忽略基类产生的操作消息
        /// </summary>
        protected override bool IgnoreOpMessage { get { return true; } }

        /// <summary>
        /// 业务表单模型
        /// </summary>
        protected HtmlForm HtmlForm { get { return this.OperationContext.HtmlForm; } }

        /// <summary>
        /// 日志服务
        /// </summary>
        [InjectProperty]
        protected ILogServiceEx Logger { get; set; }

        /// <summary>
        /// 执行操作逻辑
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var numbersStr = this.GetQueryOrSimpleParam("numbers", "");
            var operationNo = this.GetQueryOrSimpleParam("op", "");

            this.OperationContext.Result.IsSuccess = true;
            this.OperationContext.Result.SimpleMessage = $"【{this.HtmlForm.Caption}】回调操作成功！";

            this.OperationContext.Result.SrvData = new Dictionary<string, object>()
            {
                { "flag", "SUCCESS" },
                { "errorMsgs", new string[]{} }
            };
        }
    }
}