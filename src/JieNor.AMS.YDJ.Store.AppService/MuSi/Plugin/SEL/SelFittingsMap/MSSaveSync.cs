using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.SEL.SelFittingsMap
{
    /// <summary>
    /// 选配配件映射：慕思通用保存同步操作
    /// </summary>
    [InjectService]
    [FormId("sel_fittingsmap")]
    [OperationNo("MSSaveSync")]
    public class MSSaveSync : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName)
            {
                case "BeforeConvertBaseData":
                    this.BeforeConvertBaseData(e);
                    break;
                case "AfterPackSourceBill":
                    this.AfterPackSourceBill(e);
                    break;
                case "BeforeSaveSourceBill":
                    this.BeforeSaveSourceBill(e);
                    break;
                case "SourceBillFieldMapping":
                    this.SourceBillFieldMapping(e);
                    break;
                case "BeforeCreateBaseData":
                    this.BeforeCreateBaseData(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// //基础资料字段值转换前事件：可指定当前需要转换的基础资料字段
        /// </summary>
        /// <param name="e"></param>
        private void BeforeConvertBaseData(OnCustomServiceEventArgs e)
        {
            //e.Cancel = true;
            //e.Result = new List<string>
            //{

            //};
        }

        /// <summary>
        /// 来源单据打包后事件：可对打包后的数据包进行处理或者直接覆盖整个数据包都是可以的
        /// </summary>
        /// <param name="e"></param>
        private void AfterPackSourceBill(OnCustomServiceEventArgs e)
        {
            //var dataEntitys = (e.EventData as DynamicObject[])?.ToList();
            //if (dataEntitys == null || dataEntitys.Count < 1) return;

        }

        /// <summary>
        /// 创建基础资料前事件：可更改哪些基础资料不可创建
        /// </summary>
        /// <param name="e"></param>
        private void BeforeCreateBaseData(OnCustomServiceEventArgs e)
        {
            e.Cancel = true;
            e.Result = new List<string>
            {
                "ydj_product"
            };
        }

        /// <summary>
        /// 来源单据保存前事件：可对当前要保存的数据包做处理
        /// </summary>
        /// <param name="e"></param>
        private void BeforeSaveSourceBill(OnCustomServiceEventArgs e)
        {
            var dataEntitys = (e.EventData as DynamicObject[])?.ToList();
            if (dataEntitys == null || dataEntitys.Count < 1) return;
            SetMapData(dataEntitys);
        }

        /// <summary>
        /// 根据传入参数同步选配配件映射数据
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void SetMapData(List<DynamicObject> dataEntitys) {
            var sel_fittingsForm = this.MetaModelService.LoadFormModel(this.Context, "sel_fittingsmap");
            foreach (var dataEntity in dataEntitys)
            {
                if (dataEntity["fcategoryid_d"].IsNullOrEmptyOrWhiteSpace()) 
                {
                    //商品类别 默认为"1001" (床架)
                    var category = this.Context.LoadBizDataByNo("ydj_category", "fnumber", new string[] { "1001" }).FirstOrDefault();
                    dataEntity["fcategoryid_d"] = category?["id"];
                }
                //选配配件-型号明细
                var dimensionmodelentity = dataEntity["fdimensionmodelentity"] as DynamicObjectCollection;
                var sel_fittingsFormEntity = sel_fittingsForm.GetEntryEntity("fdimensionmodelentity");

                dimensionmodelentity.Clear();

                //选配配件-条件明细
                var ffittingsmapentity = dataEntity["ffittingsmapentity"] as DynamicObjectCollection;
                var ffittingsmapFormEntity = sel_fittingsForm.GetEntryEntity("ffittingsmapentity");
                //ffittingsmapentity.Clear();

                string MidDataJson = Convert.ToString(dataEntity?["middatajson"]);
                var MidDataObj = JArray.Parse(MidDataJson);
                foreach (JObject middata in MidDataObj) {
                    var dimensionmodel = new DynamicObject(sel_fittingsFormEntity.DynamicObjectType);
                   // var ffittingsmapmodel = new DynamicObject(ffittingsmapFormEntity.DynamicObjectType);

                    var qty = Convert.ToInt32(middata["fqty"]);
                    if (qty == 0) qty = 1;
                    //如果【按型号匹配】=”是”时, 获取对应中台”产品与配件映射”的dcode (类货品/产品/排骨架编码)来找到对应的《型号》, 通过编码匹配
                    var dcode = Convert.ToString(middata["dcode"]).Trim();

                    var dtype = Convert.ToString(middata["dtype"]).Trim();
                    //配件编码
                    var dpartsnum = Convert.ToString(middata["dpartsnum"]).Trim();
                    var dpartsname = Convert.ToString(middata["dpartsname"]).Trim();
                    var dproductnum = Convert.ToString(middata["dproductnum"]).Trim();
                    var dproductname = Convert.ToString(middata["dproductname"]).Trim();
                    var dwide = Convert.ToString(middata["dwide"]).Trim();
                    var dlang = Convert.ToString(middata["dlang"]).Trim();
                    var dhigh = Convert.ToString(middata["dhigh"]).Trim();
                    var dother = Convert.ToString(middata["dother"]).Trim();
                    var disable = Convert.ToBoolean(middata["fdisable"]);

                    var product = this.Context.LoadBizDataByNo("ydj_product", "fnumber", new string[] { dpartsnum }).FirstOrDefault();
                    var product_new = this.Context.LoadBizDataByNo("ydj_product", "fnumber", new string[] { dproductnum }).FirstOrDefault();
                    //是否允许选配

                    if (product == null)
                    {
                        this.Result.ComplexMessage.ErrorMessages.Add($"编码为{dproductnum}的配件{dpartsnum}商品{dpartsname}未同步！请先同步!");
                        continue;
                    }
                    var fispresetprop = Convert.ToBoolean(product?["fispresetprop"]);
                    var condition = string.Empty;
                    DynamicObject attrinfo = null;
                    var dimension = "";
                    if (dtype == "床箱底板")
                    {
                        //配件类型为 ” 床箱底板”时, 要勾选上【按型号匹配】
                        dataEntity["fmatchbymodel"] = true;
                        //床箱底板且勾选允许选配，则配件辅助属性按以下规则生成，不满足此情况的辅助属性都为空
                        if (fispresetprop)
                        {
                            //处理辅助属性生成，如果没有找到属性、属性值抛出异常
                            attrinfo = DoAuxpropValue(middata);
                        }
                        var sb = new StringBuilder();
                        sb.Append($"\"[床架宽]\"== {dwide} && ");
                        sb.Append($"\"[床架长]\"== {dlang} && ");
                        sb.Append($"\"[床脚高度]\"== {dhigh} && ");
                        sb.Append($"\"[床架其它定制]\"=='{dother}' ");
                        condition = sb.ToString();
                        //构建配件维度-型号明细
                        dimension = dcode;
                    }
                    else if(dtype == "铁架床") {
                        //配件类型为 ” 铁床架”时, 要勾选上【按商品匹配】
                        dataEntity["fmatchbyproduct"] = true;
                        //床箱底板且勾选允许选配，则配件辅助属性按以下规则生成，不满足此情况的辅助属性都为空
                        if (fispresetprop)
                        {
                            //处理辅助属性生成，如果没有找到属性、属性值抛出异常
                            attrinfo = DoAuxpropValue(middata);
                        }
                        var str = new List<string>();
                        var sb = new StringBuilder();
                        if (!dwide.IsNullOrEmptyOrWhiteSpace()) {
                            //sb.Append($"\"[床架宽]\"== {dwide} && ");
                            str.Add($"\"[床架宽]\"== {dwide} ");
                        }
                        if(!dlang.IsNullOrEmptyOrWhiteSpace())
                        {
                            //sb.Append($"\"[床架长]\"== {dlang} && ");
                            str.Add($"\"[床架长]\"== {dlang} ");
                        }
                        if (!dhigh.IsNullOrEmptyOrWhiteSpace()) {
                            //sb.Append($"\"[床脚高度]\"== {dhigh} ");
                            str.Add($"\"[床脚高度]\"== {dhigh} ");
                        }
                        if (str.Count > 1)
                        {
                            condition = String.Join(" && ", str);
                        }
                        else {
                            condition = String.Join("", str);
                        }
                        //condition = sb.ToString();
                        //构建配件维度-型号明细
                        //dimension = dcode;
                    }
                    //【条件】的生成要根据【配件类型】来判断执行不同逻辑
                    //1、当配件类型为支撑杆 dlang 即表达式条件
                    else if (dtype == "支撑杆")
                    {
                        //如果【配件类型】=”支撑杆” 传过来的dlang 即为条件表达式
                        condition = dlang;
                    }
                    else if (dtype == "气压杆")
                    {
                        condition = $"\"[排骨架]\"=='{Convert.ToString(middata["dname"]).Trim()}'";
                    }
                    //改成增量形式
                    var ffittingsmapmodel = ffittingsmapentity.FirstOrDefault(s => Convert.ToString(s["ftranid"]).EqualsIgnoreCase(Convert.ToString(middata["id"])));
                    if (ffittingsmapmodel == null)
                    {
                        ffittingsmapmodel = (DynamicObject)ffittingsmapentity.DynamicCollectionItemPropertyType.CreateInstance();
                    }
                    //构建选配配件-条件明细
                    ffittingsmapmodel["ftranid"] = Convert.ToString(middata["id"]);
                    ffittingsmapmodel["fconditions"] = condition;
                    ffittingsmapmodel["fmaterialid"] = product?["id"];
                    ffittingsmapmodel["fmaterialid_ref"] = product;
                    ffittingsmapmodel["fmaterialidnew"] = product_new?["id"];
                    ffittingsmapmodel["fmaterialidnew_ref"] = product_new;
                    ffittingsmapmodel["fpropid_ref"] = attrinfo;
                    ffittingsmapmodel["fqty"] = qty;
                    ffittingsmapmodel["fdisable"] = disable;
                    //如果已有此维度数据则不插入明细
                    //if (!ffittingsmapentity.Any(o => o["fmaterialid"].ToString().EqualsIgnoreCase(product?["id"].ToString())
                    //&& Convert.ToInt32(o["fqty"]).Equals(qty)
                    //&& Convert.ToString(o["fconditions"]).EqualsIgnoreCase(condition)
                    //))
                    if (!ffittingsmapentity.Any(o => Convert.ToString(o["ftranid"]).EqualsIgnoreCase(Convert.ToString(middata["id"]))
                    ))
                    {
                        ffittingsmapentity.Add(ffittingsmapmodel);
                    }

                    if (!dimension.IsNullOrEmptyOrWhiteSpace()) {
                        var dimension_p = this.Context.LoadBizDataByNo("sel_type", "fnumber", new string[] { dimension }).FirstOrDefault();
                        dimensionmodel["fseltypeid"] = dimension_p?["id"];
                        dimensionmodel["fseltypeid_ref"] = dimension_p;
                        dimensionmodelentity.Add(dimensionmodel);
                    }


                    //foreach (var mapentity in ffittingsmapentity)
                    //{
                    //    mapentity["fconditions"] = condition;
                    //    mapentity["fmaterialid"] = product["id"];
                    //    mapentity["fmaterialid_ref"] = product;
                    //    mapentity["fpropid_ref"] = attrinfo;
                    //}
                    //foreach (var modelentity in dimensionmodelentity)
                    //{
                    //    if (!dimension.IsNullOrEmptyOrWhiteSpace())
                    //    {
                    //        var dimension_p = this.Context.LoadBizDataByNo("ydj_product", "fnumber", new string[] { dimension }).FirstOrDefault();
                    //        modelentity["fseltypeid"] = dimension_p["id"];
                    //        modelentity["fseltypeid_ref"] = dimension_p;
                    //    }
                    //}
                }
            }
        }

        private DynamicObject DoAuxpropValue(JObject MidDataObj) 
        {
            //dwide + dlang + dhigh 来生成结构化的【配件辅助属性】
            var dwide = Convert.ToString(MidDataObj["dwide"]).Trim();
            var dlang = Convert.ToString(MidDataObj["dlang"]).Trim();
            var dhigh = Convert.ToString(MidDataObj["dhigh"]).Trim();
            //辅助属性结构映射为【配件辅助属性 - 床箱底板长】= 【中台的dlang】, 【配件辅助属性 - 床箱底板宽】= 【中台的dwide】, 【配件辅助属性 - 床脚高度】= 【中台的dhigh】
            var map = new Dictionary<string, BaseDataModel>();

            List<string> propNames = new List<string>() { "床箱底板长", "床箱底板宽", "床脚高度" };
            List<Tuple<string, string>> propValueNumers = new List<Tuple<string, string>>() { new Tuple<string, string>("床箱底板长", dlang),
                                                                                              new Tuple<string, string>("床箱底板宽", dwide),
                                                                                              new Tuple<string, string>("床脚高度", dhigh)
                                                                                            };
            var notFoundPropNumbers = new List<string>();
            var notFoundPropValueNumbers = new List<Tuple<string, string>>();
            if (propNames.Any())
            {
                string sql =
                    $"select fid,fnumber,fname from t_sel_prop where fname in ({string.Join(",", propNames.Distinct().Select(s => $"'{s}'"))})";
                using (var reader = this.DBService.ExecuteReader(this.Context, sql))
                {
                    while (reader.Read())
                    {
                        string id = reader.GetValueToString("fid");
                        string number = reader.GetValueToString("fnumber");
                        string name = reader.GetValueToString("fname");

                        map[$"sel_prop${name}"] = new BaseDataModel
                        {
                            Id = id,
                            Number = number,
                            Name = name
                        };
                    }
                }

                foreach (var propNumer in propNames)
                {
                    if (!map.ContainsKey($"sel_prop${propNumer}"))
                    {
                        notFoundPropNumbers.Add(propNumer);
                    }
                }
            }

            if (propValueNumers.Any())
            {
                List<string> sqls = new List<string>();

                foreach (var propValueNumer in propValueNumers)
                {
                    if (map.TryGetValue($"sel_prop${propValueNumer.Item1}", out var prop))
                    {
                        // 增加过滤条件：只匹配标准属性值，即【非标】=否
                        sqls.Add($"select fid,fnumber,fname,fpropid, '{prop.Name}' as fpropnumber from t_sel_propvalue where fnumber='{propValueNumer.Item2}' and fpropid='{prop.Id}'");
                    }
                }

                string sql = string.Join(" union all ", sqls);
                using (var reader = this.DBService.ExecuteReader(this.Context, sql))
                {
                    while (reader.Read())
                    {
                        string id = reader.GetValueToString("fid");
                        string number = reader.GetValueToString("fnumber");
                        string name = reader.GetValueToString("fname");
                        string propId = reader.GetValueToString("fpropid");
                        string propNumber = reader.GetValueToString("fpropnumber");

                        map[$"sel_propvalue${propNumber}${number}"] = new BaseDataModel
                        {
                            Id = id,
                            Number = number,
                            Name = name
                        };
                    }
                }

                foreach (var propValueNumer in propValueNumers)
                {
                    if (!map.ContainsKey($"sel_propvalue${propValueNumer.Item1}${propValueNumer.Item2}"))
                    {
                        notFoundPropValueNumbers.Add(propValueNumer);
                    }
                }
            }

            if (notFoundPropNumbers.Any() || notFoundPropValueNumbers.Any())
            {
                string msg = string.Empty;

                if (notFoundPropNumbers.Any()) msg += $"以下属性未同步：{string.Join(",", notFoundPropNumbers)}！";
                if (notFoundPropValueNumbers.Any()) msg += $"以下属性值未同步：{string.Join(",", notFoundPropValueNumbers.Select(s => $"({s.Item1},{s.Item2})"))}！";

                throw new BusinessException(msg);
            }

                    var auxPropValSetForm = this.MetaModelService.LoadFormModel(this.Context, "bd_auxpropvalueset");
                    var auxPropValSetObj = new DynamicObject(auxPropValSetForm.GetDynamicObjectType(this.Context));
                    var auxPropValSetEntity = auxPropValSetForm.GetEntryEntity("FEntity");
                    var auxPropValSetEntrys = auxPropValSetObj["FEntity"] as DynamicObjectCollection;

                    foreach (var p in propNames)
                    {
                        //string propNumber =map[$"sel_prop${p}"].ToString();
                        string propValueNumber = propValueNumers.Where(o=>o.Item1 == p).Select(a=>a.Item2).FirstOrDefault();

                        var prop = map[$"sel_prop${p}"];
                        var propValue = map[$"sel_propvalue${p}${propValueNumber}"];

                        var auxPropValSetEntry = new DynamicObject(auxPropValSetEntity.DynamicObjectType);
                        auxPropValSetEntry["fauxpropid"] = prop.Id;
                        auxPropValSetEntry["fvalueid"] = propValue.Id;
                        auxPropValSetEntry["fvaluename"] = propValue.Name;
                        auxPropValSetEntry["fvaluenumber"] = propValue.Number;
                        auxPropValSetEntrys.Add(auxPropValSetEntry);
                    }
            return auxPropValSetObj;
        }

        /// <summary>
        /// 来源单据字段映射事件：可对已存在的数据包做映射覆盖
        /// </summary>
        /// <param name="e"></param>
        private void SourceBillFieldMapping(OnCustomServiceEventArgs e)
        {
            //var eventData = e.EventData as Tuple<DynamicObject[], IEnumerable<DynamicObject>>;
            //if (eventData == null
            //    || eventData.Item1 == null
            //    || eventData.Item1.Length < 1) return;
        }
    }

    public class BaseDataModel
    {
        public string Id { get; set; }

        public string Number { get; set; }

        public string Name { get; set; }
    }
}
