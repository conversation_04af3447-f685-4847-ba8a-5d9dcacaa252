using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.BAS.Product
{
    /// <summary>
    /// 自建商品同步
    /// </summary>
    [InjectService]
    [FormId("ydj_product")]
    [OperationNo("synctomusi")]
    [ThirdSystemId("musi")]
    public class TransferToMuSi : AbstractSyncDataToMuSiPlugIn
    {
        /// <summary>
        /// 字段映射前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeFieldMapping(BeforeFieldMappingEventArgs e)
        {
            base.BeforeFieldMapping(e);

            if (e.Entity == null || e.FieldEntry == null) return;
            var dataEntity = e.Entity;
            var bizEntity = e.DataEntity;
            string extFieldId = Convert.ToString(e.FieldEntry["fextfieldid"]).ToLower();
            DynamicObject DealerObj = null;
            string fcategoryid = Convert.ToString(dataEntity["fcategoryid"]);
            switch (extFieldId)
            {
                case "subcatcode":
                case "subcate":
                case "midcatcode":
                case "midcate":
                case "bigcatcode":
                case "bigcate":
                    //string sql = $@"select b.fnumber as subCatCode,b.fname as subCate
                    //            ,c.fnumber as midCatCode,c.fname as midCate
                    //            ,d.fnumber as bigCatCode,d.fname as bigCate
                    //        from t_bd_material a
                    //        left join ser_ydj_category b on a.fcategoryid=b.fid
                    //        left join ser_ydj_category c on b.fparentid=c.fid
                    //        left join ser_ydj_category d on c.fparentid=d.fid
                    //        where a.fnumber='{fnumber}' and a.fmainorgid='{fmainorgid}'
                    //       ";
                    string sql = $@"select b.fnumber as subCatCode,b.fname as subCate
                                ,c.fnumber as midCatCode,c.fname as midCate
                                ,d.fnumber as bigCatCode,d.fname as bigCate
                            from ser_ydj_category b
                            left join ser_ydj_category c on b.fparentid=c.fid
                            left join ser_ydj_category d on c.fparentid=d.fid
                            where b.fid='{fcategoryid}'
                           ";
                    var dbService = this.UserContext.Container.GetService<IDBService>();
                    DealerObj = dbService.ExecuteDynamicObject(this.UserContext, sql).FirstOrDefault();
                    break;
            }
            
            switch (extFieldId)
            {
                // 小类编码
                case "subcatcode":
                    e.Result = DealerObj["subCatCode"];
                    e.Cancel = true;
                    break;
                // 小类
                case "subcate":
                    e.Result = DealerObj["subCate"];
                    e.Cancel = true;
                    break;
                // 中类编码
                case "midcatcode":
                    e.Result = DealerObj["midCatCode"];
                    e.Cancel = true;
                    break;
                // 中类
                case "midcate":
                    e.Result = DealerObj["midCate"];
                    e.Cancel = true;
                    break;
                // 大类编码
                case "bigcatcode":
                    e.Result = DealerObj["bigCatCode"];
                    e.Cancel = true;
                    break;
                // 大类
                case "bigcate":
                    e.Result = DealerObj["bigCate"];
                    e.Cancel = true;
                    break;
                //创建时间
                case "crtdate":
                    e.Result = dataEntity["fcreatedate"];
                    break;
                //修改时间
                case "upddate":
                    e.Result = dataEntity["fmodifydate"];
                    break;

            }
        }

    }
}
