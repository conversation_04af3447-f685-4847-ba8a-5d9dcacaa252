using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.BAS.BizTask
{
    /// <summary>
    /// 计划任务：保存
    /// </summary>
    [InjectService]
    [FormId("bas_task")]
    [OperationNo("save")]
    public class TaskSave : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (this.Context.IsTopOrg) return true;

                // 有部分计划任务只能总部配置
                var fpluginid = Convert.ToString(newData["fpluginid"]);

                List<string> pluginIds = new List<string> { "transferfrommusi", "transfertomusimq", "transfertomusi", "musimqenqueue", "getaiquestion", "operationlogautoretry", "agentautoaudit", "deliverautoaudit", "pricecalculate", "purorderchgautoaudit", "serviceautoclose", "stockageanalysis", "storeautoaudit", "storecreatedepttask" };
                if (pluginIds.Contains(fpluginid, StringComparer.OrdinalIgnoreCase))
                {
                    return false;
                }

                return true;
            }).WithMessage("{0}", (billObj, propObj) => $"执行计划【{billObj["fpluginid_txt"]}】只能在总部创建！"));
        }
    }
}
