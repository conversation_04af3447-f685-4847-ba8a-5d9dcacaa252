using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.BAS.Brand
{
    /// <summary>
    /// 品牌：慕思同步
    /// </summary>
    [InjectService]
    [FormId("ydj_brand")]
    [OperationNo("syncfrommusi")]
    [ThirdSystemId("musi")]
    public class TransferToMuSi : AbstractSyncDataFromMuSiPlugIn
    {
        public override void BeforeSave(BeforeSaveEventArgs e)
        {
            base.BeforeSave(e);

            if (e.DataEntitys.IsNullOrEmpty()) return;

            foreach (var dataEntity in e.DataEntitys)
            {
                dataEntity["fmusibrand"] = 1;
            }
        }
    }
}
