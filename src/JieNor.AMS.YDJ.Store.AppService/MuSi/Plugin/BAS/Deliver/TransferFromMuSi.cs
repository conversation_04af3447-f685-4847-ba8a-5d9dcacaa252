using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.BAS.Deliver
{
    /// <summary>
    /// 送达方：慕思同步
    /// </summary>
    [InjectService]
    [FormId("bas_deliver")]
    [OperationNo("syncfrommusi")]
    [ThirdSystemId("musi")]
    public class TransferToMuSi : AbstractSyncDataFromMuSiPlugIn
    {
        [InjectProperty]
        public IDeliverService DeliverService { get; set; }

        public override void BeforeSave(BeforeSaveEventArgs e)
        {
            base.BeforeSave(e);

            if (e.DataEntitys.IsNullOrEmpty()) return;

            this.DeliverService.UpdateBoss(this.Context, e.DataEntitys);

            var lstValidDataEntities = new List<DynamicObject>();

            var agentFld = this.HtmlForm.GetField("fagentid");
            var actualownernumberFld = this.HtmlForm.GetField("actualownernumber");

            foreach (var dataEntity in e.DataEntitys)
            {
                // 假如没有前置同步经销商，过滤不执行后续的保存
                if (dataEntity["fagentid"].IsNullOrEmptyOrWhiteSpace())
                {
                    string msg =
                        $"{this.HtmlForm.Caption}{dataEntity["fnumber"]}的【{agentFld.Caption}】字段为空，无法保存，请检查是否未同步经销商！";

                    this.WriteOpLog(msg);
                    e.Result.ComplexMessage.ErrorMessages.Add(msg);

                    continue;
                }

                if (dataEntity["actualownernumber"].IsNullOrEmptyOrWhiteSpace())
                {
                    string msg =
                        $"{this.HtmlForm.Caption}{dataEntity["fnumber"]}的【{actualownernumberFld.Caption}】字段为空，无法保存，请检查是否未同步实控人！";

                    this.WriteOpLog(msg);
                    e.Result.ComplexMessage.ErrorMessages.Add(msg);

                    continue;
                }

                lstValidDataEntities.Add(dataEntity);
            }

            foreach (var dataEntity in lstValidDataEntities)
            {
                // 禁用要变回创建状态
                // 填写禁用人/禁用时间
                // 清空审核人等相关信息

                bool fforbidstatus = Convert.ToBoolean(dataEntity["fforbidstatus"]);
                string fforbidid = Convert.ToString(dataEntity["fforbidid"]);

                // 禁用且禁用人为空，表示由接口更新
                if (fforbidstatus && fforbidid.IsNullOrEmptyOrWhiteSpace())
                {
                    dataEntity["fstatus"] = "B";
                    dataEntity["fforbidid"] = this.Context.UserId;
                    dataEntity["fforbiddate"] = DateTime.Now;

                    dataEntity["fapproveid"] = " ";
                    dataEntity["fapprovedate"] = null;
                }
            }

            e.DataEntitys = lstValidDataEntities;
        }

        public override void AfterSave(AfterSaveEventArgs e)
        {
            base.AfterSave(e);

            var delivers = e.DataEntitys;
            if (delivers.IsNullOrEmpty()) return;

            var deliverService = this.Container.GetService<IDeliverService>();
            deliverService.RewriteCity(this.Context, delivers);
            deliverService.UpdateBrandEntry(this.Context, delivers);
            //deliverService.UpdateSalOrg(this.Context, delivers);
            deliverService.UpdateSalOrgBySales(this.Context, delivers);
            deliverService.UpdateCrmDistributor(this.Context, delivers);
            if (delivers.Any())
            {
                //改成标准保存目的是记录日志 会引发保存数据包被提示已经被更改
               // this.Gateway.InvokeBillOperation(this.Context, "bas_deliver", delivers, "save",
               //this.Option.ToDictionary());
               this.Context.SaveBizData("bas_deliver", delivers);
            }

            deliverService.AddOrUpdateProductAuth(this.Context, delivers);
            deliverService.UpdateAgentAndCrmDistributor(this.Context, delivers);

            // 更新《门店》（解决拉取顺序先后有问题时，互相反写）
            var agentIds = e.DataEntitys.Select(s => Convert.ToString(s["fagentid"])).ToList();
            var stores = this.Context.LoadBizDataByNo("bas_store", "fagentid", agentIds);
            var storeService = this.Container.GetService<IStoreService>();
            storeService.UpdateAgentAndDeliver(this.Context, stores);

            // 自动保存
            if (stores.Any())
            {
                this.Context.SaveBizData("bas_store", stores);
                //改成标准保存目的是记录日志 会引发保存数据包被提示已经被更改
                //this.Gateway.InvokeBillOperation(this.Context, "bas_store", stores, "save",
                //this.Option.ToDictionary()); 
            }
        }

        public override void AfterExecute(AfterExecuteEventArgs e) 
        {
            base.AfterExecute(e);

            if (e.DataEntitys.IsNullOrEmpty()) return;

            IOperationResult result = null;

            var dataEntities = e.DataEntitys;
            if (!dataEntities.IsNullOrEmpty())
            {
                foreach (var dataEntity in dataEntities)
                {
                    //改为【提交】状态，便于后续全部执行审核
                    //dataEntity["fstatus"] = "D";
                    if (Convert.ToString(dataEntity["fstatus"]) != "D" && Convert.ToString(dataEntity["fstatus"]) != "E") 
                    {
                        result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, new[] { dataEntity }, "submit",
                        this.Option.ToDictionary());
                        this.Result.MergeResult(result);
                    }
                }
            }
        }
    }
}
