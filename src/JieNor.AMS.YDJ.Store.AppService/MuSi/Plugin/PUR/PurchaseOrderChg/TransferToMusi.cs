using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.PUR.PurchaseOrderChg
{
    /// <summary>
    /// 采购订单：同步
    /// 大佬们改这里的话，对应的TransferToMuSiOMS也一起改下，谢谢
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder_chg")]
    [OperationNo("synctomusi")]
    [ThirdSystemId("musi")]
    public class TransferToMuSi : AbstractSyncDataToMuSiPlugIn
    {
        bool IsDirectSale = false;
        /// <summary>
        /// 字段映射前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeFieldMapping(BeforeFieldMappingEventArgs e)
        {
            base.BeforeFieldMapping(e);

            if (e.Entity == null || e.FieldEntry == null) return;

            var dataEntity = e.Entity;
            var bizEntity = e.DataEntity;

            string extFieldId = Convert.ToString(e.FieldEntry["fextfieldid"]).ToLower();
            switch (extFieldId)
            {
                // 变更状态
                case "alterflag":
                    {
                        //删除就是传2，更新就是传1； 新增明细 是会被过滤的 不会传过去
                        e.Cancel = true;
                        e.Result = "";

                        var changetype = Convert.ToString(dataEntity["fentrychange"]);
                        if (changetype == "entrychange_01")
                        {
                            e.Result = "1";
                        }
                        else if (changetype == "entrychange_03")
                        {
                            e.Result = "2";
                        }
                        var fclosestatus_e_chg = Convert.ToString(dataEntity?["fclosestatus_e_chg"]);
                        if (fclosestatus_e_chg == "4")
                        {
                            //如果是关闭状态的明细 则把变更状态 更新为 删除，接口就会把alterFlag 传2 即为取消
                            e.Result = "2";
                        }
                    }
                    break;
              
            }
            if (this.IsDirectSale)
            {
                switch (extFieldId)
                {
                    case "salorgcode":
                        //直营，传销售组织
                        e.Cancel = true;
                        var agentItem = this.UserContext.LoadBizDataById("bas_agent", Convert.ToString(dataEntity["fmainorgid"]));
                        if (agentItem != null)
                        {
                            var mate = this.UserContext.Container.GetService<IMetaModelService>();
                            var agentForm = mate.LoadFormModel(this.UserContext, "bas_agent");
                            // 加载数据
                            var refObjMgr = this.UserContext.Container.GetService<LoadReferenceObjectManager>();
                            refObjMgr?.Load(this.UserContext, new DynamicObject[] { agentItem }, true, agentForm, new List<string> { "fsaleorgid" });
                            e.Result = Convert.ToString((agentItem["fsaleorgid_ref"] as DynamicObject)["fnumber"]); //传销售组织
                        }

                        break;
                }
            }
        }
        public override void BeforePackSourceBill(BeforePackSourceBillEventArgs e)
        {
            base.BeforePackSourceBill(e);

            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;

            // 获取合同附件
            var purchaseorders = e.DataEntitys;

            if (purchaseorders.FirstOrDefault() != null)
            {
                var orgId = Convert.ToString(purchaseorders.FirstOrDefault()["fmainorgid"]);

                var agentDy = this.UserContext.LoadBizBillHeadDataById("bas_agent", orgId, "fmanagemodel");

                string manageModel = string.Empty;

                if (agentDy != null)
                {
                    manageModel = Convert.ToString(agentDy["fmanagemodel"]);
                    if (manageModel.Equals("1"))
                    {
                        this.IsDirectSale = true;
                    }
                }
            }
        }

    }
}
