using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.PUR.PurchaseOrder
{
    /// <summary>
    /// 采购订单：同步采购订单变更单到中台
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("SubmitHQ_Chg")]
    public class SubmitHQ_Chg : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var refMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
            var formDt = this.HtmlForm.GetDynamicObjectType(this.Context);

            var formPara = this.PageSession?.FormParameter;
            Enu_DomainType domainType = Enu_DomainType.Bill;
            if (formPara is FormParameter)
            {
                domainType = ((FormParameter)formPara).DomainType;
            }

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string status = Convert.ToString(newData["fstatus"]);
                return (status.EqualsIgnoreCase("E") || Convert.ToString(newData["fchangestatus"]) == "1");
            }).WithMessage("采购订单【{0}】未审核不允许提交至总部!", (billObj, propObj) => propObj["fbillno"]));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string fhqderstatus = Convert.ToString(newData["fhqderstatus"]);
                if (fhqderstatus.IsNullOrEmptyOrWhiteSpace())
                {
                    return true;
                }
                // 【总部合同状态】=【驳回】、【终审】、【已提交总部】可【提交总部】
                if (fhqderstatus.EqualsIgnoreCase("05") || fhqderstatus.EqualsIgnoreCase("02") || fhqderstatus.EqualsIgnoreCase("03"))
                {
                    return true;
                }

                return false;
            }).WithMessage("采购订单【{0}】已提交至总部, 不允许重复提交!", (billObj, propObj) => propObj["fbillno"]));
            var errorMessage = string.Empty;
            //1. 在 采购订单 列表 与表单  点击 <提交总部>, 如果存在 非总部商品时, 要报错提示"采购订单存在非总部商品, 不允许提交至总部!"
            var topOrgId = this.Context.IsTopOrg ? this.Context.Company : this.Context.TopCompanyId;

            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    errorMessage = "";
            //    //加载引用数据
            //    refMgr.Load(this.Context, formDt, newData, false);
            //    var entrys = newData["fentity"] as DynamicObjectCollection;
            //    var flag = true;
            //    foreach (var entry in entrys)
            //    {
            //        var product = entry["fmaterialid_ref"] as DynamicObject;
            //        if (Convert.ToString(product["fmainorgid"]) != topOrgId)
            //        {
            //            errorMessage += $"采购订单存在非总部商品【{product["fname"]}】 不允许提交至总部";
            //            flag = false;
            //        }
            //    }
            //    return flag;
            //}).WithMessage("{0}", (billObj, propObj) => errorMessage));

            // 增加产品销售组织校验
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMessage = "";

                //1.1) 采购订单 如果有 送达方时, 根据该送达方基础资料里的"销售组织"进行匹配(如果没有到送达方就不用该校验)
                //1.2) 匹配商品明细的商品 基础资料的 单据体 - 产品销售组织 里的 销售组织, 匹配上后或取对应行的"禁用状态"
                //1.3) 如果"禁用状态" = 已禁用, 则不允许转采购, 这一行就不会转到采购订单上, 其它数据可以继续转采购, 但是如果没有任何数据可以转采购时, 要提示错误信息"商品XXXX 在 XXXXX (某某销售组织) 已停产, 不允许采购!"
                //1.4) 如果"禁用状态" = 已启用, 则就可以正常转采购
                //1.5) 如果没有匹配到销售组织 , 则不允许采购(只针对总部的商品, 非总部的不需要该校验), 要提示错误信息"商品XXXX 已停产不允许采购, 请重新删除该商品重新提交!"
                //http://dmp.jienor.com:81/zentao/task-view-28399.html

                var fdeliverid = Convert.ToString(newData["fdeliverid"]);
                if (fdeliverid.IsNullOrEmptyOrWhiteSpace()) return true;

                //加载引用数据
                refMgr.Load(this.Context, formDt, newData, false);

                var deliver = newData["fdeliverid_ref"] as DynamicObject;
                if (deliver == null) return true;

                // 送达方的销售组织
                var fsaleorgid = Convert.ToString(deliver["fsaleorgid"]);
                if (fsaleorgid.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMessage = $"送达方{deliver["fname"]}没有关联销售组织!";
                    return false;
                }

                var saleOrg = this.Context.LoadBizDataById("bas_organization", fsaleorgid);
                if (saleOrg == null)
                {
                    errorMessage = $"送达方{deliver["fname"]}关联的销售组织已删除!";
                    return false;
                }

                var entrys = newData["fentity"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    var product = entry["fmaterialid_ref"] as DynamicObject;
                    // 非总部的不需要该校验
                    if (Convert.ToString(product["fmainorgid"]) != topOrgId) continue;

                    // 匹配销售组织
                    var saleOrgEntrys = product["fsaleorgentry"] as DynamicObjectCollection;
                    var salOrgEntry = saleOrgEntrys?.FirstOrDefault(s => Convert.ToString(s["fsaleorgid"]).EqualsIgnoreCase(fsaleorgid));

                    // 如果没有匹配到销售组织 , 则不允许采购(只针对总部的商品, 非总部的不需要该校验), 要提示错误信息"商品XXXX 已停产不允许采购, 请重新删除该商品重新提交!"
                    if (salOrgEntry == null)
                    {
                        errorMessage += $"商品{product["fname"]} 已停产不允许采购, 请重新删除该商品重新提交!<br/>";
                        continue;
                    }

                    // 如果"禁用状态" = 已禁用, 则不允许转采购, 这一行就不会转到采购订单上, 其它数据可以继续转采购, 但是如果没有任何数据可以转采购时, 要提示错误信息"商品XXXX 在 XXXXX (某某销售组织) 已停产, 不允许采购!"
                    if (Convert.ToString(salOrgEntry["fdisablestatus"]).EqualsIgnoreCase("2"))
                    {
                        errorMessage += $"商品{product["fname"]} 在 {saleOrg["fname"]} 已停产, 不允许采购!<br/>";
                        continue;
                    }
                }

                return errorMessage.IsNullOrEmptyOrWhiteSpace();

            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            /*
             采购订单 增加 当 商品行的"业绩品牌" = "通配品牌", 不允许提交至总部, 要提示错误信息, "采购订单XXXX存在业绩品牌为"通配品牌", 不允许提交至总部!"
             */
            var errorMsg = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var entrys = newData["fentity"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    var series = entry["fresultbrandid_ref"] as DynamicObject;
                    if (Convert.ToString(series?["fname"]) == "通配品牌")
                    {
                        errorMsg += $"采购订单{newData["fbillno"]}存在业绩品牌为\"通配品牌\", 不允许提交至总部";
                        continue;
                    }
                }
                return errorMsg.IsNullOrEmptyOrWhiteSpace();
            }).WithMessage("{0}", (billObj, propObj) => errorMsg));

            // 非总部经销商不允许提交总部 
            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    // 表头的供应商 对应基础资料里 单据头"销售组织"为空时, 不允许提交到总部
            //    var supplierId = Convert.ToString(newData["fsupplierid"]);

            //    var supplier = newData["fsupplierid_ref"] as DynamicObject;

            //    var orgId = Convert.ToString(supplier?["forgid"]);

            //    return !orgId.IsNullOrEmptyOrWhiteSpace();

            //}).WithMessage("当前供应商不为总部供应商, 不允许提交至总部!"));

            // 启用选配校验辅助属性值判断
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                // 如果对应《采购订单》商品明细行的商品  对应《商品》基础资料 勾选上"允许选配" 且  《采购订单》商品明细对应行 "辅助属性"为空时, 不允许提交总部

                var errorMsgs = new List<string>();

                var entrys = newData["fentity"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    var product = entry["fmaterialid_ref"] as DynamicObject;
                    if (product == null) continue;

                    var isPresetProp = Convert.ToBoolean(product["fispresetprop"]);
                    var attrInfo = Convert.ToString(entry["fattrinfo"]);
                    if (isPresetProp && attrInfo.IsNullOrEmptyOrWhiteSpace())
                    {
                        if (domainType == Enu_DomainType.Bill)
                        {
                            errorMsgs.Add($"明细行 {entry["Fseq"]} 商品 {product["fname"]} 为定制品,  但辅助属性为空, 不允许提交总部!");
                        }
                        else
                        {
                            errorMsgs.Add($"采购订单 {newData["fbillno"]} 商品 {product["fname"]} 为定制品,  但辅助属性为空, 不允许提交总部!");
                        }
                    }
                }

                if (errorMsgs.Any())
                {
                    errorMsg = string.Join("\r\n", errorMsgs);
                    return false;
                }

                return true;

            }).WithMessage("{0}", (billObj, propObj) => errorMsg));

            //判断商品业绩品牌是否都存在于 送达方品牌配置中
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fdeliverid = Convert.ToString(newData["fdeliverid"]);
                bool exist = CheckDeliverBrand(fdeliverid, newData,out errorMsg);
                return exist;
            }).WithMessage("{0}", (billObj, propObj) => errorMsg));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                bool exist = CheckProduct(newData, out errorMsg);
                return exist;
            }).WithMessage("{0}", (billObj, propObj) => errorMsg));
        }

        /// <summary>
        /// 判断 商品业绩品牌是否存在于 送达方品牌配置中
        /// </summary>
        /// <param name="deliverId"></param>
        /// <returns></returns>
        private bool CheckDeliverBrand(string deliverId,DynamicObject newData, out string errorMsg) 
        {
            errorMsg = "";
            bool exist = true;
            var fentity = newData["fentity"] as DynamicObjectCollection;
            if (deliverId.IsNullOrEmptyOrWhiteSpace()) return true;

            var deliverObj = this.Context.LoadBizDataById("bas_deliver", deliverId);
            //获取送达方品牌信息
            var deliverentry = deliverObj?["fentry"] as DynamicObjectCollection;
            var DeliverBrands = deliverentry.Select(o => o["fserieid"]);
            foreach (var entry in fentity) 
            {
                //找到一条不包含于送达方品牌信息明细中的商品行，则抛出
                if (!DeliverBrands.Contains(entry["fresultbrandid"])) 
                {
                    errorMsg += $"采购订单{newData?["fbillno"]} , 商品{Convert.ToString((entry["fmaterialid_ref"] as DynamicObject)?["fname"])}的业绩品牌不在送达方的品牌授权中, 不允许提交总部! <br>";
                }
            }
            if (!errorMsg.IsNullOrEmptyOrWhiteSpace()) 
            {
                exist = false;
            }
            //遍历传过来的商品明细，业绩品牌不存在于送达方品牌信息中则直接返回。
            return exist;
        }

        /// <summary>
        /// 新渠道床架与新渠道排骨架要按照 1:1 数量下单
        /// </summary>
        /// <param name="newData"></param>
        /// <param name="errorMsg"></param>
        /// <returns></returns>
        private bool CheckProduct(DynamicObject newData, out string errorMsg) {
            errorMsg = "";
            bool exist = true;
            var fentitys = newData["fentity"] as DynamicObjectCollection;
            //只统计业绩品牌为新渠道的商品行，其它的不需要比较
            //#75244 【250491】 【慕思现场4.10-4.14】新渠道系列拆分Z2 慕思经典-甄选 ，Z5 慕思经典-优选
            var NewBal = this.Context.LoadBizDataByFilter("ydj_series", $"fisnewchannel='1'").Select(o=>Convert.ToString(o["id"])).ToList();
            //var fentitysre = fentitys.Where(o=> NewBal.Contains(Convert.ToString(o["fresultbrandid"])));
            //获取商品对应类别 
            var fentitysre = fentitys.Where(o => NewBal.Contains(Convert.ToString((o["fmaterialid_ref"] as DynamicObject)?["fseriesid"])));
            Dictionary<string, int> ProDic = fentitysre.ToDictionary(k=>Convert.ToString(k["fmaterialid"]),v=>Convert.ToInt32(v["fbizqty"]));
            var productService = this.Context.Container.GetService<IProductService>();
            int sumCJ = 0;
            int sumPGJ = 0;
            foreach (var dic in ProDic) 
            {
                var productid = dic.Key.ToString();
                //获取商品类别名称
                List<string> categorylist = productService.LoadProductParentCategoryIds(this.Context, productid);
                var categorynameobj = this.Context.LoadBizDataByFilter("ydj_category", $"fid in('{string.Join("','", categorylist)}')");
                List<string> categorynamelist = categorynameobj.Select(o => o["fname"].ToString()).ToList();
                if (categorynamelist.Contains("床架")) 
                {
                    sumCJ += dic.Value;
                }
                if (categorynamelist.Contains("排骨架"))
                {
                    sumPGJ += dic.Value;
                }
            }
            if (sumCJ != sumPGJ && (sumCJ != 0 || sumPGJ != 0)) 
            {
                exist = false;
                errorMsg = "新渠道的床架必须搭配新渠道的排骨架一起采购, 当前采购订单未到1:1采购数量要求不允许向总部采购!";
            }
            return exist;
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;

            foreach (var dataEntity in e.DataEntitys)
            {
                // 更新【总部合同状态】=提交至总部
                dataEntity["fhqderstatus"] = "02";
            }

            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(e.DataEntitys);

            SyncPurchaseOrder(e.DataEntitys);

            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "采购订单变更单已提交至慕思总部！";
            this.AddRefreshPageAction();
        }

        /// <summary>
        /// 同步采购订单
        /// </summary>
        /// <param name="purchaseOrders">采购订单</param>
        private void SyncPurchaseOrder(IEnumerable<DynamicObject> purchaseOrders)
        {
            List<DynamicObject> purchaseOrdersChg = new List<DynamicObject>();
            foreach (var purchaseOrder in purchaseOrders) 
            {
                //获取最新生成的采购订单变更单
                var fbillno = Convert.ToString(purchaseOrder?["fbillno"]);
                var PurOrderObj = this.Context.LoadBizDataByACLFilter("ydj_purchaseorder_chg", $" fsourcenumber ='{fbillno}' ").OrderByDescending(o => o["fcreatedate"]).FirstOrDefault();
                if (PurOrderObj != null) {
                    purchaseOrdersChg.Add(PurOrderObj);
                }
            }
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_purchaseorder_chg");

            var fieldMapObjs = this.Container.GetService<IMuSiBizObjMapService>()
                .GetBizObjMaps(this.Context, htmlForm, Enu_MuSiSyncDir.CurrentToMuSi, Enu_MuSiSyncTimePoint.SyncManual);

            foreach (var fieldMapObj in fieldMapObjs)
            {
                var option = new Dictionary<string, object>();
                option["__fieldMapObj__"] = fieldMapObj;
                option["extAppId"] = fieldMapObj["fextappid"];

                var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_purchaseorder_chg", purchaseOrdersChg, "synctomusi", option);
                result.ThrowIfHasError(true, $"{htmlForm.Caption}{this.OperationName}失败！");
            }
        }
    }
}
