using JieNor.Framework;
using JieNor.Framework.Consts;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.PUR.PurchaseOrder
{
    /// <summary>
    /// 采购订单：关闭
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("bizclose")]
    public class BizClose : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;

            /*
             3.采购订单商品明细行手动处理逻辑：
                3.1.手动关闭部分商品行：手动关闭的商品.行关闭状态=”手动关闭”，未关闭的商品.行关闭状态=”正常”，此时需反写【单据头.关闭状态】=”部分关闭”。
                3.2.仅商品.行关闭状态=”手动关闭”允许操作<反关闭>按钮。
                3.2.手动关闭全部商品行：手动关闭的商品.行关闭状态=”手动关闭”，此时需反写【单据头.关闭状态】=”整单关闭”。
            4.采购订单单据头手动处理逻辑：
                4.1.手动操作单据头<订单关闭>按钮（此按钮已被隐藏）：则需设置【单据头.关闭状态】=”整单关闭”，商品行.行关闭状态=”正常”需设置为”手动关闭”。
                4.2.手动操作单据头<订单反关闭>按钮（此按钮已被隐藏）：
                    4.2.1.如果商品存在商品行.行关闭状态=“自动关闭”，则如果商品行则需设置【单据头.关闭状态】=”部分关闭”，则商品行.行关闭状态=”手动关闭”需设置为”正常”。
                    4.2.2.如果商品不存在商品行.行关闭状态=“自动关闭”，则如果商品行则需设置【单据头.关闭状态】=”正常”，则商品行.行关闭状态=”手动关闭”需设置为”正常”。
             */
            //< !--'0':'正常','1':'整单关闭','2':'部分关闭','3':'自动关闭','4':'手动关闭'-- >

            // 套件与沙发商品是否成套采购
            var profileService = this.Container.GetService<ISystemProfile>();
            var isPdk = profileService.GetSystemParameter(this.Context, "pur_systemparam", "fispdk", false);

            //选中的商品明细行Id
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds.IsNullOrEmptyOrWhiteSpace() ? new List<string>() : JsonConvert.DeserializeObject<List<string>>(rowIds);

            foreach (var purchaseOrder in e.DataEntitys)
            {
                var purEntryDatas = purchaseOrder["fentity"] as DynamicObjectCollection;

                var selPurEntryDatas = purEntryDatas
                    .Where(t => !selectRowIds.Any() || selectRowIds.Contains(t["id"].ToString()))
                    .ToList();

                // 采购数量为0时不允许关闭
                foreach (var entry in selPurEntryDatas)
                {
                    if (Convert.ToDecimal(entry["fbizqty"]) == 0)
                    {
                        this.Result.ComplexMessage.WarningMessages.Add($"第{entry["fseq"]}行商品明细的采购数量为0，不允许关闭。");
                    }
                }

                // 将不是【自动关闭】且【采购数量】>0 的明细行设置为【手动关闭】
                selPurEntryDatas
                    .FindAll(t => Convert.ToString(t["fclosestatus_e"]) != CloseStatusConst.Auto && Convert.ToDecimal(t["fbizqty"]) > 0)
                    .ForEach(t => t["fclosestatus_e"] = CloseStatusConst.Manual);

                // 最终走统一的关闭状态计算逻辑
                Core.Helpers.DocumentStatusHelper.CalcPurchaseOrderCloseStatus(purchaseOrder, "bizclose");

                // #36178 套件与沙发组合的同组商品一起关闭或反关闭
                Core.Helpers.DocumentStatusHelper.SetSuitCombProductCloseStatus(
                    isPdk, this.HtmlForm, purchaseOrder, purEntryDatas, selPurEntryDatas,this.Context);
            }

            //this.Context.SaveBizData("ydj_purchaseorder", e.DataEntitys);

            var gateway = this.Context.Container.GetService<IHttpServiceInvoker>();
            var result = gateway.InvokeBillOperation(this.Context, "ydj_purchaseorder", e.DataEntitys, "save",
                new Dictionary<string, object> { { "NoValidation", "1" } });
        }
    }

    /// <summary>
    /// 采购订单：反关闭
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("unclose")]
    public class UnClose : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;

            /*
             3.采购订单商品明细行手动处理逻辑：
                3.1.手动关闭部分商品行：手动关闭的商品.行关闭状态=”手动关闭”，未关闭的商品.行关闭状态=”正常”，此时需反写【单据头.关闭状态】=”部分关闭”。
                3.2.仅商品.行关闭状态=”手动关闭”允许操作<反关闭>按钮。
                3.2.手动关闭全部商品行：手动关闭的商品.行关闭状态=”手动关闭”，此时需反写【单据头.关闭状态】=”整单关闭”。
            4.采购订单单据头手动处理逻辑：
                4.1.手动操作单据头<订单关闭>按钮（此按钮已被隐藏）：则需设置【单据头.关闭状态】=”整单关闭”，商品行.行关闭状态=”正常”需设置为”手动关闭”。
                4.2.手动操作单据头<订单反关闭>按钮（此按钮已被隐藏）：
                    4.2.1.如果商品存在商品行.行关闭状态=“自动关闭”，则如果商品行则需设置【单据头.关闭状态】=”部分关闭”，则商品行.行关闭状态=”手动关闭”需设置为”正常”。
                    4.2.2.如果商品不存在商品行.行关闭状态=“自动关闭”，则如果商品行则需设置【单据头.关闭状态】=”正常”，则商品行.行关闭状态=”手动关闭”需设置为”正常”。
             */
            //< !--'0':'正常','1':'整单关闭','2':'部分关闭','3':'自动关闭','4':'手动关闭'-- >

            // 套件与沙发商品是否成套采购
            var profileService = this.Container.GetService<ISystemProfile>();
            var isPdk = profileService.GetSystemParameter(this.Context, "pur_systemparam", "fispdk", false);

            //选中的商品明细行Id
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            //是否将手动关闭且采购数量不为0的明细行关闭状态更新
            var modifyCloseStatus= this.GetQueryOrSimpleParam<string>("modifyCloseStatus","")=="1";
            var selectRowIds = rowIds.IsNullOrEmptyOrWhiteSpace() ? new List<string>() : JsonConvert.DeserializeObject<List<string>>(rowIds);

            foreach (var purchaseOrder in e.DataEntitys)
            {
                var purEntryDatas = purchaseOrder["fentity"] as DynamicObjectCollection;
                foreach (var item in purEntryDatas)
                {
                    if(modifyCloseStatus&&int.Parse(Convert.ToString(item["fclosestatus_e"])) == (int)CloseStatus.Manual)
                    {
                        //使后续进入更正关闭状态
                        item["fclosestatus_e"] = (int)CloseStatus.Default;
                    }
                }

                var selPurEntryDatas = purEntryDatas
                    .Where(t => !selectRowIds.Any() || selectRowIds.Contains(t["id"].ToString()))
                    .ToList();

                // 采购数量为0时不允许关闭
                foreach (var entry in selPurEntryDatas)
                {
                    if (Convert.ToDecimal(entry["fbizqty"]) == 0)
                    {
                        this.Result.ComplexMessage.WarningMessages.Add($"第{entry["fseq"]}行商品明细的采购数量为0，不允许反关闭。");
                    }
                }

                // 最终走统一的关闭状态计算逻辑
                Core.Helpers.DocumentStatusHelper.CalcPurchaseOrderCloseStatus(purchaseOrder);

                // #36178 套件与沙发组合的同组商品一起关闭或反关闭
                Core.Helpers.DocumentStatusHelper.SetSuitCombProductCloseStatus(
                    isPdk, this.HtmlForm, purchaseOrder, purEntryDatas, selPurEntryDatas,this.Context);
            }

            //this.Context.SaveBizData("ydj_purchaseorder", e.DataEntitys);

            var gateway = this.Context.Container.GetService<IHttpServiceInvoker>();
            var result = gateway.InvokeBillOperation(this.Context, "ydj_purchaseorder", e.DataEntitys, "save",
                new Dictionary<string, object> { { "NoValidation", "1" } });
        }
    }
}