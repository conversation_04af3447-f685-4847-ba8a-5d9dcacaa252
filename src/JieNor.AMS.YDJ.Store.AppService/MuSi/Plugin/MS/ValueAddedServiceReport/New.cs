using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Api;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.MS.ValueAddedServiceReport
{
    /// <summary>
    /// 增值服务报表
    /// </summary>
    [InjectService]
    [FormId("ms_valueaddedservicereport")]
    [OperationNo("new")]
    public class New : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;


            var CompanyId = this.Context.Company;
            if (CompanyId == null)
            {
                throw new BusinessException("获取当前组织Id失败！");
            }

            var sqlText = string.Empty;

            //是否为总部组织
            var orgObj = this.Context.LoadBizBillHeadDataById("BAS_ORGANIZATION", CompanyId, "ftopcompanyid");

            if (orgObj != null && orgObj["ftopcompanyid"].Equals(CompanyId))
            {
                sqlText = $@"/*dialect*/select  b.fid, b.fnumber, b.fname, b.fcdppassword from t_ms_crmdistributor b with(nolock) where b.fforbidstatus='0' and b.fstate = '0' order by TRY_CONVERT(datetime, b.flastlogintime) desc";
            }
            else
            {
                sqlText = $@"/*dialect*/select distinct fid, fnumber, fname, fcdppassword,TRY_CONVERT(datetime, flastlogintime) flastlogintime from
                               (
                                select b.fid, b.fnumber, b.fname, b.fcdppassword,b.flastlogintime from t_ms_crmdistributor b with(nolock)
                                inner join t_bas_macentry me with(nolock) on b.fagentid like '%'+me.fsubagentid+'%'
                                inner join t_bas_mac m with(nolock) on m.fid=me.fid
                                where (m.fmainagentid=@fmainorgid or b.fagentid like '%'+@fmainorgid+'%' or m.fmainorgid=@fmainorgid) and m.fforbidstatus='0' and b.fforbidstatus='0' and b.fstate = '0'
                                union all
                                select b.fid, b.fnumber, b.fname, b.fcdppassword,b.flastlogintime from t_ms_crmdistributor b with(nolock) where b.fagentid like '%' + @fmainorgid + '%' and b.fforbidstatus='0' and b.fstate = '0'
                                ) as t
                            order by flastlogintime desc";
            }

            List<SqlParam> param = new List<SqlParam>();
            param.Add(new SqlParam("@fmainorgid", DbType.String, CompanyId));
            var data = this.Context.ExecuteDynamicObject(sqlText, param);

            List<Dictionary<string, object>> SrvData = new List<Dictionary<string, object>>();

            foreach (var item in data)
            {
                //Base64密码解密
                var password = JNConvert.ToStringAndTrim(item["fcdppassword"]);
                byte[] decodedBytes = Convert.FromBase64String(password);
                password = System.Text.Encoding.UTF8.GetString(decodedBytes);

                SrvData.Add(new Dictionary<string, object>
                    {
                        { "fid",JNConvert.ToStringAndTrim(item["fid"])},
                        { "fnumber",JNConvert.ToStringAndTrim(item["fnumber"])},
                        { "fname",JNConvert.ToStringAndTrim(item["fname"])},
                        { "fcdppassword",password}
                    });
            }

            if (SrvData.Count <= 0)
            {
                throw new BusinessException("对不起，当前组织下 【禁用状态】=“否”且 【招商经销商状态】=“启用”的招商经销商不存在！请检查！");
            }

            //如果只有1个招商经销商，且有历史【CDP登录密码】
            if (SrvData.Count == 1 && !SrvData[0]["fcdppassword"].IsNullOrEmptyOrWhiteSpace())
            {
                //Base64编码加密
                byte[] bytesToEncode = System.Text.Encoding.UTF8.GetBytes(JNConvert.ToStringAndTrim(SrvData[0]["fcdppassword"]));
                string base64EncodedText = Convert.ToBase64String(bytesToEncode);

                var requestData = new Dictionary<string, string>();
                requestData["username"] = JNConvert.ToStringAndTrim(SrvData[0]["fnumber"]);
                requestData["password"] = base64EncodedText;


                //获取跳转Url
                var resp = MusiMenberCDPApi.GetUrl(this.Context, requestData, "ms_valueaddedservicereport");
                if (resp["success"].Equals("1"))
                {
                    e.DataEntitys[0]["furl"] = resp["message"];
                    e.DataEntitys[0]["fcanlogin"] = true;
                }
                else
                {
                    e.DataEntitys[0]["furl"] = "";
                    e.DataEntitys[0]["fcanlogin"] = false;
                }

            }

        }
    }
}
