using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.MS.MqSyncRecord
{
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("getmqsyncrecordinfo")]
    public class GetMqSyncRecordInfo : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            string cacheKey = $"mss.syncdata.{this.HtmlForm.Id}";
            MuSiSaleMQClient client = new MuSiSaleMQClient(this.Context);

            client.GetMqSyncInfos(cacheKey, out long count, out string topData);
            this.Result.SrvData = new { count = count, topData = topData };
            this.Result.IsSuccess = true;
        }
    }
}
