using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：同步共享费用计提单号
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("synctomusi")]
    [ThirdSystemId("musi")]
    public class TransferToMuSiShareBillNo : AbstractSyncDataToMuSiPlugIn
    {
        /// <summary>
        /// 字段映射前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeFieldMapping(BeforeFieldMappingEventArgs e)
        {
            // 添加调试日志
            Console.WriteLine($"[TransferToMuSiShareBillNo] BeforeFieldMapping 被调用");

            base.BeforeFieldMapping(e);

            if (e.Entity == null || e.FieldEntry == null) return;

            var dataEntity = e.Entity;
            var bizEntity = e.DataEntity;

            string extFieldId = Convert.ToString(e.FieldEntry["fextfieldid"]).ToLower();

            switch (extFieldId)
            {
                //费用归属公司(传当前组织id所在的销售组织的编码)
                case "zycompnum":
                    e.Cancel = true;
                    e.Result = GetSaleOrgNumber(e.DataEntity);
                    break;
                case "zycomments":
                    e.Cancel = true;
                    e.Result = GetZyComments(e.DataEntity);
                    break;
            }
        }

        /// <summary>
        /// 获取费用归属公司(传当前组织id所在的销售组织的编码)
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        private string GetSaleOrgNumber(DynamicObject dataEntity)
        {
            var saleOrgNumber = string.Empty;
            var orgId = Convert.ToString(dataEntity["fmainorgid"]);
            if (!orgId.IsNullOrEmptyOrWhiteSpace())
            {
                var agentDy = this.UserContext.LoadBizBillHeadDataById("bas_agent",orgId,"fsaleorgid");
                if (agentDy != null)
                {
                    var saleOrgId = Convert.ToString(agentDy["fsaleorgid"]);
                    if (!saleOrgId.IsNullOrEmptyOrWhiteSpace())
                    {
                        var orgDy = this.UserContext.LoadBizBillHeadDataById("bas_organization",saleOrgId,"fnumber");
                        if (orgDy != null)
                        {
                            saleOrgNumber = Convert.ToString(orgDy["fnumber"]);
                        }
                    }
                }
            }

            return saleOrgNumber;
        }

        /// <summary>
        /// 备注(传：预提+合同渠道名称+CRM销售合同号+应付佣金)
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        private string GetZyComments(DynamicObject dataEntity)
        {
            var comments = new StringBuilder();
            var orderNo = Convert.ToString(dataEntity["fbillno"]);
            var channelId = Convert.ToString(dataEntity["fchannel"]);
            comments.Append("预提");
            if (!channelId.IsNullOrEmptyOrWhiteSpace())
            {
                var cannelDy = dataEntity["fchannel_ref"] as DynamicObject;
                if (cannelDy == null)
                {
                    cannelDy = this.UserContext.LoadBizBillHeadDataById("ste_channel",channelId,"fname,fnumber");
                }
                if (cannelDy != null)
                {
                    comments.Append($"+{Convert.ToString(cannelDy["fname"])}");
                }
            }

            comments.Append($"+{orderNo}+{Convert.ToString(dataEntity["fplanbrokerage"])}");
            return comments.ToString();
        }


        /// <summary>
        /// 打包前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforePackSourceBill(BeforePackSourceBillEventArgs e)
        {
            base.BeforePackSourceBill(e);

            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;

            
        }



        /// <summary>
        /// 来源单据打包后事件
        /// </summary>
        /// <param name="e"></param>
        public override void AfterPackSourceBill(AfterPackSourceBillEventArgs e)
        {
            base.AfterPackSourceBill(e);

            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            
        }
        
    }
}
