using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.STE.Channel
{
    /// <summary>
    /// 合同渠道：慕思同步
    /// </summary>
    [InjectService]
    [FormId("ste_channel")]
    [OperationNo("syncfrommusi")]
    [ThirdSystemId("musi")]
    public class TransferFromMuSi : AbstractSyncDataFromMuSiPlugIn
    {
        //表示经销商主数据
        private const string moduleVal = "distributor";
        //three_store_type表示三级渠道/大客户
        private const string typeVal = "three_store_type";

        public override void BeforeSave(BeforeSaveEventArgs e)
        {
            base.BeforeSave(e);

            var dataEntities = e.DataEntitys;
            if (dataEntities.IsNullOrEmpty()) return;

            var channelAll = GetChannel();
            var lstValidDataEntities = new List<DynamicObject>();
            foreach (var dataEntity in e.DataEntitys)
            {
                var fcontacts = dataEntity["fcontacts"];
                var fphone = dataEntity["fphone"];
                var fname = dataEntity["fname"];
                var fnumber = dataEntity["fnumber"];
                var fcompany = dataEntity["fcompany"];
                var fmainorgid = dataEntity["fmainorgid"];

                if (fcontacts.IsNullOrEmptyOrWhiteSpace() || fphone.IsNullOrEmptyOrWhiteSpace())
                    continue;

                var entDt = channelAll.Where(x => Convert.ToString(x["fcontacts"]) == Convert.ToString(fcontacts)
                   && Convert.ToString(x["fphone"]) == Convert.ToString(fphone)
                    && Convert.ToString(x["fmainorgid"]) == Convert.ToString(fmainorgid)
                   ).ToList();
                //联系人和号码重复更新
                if (entDt != null && entDt.Count > 0)
                {
                    string sqlStr = $@"update t_ste_channel set fname='{fname}',fnumber='{fnumber}',fcompany='{fcompany}
' where fcontacts='{fcontacts}' and fphone='{fphone}' and fmainorgid='{fmainorgid}'";

                    var dbServiceEx = Context.Container.GetService<IDBServiceEx>();
                    dbServiceEx.Execute(Context, sqlStr);
                    continue;
                }
                //接口同步数据标记
                dataEntity["fisapi"] = "1";
                lstValidDataEntities.Add(dataEntity);
            }

            e.DataEntitys = lstValidDataEntities;
        }

        public override void AfterSave(AfterSaveEventArgs e)
        {
            base.AfterSave(e);

            if (e.DataEntitys.IsNullOrEmpty()) return;
        }


        public override void BeforeFieldMapping(BeforeFieldMappingEventArgs e)
        {
            base.BeforeFieldMapping(e);

            var fldId = e.FieldEntry["fmyfieldid"]?.ToString();
            string fextfieldid = e.FieldEntry["fextfieldid"]?.ToString();
            e.ExternalData.TryGetValue("module", out var module);
            e.ExternalData.TryGetValue("type", out var type);
            e.ExternalData.TryGetValue("explain_json", out var explain_json);
            if (
                (!module.IsNullOrEmptyOrWhiteSpace() && Convert.ToString(module) != moduleVal
                && !type.IsNullOrEmptyOrWhiteSpace() && Convert.ToString(type) != typeVal)
               || explain_json == null)
            {
                return;
            }

            switch (fldId.ToLower())
            {
                case "fname":
                    e.ExternalData.TryGetValue("content", out var fname);
                    e.Result = fname;
                    e.Cancel = true;
                    break;
                case "fcompany":
                    var partnerBusinessLicenseName = JObject.Parse(explain_json.ToString())["partnerBusinessLicenseName"];
                    e.Result = partnerBusinessLicenseName;
                    e.Cancel = true;
                    break;
                case "fnumber":
                    var partnerCreditCode = JObject.Parse(explain_json.ToString())["partnerCreditCode"];
                    e.Result = partnerCreditCode;
                    e.Cancel = true;
                    break;
                case "fcontacts":
                    var partnerContactPersonName = JObject.Parse(explain_json.ToString())["partnerContactPersonName"];
                    e.Result = partnerContactPersonName;
                    e.Cancel = true;
                    break;
                case "fphone":
                    var partnerContactPersonPhone = JObject.Parse(explain_json.ToString())["partnerContactPersonPhone"];
                    e.Result = partnerContactPersonPhone;
                    e.Cancel = true;
                    break;
                case "ftype":
                    e.Result = "channel_type_01";
                    e.Cancel = true;
                    break;
                case "fmainorgid":
                    var licenseNumber = JObject.Parse(explain_json.ToString())["licenseNumber"];
                    var data = GetOrgid(Convert.ToString(licenseNumber));
                    if (data != null && data.Count > 0)
                    {
                        e.Result = data.FirstOrDefault()["fid"];
                        e.Cancel = true;
                    }
                    break;
            }

        }

        /// <summary>
        /// 得到全部合作渠道 
        /// </summary>
        /// <returns></returns>
        private DynamicObjectCollection GetOrgid(string fnumber)
        {
            string strSql = $"select top 1 fid from t_bas_agent with(nolock) where fnumber='{fnumber}' ";
            var data = this.Context.ExecuteDynamicObject(strSql, new List<SqlParam>() { });
            return data;
        }

        /// <summary>
        /// 得到全部合作渠道 
        /// </summary>
        /// <returns></returns>
        private DynamicObjectCollection GetChannel()
        {
            string strSql = "select fcontacts,fphone,fmainorgid from t_ste_channel with(nolock) ";
            var data = this.Context.ExecuteDynamicObject(strSql, new List<SqlParam>() { });
            return data;
        }
    }
}
