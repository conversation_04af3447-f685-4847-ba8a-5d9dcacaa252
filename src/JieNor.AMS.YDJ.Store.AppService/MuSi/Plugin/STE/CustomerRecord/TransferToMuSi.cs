using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.STE.CustomerRecord
{
    /// <summary>
    /// 商机关闭：慕思同步
    /// </summary>
    [InjectService]
    [FormId("ydj_customerrecord")]
    [OperationNo("synctomusi")]
    [ThirdSystemId("musi")]
    public class TransferToMuSi : AbstractSyncDataToMuSiPlugIn
    {
        /// <summary>
        /// 字段映射前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeFieldMapping(BeforeFieldMappingEventArgs e)
        {
            base.BeforeFieldMapping(e);

            if (e.Entity == null || e.FieldEntry == null) return;

            var dataEntity = e.Entity;

            string extFieldId = Convert.ToString(e.FieldEntry["fextfieldid"]).ToLower();
            switch (extFieldId)
            {
                case "status":
                    {
                        e.Cancel = true;
                        e.Result = "";

                        //string fchancestatus = Convert.ToString(dataEntity["fchancestatus"]);
                        //int status = 0;
                        //switch (fchancestatus)
                        //{
                        //    case "chance_status_04":
                        //        status = 4;
                        //        break;
                        //    case "chance_status_06":
                        //        status = 5;
                        //        break;
                        //}
                        e.Result = "Close";
                    }
                    break;
                case "type":
                    {
                        e.Cancel = true;
                        e.Result = "金蝶系统";
                    }
                    break;
            }
        }
       
    }
}
