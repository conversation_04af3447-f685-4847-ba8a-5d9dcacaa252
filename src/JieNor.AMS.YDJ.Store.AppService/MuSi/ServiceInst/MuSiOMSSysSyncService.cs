using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.ServiceInst
{
    /// <summary>
    /// 慕思OMS平台系统数据集成服务
    /// </summary>
    [InjectService("musiomssync")]
    //[ServiceMetaAttribute("name", "慕思数据集成服务")]
    [ServiceMetaAttribute("serviceid", YDJHtmlElementType.HtmlBizService_MuSiOMSSysSync)]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public class MuSiOMSSysSyncService : AbstractBaseService
    {
        /// <summary>
        /// 关闭事务
        /// </summary>
        public override int TransactionMode => _tranMode;

        /// <summary>
        /// 事务模式
        /// </summary>
        private int _tranMode = 1;

        /// <summary>
        /// 外部应用标识
        /// </summary>
        protected string ExtAppId { get; set; }

        /// <summary>
        /// 字段映射对象主键
        /// </summary>
        protected string BillMapId { get; set; }

        /// <summary>
        /// 外部应用动态对象
        /// </summary>
        protected DynamicObject ExtAppObj { get; set; }

        /// <summary>
        /// 服务初始化参数
        /// </summary>
        /// <param name="servicePara"></param>
        protected override void OnServiceInitialized(string servicePara)
        {
            base.OnServiceInitialized(servicePara);

            this.Option.SetVariableValue("servicePara", servicePara);

            //服务参数
            if (servicePara.IsNullOrEmptyOrWhiteSpace()) return;
            var jsonPara = JObject.Parse(servicePara);
            if (jsonPara == null) return;

            //服务配置
            var jsonSerConfig = Convert.ToString(jsonPara["serConfig"]);
            if (jsonSerConfig.IsNullOrEmptyOrWhiteSpace()) return;
            var joConfig = JObject.Parse(jsonSerConfig);

            //允许配置决定事务模式：默认开启
            var tranMode = joConfig["tranMode"]?.ToString() ?? "0";
            int.TryParse(tranMode, out _tranMode);

            this.ExtAppId = joConfig["extAppId"]?.ToString();
            this.BillMapId = joConfig["billMapId"]?.ToString();
        }

        /// <summary>
        /// 处理业务数据同步逻辑
        /// </summary>
        /// <param name="dataEntities"></param>
        public override void ExecuteService(ref DynamicObject[] dataEntities)
        {
            //可能计划任务定时调用时传递过来的
            var fieldMapObj = this.GetQueryOrSimpleParam<DynamicObject>("__fieldMapObj__");
            if (fieldMapObj == null)
            {
                if (this.BillMapId.IsNullOrEmptyOrWhiteSpace()) return;

                //业务对象映射关系
                fieldMapObj = this.Context.LoadBizDataById("si_musibizobjmap", this.BillMapId)?.Clone(false, false) as DynamicObject;
            }
            if (fieldMapObj == null) return;
            this.BillMapId = fieldMapObj["id"] as string;

            //外部应用
            var extAppObj = this.GetExternalAppObject(fieldMapObj["fextappid"] as string);
            if (extAppObj == null)
            {
                throw new BusinessException("同步失败，找不到待同步到的目标系统信息！");
            }
            this.ExtAppObj = extAppObj;

            //同步方向
            var isToMuSi = Convert.ToString(fieldMapObj["fsyncdir"]).EqualsIgnoreCase("currenttomusi");
            var opCode = isToMuSi ? "tomusioms" : "frommusi";

            var mapMeta = this.MetaModelService.LoadFormModel(this.Context, "si_musibizobjmap");
            // 异步执行
            var isAsync = mapMeta.GetField("fisasync") != null && Convert.ToBoolean(fieldMapObj["fisasync"]);

            if (isAsync)
            {
                var billData = dataEntities;

                Task.Run(() =>
                {
                    this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, billData, opCode, this.Option.ToDictionary());
                });
            }
            else
            {
                var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, dataEntities, opCode, this.Option.ToDictionary());

                this.Result.MergeResult(result);
                this.Result.SrvData = result.SrvData;
            }
        }

        /// <summary>
        /// 获取外部应用产品参数
        /// </summary>
        /// <param name="thirdSysId"></param>
        /// <returns></returns>
        private DynamicObject GetExternalAppObject(string thirdSysId)
        {
            return this.Context.LoadBizDataById("sys_externalapp", thirdSysId);
        }

        /// <summary>
        /// 获取外部应用标识
        /// </summary>
        /// <param name="bizObjMapId"></param>
        /// <returns></returns>
        private string GetExternalAppId(string bizObjMapId)
        {
            var bizObjMap = this.Context.LoadBizDataById("si_musibizobjmap", bizObjMapId);
            return Convert.ToString(bizObjMap?["fextappid"]);
        }
    }
}
