using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.SchedulerTask
{
    /// <summary>
    /// 集成操作日志自动重试
    /// </summary>
    [InjectService]
    [TaskSvrId("operationlogautoretry")]
    [Caption("集成操作日志自动重试（在总部执行）")]
    public class OperationLogAutoRetryTask : AbstractScheduleWorker
    {
        /// <summary>
        /// 计划任务配置时的参数设置界面
        /// </summary>
        public override string ParamFormId => "si_operationlogautoretrytaskparam";

        /// <summary>
        /// 计划同步任务参数
        /// </summary>
        protected Dictionary<string, object> JobParameter { get; set; }

        protected IAgentService AgentService { get; set; }

        protected IHttpServiceInvoker Gateway { get; set; }

        protected ILogServiceEx LogService { get; set; }

        protected IMetaModelService MetaModelService { get; set; }

        protected HtmlForm OperationLogMeta { get; set; }

        protected UserContext SlaveContext { get; set; }

        protected IDBService DBService { get; set; }

        /// <summary>
        /// 执行任务逻辑
        /// </summary>
        /// <returns></returns>
        protected override async Task DoExecute()
        {
            this.UserContext.SetUserSession(this.UserContext.CreateAdminDbContext().UserSession);
            this.SlaveContext = this.UserContext.CreateSlaveDBContext();

            this.Gateway = this.UserContext.Container.GetService<IHttpServiceInvoker>();
            this.AgentService = this.UserContext.Container.GetService<IAgentService>();
            this.LogService = this.UserContext.Container.GetService<ILogServiceEx>();
            this.MetaModelService = this.UserContext.Container.GetService<IMetaModelService>();
            this.DBService = this.UserContext.Container.GetService<IDBService>();

            this.OperationLogMeta = this.MetaModelService.LoadFormModel(this.UserContext, "si_operationlog");

            this.JobParameter = Convert.ToString(this.TaskObject.Parameter).FromJson<Dictionary<string, object>>() ?? new Dictionary<string, object>();

            var fbatchsize = this.JobParameter.GetInt("fbatchsize");
            if (fbatchsize <= 0)
            {
                fbatchsize = 10;
            }

            if (fbatchsize > 100)
            {
                fbatchsize = 100;
            }

            var fapilist = this.JobParameter.GetString("fapilist")
                .Split(new string[] { "\r\n", "\n" }, StringSplitOptions.RemoveEmptyEntries)
                .Select(s => s?.Trim())
                .ToList();

            if (!fapilist.Any())
            {
                this.WriteLog("配置项 【处理接口列表】 没有数据！");

                return;
            }

            this.WriteLog($"处理接口列表：{fapilist.Select(s => $"【{s}】").JoinEx("", false)}");

            var sql =
                $@"
select top {fbatchsize} {this.OperationLogMeta.BillPKFldName} as id 
from {this.OperationLogMeta.BillHeadTableName} with(nolock) 
where fopstatus='3' and fcanretry='1' and fisretry='0' and frequestdata<>'' and fopapi in ({fapilist.JoinEx(",", true)})
order by {this.OperationLogMeta.BillPKFldName}
";

            var ids = this.SlaveContext.ExecuteDynamicObject(sql, new List<SqlParam>())
                ?.Select(s => Convert.ToString(s["id"])).ToList();

            if (ids == null || !ids.Any())
            {
                this.WriteLog("没有可重试的集成操作日志！");

                return;
            }

            var logs = this.SlaveContext.LoadBizDataById(this.OperationLogMeta.Id, ids);
            var option = new Dictionary<string, object>();

            foreach (var log in logs)
            {
                var number = Convert.ToString(log["ffailnumbers"]);

                var result = this.Gateway.InvokeBillOperation(this.SlaveContext, this.OperationLogMeta.Id, new[] { log }, "retry",
                    option);

                this.WriteLog($"重试日志【{log["fmyobjectid"]}】{number} {log["fopname"]}({log["fopapi"]})：{result}");

                Thread.Sleep(1 * 1000);
            }
        }
    }
}