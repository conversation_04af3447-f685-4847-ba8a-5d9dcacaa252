using JieNor.Framework;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Utils
{
    public class SignConvertUtil
    {
        public static string generateSign(string callerService, string contextPath, string version, string timestamp,
            string serviceSecret, string requestPath)
        {
            string sign = string.Empty;

            if (callerService.IsNullOrEmptyOrWhiteSpace() || contextPath.IsNullOrEmptyOrWhiteSpace() ||
                timestamp.IsNullOrEmptyOrWhiteSpace() || serviceSecret.IsNullOrEmptyOrWhiteSpace())
            {
                return sign;
            }

            SortedDictionary<string, string> map = new SortedDictionary<string, string>();
            map.Add("callerService", callerService);
            map.Add("contextPath", contextPath);
            try
            {
                if (requestPath != null)
                {
                    StringBuilder sb = new StringBuilder();
                    foreach (string part in requestPath.Split('/'))
                    {
                        sb.Append("/").Append(Uri.EscapeDataString(part));
                    }

                    map.Add("requestPath", sb.ToString().Substring(1));
                }

                map.Add("timestamp", timestamp);
                map.Add("v", version);
                sign = generateMD5Sign(serviceSecret, map);
            }
            catch (Exception e)
            {
                return "";
            }

            return sign;
        }

        private static String generateMD5Sign(String secret, SortedDictionary<string, string> parameters)
        {
            //var md5 = new MD5CryptoServiceProvider();
            //.net 8中的,MD5CryptoServiceProvider类在.net 8中已经过时了
            var md5Hash = MD5.Create();
            //MessageDigest md5 = MessageDigest.getInstance("MD5");

            var newMd5Byte = md5Hash.ComputeHash(Encoding.UTF8.GetBytes(generateConcatSign(secret, parameters)));

            //var bytes = md5.ComputeHash(Encoding.UTF8.GetBytes(generateConcatSign(secret, parameters)));

            //byte[] bytes = md5.digest(generateConcatSign(secret, parameters).getBytes("utf- 8"));

            //var oldMd5 = byteToHex(bytes);

            //var newMd5 = byteToHex(newMd5Byte);

            return byteToHex(newMd5Byte);
        }

        private static String generateConcatSign(String secret, SortedDictionary<string, string> parameters)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append(secret);
            var keys = parameters.Keys;
            foreach (var key in keys)
            {
                var values = parameters[key];
                sb.Append(key);
                sb.Append(values);
            }

            return sb.Append(secret).ToString();
        }

        private static String byteToHex(byte[] bytesIn)
        {
            StringBuilder sb = new StringBuilder();
            foreach (var byteIn in bytesIn)
            {
                //这个与Java中的String bt = Integer.toHexString(byteIn & 0xff);效果一样
                var bt = (byteIn & 0xff).ToString("x2");

                if (bt.Length == 1)
                {
                    sb.Append(0).Append(bt);
                }
                else
                {
                    sb.Append(bt);
                }
            }

            return sb.ToString().ToUpper();
        }
    }

}
