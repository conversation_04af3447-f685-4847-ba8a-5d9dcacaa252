using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.DTO
{
    /// <summary>
    /// 直营中台出库单
    /// </summary>
    public class SoStockOutDTO : BaseDTO
    {
        public string zyPZ { get; set; }
        /// <summary>
        /// 终端交货单号
        /// </summary>
        public string zyInvNum { get; set; }
        public List<Entity> entity { get; set; }

    }
    public class Entity
    {
        /// <summary>
        /// 行ID
        /// </summary>
        public string zyItemId { get; set; }

        /// <summary>
        /// 行号
        /// </summary>
        public string zyItemLine { get; set; }

        /// <summary>
        /// SAP销售订单编号
        /// </summary>
        public string zyOrdNum { get; set; }

        /// <summary>
        /// SAP销售订单行号
        /// </summary>
        public string zyOrdId { get; set; }

        /// <summary>
        /// SAP销售订单行号
        /// </summary>
        public string zyOrdLine { get; set; }

        /// <summary>
        ///物料号
        /// </summary>
        public string zyProdCode { get; set; }

        /// <summary>
        ///出库数量
        /// </summary>
        public string zyQty { get; set; }

        /// <summary>
        ///工厂
        /// </summary>
        public string zyFactory { get; set; }

        /// <summary>
        ///库存地点
        /// </summary>
        public string zyInvLoc { get; set; }

        /// <summary>
        ///批次
        /// </summary>
        public string zyBatchNum { get; set; }

        /// <summary>
        ///单位
        /// </summary>
        public string zyUnit { get; set; }
    }
}
