using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Service
{
    [InjectService]
    public class BillNoServiceExt : IBillNoServiceExt
    {
        private const string YDJORDER = "ydj_order";
        private const string YDJPURCHASEORDER = "ydj_purchaseorder";
        private List<string> formIds = new List<string>() { YDJORDER, YDJPURCHASEORDER };

        public string GetBillNoPreFit(UserContext ctx, string formId, DynamicObject billData, string oldPreFit)
        {
            return oldPreFit;
        }

        /// <summary>
        /// 编号产生之后，添加后缀
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="billNos"></param>
        /// <returns></returns>
        public Dictionary<string, DynamicObject> AppendBillNoPreFix(UserContext ctx, string formId, Dictionary<string, DynamicObject> billNos)
        {
            // 如果不是销售合同，直接返回
            if (!formIds.Contains(formId))
            {
                return billNos;
            }
            Dictionary<string, DynamicObject> res = new Dictionary<string, DynamicObject>();
            foreach (var bill in billNos)
            {
                if (formId == YDJORDER)
                {
                    var billType = bill.Value.GetValue<string>("fbilltype", "");
                    if (bill.Value["fbilltype_ref"] == null)
                    {
                        var htmlForm = ctx.Container.GetService<IMetaModelService>()?.LoadFormModel(ctx, formId);
                        // 加载数据
                        var refObjMgr = ctx.Container.GetService<LoadReferenceObjectManager>();
                        refObjMgr?.Load(ctx, new object[] { bill.Value }, true, htmlForm, new List<string> { "fbilltype" });
                    }
                    billType = Convert.ToString((bill.Value["fbilltype_ref"] as DynamicObject)?["fname"]);
                    //if (billType == "ydj_order_vsix_swj")
                    if (billType == "v6定制柜合同")
                    {
                        bill.Value.SetValue("fbillno", "DZ" + bill.Key);
                    }
                }
                else if (formId == YDJPURCHASEORDER)
                {
                    var fomsservice = bill.Value.GetValue<bool>("fomsservice", false);
                    if (fomsservice)
                    {
                        bill.Value.SetValue("fbillno", "DZ" + bill.Key);
                    }
                }

            }
            return res;
        }

        /// <summary>
        /// 获取最大流水号
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="svc"></param>
        /// <param name="htmlForm"></param>
        /// <param name="fld"></param>
        /// <param name="prefix"></param>
        /// <param name="len"></param>
        /// <returns></returns>
        public string GetMaxNo(UserContext ctx, IDBService svc, HtmlForm htmlForm, HtmlField fld, string prefix, int len, out bool isHandled)
        {
            isHandled = true;
            string dzPrefix = $"DZ{prefix}";
            //去除仅仅限制销售合同、采购订单的判断
            //if (!formIds.Contains(htmlForm.Id))
            //{
            //    isHandled = false;
            //    return string.Empty;
            //}

            var mainorgidWhere = "";
            var mainorgidField = htmlForm.GetField("fmainorgid");
            //不用加组织条件根据 XSHT1010064 开头找最大编号即可，因为可能会有那种 更换营业执照的情况，要考虑子经销商已经做个单了。
            //if (mainorgidField != null)
            //{
            //    mainorgidWhere = $" and fmainorgid='{ctx.Company}'";
            //} 
            var billNoPattern = prefix;
            // 销售合同流水号
            string sql = string.Format(@"select max({5}) as fno from {1} with(nolock) where  {0} like  '{2}%'   {3}    and len({0})={4}",
                fld.Id, htmlForm.HeadEntity.TableName, billNoPattern, mainorgidWhere, len, htmlForm.NumberFldKey);

            // 定制单流水号
            string sqlDZ = string.Format(@"select max({5}) as fno from {1} with(nolock) where  {0} like  'DZ{2}%'   {3}    and len({0})={4}",
               fld.Id, htmlForm.HeadEntity.TableName, billNoPattern, mainorgidWhere, len + 2,htmlForm.NumberFldKey);

            // 获取最大值           
            string no = string.Empty;
            using (var reader = svc.ExecuteReader(ctx, sql))
            {
                if (reader.Read())
                {
                    no = reader.GetValueToString("fno");
                }
            }
            string dzno = string.Empty;
            using (var reader = svc.ExecuteReader(ctx, sqlDZ))
            {
                if (reader.Read())
                {
                    dzno = reader.GetValueToString("fno");
                }
            }

            if (dzno.IsNullOrEmptyOrWhiteSpace() && !no.IsNullOrEmptyOrWhiteSpace())
            {
                return getSeriNo(prefix, no).ToString();
            }
            if (no.IsNullOrEmptyOrWhiteSpace() && !dzno.IsNullOrEmptyOrWhiteSpace())
            {
                return getSeriNo(dzPrefix, dzno).ToString();
            }
            // 比较大小
            var noInt = getSeriNo(prefix, no);
            var dznoInt = getSeriNo(dzPrefix, dzno);

            return noInt > dznoInt ? noInt.ToString() : dznoInt.ToString();
        }

        private static int getSeriNo(string prefix, string no)
        {
            //表里面没有数据，返回0
            if (no.IsNullOrEmptyOrWhiteSpace())
            {
                return 0;
            }
            return int.Parse(no.ToUpper().Replace(prefix, ""));
        }
    }
}
