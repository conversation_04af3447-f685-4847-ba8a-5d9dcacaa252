using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.Utils;
using JieNor.AMS.YDJ.Core;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.AMS.YDJ.Store.AppService
{
    /// <summary>
    /// 采购订单服务定义
    /// </summary>
    [InjectService]
    public class PurchaseOrderService : IPurchaseOrderService
    {
        /// <summary>
        /// 检查采购订单是否是协同采购
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="dataEntity">采购订单实体对象</param>
        /// <returns>是否是协同采购</returns>
        public bool CheckIsSync(UserContext userCtx, DynamicObject dataEntity)
        {
            var supplierId = dataEntity["fsupplierid"] as string;
            if (supplierId.IsNullOrEmptyOrWhiteSpace()) return false;

            var htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, "ydj_supplier");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));

            var supplier = dm.Select(supplierId) as DynamicObject;
            if (supplier != null)
            {
                return Convert.ToString(supplier["fcoostate"]).EqualsIgnoreCase("已协同")
                        && !supplier["fcoocompanyid"].IsNullOrEmptyOrWhiteSpace()
                        && !supplier["fcooproductid"].IsNullOrEmptyOrWhiteSpace();
            }

            return false;
        }



        /// <summary>
        /// 计算结算信息（模拟前端计算）
        /// 注：需要自行调用保存
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="purchaseOrders">采购订单</param>
        /// <returns></returns>
        public void CalculateSettlement(UserContext userCtx, IEnumerable<DynamicObject> purchaseOrders)
        {
            foreach (var purchaseOrder in purchaseOrders)
            {
                CalculateSettlement(userCtx, purchaseOrder);
            }
        }

        /// <summary>
        /// 计算结算信息（模拟前端计算）
        /// 注：需要自行调用保存
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="purchaseOrder">采购订单</param>
        /// <returns></returns>
        public void CalculateSettlement(UserContext userCtx, DynamicObject purchaseOrder)
        {
            var productEntrys = purchaseOrder["fentity"] as DynamicObjectCollection;
            // 成交金额
            var billAmount = 0M;
            // 货品原值
            var dealAmount = 0M;
            // 折扣额
            var distAmount = 0M;
            //已付金额
            var settleAmount = Convert.ToDecimal(purchaseOrder?["fsettleamount"]);

            foreach (var productEntry in productEntrys)
            {
                var fcooeditstatus = Convert.ToString(productEntry["fcooeditstatus"]);
                if (!fcooeditstatus.EqualsIgnoreCase("3"))
                {
                    billAmount += Convert.ToDecimal(productEntry["fdealamount"]);
                }

                dealAmount += Convert.ToDecimal(productEntry["famount"]);
                distAmount += Convert.ToDecimal(productEntry["fdistamount"]);
            }

            // 成交金额
            purchaseOrder["ffbillamount"] = billAmount;
            // 货品原值
            purchaseOrder["fdealamount"] = dealAmount;
            // 折扣额
            purchaseOrder["fdistamount"] = distAmount;
            // 待结算金额 = 成交金额 - 已付金额
            purchaseOrder["fpayamount"] = billAmount - settleAmount;
        }

        /// <summary>
        /// 计算【结算状态】
        /// 注：需要自行调用保存
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="orders">销售合同实体对象</param>
        /// <returns></returns>
        public void CalculateReceiptStatus(UserContext userCtx, DynamicObject purchaseOrder)
        {
            if (purchaseOrder.IsNullOrEmpty()) return;

            // 已付金额
            var settledAmount = Convert.ToDecimal(purchaseOrder["fsettleamount"]);
            // 成交金额
            var sumdealamount = Convert.ToDecimal(purchaseOrder["ffbillamount"]);

            //结算状态
            if (settledAmount <= 0)
            {
                //全款未付
                purchaseOrder["fpaystatus"] = "paystatus_type_01";
            }
            else if (settledAmount > 0 && settledAmount < sumdealamount)
            {
                //部分付款
                purchaseOrder["fpaystatus"] = "paystatus_type_02";
            }
            else if (settledAmount >= sumdealamount)
            {
                //全款已付
                purchaseOrder["fpaystatus"] = "paystatus_type_03";
            }
        }

        /// <summary>
        /// 检查非标审批状态
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="purchaseOrder"></param>
        /// <param name="errorMsg"></param>
        /// <returns></returns>
        public bool CheckUnstdStatus(UserContext userCtx, DynamicObject purchaseOrder, out string errorMsg)
        {
            errorMsg = string.Empty;

            //// 单据类型=期初采购订单时，不校检
            //string fbilltype = Convert.ToString(purchaseOrder["fbilltypeid"]);
            //if (fbilltype.EqualsIgnoreCase("ydj_purchaseorder_qc")) return true;

            var entrys = purchaseOrder["fentity"] as DynamicObjectCollection;


            //是自建商品，则获取经销商参数【自建商品零售价可编辑】判断
            var profileService = userCtx.Container.GetService<ISystemProfile>();
            //Task5509 如果变量【零售价可编辑】=“是”，即使【非标商品】=“是”，则在操作<提交> < 审核 > 时需跳过【非标审批状态】的校验。
            var owneditprice = profileService.GetSystemParameter(userCtx, "bas_storesysparam", "fowneditprice", false);//参数【自建商品零售价可编辑】

            var productIds = entrys.Select(x => Convert.ToString(x["fmaterialid"]));
            var sql = $@"select t1.fid,t1.fnumber,t1.fmainorgid,t2.feditprice from t_bd_material t1 with(nolock)
                        left join ser_ydj_category t2 with(nolock) on t1.fcategoryid=t2.fid
                        where t1.fid in ({productIds.JoinEx(",", true)})";
            var dbService = userCtx.Container.GetService<IDBService>();
            var productinfo = dbService.ExecuteDynamicObject(userCtx, sql);


            List<string> errorProductIds = new List<string>();

            foreach (var entry in entrys)
            {
                var result = productinfo.Where(x => Convert.ToString(x["fid"]) == Convert.ToString(entry["fmaterialid"])).FirstOrDefault();
                var fmainorgid = Convert.ToString(result["fmainorgid"]);//商品数据来源
                //判断商品是自建商品还是总部商品
                if (fmainorgid == userCtx.Company)
                {
                    if (owneditprice) continue;
                }
                else
                {
                    //是总部商品，则判断商品.商品类别.零售价可编辑=“是”
                    if (Convert.ToString(result["feditprice"]) == "1") continue;
                }

                string funstdtypestatus = Convert.ToString(entry["funstdtypestatus"]);
                bool funstdtype = Convert.ToBoolean((entry["funstdtype"]));
                // 其中一个商品行存在【是否非标】=【是】且【非标审批状态】!=【终审】（总部审批通过）
                if (funstdtype && !funstdtypestatus.EqualsIgnoreCase("06"))
                {
                    errorProductIds.Add(Convert.ToString(entry["fmaterialid"]));
                }
            }

            if (errorProductIds.Any())
            {
                var products = userCtx.LoadBizDataById("ydj_product", errorProductIds);

                foreach (var errorProductId in errorProductIds)
                {
                    var product = products.FirstOrDefault(s =>
                        Convert.ToString(s["id"]).EqualsIgnoreCase(errorProductId));
                    if (product == null) continue;

                    errorMsg += $"非标商品【{product["fname"]}】先非标审批等总部非标终审通过再提交！\r\n";
                }

                return false;
            }

            return true;
        }

        /// <summary>
        /// 通过送达方查找供应商
        /// </summary>
        /// <param name="deliverId">送达方id</param>
        /// <returns></returns>
        public string GetSupplierIdByDeliverId(UserContext userCtx, string deliverId)
        {
            if (deliverId.IsNullOrEmptyOrWhiteSpace()) return string.Empty;

            var metaModelService = userCtx.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(userCtx, "bas_deliver");
            var dm = userCtx.Container.GetService<IDataManager>();
            var formDt = htmlForm.GetDynamicObjectType(userCtx);
            dm.InitDbContext(userCtx, formDt);
            //根据id获取
            var initstockbillObj = dm.Select(deliverId) as DynamicObject;

            //// 按实控人 获取当前用户对应的所有经销商组织
            //var agentInfos = new ProductDataIsolateHelper().GetCurrentUserAgentAll(userCtx);
            ////组织
            //var agents = agents.Select(o => o.OrgId).ToList();
            //var cid = userCtx.IsTopOrg ? userCtx.TopCompanyId : userCtx.Company;
            //agents.Add(cid);

            // 送达方带出的供应商取当前企业下的。
            var agents = new[] { userCtx.Company };

            if (initstockbillObj != null)
            {
                string strSql = @"select top 1 fid from t_ydj_supplier where forgid=@forgid and fmainorgid in ('{0}')".Fmt(string.Join("','", agents));
                var sqlParam = new List<SqlParam>
                {
                    new SqlParam("@forgid", DbType.String, Convert.ToString(initstockbillObj["fsaleorgid"]))
                };
                using (var dr = userCtx.ExecuteReader(strSql, sqlParam))
                {
                    if (dr.Read())
                    {
                        return dr.GetValueToString("fid");
                    }
                }
            }
            return string.Empty;
        }

        /// <summary>
        /// 变更中或已提交状态
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="fbillno"></param>
        /// <returns></returns>
        public bool ChangeOrSubmitStatus(UserContext userCtx, DynamicObject dyn)
        {
            var orgin = userCtx.Company;
            if (dyn["fsourcetype"].IsNullOrEmptyOrWhiteSpace())
                return true;

            string strSql = string.Format(@"select t2.fchangestatus,t2.fstatus from t_{2} t1
            join t_ydj_purchaseorder t2 on t1.fsourcenumber =t2.fbillno where t1.fbillno='{0}' and t1.fmainorgid='{1}' ", Convert.ToString(dyn["fsourcenumber"]), orgin, Convert.ToString(dyn["fsourcetype"]));
            var data = userCtx.ExecuteDynamicObject(strSql, new List<SqlParam>() { });
            if (data.Count > 0)
            {
                var changestatus = Convert.ToString(data[0]["fchangestatus"]);
                var fstatus = Convert.ToString(data[0]["fstatus"]);
                if (changestatus == "1" || fstatus.EqualsIgnoreCase("D"))
                    return false;//变更中或已提交状态
                else
                    return true;
            }
            return true;
        }

        ///// <summary>
        ///// 通过经理销商ID 查到对应的送达方
        ///// </summary>
        ///// <param name="userCtx"></param>
        ///// <param name="fagentids"></param>
        ///// <returns></returns>
        //public IEnumerable<DynamicObject> DeliverSaleorgids(UserContext userCtx, List<string> fagentids)
        //{
        //    var metaModelService = userCtx.Container.GetService<IMetaModelService>();
        //    var htmlForm = metaModelService.LoadFormModel(userCtx, "bas_deliver");
        //    var dm = userCtx.Container.GetService<IDataManager>();
        //    dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));
        //    var where = "fagentid in ('{0}')".Fmt(string.Join("','", fagentids));
        //    var sqlParam = new List<SqlParam>
        //    {

        //    };
        //    var reader = userCtx.GetPkIdDataReader(htmlForm, where, sqlParam);
        //    var data = dm.SelectBy(reader).OfType<DynamicObject>();
        //    return data;
        //}

        ///// <summary>
        ///// 通过送达方查找对应的供应商
        ///// </summary>
        ///// <param name="userCtx"></param>
        ///// <returns></returns>
        //public IEnumerable<DynamicObject> SearchSupplierBySaleorgid(UserContext userCtx)
        //{
        //    // 按 城市 + 实控人 获取当前用户对应的所有经销商组织
        //    var AgentInfos = new ProductDataIsolateHelper().GetCurrentUserAgentInfos(userCtx);
        //    //组织
        //    var Agents = AgentInfos.Select(o => o.OrgId).ToList();
        //    var cid = userCtx.IsTopOrg ? userCtx.TopCompanyId : userCtx.Company;
        //    Agents.Add(cid);
        //    var deliverList = DeliverSaleorgids(userCtx, Agents);
        //    //送达方组织
        //    var forgids = new List<string>();
        //    forgids = deliverList.Select(x => Convert.ToString(x["fsaleorgid"])).ToList();

        //    var metaModelService = userCtx.Container.GetService<IMetaModelService>();
        //    var htmlForm = metaModelService.LoadFormModel(userCtx, "ydj_supplier");
        //    var dm = userCtx.Container.GetService<IDataManager>();
        //    dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));
        //    var where = "forgid in ('{0}')".Fmt(string.Join("','", forgids));
        //    var sqlParam = new List<SqlParam>
        //    {

        //    };
        //    var reader = userCtx.GetPkIdDataReader(htmlForm, where, sqlParam);
        //    var data = dm.SelectBy(reader).OfType<DynamicObject>();
        //    return data;

        //}




        /// <summary>
        /// 获取明细行匹配的送达方
        /// </summary>
        /// <param name="purchaseOrder">采购订单</param>
        /// <param name="entrys">待同步商品行</param>
        /// <returns>
        /// {
        ///     "明细行id": 送达方对象
        /// }
        /// 如果明细行没有匹配的送达方，则不存在此行
        /// </returns>
        public Dictionary<string, DynamicObject> GetMatchDeliver(UserContext userCtx, DynamicObject purchaseOrder, List<DynamicObject> entrys)
        {
            /* Feat#4620
             * 2.   《采购订单》的<非标审批>的接口增加字段映射, 将【送达方】传给中台 (编码与名称), 如果是二级分销商则会根据上游《销售合同》的数据逻辑找到送达方传给中台
             * A.     如果《采购订单》单据头的【送达方】不为空时, 直接传给中台即可
             * B.     如果《采购订单》单据头的【送达方】为空 且 当前企业为二级分销商时, 按以下逻辑匹配到送达方后再传给中台到对应字段
             *  1)   首先需要找到获取到上游一级经销商基础数据, 同时还要用一级经销商找所有关联的主子经销商, 逻辑为根据一级的《经销商》匹配 未禁用的《主经销商配置表》表头的【主经销商】, 获取该《主经销商配置表》表头的【主经销商】与 表体的【子经销商】
             *  2)   根据 刚刚《主经销商配置表》表头的【主经销商】与 表体的【子经销商】, 找到所有 相同【经销商】(只要匹配到其中一个即可) 且 未禁用 的《送达方》
             *  3)   再来需要获取城市的信息作为匹配送达方的条件之一, 这里需要判断《采购订单》是否有上游《销售合同》
             *      1)   如果《采购订单》有上游《销售合同》时, 根据上游《销售合同》表头的【门店名称】(销售部门)的 对应《部门》的【关联门店】, 找到对应《门店》基础资料的【城市】, 匹配对应《送达方》的【城市】, 进行过滤
             *      2)   如果《采购订单》没有上游《销售合同》时, 根据《采购订单》表头的【采购部门】的 对应《部门》的【关联门店】, 找到对应《门店》基础资料的【城市】, 匹配对应《送达方》的【城市】, 进行过滤
             *  4)   同时根据该《采购订单》的商品明细的【业绩品牌】, 匹配 《送达方》 品牌信息的【系列】
             *  5)   用3与4的条件匹配上送达方时则获取对应《送达方》来传给中台
             *  6)   如果匹配到多个的时候, 系统随机给一其中个《送达方》
             */

            Dictionary<string, DynamicObject> entryDeliverMap = new Dictionary<string, DynamicObject>();

            var topCtx = userCtx.CreateTopOrgDBContext();

            var deliverId = Convert.ToString(purchaseOrder["fdeliverid"]);
            var deliver = topCtx.LoadBizDataById("bas_deliver", deliverId);
            if (deliver != null)
            {
                foreach (var entry in entrys)
                {
                    var entryId = Convert.ToString(entry["id"]);
                    if (deliver != null)
                    {
                        entryDeliverMap[entryId] = deliver;
                    }
                }

                return entryDeliverMap;
            }

            var agent = topCtx.LoadBizDataById("bas_agent", userCtx.Company);

            // 二级分销商
            var fisreseller = Convert.ToBoolean(agent["fisreseller"]);
            if (!fisreseller)
            {
                return entryDeliverMap;
            }

            var parentAgent = topCtx.LoadBizDataById("bas_agent", Convert.ToString(agent["forgid"]));
            if (parentAgent == null)
            {
                return entryDeliverMap;
            }
            var parentAgentId = Convert.ToString(parentAgent["id"]);

            // 二级分销商
            var agentIds = new List<string>();
            agentIds.Add(parentAgentId);

            var agentService = topCtx.Container.GetService<IAgentService>();
            var subAgentIds = agentService.GetSubAgentIds(topCtx, parentAgentId);
            agentIds.AddRange(subAgentIds);

            // 获取《送达方》
            var delivers = topCtx.LoadBizDataByFilter("bas_deliver", $"fforbidstatus='0' and fagentid in ({agentIds.JoinEx(",", true)})");
            if (delivers == null)
            {
                return entryDeliverMap;
            }

            DynamicObject dept = null;

            // 判断是否有上游销售合同，如果有上游合同，则找销售合同上的销售部门
            var fsourcetype = Convert.ToString(purchaseOrder["fsourcetype"]);
            if (fsourcetype.EqualsIgnoreCase("ydj_order"))
            {
                var fsourcenumber = Convert.ToString(purchaseOrder["fsourcenumber"]);

                var order = userCtx.LoadBizDataByNo("ydj_order", "fbillno", new[] { fsourcenumber })
                    .FirstOrDefault();

                var deptId = Convert.ToString(order?["fdeptid"]);
                dept = userCtx.LoadBizDataById("ydj_dept", deptId);
            }
            // 默认找采购订单的采购部门
            else
            {
                var deptId = Convert.ToString(purchaseOrder["fdeptid"]);
                dept = userCtx.LoadBizDataById("ydj_dept", deptId);
            }

            var storeId = Convert.ToString(dept?["fstore"]);
            // 找到门店
            var store = topCtx.LoadBizDataById("bas_store", storeId);
            if (store == null)
            {
                return entryDeliverMap;
            }

            string cityId = Convert.ToString(store["fmycity"]);
            // 按城市筛选《送达方》
            delivers = delivers.Where(s => Convert.ToString(s["fcity"]).EqualsIgnoreCase(cityId)).ToList();

            var brandInfos = delivers.SelectMany(s => (DynamicObjectCollection)s["fentry"]);

            foreach (var entry in entrys)
            {
                // 根据该《采购订单》的商品明细的【业绩品牌】, 匹配 《送达方》 品牌信息的【系列】
                var fresultbrandid = Convert.ToString(entry["fresultbrandid"]);
                if (fresultbrandid.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                var brandInfo =
                    brandInfos.FirstOrDefault(s => Convert.ToString(s["fserieid"]).EqualsIgnoreCase(fresultbrandid));
                if (brandInfo == null)
                {
                    continue;
                }

                var matchDeliver = brandInfo.Parent as DynamicObject;
                if (matchDeliver == null)
                {
                    continue;
                }

                entryDeliverMap[Convert.ToString(entry["id"])] = matchDeliver;
            }

            return entryDeliverMap;
        }

        /// <summary>
        /// 先返回当前经销商自建的单据类型，否则再返回系统预制的
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="billtypename"></param>
        /// <returns></returns>
        public string GetBillTypeData(UserContext userCtx, HtmlForm htmlForm, string billtypename)
        {
            var svc = userCtx.Container.GetService<IBillTypeService>();
            var billTypeInfos = svc.GetBillTypeInfors(userCtx, htmlForm);

            //如果有自建则返回自建，否则返回系统预置
            var billTypeInfo = billTypeInfos.FirstOrDefault(f => !f.fprimitivename.IsNullOrEmptyOrWhiteSpace() && f.fprimitivename == billtypename);
            if (billTypeInfo == null)
            {
                billTypeInfo = billTypeInfos.FirstOrDefault(f => f.fname == billtypename);
            }

            return billTypeInfo.fid;

            //var metaSrv = userCtx.Container.GetService<IMetaModelService>();
            //var purForm = metaSrv.LoadFormModel(userCtx, "bd_billtype");
            //var dm = userCtx.Container.GetService<IDataManager>();
            //dm.InitDbContext(userCtx, purForm.GetDynamicObjectType(userCtx));
            //var reader = userCtx.GetPkIdDataReader(purForm, "", new List<SqlParam>());
            //var billData = dm.SelectBy(reader).OfType<DynamicObject>();

            ////先查找该单据类型
            //var billtypeobj = billData.Where(x => (Convert.ToString(x["fmainorgid"]) == userCtx.Company || Convert.ToString(x["fmainorgid"]) == "0")
            //                                      && Convert.ToString(x["fname"]) == billtypename && Convert.ToString(x["fbizobject"]) == "ydj_purchaseorder").ToList();

            //var billtypeid = billtypeobj.Where(x => !Convert.ToBoolean(x["fispreset"])).Select(x => Convert.ToString(x["id"])).FirstOrDefault();
            ////如果有自建则返回自建
            //if (!billtypeid.IsNullOrEmptyOrWhiteSpace())
            //    return billtypeid;
            //else
            //{
            //    return billtypeobj.Select(x => Convert.ToString(x["id"])).FirstOrDefault();
            //}
        }

        /// <summary>
        /// 填充非标信息（从数据库重新获取）
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="purchaseOrders"></param>
        public void FillUnstdInfo(UserContext userCtx, IEnumerable<DynamicObject> purchaseOrders)
        {
            if (purchaseOrders == null || !purchaseOrders.Any()) return;

            var purchaseOrderEntryIds = purchaseOrders
                // 过滤非新增且不是总部手工单
                .Where(s => !s.IsNullOrEmptyOrWhiteSpace() && !Convert.ToString(s["fbilltypeid"]).EqualsIgnoreCase("ydj_purchaseorder_zb"))
                .SelectMany(s => s["fentity"] as DynamicObjectCollection)
                .Where(s =>
                {
                    // 过滤【是否非标】=是 且 【零售价】=0 且 【非标审核状态】=待审批 
                    var funstdtype = Convert.ToBoolean(s["funstdtype"]);
                    var funstdtypestatus = Convert.ToString(s["funstdtypestatus"]);
                    var fprice = Convert.ToDecimal(s["fprice"]);

                    return funstdtype && fprice <= 0 && funstdtypestatus.EqualsIgnoreCase("02");
                })
                .Select(s => Convert.ToString(s["id"]));

            if (!purchaseOrderEntryIds.Any()) return;

            var purchaseOrderEntrys = userCtx.ExecuteDynamicObject(
                $"select fentryid, fprice, famount, fdistrate, fdistamount, fdealamount, fdealprice, funstdtypestatus, funstdtypecomment, funstdtype from t_ydj_poorderentry where fentryid in ({purchaseOrderEntryIds.JoinEx(",", true)})", new List<SqlParam>());

            foreach (var purchaseOrder in purchaseOrders)
            {
                var fentry = purchaseOrder["fentity"] as DynamicObjectCollection;
                foreach (var entry in fentry)
                {
                    var entryId = Convert.ToString(entry["id"]);
                    // 数据库里找不到，跳过
                    var dbEntry = purchaseOrderEntrys.FirstOrDefault(s =>
                        Convert.ToString(s["fentryid"]).EqualsIgnoreCase(entryId));
                    if (dbEntry == null)
                    {
                        continue;
                    }

                    // 使用数据库内的数据重新更新
                    entry["funstdtypestatus"] = dbEntry["funstdtypestatus"];
                    entry["fprice"] = dbEntry["fprice"];
                    entry["famount"] = dbEntry["famount"];
                    entry["fdistrate"] = dbEntry["fdistrate"];
                    entry["fdistamount"] = dbEntry["fdistamount"];
                    entry["fdealamount"] = dbEntry["fdealamount"];
                    entry["fdealprice"] = dbEntry["fdealprice"];
                    entry["funstdtypecomment"] = dbEntry["funstdtypecomment"];
                }
            }
        }

        /// <summary>
        /// 更新《采购入库单》
        /// </summary>
        public void UpdatePostockin(UserContext userCtx, DynamicObject purchaseOrder)
        {
            //当存在总部通过接口：”/msapi/purchaseorder/save?format=json”中的
            //【fprice】（采购单价）或【fdistrate】（折扣率）或【fdealprice】（成交单价）或【fdealamount_e】（成交金额）或【fdistamount_e】（折扣额）字段的
            //下发导致金蝶系统更新了《采购订单》的【采购单价】、【成交单价】、【成交金额】、【金额】、【折扣额】、【折扣率】时；
            //在原功能逻辑不变下增加逻辑：通过《采购订单》的【单据编号】+【商品编码】+【辅助属性】+【定制说明】+【行号】去匹配关联的下游《采购入库单》，
            //并更新《采购入库单》的【成交单价】、【采购单价】、【成交金额】、【金额】、【修改日期】、【修改人】，
            //并且操作记录要记录此次修改的记录，操作人为系统管理员。补充说明：《采购入库单》【金额】=【采购单价】*【实收数量】；【成交金额】=【成交单价】*【实收数量】；
            if (!purchaseOrder.IsNullOrEmptyOrWhiteSpace())
            {
                //创建总部（系统管理员）用户上下文
                var topCtx = userCtx.CreateAdminDbContext();

                var fsourcenumber = Convert.ToString(purchaseOrder["fbillno"]).Trim();

                if (!fsourcenumber.IsNullOrEmptyOrWhiteSpace())
                {
                    var sqlParams = new List<SqlParam>
                    {
                        new SqlParam("@sourceinterid", System.Data.DbType.String, Convert.ToString(purchaseOrder["id"]))
                    };
                    var postockinObjs = userCtx.LoadBizDataByFilter("stk_postockin", " exists(select 1 from T_STK_POSTOCKINENTRY with(nolock) where T_STK_POSTOCKINENTRY.fid=T_STK_POSTOCKIN.fid and T_STK_POSTOCKINENTRY.fsourceinterid=@sourceinterid) ", false, sqlParams);

                    var purchaseOrderEntitys = purchaseOrder["fentity"] as DynamicObjectCollection;

                    List<DynamicObject> changePostockinObjs = new List<DynamicObject>();
                    List<LogEntry> logEntrys = new List<LogEntry>();

                    foreach (var item in postockinObjs)
                    {
                        var needChange = false;

                        var postockinEntitys = item["fentity"] as DynamicObjectCollection;

                        List<string> logContent = new List<string>();
                        foreach (var postockinEntity in postockinEntitys)
                        {
                            var existEntry = purchaseOrderEntitys.Where(o => Convert.ToString(o["id"]).Trim().Equals(Convert.ToString(postockinEntity["fsourceentryid"]).Trim())).FirstOrDefault();

                            if (!existEntry.IsNullOrEmptyOrWhiteSpace())
                            {
                                //《采购入库单》【金额】=【采购单价】*【实收数量】；【成交金额】=【成交单价】*【实收数量】

                                //《采购入库单》更新前的相关金额
                                var fprice = Convert.ToDecimal(postockinEntity["fprice"]);
                                var fpoprice = Convert.ToDecimal(postockinEntity["fpoprice"]);
                                var famount = Convert.ToDecimal(postockinEntity["famount"]);
                                var fprfpoamountice = Convert.ToDecimal(postockinEntity["fpoamount"]);

                                //《采购入库单》即将更新后的相关金额
                                var fbizqty = Convert.ToDecimal(postockinEntity["fbizqty"]);
                                var fprice_new = Convert.ToDecimal(existEntry["fdealprice"]);
                                var fpoprice_new = Convert.ToDecimal(existEntry["fprice"]);
                                var famount_new = fprice_new * fbizqty;
                                var fprfpoamountice_new = fpoprice_new * fbizqty;

                                if (fprice != fprice_new || fpoprice != fpoprice_new || famount != famount_new || fprfpoamountice != fprfpoamountice_new)
                                {

                                    postockinEntity["fprice"] = fprice_new;               //成交单价
                                    postockinEntity["fpoprice"] = fpoprice_new;           //采购单价
                                    postockinEntity["famount"] = famount_new;             //成交金额
                                    postockinEntity["fpoamount"] = fprfpoamountice_new;   //金额

                                    needChange = true;

                                    var fseq = Convert.ToString(existEntry["FSeq"]);
                                    logContent.Add($@"第{fseq}行，【成交单价】旧值：{fprice.ToString("0.##")}，新值：{fprice_new.ToString("0.##")}；【采购单价】旧值：{fpoprice.ToString("0.##")}，新值：{fpoprice_new.ToString("0.##")}；
                                                    【成交金额】旧值：{famount.ToString("0.##")}，新值：{famount_new.ToString("0.##")}；【金额】旧值：{fprfpoamountice.ToString("0.##")}，新值：{fprfpoamountice_new.ToString("0.##")}；");
                                }

                            }
                        }

                        if (needChange)
                        {

                            item["fupdatepricedate"] = DateTime.Now;              //更新价格日期
                            item["fisupdateprice"] = "1";                         //是否更新价格

                            changePostockinObjs.Add(item);

                            logEntrys.Add(new LogEntry
                            {
                                BillIds = item["id"] as string,
                                BillNos = item["fbillno"] as string,
                                BillFormId = "stk_postockin",
                                OpName = "更新价格",
                                OpCode = "UpdatePrice",
                                Content = "执行了【更新价格】操作！{0}".Fmt(logContent.JoinEx("", true)),
                                DebugData = "执行了【更新价格】操作！{0}".Fmt(logContent.JoinEx("", true)),
                                Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                                Level = Enu_LogLevel.Info.ToString(),
                                LogType = Enu_LogType.RecordType_03
                            });
                        }
                    }

                    if (changePostockinObjs.Count > 0)
                    {
                        //var res = this.Gateway.InvokeBillOperation(topCtx, "stk_postockin", changePostockinObjs, "save", new Dictionary<string, object>() { });
                        var loger = userCtx.Container.GetService<ILogService>();
                        var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
                        //业务校验太多，改用暂存方式，日志单独手动记录
                        var res = gateway.InvokeBillOperation(topCtx, "stk_postockin", changePostockinObjs, "draft", new Dictionary<string, object>() { { "IgnoreCheckPermssion", true } });
                        res.ThrowIfHasError();
                        foreach (var logItm in logEntrys)
                        {
                            loger.WriteLog(topCtx, logItm);
                        }
                    }
                }

            }

        }



        /// <summary>
        /// 在经销商做了《采购订单变更单》如果修改了《采购订单》的【采购单价】、【成交单价】、【成交金额】、【金额】、【折扣额】、【折扣率】
        /// 审核《采购订单变更单》后；在原功能逻辑不变下，增加逻辑：通过《采购订单》的【单据编号】+【商品编码】+【辅助属性】+【定制说明】+【行号】
        /// 去匹配关联的下游《采购入库单》，并更新《采购入库单》的【成交单价】、【采购单价】、【成交金额】、【金额】、【修改日期】、【修改人】；
        /// 并且操作记录要记录此次修改的记录，操作人为《采购订单变更单》的创建人。
        /// </summary>
        /// <param name="dataEntitys"></param>
        public void UpdatePostockin(UserContext userCtx, DynamicObject[] dataEntitys, Dictionary<string, string> unAuditPurChgIds)
        {
            foreach (var dataEntity in dataEntitys)
            {
                var fbillno = Convert.ToString(dataEntity["fbillno"]).Trim();
                var purFentitys = dataEntity["fentity"] as DynamicObjectCollection;
                if (unAuditPurChgIds.ContainsKey(fbillno))
                {
                    var purchaseorderchgObj =
                        userCtx.LoadBizDataById("ydj_purchaseorder_chg", unAuditPurChgIds[fbillno]);
                    if (!purchaseorderchgObj.IsNullOrEmptyOrWhiteSpace())
                    {
                        var userId = Convert.ToString(purchaseorderchgObj["fcreatorid"]).Trim();

                        var sqlParams = new List<SqlParam>
                        {
                            new SqlParam("@sourceinterid", System.Data.DbType.String, Convert.ToString(dataEntity["id"]))
                        };
                        var postockinObjs = userCtx.LoadBizDataByFilter("stk_postockin", " exists(select 1 from T_STK_POSTOCKINENTRY with(nolock) where T_STK_POSTOCKINENTRY.fid=T_STK_POSTOCKIN.fid and T_STK_POSTOCKINENTRY.fsourceinterid=@sourceinterid) ", false, sqlParams);


                        List<DynamicObject> changePostockinObjs = new List<DynamicObject>();

                        foreach (var item in postockinObjs)
                        {
                            var needChange = false;

                            var postockinEntitys = item["fentity"] as DynamicObjectCollection;

                            foreach (var postockinEntity in postockinEntitys)
                            {
                                var existEntry = purFentitys.Where(o =>
                                        Convert.ToString(o["id"]).Trim()
                                            .Equals(Convert.ToString(postockinEntity["fsourceentryid"]).Trim()))
                                    .FirstOrDefault();

                                if (!existEntry.IsNullOrEmptyOrWhiteSpace())
                                {
                                    //《采购入库单》【金额】=【采购单价】*【实收数量】；【成交金额】=【成交单价】*【实收数量】

                                    //《采购入库单》更新前的相关金额
                                    var fprice = Convert.ToDecimal(postockinEntity["fprice"]);
                                    var fpoprice = Convert.ToDecimal(postockinEntity["fpoprice"]);
                                    var famount = Convert.ToDecimal(postockinEntity["famount"]);
                                    var fprfpoamountice = Convert.ToDecimal(postockinEntity["fpoamount"]);

                                    //《采购入库单》即将更新后的相关金额
                                    var fbizqty = Convert.ToDecimal(postockinEntity["fbizqty"]);
                                    var fprice_new = Convert.ToDecimal(existEntry["fdealprice"]);
                                    var fpoprice_new = Convert.ToDecimal(existEntry["fprice"]);
                                    var famount_new = fprice_new * fbizqty;
                                    var fprfpoamountice_new = fpoprice_new * fbizqty;

                                    if (fprice != fprice_new || fpoprice != fpoprice_new || famount != famount_new ||
                                        fprfpoamountice != fprfpoamountice_new)
                                    {

                                        postockinEntity["fprice"] = fprice_new; //成交单价
                                        postockinEntity["fpoprice"] = fpoprice_new; //采购单价
                                        postockinEntity["famount"] = famount_new; //成交金额
                                        postockinEntity["fpoamount"] = fprfpoamountice_new; //金额

                                        needChange = true;
                                    }

                                }
                            }

                            if (needChange)
                            {

                                item["fupdatepricedate"] = DateTime.Now; //更新价格日期
                                item["fisupdateprice"] = "1"; //是否更新价格

                                changePostockinObjs.Add(item);
                            }

                        }

                        if (changePostockinObjs.Count > 0)
                        {
                            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
                            var ctx = userCtx.CreaeteaAgentDBContextWithUser(userCtx.Company, userId);
                            var res = gateway.InvokeBillOperation(ctx, "stk_postockin", changePostockinObjs,
                                "draft", new Dictionary<string, object>() { { "IgnoreCheckPermssion", true } });
                            res.ThrowIfHasError();
                        }


                    }
                }
            }
        }

    }
}