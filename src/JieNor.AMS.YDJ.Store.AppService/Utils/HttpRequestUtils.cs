using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.Store.AppService.Utils
{
    /// <summary>
    /// Http请求帮助类
    /// </summary>
    public class HttpRequestUtils
    {
    /// <summary>
    /// 向指定的服务器地址发送Web请求，并建请求结果序列化为指定类型的实体对象
    /// </summary>
    /// <typeparam name="TResponse">实体对象类型</typeparam>
    /// <param name="url">请求地址</param>
    /// <param name="method">请求方式：GET/POST 默认为GET</param>
    /// <param name="data">请求参数：只有 POST 方式才需要，GET 方式直接在 url 后面拼接参数即可</param>
    /// <param name="header">请求头</param>
    /// <returns>实体对象</returns>
    public static TResponse SendWebRequest<TResponse>(
            string url,
            string method = "GET",
            object data = null,
            Dictionary<string, string> header = null) where TResponse : class
        {
            var request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = method;
            request.ContentType = "text/html;charset=UTF-8";

            //设置请求头
            if (header != null)
            {
                foreach (var key in header.Keys)
                {
                    request.Headers.Add(key, header[key]);
                }
            }

            //设置POST请求参数
            if (data != null)
            {
                JsonSerializerSettings settings = new JsonSerializerSettings
                {
                    ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver()
                    //ContractResolver = new Newtonsoft.Json.Serialization.CamelCaseExceptDictionaryKeysResolver()
                };
                var reqJson = JsonConvert.SerializeObject(data,settings);
                byte[] buffer = Encoding.UTF8.GetBytes(reqJson);
                if (method.Equals("POST", StringComparison.OrdinalIgnoreCase))
                {
                    request.ContentType = "application/json;charset=utf-8";
                    request.ContentLength = buffer.Length;
                }
                var reqStream = request.GetRequestStream();
                reqStream.Write(buffer, 0, buffer.Length);
                reqStream.Close();
            }

            var response = (HttpWebResponse)request.GetResponse();
            var resStream = response.GetResponseStream();
            var resReader = new StreamReader(resStream, Encoding.UTF8);
            var resJson = resReader.ReadToEnd();
            resReader.Close();
            resStream.Close();

            if (typeof(TResponse).Name.Contains("String")) return resJson as TResponse;

            var obj = JsonConvert.DeserializeObject<TResponse>(resJson);
            return obj;
        }
    }
}
