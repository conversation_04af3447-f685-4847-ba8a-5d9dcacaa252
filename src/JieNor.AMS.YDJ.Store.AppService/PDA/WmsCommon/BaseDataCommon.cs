using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.AMS.YDJ.Store.AppService.PDA.WmsCommon
{
    /// <summary>
    /// PDA基础资料通用接口
    /// </summary>
    [InjectService]
    [FormId("ydj_product")]
    [OperationNo("getwmsbasedata")]
    public class BaseDataCommon : AbstractOperationServicePlugIn
    {
        protected UserContext AgentContext { get; set; }
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var formid = this.GetQueryOrSimpleParam<string>("formid", "");
            var filter = this.GetQueryOrSimpleParam<string>("filter", "");
            var agentId = this.GetQueryOrSimpleParam<string>("agentid");//经销商ID
            this.AgentContext = this.Context.CreateAgentDBContext(agentId);
            if (formid.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.SimpleMessage="参数不正确，未指定要获取的基础资料类型！";
                this.Result.IsSuccess = false;
                return;
            }
            
            var meta = HtmlParser.LoadFormMetaFromCache(formid, this.AgentContext);

            var filterstr = $" fforbidstatus='0' and fmainorgid='{this.AgentContext.Company}' ";
            if (meta.FieldList.Any(f => f.Key.EqualsIgnoreCase("fforbidstatus")))
            {
                filterstr += $" and fforbidstatus='0' ";              
            }

            if (!string.IsNullOrWhiteSpace(filter))
            {
                switch (formid)
                {
                    case "ydj_customer":
                        filterstr += $" and (fnumber like '%{filter}%' or fname like '%{filter}%' or fphone like '%{filter}%' ) ";
                    break;
                    default:
                        filterstr += $" and (fnumber like '%{filter}%' or fname like '%{filter}%') ";
                        break;
                }
            }

            var datas = this.AgentContext.LoadBizDataByFilter(formid, filterstr);

            if (datas == null || !datas.Any())
            {
                this.Result.SimpleMessage = "没有相关数据！";
                this.Result.IsSuccess = false;
                return;
            }

            var uiConverter = this.AgentContext.Container.GetService<IUiDataConverter>();
            var htmlForm = this.MetaModelService.LoadFormModel(this.AgentContext, formid);
            JArray jar = new JArray();
            foreach(var bill in datas)
            {
                var uidata = uiConverter.CreateUIDataObject(this.AgentContext, htmlForm, bill);
                jar.Add(uidata);
            }                                  

            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "执行成功!";
            this.Result.SrvData = jar;
        }
    }
}
