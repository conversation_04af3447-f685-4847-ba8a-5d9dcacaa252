using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.PDA.WmsCommon
{
    public class AuditHelper
    {
        /// <summary>
        /// 自动流转库存调拨单的审批流
        /// </summary>
        /// <param name="userContext">用户操作上下文</param>
        /// <param name="formId">业务表单ID</param>
        /// <param name="objDatas">业务数据</param>
        /// <param name="errMsgs">异常信息</param>
        /// <returns></returns>
        public static bool AutoAuditFlowInventoryTransfer(UserContext userContext, string formId, List<DynamicObject> objDatas, ref List<string> errMsgs)
        {
            var flowObjs = new List<DynamicObject>();

            foreach (var data in objDatas)
            {
                var fflowinstanceid = Convert.ToString(data["fflowinstanceid"]);

                if (fflowinstanceid.IsNullOrEmptyOrWhiteSpace()) continue;

                flowObjs.Add(data);
            }

            if (flowObjs.Any())
            {
                try
                {
                    var opResult = userContext.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(userContext, "stk_inventorytransfer", flowObjs, "auditflow", null);
                    opResult.ThrowIfHasError(true, "库存调拨单审批流自动流转失败！");
                }
                catch (Exception ex)
                {
                    errMsgs.Add(ex.Message);
                }
            }

            return !errMsgs.Any();
        }
    }
}
