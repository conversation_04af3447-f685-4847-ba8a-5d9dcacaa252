using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Store.AppService.Model.DeliveryScanTask;
using JieNor.AMS.YDJ.Store.AppService.PDA.WmsCommon;
using JieNor.AMS.YDJ.Store.AppService.PDA.DeliveryScanTask;
using JieNor.AMS.YDJ.Store.AppService.Model;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp;

namespace JieNor.AMS.YDJ.Store.AppService.PDA.Receptionscantask
{
    /// <summary>
    /// 收货扫描任务：批量扫码提交
    /// </summary>
    [InjectService]
    [FormId("bcm_receptionscantask")]
    [OperationNo("batchscansubmit")]
    public class BatchScanSubmit : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 初始化服务插件上下文时触发的事件
        /// </summary>
        /// <param name="operCtx"></param>
        /// <param name="serviceList"></param>
        public new void InitializeOperationContext(OperationContext operCtx, params object[] serviceList)
        {
            // 启用幂等性检查
            var serCtrlOpt = serviceList.FirstOrDefault(o => o is ServiceControlOption) as ServiceControlOption;
            serCtrlOpt.SupportIdemotency = true;

            base.InitializeOperationContext(operCtx, serviceList);
        }

        protected UserContext AgentContext { get; set; }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            var data = this.GetQueryOrSimpleParam<string>("scandata");//扫描数据信息
            var agentId = this.GetQueryOrSimpleParam<string>("agentid");//经销商ID
            //批量收货
            string logName = "ReceptionScanTaskBatchScanSubmit/" + DateTime.Now.ToString("yyyyMMddHHmmss");
            LogHelper.WriteLog(logName, data);
            this.Result.IsSuccess = false;
            this.AgentContext = this.Context.CreaeteaAgentDBContextWithUser(agentId);
            var svc = this.AgentContext.Container.GetService<IBaseFormProvider>();
            var getway = this.AgentContext.Container.GetService<IHttpServiceInvoker>();
            ScanData submitData = data.FromJson<ScanData>();
            List<string>
                bclist = new List<string>(),//所有条码
                savescans = new List<string>();//已保存的条码扫描记录
            List<DynamicObject>
                allWareHouses = new List<DynamicObject>(),//仓库
                allbarcodemasters = new List<DynamicObject>(),//所有条码数据包
                tasks = new List<DynamicObject>(),//所有收货扫描任务数据包
                allscanresults = new List<DynamicObject>(),//所有收货任务扫描记录
                allMaterials = new List<DynamicObject>(),//所有的物料信息
                saveScanRecords = new List<DynamicObject>(),//需要保存的扫描记录
                savebcMaster = new List<DynamicObject>(),//需要保存的条码主档
                allsources = new List<DynamicObject>(),//源采购订单
                purchaseInOrders = new List<DynamicObject>();//生成的采购入库单
            string
                newstaffid = svc.GetMyStaff(this.AgentContext)?.Id,//员工ID
                newdepartid = svc.GetMyDepartment(this.AgentContext)?.Id;//部门ID
            var htmlForm = this.MetaModelService.LoadFormModel(this.AgentContext, "bcm_scanresult");//条码扫描记录 
            #region 校验及加载数据
            if (CheckBody(submitData, allWareHouses))
                return;
            BarCodeMasterHelper.GetSubBarCode(this.AgentContext,
                submitData.materialInfos.SelectMany(s => s.barcodeinfos).Select(s => s.barcode).Distinct().ToList(),
                ref bclist);
            if (CheckIntegrality(bclist))
                return;
            allbarcodemasters = this.AgentContext.LoadBizDataByFilter("bcm_barcodemaster", $" fnumber in ('{string.Join("','", bclist)}')");
            //判断提交的箱条码中，是否有提交数量不等于子条码数量的
            var bcErrMsg = new List<string>();
            foreach (var material in submitData.materialInfos)
            {
                foreach (var bc in material.barcodeinfos)
                {
                    var bcMaster = allbarcodemasters.Where(t => Convert.ToString(t["fnumber"]).EqualsIgnoreCase(bc.barcode)).FirstOrDefault();
                    if (bcMaster != null && Convert.ToString(bcMaster["fpackagtype"]) == "3")
                    {
                        //一包多件类型的条码，判断是否为箱条码，且提交数量是否为子条码总数
                        var bcEnts = bcMaster["fentity"] as DynamicObjectCollection;
                        if (bcEnts != null && bcEnts.Any() && bcEnts.Any(t => !t["fbarcode"].IsNullOrEmptyOrWhiteSpace()))
                        {
                            var sumCount = bcEnts.Where(f => !f["fbarcode"].IsNullOrEmptyOrWhiteSpace()).Select(t => Convert.ToInt32(t["fqty"])).Sum();
                            if (sumCount != bc.qty)
                            {
                                bcErrMsg.Add($"条码【{bc.barcode}】提交的数量【{bc.qty}】异常，请检查！");
                            }
                        }
                    }
                }
            }
            if (bcErrMsg.Any())
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = String.Join("", bcErrMsg);
                return;
            }

            var sss = submitData.materialInfos.SelectMany(s => s.billno).Distinct().ToList();
            tasks = this.AgentContext.LoadBizDataByFilter("bcm_receptionscantask", $" fbillno in ('{string.Join("','", submitData.materialInfos.Select(s => s.billno).Distinct().ToList())}')");
            if (!tasks.Any())
            {
                this.Result.SimpleMessage = $"提交失败。未找到对应的收货扫描任务数据。";
                return;
            }
            var entrys = tasks.SelectMany(s => s["ftaskentity"] as DynamicObjectCollection).ToList();
            if (CheckDatas(submitData.materialInfos, entrys))
                return;
            allscanresults = this.AgentContext.LoadBizDataByFilter("bcm_scanresult",
                $" fscantaskformid='bcm_receptionscantask' " +
                $"and fscantaskbillno in ('{string.Join("','", submitData.materialInfos.Select(s => s.billno))}') " +
                $"and fbarcode in ('{string.Join("','", allbarcodemasters.Select(f => f["Id"]?.ToString()).Distinct().ToList())}')");
            allMaterials = this.AgentContext.LoadBizDataById("ydj_product", submitData.materialInfos.Select(f => f.materialid).ToList(), true);
            allsources = this.AgentContext.LoadBizDataByFilter("ydj_purchaseorder", $"fbillno in ('{string.Join("','", tasks.SelectMany(s => s["ftaskentity"] as DynamicObjectCollection).Select(s => Convert.ToString(s["fsourcebillno"])).Distinct())}')", true);
            //需判断所有采购订单是否都已审核
            var noAuditSources = allsources.Where(t => Convert.ToString(t["fstatus"]) != "E").ToList();
            if (noAuditSources != null && noAuditSources.Any())
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = $"上游采购订单【{string.Join("、", noAuditSources.Select(t => Convert.ToString(t["fbillno"])).ToList())}】未审核，不允许提交本次收货！";
                return;
            }
            var allEntryIds = submitData.materialInfos.Where(m => !m.enid.IsNullOrEmptyOrWhiteSpace()).Select(t => t.enid).Distinct().ToList();
            if (!SubmitHelper.CheckEntrysSubmitStatus(this.AgentContext, allEntryIds))
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = $"本次提交商品对应的收货任务，正在被其它PDA提交占用中，请稍等片刻再提交，谢谢！";
                return;
            }
            SubmitHelper.MarkEntrysSubmitStatus(this.AgentContext, allEntryIds, true);
            #endregion
            foreach (var task in tasks)
            {
                DynamicObject purchaseInOrder = null;
                DynamicObjectCollection purchaseInOrderEntitys = null;
                task["ftaskstatus"] = "ftaskstatus_03";//作业中      
                var matchInfos = submitData.materialInfos.Where(w => w.billno.Equals(Convert.ToString(task["fbillno"])));
                if (matchInfos == null || !matchInfos.Any())
                    continue;
                var entitys = task["ftaskentity"] as DynamicObjectCollection;
                var subEntitys = task["fsubmitentity"] as DynamicObjectCollection;
                var ftask_type = Convert.ToString(task["ftask_type"]);
                if (ftask_type.Equals("stk_postockin"))
                {
                    purchaseInOrder = BuildPostockinHead(task, newstaffid, newdepartid);
                    purchaseInOrderEntitys = purchaseInOrder["fentity"] as DynamicObjectCollection;
                }
                //下面开始直接抄原有的收货提交
                foreach (var info in matchInfos)
                {
                    var entity = entitys.FirstOrDefault(f => Convert.ToString(f["id"]).EqualsIgnoreCase(info.enid));
                    if (entity == null || info.scanqty <= 0)
                        continue;

                    //获取当前明细行已提交的条码扫描记录数据
                    var submitscanresults = allscanresults.Where(f => Convert.ToString(f["fscantaskformid"]) == this.HtmlForm.Id && Convert.ToString(f["fscantaskbillno"]) == info.billno && Convert.ToString(f["fscantaskentryid"]) == Convert.ToString(entity["Id"])).ToList();
                    var noSubmitBC = info.barcodeinfos.Where(x => !submitscanresults.Exists(y => Convert.ToString(y["fbarcodetext"]).Trim() == x.barcode.Trim()
                                                                                            && Convert.ToDateTime(y["fscandatetime"]) == Convert.ToDateTime(x.scantime))).ToList();
                    var submitedBC = info.barcodeinfos.Where(t => !noSubmitBC.Contains(t)).ToList();//已经提交过的

                    foreach (var bc in submitedBC)
                    {
                        //将已经提交过的条码对应的数量扣减掉
                        info.scanqty -= bc.qty;
                        //写入条码扫描记录
                        var currScanResult = submitscanresults.FirstOrDefault(t => Convert.ToString(t["fbarcodetext"]).Contains(bc.barcode));
                        if (currScanResult == null) continue;//防错
                        var scanRecord = currScanResult.Clone() as DynamicObject;
                        scanRecord["fbillno"] = null;//清空单据编号
                        var remarks = "采购订单";
                        switch (entity["fsourceformid"])
                        {
                            case "ydj_purchaseorder":
                                remarks = "采购订单";
                                break;
                            case "stk_sostockreturn":
                                remarks = "销售退货";
                                break;
                            case "stk_otherstockin":
                                remarks = "其它入库";
                                break;
                        }
                        remarks += "：提交成功。（特别备注：条码因网络卡顿原因被重复提交，程序已做去重处理。）";
                        scanRecord["fdescription"] = remarks;//备注
                        scanRecord["fopdatetime"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");//DateTime.Now;//操作日期   
                        saveScanRecords.Add(scanRecord);
                    }
                    if (info.scanqty <= 0)
                    {
                        //如果商品的条码都已经提交过了，则跳过这个商品
                        continue;
                    }

                    info.barcodeinfos = noSubmitBC;
                    //根据单位和数量获取基本单位对应的数量
                    var baseqty = UnitConvertHelper.UnitConvertToBase(this.AgentContext, allMaterials, info.materialcode, Convert.ToString(entity["fbizunitid"]), info.scanqty, Convert.ToString(entity["funitid"]));
                    entity["fwaitworkqty"] = Convert.ToDecimal(entity["fwaitworkqty"]) - info.scanqty;//待作业数量 = 待作业数量 - 本次扫描数量
                    entity["fworkedqty"] = Convert.ToDecimal(entity["fworkedqty"]) + info.scanqty; //已作业数量 = 已作业数量 + 本次扫描数量
                    var bcCount = info.barcodeinfos.Count();
                    entity["fwaitscanqty"] = Convert.ToDecimal(entity["fwaitscanqty"]) - bcCount;//待扫包数 = 待扫包数 - 本次提交的条码数量
                    entity["fscannedqty"] = Convert.ToDecimal(entity["fscannedqty"]) + bcCount; //已扫包数 = 已扫包数 + 本次提交的条码数量
                    List<StoreValueModel> tuples = new List<StoreValueModel>();
                    var bcgroups = info.barcodeinfos.GroupBy(f => new { storeid = f.storeid, storelocationid = f.storelocationid }).ToList();
                    foreach (var bcgroup in bcgroups)
                    {
                        var fstorehouseid = bcgroup.Key.storeid;
                        var fstorelocationid = bcgroup.Key.storelocationid;
                        var warehouse = allWareHouses.FirstOrDefault(f => f["Id"]?.ToString() == fstorehouseid);
                        var fstockstatus = warehouse == null ? "311858936800219137" : warehouse["fstockid"]?.ToString();
                        var nowbizqty = 0;
                        var nowreceptionpackageqty = 0;
                        foreach (var item in bcgroup)
                        {
                            nowreceptionpackageqty += 1;
                            nowbizqty += item.qty;
                        }
                        var tup111 = tuples.FirstOrDefault(f => f.storeid == fstorehouseid && f.storelocationid == fstorelocationid);
                        if (tup111 != null)
                        {
                            tup111.qty += nowbizqty;
                            tup111.packqty += nowreceptionpackageqty;
                        }
                        else
                        {
                            var tup222 = new StoreValueModel();
                            tup222.storeid = fstorehouseid;
                            tup222.storelocationid = fstorelocationid;
                            tup222.storestatus = fstockstatus;
                            tup222.qty = nowbizqty;
                            tup222.packqty = nowreceptionpackageqty;
                            tuples.Add(tup222);
                        }
                    }
                    //为了兼容手工添加数量和扫描条码混合的情况，此处根据商品数量和条码的总数量比对一下，多出来的就是手工添加的数量了
                    //注意手工添加的数量取提交时表头的仓库仓位
                    var countqty = tuples.Sum(f => f.qty);
                    if (countqty < info.scanqty)
                    {
                        //var fstorehouseid = submitData.storeid;
                        //var fstorelocationid = submitData.storelocationid;
                        //BUG#29620：调整仓库、仓位获取位置
                        var fstorehouseid = info.storeid;
                        var fstorelocationid = info.storelocationid;
                        var warehouse = allWareHouses.FirstOrDefault(f => f["Id"]?.ToString() == fstorehouseid);
                        var fstockstatus = warehouse == null ? "311858936800219137" : warehouse["fstockid"]?.ToString();
                        var nowbizqty = info.scanqty - countqty;
                        var nowreceptionpackageqty = 0;
                        var tup111 = tuples.FirstOrDefault(f => f.storeid == fstorehouseid && f.storelocationid == fstorelocationid);
                        if (tup111 != null)
                        {
                            tup111.qty += nowbizqty;
                            tup111.packqty += nowreceptionpackageqty;
                        }
                        else
                        {
                            var tup222 = new StoreValueModel();
                            tup222.storeid = fstorehouseid;
                            tup222.storelocationid = fstorelocationid;
                            tup222.storestatus = fstockstatus;
                            tup222.qty = nowbizqty;
                            tup222.packqty = nowreceptionpackageqty;
                            tuples.Add(tup222);
                        }
                    }

                    var storeid = submitData.storeid;
                    var storelocationid = submitData.storelocationid;
                    var stockstatus = "311858936800219137";
                    decimal bizqty = info.scanqty;
                    decimal packqty = 0;
                    if (tuples != null && tuples.Count > 0)
                    {
                        storeid = tuples[0].storeid;
                        storelocationid = tuples[0].storelocationid;
                        stockstatus = tuples[0].storestatus;
                        bizqty = tuples[0].qty;
                        packqty = tuples[0].packqty;
                    }
                    var bizbaseqty = UnitConvertHelper.UnitConvertToBase(this.AgentContext, allMaterials, info.materialcode, Convert.ToString(entity["fbizunitid"]), bizqty, Convert.ToString(entity["funitid"]));

                    var subEntity = subEntitys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                    subEntity["fmaterialid_s"] = info.materialid;//商品
                    subEntity["fattrinfo"] = entity["fattrinfo"];//辅助属性
                    subEntity["fcustomdesc"] = entity["fcustomdesc"];//定制说明
                    subEntity["fworkqty"] = bizqty;//作业数量
                    subEntity["fbizunitid"] = entity["fbizunitid"];//单位
                    subEntity["fpackagekqty"] = packqty;//作业包数                
                    subEntity["fstorehouseid"] = storeid;//仓库
                    subEntity["fstorelocationid"] = storelocationid;//仓位
                    subEntity["flotno"] = entity["flotno"];//批号
                    subEntity["fmtono"] = entity["fmtono"];//物流跟踪号
                    subEntity["fownertype"] = entity["fownertype"];//货主类型
                    subEntity["fownerid"] = entity["fownerid"];//货主
                    subEntity["fsuitegroupid"] = entity["fsuitegroupid"];//套件组合号
                    subEntity["fcategorygroupid"] = entity["fcategorygroupid"];//配件组合号
                    subEntity["fsafagroupid"] = entity["fsafagroupid"];//套件组合号
                    subEntity["fsafagroupid"] = entity["fsafagroupid"];//沙发组合号               
                    subEntity["fqty"] = bizbaseqty;//基本单位数量
                    subEntity["funitid"] = entity["funitid"];//基本单位
                    subEntity["flinkformid"] = entity["flinkformid"];//关联单据
                    subEntity["flinkbillno"] = entity["flinkbillno"];//关联单据编号
                    subEntity["flinkrownumber"] = entity["flinkrownumber"];//关联单行号
                    subEntity["flinkrowinterid"] = entity["flinkrowinterid"];//关联单行内码
                    subEntity["fscanrowinterid"] = entity["Id"];//扫描任务明细行内码
                    subEntity["fsourceformid"] = entity["fsourceformid"];//来源单据
                    subEntity["fsourcebillno"] = entity["fsourcebillno"];//来源单据编号
                    subEntity["fsourceinterid"] = entity["fsourceinterid"];//来源单内码
                    subEntity["fsourceentryid"] = entity["fsourceentryid"];//来源单分录内码
                    subEntity["fsubmitstaffid"] = newstaffid;//提交人
                    subEntity["fsubmitdate"] = DateTime.Now;//提交时间
                    subEntitys.Add(subEntity);

                    if (tuples != null && tuples.Count > 1)
                    {
                        for (int i = 0; i < tuples.Count; i++)
                        {
                            if (i == 0) continue;
                            var curbizbaseqty = UnitConvertHelper.UnitConvertToBase(this.AgentContext, allMaterials, info.materialcode, Convert.ToString(entity["fbizunitid"]), tuples[i].qty, Convert.ToString(entity["funitid"]));
                            var newsubEntity = subEntity.Clone() as DynamicObject;
                            newsubEntity["fworkqty"] = tuples[i].qty;//作业数量
                            newsubEntity["fpackagekqty"] = tuples[i].packqty;//作业包数
                            newsubEntity["fstorehouseid"] = tuples[i].storeid;//仓库
                            newsubEntity["fstorelocationid"] = tuples[i].storelocationid;//仓位
                            newsubEntity["fqty"] = curbizbaseqty;//基本单位数量
                            subEntitys.Add(newsubEntity);
                        }
                    }

                    foreach (var barcode in info.barcodeinfos)
                    {
                        var barcodeinfo = info.barcodeinfos.FirstOrDefault(t => t.barcode == barcode.barcode);
                        var lists = new List<string>();
                        BarCodeMasterHelper.GetSubBarCode(this.AgentContext, new List<string> { barcodeinfo.barcode }, ref lists);
                        foreach (var bc in lists)
                        {
                            if (savescans.Contains(bc)) continue;
                            var barcodeMaster = allbarcodemasters.FirstOrDefault(t => t["fnumber"]?.ToString() == bc);
                            if (barcodeMaster != null)
                            {
                                var scanRecord = htmlForm.GetDynamicObjectType(this.AgentContext).CreateInstance() as DynamicObject;

                                //扫描记录--商品明细
                                var objDetail = scanRecord["fentity"] as DynamicObjectCollection;

                                scanRecord["fbarcode"] = barcodeMaster["Id"];//条码
                                scanRecord["fbarcodetext"] = bc;//条码码文

                                var fsourceformid = Convert.ToString(entity["flinkformid"]);
                                var fsourcebillno = Convert.ToString(entity["flinkbillno"]);
                                if (string.IsNullOrWhiteSpace(fsourceformid))
                                {
                                    fsourceformid = Convert.ToString(entity["fsourceformid"]);
                                    fsourcebillno = Convert.ToString(entity["fsourcebillno"]);
                                }
                                scanRecord["fsourceformid"] = fsourceformid;//来源单据:收货扫描任务对应的关联单据,为空则取来源单据
                                scanRecord["fsourcebillno"] = fsourcebillno;//来源单据编号：收货扫描任务对应的关联单据编号，为空则取来源单据编号
                                scanRecord["fscansceneid"] = "1";//'1':'收货','2':'发货','3':'调拨','4':'盘点','5':'装箱','6':'拆箱'                          
                                scanRecord["foperatorid"] = this.AgentContext.UserId;//操作员
                                scanRecord["fopdatetime"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");//DateTime.Now;//操作日期                    
                                scanRecord["fpda"] = submitData.pda;//PDA标识
                                scanRecord["fdeviceid"] = submitData.pda;//PDA标识
                                scanRecord["fscanqty"] = 1;//扫描数量
                                scanRecord["fstorehouseid"] = barcodeinfo == null ? submitData.storeid : barcodeinfo.storeid;//仓库
                                scanRecord["fstorelocationid"] = barcodeinfo == null ? submitData.storelocationid : barcodeinfo.storelocationid;//仓位
                                var remarks = "采购订单";
                                switch (entity["fsourceformid"])
                                {
                                    case "ydj_purchaseorder":
                                        remarks = "采购订单";
                                        break;
                                    case "stk_sostockreturn":
                                        remarks = "销售退货";
                                        break;
                                    case "stk_otherstockin":
                                        remarks = "其它入库";
                                        break;
                                }
                                scanRecord["fdescription"] = remarks;//备注
                                scanRecord["fscantaskformid"] = "bcm_receptionscantask";//扫描任务
                                scanRecord["fscantaskbillno"] = task["fbillno"];//扫描任务编号
                                scanRecord["fscantaskentryid"] = entity["Id"];//扫描任务行内码
                                scanRecord["fisparentbarcode"] = barcode.barcode == bc ? "1" : "0";//是否父级条码
                                scanRecord["fscandatetime"] = Convert.ToDateTime(barcode.scantime);//扫描时间

                                var barcodeMasterEntitys = barcodeMaster["fentity"] as DynamicObjectCollection;

                                foreach (var entitysItem in barcodeMasterEntitys)
                                {
                                    var detailItem = objDetail.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                                    detailItem["fmaterialid"] = entitysItem["fmaterialid"];
                                    detailItem["fattrinfo"] = entitysItem["fattrinfo"];
                                    detailItem["fcustomdesc"] = entitysItem["fcustomdesc"];
                                    objDetail.Add(detailItem);
                                }

                                saveScanRecords.Add(scanRecord);

                                barcodeMaster["fstorehouseid"] = barcodeinfo == null ? submitData.storeid : barcodeinfo.storeid;//仓库
                                barcodeMaster["fstorelocationid"] = barcodeinfo == null ? submitData.storelocationid : barcodeinfo.storelocationid;//仓位
                                barcodeMaster["fbizstatus"] = "1";//业务状态
                                if (Convert.ToString(task["ftask_type"]).EqualsIgnoreCase("stk_postockin"))
                                {
                                    barcodeMaster["fisscannedcode"] = "1";
                                }
                                savebcMaster.Add(barcodeMaster);
                                savescans.Add(bc);
                            }
                        }
                    }

                    var purchaseInOrderEntity = purchaseInOrderEntitys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;

                    #region 产生采购入库单
                    if (ftask_type == "stk_postockin")
                    {
                        var purchaseOrder = allsources.FirstOrDefault(f => f["fbillno"]?.ToString() == entity["fsourcebillno"]?.ToString());
                        if (purchaseOrder != null)
                        {
                            purchaseInOrder["fsupplierid"] = purchaseOrder["fsupplierid"]; //供应商
                            purchaseInOrder["fpostaffid"] = purchaseOrder["fpostaffid"];//采购员
                            purchaseInOrder["fpodeptid"] = purchaseOrder["fpodeptid"];//采购部门
                            purchaseInOrder["fsupplieraddr"] = purchaseOrder["fsupplieraddr"];//供方地址
                            purchaseInOrder["fsourcenumber"] = purchaseOrder["fbillno"];//源单编码
                            purchaseInOrder["fsourceinterid"] = purchaseOrder["Id"];//源单id
                            purchaseInOrder["fdescription"] = purchaseOrder["fdescription"];//备注
                            purchaseInOrder["frenewalflag"] = purchaseOrder["frenewalflag"]; //焕新订单标记

                            var purchaseOrderEntitys = purchaseOrder["fentity"] as DynamicObjectCollection;
                            var purchaseOrderEntity = purchaseOrderEntitys.FirstOrDefault(f => f["Id"]?.ToString() == entity["fsourceentryid"]?.ToString());
                            if (purchaseOrderEntity == null) continue;

                            var fpoprice = Convert.ToDecimal(purchaseOrderEntity["fprice"]);//采购单价
                            var fprice = Convert.ToDecimal(purchaseOrderEntity["fdealprice"]);//成交单价
                            //不启用就走历史逻辑
                            var agentObj = this.AgentContext.LoadBizBillHeadDataById("bas_agent", this.AgentContext.Company, "fiscarrysupnoprice");
                            if (agentObj != null && Convert.ToInt32(agentObj["fiscarrysupnoprice"]) == 1)
                            {
                                fprice = Convert.ToDecimal(entity["fprice"]);//成交单价
                            }

                            //基本单位应收数量 = 本次提交数量                        
                            var fmaterialid_ref = purchaseOrderEntity["fmaterialid_ref"] as DynamicObject;
                            var materialid = purchaseOrderEntity["fmaterialid"]?.ToString();
                            var sotckunitid = Convert.ToString(fmaterialid_ref?["fstockunitid"]);
                            var unitid = purchaseOrderEntity["funitid"]?.ToString();
                            var purchaseunitid = purchaseOrderEntity["fbizunitid"]?.ToString();
                            var baseysqty = UnitConvertHelper.UnitConvertToBaseById(this.AgentContext, allMaterials, materialid, sotckunitid, bizqty, unitid);
                            var purchaseqty = UnitConvertHelper.BaseUnitConvertById(this.AgentContext, allMaterials, materialid, unitid, baseysqty, purchaseunitid);

                            purchaseInOrderEntity["fmaterialid"] = purchaseOrderEntity["fmaterialid"];//商品
                            purchaseInOrderEntity["fcustomdesc"] = purchaseOrderEntity["fcustomdes_e"];//定制说明
                            purchaseInOrderEntity["fattrinfo"] = purchaseOrderEntity["fattrinfo"]; //辅助属性
                            purchaseInOrderEntity["funitid"] = purchaseOrderEntity["funitid"];//基本单位
                            purchaseInOrderEntity["fbizunitid"] = purchaseOrderEntity["fbizunitid"];//采购单位
                            purchaseInOrderEntity["fstockunitid"] = Convert.ToString(fmaterialid_ref?["fstockunitid"]); //库存单位         
                            purchaseInOrderEntity["forderqty"] = purchaseOrderEntity["fqty"];//采购订单数量
                            purchaseInOrderEntity["fprice"] = fprice;//成交单价
                            purchaseInOrderEntity["fpoprice"] = purchaseOrderEntity["fprice"];//采购单价
                            purchaseInOrderEntity["fmtono"] = purchaseOrderEntity["fmtono"];//物流跟踪号                                    
                            purchaseInOrderEntity["fownertype"] = purchaseOrderEntity["fownertype"];//货主类型
                            purchaseInOrderEntity["fownerid"] = purchaseOrderEntity["fownerid"];//货主
                            purchaseInOrderEntity["fentrynote"] = purchaseOrderEntity["fnote"];//备注
                            purchaseInOrderEntity["fsourceformid"] = "ydj_purchaseorder";//来源单类型
                            purchaseInOrderEntity["fsourcebillno"] = purchaseOrder["fbillno"];//来源单编号                                        
                            purchaseInOrderEntity["fsourceentryid"] = purchaseOrderEntity["Id"];//来源单分录内码
                            purchaseInOrderEntity["fsourceinterid"] = purchaseOrder["Id"];//采购订单内码
                            purchaseInOrderEntity["fpoorderno"] = purchaseOrder["fbillno"];//采购订单编号
                            purchaseInOrderEntity["fpoorderinterid"] = purchaseOrder["Id"];//采购订单内码
                            purchaseInOrderEntity["fpoorderentryid"] = purchaseOrderEntity["Id"];//采购订单分录内码
                            purchaseInOrderEntity["FSeq"] = purchaseOrderEntity["FSeq"];//采购订单行号
                            purchaseInOrderEntity["fmtrlimage"] = purchaseOrderEntity["fmtrlimage"];//图片
                            purchaseInOrderEntity["fsoorderno"] = purchaseOrderEntity["fsoorderno"];//销售合同编号
                            purchaseInOrderEntity["fsoorderinterid"] = purchaseOrderEntity["fsoorderinterid"];//销售合同内码
                            purchaseInOrderEntity["fsoorderentryid"] = purchaseOrderEntity["fsoorderentryid"];//销售合同分录内码
                            purchaseInOrderEntity["fcustomer"] = purchaseOrderEntity["fcustomer"];//客户
                            purchaseInOrderEntity["fhqderno"] = purchaseOrder["fhqderno"];//总部合同号
                            purchaseInOrderEntity["fdeliveryfactoryid"] = entity["fdeliveryfactoryid"];//交货工厂
                            purchaseInOrderEntity["fplanqty"] = baseysqty;//基本单位应收数量 
                            purchaseInOrderEntity["fqty"] = baseysqty;//基本单位实收数量
                            purchaseInOrderEntity["fbizqty"] = bizqty;//实收数量
                            purchaseInOrderEntity["freceptionpackageqty"] = packqty;//采购实收包数
                            purchaseInOrderEntity["fstockqty"] = bizqty;//库存单位实收数量
                            purchaseInOrderEntity["fstorehouseid"] = storeid;//仓库
                            purchaseInOrderEntity["fstorelocationid"] = storelocationid;//仓位
                            purchaseInOrderEntity["fstockstatus"] = stockstatus;//库存状态
                            purchaseInOrderEntity["fpoamount"] = bizqty * fpoprice;//金额
                            purchaseInOrderEntity["famount"] = bizqty * fprice;//成交金额
                            purchaseInOrderEntity["fbizplanqty"] = bizqty;//应收数量
                            purchaseInOrderEntity["fbizorderqty"] = purchaseqty;//采购订单数量
                            purchaseInOrderEntity["fresultbrandid"] = purchaseOrderEntity["fresultbrandid"];//业绩品牌
                            purchaseInOrderEntity["fentrystaffid"] = purchaseOrderEntity["fentrystaffid"];//销售员
                            purchaseInOrderEntity["fsupplierorderno"] = purchaseOrder["fsupplierorderno"];//供货方订单号

                            purchaseInOrderEntity["fvolumeunit"] = entity["fvolumeunit"];//体积单位
                            purchaseInOrderEntity["ftotalvolume"] = entity["ftotalvolume"];//总体积
                            purchaseInOrderEntity["fsinglevolume"] = entity["fsinglevolume"];//单位体积

                            if (tuples != null && tuples.Count > 1)
                            {
                                for (int i = 0; i < tuples.Count; i++)
                                {
                                    if (i == 0) continue;
                                    var curbizbaseqty = UnitConvertHelper.UnitConvertToBaseById(this.AgentContext, allMaterials, materialid, sotckunitid, tuples[i].qty, unitid);
                                    var curpurchaseqty = UnitConvertHelper.BaseUnitConvertById(this.AgentContext, allMaterials, materialid, unitid, curbizbaseqty, purchaseunitid);
                                    var newpurchaseInOrderEntitys = purchaseInOrderEntity.Clone() as DynamicObject;
                                    newpurchaseInOrderEntitys["fplanqty"] = curbizbaseqty;//基本单位应收数量 
                                    newpurchaseInOrderEntitys["fqty"] = curbizbaseqty;//基本单位实收数量
                                    newpurchaseInOrderEntitys["fbizqty"] = tuples[i].qty;//实收数量
                                    newpurchaseInOrderEntitys["freceptionpackageqty"] = tuples[i].packqty;//采购实收包数
                                    newpurchaseInOrderEntitys["fstockqty"] = tuples[i].qty;//库存单位实收数量
                                    newpurchaseInOrderEntitys["fstorehouseid"] = tuples[i].storeid;//仓库
                                    newpurchaseInOrderEntitys["fstorelocationid"] = tuples[i].storelocationid;//仓位
                                    newpurchaseInOrderEntitys["fstockstatus"] = tuples[i].storestatus;//库存状态
                                    newpurchaseInOrderEntitys["fpoamount"] = tuples[i].qty * fpoprice;//金额
                                    newpurchaseInOrderEntitys["famount"] = tuples[i].qty * fprice;//成交金额
                                    newpurchaseInOrderEntitys["fbizplanqty"] = tuples[i].qty;//应收数量
                                    newpurchaseInOrderEntitys["fbizorderqty"] = curpurchaseqty;//采购订单数量
                                    purchaseInOrderEntitys.Add(newpurchaseInOrderEntitys);
                                }
                            }
                        }
                    }

                    #endregion

                    purchaseInOrderEntitys.Add(purchaseInOrderEntity);

                    foreach (var item in purchaseInOrderEntitys)
                    {
                        var nowsuben = subEntitys.Where(f =>
                           Convert.ToString(f["fmaterialid_s"]).Trim() == Convert.ToString(item["fmaterialid"]).Trim()
                           && Convert.ToString(f["fattrinfo"]).Trim() == Convert.ToString(item["fattrinfo"]).Trim()
                           && Convert.ToString(f["fcustomdesc"]).Trim() == Convert.ToString(item["fcustomdesc"]).Trim()
                           && Convert.ToString(f["fmtono"]).Trim() == Convert.ToString(item["fmtono"]).Trim()
                           && Convert.ToString(f["flotno"]).Trim() == Convert.ToString(item["flotno"]).Trim()
                           && Convert.ToString(f["fownertype"]).Trim() == Convert.ToString(item["fownertype"]).Trim()
                           && Convert.ToString(f["fownerid"]).Trim() == Convert.ToString(item["fownerid"]).Trim()
                           && Convert.ToString(f["fstorehouseid"]).Trim() == Convert.ToString(item["fstorehouseid"]).Trim()
                           && Convert.ToString(f["fstorelocationid"]).Trim() == Convert.ToString(item["fstorelocationid"]).Trim()
                           ).OrderByDescending(f => f["fsubmitdate"]).FirstOrDefault();
                        if (nowsuben != null)
                        {
                            nowsuben["flinkformid"] = "stk_postockin";
                            nowsuben["flinkbillno"] = purchaseInOrder["fbillno"];
                            nowsuben["flinkrownumber"] = item["fseq"];
                            nowsuben["flinkrowinterid"] = item["Id"];
                        }
                    }
                }
                if (purchaseInOrderEntitys.Any())
                {
                    //二次提交条码时，商品明细会没有数据，此时不用保存
                    purchaseInOrders.Add(purchaseInOrder);
                }
            }
            if (saveScanRecords != null && saveScanRecords.Count > 0)
            {
                //this.AgentContext.SaveBizData("bcm_scanresult", saveScanRecords);
                var scanrecordsaveres = getway.InvokeBillOperation(this.AgentContext, "bcm_scanresult", saveScanRecords, "save", null);
                if (scanrecordsaveres != null && !scanrecordsaveres.IsSuccess)
                {
                    throw new Exception($"保存条码扫描记录失败:" + string.Join("", scanrecordsaveres.ComplexMessage.ErrorMessages));
                }
            }
            if (savebcMaster != null && savebcMaster.Count > 0)
            {
                //this.AgentContext.SaveBizData("bcm_barcodemaster", savebcMaster);
                var bcmastersaveres = getway.InvokeBillOperation(this.AgentContext, "bcm_barcodemaster", savebcMaster, "save", null);
                if (bcmastersaveres != null && !bcmastersaveres.IsSuccess)
                {
                    throw new Exception($"保存条码主档失败:" + string.Join("", bcmastersaveres.ComplexMessage.ErrorMessages));
                }
            }
            if (purchaseInOrders.Any())
            {
                //this.AgentContext.SaveBizData("stk_postockin", purchaseInOrder);
                //var poinsaveres = getway.InvokeBillOperation(this.AgentContext, "stk_postockin", purchaseInOrders, "save", null);
                foreach (var purchaseInOrder in purchaseInOrders)
                {
                    var saveResult = this.AgentContext.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(this.AgentContext, "stk_postockin", new List<DynamicObject>() { purchaseInOrder }, "save", new Dictionary<string, object>());
                    if (saveResult != null && !saveResult.IsSuccess)
                    {
                        //throw new Exception($"保存采购入库单失败:" + string.Join("", poinsaveres.ComplexMessage.ErrorMessages));
                        saveResult.ThrowIfHasError(true, $"【{htmlForm.Caption}】保存失败:");
                    }
                    ScanSubmit.AutoAuditBizData(this.AgentContext, "stk_postockin", purchaseInOrder);
                }    
            }
            UpdateTaskType(tasks);
            if (tasks.Any())
            {
                var bizsaveres = getway.InvokeBillOperation(this.AgentContext, "bcm_receptionscantask", tasks, "save", null);
                if (bizsaveres != null && !bizsaveres.IsSuccess)
                {
                    throw new Exception($"保存{this.HtmlForm.Caption}失败:" + string.Join("", bizsaveres.ComplexMessage.ErrorMessages));
                }
            }
            SubmitHelper.MarkEntrysSubmitStatus(this.AgentContext, allEntryIds, false);

            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "批量收货提交成功。";
        }
        /// <summary>
        /// 校验请求
        /// </summary>
        /// <returns></returns>
        private bool CheckBody(ScanData submitData, List<DynamicObject> allWareHouses)
        {
            var ret = new List<string>();
            if (submitData.IsNullOrEmptyOrWhiteSpace())
                ret.Add("扫描提交数据包不能为空!");
            if (submitData.materialInfos == null || !submitData.materialInfos.Any())
                ret.Add("物料信息不能为空!");
            if (submitData.storeid.IsNullOrEmptyOrWhiteSpace())
                ret.Add("仓库信息未填。");
            var storelists = new List<string>();
            submitData.materialInfos.ForEach(f =>
            {
                var barcodeQtySum = 0;
                f.barcodeinfos.ForEach(ff =>
                {
                    barcodeQtySum += ff.qty;
                    if (!ff.barcode.IsNullOrEmptyOrWhiteSpace() && ff.storeid.IsNullOrEmptyOrWhiteSpace())
                        ret.Add($"条码【{ff.barcode}】收入仓库不能为空!");
                    if (!storelists.Contains(ff.storeid))
                        storelists.Add(ff.storeid);
                });
                if (f.scanqty != barcodeQtySum)
                {
                    ret.Add($"商品（【{f.materialcode}】{f.materialname}）提交的数量与扫描的数量不相符，请检查！");
                }
            });
            allWareHouses = this.AgentContext.LoadBizDataById("ydj_storehouse", storelists, true);
            if (submitData.storelocationid.IsNullOrEmptyOrWhiteSpace())
            {
                var headwarehouse = allWareHouses.FirstOrDefault(f => f["Id"]?.ToString() == submitData.storeid);
                if (headwarehouse != null)
                {
                    var headwhens = headwarehouse["fentity"] as DynamicObjectCollection;
                    if (headwhens != null && headwhens.Any())
                    {
                        var ddd = headwhens.Where(f => !f["flocnumber"].IsNullOrEmptyOrWhiteSpace()).ToList();
                        if (ddd != null && ddd.Any())
                        {
                            ret.Add($"仓库【{headwarehouse["fname"]?.ToString()}】启用了仓位，请录入仓位!");
                        }
                    }
                }
            }


            //获取所有的仓库信息
            //var allWareHouses = this.AgentContext.LoadBizDataById("ydj_storehouse", storelists, true);
            foreach (var mat in submitData.materialInfos)
            {
                foreach (var item in mat.barcodeinfos)
                {
                    var store = allWareHouses.FirstOrDefault(f => f["Id"]?.ToString() == item.storeid);
                    if (store != null)
                    {
                        var storeentitys = store["fentity"] as DynamicObjectCollection;
                        if (storeentitys != null && storeentitys.Count > 0 && string.IsNullOrWhiteSpace(item.storelocationid))
                        {
                            var ddd = storeentitys.Where(f => !f["flocnumber"].IsNullOrEmptyOrWhiteSpace()).ToList();
                            if (ddd != null && ddd.Count > 0 && item.storelocationid.IsNullOrEmptyOrWhiteSpace())
                                ret.Add($"条码【{item.barcode}】仓库【{store["fname"]?.ToString()}】明细中维护了仓位，仓位必须录入!");
                        }
                    }
                }
            }
            //校验扫描提交的商品是否已存在足量的入库单，或超收
            string stockInSql = @"SELECT C.fbillno AS purNo,D.fentryid,F.fnumber AS matNo,F.fname AS matName,MAX(D.fbizqty) AS cgQty,SUM(E.fbizqty) AS yrkQty 
                                  FROM T_BCM_RECEPTIONSCANTASK AS A WITH(NOLOCK) 
                                  INNER JOIN T_BCM_RESCANTASKENTITY AS B WITH(NOLOCK) ON A.fid=B.fid 
                                  INNER JOIN T_YDJ_PURCHASEORDER AS C WITH(NOLOCK) ON B.fsourcebillno=C.fbillno 
                                  INNER JOIN T_YDJ_POORDERENTRY AS D WITH(NOLOCK) ON C.fid=D.fid AND B.fsourceentryid=D.fentryid 
                                  INNER JOIN T_STK_POSTOCKINENTRY AS E WITH(NOLOCK) ON C.fid=E.fsourceinterid AND D.fentryid=E.fpoorderentryid 
                                  INNER JOIN T_BD_MATERIAL AS F WITH(NOLOCK) ON D.fmaterialid=F.fid 
                                  WHERE A.fbillno='{0}' AND B.fmaterialid='{1}' AND B.fentryid='{2}' 
                                  GROUP BY C.fbillno,D.fentryid,F.fnumber,F.fname ";
            submitData.materialInfos.ForEach(t =>
            {
                var currSql = stockInSql.Fmt(submitData.taskno, t.materialid, t.enid);
                var stockInfos = this.AgentContext.ExecuteDynamicObject(currSql, null).ToList();
                if (stockInfos != null && stockInfos.Any())
                {
                    var stkInfo = stockInfos.FirstOrDefault();
                    var yrkQty = Convert.ToDecimal(stkInfo["yrkQty"]);//已入库数量
                    var cgQty = Convert.ToDecimal(stkInfo["cgQty"]);//采购数量
                    if (yrkQty + t.scanqty > cgQty)//已入库数量 + 扫描数量 > 采购数量
                    {
                        var matNo = Convert.ToString(stkInfo["matNo"]);
                        var matName = Convert.ToString(stkInfo["matName"]);
                        ret.Add($"任务【{submitData.taskno}】中商品【{matNo}-{matName}】已入库或超收，请检查！");
                    }
                }
            });
            if (ret.Any())
                this.Result.SimpleMessage = $"提交失败。原因如下：{string.Join("", ret)}";
            return ret.Any();
        }
        /// <summary>
        /// 检查齐套
        /// </summary>
        /// <param name="bclist"></param>
        /// <returns></returns>
        private bool CheckIntegrality(List<string> bclist)
        {
            var ret = new List<string>();
            var searchSql = $@"select a.fid as Id,a.fnumber as fbarcode,a.fpackagtype,a.fpackcount,
a.fpackindex,b.fmaterialid,c.fname as fmaterialname,b.fattrinfo,fcustomdesc,b.flotno,b.fmtono,b.fownertype,b.fownerid
from T_BCM_BARCODEMASTER a 
left join T_BCM_MASTERMTRLENTRY b on b.fid=a.fid
left join T_BD_MATERIAL c on c.fid = b.fmaterialid
where 1=1 and a.fforbidstatus='0'
and  a.fmainorgid = '{this.AgentContext.BizOrgId}' 
and a.fnumber in ('{string.Join("','", bclist)}')";
            var bcmasters = this.AgentContext.ExecuteDynamicObject(searchSql, null).ToList();
            BarCodeMasterHelper.CheckIntegrality(bcmasters, bclist, ret);
            if (ret.Any())
                this.Result.SimpleMessage = $"提交失败。齐套校验不通过：{string.Join("", ret)}";
            return ret.Any();
        }
        /// <summary>
        /// 检查数据
        /// </summary>
        /// <param name="scanMaterialInfos"></param>
        /// <returns></returns>
        private bool CheckDatas(List<ScanMaterialInfo> scanMaterialInfos, List<DynamicObject> entrys)
        {
            var ret = new List<string>();
            var billnos = scanMaterialInfos.Select(s => s.billno);
            var isError = true;
            foreach (var item in scanMaterialInfos)
            {
                var entity = entrys.FirstOrDefault(f => Convert.ToString(f["id"]).EqualsIgnoreCase(item.enid));
                if (entity == null)
                {
                    ret.Add($"商品【{item.materialname}】在PDA所扫条码对应的收货扫描任务【{string.Join(",", billnos)}】中均未匹配到明细商品，请检查!");
                    continue;
                }
                var waitqty = Convert.ToDecimal(entity["fwaitworkqty"]);
                if (item.scanqty > 0)
                    isError = false;
                if (item.scanqty > waitqty)
                {
                    ret.Add($"商品【{item.materialname}】本次收货数量【{Math.Round(item.scanqty, 2)}】大于待收数量【{Math.Round(waitqty, 2)}】, 不允许超收！");
                }
            }
            if (isError)
                ret.Add("提交任务至少录入一行商品数量不为0, 否则不允许收货。");
            if (ret.Any())
                this.Result.SimpleMessage = $"提交失败。原因如下：{string.Join("", ret)}";
            return ret.Any();
        }
        /// <summary>
        /// 构造采购入库单单据头信息
        /// </summary>
        /// <param name="task"></param>
        /// <param name="newstaffid"></param>
        /// <param name="newdepartid"></param>
        /// <returns></returns>
        private DynamicObject BuildPostockinHead(DynamicObject task, string newstaffid, string newdepartid)
        {
            var postockinForm = this.MetaModelService.LoadFormModel(this.AgentContext, "stk_postockin");//采购入库单
            var purchaseInOrder = postockinForm.GetDynamicObjectType(this.AgentContext).CreateInstance() as DynamicObject;
            var billtypecollection = ScanSubmit.GetBillType(this.AgentContext, "stk_postockin");
            var billtype = billtypecollection.FirstOrDefault(f => f.fname == "标准采购入库" || (!f.fprimitivename.IsNullOrEmptyOrWhiteSpace() && f.fprimitivename == "标准采购入库"));
            purchaseInOrder["fbilltype"] = billtype == null ? "poinstock_billtype_01" : billtype.fid;//"标准采购入库";//单据类型
            purchaseInOrder["fdate"] = DateTime.Now;//入库日期
            purchaseInOrder["fdeliverybillno"] = task["flogisticsno"];//物流单号
            purchaseInOrder["fstockstaffid"] = newstaffid;
            purchaseInOrder["fstockdeptid"] = newdepartid;
            purchaseInOrder["fsourcetype"] = "ydj_purchaseorder";//来源单据
            purchaseInOrder["fmainorgid"] = this.AgentContext.Company;//使用组织ID
            purchaseInOrder["fbizruleid"] = "ydj_purchaseorder2stk_postockin";//规则
            purchaseInOrder["freceiveno"] = task["freceptionno"];//收货单号
            purchaseInOrder["fpdainstock"] = true;//是否PDA作业
            purchaseInOrder["fsenddate"] = task["fsenddate"];//工厂发货日期
            return purchaseInOrder;
        }
        private void UpdateTaskType(List<DynamicObject> tasks)
        {
            foreach (var task in tasks)
            {
                var fens = task["ftaskentity"] as DynamicObjectCollection;
                var unfinish = fens.Where(w => Convert.ToDecimal(w["fwaitworkqty"]) > 0).Any();
                if (unfinish)
                {
                    task["ftaskstatus"] = "ftaskstatus_03";//作业中      
                }
                else
                {
                    task["ftaskstatus"] = "ftaskstatus_04";//已完成    
                    task["fisbatchwork"] = "1";//记录任务作业方式
                }
            }
        }

        /// <summary>
        /// 初始化单据锁
        /// </summary>
        /// <param name="e"></param>
        public override void InitDistributedLocks(InitDistributedLocksEventArgs e)
        {
            base.InitDistributedLocks(e);

            var scanData = this.GetQueryOrSimpleParam<string>("scandata");//扫描数据信息
            ScanData submitData = scanData.FromJson<ScanData>();
            if (submitData.IsNullOrEmptyOrWhiteSpace() || submitData.materialInfos.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }

            //获取所有提交的扫描任务号
            var allTaskNo = submitData.materialInfos.Where(t => !t.billno.IsNullOrEmptyOrWhiteSpace()).Select(s => s.billno).Distinct().ToList();
            if (allTaskNo.IsNullOrEmptyOrWhiteSpace() || !allTaskNo.Any())
            {
                return;
            }

            e.DistributedLocks = allTaskNo.ToDictionary(t => $"DistributedLock:PdaScanSubmit:{t}", t => $"{this.HtmlForm.Caption}【{t}】正在提交中，请稍后再操作！");
        }
    }
}
