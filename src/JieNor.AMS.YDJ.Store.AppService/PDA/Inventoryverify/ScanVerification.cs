using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using JieNor.AMS.YDJ.Store.AppService.Model.Inventoryverify;

namespace JieNor.AMS.YDJ.Store.AppService.PDA.Inventoryverify
{
    /// <summary>
    /// PDA期初盘点 扫码获取条码相关信息
    /// </summary>
    [InjectService]
    [FormId("bcm_barcodemaster")]
    [OperationNo("scanverification")]
    public class ScanVerification : AbstractOperationServicePlugIn
    {
        protected UserContext AgentContext { get; set; }
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var barcode = this.GetQueryOrSimpleParam<string>("barcode");//条码信息
            var agentId = this.GetQueryOrSimpleParam<string>("agentid");//经销商ID
            var storehouseid = this.GetQueryOrSimpleParam<string>("storehouseid");//仓库id
            var storelocationid = this.GetQueryOrSimpleParam<string>("storelocationid");//仓位Id
            this.AgentContext = this.Context.CreateAgentDBContext(agentId);
            if (string.IsNullOrWhiteSpace(barcode))
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "参数【商品条码】不能为空!";
                return;
            }
            var filter = $" fforbidstatus='0' and fmainorgid='{this.AgentContext.Company}' and fnumber ='{barcode}' ";
            var barCodeInfo = this.AgentContext.LoadBizDataByFilter(this.HtmlForm.Id, filter, true).FirstOrDefault();
            if (barCodeInfo == null)
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = $"扫描失败, 当前扫描的条码【{barcode}】不存在！";
                return;
            }

            var result = new ScanVerificationModel();
            var profileService = this.AgentContext.Container.GetService<ISystemProfile>();
            result.IsWarn = profileService.GetSystemParameter(this.AgentContext, "stk_stockparam", "fpdacheckwarehousestatus", false);
            if (result.IsWarn)
            {
                var taskstatus = Convert.ToString(barCodeInfo["fbizstatus"]);
                result.mainid = Convert.ToString(barCodeInfo["Id"]);
                result.barcodeno = Convert.ToString(barCodeInfo["fnumber"]);
                result.bizstatus = "待入库";
                switch (taskstatus)
                {
                    case "1":
                        result.bizstatus = "可用";
                        break;
                    case "2":
                        result.bizstatus = "已出库";
                        break;
                    case "4":
                        result.bizstatus = "待入库";
                        break;
                    case "5":
                        result.bizstatus = "已备货";
                        break;
                }
                var store = barCodeInfo["fstorehouseid_ref"] as DynamicObject;
                result.store = new
                {
                    id = store?["Id"],
                    fnumber = store?["fnumber"],
                    fname = store?["fname"]
                };
                var storelocation = barCodeInfo["fstorelocationid_ref"] as DynamicObject;
                result.storelocation = new
                {
                    id = storelocation?["Id"],
                    fnumber = storelocation?["fnumber"],
                    fname = storelocation?["fname"]
                };

                var sql = $@"select top 1 t1.fid,t2.fname sourcebillname,fsourcebillno,t3.fname scantaskbillname,fscantaskbillno
                        from t_bcm_scanresult t1 with(nolock) 
                        left join T_SYS_BIZOBJECT t2 with(nolock) on t1.fsourceformid=t2.FId
                        left join T_SYS_BIZOBJECT t3 with(nolock) on t1.fscantaskformid=t3.FId
                        where fbarcode='{result.mainid}' and fmainorgid='{this.AgentContext.Company}' order by fopdatetime desc";
                var dbService = this.Container.GetService<IDBService>();
                var data = dbService.ExecuteDynamicObject(this.Context, sql)?.FirstOrDefault();
                if (data != null)
                {
                    result.bizformname = Convert.ToString(data["sourcebillname"]) ?? Convert.ToString(data["scantaskbillname"]);
                    result.bizbillno = Convert.ToString(data["fsourcebillno"]) ?? Convert.ToString(data["fscantaskbillno"]);
                }
            }
            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = $"获取成功！";
            this.Result.SrvData = result;
        }
    }
}
