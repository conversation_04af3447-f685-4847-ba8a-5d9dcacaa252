using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.Stock.AppService.PDA.Inventoryverify
{
    /// <summary>
    /// PDA期初盘点仓库和仓位扫描
    /// </summary>
    [InjectService]
    [FormId("stk_inventoryverify")]
    [OperationNo("warehousescan")]
    public class WareHouseScan:AbstractOperationServicePlugIn
    {
        protected UserContext AgentContext { get; set; }
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            //扫描条码
            var barcode = this.GetQueryOrSimpleParam<string>("barcode");
            //扫描类型：1仓库 2仓位 3条码
            var type = this.GetQueryOrSimpleParam<string>("type");
            //扫描仓库
            var warehouse = this.GetQueryOrSimpleParam<string>("warehouse");
            var agentId = this.GetQueryOrSimpleParam<string>("agentid");//经销商ID
            this.AgentContext = this.Context.CreateAgentDBContext(agentId);
            var errMsg = new List<string>();
            if (string.IsNullOrWhiteSpace(barcode))
            {
                errMsg.Add("扫描条码不能为空!");                
            }
            if (string.IsNullOrWhiteSpace(type))
            {
                errMsg.Add("扫描类型不能为空!");                
            }
            if (type == "2" && string.IsNullOrWhiteSpace(warehouse))
            {
                errMsg.Add("扫描类型为仓位时，扫描仓库不能为空!");
            }
            if (errMsg.Count > 0)
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = string.Join("", errMsg);
                return;
            }

            var formid = "ydj_storehouse";
            var filter = "";
            switch (type)
            {
                case "1"://仓库
                    filter = $" fnumber = '{barcode}'";                   
                    break;
                case "2"://仓位
                    filter = $" fnumber = '{warehouse}'";
                    break;
                default:
                    this.Result.SimpleMessage="传递参数不正确，扫描类型值只能为1(仓库)和2(仓位)！";
                    return;
            }
            var bizData = this.AgentContext.LoadBizDataByFilter(formid, filter).FirstOrDefault();
            if (bizData == null)
            {
                this.Result.SimpleMessage=$"条码【{barcode}】未找到对应的仓库信息,请检查";
                this.Result.IsSuccess = false;
                return;
            }

            if (type == "1")
            {
                var isuselocation = "0";
                var entitys = bizData["fentity"] as DynamicObjectCollection;
                if (entitys != null && entitys.Count > 0)
                {
                    var locinfos = entitys.Where(f => !f["flocnumber"].IsNullOrEmptyOrWhiteSpace());
                    if (locinfos != null && locinfos.Any())
                    {
                        isuselocation = "1";
                    }
                }
                this.Result.SrvData = new
                {
                    WareHouseId = bizData["Id"]?.ToString(),
                    WareHosueCode = bizData["fnumber"]?.ToString(),
                    WareHouseName = bizData["fname"]?.ToString(),
                    IsUseLocation = isuselocation
                };
            }
            else
            {
                var localtions = bizData["fentity"] as DynamicObjectCollection;
                if (localtions.Count <= 0)
                {
                    this.Result.SimpleMessage=$"仓库【{bizData["fname"]?.ToString()}】没有维护仓位信息！";
                    this.Result.IsSuccess = false;
                    return;
                }
                var localtion = localtions.FirstOrDefault(f=>f["flocnumber"]?.ToString() == barcode);
                if (localtion == null)
                {
                    this.Result.SimpleMessage=$"条码【{barcode}】在仓库【{bizData["fname"]?.ToString()}】未找到对应的仓位!";
                    this.Result.IsSuccess = false;
                    return;
                }
                this.Result.SrvData = new
                {
                    WareHouseLocationId = localtion["Id"]?.ToString(),
                    WareHouseLocationCode = localtion["flocnumber"]?.ToString(),
                    WareHouseLocationName = localtion["flocname"]?.ToString()
                }; 
            }
            this.Result.IsSuccess = true;
            this.Result.SimpleMessage="条码扫描成功！";
        }
    }
}
