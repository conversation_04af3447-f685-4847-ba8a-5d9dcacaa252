using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Store.AppService.Model.DeliveryScanTask;
using JieNor.Framework.SuperOrm;
using JieNor.AMS.YDJ.Store.AppService.PDA.WmsCommon;
using JieNor.AMS.YDJ.Store.AppService.Model;
using JieNor.Framework.DataTransferObject.Poco;

namespace JieNor.AMS.YDJ.Store.AppService.PDA.SendScanTask
{
    /// <summary>
    /// PDA获取调出数量
    /// </summary>
    [InjectService]
    [FormId("bcm_transferintask")]
    [OperationNo("getoutqty")]
    public class GetOutQty : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var enId = this.GetQueryOrSimpleParam<string>("enIds","");//经销商ID
            if (enId.IsNullOrEmptyOrWhiteSpace())
                return;
            var enIdLst = enId.Split(',');
            var sql = $"select fentryid as fsourceentryid,fstockoutqty from t_stk_invtransferentry where fentryid in ('{string.Join("','", enIdLst)}')";
            var ret = this.Context.Container.GetService<IDBService>().ExecuteDynamicObject(this.Context, sql);
            if (ret == null || !ret.Any())
                return;
            this.Result.SrvData = ret;
        }
    }
}
