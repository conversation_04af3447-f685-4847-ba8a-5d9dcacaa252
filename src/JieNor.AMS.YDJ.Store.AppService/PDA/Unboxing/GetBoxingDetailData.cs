using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework;
using System.Data;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Store.AppService.Model.Unboxing;
using JieNor.AMS.YDJ.Store.AppService.Model;
using JieNor.AMS.YDJ.Store.AppService.PDA.WmsCommon;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.PDA.Unboxing
{
    /// <summary>
    /// 获取装箱条码明细数据
    /// </summary>
    [InjectService]
    [FormId("bcm_barcodemaster")]
    [OperationNo("getboxingdetaildata")]
    public class GetBoxingDetailData: AbstractOperationServicePlugIn
    {
        protected UserContext AgentContext { get; set; }
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            var type = this.GetQueryOrSimpleParam<string>("type",""); //查询类型 1拼箱 2拆箱
            var barcode = this.GetQueryOrSimpleParam<string>("barcode", ""); //装箱条码
            var agentId = this.GetQueryOrSimpleParam<string>("agentid");//经销商ID
            this.AgentContext = this.Context.CreateAgentDBContext(agentId);

            if (string.IsNullOrWhiteSpace(barcode))
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add($"参数【商品条码】不能为空!");
                return;
            }
            if (string.IsNullOrWhiteSpace(type))
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add($"参数【查询类型】不能为空!");
                return;
            }

            var filter = $" fforbidstatus='0' and fmainorgid='{this.AgentContext.Company}' and fnumber ='{barcode}' ";
            var barCodeInfos = this.AgentContext.LoadBizDataByFilter(this.HtmlForm.Id, filter, true);
            if (barCodeInfos == null || barCodeInfos.Count<=0)
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add($"扫描失败, 当前扫描的条码【{barcode}】不存在！");
                return;
            }
            if (barCodeInfos.Count > 1)
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add($"当前扫描的条码【{barcode}】在当前组织下存在多条相同码文的数据，请先处理异常！");
                return;
            }
            var barCodeInfo = barCodeInfos.FirstOrDefault();
            var entitys = barCodeInfo["fentity"] as DynamicObjectCollection;
            var en = entitys.FirstOrDefault(f => f["fbarcode"].IsNullOrEmptyOrWhiteSpace());
            var packtype = Convert.ToString(barCodeInfo["fpackagtype"]);           
            if (type == "1")
            {               
                //1标准 2一件多包 3一包多件
                if (packtype != "1")
                {
                    this.Result.IsSuccess = false;
                    this.Result.ComplexMessage.ErrorMessages.Add($"当前扫描的条码【{barcode}】打包类型不为【标准】，不支持装箱！");
                    return;
                }
                                
                if (en != null)
                {
                    this.Result.IsSuccess = false;
                    this.Result.ComplexMessage.ErrorMessages.Add($"扫描失败, 当前扫描的条码【{barcode}】为底层商品包装码, 不支持作为装箱条码！");
                    return;
                }
            }
            else
            {
                if (packtype == "2")
                {
                    this.Result.IsSuccess = false;
                    this.Result.ComplexMessage.ErrorMessages.Add($"当前扫描的条码【{barcode}】打包类型为【1件多包】，不支持拆包！");
                    return;
                }              
            }

            //获取所有子条码对应的条码主档
            var subbcnos = new List<string>();
            BarCodeMasterHelper.GetSubBarCode(this.AgentContext, new List<string>() { barcode }, ref subbcnos);
            var alllastbcmasters = this.AgentContext.LoadBizDataByFilter(this.HtmlForm.Id,$" fnumber in ('{string.Join("','",subbcnos)}') ",true);

            var resbarcodeinfo = new BarCodeInfo();
            var materialinfos = new List<MaterialInfo>();
            resbarcodeinfo.mainbarcode = barcode;

            var loadSer = this.Container.GetService<LoadReferenceObjectManager>();
            foreach (var entity in entitys)
            {
                var cursubbclists = new List<string>();
                var cursubbc = Convert.ToString(entity["fbarcode"]);
                if (cursubbc.IsNullOrEmptyOrWhiteSpace())
                {
                    var materialinforef = entity["fmaterialid_ref"] as DynamicObject;
                    if (materialinforef == null) continue;
                    var materialinfo = new MaterialInfo();
                    materialinfo.subbarcode = string.IsNullOrWhiteSpace(cursubbc) ? barcode : cursubbc;
                    materialinfo.material = new BaseDataModel(materialinforef);
                    var baseunitinfo = materialinforef["funitid_ref"] as DynamicObject;
                    materialinfo.baseunit = new Model.BaseDataModel(baseunitinfo);
                    var stockunitinfo = materialinforef["fstockunitid_ref"] as DynamicObject;
                    materialinfo.stockunit = new Model.BaseDataModel(stockunitinfo);
                    var attrinfo = entity["fattrinfo_ref"] as DynamicObject;
                    materialinfo.attrinfo = new Model.BaseDataModel(attrinfo);
                    materialinfo.customdesc = Convert.ToString(entity["fcustomdesc"]);
                    materialinfo.mtono = Convert.ToString(entity["fmtono"]);
                    materialinfo.lotno = Convert.ToString(entity["flotno"]);
                    materialinfo.ownertype = Convert.ToString(entity["fownertype"]);
                    materialinfo.ownerid = Convert.ToString(entity["fownerid"]);
                    materialinfo.mtrlmodel = Convert.ToString(materialinforef["fspecifica"]);
                    var fbrandid_ref = materialinforef["fbrandid_ref"] as DynamicObject;
                    materialinfo.brandid = new Model.BaseDataModel(fbrandid_ref);
                    var fseriesid_ref = materialinforef["fseriesid_ref"] as DynamicObject;
                    materialinfo.seriesid = new Model.BaseDataModel(fseriesid_ref);
                    materialinfo.stockqty = Math.Round(Convert.ToDecimal(entity["fstockqty"]), 2);
                    materialinfo.packcount = Convert.ToInt32(barCodeInfo["fpackcount"]);
                    materialinfo.packindex = Convert.ToInt32(barCodeInfo["fpackindex"]);
                    materialinfos.Add(materialinfo);
                }
                else
                {
                    var curbarcodeinfo = alllastbcmasters.FirstOrDefault(f => f["fnumber"]?.ToString() == cursubbc);
                    BarCodeMasterHelper.GetLastBarCode(this.AgentContext, new List<string>() { cursubbc }, ref cursubbclists);
                    foreach (var subbc in cursubbclists)
                    {
                        var bcinfo = alllastbcmasters.FirstOrDefault(f => f["fnumber"]?.ToString() == subbc);
                        if (bcinfo == null) continue;
                        var subentitys = bcinfo["fentity"] as DynamicObjectCollection;
                        foreach (var subentity in subentitys)
                        {
                            var materialinforef = subentity["fmaterialid_ref"] as DynamicObject;
                            if (materialinforef == null) continue;
                            loadSer.Load(this.AgentContext, materialinforef.DynamicObjectType, materialinforef, false);
                            var ddd = materialinfos.FirstOrDefault(f =>
                              Convert.ToString((f.material as Model.BaseDataModel).id).Trim() == Convert.ToString(materialinforef["Id"]).Trim()
                               && Convert.ToString((f.attrinfo as Model.BaseDataModel).id).Trim() == Convert.ToString(subentity["fattrinfo"]).Trim()
                               && Convert.ToString(f.customdesc).Trim() == Convert.ToString(subentity["fcustomdesc"]).Trim()
                               && Convert.ToString(f.mtono).Trim() == Convert.ToString(subentity["fmtono"]).Trim()
                               && Convert.ToString(f.lotno).Trim() == Convert.ToString(subentity["flotno"]).Trim()
                               && Convert.ToString(f.ownertype).Trim() == Convert.ToString(subentity["fownertype"]).Trim()
                               && Convert.ToString(f.subbarcode).Trim() == Convert.ToString(cursubbc).Trim()
                               //&& Convert.ToString(f.ownerid).Trim() == Convert.ToString(subentity["ownerid"]).Trim()
                               );
                            if (ddd != null)
                            {
                                ddd.stockqty = Math.Round(ddd.stockqty + Convert.ToDecimal(subentity["fstockqty"]));
                            }
                            else
                            {
                                var materialinfo = new MaterialInfo();
                                materialinfo.subbarcode = string.IsNullOrWhiteSpace(cursubbc) ? barcode : cursubbc;
                                materialinfo.material = new BaseDataModel(materialinforef);
                                var baseunitinfo = materialinforef["funitid_ref"] as DynamicObject;
                                materialinfo.baseunit = new Model.BaseDataModel(baseunitinfo);
                                var stockunitinfo = materialinforef["fstockunitid_ref"] as DynamicObject;
                                materialinfo.stockunit = new Model.BaseDataModel(stockunitinfo);
                                var attrinfo = subentity["fattrinfo_ref"] as DynamicObject;
                                materialinfo.attrinfo = new Model.BaseDataModel(attrinfo);
                                materialinfo.customdesc = Convert.ToString(subentity["fcustomdesc"]);
                                materialinfo.mtono = Convert.ToString(subentity["fmtono"]);
                                materialinfo.lotno = Convert.ToString(subentity["flotno"]);
                                materialinfo.ownertype = Convert.ToString(subentity["fownertype"]);
                                materialinfo.ownerid = Convert.ToString(subentity["fownerid"]);
                                materialinfo.mtrlmodel = Convert.ToString(materialinforef["fspecifica"]);
                                var fbrandid_ref = materialinforef["fbrandid_ref"] as DynamicObject;
                                materialinfo.brandid = new Model.BaseDataModel(fbrandid_ref);
                                var fseriesid_ref = materialinforef["fseriesid_ref"] as DynamicObject;
                                materialinfo.seriesid = new Model.BaseDataModel(fseriesid_ref);
                                materialinfo.stockqty = Math.Round(Convert.ToDecimal(subentity["fstockqty"]), 2);
                                materialinfo.packcount = curbarcodeinfo == null ? 1 : Convert.ToInt32(curbarcodeinfo["fpackcount"]);
                                materialinfo.packindex = curbarcodeinfo == null ? 1 : Convert.ToInt32(curbarcodeinfo["fpackindex"]);
                                materialinfos.Add(materialinfo);
                            }
                        }
                    }
                }               
            }
            resbarcodeinfo.materials = materialinfos;

            this.Result.IsSuccess = true;
            this.Result.ComplexMessage.SuccessMessages.Add("执行成功!");
            this.Result.SrvData = resbarcodeinfo;
        }
        
    }
}
