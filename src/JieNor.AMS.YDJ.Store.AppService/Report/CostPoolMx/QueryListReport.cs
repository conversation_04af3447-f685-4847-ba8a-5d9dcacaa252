using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Report.CostPoolMx
{
    /// <summary>
    /// 父页面向子页面传经销商参数
    /// </summary>
    [InjectService]
    [FormId("rpt_costpoolmx")]
    [OperationNo("QueryListReport")]
    public class QueryListReport : AbstractReportServicePlugIn
    {
        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            var fagentid = this.GetQueryOrSimpleParam<string>("fagentid");
            var org = this.GetQueryOrSimpleParam<string>("org");
            var type = this.GetQueryOrSimpleParam<string>("type");
            var fdatetime_s = this.GetQueryOrSimpleParam<string>("fdatetime_s");
            var fdatetime_e = this.GetQueryOrSimpleParam<string>("fdatetime_e");
            if (!fagentid.IsNullOrEmptyOrWhiteSpace())
            {
                if (this.PageSession == null)
                {
                    throw new BusinessException("页面缓存区已回收，请关闭本页面后重新打开！");
                }
                this.PageSession.Agent = fagentid;
            }
            if (!type.IsNullOrEmptyOrWhiteSpace())
            {
                if (this.PageSession == null)
                {
                    throw new BusinessException("页面缓存区已回收，请关闭本页面后重新打开！");
                }
                this.PageSession.type = type;
            }
            if (!org.IsNullOrEmptyOrWhiteSpace())
            {
                if (this.PageSession == null)
                {
                    throw new BusinessException("页面缓存区已回收，请关闭本页面后重新打开！");
                }
                this.PageSession.org = org;
            }
            if (!fdatetime_s.IsNullOrEmptyOrWhiteSpace())
            {
                if (this.PageSession == null)
                {
                    throw new BusinessException("页面缓存区已回收，请关闭本页面后重新打开！");
                }
                this.PageSession.fdatetime_s = fdatetime_s;
            }
            if (!fdatetime_e.IsNullOrEmptyOrWhiteSpace())
            {
                if (this.PageSession == null)
                {
                    throw new BusinessException("页面缓存区已回收，请关闭本页面后重新打开！");
                }
                this.PageSession.fdatetime_e = fdatetime_e;
            }
        }
    }
}
