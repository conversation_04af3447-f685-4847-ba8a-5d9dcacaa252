using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Report.ProvinceStockSummary
{
    /// <summary>
    /// 父页面向子页面传经销商参数
    /// </summary>
    [InjectService]
    [FormId("rpt_provincestocksummary")]
    [OperationNo("QueryListReport")]
    public class QueryListReport : AbstractReportServicePlugIn
    {

        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        protected override void OnIniHtmlForm(HtmlForm htmlForm)
        {
            base.OnIniHtmlForm(htmlForm);
        }

        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            var fcategoryid = this.GetQueryOrSimpleParam<string>("fcategoryid");
            if (!fcategoryid.IsNullOrEmptyOrWhiteSpace())
            {
                this.PageSession.fcategoryid = string.Join(",", fcategoryid);
            }
            var fsaleregionalid = this.GetQueryOrSimpleParam<string>("fsaleregionalid");
            if (!fsaleregionalid.IsNullOrEmptyOrWhiteSpace())
            {
                this.PageSession.fsaleregionalid = string.Join(",", fsaleregionalid);
            }
            var fserviceregionalid = this.GetQueryOrSimpleParam<string>("fserviceregionalid");
            if (!fsaleregionalid.IsNullOrEmptyOrWhiteSpace())
            {
                this.PageSession.fserviceregionalid = string.Join(",", fserviceregionalid);
            }
            
        }
    }
}
