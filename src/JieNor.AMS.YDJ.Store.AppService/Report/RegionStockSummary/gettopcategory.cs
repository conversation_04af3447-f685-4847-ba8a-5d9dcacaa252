using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.Interface.QueryBuilder;

namespace JieNor.AMS.YDJ.Store.AppService.Report.RegionStockSummary
{
    /// <summary>
    /// 父页面向子页面传经销商参数
    /// </summary>
    [InjectService]
    [FormId("rpt_regionstocksummary")]
    [OperationNo("GetTopCategory")]
    public class GetTopCategory : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            this.Result.SrvData = this.CheckCategoryByName();
        }

        private DynamicObjectCollection CheckCategoryByName()
        {
            string sql = $@" SELECT  fid,fname FROM dbo.SER_YDJ_CATEGORY WHERE fparentid='822158608392392828' AND fmainorgid='821347239912935425'
								UNION ALL SELECT 'Other' fid,'其他类' fname";
            return this.DBService.ExecuteDynamicObject(this.Context, sql);
        }
    }
}
