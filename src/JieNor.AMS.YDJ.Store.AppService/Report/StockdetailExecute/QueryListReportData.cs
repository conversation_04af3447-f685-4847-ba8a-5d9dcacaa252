//using System;
//using System.Collections.Generic;
//using System.Linq;
//using JieNor.Framework;
//using JieNor.Framework.Interface;
//using JieNor.Framework.IoC;
//using JieNor.Framework.SuperOrm.DataEntity;
//using JieNor.Framework.SuperOrm;
//using System.Data;

//namespace JieNor.AMS.YDJ.Store.AppService.Report.StockdetailExecute
//{
//    /// <summary>
//    /// 采购订单执行情况报表：准备报表数据源
//    /// </summary>
//    [InjectService]
//    [FormId("rpt_stockdetail")]
//    [OperationNo("QueryListReportData")]
//    public class QueryListReportData : AbstractReportServicePlugIn
//    {
//        /// <summary>
//        /// 执行报表逻辑
//        /// </summary>
//        protected override void OnExecuteLogic()
//        {
//            this.Option.SetVariableValue("OrderByString", "forder asc");
//            //默认查询三个月的数据
//            //查询数据后往报表对应的临时表中插入数据
//            var insertSql = $@"/*dialect*/

//            DECLARE @SDate DATE,@EDate DATE
//            SET @SDate='{DateTime.Now.AddMonths(-3).ToShortDateString()}'
//            SET @EDate='{ DateTime.Now.ToShortDateString() }'


//            EXEC rpt_stockdetail @SDate,@EDate,@fmainorgid


//            INSERT INTO dbo.{this.DataSourceTableName}
//            (
//                fid,
//                fseq,
//                FFormId,
//                fmaterialid,
//                fattrinfo,
//                fstorehouseid,
//                fstockstatus,
//                fmtono,
//                flotno,
//                fownertype,
//                fownerid,
//                funitid,
//                startCount,
//                stockin,
//                stockout,
//                endCount,
//                billType,
//                fbillno,
//                fdate,
//	            fcustomdesc,
//	            fstorelocationid
//            )EXEC rpt_stockdetail @SDate,@EDate,@fmainorgid
//            ";

//            var sqlParam = new List<SqlParam>
//            {
//                new SqlParam("@ffromid", DbType.String, this.HtmlForm.Id),
//                new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
//            };

//            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();
//            dbServiceExt.Execute(this.Context, insertSql, sqlParam);
//        }
//    }
//}