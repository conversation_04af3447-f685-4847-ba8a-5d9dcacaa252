using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.Core.Interface.StockInit;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace JieNor.AMS.YDJ.Stock.AppService.Rpt.PurchaseAnalysis
{
    [InjectService]
    [FormId("rpt_purchaseanalysis")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {
        /// <summary>
        /// 库存访问服务
        /// </summary>
        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        /// <summary>
        /// 检查当前过滤界面必须录入的信息 
        /// </summary>
        protected void CheckDataEnvironment()
        {
            DateTime? dtDateFrom = (DateTime?)this.CustomFilterObject["fdatefrom"];
            DateTime? dtDateTo = (DateTime?)this.CustomFilterObject["fdateto"];
            if (this.CustomFilterObject.IsNullOrEmpty())
            {
                throw new BusinessException("过滤条件不可以为空！");
            }
            if (dtDateFrom == null)
            {
                throw new BusinessException("过滤条件【起始日期】必录！");
            }
            if (dtDateTo == null)
            {
                throw new BusinessException("过滤条件【结束日期】必录！");
            }
            if (dtDateTo < dtDateFrom)
            {
                throw new BusinessException("过滤条件【结束日期】不能小于【开始日期】！");
            }
        }

        /// <summary>
        /// 获得当前库存期间信息
        /// </summary>
        /// <param name="dtStart"></param>
        /// <param name="dtEnd"></param>
        protected void TryGetInventoryPeriodDate(out DateTime? dtStart, out DateTime? dtEnd)
        {
            dtStart = (DateTime?)this.CustomFilterObject["fdatefrom"];
            if (dtStart.HasValue)
            {
                dtStart = dtStart.Value.DayBegin();
            }
            dtEnd = (DateTime?)this.CustomFilterObject["fdateto"];
            if (dtEnd.HasValue)
            {
                dtEnd = dtEnd.Value.DayEnd();
            }
        }

        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            this.CheckDataEnvironment();
            this.ProfitListData();
        }

        /// <summary>
        /// 查询数据后往报表对应的临时表中插入数据
        /// </summary>
        protected void ProfitListData()
        {
            DateTime? dtStart;
            DateTime? dtEnd;
            this.TryGetInventoryPeriodDate(out dtStart, out dtEnd);

            if (dtStart.IsNullOrEmptyOrWhiteSpace() || dtEnd.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            var fbrandid = this.CustomFilterObject["fbrandid"] as string;
            var fbrandidArray = fbrandid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            var fproductid = this.CustomFilterObject["fproductid"] as string;
            var fproductidArray = fproductid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            var fmtrlmodel = this.CustomFilterObject["fmtrlmodel"] as string;

            var searchdetail = Convert.ToBoolean(this.CustomFilterObject["fsearchdetail"]);
            //成本取值来源
            var fsourcecost = this.CustomFilterObject["fsourcecost"] as string;

            
            var sqlParams = new List<SqlParam>
            {
              new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
            };

            var whereBuilder = new StringBuilder();
            var filterBuilder = new StringBuilder();
            if (fbrandidArray != null && fbrandidArray.Length > 0)
            {
                builSqlAndAddParams(fbrandidArray, "fbrandid", filterBuilder, sqlParams);
            }

            if (fproductidArray != null && fproductidArray.Length > 0)
            {
                builSqlAndAddParams(fproductidArray, "fproductid", filterBuilder, sqlParams);
            }
            if (false == string.IsNullOrWhiteSpace(fmtrlmodel))
            {
                filterBuilder.AppendFormat("and fmtrlmodel like '%{0}%' ".Fmt(fmtrlmodel));
            }

            //默认查询销售订单
            var mainTableName = "t_ydj_order";
            var entryTableName = "t_ydj_orderentry";
            var forderdate = "forderdate";
            var productid = "fproductid";

            if (fsourcecost.EqualsIgnoreCase("1"))
            {
                mainTableName = "t_ydj_saleintention";
                entryTableName = "t_ydj_saleentry";
                forderdate = "fdate";
                productid = "fmaterialid";
            }

            if (dtStart != null)
            {
                whereBuilder.Append(" and {0} >= @fdatefrom ".Fmt(forderdate));
                sqlParams.Add(new SqlParam("@fdatefrom", DbType.DateTime, dtStart.Value));
            }

            if (dtEnd != null)
            {
                whereBuilder.Append(" and {0} < @fdateto ".Fmt(forderdate));
                sqlParams.Add(new SqlParam("@fdateto", DbType.DateTime, dtEnd.Value));
            }
            
            var stocksql = "isNULL((select sum(fqty) from t_stk_inventorylist where fmaterialid = s.fproductid),0)";
            //查询商品在途量  在途量计算规则：其他入库单（未审核且未作废）实退数量，库存调拨单（未审核且未作废）调拨数量，销售退货单（未审核且未作废）实退数量，采购订单（已审核且未入库） 采购数量-采购入库数量 +采购退换数量
            var orderinsql = @"select sum(fbizqty) as fbizqty from (
                            (select fbizqty,fmaterialid from t_stk_otherstockinentry oc left join t_stk_otherstockin o on o.fid = oc.fid where  o.fstatus <> 'E' and o.fstatus !='C'  )
                            union all
                            (select fbizqty,fmaterialid from  t_stk_invtransferentry ic left join  t_stk_invtransfer i on i.fid = ic.fid where  i.fstatus <> 'E' and i.fstatus !='C' )
                            union all
                            (select fbizqty,fmaterialid from t_stk_sostockreturnentry sc left join t_stk_sostockreturn so on so.fid =sc.fid where so.fstatus <> 'E' and so.fstatus <> 'C')
                            union all
                            (select (fbizqty-fbizinstockqty+fbizreturnqty) as fbizqty,fmaterialid from t_ydj_poorderentry pc left join t_ydj_purchaseorder p on p.fid =pc.fid where p.fstatus = 'E'))q where q.fmaterialid=s.fproductid";
            //先按商品明细去统计 
            var costAmountExpression = $@"select fbrandid,fseriesid,fcategoryid,fproductid,isNULL(fmtrlmodel,'')  as fmtrlmodel,fsalesamount,fsalesqty,(fstocksqty*fsalprice) as fstockamount,fstocksqty,(forderinqty*fsalprice)  as  forderinamount,forderinqty from
                    (select s.fbrandid,s.fseriesid,isNULL(c.fid,'') as fcategoryid,m2.fid as fproductid,m2.fspecifica as fmtrlmodel,
                     fsalesqty, fsalesamount ,{stocksql} as fstocksqty, 
                     m2.fsalprice,
                     isNULL(({orderinsql}),0)  as forderinqty from (
                     select fbrandid,fseriesid,fcategoryid,sum(z.fbizqty) as fsalesqty,sum(z.fdealamount) as fsalesamount,fproductid from (
                     select m.fbrandid,m.fseriesid,m.fcategoryid,a.fbizqty,a.fdealamount,m.fid as fproductid
                     from {entryTableName} a left join {mainTableName} b on a.fid = b.fid
                     left join t_bd_material m on a.{productid} = m.fid
                     where  b.fstatus ='E' and b.fmainorgid =@fmainorgid  {whereBuilder.ToString()})z 
                     group by fbrandid,fseriesid,fcategoryid,fproductid)s 
                     left join ser_ydj_category c on s.fcategoryid = c.fid 
                     left join t_bd_material m2 on m2.fid=s.fproductid)r";
            //
            if (!searchdetail)
            {
                costAmountExpression = $@"select fbrandid,fseriesid,fcategoryid,'' as fproductid,'' as fmtrlmodel,sum(fsalesamount) as fsalesamount,sum(fsalesqty) as fsalesqty,
                                        sum(fstockamount) as fstockamount,sum(fstocksqty) as fstocksqty,sum(forderinamount)
                                        as forderinamount,sum(forderinqty) as forderinqty from ({costAmountExpression})ret group by ret.fbrandid,ret.fseriesid,ret.fcategoryid";
            }
            
            var strSql = $@"
insert into {this.DataSourceTableName}
      (fid,fbrandid,fseriesid,fcategoryid,fproductid,fmtrlmodel,fsalesamount,fsalesqty,fstockamount,fstocksqty,forderinamount,forderinqty)
 (select NEWID() as fid,* from (
{costAmountExpression}
)rr where 1=1 {filterBuilder.ToString()})
";

            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();

            dbServiceExt.Execute(this.Context, strSql, sqlParams);

        }

        private void builSqlAndAddParams(string[] data, string fieldKey, StringBuilder sql, List<SqlParam> sqlParams)
        {
            if (data == null || data.Length <= 0)
            {
                return;
            }

            if (data.Length == 1)
            {
                sqlParams.Add(new SqlParam($"@{fieldKey}", DbType.String, data[0]));
                sql.Append($" and {fieldKey} = @{fieldKey} ");
                return;
            }

            sqlParams.AddRange(data.Select((x, i) => new SqlParam($"@{fieldKey}{i}", DbType.String, x)));
            sql.Append($" and {fieldKey} in ( ");
            sql.Append(string.Join(",", data.Select((x, i) => $"@{fieldKey}{i}")));
            sql.Append(") ");
        }
    }
}
