using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Data;
using JieNor.Framework.CustomException;
using Newtonsoft.Json;
using JieNor.Framework.MetaCore.FormOp;
using System.Linq;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Core.Interface;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Report
{
    /// <summary>
    /// 资金账户（经销商）
    /// </summary>
    [InjectService("CustomerCapitalChart")]
    [FormId("sal_dealeraccount")]
    public class CustomerCapitalChart : BaseCapitalChart
    {
        protected override IDataReader BuildQueryData(out ListDesc desc)
        {
            int pageIndex = 1;
            int pageSize = 10;
            int.TryParse(this.GetQueryOrSimpleParam<string>("pageIndex"), out pageIndex);
            int.TryParse(this.GetQueryOrSimpleParam<string>("pageSize"), out pageSize);

            string requestJsonStr = this.GetQueryOrSimpleParam<string>("params");
            JObject JParams = null;

            if (string.IsNullOrWhiteSpace(requestJsonStr) == false)
            {
                JParams = JObject.Parse(requestJsonStr);
            }

            string dealernameFilter = "";
            if (JParams != null)
            {
                var customerid = Convert.ToString(JParams["fcustomerid"]);
                if (!customerid.IsNullOrEmptyOrWhiteSpace())
                {
                    dealernameFilter = string.Format(" and b.fid='{0}' ", customerid);
                }
            }

            #region 参数处理
            if (pageIndex < 1) pageIndex = 1;
            if (pageSize < 1) pageSize = 10;
            string dealerid = this.GetQueryOrSimpleParam<string>("fdealerid");// 经销商id
            if (!dealerid.IsNullOrEmptyOrWhiteSpace())
                dealerid = dealerid.Replace(";", "").Replace("'", "").Replace("or ", "").Replace("--", "").Replace(" ", "");

            var synAccountBalanceService = this.Container.GetService<ISynAccountBalanceService>();
            var accounts = synAccountBalanceService.GetAllAccount(this.UserCtx);
            desc = new ListDesc();
            desc.CurrentRows = 0;

            if (accounts == null || accounts.Any() == false)
            {
                return null;
            }

            string Sql = string.Format(@"
            select fdealerid,{3}
            fdealername,FJNIdentity,{4}
            ' ' as foperate from 
            (
                select b.fid as fdealerid,{5}b.fname as fdealername,ROW_NUMBER() OVER(Order By b.fid) AS FJNIdentity from t_ydj_customer b 
                where b.fmainorgid='{0}' {1} {6}
            ) X where FJNIdentity > {2} and FJNIdentity <= {7}",
            this.UserCtx.Company,
            string.IsNullOrWhiteSpace(dealerid) == true ? "" : string.Format("and b.fid='{0}'", dealerid),
            string.IsNullOrWhiteSpace(dealerid) == false ? 0 : pageSize * (pageIndex - 1),
            string.Join(string.Empty, accounts.Select((x, i) => string.Format(string.Concat(ACCOUNTKEYFORMAT, ","), i))),
            string.Format("({0}) as fsum,", string.Join("+", accounts.Select((x, i) => string.Format(ACCOUNTKEYFORMAT, i)))),
            string.Join(string.Empty, accounts.Select((x, i) => string.Format("isnull((select fbalance_e from t_ydj_CustomerAccount where fid=b.fid and fpurpose='{0}'),0) as {1},", x.AccountId, string.Format(ACCOUNTKEYFORMAT, i))))
            , dealernameFilter, pageSize * pageIndex );
            #endregion

            var dbSvc = this.UserCtx.Container.GetService<IDBService>();
            //if (dealerid.IsNullOrEmptyOrWhiteSpace())
            //{
            //    using (var reader = dbSvc.ExecuteReader(this.UserCtx, $"select count(1) from(select a.fid from t_ydj_CustomerAccount as a inner join t_ydj_customer as b on a.fid=b.fid where b.fmainorgid='{this.UserCtx.Company}' and b.fcoostate=N'已协同' GROUP BY a.fid)X"))
            //    {
            //        while (reader.Read())
            //        {
            //            desc.CurrentRows = Convert.ToInt64(reader[0]);
            //            break;
            //        }
            //    }
            //}
            if (dealerid.IsNullOrEmptyOrWhiteSpace())
            {
                using (var reader = this.DBService.ExecuteReader(this.UserCtx, $"select count(1) from(select a.fid from t_ydj_CustomerAccount as a inner join t_ydj_customer as b on a.fid=b.fid where b.fmainorgid='{this.UserCtx.Company}' " + dealernameFilter + " GROUP BY a.fid)X"))
                {
                    if (reader.Read())
                    {
                        desc.CurrentRows = Convert.ToInt64(reader[0]);
                    }
                }
            }
            desc.PageCount = pageSize; //每页条数
            desc.Bills = desc.CurrentRows;//单据数
            desc.Rows = desc.CurrentRows;//总记录数
            return dbSvc.ExecuteReader(this.UserCtx, Sql);
        }
    }

    /// <summary>
    /// 账户明细（经销商）
    /// </summary>
    //[InjectService("CustomerCapitalDetailChart")]
    //[FormId("sal_dealerdetail")]
    public class CustomerCapitalDetailChart : OfficeRptDataService
    {
        private string Company
        {
            get
            {
                return this.UserCtx.Company;
            }
        }
        private string Customerid
        {
            get { return this.GetQueryOrSimpleParam<string>("fdealerid"); }
        }
        private string AccountType
        {
            get { return GetAccountType(); }
        }
        private string Coocompanyid
        {
            get { return GetCooObjId(this.UserCtx, Customerid); }
        }
        private Tuple<string, string> DateTimeFormat()
        {
            int datetype = this.GetQueryOrSimpleParam<int>("date");
            string StartDate = this.GetQueryOrSimpleParam<string>("startdate");
            string EndDate = this.GetQueryOrSimpleParam<string>("enddate");
            DateTime DN = DateTime.Now;
            DateTime Ds = new DateTime();
            DateTime De = new DateTime();
            if (StartDate.IsNullOrEmptyOrWhiteSpace() || EndDate.IsNullOrEmptyOrWhiteSpace())
            {
                switch (datetype)
                {
                    case 1://本月
                        Ds = new DateTime(DN.Year, DN.Month, 1);
                        De = Ds.AddMonths(1);
                        break;
                    case 2://上月
                        Ds = new DateTime(DN.Year, DN.Month, 1).AddMonths(-1);
                        De = new DateTime(DN.Year, DN.Month, 1);
                        break;
                    case 3://本年
                        Ds = new DateTime(DN.Year, 1, 1);
                        De = new DateTime(DN.Year, 1, 1).AddYears(1);
                        break;
                    case 4://上年
                        Ds = new DateTime(DN.Year, 1, 1).AddYears(-1);
                        De = new DateTime(DN.Year, 1, 1);
                        break;
                    default:
                        break;
                }
            }
            if (Ds == new DateTime())
            {
                if (!DateTime.TryParse(StartDate, out Ds) || !DateTime.TryParse(EndDate, out De))
                {
                    throw new BusinessException("时间格式有误");
                }
                else
                    if (Ds > De)
                {
                    throw new BusinessException("开始时间不能大于结束时间");
                }
            }
            return new Tuple<string, string>(Ds.ToString("yyyy-MM-dd HH:mm:ss"), De.AddDays(1).ToString("yyyy-MM-dd HH:mm:ss"));
        }
        protected override IDataReader BuildQueryData(out ListDesc desc)
        {
            desc = new ListDesc();
            if (Customerid.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("参数[fdealerid]为空或NULL");
            }
            string[] accountTypes = new string[] { "all", "settleaccount_type_01", "settleaccount_type_02", "settleaccount_type_03" };
            string ftype = AccountType.IsNullOrEmptyOrWhiteSpace() ? GetAccountType() : AccountType;
            if (ftype.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("参数[ftype]不能为空！");
            }
            else if (accountTypes.Contains(ftype.ToLower()) == false)
            {
                throw new BusinessException("请求参数[ftype]错误！");
            }
            string fcoocompanyid = Coocompanyid.IsNullOrEmptyOrWhiteSpace() ? GetCooObjId(this.UserCtx, Customerid) : Coocompanyid;
            if (ftype.EqualsIgnoreCase("all")) ftype = "";
            //记录条数
            //string Sql = $"select (select count(1) from T_COO_CHARGEMONEY where fbusinessstatus='inpour_status_02' and fmainorgid = '{Company}' and ((fsourceform = 'ydj_customer' and fsourceid = '{Customerid}') or fcoocompanyid='{fcoocompanyid}') and fcreatedate>= '{DateTimeFormat().Item1}' and fcreatedate< '{DateTimeFormat().Item2}')+(select count (1) from T_COO_INPOURMONEY where fbusinessstatus='inpour_status_02' and fmainorgid = '{Company}' and ((fsourceform = 'ydj_customer' and fsourceid = '{Customerid}') or fcoocompanyid='{fcoocompanyid}') and fcreatedate>= '{DateTimeFormat().Item1}' and fcreatedate< '{DateTimeFormat().Item2}')";
            //Sql += $"+(select count(1) from t_ydj_purchasesettle where fmainorgid='{Company}' and fcreatedate>='{DateTimeFormat().Item1}' and fcreatedate<'{DateTimeFormat().Item2}' and (fpurcompanyid='{fcoocompanyid}' or fsalecompanyid='{fcoocompanyid}'))+(select count(1) from t_ydj_purchasesettle where fmainorgid='{Company}' and fcreatedate>='{DateTimeFormat().Item1}' and fcreatedate<'{DateTimeFormat().Item2}' and (fpurcompanyid='{fcoocompanyid}' or fsalecompanyid='{fcoocompanyid}'))";

            string Sql = string.Format(@"select (select count(1) from T_COO_CHARGEMONEY 
where fbusinessstatus='inpour_status_02' and fmainorgid = '{0}' and ((fsourceform = 'ydj_customer' and fsourceid = '{3}') or fcoocompanyid='{4}') 
and fcreatedate>= '{1}' and fcreatedate< '{2}' and fusagetype {5})
+(select count (1) from T_COO_INPOURMONEY where fbusinessstatus='inpour_status_02' and fmainorgid = '{0}' and ((fsourceform = 'ydj_customer' and fsourceid = '{3}') or fcoocompanyid='{4}') 
and fcreatedate>= '{1}' and fcreatedate< '{2}' and fusagetype {5})
 +(select count(1) from t_ydj_pursettleentry where faccounttype {5} and fconfirmstatus='settleconfirm_status_02' and fid in(select fid from t_ydj_purchasesettle where fmainorgid='{0}' and fcreatedate>='{1}' and fcreatedate<'{2}' and (fpurcompanyid='{4}' or fsalecompanyid='{4}')) )
+(select count(1) from t_ydj_salesettleentry where faccounttype {5} and fconfirmstatus='settleconfirm_status_02' and fid in(select fid from t_ydj_salesettle where fmainorgid='{0}' and fcreatedate>='{1}' and fcreatedate<'{2}' and (fpurcompanyid='{4}' or fsalecompanyid='{4}')) )",
Company,
DateTimeFormat().Item1,
DateTimeFormat().Item2,
Customerid,
fcoocompanyid,
ftype.IsNullOrEmptyOrWhiteSpace() ? ">''" : string.Format("='{0}'", ftype));

            using (var reader = this.DBService.ExecuteReader(this.UserCtx, Sql))
            {
                if (reader.Read())
                {
                    desc.CurrentRows = Convert.ToInt64(reader[0]);
                }
            }
            Sql = $"select * from(";
            // 充值、扣款
            Sql += $"select fid,fcreatedate,ftranid,fmoney as finpourmoney,0 as fchargemoney,'协同充值' as fvaluetype,fusagetype as 'ftype',' ' as foperate,0 as fsum,(select top 1 fenumitem from T_BD_ENUMDATAENTRY where fentryid=fusagetype )as  fusagetype  from T_COO_INPOURMONEY where fbusinessstatus='inpour_status_02' and fmainorgid = '{Company}' and ((fsourceform = 'ydj_customer' and fsourceid = '{Customerid}') or fcoocompanyid='{fcoocompanyid}') and fcreatedate>= '{DateTimeFormat().Item1}' and fcreatedate< '{DateTimeFormat().Item2}' ";
            Sql += " union all ";
            Sql += $"select fid,fcreatedate,ftranid,0 as finpourmoney,fmoney as fchargemoney,'协同扣款' as fvaluetype,fusagetype as 'ftype',' ' as foperate,0 as fsum,(select top 1 fenumitem from T_BD_ENUMDATAENTRY where fentryid=fusagetype )as  fusagetype  from T_COO_CHARGEMONEY where fbusinessstatus='inpour_status_02' and fmainorgid = '{Company}' and ((fsourceform = 'ydj_customer' and fsourceid = '{Customerid}') or fcoocompanyid='{fcoocompanyid}') and fcreatedate>= '{DateTimeFormat().Item1}' and fcreatedate< '{DateTimeFormat().Item2}'";
            Sql += " union all ";
            // 销售结算、采购结算
            Sql += $"select psn.fentryid,p.fcreatedate,ftranid,0 as finpourmoney,psn.famount as fchargemoney,'采购结算' as fvaluetype,psn.faccounttype as 'ftype',' ' as foperate,0 as fsum, (select top 1 fenumitem from T_BD_ENUMDATAENTRY where fentryid = psn.faccounttype) as fusagetype from t_ydj_purchasesettle p inner join t_ydj_pursettleentry psn on p.fid = psn.fid where fbilltypeid = 'settlebiz_type_02' and psn.fconfirmstatus='settleconfirm_status_02' and fmainorgid = '{Company}' and(fpurcompanyid = '{fcoocompanyid}' or fsalecompanyid = '{fcoocompanyid}') and p.fcreatedate >= '{DateTimeFormat().Item1}' and p.fcreatedate < '{DateTimeFormat().Item2}'";
            Sql += " union all ";
            Sql += $"select ssn.fentryid,s.fcreatedate,ftranid,0 as finpourmoney,ssn.famount as fchargemoney,'销售结算' as fvaluetype,ssn.faccounttype as 'ftype',' ' as foperate,0 as fsum, (select top 1 fenumitem from T_BD_ENUMDATAENTRY where fentryid = ssn.faccounttype) as fusagetype from t_ydj_salesettle s inner join t_ydj_salesettleentry ssn on s.fid = ssn.fid where fbilltypeid = 'settlebiz_type_02' and ssn.fconfirmstatus='settleconfirm_status_02' and fmainorgid = '{Company}' and(fpurcompanyid = '{fcoocompanyid}' or fsalecompanyid = '{fcoocompanyid}') and s.fcreatedate >= '{DateTimeFormat().Item1}' and s.fcreatedate < '{DateTimeFormat().Item2}'";

            Sql += string.Format(") T {0} order by fcreatedate", ftype.IsNullOrEmptyOrWhiteSpace() ? "" : string.Format("where ftype='{0}'", ftype));
            return this.DBService.ExecuteReader(this.UserCtx, Sql);
        }

        protected override void BuildRptChartDataSource(UserContext userCtx, ReportOperationContext rptOperCtx, ReportDataSource rptDataSource)
        {
            if (rptDataSource != null && rptDataSource.RptGridDataSource.Count() > 0)
            {
                int pageIndex = 1;
                int pageSize = 10;
                int.TryParse(this.GetQueryOrSimpleParam<string>("pageIndex"), out pageIndex);
                int.TryParse(this.GetQueryOrSimpleParam<string>("pageSize"), out pageSize);

                string fcoocompanyid = Coocompanyid.IsNullOrEmptyOrWhiteSpace() ? GetCooObjId(this.UserCtx, Customerid) : Coocompanyid;
                string ftype = AccountType.IsNullOrEmptyOrWhiteSpace() ? GetAccountType() : AccountType;
                if (ftype.EqualsIgnoreCase("all")) ftype = "";
                //先算出当前时间之前的充值和使用金额
                string Sql = "select sum(a) from (";
                Sql += $"select isnull(sum(fmoney),0) as a from T_COO_INPOURMONEY where fbusinessstatus='inpour_status_02' and fusagetype {string.Format("{0} '{1}'", ftype.IsNullOrEmptyOrWhiteSpace() ? ">" : "=", ftype)} and fmainorgid='{Company}' and ((fsourceform = 'ydj_customer' and fsourceid = '{Customerid}') or fcoocompanyid='{fcoocompanyid}') and fcreatedate<'{DateTimeFormat().Item1}' ";
                Sql += " union all ";
                Sql += $"select 0-isnull(sum(fmoney),0) from T_COO_CHARGEMONEY where fbusinessstatus='inpour_status_02' and fusagetype {string.Format("{0} '{1}'", ftype.IsNullOrEmptyOrWhiteSpace() ? ">" : "=", ftype)} and fmainorgid='{Company}' and ((fsourceform = 'ydj_customer' and fsourceid = '{Customerid}') or fcoocompanyid='{fcoocompanyid}') and fcreatedate<'{DateTimeFormat().Item1}' ";

                // 销售结算、采购结算
                Sql += " union all ";
                Sql += $"select 0-isnull(sum(famount),0) from t_ydj_pursettleentry where faccounttype {string.Format("{0} '{1}'", ftype.IsNullOrEmptyOrWhiteSpace() ? ">" : "=", ftype)} and fconfirmstatus='settleconfirm_status_02' and fid in(select fid from t_ydj_purchasesettle where fmainorgid='{Company}' and fcreatedate<'{DateTimeFormat().Item1}' and (fpurcompanyid='{fcoocompanyid}' or fsalecompanyid='{fcoocompanyid}'))  ";
                Sql += " union all ";
                Sql += $"select 0-isnull(sum(famount),0) from t_ydj_salesettleentry where faccounttype {string.Format("{0} '{1}'", ftype.IsNullOrEmptyOrWhiteSpace() ? ">" : "=", ftype)} and fconfirmstatus='settleconfirm_status_02' and fid in(select fid from t_ydj_salesettle where fmainorgid='{Company}' and fcreatedate<'{DateTimeFormat().Item1}' and (fpurcompanyid='{fcoocompanyid}' or fsalecompanyid='{fcoocompanyid}'))  ";


                //Sql += " union all ";
                //Sql += $"select isnull(sum(fbalance_e),0) from t_ydj_customerAccount where fpurpose {string.Format("{0} '{1}'", ftype.IsNullOrEmptyOrWhiteSpace() ? ">" : "=", ftype)} and fid='{Customerid}'";
                Sql += ")T";
                double fristMoney = 0;
                using (var reader = this.DBService.ExecuteReader(this.UserCtx, Sql))
                {
                    if (reader.Read())
                    {
                        fristMoney = Convert.ToDouble(reader[0]);
                    }
                }
                List<Dictionary<string, object>> rptGridData = new List<Dictionary<string, object>>();
                List<Dictionary<string, object>> chartData = rptDataSource.RptGridDataSource.ToList();
                foreach (var item in chartData)
                {
                    if (Convert.ToString(item["fvaluetype"]) == "协同充值")//充值+
                    {
                        fristMoney = fristMoney + Convert.ToDouble(item["finpourmoney"]);
                    }
                    else//扣款-
                    {
                        fristMoney = fristMoney - Convert.ToDouble(item["fchargemoney"]);
                    }
                    item["fsum"] = fristMoney;
                    rptGridData.Add(item);
                }
                if (rptGridData.Count > (pageIndex - 1) * pageSize)
                {
                    rptDataSource.RptGridDataSource = rptGridData.Skip((pageIndex - 1) * pageSize).Take(pageSize).AsEnumerable();
                }
                else
                {
                    rptDataSource.RptGridDataSource = new List<Dictionary<string, object>>();
                }

            }
        }

        private string GetCooObjId(UserContext userCtx, string customerId)
        {
            if (string.IsNullOrWhiteSpace(customerId)) return "";

            var dm = this.GetDataManager();
            var formMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(userCtx, "ydj_customer");
            dm.InitDbContext(userCtx, formMeta.GetDynamicObjectType(userCtx));
            var pkIdReader = userCtx.GetPkIdDataReader(formMeta, "fid=@Id",
                new SqlParam[]
                {
                    new SqlParam("Id", DbType.String,customerId),
                });
            DynamicObject dyObj = dm.SelectBy(pkIdReader).OfType<DynamicObject>().FirstOrDefault();
            if (dyObj == null) return "";
            return Convert.ToString(dyObj["fcoocompanyid"]);
        }

        private string GetAccountType()
        {
            //ftype类型有  所有-all，货款-pay，返利-revert，保证金-deposit
            var account_type = this.GetQueryOrSimpleParam<string>("ftype");
            if (account_type.EqualsIgnoreCase("pay"))
                return "settleaccount_type_01";
            else if (account_type.EqualsIgnoreCase("revert"))
                return "settleaccount_type_02";
            else if (account_type.EqualsIgnoreCase("deposit"))
                return "settleaccount_type_03";
            return account_type;
        }
    }
}
