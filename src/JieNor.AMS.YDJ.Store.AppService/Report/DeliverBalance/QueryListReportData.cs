using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.AMS.YDJ.Store.AppService.MuSi;
using System.Data.Common;

namespace JieNor.AMS.YDJ.Store.AppService.Report.DeliverBalance
{
    /// <summary>
    /// 售达方余额报表
    /// </summary>
    [InjectService]
    [FormId("rpt_deliverbalance")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {
              
        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {  
            this.GetSaleOrderData();
        }
        /// <summary>
        /// 查询数据后往报表对应的临时表中插入数据
        /// </summary>
        private void GetSaleOrderData()
        {


            #region 根据经销商获取其对应组织

            #endregion

            GetAgentBalanceByInvoke();

            StringBuilder sbOrderSql = new StringBuilder();            
            StringBuilder sbInsertSelectSql = new StringBuilder();
            StringBuilder sbOrderSelectSql = new StringBuilder();
            StringBuilder sbOrderFromSql = new StringBuilder();
            StringBuilder sbOrderWhereSql = new StringBuilder();
            StringBuilder sbSelectSql = new StringBuilder();

            DateTime? dtStart;
            DateTime? dtEnd;
            DateTime? dtStart_delivery;
            DateTime? dtEnd_delivery;
             
        }

        /// <summary>
        /// 根据经销商获取公司编码
        /// </summary>
        /// <param name="agent"></param>
        /// <returns></returns>
        private Dictionary<string, string> GetCompanyDic(string[] agent) 
        {
            Dictionary<string, string> Dic;
            string company = string.Empty;
            var sql =$@"select forganizationid,fcompany from t_ydj_orgcompanyentry where forganizationid in  ('{string.Join("','", agent)}')";
            var data = this.DBService.ExecuteDynamicObject(this.Context, sql)?.ToDictionary(k=>Convert.ToString(k["forganizationid"]),v=>Convert.ToString(v["fcompany"]));
            return data;
        }

        //根据经销商信息获取其商品授权中的系列
        private Dictionary<string, Dictionary<string, string>> GetAgentAndCompanyData(string[] agent) 
        {
            var sql = $@"SELECT distinct ag.fid as fagentid,fcompany,fcompanyname FROM dbo.T_BAS_AGENT ag
                        INNER JOIN t_ms_saleschannel_org s ON s.fcustomernumber = ag.fnumber AND s.fforbidstatus ='0'
                        INNER JOIN dbo.T_BAS_ORGANIZATION org ON org.fnumber =s.forgnumber and org.forgtype in ('1','2') and org.fforbidstatus = 0
                        inner join t_ydj_orgcompanyentry on org.fid = t_ydj_orgcompanyentry.forganizationid
                        WHERE ag.fid in ('{string.Join("','", agent)}') and (fchannelnumber ='20' OR fchannelnumber ='10' OR fchannelnumber ='50')";
 
            var data = this.DBService.ExecuteDynamicObject(this.Context, sql).ToList();
            var Dic_AgentAndCompany = new Dictionary<string, Dictionary<string, string>>();

            var firsts = data.GroupBy(o => o["fagentid"] as string).ToList();
            foreach (var first in firsts)
            {
                var list = first.ToList();
                var DIC = new Dictionary<string, string> { };
                foreach (var item in list)
                {
                    DIC.Add(Convert.ToString(item["fcompany"]), Convert.ToString(item["fcompanyname"]));
                }
                Dic_AgentAndCompany.Add(first.Key, DIC);
            }  
            return Dic_AgentAndCompany;

            //DICA：通过经销商找到对应的系列配置（因为系列可以配置多组所以拆开成字典了）结果如下 
            //{"agentA","seriesA"}
            //{"agentA","seriesB"}
            //{"agentB","seriesA"}

            //DICB：通过系列在 业绩品牌与销售组织（只有系列和组织的关系）中找到组织 再通过组织在 销售组织与公司（只有组织和公司的关系）中找到对应的公司 结果如下
            //{"seriesA":"orgA"}
            //{"seriesA":"orgB"}
            //{"seriesA":"orgC"}
            //{"seriesB":"orgA"}

            //怎么把DICA和DICB合并起来 最后转化成一个新的DICC 也就是经销商和组织的关系？
            //{"agentA","orgA"}
            //{"agentA","orgB"}
            //{"agentA","orgC"}
            //{"agentB","orgA"}
            //{"agentB","orgB"}
            //{"agentB","orgC"}
        }

        /// <summary>
        /// 调用中台接口获取经销商余额数据
        /// </summary>
        private void GetAgentBalanceByInvoke()
        {
            List<string> errMsg = new List<string>();
            //获取父页面传过来的经销商信息
            var agents = this.ParentPageSession?.Agent as string;
            //如果选择多个经销商传过来则请求多次接口，接口不支持批量传入经销商，只能一个个返回经销商余额然后做拼接
            string[] agentLst = agents.Split(',');
            //var CompanyNumDic = GetCompanyDic(agentLst);
            var CompanyNumDic = GetAgentAndCompanyData(agentLst);
            foreach (var agent in agentLst) {
                // to do  首先会先获取所有勾选行的经销商, 根据该经销商找到对应《商品授权清单》的授权组织, 获取对应所授权的系列匹配《销售组织与业绩品牌关系》找到所有对应的【销售组织】, 再用【销售组织】匹配《销售组织与销售公司关系》找到对应 销售公司的【公司编号】
                var CompanyNumLst =new List<string>();
                var CompanyLst = new Dictionary<string,string>();
                //根据经销商取出对应公司编码
                CompanyNumDic.TryGetValue(agent,out CompanyLst);
                if (CompanyLst == null || CompanyLst.Count == 0 ) continue;
                var agentNum = this.Context.LoadBizDataById("bas_agent", agent)?["fnumber"].ToString();
                foreach (var Company in CompanyLst) {
                    var CompanyNum = Company.Key.ToString();
                    var CompanyName = Company.Value.ToString();
                    var resp = MuSiApi.GetAgentBalance(this.Context, this.HtmlForm, agentNum, CompanyNum);
                    if (resp.Code == 200)
                    {
                        string resultdata = Convert.ToString(resp.data);
                        if (!resultdata.IsNullOrEmptyOrWhiteSpace())
                        {
                            JObject obj = JObject.Parse(resultdata);
                            var item = obj["item"];
                            var bALANCE = item.GetJsonValue("bALANCE", "");
                            var bALANCEDJ = item.GetJsonValue("bALANCEDJ", "");
                            var bALANCEGD = item.GetJsonValue("bALANCEGD", "");
                            //string item = obj.GetJsonValue("item","");
                            //返回余额数据先存在datatable 中
                            var fieldKeys = new string[]
                            {
                            "bALANCE","bALANCEDJ","bALANCEGD"
                            };
                            var dataTable = new DataTable();
                            foreach (var fieldKey in fieldKeys)
                            {
                                dataTable.Columns.Add(fieldKey);
                            }
                            dataTable.BeginLoadData();
                            dataTable.LoadDataRow(new object[] { bALANCE, bALANCEDJ, bALANCEGD }, true);
                            dataTable.EndLoadData();

                            if (dataTable.Rows.Count > 0)
                            {
                                using (var trans = this.Context.CreateTransaction())
                                {
                                    //将数据存到临时表中
                                    var tempTable = this.DBService.CreateTempTableWithDataTable(this.Context, dataTable, 2000,null,null,false);
                                    var sql = $@"insert into {this.DataSourceTableName}
                                                      (fid,fagent,fcompany,fdatetime,famount,famountfrezee)
                                                    select newid(),{agent},'{CompanyName}',getdate(),bALANCE,bALANCEDJ from {tempTable}";
                                    this.DBService.ExecuteDynamicObject(this.Context, sql);
                                    this.DBService.DeleteTempTableByName(this.Context, tempTable, true);

                                    trans.Complete();
                                }
                            }
                        }
                    }
                    else
                    {
                        var errinfo = $"请求 售达方余额(经销商)查询 失败，失败原因：" + resp.Msg;
                        errMsg.Add(errinfo);
                    }
                }
            }
            if (errMsg.Count > 0)
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = string.Join("", errMsg);
                return;
            }
        }
    }
}
