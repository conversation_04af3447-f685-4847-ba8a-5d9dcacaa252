using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.Framework.MetaCore.FormModel.List;

namespace JieNor.AMS.YDJ.Store.AppService.Report.OrderBalance
{
    /// <summary>
    /// 销售合同明细报表
    /// </summary>
    [InjectService]
    [FormId("rpt_orderbalance")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {


        /// <summary>
        /// 检查当前过滤界面必须录入的信息 
        /// </summary>
        protected void CheckDataEnvironment()
        {

            DateTime? dtDateFrom = (DateTime?)this.CustomFilterObject["fdatefrom"];

            DateTime? dtDateTo = (DateTime?)this.CustomFilterObject["fdateto"];

            if (dtDateTo < dtDateFrom)
            {
                throw new BusinessException("过滤条件【截止日期不能早于开始日期】！");
            }
            if ((dtDateFrom == null && dtDateTo != null) || (dtDateFrom != null && dtDateTo == null))
            {
                throw new BusinessException("过滤条件【起始日期必须填写完整】！");
            }

            DateTime? dtDateFrom_delivery = (DateTime?)this.CustomFilterObject["fdatefrom_delivery"];

            DateTime? dtDateTo_delivery = (DateTime?)this.CustomFilterObject["fdateto_delivery"];

            if (dtDateTo_delivery < dtDateFrom_delivery)
            {
                throw new BusinessException("过滤条件【截止日期不能早于开始日期】！");
            }
            if ((dtDateFrom_delivery == null && dtDateTo_delivery != null) || (dtDateFrom_delivery != null && dtDateTo_delivery == null))
            {
                throw new BusinessException("过滤条件【起始日期必须填写完整】！");
            }

        }

        /// <summary>
        /// 获得当前库存期间信息
        /// </summary>
        /// <param name="dtStart"></param>
        /// <param name="dtEnd"></param>
        protected void TryGetInventoryPeriodDate(out DateTime? dtStart, out DateTime? dtEnd, out DateTime? dtStart_delivery, out DateTime? dtEnd_delivery)
        {
            dtStart = (DateTime?)this.CustomFilterObject["fdatefrom"];
            if (dtStart.HasValue)
            {
                dtStart = dtStart.Value.DayBegin();
            }
            dtEnd = (DateTime?)this.CustomFilterObject["fdateto"];
            if (dtEnd.HasValue)
            {
                dtEnd = dtEnd.Value.DayEnd();
            }
            dtStart_delivery = (DateTime?)this.CustomFilterObject["fdatefrom_delivery"];
            if (dtStart_delivery.HasValue)
            {
                dtStart_delivery = dtStart_delivery.Value.DayBegin();
            }
            dtEnd_delivery = (DateTime?)this.CustomFilterObject["fdateto_delivery"];
            if (dtEnd_delivery.HasValue)
            {
                dtEnd_delivery = dtEnd_delivery.Value.DayEnd();
            }
        }
        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            this.CheckDataEnvironment();

            DebugUtil.WriteLogToFile($"销售合同明细报表开始：{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} ms", "rpt_orderbalance");

            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            this.GetSaleOrderData();

            stopwatch.Stop();

            DebugUtil.WriteLogToFile($"销售合同明细报表耗时：{stopwatch.ElapsedMilliseconds} ms", "rpt_orderbalance");

        }

        //protected override void OnAfterListData(List<Dictionary<string, object>> listData)
        //{
        //    base.OnAfterListData(listData);

        //    var gp = listData.GroupBy(a => a["fbillno"]).ToList();
        //    gp.ForEach(a =>
        //    {
        //        var _orders = listData.Where(_ => Convert.ToString(_["fbillno"]).Equals(a.Key)).ToList();
        //        if (_orders.Count() > 1)
        //        {
        //            _orders.Where(b => !Convert.ToString(b["fbillhead_id"]).Equals(Convert.ToString(_orders.FirstOrDefault()["fbillhead_id"]))).ToList().ForEach(c =>
        //            {
        //                c["factrefundamount"] = 0;
        //                c["fdealsumamount"] = 0;

        //                c["fdistsumamount"] = 0;//折扣总额
        //                c["freceivable"] = 0;//确认收款总额
        //                c["ffaceamount"] = 0;//折扣前总额
        //            });
        //        }
        //    });
        //}


        protected override void OnPrepareReportQueryParameter(SqlBuilderParameter listQueryPara)
        {
            base.OnPrepareReportQueryParameter(listQueryPara);
            //listQueryPara.OrderByString = "order by forderdate,fsointerid,fsoentryid,fdatatype";
            listQueryPara.OrderByString = "order by fjnidentityid";
        }

        /// <summary>
        /// 查询数据后往报表对应的临时表中插入数据
        /// </summary>
        private void GetSaleOrderData()
        {

            StringBuilder sbOrderSql = new StringBuilder();
            StringBuilder sbInsertSelectSql = new StringBuilder();
            StringBuilder sbOrderSelectSql = new StringBuilder();
            StringBuilder sbOrderFromSql = new StringBuilder();
            StringBuilder sbOrderWhereSql = new StringBuilder();
            StringBuilder sbSelectSql = new StringBuilder();
            StringBuilder sbGroupSql = new StringBuilder();

            DateTime? dtStart;
            DateTime? dtEnd;
            DateTime? dtStart_delivery;
            DateTime? dtEnd_delivery;
            this.TryGetInventoryPeriodDate(out dtStart, out dtEnd, out dtStart_delivery, out dtEnd_delivery);

            var sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, this.Context.Company)
            };

            if (this.SimpleData.TryGetValue("__customFilter__", out var __customFilter__))
            {
                if (__customFilter__.IsNullOrEmptyOrWhiteSpace())
                {
                    dtStart = DateTime.Now.AddMonths(-3).DayBegin();
                    dtEnd = DateTime.Now.DayEnd();
                }
            }

            //if ((dtStart == null || dtEnd == null) && (dtStart_delivery == null || dtEnd_delivery == null))
            //{
            //    throw new Exception("创建时间或合同交货日期至少一个时间段有值！");
            //}

            //如果创建日期不为空
            if (dtStart != null && dtEnd != null)
            {
                //if ((dtEnd - dtStart).Value.TotalDays > 365)
                //{
                //    throw new Exception("创建时间范围不能超过1年！");
                //}

                sqlParams.Add(new SqlParam("@fdatefrom", DbType.DateTime, dtStart));
                sqlParams.Add(new SqlParam("@fdateto", DbType.DateTime, dtEnd));
                sbOrderWhereSql.Append(" and t.fcreatedate between @fdatefrom  and @fdateto ");
            }

            if (dtStart_delivery != null && dtEnd_delivery != null)
            {
                //if ((dtEnd_delivery - dtStart_delivery).Value.TotalDays > 365)
                //{
                //    throw new Exception("合同交货日期范围不能超过1年！");
                //}

                sqlParams.Add(new SqlParam("@fdatefrom_delivery", DbType.DateTime, dtStart_delivery));
                sqlParams.Add(new SqlParam("@fdateto_delivery", DbType.DateTime, dtEnd_delivery));
                sbOrderWhereSql.Append(" and t.fdeliverydate between @fdatefrom_delivery  and @fdateto_delivery ");
            }

            ////获取总部经销商ID 
            //string topCompanyId = this.Context.TopCompanyId;

            //客户 多个
            var fcustomerid = this.CustomFilterObject["fcustomerid"] as string;
            var fcustomeridArray = fcustomerid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (fcustomeridArray != null && fcustomeridArray.Length > 0)
            {
                sqlParams.AddRange(fcustomeridArray.Select((x, i) => new SqlParam($"@fcustomerid{i}", System.Data.DbType.String, x)));
                sbOrderWhereSql.AppendFormat(" and t.fcustomerid in ({0}) ", string.Join(",", fcustomeridArray.Select((x, i) => $"@fcustomerid{i}")));
            }

            //商品 多个
            var fproductid = this.CustomFilterObject["fproductid"] as string;
            var fproductidArray = fproductid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (fproductidArray != null && fproductidArray.Length > 0)
            {
                sqlParams.AddRange(fproductidArray.Select((x, i) => new SqlParam($"@fproductid{i}", System.Data.DbType.String, x)));
                sbOrderWhereSql.AppendFormat(" and te.fproductid in ({0}) ", string.Join(",", fproductidArray.Select((x, i) => $"@fproductid{i}")));
            }

            //合同编号 模糊查询
            var fbillno = this.CustomFilterObject["fbillno"] as string;
            if (!fbillno.IsNullOrEmptyOrWhiteSpace())
            {
                sqlParams.Add(new SqlParam("@fbillno", DbType.String, $"%{fbillno}%"));
                sbOrderWhereSql.Append(" and t.fbillno like @fbillno ");
            }

            //手工单号编号 模糊查询
            var fwithin = this.CustomFilterObject["fwithin"] as string;
            if (!fwithin.IsNullOrEmptyOrWhiteSpace())
            {
                sqlParams.Add(new SqlParam("@fwithin", DbType.String, $"%{fwithin}%"));
                sbOrderWhereSql.Append(" and t.fwithin like @fwithin ");
            }

            //创建人 多个
            var fcreatorid = this.CustomFilterObject["fcreatorid"] as string;
            var fcreatoridArray = fcreatorid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (fcreatoridArray != null && fcreatoridArray.Length > 0)
            {
                sqlParams.AddRange(fcreatoridArray.Select((x, i) => new SqlParam($"@fcreatorid{i}", System.Data.DbType.String, x)));
                sbOrderWhereSql.AppendFormat(" and t.fcreatorid in ({0}) ", string.Join(",", fcreatoridArray.Select((x, i) => $"@fcreatorid{i}")));
            }

            //业绩品牌 多个
            var fresultbrandid = this.CustomFilterObject["fresultbrandid"] as string;
            var fresultbrandidArray = fresultbrandid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (fresultbrandidArray != null && fresultbrandidArray.Length > 0)
            {
                sqlParams.AddRange(fresultbrandidArray.Select((x, i) => new SqlParam($"@fresultbrandid{i}", System.Data.DbType.String, x)));
                sbOrderWhereSql.AppendFormat(" and te.fresultbrandid in ({0}) ", string.Join(",", fresultbrandidArray.Select((x, i) => $"@fresultbrandid{i}")));
            }

            //门店 多个
            var fstoreid = this.CustomFilterObject["fstoreid"] as string;
            var fstoreidArray = fstoreid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (fstoreidArray != null && fstoreidArray.Length > 0)
            {
                sqlParams.AddRange(fstoreidArray.Select((x, i) => new SqlParam($"@fstoreid{i}", System.Data.DbType.String, x)));
                sbOrderWhereSql.AppendFormat(" and t.fstore in ({0}) ", string.Join(",", fstoreidArray.Select((x, i) => $"@fstoreid{i}")));
            }

            //显示合同流程状态
            var flinkpro = this.CustomFilterObject["flinkpro"] as string;
            if (!flinkpro.IsNullOrEmptyOrWhiteSpace())
            {
                sqlParams.Add(new SqlParam("@flinkpro", DbType.String, flinkpro));
                sbOrderWhereSql.Append(" and te.flinkpro = @flinkpro ");
            }

            //销售员 多个
            var assistant = this.CustomFilterObject["assistant"] as string;
            var assistantArray = assistant?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            List<string> assistantList = new List<string>();
            if (assistantArray != null && assistantArray.Length > 0)
            {
                sqlParams.AddRange(assistantArray.Select((x, i) => new SqlParam($"@fstaffid{i}", System.Data.DbType.String, x)));
                sbOrderWhereSql.AppendFormat(" and exists(select 1 from t_ydj_orderduty td with(nolock) where t.fid=td.fid and td.fdutyid in ({0}) )", string.Join(",", assistantArray.Select((x, i) => $"@fstaffid{i}")));
            }

            /*
             * 1.先做基础表
             * 2.多线程更新相应字段
             * 3.部分字段做数据冗余处理
             */

            var stopWatch = new Stopwatch();
            stopWatch.Start();

            string msg = $"做基础表，开始时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}";

            #region 第1步，先做基础表
            sbSelectSql.Append($@"
select newid(), row_number() over(order by t.fbillno, te.fseq) frow, t.fid, te.fentryid, t.fbillno, te.FSeq, t.fbilltype, te.flinkpro, t.fcreatedate, t.forderdate, t.fdeliverydate, t.fstore, t.fcustomerid, t.fphone, t.faddress
	, te.fproductid, te.fattrinfo_e, te.fcustomdes_e, te.fsuitdescription, te.fresultbrandid, te.funstdtype, te.fisgiveaway, te.fbizqty
	, te.fbizunitid, te.fbizoutqty, te.fprice, te.fdealprice, te.fdistrate, te.fdistamount, te.famount, te.fdealamount, te.fbizpurqty, t.fclosedate, t.fwithin, t.fmallorderno
	, t.fdescription, t.fcreatorid, t.funreceived, t.fdealamount as fdealsumamount, t.fstatus as orderstatus, t.freceivable, t.factrefundamount, t.ffaceamount, t.fdistamount as fdistsumamount
	, te.fclosestatus, t.fneedtransferorder, te.fcostprice, te.fcost, te.fdealprice as foutprice, te.fdealprice * (te.fbizoutqty-te.fbizreturnqty) as foutamount
	, t.fstaffid, t.fdeptid, te.fterprice, te.fteramount, te.fbiztype, te.fbizreserveqty, te.funitid
    , (Case when te.fbizoutqty<=0 then DATEDIFF(DAY,t.fdeliverydate,GETDATE()) else 0 end) as foutday
    , te.findbqty, te.fbizpurinqty, te.fbizpurqty-te.fbizpurinqty
    , ISNULL(po.fbillno, '') as fpoorderno, ISNULL(pe.fseq, 0) as fpoorderentryseq, po.fcreatedate as fpurcreatedate, po.fdeliveryplandate, ISNULL(po.fhqderstatus, ''), ISNULL(po.fhqsyncdesc, '')
from T_YDJ_ORDER t with(nolock)
inner join T_YDJ_ORDERENTRY te with(nolock) on t.fid=te.fid
left join T_YDJ_POORDERENTRY pe with(nolock) on te.fentryid=pe.fsoorderentryid
left join T_YDJ_PURCHASEORDER po with(nolock) on po.fid=pe.fid 
where t.fmainorgid=@fmainorgid and t.fcancelstatus='0' and (po.fcancelstatus is null or po.fcancelstatus='0')
");

            var strSql = $@"/*dialect*/ 
insert into {this.DataSourceTableName} 
(fid, fjnidentityid, forderinterid, forderentryid, fbillno, FSeq, fbilltype, flinkpro, fcreatedate, forderdate, fdeliverydate, fstore, fcustomerid, fphone, faddress
	, fproductid, fattrinfo_e, fcustomdes_e, fsuitdescription, fresultbrandid, funstdtype, fisgiveaway, fbizqty
	, fbizunitid, fbizoutqty, fprice, fdealprice, fdistrate, fdistamount, famount, fdealamount, fbizpurqty, fclosedate, fwithin, fmallorderno
	, fdescription, fcreatorid, funreceived, fdealsumamount, forderstatus, freceivable, factrefundamount, ffaceamount, fdistsumamount
	, fclosestatus, fneedtransferorder, fcostprice, fcost, foutprice, foutamount, fstaffid, fdeptid, fterprice, fteramount, fbiztype, fbizreserveqty, funitid, foutday, findbqty, fbizpurinqty, fbizunreturnqty
    , fpoorderno, fpoorderentryseq, fpurcreatedate, fdeliveryplandate, fhqderstatus, fhqsyncdesc)
{sbSelectSql.ToString()}
{sbOrderWhereSql.ToString()}
                            ";
            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();

            ////在此之前手动创建索引，避免后面循环自动创建索引
            //var idxName = "idx_" + this.DataSourceTableName;
            ////判断this.DataSourceTableName是否有主键索引，有索引则不创建索引
            //var indexSql = @"/*dialect*/ IF NOT EXISTS(SELECT 1 FROM sysindexes with(nolock) WHERE  id=object_id('{1}') and [name]='{0}' ) 
            //                                            create index {0} on {1}(fid) ;
            //                                                        ".Fmt(idxName, this.DataSourceTableName);
            //dbServiceExt.Execute(this.Context, indexSql);

            dbServiceExt.Execute(this.Context, strSql, sqlParams);

            var indexSql = @"/*dialect*/
IF NOT EXISTS(SELECT 1 FROM sysindexes with(nolock) WHERE  id=object_id('{0}') and [name]='idx_{0}_forderinterid ' ) 
begin
create index idx_{0}_forderinterid on {0} (forderinterid);
end

IF NOT EXISTS(SELECT 1 FROM sysindexes with(nolock) WHERE  id=object_id('{0}') and [name]='idx_{0}_forderentryid' ) 
begin
create index idx_{0}_forderentryid on {0} (forderentryid);
end
                                                                    ".Fmt(this.DataSourceTableName);
            dbServiceExt.Execute(this.Context, indexSql);

            stopWatch.Stop();

            msg += $"，耗时：{stopWatch.ElapsedMilliseconds} ms";
            msg += Environment.NewLine + "执行SQL：" + strSql;
            DebugUtil.WriteLogToFile(msg, "rpt_orderbalance");

            #endregion

            //UpdatePurchaseOrderData();
            UpdateOrderData();
            UpdateSoStockData();
            UpdateReserveData();
            UpdatePriceData();


            //Parallel.ForEach(s=> )
        }

        private void UpdatePurchaseOrderData()
        {
            /*
             * fporowno：采购订单编码+行项目号
             * fpurcreatedate：采购订单创建时间合并
             * fdeliveryplandate：采购订单日期合并
             * fhqderstatus：采购订单合并
             */
            var stopWatch = new Stopwatch();
            stopWatch.Start();

            string msg = $"UpdatePurchaseOrderDat，开始时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}";


            var strSql = $@"/*dialect*/
update t set 
fporowno=ISNULL(STUFF((
	SELECT ','+fbillno+'-'+cast(pomx.fseq as varchar(10)) 
	FROM T_YDJ_POORDERENTRY pomx with(nolock) 
	INNER JOIN T_YDJ_PURCHASEORDER po with(nolock) ON po.fid = pomx.fid 
	WHERE pomx.fsourceentryid=t.fid 
FOR XML PATH('')),1,1,''), ''),
fhqsyncdesc=ISNULL(STUFF((
	SELECT ',' + po.fhqsyncdesc
	FROM T_YDJ_POORDERENTRY pomx WITH (NOLOCK) 
	INNER JOIN T_YDJ_PURCHASEORDER po WITH (NOLOCK) ON po.fid = pomx.fid 
	WHERE pomx.fsourceentryid=t.fid AND po.fhqsyncdesc!='' 
FOR XML PATH('')),1,1,''), ''),
fpurcreatedate=ISNULL(STUFF((
	SELECT ','+ISNULL(CONVERT(varchar(100),po.fcreatedate, 23),'') 
	FROM T_YDJ_POORDERENTRY pomx with(nolock) 
	INNER JOIN T_YDJ_PURCHASEORDER po with(nolock) ON po.fid = pomx.fid 
	WHERE pomx.fsourceentryid=t.fid
FOR XML PATH('')),1,1,'' ), ''),
fhqderstatus=ISNULL(STUFF((
	SELECT ','+(CASE po.fhqderstatus WHEN '01' THEN '新建' WHEN '02' THEN '提交至总部' WHEN '03' THEN '已终审' WHEN '04' THEN '排产中' WHEN '05' THEN '驳回' ELSE '' END) 
	FROM T_YDJ_POORDERENTRY pomx with(nolock) 
	INNER JOIN T_YDJ_PURCHASEORDER po with(nolock) ON po.fid = pomx.fid 
	WHERE pomx.fsourceentryid=t.fid AND po.fhqderstatus !='' 
FOR XML PATH('')),1,1,'' ), ''),
fdeliveryplandate=ISNULL(STUFF((
	SELECT ','+ISNULL(CONVERT(varchar(100),po.fdeliveryplandate, 23),'') 
	FROM T_YDJ_POORDERENTRY pomx with(nolock) 
	INNER JOIN T_YDJ_PURCHASEORDER po with(nolock) ON po.fid = pomx.fid 
	WHERE pomx.fsourceentryid=t.fid
FOR XML PATH('')),1,1,'' ), '')
from {this.DataSourceTableName} t;

;
            ";


            this.DBService.ExecuteDynamicObject(this.Context, strSql);

            stopWatch.Stop();

            msg += $"，耗时：{stopWatch.ElapsedMilliseconds} ms";
            msg += Environment.NewLine + "执行SQL：" + strSql;
            DebugUtil.WriteLogToFile(msg, "rpt_orderbalance");

        }

        private void UpdateOrderData()
        {
            //fassistant：联合开单销售员
            //fassistantdep：联合开单销售部门

            var stopWatch = new Stopwatch();
            stopWatch.Start();

            string msg = $"UpdateOrderData，开始时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}";

            string strSql = $@"/*dialect*/
update t set fassistant=td.fstaffrationames, fassistantdep=td.fstaffdeptnames
from {this.DataSourceTableName} t
inner join T_YDJ_ORDERDUTY_EXT td with(nolock) on t.forderinterid=td.fid
";

            this.DBService.ExecuteDynamicObject(this.Context, strSql);

            stopWatch.Stop();

            msg += $"，耗时：{stopWatch.ElapsedMilliseconds} ms";
            msg += Environment.NewLine + "执行SQL：" + strSql;
            DebugUtil.WriteLogToFile(msg, "rpt_orderbalance");

        }

        private void UpdateSoStockData()
        {
            /*
             * --foutday：超期送货天数：销售出库数<=0时，取交货时间与当前日期的天数；当销售出库数>0时，取交货时间与关联的销售出库单的业务时间的最大值的差值天数
 --ISNULL( 
	--CASE WHEN odmx.fbizoutqty <= 0 THEN DATEDIFF(DAY,od.fdeliverydate,GETDATE()) 
	--WHEN odmx.fbizoutqty > 0  THEN  
	--	DATEDIFF (DAY, od.fdeliverydate,
	--	(
	--		SELECT  MAX(b.fdate) 
	--		FROM  dbo.T_STK_SOSTOCKOUTENTRY AS a with(nolock) 
	--		INNER JOIN dbo.T_STK_SOSTOCKOUT AS b with(nolock) ON a.fid = b.fid 
	--		where odmx.fentryid = a.fsoorderentryid)
	--	)                          
	--END,0)
             */


            var stopWatch = new Stopwatch();
            stopWatch.Start();

            string msg = $"UpdateSoStockData，开始时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}";

            string strSql = $@"/*dialect*/
update t set foutday=DATEDIFF(DAY, fdeliverydate, te.fsodate)
from {this.DataSourceTableName} t
inner join
(
    select te.fsoorderentryid, MAX(t.fdate) fsodate from T_STK_SOSTOCKOUTENTRY te with(nolock)
    inner join T_STK_SOSTOCKOUT t with(nolock) on te.fid=t.fid
    where exists(select 1 from {this.DataSourceTableName} ts with(nolock) where ts.forderentryid=te.fsoorderentryid and ts.fbizoutqty>0) and t.fcancelstatus='0'
    group by te.fsoorderentryid
) te on t.forderentryid=te.fsoorderentryid

            ";

            this.DBService.ExecuteDynamicObject(this.Context, strSql);

            stopWatch.Stop();

            msg += $"，耗时：{stopWatch.ElapsedMilliseconds} ms";
            msg += Environment.NewLine + "执行SQL：" + strSql;
            DebugUtil.WriteLogToFile(msg, "rpt_orderbalance");
        }

        private void UpdateReserveData()
        {
            /*
             * freserveday：库存占用预留天数，取预留方向为进的最大的操作时间取当前时间比较
             * freservedateto：预留至，取预留明细的值
             */

            var stopWatch = new Stopwatch();
            stopWatch.Start();

            string msg = $"UpdateReserveData，开始时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}";

            string strSql = $@"/*dialect*/
update t set freserveday=re.freserveday, freservedateto=re.freservedateto
from {this.DataSourceTableName} t
inner join 
(
    select re.fsourceentryid, MAX(re.freservedateto) as freservedateto, DATEDIFF(DAY, MAX(rd.foptime), GETDATE()) freserveday 
    from T_STK_RESERVEBILLENTRY re with(nolock)  
    left join T_STK_RESERVEBILLDETAIL rd with(nolock) on re.fentryid=rd.fentryid and rd.fdirection_d='0'
    where exists(select 1 from {this.DataSourceTableName} t where t.forderentryid=re.fsourceentryid)
    group by re.fsourceentryid
) re on t.forderentryid=re.fsourceentryid
            ";

            this.DBService.ExecuteDynamicObject(this.Context, strSql);

            stopWatch.Stop();

            msg += $"，耗时：{stopWatch.ElapsedMilliseconds} ms";
            msg += Environment.NewLine + "执行SQL：" + strSql;
            DebugUtil.WriteLogToFile(msg, "rpt_orderbalance");
        }

        private void UpdatePriceData()
        {
            //fpurfacprice、fpurdealprice、fpurfacamount、fpurdealamount：综合价目表取值更新

            var stopWatch = new Stopwatch();
            stopWatch.Start();

            string msg = $"UpdatePriceData，开始时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}";

            string strSql = $@"/*dialect*/
update t set fpurfacprice=pr.fpurfacprice, fpurdealprice=pr.fpurdealprice, fpurdealamount=pr.fpurdealprice * fbizqty, fpurfacamount=pr.fpurfacprice * fbizqty
from {this.DataSourceTableName} t
inner join t_ydj_pricesynthesize pr with(nolock) on pr.fmainorgid='{this.Context.Company}'
and t.fproductid = pr.fmaterialid and t.fattrinfo_e=pr.fattrinfo_e 
and t.fcustomdes_e = pr.fcustomdesc and t.funitid = pr.funitid
";

            this.DBService.ExecuteDynamicObject(this.Context, strSql);

            stopWatch.Stop();

            msg += $"，耗时：{stopWatch.ElapsedMilliseconds} ms";
            msg += Environment.NewLine + "执行SQL：" + strSql;
            DebugUtil.WriteLogToFile(msg, "rpt_orderbalance");
        }
    }
}
