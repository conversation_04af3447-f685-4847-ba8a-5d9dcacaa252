using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.Interface.BizTask;
using System.Threading;
using JieNor.Framework.Interface.QueryBuilder;

namespace JieNor.AMS.YDJ.Store.AppService.SchedulerTask
{
    /// <summary>
    /// 综合价目表计算
    /// </summary>
    [InjectService]
    [TaskSvrId("pricecalculate")]
    [Caption("综合价目表计算（在总部执行）")]
    //[TaskMultiInstance()]
    [Browsable(false)]
    public class PriceCalculate : AbstractScheduleWorker
    {
        /// <summary>
        /// 执行任务逻辑
        /// </summary>
        /// <returns></returns>
        protected override async Task DoExecute()
        {

            var ctx = this.UserContext;
            var tmpTableName = string.Empty;
            try
            {
                //查询有业务产生的组织经销商（采购订单/销售合同/销售价目/采购价目/盘点单/其他入库单/销售退货单）
                var orgStrSql = $@"select fmainorgid from t_ydj_purchaseorder with(nolock) group by fmainorgid
                                    union
                                    select fmainorgid from t_ydj_order with(nolock) group by fmainorgid
                                    union
                                    select fmainorgid from t_ydj_price with(nolock) group by fmainorgid
                                    union
                                    select fmainorgid from t_ydj_purchaseprice with(nolock) group by fmainorgid
                                    union
                                    select fmainorgid from t_stk_invverify with(nolock) group by fmainorgid
                                    union
                                    select fmainorgid from t_stk_otherstockin with(nolock) group by fmainorgid
                                    union
                                    select fmainorgid from t_stk_sostockreturn with(nolock) group by fmainorgid";
                var allGrpOrgs = this.DBService.ExecuteDynamicObject(ctx, orgStrSql);

                var grpOrgs = new List<string>();
				//1、先跑没有综合价目表数据的经销商
				//2、再跑有综合价目表数据经销商的【最近更新日期】最旧的经销商（从更新日期最旧到最新）
                var calculateSql = $@"select fmainorgid,max(fupdatetime) fupdatetime from t_ydj_pricesynthesize with(nolock) group by fmainorgid";
				var calculateOrgs = this.DBService.ExecuteDynamicObject(ctx, calculateSql);
				if (calculateOrgs != null && calculateOrgs.Count > 0)
				{
					var except = calculateOrgs.OrderBy(x => Convert.ToDateTime(x["fupdatetime"])).Select(x => Convert.ToString(x["fmainorgid"]));
					grpOrgs = allGrpOrgs.Select(x => Convert.ToString(x["fmainorgid"])).Except(except).ToList();
					grpOrgs.AddRange(except);
				}
				else
				{
					grpOrgs = allGrpOrgs.Select(x => Convert.ToString(x["fmainorgid"])).ToList();
				}

				var priceCalculateService = ctx.Container.GetService<PriceCalculateService>();
                foreach (var item in grpOrgs)
                {
                    if (DateTime.Now.Hour > 7)
                    {
                        this.WriteLog("综合价目表计算只在凌晨0-7点更新，此次执行结束！");
                        break;
                    }

                    var fmainorgid = Convert.ToString(item);
                    if (!fmainorgid.IsNullOrEmptyOrWhiteSpace())
                    {
                        var ctxX = ctx.CreateAgentDBContext(fmainorgid);
                        try
                        {
                            //1.查询企业采购订单和销售合同商品明细商品维度数据
                            tmpTableName = this.DBService.CreateTemporaryTableName(ctxX);
                            var tableStrSql = $@"/*dialect*/select * into {tmpTableName} from (
                                select t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdes_e fcustomdesc,t1.funitid,t3.fmainorgid fproductorgid,'{ctxX.Company}' fmainorgid from t_ydj_poorderentry t1 with(nolock)
                                left join t_ydj_purchaseorder t2 with(nolock) on t1.fid=t2.fid
                                inner join t_bd_material t3 with(nolock) on t1.fmaterialid = t3.fid
                                where t2.fmainorgid='{ctxX.Company}'
                                group by t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdes_e,t1.funitid,t3.fmainorgid
                                union
                                select t1.fproductid fmaterialid,t1.fattrinfo_e,t1.fcustomdes_e fcustomdesc,t1.funitid,t3.fmainorgid fproductorgid,'{ctxX.Company}' fmainorgid from t_ydj_orderentry t1 with(nolock)
                                left join t_ydj_order t2 with(nolock) on t1.fid=t2.fid
                                inner join t_bd_material t3 with(nolock) on t1.fproductid = t3.fid
                                where t2.fmainorgid='{ctxX.Company}'
                                group by t1.fproductid,t1.fattrinfo_e,t1.fcustomdes_e,t1.funitid,t3.fmainorgid
                                ) tab ";
                            this.DBService.ExecuteDynamicObject(ctxX, tableStrSql);

                            //1.查询企业盘点单/其他入库单/销售退货单商品明细商品维度数据
                            var insertSql = $@"/*dialect*/insert into {tmpTableName} 
                                (fmaterialid,fattrinfo_e,fcustomdesc,funitid,fproductorgid,fmainorgid)
                                (select* from (select t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.funitid,t3.fmainorgid fproductorgid,'{ctxX.Company}' fmainorgid from t_stk_invverifyentry t1 with(nolock)
                                left join t_stk_invverify t2 with(nolock) on t1.fid=t2.fid
                                inner join t_bd_material t3 with(nolock) on t1.fmaterialid = t3.fid
                                where t2.fmainorgid='{ctxX.Company}'
                                group by t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.funitid,t3.fmainorgid
                                union
                                select t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.funitid,t3.fmainorgid fproductorgid,'{ctxX.Company}' fmainorgid from t_stk_otherstockinentry t1 with(nolock)
                                left join t_stk_otherstockin t2 with(nolock) on t1.fid=t2.fid
                                inner join t_bd_material t3 with(nolock) on t1.fmaterialid = t3.fid
                                where t2.fmainorgid='{ctxX.Company}'
                                group by t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.funitid,t3.fmainorgid
                                union
                                select t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.funitid,t3.fmainorgid fproductorgid,'{ctxX.Company}' fmainorgid from t_stk_sostockreturnentry t1 with(nolock)
                                left join t_stk_sostockreturn t2 with(nolock) on t1.fid=t2.fid
                                inner join t_bd_material t3 with(nolock) on t1.fmaterialid = t3.fid
                                where t2.fmainorgid='{ctxX.Company}'
                                group by t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.funitid,t3.fmainorgid
                                ) tab where not exists(select 1 from {tmpTableName} t4 with(nolock) where t4.fmaterialid=tab.fmaterialid and t4.fattrinfo_e=tab.fattrinfo_e and t4.funitid=tab.funitid and t4.fcustomdesc=tab.fcustomdesc))";
                            this.DBService.ExecuteDynamicObject(ctxX, insertSql);

                            //数据隔离规则SQL
                            var authPara = new DataQueryRuleParaInfo();
                            var tempView = ctxX.GetAuthProductDataPKID(authPara);

                            //2.查询企业销售价目商品明细商品维度数据
                            insertSql = $@"insert into {tmpTableName}
                                (fmaterialid,fattrinfo_e,fcustomdesc,funitid,fproductorgid,fmainorgid)
                                (select t1.fproductid fmaterialid,t1.fattrinfo_e,'' fcustomdesc,t1.funitid,t3.fmainorgid fproductorgid,'{ctxX.Company}' fmainorgid from t_ydj_priceentry t1 with(nolock)
                                left join t_ydj_price t2 with(nolock) on t1.fid=t2.fid
                                inner join t_bd_material t3 with(nolock) on t1.fproductid = t3.fid
                                where not exists(select 1 from {tmpTableName} t4 with(nolock) where t4.fmaterialid=t1.fproductid and t4.fattrinfo_e=t1.fattrinfo_e and t4.funitid=t1.funitid and t4.fcustomdesc='')
                                and t1.fconfirmstatus=2 and t2.fmainorgid in ('{ctxX.Company}','{ctxX.ParentCompanyId}','{ctxX.TopCompanyId}')
                                and t1.fstartdate <= getdate() and t1.fexpiredate >= getdate() --日期
                                and t1.fproductid in ({tempView})
                                group by t1.fproductid,t1.fattrinfo_e,t1.funitid,t3.fmainorgid)";
                            this.DBService.ExecuteDynamicObject(ctxX, insertSql);
                            //3.查询企业采购价目商品明细商品维度数据
                            var fmainorgids = ctxX.IsSecondOrg ? $@"'{ctxX.Company}'" : $@"'{ctxX.Company}','{ctxX.TopCompanyId}'";//二级分销不能看总部采购价
                            insertSql = $@"insert into {tmpTableName}
                                (fmaterialid,fattrinfo_e,fcustomdesc,funitid,fproductorgid,fmainorgid)
                                (select t1.fproductid_e fmaterialid,t1.fattrinfo_e,'' fcustomdesc,t1.funitid_e funitid,t3.fmainorgid fproductorgid,'{ctxX.Company}' fmainorgid from t_ydj_purchasepriceentry t1 with(nolock)
                                left join t_ydj_purchaseprice t2 with(nolock) on t1.fid=t2.fid
                                inner join t_bd_material t3 with(nolock) on t1.fproductid_e = t3.fid
                                where not exists(select 1 from {tmpTableName} t4 with(nolock) where t4.fmaterialid=t1.fproductid_e and t4.fattrinfo_e=t1.fattrinfo_e and t4.funitid=t1.funitid_e and t4.fcustomdesc='' )
                                and t1.fconfirmstatus=2 and t2.fmainorgid in ({fmainorgids})     
                                and t1.fstartdate_e <= getdate() and t1.fexpiredate_e >= getdate() --日期           
                                and t1.fproductid_e in ({tempView})
                                group by t1.fproductid_e,t1.fattrinfo_e,t1.funitid_e,t3.fmainorgid)";
                            this.DBService.ExecuteDynamicObject(ctxX, insertSql);

                            var result = priceCalculateService?.PriceCalculate(ctxX, tmpTableName, this.Option);

                            this.WriteLog("经销商 {0} ：{1}".Fmt(ctxX.Company, result.SimpleMessage));

                            this.DBService.DeleteTempTableByName(ctx, tmpTableName, true);
                        }
                        catch (Exception e)
                        {
                            this.WriteLog("经销商 {0}计算异常 ：{1}".Fmt(ctxX.Company, e.Message));
                        }
                    }
                }
            }
            catch (Exception e)
            {
                this.WriteLog("综合价目表计算异常！" + e.Message);
            }
        }
    }
}
