using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.Interface.BizTask;
using System.Data;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.SchedulerTask
{
    /// <summary>
    /// 库龄计算
    /// </summary>
    [InjectService]
    [TaskSvrId("stockageanalysis")]
    [Caption("库龄计算（在总部执行）")]
    //[TaskMultiInstance()]
    [Browsable(false)]
    public class StockAgeAnalysis : AbstractScheduleWorker
    {
        /// <summary>
        /// 任务编码
        /// </summary>
        private static readonly string Task_Number= "stockageanalysis";
        /// <summary>
        /// 任务名称
        /// </summary>
        private static readonly string Task_Name = "库龄分析";
        /// <summary>
        /// 执行任务逻辑
        /// </summary>
        /// <returns></returns>
        protected override async Task DoExecute()
        {
            var ctx = this.UserContext;
            DynamicObject taskStatus = null;
            try
            {
               taskStatus =  StartTaskStatus();

                // 设置库龄计算日期
                var stockdatestr = DateTime.Now.AddDays(1).Date.ToString("yyyy-MM-dd");
                var priceCalculateService = ctx.Container.GetService<StockAgeAnalysisService>();
                //分组查询
                //var orgStrSql = $@"select distinct fmainorgid from t_stk_inventorylist with(nolock)";
                //
                var orgStrSql = $@"select distinct fmainorgid,max(fdate_alterstock) as fdate_alterstock from t_stk_inventorylist with(nolock) 
                                    where datediff(dd,fdate_alterstock,'{stockdatestr}') < 2
                                    group by fmainorgid ";
                using (DataTable grpOrgs = this.DBService.ExecuteDataTable(ctx, orgStrSql))
                {
                    foreach (DataRow item in grpOrgs.Rows)
                    {
                        var fmainorgid = (string)item["fmainorgid"];
                        if (!fmainorgid.IsNullOrEmptyOrWhiteSpace())
                        {
                            var ctxX = ctx.CreateAgentDBContext(fmainorgid);
                            var result = priceCalculateService?.StockAgeCalculate(ctxX, stockdatestr);
                            this.WriteLog("经销商 {0} ：{1}".Fmt(ctxX.Company, result.SimpleMessage));
                        }
                    }
                }

                //补偿逻辑，如果当前经销商库存没有变化，就不会走上述逻辑，所以在库天数依然需要更新为 当前日期-入库日期 (判断 在库天数 不等于当前日期-入库日期才需要更新)
                string sql = $@"/*dialect*/update t0 set finstockday =  DATEDIFF(day,finstockdate,'{stockdatestr}')
                            --select top 100 finstockday,finstockdate,DATEDIFF(day,finstockdate,'{stockdatestr}'),*
                            ,fupdatetime=getdate()
                            from t_ydj_stockageanalysis as t0 with (nolock) 
                            where  finstockday != DATEDIFF(day,finstockdate,'{stockdatestr}')";
                var dbServiceEx = ctx.Container.GetService<IDBServiceEx>();
                dbServiceEx.Execute(ctx, sql);

                FinishTaskStatus(taskStatus,true);
            }
            catch (Exception e)
            {
                this.WriteLog("库龄计算异常！" + e.Message);
                FinishTaskStatus(taskStatus, false, e.Message);
            }
        }

        private DynamicObject StartTaskStatus()
        {
            var metaModelService = UserContext.Container.GetService<IMetaModelService>();
            var statusForm = metaModelService.LoadFormModel(UserContext, "bas_calctaskstatus");
            var dm = UserContext.Container.GetService<IDataManager>();
            dm.InitDbContext(UserContext, statusForm.GetDynamicObjectType(UserContext));

            var taskStatus = UserContext.LoadBizDataById("bas_calctaskstatus", Task_Number);
            if (taskStatus == null)
            {
                taskStatus = statusForm.GetDynamicObjectType(UserContext).CreateInstance() as DynamicObject;
                taskStatus["id"] = Task_Number;
                taskStatus["fnumber"] = Task_Number;
                taskStatus["fname"] = Task_Name;
                taskStatus["ftables"] = "t_ydj_stockageanalysis";
                taskStatus["fcolumns"] = "fupdatetime";
            }
            taskStatus["fstarttime"] = DateTime.Now;
            taskStatus["fendtime"] = null;
            taskStatus["fstatus"] = "1";

            dm.Save(taskStatus);

            return taskStatus;
        }

        private void FinishTaskStatus(DynamicObject taskStatus, bool success, string message=null)
        {
            if(taskStatus!= null)
            {
                if (success)
                {
                    taskStatus["fstatus"] = "2";
                }
                else
                {
                    taskStatus["fstatus"] = "3";
                   
                }


                string strSql = $@"select count(*) as fdatacount, max(fupdatetime) as fupdatetime from t_ydj_stockageanalysis";
                var calcdata = UserContext.ExecuteDynamicObject(strSql, null).FirstOrDefault();

                taskStatus["fremark"] = message;
                taskStatus["fendtime"] = DateTime.Now;

                taskStatus["flasttime"] = calcdata["fupdatetime"];
                taskStatus["fdatacount"] = calcdata["fdatacount"];

                var metaModelService = UserContext.Container.GetService<IMetaModelService>();
                var statusForm = metaModelService.LoadFormModel(UserContext, "bas_calctaskstatus");
                var dm = UserContext.Container.GetService<IDataManager>();
                dm.InitDbContext(UserContext, statusForm.GetDynamicObjectType(UserContext));
                dm.Save(taskStatus);
            }
        }
    }
}
