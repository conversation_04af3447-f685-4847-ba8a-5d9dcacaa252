//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;
//using JieNor.AMS.YDJ.Core.Interface;
//using JieNor.AMS.YDJ.MP.API.Utils;
//using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order;
//using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
//using JieNor.AMS.YDJ.Store.AppService.Utils;
//using JieNor.Framework;
//using JieNor.Framework.Enums;
//using JieNor.Framework.Interface;
//using JieNor.Framework.MetaCore.FormMeta;
//using JieNor.Framework.MetaCore.Validator;
//using JieNor.Framework.SuperOrm;
//using JieNor.Framework.SuperOrm.DataEntity;

//namespace JieNor.AMS.YDJ.Store.AppService.Validation.STE.Order
//{
//    /// <summary>
//    /// 销售合同：非标价格校验器
//    /// </summary>
//    public class UnstdPriceValidation : AbstractBaseValidation
//    {
//        /// <summary>
//        /// 强制实体为单据头
//        /// </summary>
//        public override string EntityKey
//        {
//            get { return "fbillhead"; }
//            set { base.EntityKey = value; }
//        }

//        /// <summary>
//        /// 初始化上下文
//        /// </summary>
//        /// <param name="userCtx"></param>
//        /// <param name="validExpr"></param>
//        protected override void InitializeContext(UserContext userCtx, string validExpr)
//        {
//            base.InitializeContext(userCtx, validExpr);
//        }

//        /// <summary>
//        /// 业务表单模型
//        /// </summary>
//        private HtmlForm HtmlForm { get; set; }

//        /// <summary>
//        /// 非标数据
//        /// </summary>
//        private string UnstdData { get; set; }

//        /// <summary>
//        /// 校验结果
//        /// </summary>
//        private ValidationResult Result { get; set; } = new ValidationResult();

//        public UnstdPriceValidation(string unstdData)
//        {
//            this.UnstdData = unstdData;
//        }

//        /// <summary>
//        /// 校验逻辑处理单元
//        /// </summary>
//        /// <param name="userCtx"></param>
//        /// <param name="formInfo"></param>
//        /// <param name="dataEntities"></param>
//        /// <param name="option"></param>
//        /// <param name="operationNo"></param>
//        /// <returns></returns>
//        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
//        {
//            if (this.UnstdData.IsNullOrEmptyOrWhiteSpace())
//            {
//                return this.Result;
//            }

//            this.HtmlForm = formInfo;

//            var checkUnstdPriceDTOs = this.UnstdData.FromJson<List<CheckUnstdPriceDTO>>();

//            var orderEntryIds = checkUnstdPriceDTOs.SelectMany(s => s.fentry).Select(s => s.id).ToList();
//            if (orderEntryIds.IsNullOrEmpty())
//            {
//                return this.Result;
//            }
//            var orderEntrys = this.Context.ExecuteDynamicObject(
//                $"select fentryid, fprice, funstdtypestatus, funstdtype from t_ydj_orderentry where fentryid in ({orderEntryIds.JoinEx(",", true)})", new List<SqlParam>());

//            foreach (var dataEntity in dataEntities)
//            {
//                if (!dataEntity.DataEntityState.FromDatabase)
//                {
//                    continue;
//                }

//                var id = dataEntity["id"]?.ToString();
//                if (id.IsNullOrEmptyOrWhiteSpace())
//                {
//                    continue;
//                }

//                var dto = checkUnstdPriceDTOs.FirstOrDefault(s => s.id.EqualsIgnoreCase(id));

//                foreach (var entryDto in dto.fentry)
//                {
//                    // 不是非标的，跳过
//                    if (!entryDto.funstdtype)
//                    {
//                        continue;
//                    }

//                    // 数据库里找不到，跳过
//                    var entry = orderEntrys.FirstOrDefault(s =>
//                        Convert.ToString(s["fentryid"]).EqualsIgnoreCase(entryDto.id));
//                    if (entry == null)
//                    {
//                        continue;
//                    }

//                    decimal price = Convert.ToDecimal(entry["fprice"]);
//                    //string unstdTypeStatus = Convert.ToString(entry["funstdtypestatus"]);

//                    //var isSame = entryDto.funstdtypestatus.EqualsIgnoreCase(unstdTypeStatus)
//                    //             && entryDto.fprice == price;
//                    var isSame = entryDto.fprice == price;
//                    if (!isSame)
//                    {
//                        this.Result.Errors.Add(new ValidationResultEntry()
//                        {
//                            ErrorMessage = $"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】存在非标价格更新请先刷新当前页面！",
//                            DataEntity = dataEntity,
//                        });

//                        break;
//                    }
//                }
//            }

//            return this.Result;
//        }
//    }
//}
