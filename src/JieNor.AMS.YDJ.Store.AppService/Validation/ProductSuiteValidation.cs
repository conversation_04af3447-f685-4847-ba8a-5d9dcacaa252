using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.CustomException;
using JieNor.AMS.YDJ.DataTransferObject;
using Newtonsoft.Json.Linq;
using System.Data;

namespace JieNor.AMS.YDJ.Store.AppService.Validation
{
    /// <summary>
    /// 商品选配套件校验器：检查商品是否是套件商品，如果是套件商品，则【选配套件】字段必须勾选
    /// </summary>
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    [ServiceMetaAttribute("validationid", YDJHtmlElementType.HtmlValidator_ProductSuiteValidation)]
    public class ProductSuiteValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);
        }

        /// <summary>
        /// 校验逻辑处理单元
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();

            //1、如果不是从数据库加载的数据包，说明当前是新增保存，此时不需要检查，因为此时该新增商品肯定不是套件
            //2、如果是选配套件，则不用检查
            var validationDatas = dataEntities?.Where(o => o.DataEntityState.FromDatabase && !Convert.ToBoolean(o["fsuiteflag"]));
            var productIds = validationDatas?.Select(o => Convert.ToString(o["id"]))?.ToList();
            if (productIds == null || !productIds.Any()) return result;

            var sqlText = @"select fproductid from t_sel_suite where ";
            if (productIds.Count() == 1)
            {
                sqlText += $" fproductid='{productIds[0]}'";
            }
            else
            {
                sqlText += $" fproductid in('{string.Join("','", productIds)}')";
            }
            var dynObjs = this.DBService.ExecuteDynamicObject(this.Context, sqlText);
            if (dynObjs == null || !dynObjs.Any()) return result;

            foreach (var item in validationDatas)
            {
                //如果当前商品是套件商品，则【选配套件】字段必须勾选
                var productId = Convert.ToString(item["id"]);
                var dynObj = dynObjs.FirstOrDefault(o => Convert.ToString(o["fproductid"]).EqualsIgnoreCase(productId));
                if (dynObj != null)
                {
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"商品【{item["fnumber"]}/{item["fname"]}】已经是套件商品，必须勾选【选配套件】字段！",
                        DataEntity = item
                    });
                }
            }

            return result;
        }
    }
}