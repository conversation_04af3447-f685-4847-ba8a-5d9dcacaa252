using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.CustomException;
using JieNor.AMS.YDJ.DataTransferObject;

namespace JieNor.AMS.YDJ.Store.AppService.Validation
{
    /// <summary>
    /// 选配范围属性值校验器：检查属性值是否是属性的有效值。
    /// </summary>
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    [ServiceMetaAttribute("validationid", YDJHtmlElementType.HtmlValidator_SelRangePropValueValidation)]
    public class SelRangePropValueValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }

        /// <summary>
        /// 校验逻辑处理单元
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();

            var propIds = new List<string>();
            var propValueIds = new List<string>();
            foreach (var dataEntitie in dataEntities)
            {
                var entrys = dataEntitie["fentity"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    var propId = Convert.ToString(entry["fpropid"]);
                    var propValueId = Convert.ToString(entry["fpropvalueid"]); //属性值，多个
                    var _propValueIds = propValueId.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                    var defPropValueId = Convert.ToString(entry["fdefaultpropvalueid"]); //默认值
                    if (propId.IsNullOrEmptyOrWhiteSpace()) continue;
                    if (!_propValueIds.Any() && defPropValueId.IsNullOrEmptyOrWhiteSpace()) continue;

                    propIds.Add(propId);

                    if (_propValueIds.Any())
                    {
                        propValueIds.AddRange(_propValueIds);
                    }
                    if (!defPropValueId.IsNullOrEmptyOrWhiteSpace())
                    {
                        propValueIds.Add(defPropValueId);
                    }
                }
            }

            //批量加载属性值名称
            DynamicObjectCollection propDatas = null;
            DynamicObjectCollection propValueDatas = null;
            propIds = propIds.Distinct().ToList();
            propValueIds = propValueIds.Distinct().ToList();
            if (propIds.Any()) propDatas = this.Context.LoadBizBillHeadDataById("sel_prop", propIds, "fnumber,fname");
            if (propValueIds.Any()) propValueDatas = this.Context.LoadBizBillHeadDataById("sel_propvalue", propValueIds, "fnumber,fname");

            //批量加载属性关联的有效属性值信息
            var propValueInfos = this.LoadPropValueInfos(propIds);

            foreach (var dataEntitie in dataEntities)
            {
                var entrys = dataEntitie["fentity"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    var propId = Convert.ToString(entry["fpropid"]);
                    var propValueId = Convert.ToString(entry["fpropvalueid"]); //属性值，多个
                    var _propValueIds = propValueId.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                    var defPropValueId = Convert.ToString(entry["fdefaultpropvalueid"]); //默认值
                    if (propId.IsNullOrEmptyOrWhiteSpace()) continue;
                    if (!_propValueIds.Any() && defPropValueId.IsNullOrEmptyOrWhiteSpace()) continue;

                    //校验属性值
                    foreach (var _propValueId in _propValueIds)
                    {
                        this.AddValidationResultEntry(dataEntitie, propValueInfos, propDatas, propValueDatas, propId, _propValueId, false, result);
                    }

                    //校验默认值
                    if (!defPropValueId.IsNullOrEmptyOrWhiteSpace())
                    {
                        this.AddValidationResultEntry(dataEntitie, propValueInfos, propDatas, propValueDatas, propId, defPropValueId, true, result);
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 添加校验结果明细
        /// </summary>
        private void AddValidationResultEntry(
            DynamicObject dataEntitie,
            DynamicObjectCollection propValueInfos,
            DynamicObjectCollection propDatas,
            DynamicObjectCollection propValueDatas,
            string propId,
            string propValueId,
            bool isDefVal,
            ValidationResult result)
        {
            var propValueInfo = propValueInfos?.FirstOrDefault(o =>
                    Convert.ToString(o["fpropid"]).EqualsIgnoreCase(propId) &&
                    Convert.ToString(o["fpropvalueid"]).EqualsIgnoreCase(propValueId));
            if (propValueInfo == null)
            {
                //属性名称
                var propName = propId;
                var propData = propDatas?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(propId));
                if (propData != null)
                {
                    propName = Convert.ToString(propData?["fnumber"]);
                    propName += "/" + Convert.ToString(propData?["fname"]);
                }

                //属性值名称
                var propValueName = propValueId;
                var propValueData = propValueDatas?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(propValueId));
                if (propValueData != null)
                {
                    propValueName = Convert.ToString(propValueData?["fnumber"]);
                    propValueName += "/" + Convert.ToString(propValueData?["fname"]);
                }

                result.Errors.Add(new ValidationResultEntry()
                {
                    ErrorMessage = $"{(isDefVal ? "默认属性值" : "属性值")}【{propValueName}】不是属性【{propName}】的有效值，请检查！",
                    DataEntity = dataEntitie
                });
            }
        }

        /// <summary>
        /// 批量加载属性关联的有效属性值信息
        /// </summary>
        private DynamicObjectCollection LoadPropValueInfos(List<string> propIds)
        {
            if (!propIds.Any()) return null;

            var sqlText = $@"select distinct fpropid,fid fpropvalueid from t_sel_propvalue where";
            if (propIds.Count == 1)
            {
                sqlText += $" fpropid='{propIds[0]}'";
            }
            else
            {
                sqlText += $" fpropid in('{string.Join("','", propIds)}')";
            }

            var datas = this.DBService.ExecuteDynamicObject(this.Context, sqlText);
            return datas;
        }
    }
}