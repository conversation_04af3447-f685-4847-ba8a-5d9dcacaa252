using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sal.Collectreceipt
{
    /// <summary>
    /// 其他应收/付单：审核
    /// 作者：zpf
    /// 日期：2021-01-18
    /// </summary>
    [InjectService]
    [FormId("ydj_collectreceipt|ydj_payreceipt")]
    [OperationNo("audit")]
    public class Audit : AbstractOperationServicePlugIn
    {
        /*
         * 1、审核《其他应收单》和《其他应付单》反写到接单经销商的《转单申请单》、目标经销商的《转单申请单》,
         * 目标经销商的《销售合同》, 接单方的《销售合同》的【转单状态】标识为【已结算】。
         */
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            Helpers.AuditOrUnAuditHelper.ReceivablesOrPaymentsAuditOrUnAudit(this.HtmlForm.Id,e.DataEntitys,this.Context,true);
            this.Result.SimpleMessage = "上游单据反写成功！";
            this.Result.IsSuccess = true;
        }
    }
}
