using JieNor.AMS.YDJ.Core.Interface.Finance;
using JieNor.AMS.YDJ.Core.Interface.StockInit;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sal.ClosedAccounts
{
    /// <summary>
    /// 财务关账
    /// 作者：张鹏飞
    /// 日期：2022-07-26
    /// </summary>
    [InjectService]
    [FormId("ydj_closedaccounts")]
    [OperationNo("closing")]
    public class Closing : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null ||
                e.DataEntitys.Length == 0)
            {
                throw new BusinessException("当前上下文数据不存在，无法关账！");
            }

            DateTime closeDate;
            if (!DateTime.TryParse(Convert.ToString(e.DataEntitys.FirstOrDefault()["fclosuredate"]), out closeDate))
            {
                throw new BusinessException("请输入合法的关账日期后再执行此操作！");
            }

            //执行关账服务
            var result = this.Container.GetService<IFinanceBaseService>()?.Closing(this.Context, closeDate, this.Option);
            this.Result.MergeResult(result);
            this.AddRefreshPageAction();
        }
    }
}
