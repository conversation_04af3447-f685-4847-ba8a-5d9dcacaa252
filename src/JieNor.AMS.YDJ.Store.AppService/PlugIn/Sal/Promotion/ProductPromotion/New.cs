using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sal.Promotion.ProductPromotion
{
    /// <summary>
    /// 商品促销：新建
    /// </summary>
    [InjectService]
    [FormId("ydj_productpromotion")]
    [OperationNo("New")]
    public class New : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName.ToLower())
            {
                case "aftercreateuidata":
                    this.ProcAfterCreateUiData(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 处理UI数据打包逻辑
        /// </summary>
        /// <param name="e"></param>
        private void ProcAfterCreateUiData(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as JObject;
            if (eventData == null) return;

            var systemProfile = this.Container.GetService<ISystemProfile>();
            var fisgiftdiscount = systemProfile.GetSystemParameter<bool>(this.Context, "bas_storesysparam", "fisgiftdiscount");

            eventData["fisgiftdiscount"] = fisgiftdiscount;
        }
    }
}