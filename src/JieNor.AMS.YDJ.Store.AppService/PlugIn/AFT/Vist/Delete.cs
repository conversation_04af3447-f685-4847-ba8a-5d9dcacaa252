using System;
using System.Linq;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework;
using System.Collections.Generic;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Enums;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.AFT.Vist
{
    /// <summary>
    /// 回访单：删除
    /// </summary>
    [InjectService]
    [FormId("ydj_vist")]
    [OperationNo("delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            ///*
            //    定义表头校验规则
            //*/
            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    if (!(Convert.ToString(newData["ffeedstatus"]).EqualsIgnoreCase("aft_service_01") && newData["fsourcecancl"].Equals("1")))
            //    {
            //        return false;
            //    }
            //    return true;
            //}).WithMessage("【{0}】删除失败，仅允许删除售后状态为【待处理】且来源渠道为【自建】的单据！", (billObj, propObj) => propObj["fbillno"]));
        }
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;

            var fieldMapObjs = this.Context.Container.GetService<IMuSiBizObjMapService>()
                .GetBizObjMaps(this.Context, this.HtmlForm, Enu_MuSiSyncDir.CurrentToMuSi, Enu_MuSiSyncTimePoint.SyncManual);

            IOperationResult result = null;
            foreach (var fieldMapObj in fieldMapObjs)
            {
                var option = new Dictionary<string, object>();
                var ffieldentry = fieldMapObj["ffieldentry"] as DynamicObjectCollection;
                var isReturnData = ffieldentry.FirstOrDefault(x => Convert.ToString(x["fextfieldid"]).EqualsIgnoreCase("delStatus"));
                if (isReturnData != null)
                {
                    isReturnData["fmyconstval"] = true;
                }
                option["__fieldMapObj__"] = fieldMapObj;

                result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, e.DataEntitys, "synctomusi", option);
                result?.ThrowIfHasError(true, $"{this.HtmlForm.Caption}数据同步给总部过程出错，请重试！");
                result.MergeResult(result);
            }
            if (result == null)
            {
                throw new BusinessException($"请到数据同步设置菜单界面配置慕思营销中台配置项！");
            }
            if (!result.IsSuccess)
            {
                throw new BusinessException($"{this.HtmlForm.Caption}数据同步给总部过程出错，请重试！");
            }
        }
    }
}