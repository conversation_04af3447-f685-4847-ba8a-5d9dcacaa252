using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Master
{
    /// <summary>
    /// 师傅：获取师傅的交易明细
    /// </summary>
    [InjectService]
    [FormId("ydj_master")]
    [OperationNo("querytrans")]
    public class QueryTrans : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 查询类型：
        /// cash：提现
        /// order：订单收入
        /// </summary>
        public string SearchType { get; set; }

        /// <summary>
        /// 操作执行结束处理单元
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var searchType = this.GetQueryOrSimpleParam<string>("searchType", "");
            var orderByString = this.GetQueryOrSimpleParam<string>("orderByString", "");
            var pageIndex = this.GetQueryOrSimpleParam<int>("pageIndex", 1);
            var pageSize = this.GetQueryOrSimpleParam<int>("pageSize", 10);
            if (orderByString.IsNullOrEmptyOrWhiteSpace()) orderByString = "date desc";
            if (pageIndex <= 0) pageIndex = 1;
            if (pageSize <= 0) pageSize = 10;
            var masterId = this.GetQueryOrSimpleParam<string>("masterId", "");
            if (masterId.IsNullOrEmptyOrWhiteSpace())
            {
                //获取师傅信息
                var masterService = this.Container.GetService<IMasterService>();
                var master = masterService.GetMasterByIdOrContext(this.Context);
                masterId = master["id"] as string;
            }
            this.SearchType = searchType.ToLower();

            var dataSql = "";
            switch (this.SearchType)
            {
                //提现
                case "cash":
                    dataSql = $@"
		            select ps.fid as id,ps.fpaytime as 'date','支出' as direction,ed.fenumitem as 'status','' as orderNo,
		            '提现' as purpose,e3.fenumitem as way,'线下转账' as channel,ps.fmoney as amount from t_pay_settleorder ps 
		            left join t_bd_enumdataentry ed on ed.fentryid=ps.fpaystatus 
		            left join t_bd_enumdataentry e3 on e3.fentryid=ps.foppcnlmode 
                    where ps.fmainorgid=@fmainorgid and ps.foppid=@masterId and ps.fbiztype='ydj_master' and ps.fbiznumber=@masterId  
                    and ps.fpurpose='fpurpose_01' and ps.fdirection='fdirection_02' and ps.fbizpurpose='bizpurpose_03'";
                    break;
                //订单收入
                case "order":
                    dataSql = $@"
                    select tk.fid as id,tk.freportdate as 'date','收入' as direction,tk.fsettlestatus as 'status',tk.fbillno as orderNo,
                    '订单收入' as purpose,'线下支付' as way,'内部账户支付' as channel,tk.fexpectamount as amount from t_ydj_service tk 
		            where tk.fmainorgid=@fmainorgid and tk.fmasterid=@masterId 
                    and tk.freportdate is not null and tk.fsettlestatus in('settle_status01_01','settle_status02_01','settle_status02')";
                    break;
                //全部
                default:
                    dataSql = $@"
		            select ps.fid as id,ps.fpaytime as 'date','支出' as direction,ed.fenumitem as 'status','' as orderNo,
		            '提现' as purpose,e3.fenumitem as way,'线下转账' as channel,ps.fmoney as amount from t_pay_settleorder ps 
		            left join t_bd_enumdataentry ed on ed.fentryid=ps.fpaystatus 
		            left join t_bd_enumdataentry e3 on e3.fentryid=ps.foppcnlmode 
                    where ps.fmainorgid=@fmainorgid and ps.foppid=@masterId and ps.fbiztype='ydj_master' and ps.fbiznumber=@masterId  
                    and ps.fpurpose='fpurpose_01' and ps.fdirection='fdirection_02' and ps.fbizpurpose='bizpurpose_03'
		            union
                    select tk.fid as id,tk.freportdate as 'date','收入' as direction,tk.fsettlestatus as 'status',tk.fbillno as orderNo,
                    '订单收入' as purpose,'线下支付' as way,'内部账户支付' as channel,tk.fexpectamount as amount from t_ydj_service tk 
		            where tk.fmainorgid=@fmainorgid and tk.fmasterid=@masterId 
                    and tk.freportdate is not null and tk.fsettlestatus in('settle_status01_01','settle_status02_01','settle_status02')";
                    break;
            }

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("@masterId", System.Data.DbType.String, masterId)
            };

            //查询分页数据
            var selectDataSql = $@"
            select top {pageSize} * from 
            (
	            select *,row_number() over(order by {orderByString}) as fjnidentity from ({dataSql}) t1
            ) t2 where fjnidentity>{pageSize * (pageIndex - 1)}";

            //查询总记录数
            var selectCountSql = $@"select count(1) from ({dataSql}) t1";

            List<Dictionary<string, object>> datas = new List<Dictionary<string, object>>();
            using (var reader = this.DBService.ExecuteReader(this.Context, selectDataSql, sqlParam))
            {
                while (reader.Read())
                {
                    Dictionary<string, object> data = new Dictionary<string, object>();
                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        var objValue = reader[i];
                        var colName = reader.GetName(i);

                        //处理交易状态
                        if (colName.EqualsIgnoreCase("status"))
                        {
                            var strStatus = Convert.ToString(objValue).ToLower();
                            switch (strStatus)
                            {
                                case "settle_status01_01":
                                case "settle_status02_01":
                                    objValue = "等待确认";
                                    break;
                                case "settle_status02":
                                    objValue = "交易完成";
                                    break;
                                default:
                                    break;
                            }
                        }

                        data[colName] = objValue.IsNullOrEmptyOrWhiteSpace() ? "" : objValue;
                    }
                    datas.Add(data);
                }
            }

            var records = 0;
            using (var reader = this.DBService.ExecuteReader(this.Context, selectCountSql, sqlParam))
            {
                if (reader.Read())
                {
                    records = Convert.ToInt32(reader[0]);
                }
            }

            this.Result.SrvData = new
            {
                pageIndex = pageIndex,
                pageSize = pageSize,
                pageCount = decimal.ToInt64(Math.Ceiling(records * 1.0m / pageSize)),
                records = records,
                datas = datas
            };
            this.Result.IsSuccess = true;
        }
    }
}