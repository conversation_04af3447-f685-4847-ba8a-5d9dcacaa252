using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework;
using JieNor.AMS.YDJ.DataTransferObject.IncomeDisburse;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MerchantOrder
{
    /// <summary>
    /// 商户订单：验收
    /// </summary>
    [InjectService]
    [FormId("ydj_merchantorder")]
    [OperationNo("sht_acceptbill")]
    public class AcceptBill : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 实现规则校验控制
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!Convert.ToString(newData["fserstatus"]).EqualsIgnoreCase("sht_serstatus07"))
                {
                    return false;
                }
                return true;
            }).WithMessage("商户订单{0}不是已完工状态，不允许验收！", (dataObj, serStatus) => dataObj["fbillno"]));

            e.Rules.Add(this.RuleFor("fbillhead", dataObj => dataObj["fserviceid"] as string)
                .NotEmpty()
                .NotNull()
                .WithMessage("商户订单{0}没有关联服务单，不允许验收！", (dataObj, serStatus) => dataObj["fbillno"]));
        }

        /// <summary>
        /// 操作前逻辑处理
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            List<DynamicObject> lstPassedDataObjs = new List<DynamicObject>();

            var serverFormId = "ydj_service";
            var serviceDm = this.Container.GetService<IDataManager>();
            var serverHtmlForm = this.MetaModelService.LoadFormModel(this.Context, serverFormId);
            serviceDm.InitDbContext(this.Context, serverHtmlForm.GetDynamicObjectType(this.Context));

            var serviceChangeMeta = this.MetaModelService.LoadFormModel(this.Context, "ydj_servicechange");
            var serviceChangeDm = this.GetDataManager();
            serviceChangeDm.InitDbContext(this.Context, serviceChangeMeta.GetDynamicObjectType(this.Context));

            foreach (var dataEntity in e.DataEntitys)
            {
                string fserviceid = Convert.ToString(dataEntity["fserviceid"]);
                
                var serviceEntities = serviceDm.Select(fserviceid.Split(',')).OfType<DynamicObject>().ToArray();

                if (serviceEntities == null || serviceEntities.Length <= 0)
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"商户订单{dataEntity["fbillno"]}关联服务单已不存在，不需要验收！");
                    continue;
                }

                //校验是否存在关联的补价信息未处理
                var pkIdReader = this.Context.GetPkIdDataReader(serviceChangeMeta, "fdealerid=@dealerId and fcancelstatus='0'", new SqlParam[]{
                    new SqlParam("dealerId", System.Data.DbType.String, dataEntity["fmerchantid"])
                });
                var linkChangeBillObjs = serviceChangeDm.SelectBy(pkIdReader)
                    .OfType<DynamicObject>();
                if (linkChangeBillObjs.Any())
                {
                    this.Result.SimpleData["linkChangeBills"] = linkChangeBillObjs.Select(o => o["fbillno"]).Distinct().ToJson();
                    var allLinkBillNo = string.Join(",", linkChangeBillObjs.Select(o => o["fbillno"]).Distinct());
                    this.Result.ComplexMessage.ErrorMessages.Add($"商户订单{dataEntity["fbillno"]}存在关联未处理的服务变更单({allLinkBillNo})，请先处理变更后再来验收！");
                    continue;
                }

                bool checkError = false;
                foreach (var serviceEntity in serviceEntities)
                {
                    var fsettlestatus = Convert.ToString(serviceEntity["fsettlestatus"]);
                    if (!string.IsNullOrWhiteSpace(fsettlestatus) && fsettlestatus != "settle_status01")
                    {
                        this.Result.ComplexMessage.ErrorMessages.Add($"商户订单{dataEntity["fbillno"]}关联的服务单{serviceEntity["fbillno"]}不是未结算状态，不允许验收！");
                        checkError = true;
                        break;
                    }

                    var fserstatus = Convert.ToString(serviceEntity["fserstatus"]);
                    if (fserstatus != "sersta05" && fserstatus != "sersta08" && fserstatus != "sersta11")
                    {
                        this.Result.ComplexMessage.ErrorMessages.Add($"商户订单{dataEntity["fbillno"]}关联的服务单{serviceEntity["fbillno"]}不是待评价、待回访或关闭状态，不允许验收！");
                        checkError = true;
                        break;
                    }

                    var evaluate = this.GetQueryOrSimpleParam<string>("evaluate");
                    if (!string.IsNullOrWhiteSpace(evaluate))
                    {
                        serviceEntity["fevaluate"] = evaluate;
                    }

                    serviceEntity["fsettlestatus"] = "settle_status01_01";//可结算
                }

                if (checkError)
                {
                    continue;
                }

                //验收时检查是否已结算完毕，未结算则进行自动结算
                this.AutoSettleAmount(dataEntity);

                //关联服务单修改保存
                var result = this.Gateway.InvokeBillOperation(this.Context,
                                                              serverFormId,
                                                              serviceEntities,
                                                              "save",
                                                              new Dictionary<string, object> { });
                this.Result.MergeResult(result);


                

                //处理通过的商户订单记录下来
                lstPassedDataObjs.Add(dataEntity);
            }

            e.DataEntitys = lstPassedDataObjs.ToArray();
        }


        private void AutoSettleAmount(DynamicObject dataEntity)
        {
            decimal dExpectAmount = (decimal)dataEntity["fexpectamount"];
            decimal dSettleAmount = (decimal)dataEntity["fsettleamount"];
            if (Math.Abs(dSettleAmount - dExpectAmount) < 0.01m) return;

            IncomeDisburseRecord tranDto = new IncomeDisburseRecord()
            {
                //收
                Direction = "direction_02",
                BizDirection = "bizdirection_01",
                //订单扣款
                SceneType = "bizpurpose_02",
                SettleDate = DateTime.Now,
                SettleAgentId = "",
                TranFormId = "ydj_customer",
                TranBillId = dataEntity["fmerchantid"] as string,
                LinkFormId = "ydj_merchantorder",
                LinkBillId = dataEntity["id"] as string,
                LinkBillNo = dataEntity["fbillno"] as string,
                LinkTranId = dataEntity["ftranid"] as string,
                AttachId = "",
                Description = "商户验收时系统自动扣款！",
                MyBankId = "",
                SynBankId = "",
                SynBankName = "",
                SynBankNum = "",
                SynAccountName = "",
                AutoConfirm = true
            };

            List<AccountSettleInfo> accountSettleInfo = new List<AccountSettleInfo>();
            tranDto.AccountSettleInfo = accountSettleInfo;
            accountSettleInfo.Add(new AccountSettleInfo
            {
                AccountName = "货款账户",
                AccountId = "settleaccount_type_01",
                Amount = dExpectAmount - dSettleAmount,
                BankCardNo = "",
                SettleType = "payway_01"
            });

            var synAccountBalanceService = this.Container.GetService<ISynAccountBalanceService>();
            var settleResult = synAccountBalanceService.CreateIncomeDisburseRecord(this.Context, tranDto, this.Option);
            settleResult.ThrowIfHasError(true, "确认时结算订单出现异常错误！");

            this.Result.ComplexMessage.SuccessMessages.Add($"商户订单【{dataEntity["fbillno"]}】已自动扣款￥{MathUtil.Round(dExpectAmount - dSettleAmount, 2, RoundMode.AwayFromZero) }，详情可查看账户交易明细！");
        }
    }
}
