using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SER.Service
{
    [InjectService]
    [FormId("ydj_service")]
    [OperationNo("queryserviceorderbilltype")]
    public class QueryServiceOrderBillType : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            //var billTypeService = this.Context.Container.GetService<IBillTypeService>();
            var serviceBillTypeDysInfo = GetServiceBillTypeDynamicObjectInfo(this.Context,this.HtmlForm.Id);
            //var billTypeInfors = billTypeService.GetBillTypeInfors(this.Context, this.HtmlForm.Id);
            // billTypeService.PresetBillTypeParam();

            var serviceOrderBillTypeInfos = new List<ServiceOrderBillTypeInfo>();

            var resultDic = new Dictionary<string,object>();

            if (serviceBillTypeDysInfo != null && serviceBillTypeDysInfo.Any())
            {
                serviceBillTypeDysInfo = serviceBillTypeDysInfo.Where(x => !Convert.ToBoolean(Convert.ToInt32(x["fforbidstatus"]))).ToList();
                foreach (var tempBilTypeDy in serviceBillTypeDysInfo)
                {
                    var serviceOrderBillTypeInfo = new ServiceOrderBillTypeInfo();
                    serviceOrderBillTypeInfo.Id = Convert.ToString(tempBilTypeDy["id"]);
                    serviceOrderBillTypeInfo.Name = Convert.ToString(tempBilTypeDy["fname"]);
                    //前端中的值也是名称
                    serviceOrderBillTypeInfo.Number = Convert.ToString(tempBilTypeDy["fname"]);
                    
                    serviceOrderBillTypeInfos.Add(serviceOrderBillTypeInfo);
                }
            }

            /*if (billTypeInfors != null && billTypeInfors.Any())
            {
                var distinctBillTypeInfos = billTypeInfors.Where(x=> !x.fforbidstatus).Distinct(x=>x.fid).ToList();
                if (distinctBillTypeInfos != null && distinctBillTypeInfos.Any())
                {
                    foreach (var distinctBillTypeInfo in distinctBillTypeInfos)
                    {
                        var serviceOrderBillTypeInfo = new ServiceOrderBillTypeInfo();

                        serviceOrderBillTypeInfo.Id = distinctBillTypeInfo.fid;
                        serviceOrderBillTypeInfo.Name = distinctBillTypeInfo.fname;
                        //前端中的值也是名称
                        serviceOrderBillTypeInfo.Number = distinctBillTypeInfo.fname;

                        serviceOrderBillTypeInfos.Add(serviceOrderBillTypeInfo);
                    }
                }
            }*/

            if (serviceOrderBillTypeInfos != null && serviceOrderBillTypeInfos.Any())
            {
                resultDic.Add("hasBillTypeInfo",true);
            }
            else
            {
                resultDic.Add("hasBillTypeInfo", false);
            }

            resultDic.Add("serviceOrderBillTypeInfos",serviceOrderBillTypeInfos);

            this.Result.IsSuccess = true;
            this.Result.SrvData = resultDic;
        }
        
        private List<DynamicObject> GetServiceBillTypeDynamicObjectInfo(UserContext ctx,string htmlFormId)
        {
            var billTypeDys = new List<DynamicObject>();
            var serviceBillTypeForm = this.MetaModelService.LoadFormModel(ctx, "bd_billtype");
            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, serviceBillTypeForm.GetDynamicObjectType(ctx));
            var reader = ctx.GetPkIdDataReader(serviceBillTypeForm, $" fbizobject='{htmlFormId}' ", new List<SqlParam>());
            var serviceBillTypeDys = dm.SelectBy(reader).OfType<DynamicObject>();
            reader.Dispose();
            if (serviceBillTypeDys != null && serviceBillTypeDys.Any())
            {
                //根据名称去分组
                var groupByBillTypeNameDys = serviceBillTypeDys.GroupBy(x => Convert.ToString(x["fname"])).ToList();
                foreach (var groupByBillTypeNameDy in groupByBillTypeNameDys)
                {
                    var billTypeDyList = groupByBillTypeNameDy.ToList();
                    //优先找本公司下面的,没有被禁用的
                    var currentCompanyBillTypeDys = billTypeDyList.Where(x =>
                        Convert.ToString(x["fmainorgid"]).Equals(ctx.Company)).ToList();
                    if (currentCompanyBillTypeDys != null && currentCompanyBillTypeDys.Any())
                    {
                        billTypeDys.AddRange(currentCompanyBillTypeDys);
                        continue;
                    }

                    //如果没有找到，那找共享的
                    var shareBillTypeDys = billTypeDyList.Where(x =>
                                                                                                Convert.ToString(x["fmainorgid"]).Equals("0")).ToList();
                    if (shareBillTypeDys != null && shareBillTypeDys.Any())
                    {
                        billTypeDys.AddRange(shareBillTypeDys);
                        continue;
                    }
                    
                    //没有找到，那就再找总部的下面的。
                    var parentCompanyBillTypeDys = billTypeDyList.Where(x =>
                        Convert.ToString(x["fmainorgid"]).Equals(ctx.TopCompanyId)).ToList();

                    if (parentCompanyBillTypeDys != null && parentCompanyBillTypeDys.Any())
                    {
                        parentCompanyBillTypeDys.AddRange(parentCompanyBillTypeDys);
                        continue;
                    }

                   
                }
            }
            /*using ()
            {
                
            }*/
            
            return billTypeDys;
        }
    }
    
    public class ServiceOrderBillTypeInfo
    {
        public string  Id { set;get; }

        public string  Name { set; get; }

        public string Number { set; get; }

    }
}