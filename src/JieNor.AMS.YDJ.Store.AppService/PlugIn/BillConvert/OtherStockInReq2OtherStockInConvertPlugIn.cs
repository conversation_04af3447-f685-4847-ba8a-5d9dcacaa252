using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    [InjectService]
    [FormId("stk_otherstockin")]
    [OperationNo("stk_otherstockinreq2stk_otherstockin")]
    public class OtherStockInReq2OtherStockInConvertPlugIn : BaseScanTaskConvertPlugIn
    {
        
    }
}
