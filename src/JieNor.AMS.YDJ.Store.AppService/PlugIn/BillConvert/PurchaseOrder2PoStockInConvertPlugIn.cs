using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 采购订单下推销售采购入库单
    /// </summary>
    [InjectService]
    [FormId("stk_postockin")]
    [OperationNo("ydj_purchaseorder2stk_postockin")]
    public class PurchaseOrder2PoStockInConvertPlugIn : AbstractConvertServicePlugIn
    {
        private JArray orders = null;
        /// <summary>
        /// 默认仓库
        /// </summary>
        private string defaultStoreId = string.Empty;
        /// <summary>
        /// 默认仓位
        /// </summary>
        private string defaultLocationId = string.Empty;
        protected IEnumerable<Dictionary<string, object>> InstockData { get; set; }
        ///// <summary>
        ///// 是否二级经销商
        ///// </summary>
        //private bool isSecondOrg;
        protected override void OnInitialized(InitializeServiceEventArgs e)
        {
            base.OnInitialized(e);
            orders = this.Option.GetVariableValue<JArray>("orders", null);
            var profileService = this.UserContext.Container.GetService<ISystemProfile>();
            defaultStoreId = profileService.GetSystemParameter(UserContext, "stk_stockparam", "fdefaultstoreid", string.Empty);
            defaultLocationId = profileService.GetSystemParameter(UserContext, "stk_stockparam", "fdefaultlocationid", string.Empty);
            var currentAgent = this.UserContext.LoadBizBillHeadDataById("bas_agent", this.UserContext.Company, "fisreseller");
            //isSecondOrg = currentAgent!= null && Convert.ToString(currentAgent["fisreseller"]) == "1";

            if (this.UserContext.IsDirectSale)
            {
                var inStockDic = new List<Dictionary<string, object>>();
                string recDatas = "";
                this.Option.TryGetVariableValue("recDatas", out recDatas);
                if (recDatas != null)
                {
                    inStockDic = recDatas == null ? new List<Dictionary<string, object>>() : JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(recDatas);
                }
                this.InstockData = inStockDic;
            }

        }


        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);
            var dataEntities = e.TargetDataEntities?.ToList();
            if (dataEntities == null || dataEntities.Count <= 0)
            {
                return;
            }

            //过滤套件头商品
            RemoveKitHeadProduct(e.TargetDataEntities);
            string errormsg = "";
            //要过滤的ID
            var filterid = new List<string>();
            //验证过滤
            CheckFbizqty(filterid, e.TargetDataEntities.ToArray(), out errormsg);
            //修改下推数量并返回下推行
            var result = ModifyFqty(e.TargetDataEntities.ToArray(), filterid);

            if (result == 0)
            {
                throw new BusinessException(errormsg);
            }
        }

        /// <summary>
        /// 修改下推数量
        /// </summary>
        /// <param name="dataEntities"></param>
        private int ModifyFqty(DynamicObject[] dataEntities, List<string> filter)
        {
            int count = 0;
            foreach (var item in dataEntities)
            {
                var fsourceentryid = "fsourceentryid";
                var fentries = item["fentity"] as DynamicObjectCollection;
                // 计算采购入库单.入库明细的【实收数量】汇总
                var stockDic = GetStockQty(fentries);
                // 计算采购订单.端口明细的【采购数量】
                var purDic = GetPurQty(fentries);
                var rmData = new List<DynamicObject>();
                //排除已选单过且用户修改过的数据--bug#24111
                foreach (var ent in fentries?.Where(x => x["id"].IsNullOrEmptyOrWhiteSpace()))
                {
                    if (!ent[fsourceentryid].IsNullOrEmptyOrWhiteSpace() && filter.Contains(ent[fsourceentryid].ToString()))
                    {
                        rmData.Add(ent);
                        continue;
                    }


                    var fpoorderentryid = Convert.ToString(ent[fsourceentryid]);
                    var newqty = Convert.ToInt32(ent["forderqty"]);
                    //http://dmp.jienor.com:81/zentao/bug-view-24700.html 数量可能为浮点数，需求要求实收数量不能四舍五入转整数，其它不调整
                    var fbizqty = Convert.ToDecimal(ent["forderqty"]);
                    if (stockDic.ContainsKey(fpoorderentryid))
                    {
                        // 29396 采购订单下推生成的《采购入库单.入库明细》的【应收数量】应等于源单采购订单.商品明细的【采购数量】-采购入库单.入库明细的【实收数量】汇总
                        newqty = purDic[fpoorderentryid] - stockDic[fpoorderentryid];
                        fbizqty = purDic[fpoorderentryid] - stockDic[fpoorderentryid];
                    }
                    decimal orderQty = fbizqty;
                    if (InstockData != null && InstockData.Count() > 0 && this.UserContext.IsDirectSale)
                    {
                        string srcEntryId = Convert.ToString(ent["fsourceentryid"]);
                        Dictionary<string, object> existReturnObj = null;
                        existReturnObj = this.InstockData.FirstOrDefault(o => o.GetString("fpoentryid").EqualsIgnoreCase(srcEntryId));

                        object _bizqty = null;
                        if (existReturnObj != null && existReturnObj.TryGetValue("fbizqty", out _bizqty))
                        {
                            if (_bizqty != null && Convert.ToDecimal(_bizqty) <= fbizqty)
                            {
                                newqty = Convert.ToInt32(_bizqty);
                                fbizqty = Convert.ToDecimal(_bizqty);
                            }
                        }

                    }
                    ent["fplanqty"] = fbizqty;
                    ent["fbizqty"] = fbizqty;
                    ent["fqty"] = fbizqty;
                    ent["fbizplanqty"] = fbizqty;
                    ent["fbizorderqty"] = fbizqty;
                    ent["forderqty"] = orderQty;
                    ent["fpoamount"] = fbizqty * Convert.ToDecimal(ent["fpoprice"]);
                    ent["famount"] = fbizqty * Convert.ToDecimal(ent["fprice"]);
                    count++;
                }
                //删除过滤行
                foreach (var val in rmData)
                {
                    fentries.Remove(val);
                }
            }
            return count;
        }

        /// <summary>
        /// 得到已入库数量
        /// </summary>
        /// <param name="entries"></param>
        private Dictionary<string, int> GetStockQty(DynamicObjectCollection entries)
        {
            var field = "fpoorderentryid";
            var ids = entries.Where(x => !x[field].IsNullOrEmptyOrWhiteSpace()).Select(x => Convert.ToString(x[field])).ToList();
            //存储 入库明细的【实收数量】汇总
            Dictionary<string, int> dic = new Dictionary<string, int>();
            if (ids.IsNullOrEmpty()) return dic;

            string strSql = @"select isnull(sum(fbizqty-fbizreturnqty),0) fbizqty,isnull(min(fsourceentryid),'') fsourceentryid from t_stk_postockin as a inner join  t_stk_postockinentry  as b on a.fid=b.fid where a.fcancelstatus=0 and fsourceentryid in ('{0}') group by fsourceentryid".Fmt(string.Join("','", ids));
            //存储 入库明细的【实收数量】汇总
            using (var dr = this.UserContext.ExecuteReader(strSql, new List<SqlParam>()))
            {
                while (dr.Read())
                {
                    dic[dr["fsourceentryid"].ToString()] = Convert.ToInt32(dr["fbizqty"]);
                }
            }
            return dic;
        }

        /// <summary>
        /// 得到采购数量
        /// </summary>
        /// <param name="entries"></param>
        private Dictionary<string, int> GetPurQty(DynamicObjectCollection entries)
        {
            var field = "fpoorderentryid";
            var ids = entries.Where(x => !x[field].IsNullOrEmptyOrWhiteSpace()).Select(x => x[field].ToString()).ToList();
            //存储源单明细的【采购数量】
            Dictionary<string, int> dic = new Dictionary<string, int>();
            if (ids.IsNullOrEmpty()) return dic;

            string strSql = @"select fentryid, fbizqty from t_ydj_poorderentry where fentryid in ('{0}')".Fmt(string.Join("','", ids));

            using (var dr = this.UserContext.ExecuteReader(strSql, new List<SqlParam>()))
            {
                while (dr.Read())
                {
                    dic[dr["fentryid"].ToString()] = Convert.ToInt32(dr["fbizqty"]);
                }
            }

            return dic;
        }

        public override void BeforeMapFieldValue(BeforeMapFieldValueEventArgs e)
        {
            base.BeforeMapFieldValue(e);

            if (e.SourceDataEntities.Any() == false)
            {
                return;
            }

            var targetFldKey = e.FieldMapObject?.Id;
            var targetField = this.TargetHtmlForm?.GetField(targetFldKey);
            if (targetField == null)
            {
                return;
            }

            var sourceDataEntity = e.SourceDataEntities.First();
            if (sourceDataEntity == null)
            {
                return;
            }

            var srcEntryId = e.SourceDataEntities.First().GetString("fentity_id");
            Dictionary<string, object> existReturnObj = null;
            if (this.UserContext.IsDirectSale)
            {
                existReturnObj = this.InstockData.FirstOrDefault(o => o.GetString("fpoentryid").EqualsIgnoreCase(srcEntryId));
            }
            //if (existReturnObj == null) return;

            bool isCancel = false;
            object targetValue = null;
            switch (targetField.Id.ToLower())
            {
                case "fstockstaffid":
                    if (this.UserContext.IsSecondOrg)
                    {
                        isCancel = true;
                        targetValue = sourceDataEntity.GetValue("fpostaffid");
                    }
                    break;
                case "fstockdeptid":
                    if (this.UserContext.IsSecondOrg)
                    {
                        isCancel = true;
                        targetValue = sourceDataEntity.GetValue("fpodeptid");
                    }
                    break;
                case "fstorehouseid":
                    if (this.UserContext.IsSecondOrg)
                    {
                        isCancel = true;
                        targetValue = defaultStoreId;
                    }
                    if (this.UserContext.IsDirectSale)
                    {
                        object fstorehouseid = "";
                        if (existReturnObj != null && existReturnObj.TryGetValue("fstorehouseid", out fstorehouseid))
                        {
                            isCancel = true;
                            targetValue = fstorehouseid;
                        }
                    }
                    break;
                case "fstorelocationid":
                    if (this.UserContext.IsSecondOrg)
                    {
                        isCancel = true;
                        targetValue = defaultLocationId;
                    }
                    if (this.UserContext.IsDirectSale)
                    {
                        object fstorelocationid = "";
                        if (existReturnObj != null && existReturnObj.TryGetValue("fstorelocationid", out fstorelocationid))
                        {
                            isCancel = true;
                            targetValue = fstorelocationid;
                        }
                    }
                    break;
                case "fqty":
                case "fstockqty":
                    if (this.UserContext.IsDirectSale)
                    {

                        object returnQty = 0m;
                        if (existReturnObj != null && existReturnObj.TryGetValue("fbizqty", out returnQty))
                        {
                            isCancel = true;
                            targetValue = returnQty;
                        }
                    }
                    break;
                case "freceptionid":
                    if (this.UserContext.IsDirectSale)
                    {
                        object feedbackinterid = "";
                        if (existReturnObj != null && existReturnObj.TryGetValue("freceptionid", out feedbackinterid))
                        {
                            isCancel = true;
                            targetValue = feedbackinterid;
                        }
                    }
                    break;
                case "freceptionentryid":
                    if (this.UserContext.IsDirectSale)
                    {
                        object feedbackentryid = "";
                        if (existReturnObj != null && existReturnObj.TryGetValue("freceptionentryid", out feedbackentryid))
                        {
                            isCancel = true;
                            targetValue = feedbackentryid;
                        }
                    }
                    break;
            }
            if (isCancel)
            {
                e.Cancel = true;
                targetField.DynamicProperty.SetValue(e.TargetEntryDataEntity, targetValue);
            }
            if (orders == null || orders.Count <= 0)
            {
                return;
            }
            var order = this.orders.FirstOrDefault(x => (string)x["trandId"] == sourceDataEntity.GetString("ftainid"));
            if (order == null)
            {
                return;
            }

            var entries = order["entries"] as JArray;
            if (entries == null || entries.Count <= 0)
            {
                return;
            }

            var entry = entries.FirstOrDefault(x => Convert.ToString(x["trandId"]) == sourceDataEntity.GetString("fentity_ftainid")) as JObject;
            if (entry == null)
            {
                return;
            }

            var qty = entry.Property("qty");
            switch (targetField.Id.ToLower())
            {
                case "fqty":
                case "fstockqty":
                    if (qty != null)
                    {
                        isCancel = true;
                        targetValue = (decimal)qty.Value;
                    }
                    break;
                case "famount":
                    var dPrice = Convert.ToDecimal(sourceDataEntity.GetValue("fprice", 0m));
                    if (qty != null)
                    {
                        isCancel = true;
                        targetValue = dPrice * ((decimal)qty.Value);
                    }
                    break;
                case "freturnqty":
                    var returnQty = entry.Property("returnQty");
                    if (returnQty != null)
                    {
                        isCancel = true;
                        targetValue = (decimal)returnQty.Value;
                    }
                    break;
                case "fplanqty":
                    var planQty = entry.Property("planQty");
                    if (planQty != null)
                    {
                        isCancel = true;
                        targetValue = (decimal)planQty.Value;
                    }
                    break;
            }

            if (isCancel)
            {
                e.Cancel = true;
                targetField.DynamicProperty.SetValue(e.TargetEntryDataEntity, targetValue);
            }
        }

        /// <summary>
        /// 校验采购数量是否大于入库明细的【实收数量】汇总
        /// </summary>
        /// <param name="dic"></param>
        /// <param name="entityids"></param>
        private void CheckFbizqty(List<string> filterlist, DynamicObject[] dataEntities, out string errormsg)
        {
            var refObjMgr = this.UserContext.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.UserContext, dataEntities.First().DynamicObjectType, dataEntities, false);

            errormsg = "";
            var ids = new List<string>();
            var fsourceentryid = "fsourceentryid";
            //存储 入库明细的【实收数量】汇总
            Dictionary<string, QtpInstock> dic = new Dictionary<string, QtpInstock>();

            foreach (var val in dataEntities)
            {
                var entries = val["fentity"] as DynamicObjectCollection;
                ids.AddRange(entries.Where(x => !x[fsourceentryid].IsNullOrEmptyOrWhiteSpace()).Select(x => x[fsourceentryid].ToString()).ToList());
            }
            string strSql = @"select isnull(min(t2.fmaterialid),'') fmaterialid,min(t2.fseq) fseq,isnull(min(t2.fbizqty),0) fbizqty,
                                    isnull(sum(t1.fbizqty-t1.fbizreturnqty),0) sumfbizqty,isnull(min(t1.fsourceentryid),'') fsourceentryid 
                from t_stk_postockinentry t1
                join t_ydj_poorderentry t2 on t1.fsourceentryid=t2.fentryid
                join t_stk_postockin t3 on t1.fid=t3.fid
                where t3.fcancelstatus=0 and t1.fsourceentryid in ('{0}') group by t1.fsourceentryid ".Fmt(string.Join("','", ids));
            //存储 入库明细的【实收数量】汇总
            using (var dr = this.UserContext.ExecuteReader(strSql, new List<SqlParam> { }))
            {
                while (dr.Read())
                {
                    dic[dr["fsourceentryid"].ToString()] = new QtpInstock()
                    {
                        fseq = Convert.ToInt32(dr["fseq"]),
                        fbizqty = Convert.ToInt32(dr["fbizqty"]),
                        sumfbizqty = Convert.ToInt32(dr["sumfbizqty"]),
                        fsourceentryid = Convert.ToString(dr["fsourceentryid"]),
                        fmaterialid = Convert.ToString(dr["fmaterialid"]),
                    };
                }
            }

            foreach (var val in dataEntities)
            {
                var entries = val["fentity"] as DynamicObjectCollection;
                var i = 0;
                foreach (var item in entries)
                {
                    i++;
                    var id = Convert.ToString(item[fsourceentryid]);
                    if (dic.ContainsKey(id))
                    {
                        var mdl = dic[id];
                        //采购数量
                        var pur_fbizqty = mdl.fbizqty;
                        //入库实收数量汇总
                        var sumfbizqty = mdl.sumfbizqty;
                        //商品
                        var matObj = item["fmaterialid_ref"] as DynamicObject;
                        if (matObj == null)
                        {
                            continue;
                        }

                        if (pur_fbizqty - sumfbizqty <= 0)
                        {
                            errormsg += "第" + mdl.fseq + "行商品【" + Convert.ToString(matObj["fname"]) + "】的采购数量[" + pur_fbizqty + "]应大于已下推的采购入库单的累计实收数量[" + sumfbizqty + "]，才允许入库！ \r\n";
                            filterlist.Add(id);
                        }
                        //else if (pur_fbizqty - sumfbizqty == 0)
                        //{
                        //    errormsg += "第" + mdl.fseq + "行商品【" + Convert.ToString(product["fname"]) + "】的采购数量[" + pur_fbizqty + "]等于已下推的采购入库单的累计实收数量[" + sumfbizqty + "]，已完全入库！ \r\n";
                        //    filterlist.Add(id);
                        //}
                    }
                }
            }
        }

        /// <summary>
        /// 过滤套件头商品（解决整单下推的时候pull不会过滤）
        /// </summary>
        /// <param name="dataEntities"></param>
        private void RemoveKitHeadProduct(IEnumerable<DynamicObject> dataEntities)
        {
            if (dataEntities == null || dataEntities.Count() <= 0) return;

            var allEntrys = dataEntities?.SelectMany(t => t["fentity"] as DynamicObjectCollection);
            var materialIds = allEntrys.Select(entry => Convert.ToString(entry["fmaterialid"])).Where(materialid => !materialid.IsNullOrEmptyOrWhiteSpace())?.Distinct()?.ToList();
            var suiteHeadEntryIds = new List<string>();
            if (materialIds != null && materialIds.Any())
            {
                var productObjs = this.UserContext.LoadBizBillHeadDataById("ydj_product", materialIds, "fsuiteflag");
                var suiteHeadIds = productObjs.Where(t => Convert.ToString(t["fsuiteflag"]).ToLower() == "true" || Convert.ToString(t["fsuiteflag"]) == "1")
                                              .Select(m => Convert.ToString(m["id"])).ToList();//套件头商品ID

                foreach (var dataEntity in dataEntities)
                {
                    var entries = dataEntity["fentity"] as DynamicObjectCollection;
                    List<DynamicObject> beRemove = new List<DynamicObject>();
                    foreach (var entry in entries)
                    {
                        if (suiteHeadIds.Contains(Convert.ToString(entry["fmaterialid"])))
                        {
                            beRemove.Add(entry);
                        }
                    }
                    foreach (var entry in beRemove)
                    {
                        entries.Remove(entry);
                    }
                }
            }
        }
    }

    public class QtpInstock
    {
        public QtpInstock() { }

        public QtpInstock(int fseq, int fbizqty, int sumfbizqty, string fsourceentryid, string fmaterialid)
        {
            this.fmaterialid = fmaterialid;
            this.fseq = fseq;
            this.fbizqty = fbizqty;
            this.sumfbizqty = sumfbizqty;
            this.fsourceentryid = fsourceentryid;
        }

        public string fmaterialid { get; set; }
        public int fseq { get; set; }
        public int fbizqty { get; set; }
        public int sumfbizqty { get; set; }
        public string fsourceentryid { get; set; }
    }
}
