using JieNor.AMS.YDJ.Core.Interface.Stock;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.YDJService
{
	/// <summary>
	/// 销售合同下推销售出库单
	/// </summary>
	[InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("ydj_order2stk_sostockout_shipper")]
    public class SalOrder2OutStockShipperConvertPlugIn : AbstractConvertServicePlugIn
	{
		public override void BeforeMapFieldValue(BeforeMapFieldValueEventArgs e)
		{
			base.BeforeMapFieldValue(e);

			if (e.SourceDataEntities.Any() == false)
			{
				return;
			}

			var targetFldKey = e.FieldMapObject?.Id;
			var targetField = this.TargetHtmlForm?.GetField(targetFldKey);
			if (targetField == null)
			{
				return;
			}

			var sourceDataEntity = e.SourceDataEntities.First();
			if (sourceDataEntity == null)
			{
				return;
			}

			bool isCancel = false;
			object targetValue = null;
			switch (targetField.Id.ToLower())
			{
				case "fconsignee":
					{
						isCancel = true;
						targetValue = "";
						var customercontactid = sourceDataEntity.GetValue("fcustomercontactid");
						if (!customercontactid.IsNullOrEmptyOrWhiteSpace())
						{
							var sqlText = $@"select fcuscontacttryid,fcontacter from t_ydj_fcuscontacttry with(nolock) 
                                        where fcuscontacttryid='{customercontactid}'";
							var dbService = this.UserContext.Container.GetService<IDBService>();
							var dynObj = dbService.ExecuteDynamicObject(this.UserContext, sqlText)?.FirstOrDefault();
							if (dynObj != null) targetValue = dynObj["fcontacter"];
						}
					}
					break;
			}
			if (isCancel)
			{
				e.Cancel = true;
				targetField.DynamicProperty.SetValue(e.TargetEntryDataEntity, targetValue);
			}
		}

		/// <summary>
		/// 单据转换执行完成后接口（目标单数据包将会生成）
		/// </summary>
		/// <param name="e"></param>
		public override void OnConvertComplete(OnConvertCompleteEventArgs e)
		{
			base.OnConvertComplete(e);

			if (e.TargetDataEntities == null || e.TargetDataEntities.Count() <= 0) return;

			var inventoryService = this.UserContext.Container.GetService<IInventoryService>();
			inventoryService.FillReserveStockInfos(this.UserContext, this.TargetHtmlForm, e.TargetDataEntities);
			inventoryService.FillFIFOStockInfos(this.UserContext, this.TargetHtmlForm, e.TargetDataEntities, false);


			if (e.ConvertMode == Enu_BillConvertMode.Default)
			{
				//仓库推荐完成后，按照商品明细仓库拆单
				List<DynamicObject> saveTargetDatas = new List<DynamicObject>();
				foreach (var targetData in e.TargetDataEntities)
				{
					var currEntrys = targetData["fentity"] as DynamicObjectCollection;
					var storeGroup = currEntrys.GroupBy(t => System.Convert.ToString(t["fstorehouseid"]));
					if (storeGroup.Count() > 1)
					{
						var nullData = targetData.Clone() as DynamicObject;
						foreach (var group in storeGroup)
						{
							var newData = targetData.Clone() as DynamicObject;
							var newEntrys = newData["fentity"] as DynamicObjectCollection;
							newEntrys.Clear();
							foreach (var ent in group)
							{
								newEntrys.Add(ent);
							}

							if (group.Key.IsNullOrEmptyOrWhiteSpace())
							{
								nullData = newData;
								continue;
							}
							saveTargetDatas.Add(newData);
						}

						if (storeGroup.Where(x => x.Key.IsNullOrEmptyOrWhiteSpace()).Any())
						{
							saveTargetDatas.Add(nullData);
						}
					}
					else
					{
						saveTargetDatas.Add(targetData);
					}
				}
				e.SetTargetDataEntities(saveTargetDatas);
			}
		}
    }
}