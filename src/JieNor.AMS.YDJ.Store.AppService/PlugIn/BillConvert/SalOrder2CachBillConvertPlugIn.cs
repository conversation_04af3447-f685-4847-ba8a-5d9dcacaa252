using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    [InjectService]
    [FormId("ydj_cash")]
    [OperationNo("ydj_order2ydj_cash")]
    public class SalOrder2CachBillConvertPlugIn : AbstractConvertServicePlugIn
    {
        public override void OnPrepareQueryBuilderParameter(OnPrepareQueryBuilderParameterEventArgs e)
        {
            if (e.ActiveQueryEntity == null) return;
            switch (e.ActiveQueryEntity.Id.ToLower())
            {
                case "fbillhead":
                    e.SqlBuilderParameter.SelectedFieldKeys.Add("freceivable");
                    break;
            }
        }
        
        public override void OnCreateLinkData(OnCreateLinkDataEventArgs e)
        {
            if (e.LinkEntryData == null) return;
            if (e.LinkRelation == null) return;

            e.LinkEntryData["FSrcBizType"] = "货款";
            object objRecvAmtVal;
            e.LinkResult.LinkSourceObject.TryGetValue("freceivable", out objRecvAmtVal);
            e.LinkEntryData["FReceivedAmount"] = objRecvAmtVal;
        }
    }
}
