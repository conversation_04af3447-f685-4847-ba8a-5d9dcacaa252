using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 销售合同下推采购订单的单据转换插件（总部下单）
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("ydj_order2ydj_purchaseorder2")]
    public class Order2PurchaseOrderConvertPlugIn: AbstractConvertServicePlugIn
    {
        /// <summary>
        /// 退货单字段计算逻辑处理
        /// </summary>
        /// <param name="e"></param>
        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);

            if (e.TargetDataEntities?.Any() != true)
            {
                throw new BusinessException("总部下单失败，未能生成采购订单！");
            }

            var htmlForm = this.MetaModelService.LoadFormModel(this.UserContext, e.TargetFormId);
            foreach(var dataEntity in e.TargetDataEntities)
            {
                dataEntity[htmlForm.TranFldKey] = string.Empty;
                dataEntity[htmlForm.ParentTranFldKey] = string.Empty;
                dataEntity[htmlForm.TopTranFldKey] = string.Empty;
                dataEntity[htmlForm.DataOriginFldKey] = string.Empty;
            }


        }
    }
}
