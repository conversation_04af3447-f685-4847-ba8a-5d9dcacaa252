using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface.Stock;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.Utils;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 销售合同下推库存调拨单的单据转换插件
    /// </summary>
    [InjectService]
    [FormId("stk_inventorytransfer")]
    [OperationNo("ydj_order2stk_inventorytransfer")]
    public class Order2InventoryTransferConvertPlugIn : AbstractConvertServicePlugIn
    {
        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);

            if (e.TargetDataEntities == null || e.TargetDataEntities.Count() <= 0) return;

            var productForm = this.MetaModelService.LoadFormModel(this.UserContext, "ydj_product");
            var dm = this.UserContext.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserContext, productForm.GetDynamicObjectType(this.UserContext));

            foreach (var target in e.TargetDataEntities)
            {
                var entrys = target["fentity"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    //如果库存单位字段没有值，则默认获取商品的库存单位
                    var productId = entry["fmaterialid"] as string;
                    if (!productId.IsNullOrEmptyOrWhiteSpace() && entry["fstockunitid"].IsNullOrEmptyOrWhiteSpace())
                    {
                        var product = dm.Select(productId) as DynamicObject;
                        if (product != null)
                        {
                            entry["fstockunitid"] = product["fstockunitid"];
                        }
                    }
                }
            }

            var inventoryService = this.UserContext.Container.GetService<IInventoryService>();
            inventoryService.FillReserveStockInfos(this.UserContext, this.TargetHtmlForm, e.TargetDataEntities);
            //去除没有预留的商品行
            this.DealTargetDatas(e);
            //排除后检查是否还存在有效数据
            if (e.TargetDataEntities == null || !e.TargetDataEntities.Any())
            {
                throw new BusinessException("销售合同未预留，禁止借货操作，请先完成预留！");
            }
            //按先进先出原则自动推荐库位
            inventoryService.FillFIFOStockInfos(this.UserContext, this.TargetHtmlForm, e.TargetDataEntities, false);

            if (e.ConvertMode == Enu_BillConvertMode.Default)
            {
                var profileService = this.UserContext.Container.GetService<ISystemProfile>();
                //如果开启库存调拨单按仓库调拨
                var fistransferbystorehouse = profileService.GetSystemParameter(this.UserContext, "stk_stockparam", "fistransferbystorehouse", false);
                if (fistransferbystorehouse)
                {
                    //参数开启，按照调出仓库拆单
                    List<DynamicObject> saveTargetDatas = new List<DynamicObject>();
                    foreach (var targetData in e.TargetDataEntities)
                    {
                        var currEntrys = targetData["fentity"] as DynamicObjectCollection;
                        var storeGroup = currEntrys.GroupBy(t => Convert.ToString(t["fstorehouseid"]));
                        if (storeGroup.Count() > 1)
                        {
                            foreach (var group in storeGroup)
                            {
                                var newData = targetData.Clone() as DynamicObject;
                                newData["fstorehouseid"] = group.Key;//设置表头调出仓库
                                var newEntrys = newData["fentity"] as DynamicObjectCollection;
                                newEntrys.Clear();
                                foreach (var ent in group)
                                {
                                    newEntrys.Add(ent);
                                }
                                saveTargetDatas.Add(newData);
                            }
                        }
                        else
                        {
                            targetData["fstorehouseid"] = storeGroup.First().Key;//设置表头调出仓库
                            saveTargetDatas.Add(targetData);
                        }
                    }
                    if (this.UserContext.IsDirectSale)
                    {
                        List<string> storeHouseIds = saveTargetDatas.Select(a => Convert.ToString(a["fstorehouseid"])).Distinct().ToList();
                        var storeHouseObjs = this.UserContext.LoadBizDataById("ydj_storehouse", storeHouseIds);
                        foreach (var item in saveTargetDatas)
                        {
                            var storeHouseObj = storeHouseObjs.Where(a => Convert.ToString(a["id"]).Equals(Convert.ToString(item["fstorehouseid"]))).FirstOrDefault();
                            if (storeHouseObj != null)
                            {
                                if (Convert.ToString(storeHouseObj["fwarehousetype"]).Equals("warehouse_01") || Convert.ToString(storeHouseObj["fwarehousetype"]).Equals("warehouse_04"))
                                {
                                    item["fstore"] = "";
                                }
                                else if (Convert.ToString(storeHouseObj["fwarehousetype"]).Equals("warehouse_02") || Convert.ToString(storeHouseObj["fwarehousetype"]).Equals("warehouse_04"))
                                {

                                    item["fstore"] = Convert.ToString(storeHouseObj["fmulstore"]).IndexOf(",") < 0 ? Convert.ToString(storeHouseObj["fmulstore"]) : "";
                                }
                            }
                        }
                    }
                    e.SetTargetDataEntities(saveTargetDatas);
                }
            }
            else
            {
                foreach (var targetData in e.TargetDataEntities)
                {
                    var currEntrys = targetData["fentity"] as DynamicObjectCollection;
                    var storeGroup = currEntrys.Where(x => !Convert.ToString(x["fstorehouseid"]).IsNullOrEmptyOrWhiteSpace()).GroupBy(t => Convert.ToString(t["fstorehouseid"]));
                    if (storeGroup != null && storeGroup.Count() > 0)
                    {
                        targetData["fstorehouseid"] = storeGroup.First().Key;//设置表头调出仓库
                    }
                }
                if (this.UserContext.IsDirectSale)
                {
                    List<string> storeHouseIds = e.TargetDataEntities.Select(a => Convert.ToString(a["fstorehouseid"])).Distinct().ToList();
                    var storeHouseObjs = this.UserContext.LoadBizDataById("ydj_storehouse", storeHouseIds);
                    foreach (var item in e.TargetDataEntities)
                    {
                        var storeHouseObj = storeHouseObjs.Where(a => Convert.ToString(a["id"]).Equals(Convert.ToString(item["fstorehouseid"]))).FirstOrDefault();
                        if (storeHouseObj != null)
                        {
                            if (Convert.ToString(storeHouseObj["fwarehousetype"]).Equals("warehouse_01") || Convert.ToString(storeHouseObj["fwarehousetype"]).Equals("warehouse_04"))
                            {
                                item["fstore"] = "";
                            }
                            else if (Convert.ToString(storeHouseObj["fwarehousetype"]).Equals("warehouse_02") || Convert.ToString(storeHouseObj["fwarehousetype"]).Equals("warehouse_04"))
                            {

                                item["fstore"] = Convert.ToString(storeHouseObj["fmulstore"]).IndexOf(",") < 0 ? Convert.ToString(storeHouseObj["fmulstore"]) : "";
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 只允许合同有预留的商品下推
        /// </summary>
        /// <param name="e"></param>
        private void DealTargetDatas(OnConvertCompleteEventArgs e)
        {
            //如果是预留单据，业务要求：合同没被预留的商品不允许调拨，包括选单调拨及下推调拨。例如：合同商品行销售数量为2，预留为1，选单调拨或者下推调拨只能调拨1一个即被预留的那1个数量
            List<DynamicObject> saveTargetDatas = new List<DynamicObject>();
            foreach (var item in e.TargetDataEntities)
            {
                var newData = item.Clone() as DynamicObject;
                var newEntrys = newData["fentity"] as DynamicObjectCollection;
                newEntrys.Clear();
                var addEntity = (item["fentity"] as DynamicObjectCollection)?.Where(x => !Convert.ToString(x["fstorehouseid"]).IsNullOrEmptyOrWhiteSpace());
                if (addEntity != null && addEntity.Count() > 0)
                {
                    addEntity.ForEach(x => { newEntrys.Add(x); });
                }
                //如果排查了所有明细行，则整单排除
                if (newEntrys.Any())
                {
                    saveTargetDatas.Add(newData);
                }
            }
            e.SetTargetDataEntities(saveTargetDatas);
        }
    }
}