using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 库存调拨单->包装清单的单据转换插件
    /// </summary>
    [InjectService]
    [FormId("bcm_packorder")]
    [OperationNo("stk_inventorytransfer2bcm_packorder")]
    public class Inventorytransfer2PackOrderConvertPlugIn : AbstractConvertServicePlugIn
    {
        public override void BeforeMapFieldValue(BeforeMapFieldValueEventArgs e)
        {
            base.BeforeMapFieldValue(e);
            //判断是否形态转换
            var fbillno = Convert.ToString(e.SourceDataEntities.FirstOrDefault()?["fbillno"]);
            var SourceObj = this.UserContext.LoadBizBillHeadDataByACLFilter("stk_inventorytransfer", $"fbillno = '{fbillno}'", "ftype")?.FirstOrDefault();
            if (SourceObj.IsNullOrEmptyOrWhiteSpace()) return;
            var ftype = Convert.ToString(SourceObj?["ftype"] ?? "");

            if (!ftype.EqualsIgnoreCase("2")) return;

            if (e.SourceDataEntities.Any() == false)
            {
                return;
            }

            var targetFldKey = e.FieldMapObject?.Id;
            var targetField = this.TargetHtmlForm?.GetField(targetFldKey);
            if (targetField == null)
            {
                return;
            }

            var sourceDataEntity = e.SourceDataEntities.First();
            if (sourceDataEntity == null)
            {
                return;
            }

            bool isCancel = false;
            object targetValue = null;  

            switch (targetField.Id.ToLower())
            { 
                case "fattrinfo":
                    var srcEntryId = e.SourceDataEntities.First().GetString("fentity_id");
                    var sqlText = $@"SELECT fattrinfoto FROM t_stk_invtransferentry with(nolock) WHERE fentryid='{srcEntryId}' ";
                    var dbService = this.UserContext.Container.GetService<IDBService>();
                    var dynObj = dbService.ExecuteDynamicObject(this.UserContext, sqlText)?.FirstOrDefault(); 
                    //形态转换条码主档的辅助属性要取调入辅助属性。 
                    if (dynObj != null)
                    {
                        isCancel = true;
                        targetValue = dynObj["fattrinfoto"];
                    }
                    break; 
            }

            if (isCancel)
            {
                e.Cancel = true;
                targetField.DynamicProperty.SetValue(e.TargetEntryDataEntity, targetValue);
            }
        }
        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);

            if (e.TargetDataEntities == null || e.TargetDataEntities.Count() <= 0) return;

            var dataEntities = e.TargetDataEntities.ToArray();

            var datas = dataEntities.SelectMany(x =>
            {
                var fentry = x["fentity"] as DynamicObjectCollection;
                return fentry.Where(z => !Convert.ToString(z["fmaterialid"]).IsNullOrEmptyOrWhiteSpace()).Select(y =>
                {
                    return new
                    {
                        fmaterialid = (string)(y["fmaterialid"])//商品id
                    };
                });
            });

            SetProductInfo(datas.Select(x => x.fmaterialid).Distinct().ToList(), dataEntities);
        }

        /// <summary>
        /// 根据商品打包类型设置打包类型,包件数和待扫描包数
        /// </summary>
        /// <param name="productIds"></param>
        /// <param name="dataEntities"></param>
        private void SetProductInfo(List<string> productIds, DynamicObject[] dataEntities)
        {
            var productForm = this.MetaModelService.LoadFormModel(this.UserContext, "ydj_product");
            var dm = this.UserContext.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserContext, productForm.GetDynamicObjectType(this.UserContext));
            var products = dm.Select(productIds).OfType<DynamicObject>().ToList();
            if (products == null || products.Count <= 0)
            {
                return;
            }

            foreach (var dataEntity in dataEntities)
            {
                var fentries = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var fentry in fentries)
                {

                    var productId = (string)(fentry["fmaterialid"]);
                    var product = products.FirstOrDefault(x => (string)(x["id"]) == productId);
                    if (product != null)
                    {
                        fentry["fpacktype"] = product["fpackagtype"];
                        switch (product["fpackagtype"])
                        {
                            case "1":
                                fentry["fpackcount"] = "1";
                                break;
                            case "2":
                                fentry["fpackcount"] = product["fbag"];
                                break;
                            case "3":
                                fentry["fpackcount"] = product["fpiece"];
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
        }
    }
}
