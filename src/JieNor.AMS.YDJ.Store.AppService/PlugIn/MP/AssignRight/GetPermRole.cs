using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MP.AssignRight
{
    /// <summary>
    /// 小程序角色授权：获取角色列表
    /// </summary>
    [InjectService]
    [FormId("mp_assignright")]
    [OperationNo("GetPermRole")]
    public class GetPermRole : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var roleMeta = this.MetaModelService.LoadFormModel(this.Context, "sec_role");
            SqlBuilderParameter sqlPara = new SqlBuilderParameter(this.Context, roleMeta);
            sqlPara.PageCount = -1;
            sqlPara.PageIndex = -1;
            sqlPara.NoIsolation = false;
            if (this.Context.IsOperationCompany)
            {
                //运维组织下授权，只显示本组织下角色，不显示业务组织下私有角色
                sqlPara.FilterString = "fmainorgid=@currentCompanyId";
            }
            sqlPara.AddParameter(new SqlParam("currentCompanyId", System.Data.DbType.String, this.Context.Company));
            sqlPara.SelectedFieldKeys.AddRange(new string[]
            {
                "fid",
                "fnumber",
                "fname",
                "fispreset",
                "fmainorgid",
            });
            var listQuery = this.Container.GetService<IListSqlBuilder>();
            var queryData = listQuery.GetQueryData(this.Context, sqlPara);
            this.Result.SrvData = new
            {
                data = queryData
            };
        }
    }
}