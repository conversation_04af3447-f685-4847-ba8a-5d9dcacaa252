using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.Serialization;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.DataTransferObject.IM;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.PushShareData
{
    /// <summary>
    /// 共享基础资料：同步推送
    /// </summary>
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("SynPushShareData")]
    public class SynPushShareData : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            string pushDatas = this.GetQueryOrSimpleParam<string>("pushDatas");
            if (string.IsNullOrWhiteSpace(pushDatas))
            {
                return;
            }

            IMMessage message = new IMMessage();
            message.SetHandler(Msg.SynMsg.SynMsgHandlerKeyConst.PushShareDataMsg);
            message.AddMessage(new IMJsonMessage() { Content = pushDatas });
            var messageDispatcher = this.Container.GetService<IMessageDispatcher>();
            messageDispatcher.PublishMessage(this.OperationContext.UserContext, message.Head.Handler, new IMMessage[] { message });

            this.Result.IsSuccess = true;
            //List<DynamicObject> rollbackEntities = new List<DynamicObject>();
            //try
            //{
            //    synPushShareData(rollbackEntities);
            //}
            //catch(Exception ex)
            //{
            //    foreach(var dataEntity in rollbackEntities)
            //    {
            //        rollbackOperationData(dataEntity);
            //    }
            //    this.writeLog(ex.ToString());
            //    throw ex;
            //}
        }
    }
}
