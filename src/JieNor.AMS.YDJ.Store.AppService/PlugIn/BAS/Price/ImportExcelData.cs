using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Price
{
    [InjectService]
    [FormId("ydj_reprice|ydj_dealprice")]
    [OperationNo("ImportExcelData")]
    public class ImportExcelData : AbstractOperationServicePlugIn
    {
        ////避免导入后点新增时页面展示按照基模板配置进行
        //public string oldHtmlFormId { get; set; }
        ///// <summary>
        ///// 初始化时避免以重写的FormId为准
        ///// </summary>
        ///// <param name="e"></param>
        //public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        //{
        //    base.InitializeOperationDataEntity(e);
        //    oldHtmlFormId = this.OperationContext.HtmlForm.Id;
        //    this.OperationContext.HtmlForm.Id = this.OperationContext.HtmlForm.BaseModel;
        //}
        //public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        //{
        //    base.AfterExecuteOperationTransaction(e);
        //    this.OperationContext.HtmlForm.Id = oldHtmlFormId;
        //}
    }
}
