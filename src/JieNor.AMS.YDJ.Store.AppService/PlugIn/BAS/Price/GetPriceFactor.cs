using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Linq;
using System;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Price
{
    /// <summary>
    /// 销售价目表：获取商品经销价
    /// </summary>
    [InjectService]
    [FormId("ydj_factorprice")]
    [OperationNo("GetPriceFactor")]
    public class GetPriceFactor : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 执行操作事务前事件，通知插件对要处理的数据进行排序等预处理
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            // 商品信息
            JArray productInfos = null;
            var productid = this.GetQueryOrSimpleParam<string>("productid");
            var clientId = this.GetQueryOrSimpleParam<string>("clientId");
            if (productid.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("请求参数 productid 为空，请检查！");
            }
            var priceService = this.Context.Container.GetService<IPriceService>();
            var Prices = priceService.GetPriceFactor(this.Context,productid,clientId);

            this.Result.IsSuccess = true;
            this.Result.SrvData = Prices;
        }

    }
}
