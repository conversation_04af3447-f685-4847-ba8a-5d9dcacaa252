using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Price
{
    /// <summary>
    /// 商品价目：明细确认
    /// </summary>
    [InjectService]
    [FormId("ydj_price|ydj_selfprice|ydj_reprice|ydj_dealprice")]
    [OperationNo("confirm")]
    public class Confirm : SetConfirmStatus
    {
        protected override string ConfirmStatus
        {
            get
            {
                return "2";
            }
        }

        protected override string PromptFieldId
        {
            get
            {
                return this.HtmlForm.NameFldKey;
            }
        }

        protected override string ProductIdKey
        {
            get
            {
                return "fproductid";
            }
        }

        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            e.Rules.Add(new ValidationOrgOperation("确认"));
        }


        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            var products = new List<ProductItem>();

            foreach (var dataEntity in e.DataEntitys)
            {
                var id = Convert.ToString(dataEntity["id"]);

                //根据fid获取选择行fentry
                var selectIds = this.SelectedRows.Where(x => x.PkValue == id && x.EntityKey == "fentry").Select(x => x.EntryPkValue).Where(x => string.IsNullOrWhiteSpace(x) == false).Distinct().ToList();

                var fentries = dataEntity["fentry"] as DynamicObjectCollection;
                if (fentries == null || fentries.Count == 0) continue;

                foreach (var fentry in fentries)
                {
                    var fentryId = Convert.ToString(fentry["id"]);
                    if (selectIds == null || selectIds.Count <= 0 || selectIds.Contains(fentryId))
                    {
                        products.Add(new ProductItem
                        {
                            ProductId = Convert.ToString(fentry["fproductid"]),
                            AuxPropValId = Convert.ToString(fentry["fattrinfo"])
                        });
                    }
                }
            }

            List<string> productIds = products.Select(s => s.ProductId).Distinct().ToList();
            List<ProductItem> matchProducts = new List<ProductItem>();

            int limit = 500;
            int totalCount = productIds.Count;
            int times = Convert.ToInt32(Math.Ceiling(1.0 * totalCount / limit));

            var priceService = this.Container.GetService<IPriceService>();
            var dbServiceEx = this.Container.GetService<IDBServiceEx>();

            for (int i = 0; i < times; i++)
            {
                var thisProductIds = productIds.Skip(limit * i).Take(limit);

                string sql =
                    $"select fid,fdefattrinfo from T_BD_MATERIAL where fmainorgid='{this.Context.Company}' and fid in ({string.Join(",", thisProductIds.Select(s => $"'{s}'"))}) ";

                using (var reader = this.DBService.ExecuteReader(this.Context, sql))
                {
                    while (reader.Read())
                    {
                        string productId = reader.GetValueToString("fid");
                        string defAttrInfo = reader.GetValueToString("fdefattrinfo");

                        // 判断商品+默认辅助属性
                        var product = matchProducts.FirstOrDefault(s =>
                            s.ProductId == productId && s.AuxPropValId == defAttrInfo);
                        if (product == null)
                        {
                            product = new ProductItem
                            {
                                ProductId = productId,
                                AuxPropValId = defAttrInfo
                            };
                            matchProducts.Add(product);
                        }
                    }
                }

                UpdateLocalPrices(dbServiceEx, priceService, matchProducts);
            }


            Task.Run(() =>
            {
                priceService.ClearPriceCache(this.Context, e.DataEntitys, "fproductid");//更新缓存方便取价服务 
            });
        }

        /// <summary>
        /// 批量更新商品本地价目
        /// </summary>
        /// <param name="dbServiceEx">数据库扩展接口</param>
        /// <param name="priceService">取价服务</param>
        /// <param name="products">商品信息</param> 
        /// <returns>商品本地价目列表</returns>
        public void UpdateLocalPrices(IDBServiceEx dbServiceEx, IPriceService priceService, List<ProductItem> products)
        {
            if (products == null || products.Count == 0) return;

            JArray productInfos = new JArray();
            foreach (var product in products)
            {
                productInfos.Add(JToken.FromObject(new
                {
                    clientId = product.ProductId,
                    productId = product.ProductId,
                    bizDate = DateTime.Now,
                    length = 0,
                    width = 0,
                    thick = 0,
                    attrInfo = new
                    {
                        id = product.AuxPropValId
                    }
                }));
            }

            var prices = priceService.GetPrice(this.Context, 1, productInfos);

            List<string> sqls = new List<string>();
            foreach (var price in prices)
            {
                if ((bool)price["success"])
                {
                    string productId = (string)price["clientId"];

                    decimal salPrice = Convert.ToDecimal(price["salPrice"]);
                    decimal guidePrice = Convert.ToDecimal(price["guidePrice"]);

                    sqls.Add($"update T_BD_MATERIAL set fmodifydate=GETDATE(), fmodifierid='{this.Context.UserId}', fsalprice={salPrice}, fguideprice={guidePrice} where fid='{productId}'");
                }
            }

            if (sqls.Any())
            {
                dbServiceEx.ExecuteBatch(this.Context, sqls);
            }
        }

        public class ProductItem
        {
            /// <summary>
            /// 商品id
            /// </summary>
            public string ProductId { get; set; }

            /// <summary>
            /// 辅助属性值id
            /// </summary>
            public string AuxPropValId { get; set; }
        }
    }
}
