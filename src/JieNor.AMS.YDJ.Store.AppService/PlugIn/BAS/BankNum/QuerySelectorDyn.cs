using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.BankNum
{
    /// <summary>
    /// 
    /// </summary>
    [InjectService]
    [FormId("ydj_banknum")]
    [OperationNo("QuerySelector")]
    public class QuerySelectorDyn : AbstractQueryDyn
    {
        //该插件功能逻辑在基类实现
    }
}