using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Series
{
    /// <summary>
    /// 系列：获取系列所属品牌
    /// </summary>
    [InjectService]
    [FormId("ydj_series")]
    [OperationNo("getbrandbyid")]
    public class GetBrandById : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var pkid = this.GetQueryOrSimpleParam("pkid", "");
            if (!string.IsNullOrWhiteSpace(pkid))
            {
                var sql = $@"/*dialect*/select fbrandid as brandid from t_ydj_series with(nolock) where fid='{pkid}'";
                var data = this.DBService.ExecuteDynamicObject(this.Context, sql)?.FirstOrDefault();
                if (data != null)
                {
                    this.Result.IsSuccess = true;
                    this.Result.SrvData = data["brandid"];
                }
            }
        }
    }
}
