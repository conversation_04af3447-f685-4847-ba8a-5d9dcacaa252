using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Auth;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.ProductAuth
{

    /// <summary>
    /// 商品授权：清除专供商品
    /// </summary>
    [InjectService]
    [FormId("ydj_productzgdialog")]
    [OperationNo("clearzgproduct")]
    public class ClearZGProduct : AbstractOperationServicePlugIn
    {

        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);
            if (e.DataEntitys == null || e.DataEntitys.Length == 0)
            {
                return;
            }

        }
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var formid = "ydj_productauth";
            var dm = this.GetDataManager();
            var productauthHtml = this.MetaModelService.LoadFormModel(this.Context, formid);
            dm.InitDbContext(this.Context, productauthHtml.GetDynamicObjectType(this.Context));
            base.BeginOperationTransaction(e);
            foreach (var item in e.DataEntitys)
            {
                DynamicObjectCollection orgs = item["forgs"] as DynamicObjectCollection;
                DynamicObjectCollection products = item["fproducts"] as DynamicObjectCollection;
                List<string> orgids = orgs.Where(_ => !string.IsNullOrEmpty(Convert.ToString(_["forgid"]))).Select(_ => Convert.ToString(_["forgid"])).ToList();
                string supplierIds = "";
                foreach (string orgItem in orgs.Select(_ => _["forgid"]).ToList())
                {
                    supplierIds += "'" + orgItem + "',";
                }
                if (supplierIds.Length > 0)
                {
                    supplierIds = supplierIds.Substring(0, supplierIds.Length - 1);
                }
                string productIds = "";
                foreach (string orgItem in products.Select(_ => _["fproductid_o"]).ToList())
                {
                    productIds += "'" + orgItem + "',";
                }
                if (productIds.Length > 0)
                {
                    productIds = productIds.Substring(0, productIds.Length - 1);
                }
                var orgItems = this.Context.LoadBizDataByFilter("bas_agent", " fid in ({0})".Fmt(supplierIds));
                var prdItems = this.Context.LoadBizDataByFilter("ydj_product", " fid in ({0})".Fmt(productIds));

                string supplierNums = string.Join(",", orgItems.Select(_ => _["fnumber"]).ToList());
                string productNums = string.Join(",", prdItems.Select(_ => _["fnumber"]).ToList());


                string ids = "";
                foreach (var orgid in orgids)
                {
                    ids += "'" + orgid + "',";
                }
                if (string.IsNullOrWhiteSpace(ids)) continue;
                ids = ids.Substring(0, ids.Length - 1);

                List<DynamicObject> productauths = this.Context.LoadBizDataByFilter(formid, " forgid in (" + ids + ")");

                List<string> removePrdAuthExcludeEntryIds = new List<string>();

                foreach (var productauthItem in productauths)
                {
                    var productAuthEntrys = productauthItem?["fproductauthexclude"] as DynamicObjectCollection;
                    foreach (var productItem in products)
                    {
                        if (!string.IsNullOrEmpty(Convert.ToString(productItem["fproductid_o"])))
                        {
                            var productAuthEntryItems = productAuthEntrys.Where(_ => Convert.ToString(_["fproductid_o"]) == Convert.ToString(productItem["fproductid_o"])).ToList();
                            if (productAuthEntryItems != null && productAuthEntryItems.Count > 0)
                            {
                                foreach (var productAuthEntryItem in productAuthEntryItems)
                                {
                                    productAuthEntrys.Remove(productAuthEntryItem);
                                    removePrdAuthExcludeEntryIds.Add(Convert.ToString(productAuthEntryItem["id"]));
                                }
                            }
                        }
                    }
                    this.Logger.WriteLog(this.Context, new LogEntry
                    {
                        BillIds = productauthItem["id"] as string,
                        BillNos = productauthItem["fnumber"] as string,
                        BillFormId = "ydj_productauth",
                        OpName = "清除专供商品",
                        OpCode = this.OperationNo,
                        Content = "执行了【清除专供商品】操作；经销商：{0}；商品：{1}".Fmt(supplierNums, productNums),
                        DebugData = "执行了【清除专供商品】操作；经销商：{0}；商品：{1}".Fmt(supplierNums, productNums),
                        Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                        Level = Enu_LogLevel.Info.ToString(),
                        LogType = Enu_LogType.RecordType_03,
                    });
                }
                var attachprepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
                attachprepareSaveDataService.PrepareDataEntity(this.Context, productauthHtml, productauths.ToArray(), Framework.SuperOrm.OperateOption.Create());
                dm.Save(productauths);

                try
                {
                    // 记录删除的明细行id
                    DebugUtil.WriteLogToFile("清除专供商品：" + new
                    {
                        RemovePrdAuthExcludeEntryIds = removePrdAuthExcludeEntryIds
                    }.ToJson(), "清除专供商品");
                }
                catch (Exception ex)
                {
                    this.Container.GetService<ILogServiceEx>().Error("清除专供商品", ex);
                }

                this.Container.GetService<IProductAuthService>().ClearPrdAuthCache(this.Context, productauths, this.HtmlForm, this.OperationNo);
            }

            if (e.DataEntitys != null && e.DataEntitys.Length > 0)
            {
                //清除缓存
                ProductDataIsolateHelper.ClearCacheByBiz(this.Context, new PrdDataIsolateChannelMessage
                {
                    Message = $"{this.HtmlForm?.Caption}-{this.OperationNo}",
                    TopCompanyId = this.Context.TopCompanyId
                });
            }
        }
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {

            if (e.DataEntitys.Count() <= 0) return;
            HashSet<string> allorgid = new HashSet<string>();
            HashSet<string> allRemoveZGProductId = new HashSet<string>();
            foreach (var item in e.DataEntitys)
            {
                var orgs = item["forgs"] as DynamicObjectCollection; // 经销商送达方数据
                if (orgs != null)
                {
                    var orgids = orgs.Where(_ => _?["forgid"] != null && _?["forgid"].ToString() != "")
                                    .Select(_ => _["forgid"].ToString())
                                    .ToList(); // 经销商主键集合
                    allorgid.UnionWith(orgids);
                }
                var products = item["fproducts"] as DynamicObjectCollection;
                if (products != null)
                {
                    var proids = products.Where(_ => _?["fproductid_o"] != null && _?["fproductid_o"].ToString() != "")
                                    .Select(_ => _["fproductid_o"].ToString())
                                    .ToList(); 
                    allRemoveZGProductId.UnionWith(proids);
                }
            }
            if (allRemoveZGProductId.Count <= 0) { return; }
            List<string> finalAllorgid = allorgid.ToList();
            if (finalAllorgid.Count <= 0) { return; }
            StringBuilder dataLog = new StringBuilder();
            dataLog.Append($"ydj_productauth:清除专供商品【{this.OperationNo}】->-begin:{DateTime.Now.ToString("yyyyMMddHHmmss")},一级组织:{string.Join(",", finalAllorgid)}");
            dataLog.AppendLine();
            // 1. 获取此次修改的授权清单
            List<DynamicObject> ProductAuths = this.Context.LoadBizDataByFilter("ydj_productauth", $" forgid in ({ string.Join(",", finalAllorgid.Select(id => $"'{id}'")) }) and  fforbidstatus = 0").ToList();
            Task task = new Task(() =>
            {
                this.Container.GetService<IProductAuthService>().ClearSecondAgentAuth(this.Context, ProductAuths, null, 
                    allRemoveZGProductId.ToList(),null,this.HtmlForm,this.OperationNo, "清除专供商品");
            });
            ThreadWorker.QuequeTask(task, result => {
                dataLog.AppendLine($"ydj_productauth:清除专供商品【{this.OperationNo}】->-end:{DateTime.Now.ToString("yyyyMMddHHmmss")},{result.Exception}");
                this.Logger.WriteLogToFile(dataLog.ToString(), "清除专供商品");
            });
           
        }
    }
}
