using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.InventoryWaterLevel 
{
    /// <summary>
    /// 库存水位：保存
    /// </summary>
    [InjectService]
    [FormId("si_inventorywaterlevel")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e) 
        {
            base.PrepareValidationRules(e);

            var errorMessage = string.Empty;  
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMessage = "";

                //'1':'送达方（优先级1）','2':'售达方（优先级2）','3':'全局（优先级3）'
                var fcontrollevel = Convert.ToString(newData["fcontrollevel"]);
                var fagentid = Convert.ToString(newData["fagentid"]);
                var fdeliverid = Convert.ToString(newData["fdeliverid"]);
                //1、当【管控级别】=“送达方（优先级1）”，【送达方名称】必录，反之必须为空。
                if (fcontrollevel.EqualsIgnoreCase("1"))
                {
                    if (fdeliverid.IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "当【管控级别】=“送达方（优先级1）”，【送达方名称】不能为空!";
                        return false;
                    }
                    if (!fagentid.IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "当【管控级别】=“送达方（优先级1）”，【售达方名称】必须为空!";
                        return false;
                    }
                }
                if (fcontrollevel.EqualsIgnoreCase("2"))
                {
                    if (!fdeliverid.IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "当【管控级别】=“售达方（优先级2）”，【送达方名称】必须为空!";
                        return false;
                    }
                    if (fagentid.IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "当【管控级别】=“售达方（优先级2）”，【售达方名称】不能为空!";
                        return false;
                    }
                }
                if (fcontrollevel.EqualsIgnoreCase("3"))
                {
                    if (!fdeliverid.IsNullOrEmptyOrWhiteSpace() || !fagentid.IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "当【管控级别】=“全局（优先级3）”，【售达方名称】【送达方名称】必须为空!";
                        return false;
                    }
                }

                var fredline = Convert.ToDecimal(newData["fredline"]);
                var fgreenline = Convert.ToDecimal(newData["fgreenline"]);
                if (fgreenline > fredline)
                {
                    errorMessage = "红灯线必须大于绿灯线!";
                    return false;
                }

                if (fgreenline < 0 ||  fredline < 0)
                {
                    errorMessage = "红灯线、绿灯线不能为负数!";
                    return false;
                }

                var entity = newData["fexceptionmodelsentity"] as DynamicObjectCollection;
                var need = entity.Any(o => Convert.ToDecimal(o["fgrennline_spu"]) > Convert.ToDecimal(o["fredline_spu"]));
                if (need) 
                {
                    errorMessage = "SPU红灯线必须大于SPU绿灯线！";
                    return false;
                }
                var has = entity.Any(o => Convert.ToDecimal(o["fgrennline_spu"]) <0 ||  Convert.ToDecimal(o["fredline_spu"])<0);
                if (has)
                {
                    errorMessage = "SPU红灯线、SPU绿灯线不能为负数！";
                    return false;
                }
                //不允许型号重复
                var hasrepet = entity.GroupBy(o => Convert.ToString(o["fseltypeid"])).Any(x => x.Count() > 1);
                if (hasrepet) 
                {
                    errorMessage = "SPU型号不允许重复！";
                    return false;
                }

                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

        }
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

        }

    }
}
