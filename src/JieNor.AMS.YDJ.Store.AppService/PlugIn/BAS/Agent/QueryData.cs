using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Agent
{
    [InjectService]
    [FormId("bas_agent")]
    [OperationNo("QueryData")]
    public class QueryData : AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName)
            {
                case "afterListData":
                    this.AfterListData(e);
                    break;
            }
        }
        private void AfterListData(OnCustomServiceEventArgs e)
        {
            var listData = e.EventData as List<Dictionary<string, object>>;
            PartMulFields(new[] { "fprovince", "flevel" }, listData);

        }

        /// <summary>
        /// 处理多选字段
        /// </summary>
        /// <param name="maskKeys"></param>
        /// <param name="listData"></param>
        public void PartMulFields(IEnumerable<string> maskKeys, List<Dictionary<string, object>> listData)
        {
            foreach (var maskKey in maskKeys)
            {
                var obj = listData?.FirstOrDefault();
                if (obj == null) continue;
                var key = listData.FirstOrDefault().Keys.FirstOrDefault(x => x.EqualsIgnoreCase(maskKey));
                if (string.IsNullOrWhiteSpace(key))
                {
                    continue;
                }
                foreach (var item in listData)
                {
                    var cityids = item["fcity"];

                    if (cityids == null) 
                    {
                        continue;
                    }
                    var cityidsLst = cityids.ToString().Split(',').ToList();
                    //城市多选的才需要特殊处理
                    if (cityidsLst.Count < 2) 
                    {
                        continue;
                    }
                    var citys = this.Context.LoadBizDataByACLFilter("ydj_city", $"fid in ('{string.Join("','", cityidsLst)}')",true);
                    var Province = citys.Select(o => (o[$"{key}_ref"] as DynamicObject)?["fenumitem"].ToString()).Distinct().JoinEx(",", false);
                    var itemValue = Province;
                    if (itemValue == null)
                    {
                        continue;
                    }
                    //var charArray = itemValue.ToString().ToCharArray();
                    item[key] = itemValue;
                }
            }
        }
    }
}
