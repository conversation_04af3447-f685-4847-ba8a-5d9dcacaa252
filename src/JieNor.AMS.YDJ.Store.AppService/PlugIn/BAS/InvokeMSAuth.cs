using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS
{
    /// <summary>
    /// 慕思认证中心调用：通用获取请求地址
    /// </summary>
    [InjectService]
    [FormId("ydj_dept|sec_role|ydj_position|ydj_staff|sec_user|bas_agent")]
    [OperationNo("invokemsauth")]
    public class InvokeMSAuth : AbstractOperationServicePlugIn
    {

        public const string CST_CacheKey_BizFuncACL = "sec:menu:{0}:{1}:{2}";
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var opcode = this.GetQueryOrSimpleParam<string>("opcode", "");
            var agentid = this.GetQueryOrSimpleParam<string>("agentid", "");
            if (opcode.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("操作参数不能为空。");
            }
            IMusiAuthService service = this.Container.GetService<IMusiAuthService>();
            var url = service.GetUrl(this.Context, this.HtmlForm, opcode);
            #region 查询相关数据
            // 根据Id查询所有的外部经销商Ids
            string sql = $@"select fid,ftranid,fisreseller,fnumber,fname from t_bas_agent with(nolock) where fid in ('{this.Context.Company}','{this.Context.ParentCompanyId}','{agentid}')";
            var agents = this.Context.ExecuteDynamicObject(sql, new List<SqlParam>());
            // 根据Id查询所有的外部用户Ids
            sql = $@"select fid,fnumber,fname,ftranid from t_sec_user with(nolock) where fid ='{this.Context.UserId}'";
            var user = this.Context.ExecuteDynamicObject(sql, new List<SqlParam>())?.FirstOrDefault();
            List<string> needCheckFormId = new List<string>() { "sec_role", "ydj_position", "ydj_staff", "sec_user" };
            List<string> preItems = needCheckFormId.IndexOf(this.HtmlForm.Id) >= 0 ? GetPremitInfo(Context, this.HtmlForm.Id) : new List<string>();
            string _preItems = string.Empty;
            //角色，岗位，员工，校验 查看、新增、编辑、导入、导出、禁用、反禁用
            List<string> commonPreItem = new List<string>() { "fw_view", "fw_new", "fw_modify", "fw_import", "fw_export", "fw_forbid", "fw_unforbid" };
            //用户，校验 查看、新增、编辑、导入、导出，禁用、反禁用、解绑企业微信、重置密码
            List<string> userPreItem = new List<string>() { "fw_view", "fw_new", "fw_modify", "fw_import", "fw_export", "fw_forbid", "fw_unforbid", "fw_resetpwd", "fw_unbindqywx" };
            string _preItemUrl = string.Empty;
            if (this.HtmlForm.Id == "sec_user")
            {
                foreach (var item in userPreItem)
                {
                    if (preItems.IndexOf(item) >= 0)
                    {
                        _preItemUrl += "&" + item + "=1";
                    }
                    else
                    {
                        _preItemUrl += "&" + item + "=0";
                    }
                }
            }
            else if (needCheckFormId.IndexOf(this.HtmlForm.Id) >= 0)
            {
                foreach (var item in commonPreItem)
                {
                    if (preItems.IndexOf(item) >= 0)
                    {
                        _preItemUrl += "&" + item + "=1";
                    }
                    else
                    {
                        _preItemUrl += "&" + item + "=0";
                    }
                }
            }
            #endregion

            var currentAgent = agents.Where(x => Convert.ToString(x["fid"]) == this.Context.Company).FirstOrDefault();
            if (opcode == "changelicense_auth")
            {
                if (agentid.IsNullOrEmptyOrWhiteSpace())
                {
                    throw new BusinessException("认证中心更换营业执照经销商参数不能为空。");
                }
                currentAgent = agents.Where(x => Convert.ToString(x["fid"]) == agentid).FirstOrDefault();
                url = $"{url}&firOrg={currentAgent?["ftranid"]}&beforeOrgId={currentAgent?["ftranid"]}&beforeOrgNum={currentAgent?["fnumber"]}&beforeOrgName={currentAgent?["fname"]}";
            }
            else
            {
                var isSecondOrg = currentAgent != null && Convert.ToString(currentAgent["fisreseller"]) == "1";
                if (isSecondOrg)
                {
                    var parentAgent = agents.Where(x => Convert.ToString(x["fid"]) == this.Context.ParentCompanyId).FirstOrDefault();
                    url = $"{url}&firOrg={parentAgent?["ftranid"]}&secOrg={currentAgent?["ftranid"]}";
                }
                else
                {
                    //如果是总部打开则传总部id到firOrg
                    if (this.Context.Company.EqualsIgnoreCase(this.Context.TopCompanyId))
                    {
                        url = $"{url}&firOrg={this.Context.TopCompanyId}";
                    }
                    else 
                    {
                        url = $"{url}&firOrg={currentAgent?["ftranid"]}";
                    }
                }
            }
            url = url + _preItemUrl;
            this.Result.SrvData = $"{url}&account={user?["fnumber"]}&accountId={user?["ftranid"]}";
            this.Result.IsSuccess = true;
        }

        /// <summary>
        /// 获取表单对应的权限项
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        private List<string> GetPremitInfo(UserContext ctx, string formId)
        {
            var cacheKey = CST_CacheKey_BizFuncACL.Fmt(ctx.Company, ctx.UserId, formId);
            var cache = ctx.Container.GetService<IRedisCache>();
            (cache as ISupportRegion).SetRegionId("Permission");
            List<string> havPermit = null;
            try
            {
                havPermit = cache.Get<List<string>>(cacheKey);
            }
            catch (Exception ex)
            {
                //忽略redis缓存取数错误
            }

            if (havPermit != null && havPermit.Any())
            {
                return havPermit;
            }

            var strSql = @" select distinct t0.fpermititemid,t0.fallow,t0.frefuse
                            from t_sec_rolefuncacl t0 with(nolock) 
                            inner join t_sec_roleuser t1  with(nolock) on t0.froleid=t1.froleid
                            inner join t_sec_user t2  with(nolock) on t1.fuserid=t2.fid
                            where  (t0.fcompanyid=@fcompanyid or t0.fcompanyid=@ftoporgid )
                                    and t0.fbizobjid=@fbizobjid
                                    and t0.fallow='1' and t2.fid=@fuserid
                            ";
            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@fcompanyid", System.Data.DbType.String,ctx.Company),
                new SqlParam("@ftoporgid", System.Data.DbType.String,ctx.TopCompanyId),//经销商管理员角色在总部组织下
                new SqlParam("@fbizobjid", System.Data.DbType.String,formId),
                new SqlParam("@fuserid", System.Data.DbType.String,ctx.UserId),
            };

            var dbService = ctx.Container.GetService<IDBService>();
            var datas = dbService.ExecuteDynamicObject(ctx, strSql, lstSqlParas);

            havPermit = new List<string>();
            var grp = (from p in datas
                       select p["fpermititemid"].ToString()).Distinct().ToList();
            foreach (var item in grp)
            {
                if (datas.Any(f => f["frefuse"] != null && f["frefuse"].ToString() == "1"))
                {
                    //有任意一个角色设置拒绝，则认为没有权限
                    continue;
                }
                havPermit.Add(item);
            }

            return havPermit;
        }
    }

    /// <summary>
    /// 获取认证中心参数
    /// </summary>
    [InjectService]
    [FormId("ms_authcenterparam")]
    [OperationNo("LoadAuthParam")]
    public class LoadAuthParam : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var topCtx = this.Context.CreateTopOrgDBContext();
            var systemProfile = topCtx.Container.GetService<ISystemProfile>();
            string sysProfileValue = systemProfile.GetProfile(topCtx, "fw", $"{this.HtmlForm.Id}_parameter");
            if (!sysProfileValue.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.SrvData = JObject.Parse(sysProfileValue);
            }
            this.Result.IsSuccess = true;
        }
    }

    /// <summary>
    /// 检查当前登录组织是否是子经销商
    /// </summary>
    [InjectService]
    [FormId("sec_role|ydj_position|ydj_staff|sec_user")]
    [OperationNo("beforeauthcheck")]
    public class BeforeAuthCheck : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            this.Result.SrvData = true;
            if (Context.IsTopOrg)
            {
                return;
            }
            string sql = $@"select fmainagentid ,fsubagentid from t_bas_mac tb0 with(nolock) 
                                                    inner join t_bas_macentry tb1  with(nolock) on tb0.fid=tb1.fid
                                                          where tb1.fsubagentid ='{this.Context.Company}' and tb0.fforbidstatus='0'";
            var agents = this.Context.ExecuteDynamicObject(sql, new List<SqlParam>());
            if (agents.Count > 0)
            {
                this.Result.SrvData = false;
            }

        }
    }
}
