using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Schedule
{
    [InjectService]
    [FormId("sys_schedule")]
    [OperationNo("getScheduleTimeListByDay")]
    public class ScheduleDataByDay : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            string Id = this.GetQueryOrSimpleParam<string>("Id");
            string time = this.GetQueryOrSimpleParam<string>("date");
            DateTime dt = DateTime.Now;
            DateTime.TryParse(time, out dt);
            string star = new DateTime(dt.Year, dt.Month, dt.Day).ToString("yyyy-MM-dd HH:mm:ss");
            string end = new DateTime(dt.Year, dt.Month, dt.Day).AddDays(1).ToString("yyyy-MM-dd HH:mm:ss");
            List<Dictionary<string, string>> list = new List<Dictionary<string, string>>();
            using (var reader = this.DBService.ExecuteReader(this.Context, $"select fid,fstartdate,fenddate,fremind,ftitle,fdescription,fposition from [t_sys_schedule]  where fparticipants like '%{Id}%' and  fstartdate>='{star}' and fstartdate<'{end}'"))
            {
                while (reader.Read())
                {
                    Dictionary<string, string> outData = new Dictionary<string, string>();
                    outData.Add("id", Convert.ToString(reader["fid"]));
                    outData.Add("time", Convert.ToString(reader["fstartdate"]));
                    outData.Add("etime", Convert.ToString(reader["fenddate"]));
                    outData.Add("remind", Convert.ToString(reader["fremind"]));
                    outData.Add("title", Convert.ToString(reader["ftitle"]));
                    outData.Add("desc", Convert.ToString(reader["fdescription"]));
                    outData.Add("address", Convert.ToString(reader["fposition"]));
                    list.Add(outData);
                }
            }
            this.Result.SrvData = list;
        }
    }
}