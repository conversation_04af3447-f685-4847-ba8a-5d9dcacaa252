using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.MainAuxProp
{
    /// <summary>
    /// 主辅属性约束：保存
    /// </summary>
    [InjectService]
    [FormId("bas_mainauxprop")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            //校验主属性信息的唯一性
            var headMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return this.CheckBillHead(newData, out headMessage);
            }).WithMessage("{0}", (billObj, propObj) => headMessage));

            //校验辅属性信息
            var entryMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return this.CheckEntry(newData, out entryMessage);
            }).WithMessage("{0}", (billObj, propObj) => entryMessage));

            //校验例外信息的唯一性
            var exclEntryMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return this.CheckExclEntry(newData, out exclEntryMessage);
            }).WithMessage("{0}", (billObj, propObj) => exclEntryMessage));

            //校验例外信息的辅助属性
            var exclEntryAuxPropMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return this.CheckExclEntryAuxProp(newData, out exclEntryAuxPropMessage);
            }).WithMessage("{0}", (billObj, propObj) => exclEntryAuxPropMessage));
        }

        /// <summary>
        /// 校验主属性信息的唯一性
        /// </summary>
        private bool CheckBillHead(DynamicObject newData, out string message)
        {
            //当前数据包是否是修改
            var pkid = "";
            var isEdit = newData.DataEntityState.FromDatabase;
            if (isEdit)
            {
                pkid = Convert.ToString(newData["id"]);
            }

            var auxPropId = Convert.ToString(newData["fauxpropid"]);
            var refEnumValue = Convert.ToString(newData["frefenumvalue"]);

            var checkOk = MainAuxPropHelper.CheckBillHeadUnique(this.Context, auxPropId, refEnumValue, out message, pkid);
            return checkOk;
        }

        /// <summary>
        /// 校验辅属性信息
        /// </summary>
        private bool CheckEntry(DynamicObject newData, out string message)
        {
            message = "";

            //按【属性类别+属性值】分组
            var entrys = newData["fentity"] as DynamicObjectCollection;
            if (!entrys.Any())
            {
                message = "至少要有一行辅属性信息！";
                return false;
            }
            var group = entrys.GroupBy(o =>
            {
                var groupKey = (
                Convert.ToString(o["fauxpropid"]).Trim() +
                Convert.ToString(o["frefenumvalue"]).Trim()).ToLowerInvariant();
                return groupKey;
            });
            foreach (var item in group)
            {
                if (item.Count() > 1)
                {
                    message = "辅属性信息已存在相同的属性值，请重新选择！";
                    break;
                }
            }
            return message.IsNullOrEmptyOrWhiteSpace();
        }

        /// <summary>
        /// 校验例外信息的唯一性
        /// </summary>
        private bool CheckExclEntry(DynamicObject newData, out string message)
        {
            message = "";

            //按【商品+辅助属性+属性类别+属性值】分组
            var entrys = newData["fexclentity"] as DynamicObjectCollection;
            var group = entrys.GroupBy(o =>
            {
                var groupKey = (
                Convert.ToString(o["fproductid"]).Trim() +
                Convert.ToString(o["fattrinfo"]).Trim() +
                Convert.ToString(o["fauxpropid"]).Trim() +
                Convert.ToString(o["frefenumvalue"]).Trim()).ToLowerInvariant();
                return groupKey;
            });
            foreach (var item in group)
            {
                if (item.Count() > 1)
                {
                    message = "例外信息已存在相同的商品属性值，请重新选择！";
                    break;
                }
            }
            return message.IsNullOrEmptyOrWhiteSpace();
        }

        /// <summary>
        /// 校验例外信息的辅助属性
        /// </summary>
        private bool CheckExclEntryAuxProp(DynamicObject newData, out string message)
        {
            message = "";

            //加载引用数据
            var refMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refMgr.Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), newData, false);

            //主属性类别名称
            var auxPropObj = newData["fauxpropid_ref"] as DynamicObject;
            var auxPropId = Convert.ToString(auxPropObj?["id"]);
            var auxPropName = Convert.ToString(auxPropObj?["fname"]);

            //辅助属性值ID
            var auxPropValueId = "";

            //值类型ID
            //var valueSourceId = Convert.ToString(newData["fvaluesource"]);
            //switch (valueSourceId)
            //{
            //    case "val_type_01": //辅助资料
            //        var enumObj = newData["frefenumvalue_ref"] as DynamicObject;
            //        //auxPropValueId = Convert.ToString(enumObj?["id"]);
            //        auxPropValueId = Convert.ToString(enumObj?["fenumitem"]); //因为辅助属性组合值里面存的是名称，所以这里也取名称
            //        break;
            //    case "val_type_03": //文本
            //        auxPropValueId = Convert.ToString(newData["ftextvalue"]);
            //        break;
            //}

            var enumObj = newData["frefenumvalue_ref"] as DynamicObject;
            auxPropValueId = Convert.ToString(enumObj?["fenumitem"]); //因为辅助属性组合值里面存的是名称，所以这里也取名称

            var entrys = newData["fexclentity"] as DynamicObjectCollection;
            foreach (var entry in entrys)
            {
                //辅助属性值中的辅助属性基础资料
                var attrInfoObj = entry["fattrinfo_ref"] as DynamicObject;
                var auxPropEntrys = attrInfoObj?["fentity"] as DynamicObjectCollection;
                var entryAuxPropObj = auxPropEntrys?.FirstOrDefault(o => Convert.ToString(o["fauxpropid"]).EqualsIgnoreCase(auxPropId));
                var valueId = Convert.ToString(entryAuxPropObj?["fvalueid"]);
                if (valueId.IsNullOrEmptyOrWhiteSpace() || !valueId.EqualsIgnoreCase(auxPropValueId))
                {
                    message = $"例外信息商品的辅助属性值中必须要有{auxPropName}，且{auxPropName}属性值必须等于当前主属性的属性值，请重新选择！";
                    break;
                }
            }

            return message.IsNullOrEmptyOrWhiteSpace();
        }
    }
}