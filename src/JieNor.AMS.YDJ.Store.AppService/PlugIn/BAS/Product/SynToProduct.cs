using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product
{
    /// <summary>
    /// 协同商品至商品
    /// </summary>
    [InjectService]
    [FormId("ydj_product")]
    [OperationNo("synToProduct")]
    public class SynToProduct : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            string strsyn = this.GetQueryOrSimpleParam<string>("syn");
            string strpro = this.GetQueryOrSimpleParam<string>("pro");
            if (strsyn.IsNullOrEmptyOrWhiteSpace() || strpro.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("参数[syn]或[pro]为空或NULL！");
            }
            string[] syn = strsyn.Split(',');
            string[] pro = strpro.Split(',');
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            List<DynamicObject> dyObj = dm.SelectBy($"fid in ('{string.Join("','", pro)}')").OfType<DynamicObject>().ToList();// as DynamicObjectCollection;
            if (dyObj == null || dyObj.Count == 0)
            {
                throw new BusinessException("请至少选择一项数据");
            }
            foreach (var item in dyObj)
            {
                string id = "";
                if (ArraySDN.outId(item["id"] as string, pro, syn, out id))
                {
                    item["fsynid"] = id;
                }
            }
            dm.Save(dyObj);
            var formMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "coo_product");
            dm.InitDbContext(this.Context, formMeta.GetDynamicObjectType(this.Context));
            List<DynamicObject> dyObj1 = dm.SelectBy($"fid in ('{string.Join("','", syn)}')").OfType<DynamicObject>().ToList();
            foreach (var item in dyObj1)
            {
                string id = "";
                if (ArraySDN.outId(item["id"] as string, syn, pro, out id))
                {
                    item["flocalproductid"] = id;
                    item["flocalproduct"] = dyObj.Where(t => t["id"] as string == id).FirstOrDefault()["fname"];
                }
            }
            dm.Save(dyObj1);
        }
    }
}