using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product
{
    /// <summary>
    /// 商品至协同商品
    /// </summary>
    [InjectService]
    [FormId("ydj_product")]
    [OperationNo("synProduct")]
    public class SynProduct : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            string id = this.GetQueryOrSimpleParam<string>("id");
            string type = this.GetQueryOrSimpleParam<string>("type");
            if (id.IsNullOrEmptyOrWhiteSpace() || type.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("参数[id]或[type]为空或NULL！");
            }
            string selectR = this.GetQueryOrSimpleParam<string>("selectedRows");
            if (selectR.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("请至少选择一项" + (type == "supplier" ? "供应商" : "客户") + "数据");
            }
            #region 查询商品数据            
            string[] pid = id.Split(',');
            List<Dictionary<string, string>> push_list = new List<Dictionary<string, string>>();
            using (var reader = this.DBService.ExecuteReader(this.Context, $"select fid, fname,(select top 1 fname from ser_ydj_category where fid=fcategoryid) as fcategory,fimage,(select top 1 fname from t_ydj_unit where fid=funitid) as funit,(select top 1 fname from t_ydj_brand where fid=fbrandid) as fbrand,fcontent from t_bd_material where fmainorgid='{this.Context.Company}' and fid " + (pid.Length == 1 ? "='" + pid[0] + "'" : " in ('" + string.Join("','", pid) + "')")))
            {
                while (reader.Read())
                {
                    Dictionary<string, string> push = new Dictionary<string, string>();
                    push.Add("fname", reader["fname"] as string);
                    push.Add("fcategory", reader["fcategory"] as string);
                    push.Add("fimage", reader["fimage"] as string);
                    push.Add("funit", reader["funit"] as string);
                    push.Add("fbrand", reader["fbrand"] as string);
                    push.Add("fpublishcompany", this.Context.Company);
                    push.Add("fdetail", reader["fcontent"] as string);
                    push.Add("fproductid", reader["fid"] as string);
                    push_list.Add(push);
                }
            }
            #endregion
            #region 向EIS站点发送协同商品数据包
            var gateWay = this.Container.GetService<IHttpMessageInvoker>();
            DynamicDTOWrapper dtoBill = new CommonBillDTO()
            {
                FormId = "syn_product",
                OperationNo = "saveAnddistribute",
                BillData = push_list.ToJson(),
                SimpleData = new Dictionary<string, string>() { { "product", this.Context.Product }, { "companytype", (type == "supplier" ? "供应商" : "客户") }, { "company", selectR } }
            };
            var resp = gateWay.InvokePrimitive<DynamicDTOWrapper, Stream>(this.Context.ToCallerContext(), dtoBill, TargetSEP.EisService);
            this.Result.SrvData = resp;
            #endregion
        }
    }
}