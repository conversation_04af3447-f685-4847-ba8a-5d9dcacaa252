using System;
using System.Linq;
using Newtonsoft.Json.Linq;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product
{
    /// <summary>
    /// 商品：根据商品名称匹配商品信息
    /// </summary>
    [InjectService]
    [FormId("ydj_product")]
    [OperationNo("MatchProduct")]
    public class MatchProduct : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            string productName = this.GetQueryOrSimpleParam<string>("productName");
            if (productName.IsNullOrEmptyOrWhiteSpace()) return;

            //商品模型
            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_product");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            var sqlParam = new SqlParam[]
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("fname", System.Data.DbType.String, productName)
            };
            var dataReader = this.Context.GetPkIdDataReader(htmlForm, "fmainorgid=@fmainorgid and fname=@fname", sqlParam);
            var dataEntity = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
            if (dataEntity != null)
            {
                //加载引用数据
                this.Context.Container.GetService<LoadReferenceObjectManager>()?.Load(this.Context, dm.DataEntityType, dataEntity, false);

                //数据打包器
                var uiConverter = this.Context.Container.GetService<IUiDataConverter>();
                var unitField = htmlForm.GetField("funitid");

                JObject joProduct = new JObject();
                joProduct["id"] = dataEntity?["id"] as string;
                joProduct["fnumber"] = dataEntity?["fnumber"] as string;
                joProduct["fname"] = dataEntity?["fname"] as string;

                JObject joQuote = new JObject();
                joQuote["fproductid"] = joProduct;
                joQuote["funitid"] = uiConverter.PackageFieldData(this.Context, htmlForm, unitField, dataEntity, null, false);

                this.Result.SrvData = joQuote;
                this.Result.IsSuccess = true;
            }

            this.Result.OptionData.Add("rowId", this.GetQueryOrSimpleParam<string>("rowId"));
        }
    }
}