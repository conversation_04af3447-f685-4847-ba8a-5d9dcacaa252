using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MaterialInit
{
    /// <summary>
    /// 导入物料
    /// </summary>
    [InjectService]
    [FormId("sys_materialinit")]
    [OperationNo("materialin")]
    public class MaterialIn : AbstractOperationServicePlugIn
    {
        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            base.OnPrepareOperationOption(e);
            e.OpCtlParam.IgnoreOpMessage = true;
        }

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }
            var dataEntity = e.DataEntitys[0];
            var fileId = Convert.ToString(dataEntity["ffileid"]);

            if (string.IsNullOrWhiteSpace(fileId))
            {
                throw new BusinessException("请先上传文件，再点导入!");
            }

            var seqSvc = this.Container.GetService<ISequenceService>();
            Dictionary<string, string> dic = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            dic.Add("fileurl", fileId.GetSignedFileUrl(false));
            dic.Add("fileid", fileId);
            ScheduleTaskObject taskInfo = new ScheduleTaskObject(seqSvc.GetSequence<string>(),
                "excel.import.materialinit",
                this.HtmlForm.Caption,
                "import",
                this.HtmlForm.Id,
                dic.ToJson()
                );
            var jobScheduler = this.Container.TryGetService<IJobScheduleService>();
            jobScheduler.ScheduleJob(this.Context, taskInfo, "");
            var progressAction = this.Context.ShowProgressForm(taskInfo.Identity, null);
            this.Result.IsSuccess = true;
            this.Result.HtmlActions.Add(progressAction);
        }
    }
}
