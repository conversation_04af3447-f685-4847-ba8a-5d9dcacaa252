using System;
using System.Linq;
using System.Text.RegularExpressions;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.CustomerInvoice
{
    [InjectService]
    [FormId("ydj_customerinvoice")]
    //[OperationNo("savecustomer")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            string errorMsg = string.Empty;
            this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return ValidateInvoiceEmails(newData, ref errorMsg);
            }).WithMessage("{0}", (billObj, propObj) => errorMsg);
            
            this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return CheckInvoiceEntryBankAccountIsNumber(newData, ref errorMsg);
            }).WithMessage("{0}", (billObj, propObj) => errorMsg);
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var customer = this.GetQueryOrSimpleParam<string>("customer", "");
            var entity = e.DataEntitys[0];

            entity["fid"] = customer;

            entity["fid"] = customer;
            
            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_customer");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            //将当前客户的其它客户联系人取消默认地址
            var sqlParam = new SqlParam[]
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company)
            };
            var dataReader = this.Context.GetPkIdDataReader(htmlForm, $"fid = '{customer}'", sqlParam);
            var customerObj = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
            var invoiceEntrys = customerObj["finvoiceentry"] as DynamicObjectCollection;
            //判断是否首条开票信息
            if (invoiceEntrys.Count == 0)
            {
                entity["fseq"] = 1;
                entity["finvoicedefault"] = true;
            }
            else {
                entity["fseq"] = invoiceEntrys.Count+1;
            }

            //如果当前传过来为默认
            if (Convert.ToBoolean(entity["finvoicedefault"])) 
            {
                foreach (var entry in invoiceEntrys) 
                {
                    entry["finvoicedefault"] = false;
                }
                dm.Save(customerObj);
                //再将当前开票处理成默认开票信息
                entity["finvoicedefault"] = true;
            }

            // this.Result.IsSuccess = true;
            //
            // this.Result.ComplexMessage.SuccessMessages.Add("保存客户开票信息成功");
            //
            // this.Result.IsShowMessage = true;
            
            //this.AddRefreshPageAction();

        }

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            // if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            // this.Result.ComplexMessage.SuccessMessages.Add("保存客户开票信息成功");
            // this.Result.IsShowMessage = true;
            // this.Result.IsSuccess = true;
        }

        /// <summary>
        /// 校验邮箱格式是否正确
        /// </summary>
        /// <param name="invoiceEntry">开票信息</param>
        /// <param name="errorMsg"></param>
        /// <returns></returns>
        private bool ValidateInvoiceEmails(DynamicObject invoiceEntry,ref string errorMsg)
        {
            Regex emailRegex = new Regex(@"^[^\s@]+@[^\s@]+\.[^\s@]+$");
            
            var isTrue = true;
            
            var invoiceEmail = Convert.ToString(invoiceEntry["finvoiceemail"]);
            
            
            if (!invoiceEmail.IsNullOrEmptyOrWhiteSpace())
            {
                string[] emails = invoiceEmail.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                foreach (var emailStr in emails)
                {
                    var isMatch = emailRegex.IsMatch(emailStr);
                    if (!isMatch)
                    {
                        isTrue = false;
                        errorMsg = $@"开票信息中电子邮箱格式有误，格式类似于***********或者多个邮箱格式************,<EMAIL>";
                        break;
                    }
                }
            }

            return isTrue;
        }

        private bool CheckInvoiceEntryBankAccountIsNumber(DynamicObject invoiceEntry, ref string errorMsg)
        {
            var isTrue = true;
            
            var checkNumRegex = new Regex(@"^\d+$");
            
            var bankAccountStr = Convert.ToString(invoiceEntry["finvoicebankaccount"]);
            
            if (!bankAccountStr.IsNullOrEmptyOrWhiteSpace())
            {
                var isMatch = checkNumRegex.IsMatch(bankAccountStr);
                if (!isMatch)
                {
                    isTrue = false;
                    errorMsg = $@"银行账户输入的格式有误，请检查格式";
                }
            }
            
            return isTrue;
        }

        /// <summary>
        /// 映射开票信息字段
        /// </summary>
        /// <param name="invoiceEntry"></param>
        /// <param name="newInvoiceEntry"></param>
        private void MapInvoiceEntryField(DynamicObject invoiceEntry, DynamicObject newInvoiceEntry)
        {
            newInvoiceEntry["finvoicetype"] = invoiceEntry["finvoicetype"];
            newInvoiceEntry["fbuyerfullname"] = invoiceEntry["fbuyerfullname"];
            newInvoiceEntry["ftaxpayeridentify"] = invoiceEntry["ftaxpayeridentify"];
            newInvoiceEntry["finvoiceemail"] = invoiceEntry["finvoiceemail"];
            newInvoiceEntry["finvoicedefault"] = invoiceEntry["finvoicedefault"];
            newInvoiceEntry["finvoiceaddress"] = invoiceEntry["finvoiceaddress"];
            newInvoiceEntry["finvoicephone"] = invoiceEntry["finvoicephone"];
            newInvoiceEntry["fdepositbankname"] = invoiceEntry["fdepositbankname"];
            newInvoiceEntry["fbankaccount"] = invoiceEntry["fbankaccount"];
        }
    }
}