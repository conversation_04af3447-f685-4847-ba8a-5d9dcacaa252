using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.AgentsParam
{
    [InjectService]
    [FormId("bas_agentsparam|bas_agentlevelfilterparam")]
    [OperationNo("getdistributornumberandname")]
    public class GetDistributorNumberAndName : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            var rowId = this.GetQueryOrSimpleParam<string>("rowId", "");

            var agentId = this.GetQueryOrSimpleParam<string>("fagentId", "");

            var agentDy = this.Context.LoadBizBillHeadDataById("bas_agent", agentId, "fname,fcrmdistributorid");
            var dic = new Dictionary<string, object>();
            if (agentDy == null)
            {

                dic.Add("isOk",false);
                var Message = "对不起，找不到该经销商信息";
                dic.Add("message",Message);
                this.Result.SrvData = dic;
                this.Result.IsSuccess = true;
            }
            else
            {
                var distributorId = Convert.ToString(agentDy["fcrmdistributorid"]);

                if (distributorId.IsNullOrEmptyOrWhiteSpace())
                {
                    dic.Clear();
                    dic.Add("isOk", false);
                    var Message = "该经销商没有关联招商经销商!";
                    dic.Add("message", Message);
                    this.Result.SrvData = dic;
                    this.Result.IsSuccess = true;
                }
                else
                {
                    var distributorIds = distributorId.Split(',').ToList();
                    var distributorDys = this.Context.LoadBizBillHeadDataById("ms_crmdistributor", distributorIds, "fname,fnumber").ToList();

                    if (distributorDys == null || distributorDys.Count <= 0)
                    {
                        dic.Clear();
                        dic.Add("isOk", false);
                        var Message = "对不起，找不到有关的招商经销商!";
                        dic.Add("message", Message);
                        this.Result.SrvData = dic;
                        this.Result.IsSuccess = true;
                    }
                    else
                    {
                        dic.Clear();
                        dic.Add("isOk", true);

                        var distributorNameSb = new StringBuilder();

                        var distributorNumberSb = new StringBuilder();

                        foreach (var distributorDy in distributorDys)
                        {
                            var distributorName = Convert.ToString(distributorDy["fname"]);

                            distributorNameSb.Append(distributorName + ",");



                            var distributorNumber = Convert.ToString(distributorDy["fnumber"]);

                            distributorNumberSb.Append(distributorNumber + ",");

                        }

                        if (distributorNumberSb.Length > 0)
                        {
                            //去除最后一个','
                            distributorNumberSb.Remove(distributorNumberSb.ToString().Length - 1, 1);
                        }

                        if (distributorNameSb.Length > 0)
                        {
                            distributorNameSb.Remove(distributorNameSb.ToString().Length - 1, 1);
                        }

                        dic.Add("distributorNumber", distributorNumberSb.ToString());

                        dic.Add("distributorName", distributorNameSb.ToString());

                        dic.Add("rowId",rowId);

                        this.Result.SrvData = dic;

                        this.Result.IsSuccess = true;
                    }
                }
            }
        }
    }
}