using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.PriceScheme
{
    /// <summary>
    /// 经销定价方案：反禁用
    /// </summary>
    [InjectService]
    [FormId("bas_pricescheme")]
    [OperationNo("UnForbid")]
    public class UnForbid : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 执行操作事务后事件，通知插件对象执行其它事务无关的业务逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            // 清除定价方案缓存
            var priceService = this.Container.GetService<IPriceService>();
            priceService.ClearPriceSchemeCache(this.Context);
        }
    }
}