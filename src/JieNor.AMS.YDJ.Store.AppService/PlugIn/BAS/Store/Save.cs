using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Store
{
    [InjectService]
    [FormId("bas_store")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;

            var storeService = this.Container.GetService<IStoreService>();

            storeService.UpdataTopOrg(this.Context, e.DataEntitys);

            AlterProductAuthDeliverEntrys(e.DataEntitys);
        }

        /// <summary>
        /// 更新商品授权清单送达方明细数据
        /// </summary>
        /// <param name="datas"></param>
        private void AlterProductAuthDeliverEntrys(DynamicObject[] stores)
        {
            if (stores == null || stores.Length == 0)
            {
                return;
            }
            var ProductAuths = new List<DynamicObject>();
            foreach (var store in stores)
            {
                var city = Convert.ToString(store["fmycity"]);
                var fsotreid = Convert.ToString(store["id"]);
                //通过门店的组织id、城市找到商品授权清单
                var ProductAuth = this.Context.LoadBizDataByFilter("ydj_productauth", $"forgid = '{fsotreid}' and fcityid = '{city}'").ToList();
                ProductAuths.AddRange(ProductAuth);
            }
            if (ProductAuths.Any())
            {
                //调用通用保存触发 更新商品授权清单送达方明细数据
                this.Gateway.InvokeBillOperation(this.Context, "ydj_productauth", ProductAuths, "save", null);
            }
        }
    }
}
