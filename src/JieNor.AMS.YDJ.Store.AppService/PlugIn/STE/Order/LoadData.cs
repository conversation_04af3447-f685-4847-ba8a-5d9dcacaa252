using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 订货单：根据主键ID获取数据
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("LoadData")]
    public class LoadData : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var dataEntity = e.DataEntitys[0];

            //加载引用数据
            this.Context.Container.GetService<LoadReferenceObjectManager>()?.Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), dataEntity, false);
            var uiConverter = this.Context.Container.GetService<IUiDataConverter>();

            this.Result.SrvData = uiConverter.CreateUIDataObject(this.Context, this.HtmlForm, dataEntity);
            this.Result.IsSuccess = true;
        }
    }
}