using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：暂存
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("draft")]
    public class Draft : AbstractOperationServicePlugIn
    {

        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            // 处理有问题的商品促销id
            var entrys = e.DataEntitys.SelectMany(s => (s["fentry"] as DynamicObjectCollection));
            foreach (var entry in entrys)
            {
                var fpromotionid = Convert.ToString(entry["fpromotionid"]);
                if (fpromotionid.EqualsIgnoreCase("{}") || fpromotionid.EqualsIgnoreCase("{id:,fnumber:,fname:}"))
                {
                    entry["fpromotionid"] = " ";
                }
            }
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            foreach (var dataEntity in e.DataEntitys)
            {
                Core.Helpers.DocumentStatusHelper.CalcOrderCloseStatus(dataEntity, this.Context);
            }
        }

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            if(e.DataEntitys == null || e.DataEntitys.Length <= 0 ) return;
            if (this.Context.IsDirectSale)
            {
                var getOption = this.GetQueryOrSimpleParam<string>("__fromdirectpurchaseorder__","");
                if (!getOption.IsNullOrEmptyOrWhiteSpace() && getOption.EqualsIgnoreCase("__fromdirectpurchaseorder__"))
                {
                    PushDirectOrderToMusiShareCostBillNo(this.Context, getOption, e.DataEntitys);
                }
            }
        }

        /// <summary>
        /// 推送到中台的共享计提单号
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="option"></param>
        /// <param name="orderDys"></param>
        public void PushDirectOrderToMusiShareCostBillNo(UserContext userCtx,string option,DynamicObject[] orderDys)
        {
            if (userCtx.IsDirectSale)
            {
                //如果是来自直营的采购订单反写销售合同的话(单据类型是门店上样的)，那么就要把有合作渠道的直营销售合同给传给中台，中台给共享，共享给我们，中台再传给我们。
                if (!option.IsNullOrEmptyOrWhiteSpace() && option.EqualsIgnoreCase("__fromdirectpurchaseorder__"))
                {
                    if (orderDys != null && orderDys.Any())
                    {
                        var findChannelOrderDys = orderDys.Where(x=> !Convert.ToString(x["fchannel"]).IsNullOrEmptyOrWhiteSpace()).ToList();
                        if (findChannelOrderDys != null && findChannelOrderDys.Any())
                        {
                            var muSiService = userCtx.Container.GetService<IMuSiService>();
                            muSiService.SyncDirectOrderShareCostBillNo(userCtx, this.HtmlForm, findChannelOrderDys);
                        }
                    }
                }
            }
            
        }
    }
}
