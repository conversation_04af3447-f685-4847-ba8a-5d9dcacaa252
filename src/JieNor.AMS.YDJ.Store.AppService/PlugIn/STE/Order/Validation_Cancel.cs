using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.StockUpdate;
using JieNor.AMS.YDJ.Core.Reserve;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Serialization;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同作废/反作废校验
    /// </summary>
    public class Validation_Cancel : AbstractBaseValidation
    {


        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }

        public virtual string OperationDesc { get; private set; }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }


        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return result;
            }

            //CheckChangeInfo(userCtx, formInfo, dataEntities, result, option, operationNo);

            return result;
        }



        /// <summary>
        /// 下游二级采购变更信息校验
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntitys"></param>
        /// <param name="result"></param>
        /// <param name="option"></param>
        private void CheckChangeInfo(UserContext ctx, HtmlForm formInfo, DynamicObject[] dataEntitys, ValidationResult result, OperateOption option, string operationNo)
        {
            if (dataEntitys == null || !dataEntitys.Any()) return;
            var resellOrders = dataEntitys.Where(x => Convert.ToBoolean(x["fisresellorder"]) && !x["fsourceid"].IsNullOrEmptyOrWhiteSpace());
            var resellPurIds= resellOrders?.Select(x => Convert.ToString(x["fsourceid"]))?.Distinct().ToList();
            if (resellPurIds != null && resellPurIds.Any())
            {
                //二级分销采购订单集合
                var purOrders = this.Context.LoadBizDataById("ydj_purchaseorder", resellPurIds);
                if (purOrders == null || !purOrders.Any()) return;

                var changePurOrders = purOrders.Where(x => Convert.ToString(x["fchangestatus"])=="1");
                if (changePurOrders == null || !changePurOrders.Any()) return;

                if (operationNo.EqualsIgnoreCase("cancel"))
                {
                    //当采购订单的一级合同状态为：提交到一级时且变更状态下
                    //对应上的一级销售合同点击作废。需要提示：二级采购订单处理变更中，不能作废
                    var errValidata = changePurOrders.Where(x => Convert.ToString(x["fonelvoderstatus"]).EqualsIgnoreCase("01"));
                    if(errValidata!=null&& errValidata.Any())
                    {
                        var errPurIds = errValidata.Select(x => Convert.ToString(x["id"])).Distinct();
                        foreach (var item in resellOrders.Where(x=> errPurIds.Contains(Convert.ToString(x["fsourceid"]))))
                        {
                            result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = @"销售合同{0}对应二级采购订单处理变更中，不能作废！".Fmt(item["fbillno"]),
                                DataEntity = item,
                            });
                        }
                    }
                }
                else if (operationNo.EqualsIgnoreCase("uncancel"))
                {
                    //当采购订单的一级合同状态为：订单已驳回时且变更状态下
                    //对应上的一级销售合同点击反作废。需要提示：二级采购订单处理变更中，不能反作废
                    var errValidata = changePurOrders.Where(x => Convert.ToString(x["fonelvoderstatus"]).EqualsIgnoreCase("03"));
                    if (errValidata != null && errValidata.Any())
                    {
                        var errPurIds = errValidata.Select(x => Convert.ToString(x["id"])).Distinct();
                        foreach (var item in resellOrders.Where(x => errPurIds.Contains(Convert.ToString(x["fsourceid"]))))
                        {
                            result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = @"销售合同{0}对应二级采购订单处理变更中，不能反作废！".Fmt(item["fbillno"]),
                                DataEntity = item,
                            });
                        }
                    }
                }
            }
        }
        
    }
}
