using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.AMS.YDJ.MS.API.Utils;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：一件代发
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("dropshipment")]
    public class DropShipment : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var errorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((newData, oldData) =>
            {
                if (!(Convert.ToBoolean(newData["fpiecesendtag"])
                    && Convert.ToString(newData["fstatus"]).EqualsIgnoreCase("E")
                    && !Convert.ToBoolean(newData["frenewalflag"])))
                {
                    errorMessage = $"{this.HtmlForm.Caption}【{newData[this.HtmlForm.GetNumberField().PropertyName]}】合同【一件代发标记】=是 并且【数据状态】=已审核 并且【焕新订单标记】=否：才允许操作此按钮!";
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));
            e.Rules.Add(new DropShipmentValidation());
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            var purchaseOrderFormId = "ydj_purchaseorder";
            var entryIds = new List<Row>();
            List<string> purchaseOrderIds = new List<string>();

            //var selectRowIds = notv6Orders.SelectMany(s => s["fentry"] as DynamicObjectCollection)
            //    .Select(s => new Row { Id = Convert.ToString(s["id"]) });
            //
            e.DataEntitys.ForEach(a =>
            {
                entryIds.AddRange((a["fentry"] as DynamicObjectCollection)
                    .Where(c => Convert.ToString(c["fdeliverytype"]).Equals("delivery_type_01"))
                    .Select(d => new Row { Id = Convert.ToString(d["id"]) }));
            }).ToList();
            var rowIds = entryIds.ToJson();
            Dictionary<string, object> keyValuePairs = new Dictionary<string, object>();
            keyValuePairs.Add("rowIds", rowIds);
            keyValuePairs.Add("IgnoreCheckPermssion", true);
            var pushPurOrderResult = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, e.DataEntitys, "pushpurorder_yjdf", keyValuePairs);
            pushPurOrderResult?.ThrowIfHasError();
            if (pushPurOrderResult.IsSuccess)
            {
                if (pushPurOrderResult.OptionData.TryGetValue("PushPurchaseOrderIds", out var value))
                {
                    if (value is List<string>)
                    {
                        purchaseOrderIds.AddRange((List<string>)value);
                    }
                }
                this.Option.SetVariableValue("DropShipmentSubmitHQ_PurchaseOrderIds", purchaseOrderIds);
                // 找到关联的采购订单
                var purchaseOrders = this.Context.LoadBizDataById(purchaseOrderFormId, purchaseOrderIds);
                var needSubmitPurchaseOrders = purchaseOrders.Where(s =>
                    !Convert.ToString(s["fstatus"]).EqualsIgnoreCase("D") &&
                    !Convert.ToString(s["fstatus"]).EqualsIgnoreCase("E"));

                if (needSubmitPurchaseOrders.Any())
                {
                    var submitResult = this.Gateway.InvokeBillOperation(this.Context, purchaseOrderFormId, needSubmitPurchaseOrders, "submit",
                        new Dictionary<string, object>
                        {
                        { "IgnoreCheckPermssion", true }
                        });
                    submitResult.ThrowIfHasError();
                }

                var needAuditPurchaseOrders = purchaseOrders.Where(s =>
                    Convert.ToString(s["fstatus"]).EqualsIgnoreCase("D"));
                if (needAuditPurchaseOrders.Any())
                {
                    var auditResult = this.Gateway.InvokeBillOperation(this.Context, purchaseOrderFormId, needAuditPurchaseOrders, "audit",
                        new Dictionary<string, object>
                        {
                        { "IgnoreCheckPermssion", true }
                        });
                    auditResult.ThrowIfHasError();
                }

            }
        }
        //public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        //{
        //    base.AfterExecuteOperationTransaction(e);

        //    if (this.Context.IsSecondOrg)
        //    {
        //    }
        //    else
        //    {
        //        var purchaseOrderFormId = "ydj_purchaseorder";
        //        this.Option.TryGetVariableValue<List<string>>("DropShipmentSubmitHQ_PurchaseOrderIds", out var purchaseOrderIds);
        //        // 找到关联的采购订单
        //        var purchaseOrders = this.Context.LoadBizDataById(purchaseOrderFormId, purchaseOrderIds);
        //        if (purchaseOrders != null && purchaseOrders.Any())
        //        {
        //              var submitHqResult=  this.Gateway.InvokeBillOperation(this.Context, purchaseOrderFormId, purchaseOrders, "submithq",
        //                    new Dictionary<string, object>
        //                    {
        //                { "IgnoreCheckPermssion", true }
        //                    });
        //                submitHqResult?.ThrowIfHasError();
        //        }
        //    }
        //}
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (this.Context.IsSecondOrg)
            {
            }
            else
            {
                var purchaseOrderFormId = "ydj_purchaseorder";
                this.Option.TryGetVariableValue<List<string>>("DropShipmentSubmitHQ_PurchaseOrderIds", out var purchaseOrderIds);
                // 找到关联的采购订单
                var purchaseOrders = this.Context.LoadBizDataById(purchaseOrderFormId, purchaseOrderIds);
                if (purchaseOrders != null && purchaseOrders.Any())
                {
                    var submitHqResult = this.Gateway.InvokeBillOperation(this.Context, purchaseOrderFormId, purchaseOrders, "submithq",
                          new Dictionary<string, object>
                          {
                        { "IgnoreCheckPermssion", true }
                          });
                    submitHqResult?.ThrowIfHasError();
                }
            }
            this.AddRefreshPageAction();
        }
    }

    public class DropShipmentValidation : AbstractBaseValidation
    {


        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }

        public virtual string OperationDesc { get; private set; }

        public LoadReferenceObjectManager RefObjMgr { get; private set; }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }


        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return result;
            }
            this.RefObjMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();

            //加载引用数据
            this.RefObjMgr?.Load(this.Context, dataEntities, false, formInfo, new List<string> { "fproductid" });
            List<DynamicObject> checkDatas = new List<DynamicObject>();
            foreach (var dataEntity in dataEntities)
            {
                CheckHasPurOrder(userCtx, dataEntity, formInfo, result);
                CheckPackQty(userCtx, dataEntity, formInfo, result);
            }

            return result;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="formInfo"></param>
        /// <param name="result"></param>
        /// <param name="noOrders"></param>
        private void CheckHasPurOrder(UserContext userCtx, DynamicObject dataEntity, HtmlForm formInfo, ValidationResult result)
        {
            var entry = dataEntity["fentry"] as DynamicObjectCollection;
            if (entry != null)
            {
                var ids = entry.Select(a => Convert.ToString(a["id"])).ToList();
                string sql = string.Format(" select pur.fid from t_ydj_poorderentry pur with(nolock) where fsoorderentryid in ('{0}')", string.Join("','", ids));
                using (var dr = this.Context.ExecuteReader(sql, null))
                {
                    if (dr.Read())
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $@"销售合同{dataEntity["fbillno"]}已生成下游采购订单，不允许再次一件代发操作！",
                            DataEntity = dataEntity,
                        });
                        return;
                    }
                }
            }
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="formInfo"></param>
        /// <param name="result"></param>
        /// <param name="noOrders"></param>
        private void CheckPackQty(UserContext userCtx, DynamicObject dataEntity, HtmlForm formInfo, ValidationResult result)
        {
            var entry = dataEntity["fentry"] as DynamicObjectCollection;
            if (entry != null)
            {
                foreach (var item in entry)
                {
                    var bizqty = Convert.ToDecimal(item["fbizqty"]);
                    var fseq = Convert.ToDecimal(item["fseq"]);
                    var fdeliverytype = Convert.ToString(item["fdeliverytype"]);
                    if (fdeliverytype.Equals("delivery_type_01"))
                    {
                        var matNumber = Convert.ToString((item["fproductid_ref"] as DynamicObject)?["fnumber"]);
                        var packqty = Convert.ToDecimal((item["fproductid_ref"] as DynamicObject)?["fpackqty"]);
                        if (packqty > 1)
                        {
                            if (bizqty % packqty > 0)
                            {
                                result.Errors.Add(new ValidationResultEntry()
                                {
                                    ErrorMessage = $@"{formInfo.Caption}{dataEntity["fbillno"]},第[{fseq}]行商品[{matNumber}]属于箱类商品，交货方式为“总部直发”，但是销售数量不满足整箱倍数，不允许一件代发操作，请核查！",
                                    DataEntity = dataEntity,
                                });
                            }
                        }
                    }
                    //else if (fdeliverytype.Equals("delivery_type_02"))
                    //{
                    //    result.Errors.Add(new ValidationResultEntry()
                    //    {
                    //        ErrorMessage = $@"{formInfo.Caption}{dataEntity["fbillno"]},不存在【交货方式】=总部直发 的商品，无法一件代发，请核查！",
                    //        DataEntity = dataEntity,
                    //    });
                    //}

                }
            }
        }
    }
}
