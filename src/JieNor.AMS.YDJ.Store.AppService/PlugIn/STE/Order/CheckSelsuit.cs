using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：家纺套件要求是否可以编辑
    /// </summary>
    [InjectService]
    [FormId("ydj_order|ydj_purchaseorder")]
    [OperationNo("CheckSelsuit")]
    public class CheckSelsuit : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            string fseries = this.GetQueryOrSimpleParam("fseries", "");
            string fcustom = this.GetQueryOrSimpleParam("fcustom", "");
            bool custom = Convert.ToBoolean(fcustom);
            string fproductid = this.GetQueryOrSimpleParam("fproductid", "");
            var productService = this.Context.Container.GetService<IProductService>();
            var seriesObj = this.Context.LoadBizDataById("ydj_series", fseries);
            var seriesNumber = Convert.ToString(seriesObj?["fnumber"]);
            List<string> categorylist = productService.LoadProductParentCategoryIds(this.Context, fproductid);

            var categorynameobj = this.Context.LoadBizDataByFilter("ydj_category", $"fid in('{string.Join("','", categorylist)}')");
            List<string> categorynamelist = categorynameobj.Select(o => o["fname"].ToString()).ToList();

            // 商品类别层级中存在“床品套件” 且系列编码为A1 且勾选了【允许定制】则可以编辑
            if (seriesNumber.EqualsIgnoreCase("A1") && custom && categorynamelist.Contains("床品套件")) {
                this.Result.IsSuccess = true;
            }
            else
            {
                this.Result.IsSuccess = false;
            }
        }
    }




}
