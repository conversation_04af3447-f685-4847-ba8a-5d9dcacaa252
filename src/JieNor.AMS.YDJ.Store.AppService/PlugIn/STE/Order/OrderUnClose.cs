using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.Framework;
using JieNor.Framework.Consts;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：订单反关闭
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("orderunclose")]
    public class OrderUnClose : AbstractOperationServicePlugIn
    {
        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;
            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fbilltype" });
        }
        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data)
                   .IsTrue((newData, oldData) => Convert.ToString(newData["flockstate"]) != "1")//锁定状态
                   .WithMessage("销售合同[{0}]锁定状态[已锁定],必须未锁定才能执行订单反关闭！", (dataObj, propObj) => dataObj["fbillno"]));

            e.Rules.Add(this.RuleFor("fbillhead", data => data)
                 .IsTrue((newData, oldData) => Convert.ToString(newData["fclosestate"]) == "1")//关闭状态
                 .WithMessage("销售合同[{0}]关闭状态[未关闭],必须已关闭才能执行订单反关闭！", (dataObj, propObj) => dataObj["fbillno"]));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //【焕新订单标记】勾选,若【总部已退款】=是，若操作<订单反关闭>，需拦截提示：“总部已退款并关闭订单，不允许订单反关闭”
                var frenewalflag = Convert.ToBoolean(newData["frenewalflag"]);
                var fiszbrefund = Convert.ToBoolean(newData["fiszbrefund"]);
                if (frenewalflag && fiszbrefund)
                {
                    return false;
                }
                return true;

            }).WithMessage("销售合同【{0}】总部已退款并关闭订单，不允许订单反关闭", (billObj, propObj) => billObj["fbillno"]));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //【焕新订单标记】勾选,若【结算进度】=收款中，若操作<订单反关闭>，需拦截提示：“【结算进度】=收款中并关闭订单，不允许订单反关闭”
                var frenewalflag = Convert.ToBoolean(newData["frenewalflag"]);
                var fsettlprogress = Convert.ToString(newData["fsettlprogress"]);
                if (frenewalflag && fsettlprogress == Enu_RenewalSettleProgress.收款中)
                {
                    return false;
                }
                return true;
            }).WithMessage("销售合同【{0}】的结算进度[收款中]并关闭订单，不允许订单反关闭", (billObj, propObj) => billObj["fbillno"]));
        }



        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            foreach (var dataEntity in e.DataEntitys)
            {
                //设置【关闭日期】=“空”，【关闭状态】=“未关闭”，【关闭人】=“空” 
                dataEntity["fclosestate"] = "0";
                dataEntity["fclosedate"] = null;
                dataEntity["fcloseid"] = "";

                var entryDatas = (dataEntity["fentry"] as DynamicObjectCollection).ToList();

                // 销售数量为0时不允许反关闭
                foreach (var entry in entryDatas)
                {
                    if (Convert.ToDecimal(entry["fbizqty"]) == 0)
                    {
                        this.Result.ComplexMessage.WarningMessages.Add($"第{entry["fseq"]}行商品明细的销售数量为0，不允许反关闭。");
                    }
                }

                // 将【手动关闭】且【销售数量】>0 的明细行设置为【正常】
                entryDatas
                    .FindAll(t => Convert.ToString(t["fclosestatus_e"]) == CloseStatusConst.Manual && Convert.ToDecimal(t["fbizqty"]) > 0)
                    .ForEach(t => t["fclosestatus_e"] = CloseStatusConst.Default);

                // 最终走统一的关闭状态计算逻辑
                Core.Helpers.DocumentStatusHelper.CalcOrderCloseStatus(dataEntity, this.Context);
            }

            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(e.DataEntitys);

            ProductDelistingHelper.DealDelistingDataByCommon(this.Context, this.HtmlForm, this.OperationNo, e.DataEntitys.ToList());

            //OMS定制
            IMuSiService muSiService = this.Container.GetService<IMuSiService>();
            foreach (var item in e.DataEntitys)
            {
                var omsservice = Convert.ToBoolean(item["fomsservice"]);
                var fbilltype = item["fbilltype_ref"] as DynamicObject;
                var v6swjBillTypeName = Convert.ToString(fbilltype["fname"]);
                if (!item["fbilltype"].IsNullOrEmptyOrWhiteSpace() && v6swjBillTypeName == "v6定制柜合同" && omsservice)
                {
                    //实时调用接口将数据传到定制OMS中台。
                    muSiService.OMSSyncAsync(this.Context, this.HtmlForm, new DynamicObject[] { item });
                }
            }

            this.AddRefreshPageAction();
            this.Result.IsSuccess = true;
            this.Result.ComplexMessage.SuccessMessages.Add("订单反关闭成功！");
        }
    }
}