using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：获取当前经销商对应的总部设计师（中台控制的配置信息）
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("gethqdesigner")]
    public class GetHqDesigner : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            var sql = "select top 1 fid,fnumber,fname from t_ste_hqdesign where fmainorgid=@fmainorgid";
            using (var reader = this.DBService.ExecuteReader(this.Context, sql, new SqlParam("@fmainorgid", DbType.String, this.Context.Company)))
            {
                while (reader.Read())
                {
                    Result.SrvData = new Dictionary<string, string>
                    {
                        {"id",reader.GetValueToString("fid")},
                        {"fnumber",reader.GetValueToString("fnumber")},
                        {"fname",reader.GetValueToString("fname")},
                    };
                }
            }
            this.Result.IsSuccess = true;
        }
    }
}
