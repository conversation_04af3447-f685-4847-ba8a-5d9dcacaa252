using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：提交变更
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("SubmitChange")]
    public class SubmitChange : SubmitBase
    {
        
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            e.Rules.Add(new Validation_SubmitChange());
        }
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var changeReason = this.GetQueryOrSimpleParam<string>("changeReason");
            if (string.IsNullOrWhiteSpace(changeReason))
            {
                throw new BusinessException("变更申请原因不能为空！");
            }
            base.BeginOperationTransaction(e);

            //变更后同步数据至K3《订购意向书》与 K3《销售订单》
            foreach (var dataEntity in e.DataEntitys)
            {
                //this.SynChangeOrderData(dataEntity);
            }
        }


        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            this.Option.SetVariableValue("UpdateReserveDate", true);
            var result = ReserveUtil.UpdateReserve(this.Context, this.HtmlForm, e.DataEntitys, this.Option);
            this.Result.MergeResult(result);

            ModifyStatus(e.DataEntitys);
        }

        /// <summary>
        /// 修改变更单状态
        /// </summary>
        /// <param name="fnumber"></param>
        private void ModifyStatus(DynamicObject[] data)
        {
            var changeReason = this.GetQueryOrSimpleParam<string>("changeReason");
            List<string> sql = new List<string>();
            foreach (var item in data)
            {
                sql.Add($@"/*dialect*/update t_ydj_order_chg set fstatus='D',fchangereason='{changeReason}' where 
                fid in (select top 1 fid from t_ydj_order_chg where fsourcenumber='{Convert.ToString(item["fbillno"])}' 
                and fsourcetype='ydj_order'  and fstatus='B' order by fid desc) ");
            }

            if (sql != null && sql.Count > 0)
            {
                try
                {
                    var dbSvc = this.Container.GetService<IDBServiceEx>();
                    dbSvc.ExecuteBatch(this.Context, sql);
                }
                catch
                {

                }

            }
        }

        /// <summary>
        /// 协同变更：
        ///	变更后同步数据至K3《订购意向书》与 K3《销售订单》
        /// </summary>
        /// <param name="incomeDisburse"></param>
        private void SynChangeOrderData(DynamicObject dataEntity)
        {
            //协同企业ID
            var target = this.GetSyncTargetSEP(dataEntity);

            //数据发送时采用异步消息模式发送，消息中指定回调类型
            var responseResult = this.Gateway.Invoke(
                this.Context,
                target,
                new CommonBillDTO()
                {
                    FormId = "order",
                    OperationNo = "SynChange",
                    BillData = "",
                    ExecInAsync = false,
                    AsyncMode = (int)Enu_AsyncMode.Background,
                    SimpleData = new Dictionary<string, string>
                    {
                        { "billno", dataEntity["fbillno"] as string } //合同编号
                    }
                }) as CommonBillDTOResponse;
            var invokeResult = responseResult?.OperationResult;
            if (invokeResult != null && !invokeResult.IsSuccess)
            {
                var errorMsgs = invokeResult.ComplexMessage.ErrorMessages;
                if (errorMsgs?.Count > 0)
                {
                    this.Result.ComplexMessage.ErrorMessages.AddRange(errorMsgs);
                    throw new BusinessException("变更同步数据失败！");
                }
            }
            invokeResult?.ThrowIfHasError(true, $"变更同步数据失败，对方系统未返回任何响应！");
        }

        /// <summary>
        /// 获取协同目标地址
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        private TargetSEP GetSyncTargetSEP(DynamicObject dataEntity)
        {
            //如果商品存在发布企业，则认为是直营模式
            var productEntry = dataEntity?["fentry"] as DynamicObjectCollection;
            var productId = productEntry?.FirstOrDefault(o => Convert.ToString(o["foperationmode"]).EqualsIgnoreCase("1"))?["fproductid"];
            if (productId.IsNullOrEmptyOrWhiteSpace()) return null;

            var productForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, productForm.GetDynamicObjectType(this.Context));
            var product = dm.Select(productId) as DynamicObject;
            if (product == null) return null;

            var publishField = productForm?.GetField(productForm.PublishCIdFldKey) as HtmlCompanyField;
            var publishcid = publishField?.DynamicProperty?.GetValue<string>(product);
            var publishpid = publishField?.ProductIdDynamicProperty?.GetValue<string>(product);

            var supplierService = this.Container.GetService<ISupplierService>();
            return supplierService.GetSyncTargetSEP(this.Context, publishcid, publishpid);
        }
        
    }
}
