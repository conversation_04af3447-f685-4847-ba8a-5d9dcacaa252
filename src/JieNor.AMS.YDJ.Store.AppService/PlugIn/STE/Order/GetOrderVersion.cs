using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：获取版本号
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("getorderversion")]
    public class GetOrderVersion : AbstractOperationServicePlugIn
    {

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {

            this.Result.SrvData = "";
            var billno = this.GetQueryOrSimpleParam<string>("billno", "");
            if (string.IsNullOrWhiteSpace(billno))
            {
                //空不管
                this.Result.IsSuccess = true;
                return;
            }
            var orders = this.Context.LoadBizDataByFilter(this.HtmlForm.Id, " fbillno=@billno", false, new List<SqlParam>() { new SqlParam("@billno", DbType.String, billno) });
            if (orders.Count > 0)
            {
                this.Result.SrvData = orders.FirstOrDefault()["fswjversion"];

            }
        }

    }
}
