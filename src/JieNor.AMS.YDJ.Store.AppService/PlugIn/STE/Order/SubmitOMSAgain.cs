using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.Store.AppService.MuSi;
using JieNor.AMS.YDJ.Store.AppService.MuSi.DTO;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同:重新提交oms
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("submitomsagain")]
    public class SubmitOMSAgain : AbstractOperationServicePlugIn
    {
        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fbilltype", "fproductid" });
        }

        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);
            if (selectRowIds == null || selectRowIds.Count == 0) return;

            string errorMsg = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (selectRowIds == null || selectRowIds.Count == 0)
                {
                    errorMsg = "请先选择商品行！";
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMsg));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var entry = newData["fentry"] as DynamicObjectCollection;
                foreach (var item in entry)
                {
                    string billtype = Convert.ToString(newData["fbilltype"]);
                    if (string.IsNullOrWhiteSpace(Convert.ToString(item["fcustomdes_e"])) && Convert.ToString((item["fproductid_ref"] as DynamicObject)?["fnumber"]).Equals("VFZ1-M001"))
                    {
                        errorMsg = $"第{Convert.ToString(item["fseq"])}行【定制说明】不能为空！";
                        return false;
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMsg));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["findenttype"]).Equals("H"))
                {
                    var entry = newData["fentry"] as DynamicObjectCollection;
                    foreach (var item in entry)
                    {
                        if (selectRowIds.Any(a => a.Id == Convert.ToString(item["id"])))
                        {
                            if (string.IsNullOrWhiteSpace(Convert.ToString(item["faftreason"])))
                            {
                                errorMsg = $"第{Convert.ToString(item["fseq"])}行【售后原因】不能为空！";
                                return false;
                            }
                            if (string.IsNullOrWhiteSpace(Convert.ToString(item["fomsreason"])))
                            {
                                errorMsg = $"第{Convert.ToString(item["fseq"])}行【原因描述】不能为空！";
                                return false;
                            }
                        }
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMsg));



            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var entry = newData["fentry"] as DynamicObjectCollection;
                foreach (var item in entry)
                {
                    string omsdeliver = Convert.ToString(item["fomsdeliver"]);
                    int omsdeliverqty = Convert.ToInt32(item["fomsdeliverqty"]);
                    if (omsdeliverqty == 0 && string.IsNullOrWhiteSpace(omsdeliver))
                    {
                        return false;
                    }
                }
                //if (!temp.IsNullOrEmptyOrWhiteSpace())
                //{
                //    return CheckIsV6Order(temp);
                //}
                return true;
            }).WithMessage("该门店无法找到V6送达方，无法提交OMS，请检查门店是否正确，谢谢！"));

        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;

            new OrderCommon(this.Context).UpdateProjectAttr(e.DataEntitys);
        }


        private DynamicObjectCollection GetAttachmentList(DynamicObject orderObj)
        {
            var attachFormMeta = this.MetaModelService.LoadFormModel(this.Context, "bd_attachlist");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, attachFormMeta.GetDynamicObjectType(this.Context));
            var pkIdReader = this.Context.GetPkIdDataReader(attachFormMeta, "flinkformid=@linkFormId and flinkbillinterid=@linkBillId", new SqlParam[]
            {
                new SqlParam("linkFormId", System.Data.DbType.String, this.HtmlForm.Id),
                new SqlParam("linkBillId", System.Data.DbType.String, orderObj["id"]),
            });

            var linkAttachBillObj = dm.SelectBy(pkIdReader)
                .OfType<DynamicObject>()
                .FirstOrDefault();
            if (linkAttachBillObj == null) return null;

            var entrys = (DynamicObjectCollection)linkAttachBillObj["fdrawentity"];

            return entrys;
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);
            if (selectRowIds == null || selectRowIds.Count == 0) return;
            var syncingEntrys = e.DataEntitys[0]["fentry"] as DynamicObjectCollection;
            SyncOmsFactoryOrder(e.DataEntitys[0], syncingEntrys, selectRowIds);
            this.AddRefreshPageAction();
            this.Result.ComplexMessage.SuccessMessages.Add($"提交OMS执行完成! ");


        }
        /// <summary>
        /// 发送终端订单进度(OMS)
        /// </summary>
        /// <param name="order"></param>
        /// <param name="syncingEntrys"></param>
        private void SyncOmsFactoryOrder(DynamicObject order, DynamicObjectCollection syncingEntrys, List<Row> selectRowIds)
        {
            var dto = new OmsOrderPogressDTO();
            foreach (var entry in syncingEntrys)//批量提交是通过分多个报文
            {
                var selRowItem = selectRowIds.Where(a => a.Id == Convert.ToString(entry["id"])).FirstOrDefault();
                if (selRowItem != null)
                {
                    dto.Entrys = (BuildData(order, entry));
                    var resp = MuSiApi.SendOmsFactoryOrderAsync(this.Context, this.HtmlForm, dto);
                    //if (resp.Code != 200)//回滚
                    //{
                    //    throw new BusinessException($"定制OMS：{this.HtmlForm.Caption}【提交OMS】 同步失败，失败原因：" + resp.Msg);
                    //}
                    //else
                    //{
                    foreach (var dynamicObject in syncingEntrys)//将【定制订单进度】设置为“待接单”
                    {
                        if (Convert.ToString(dynamicObject["id"]) == selRowItem.Id)
                        {
                            dynamicObject["fomsorderdate"] = dto.Entrys.omsOrderDate;
                            dynamicObject["fissubmitoms"] = 1;
                            dynamicObject["fomsprogress"] = "10";
                        }
                    }
                    //}
                }
            }

            var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_order", new[] { order }, "save",
                new Dictionary<string, object>());
            //this.Context.SaveBizData(this.HtmlForm.Id, order);

        }
        /// <summary>
        /// 构建数据包
        /// </summary>
        /// <param name="order"></param>
        /// <param name="fentry"></param>
        /// <returns></returns>
        private OmsOrderPogressDTO.OmsOrderPogressEntryData BuildData(DynamicObject order, DynamicObject fentry)
        {
            var entryId = Convert.ToString(fentry["ftranid"]);
            var fbillno = Convert.ToString(order["fbillno"]);
            var fomsprogress = Convert.ToString(fentry["fomsprogress"]);
            var fomsbillno = Convert.ToString(fentry["fomsbillno"]);
            var fdescription = Convert.ToString(fentry["fdescription"]);
            var fordertype = Convert.ToString(fentry["fordertype"]);
            var fstonetable = Convert.ToInt32(fentry["fstonetable"]);
            var fdesignremark = Convert.ToString(fentry["fdesignremark"]);
            var ffactoryremark = Convert.ToString(fentry["ffactoryremark"]);
            var funstdtypefactory = Convert.ToInt32(fentry["funstdtypefactory"]);
            var fissubmitoms = Convert.ToInt32(fentry["fissubmitoms"]);
            var ffactorybillno = Convert.ToString(fentry["ffactorybillno"]);

            var faftreason = Convert.ToString(fentry["faftreason"]);
            var fomsreason = Convert.ToString(fentry["fomsreason"]);
            var fomsoptionid = Convert.ToString(fentry["fomsoptionid"]);
            var faftserviceisfree = Convert.ToInt32(fentry["faftserviceisfree"]);
            var omsdesignfile = Convert.ToString(fentry["fomsdesignfile"]);
            var swjordernumber_e = Convert.ToString(fentry["fswjordernumber_e"]);

            string aftreason = "";
            switch (faftreason)
            {
                case "aftreason_1":
                    aftreason = "1";
                    break;
                case "aftreason_2":
                    aftreason = "2";
                    break;
                case "aftreason_3":
                    aftreason = "3";
                    break;
                case "aftreason_4":
                    aftreason = "4";
                    break;
                case "aftreason_5":
                    aftreason = "5";
                    break;
                default:
                    break;
            }

            var dto = new OmsOrderPogressDTO.OmsOrderPogressEntryData
            {
                saleOrderNum = fbillno,
                outId = entryId,
                orderStatus = fissubmitoms == 1 ? "10" : "5",
                code = fomsbillno,
                msg = fdescription,
                type = fordertype,
                stoneCountertop = fstonetable == 1 ? "Y" : "N",
                designRemark = fdesignremark,
                factoryRemark = ffactoryremark,
                unstandardType = funstdtypefactory == 1 ? "Y" : "N",
                swjCode = ffactorybillno,
                serviceReasons = aftreason,
                serviceReasonsDesc = fomsreason,
                schemeId = fomsoptionid,
                factoryResponsibilityFlag = faftserviceisfree,// == 1 ? "Y" : "N",
                designProposalUrl = omsdesignfile,
                omsOrderDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                swjOrdCode= ffactorybillno,
                multipleOrderNum = swjordernumber_e


            };
            return dto;
        }
        /*
         * 枚举值。 1.设计责；2.安装责；3.客户增加；4.运输损坏；5.工厂责
         */


    }


}
