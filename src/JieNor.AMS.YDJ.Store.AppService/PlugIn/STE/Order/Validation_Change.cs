using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    public class Validation_Change : AbstractBaseValidation
    {
        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);
        }

        /// <summary>
        /// 校验结果
        /// </summary>
        private ValidationResult Result { get; set; } = new ValidationResult();

        /// <summary>
        /// 校验逻辑处理单元
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return this.Result;
            }

            var fmainorgids = dataEntities.Select(x => Convert.ToString(x["fmainorgid"])).ToList();
            var agentInfo = GetAgentInfo(userCtx, fmainorgids);

            CheckOrderData(userCtx, dataEntities, agentInfo);

            return this.Result;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        /// <param name="agentInfo"></param>
        /// <param name="getRenewType"></param>
        private void CheckOrderData(UserContext userCtx, IEnumerable<DynamicObject> dataEntities, Dictionary<string, bool> agentInfo)
        {
            foreach (var dataEntity in dataEntities)
            {
                var fbillno = Convert.ToString(dataEntity["fbillno"]);
                //创建单据的经销商的经销类型-直营
                var fmainorgid = Convert.ToString(dataEntity["fmainorgid"]);
                var frenewalflag = Convert.ToBoolean(dataEntity["frenewalflag"]);//勾选焕新订单标记
                var fchstatus = Convert.ToSingle(dataEntity["fchstatus"]);//协同总部状态
                var isfmainorgid = agentInfo.GetValue(fmainorgid);
                if (isfmainorgid && frenewalflag && !fchstatus.IsNullOrEmptyOrWhiteSpace())
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"操作失败，订单编号：{fbillno}当勾选了【焕新订单标记】且创建该单据的经销商经营类型=【直营】且【协同总部状态＝提交至总部、已驳回、已终审】时,不允许操作！",
                        DataEntity = dataEntity,
                    });
                }
            }
        }

        /// <summary>
        /// 获取当前经销商相关字段信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        private Dictionary<string, bool> GetAgentInfo(UserContext userCtx, List<string> fmainorgids)
        {
            var dic = new Dictionary<string, bool>();
            if (fmainorgids.Count == 0) return dic;

            var agentInfo = userCtx.LoadBizBillHeadDataById("bas_agent", fmainorgids, "fmainorgid,fname,fnumber,actualownernumber,fmanagemodel").ToList();

            foreach (var item in agentInfo)
            {
                if (!dic.ContainsKey(Convert.ToString(item["fmainorgid"])))
                {
                    var fmanagemodel = Convert.ToString(item["fmanagemodel"]) == "1" ? true : false;
                    dic.Add(Convert.ToString(item["fmainorgid"]), fmanagemodel);
                }
            }

            return dic;
        }
    }
}
