using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 《销售合同》开单，选择【销售部门】自动带出【门店名称】时,检查门店
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("checkstore")]
    public class CheckStore : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            this.Result.IsSuccess = true;

            //3.PC端与小程序端，《销售合同》开单，选择【销售部门】自动带出【门店名称】时：

            //3.1.查询【门店名称】是否禁用，如果禁用则需提示：“对不起，XXX部门关联的门店已禁用，禁止下单！”

            //3.2.根据当前用户登录的经销商，判断是否存在《主经销商配置表》：

            //3.2.1.如果不存在：如果当前用户登录的【经销商编码】与【 关联门店 】的所属经销商的【经销商编码】不一致，则提示：“对不起，XXX部门关联的门店所属组织与当前登录组织不一致，禁止下单！”。

            //3.2.2.如果存在：再判断【主经销商编码】、 【子经销商编码】是否与【 关联门店 】的所属经销商的【经销商编码】一致，如果不一致，需提示：“对不起，XXX部门关联的门店所属组织与当前登录组织不一致，禁止下单！”。


            var isMpApi = this.GetQueryOrSimpleParam<string>("IsMpApi");
            var deptName = this.GetQueryOrSimpleParam<string>("deptName");
            var storeId = this.GetQueryOrSimpleParam<string>("storeId");
            var companyId = this.Context.Company;
            if (storeId.IsNullOrEmptyOrWhiteSpace() || companyId.IsNullOrEmptyOrWhiteSpace()) return;

            var sql = $@"/*dialect*/  select fforbidstatus from t_bas_store  with(nolock) where fid='{storeId}'";
            var storeObj = this.DBService.ExecuteDynamicObject(this.Context, sql).FirstOrDefault();

            if (!storeObj.IsNullOrEmptyOrWhiteSpace())
            {
                var fforbidstatus = storeObj["fforbidstatus"];
                if (fforbidstatus.Equals("1"))
                {
                    this.Result.IsSuccess = false;
                    throw new BusinessException($@"对不起，{deptName}部门关联的门店已禁用，禁止下单！");
                }
                    
            }

            sql = $@"/*dialect*/ select 1 from t_bas_mac t0 with(nolock) inner join T_BAS_ORGANIZATION t1 with(nolock) on t0.fmainagentid=t1.fid where t1.fid='{companyId}'";
            var count = this.DBService.ExecuteDynamicObject(this.Context, sql).Count;
            if (count > 0)
            {
                sql = $@"/*dialect*/  select 1
                                     from t_bas_mac t0 with(nolock)
                                     inner join T_BAS_ORGANIZATION t1 with(nolock) on t0.fmainagentid=t1.fid 
                                     inner join t_bas_macentry t2 with(nolock) on t2.fid=t0.fid
                                     inner join t_bas_store t3 with(nolock) on t3.fagentid=t0.fmainagentid or t3.fagentid=t2.fsubagentid
                                     where t3.fid='{storeId}' and t1.fid='{companyId}'";
                count = this.DBService.ExecuteDynamicObject(this.Context, sql).Count;

                if (count > 0)
                {

                }
                else
                {
                    this.Result.IsSuccess = false;
                    var errMsg = $@"对不起，{deptName}部门关联的门店所属组织与当前登录组织不一致，禁止下单！";
                    if (!isMpApi.IsNullOrEmptyOrWhiteSpace() && isMpApi.Equals("1"))
                    {
                        this.Result.SimpleMessage = errMsg;
                    }
                    else
                    {
                        throw new BusinessException(errMsg);
                    }
                    
                }
            }
            else
            {
                sql = $@"/*dialect*/ select 1 from t_bas_store t0 with(nolock) inner join T_BAS_ORGANIZATION t1 with(nolock) on t0.fagentid=t1.fid where t0.fid='{storeId}' and t1.fid='{companyId}'";
                count = this.DBService.ExecuteDynamicObject(this.Context, sql).Count;

                if (count > 0)
                {

                }
                else
                {
                    this.Result.IsSuccess = false;
                    var errMsg = $@"对不起，{deptName}部门关联的门店所属组织与当前登录组织不一致，禁止下单！";
                    if (!isMpApi.IsNullOrEmptyOrWhiteSpace() && isMpApi.Equals("1"))
                    {
                        this.Result.SimpleMessage = errMsg;
                    }
                    else
                    {
                        throw new BusinessException(errMsg);
                    }
                }

            }


        }

    }
}
