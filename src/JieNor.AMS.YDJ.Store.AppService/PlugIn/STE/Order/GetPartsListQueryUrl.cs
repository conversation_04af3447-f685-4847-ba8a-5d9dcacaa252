using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.BarcodeMaster;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Print;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.Office;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同/采购订单：获取OMS 部件清单查询 url
    /// </summary>
    [InjectService]
    [FormId("ydj_order|ydj_purchaseorder")]
    [OperationNo("partslistquery")]
    public class GetPartsListQueryUrl : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var url = string.Empty;
            this.Result.IsSuccess = false;
            this.Result.SrvData = "";

            var omsbillno = this.GetQueryOrSimpleParam("omsbillno", "");

            if (omsbillno.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"对不起，定制OMS单号为空,跳转失败！");
            }

            string omsurl = this.GetAppConfig("ms.oms.redirecturi");
            if (string.IsNullOrWhiteSpace(omsurl))
            {
                throw new BusinessException($"未获取到OMS地址配置信息，跳转失败！");
            }

            //自测使用
            //url = "https://wx.derucci.com:8093/#/design/oms_component_info_king?platform=oms&token=kingde&omsbillno=5007377240521006L";

            url = omsurl + $@"design/oms_component_info_king?platform=oms&token=kingde&omsbillno={omsbillno}";

            this.Result.IsSuccess = true;
            this.Result.SrvData = url;
        }

    }
}
