using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Serialization;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 填充促销活动数据
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("FillPromotionData")]
    public class FillPromotionData : AbstractOperationServicePlugIn
    {
        static List<string> PromotionFormIds = new List<string>() { "ydj_productpromotion", "ydj_combopromotion" };

        protected IOrderService OrderService { get; set; }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            this.OrderService = this.Container.GetService<IOrderService>();

            string promotionIdsStr = this.GetQueryOrSimpleParam<string>("promotionIds");
            string promotionType = this.GetQueryOrSimpleParam<string>("promotionType");
            var promotionIds = promotionIdsStr.SplitKey();

            CheckValid(promotionType, promotionIds);

            var order = GetBillData();
            this.Container.GetService<IPrepareSaveDataService>()
                .RemoveEmptyOrInvalidRow(this.Context, this.HtmlForm, new[] { order });

            var orderPromotionService = this.Container.GetService<IOrderPromotionService>();

            switch (promotionType.ToLower())
            {
                case "ydj_combopromotion":
                    var comboPromotions = this.Context.LoadBizDataById(promotionType, promotionIds);
                    orderPromotionService.UseComboPromotion(this.Context, order, comboPromotions);
                    break;
                case "ydj_productpromotion":
                    var prodcutPromotion = this.Context.LoadBizDataById(promotionType, promotionIds.First());
                    var customParam = this.GetQueryOrSimpleParam<string>("customParam");
                    var gifts = JArray.Parse(customParam);
                    orderPromotionService.UseProductPromotion(this.Context, order, prodcutPromotion, gifts);
                    break;
            }

            var refMgr = this.Container.GetService<LoadReferenceObjectManager>();
            var dt = this.HtmlForm.GetDynamicObjectType(this.Context);
            refMgr.Load(this.Context, dt, order, false);

            var uiDataConverter = this.Container.GetService<IUiDataConverter>();

            var uiData = uiDataConverter.CreateUIDataObject(this.Context, this.HtmlForm, order);

            this.Result.SrvData = uiData;
        }

        private void CheckValid(string promotionType, List<string> promotionIds)
        {
            if (!PromotionFormIds.Contains(promotionType, StringComparer.OrdinalIgnoreCase))
            {
                throw new WarnException($"不支持促销活动类型{promotionType}！");
            }

            if (promotionIds.Count == 0)
            {
                throw new WarnException($"促销活动至少选择一个！");
            }

            if (promotionType.EqualsIgnoreCase("ydj_productpromotion") && promotionIds.Count > 1)
            {
                throw new WarnException($"商品促销不支持批量选择！");
            }
        }

        /// <summary>
        /// 获取数据包
        /// </summary>
        /// <returns></returns>
        private DynamicObject GetBillData()
        {
            var billData = this.GetQueryOrSimpleParam<string>("billData");
            if (billData.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("参数错误，billData不能为空，请检查！");
            }

            return DynamicObjectUtil.GetDynamicObject(this.Context, this.HtmlForm, billData);
        }
    }
}