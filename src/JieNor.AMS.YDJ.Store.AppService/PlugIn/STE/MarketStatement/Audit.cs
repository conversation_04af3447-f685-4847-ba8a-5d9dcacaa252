using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.MarketStatement
{
    /// <summary>
    /// 卖场对账单：审核
    /// </summary>
    [InjectService]
    [FormId("ydj_marketstatement")]
    [OperationNo("Audit")]
    public class Audit : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var isCheck = this.GetQueryOrSimpleParam("ischeckbill", "true");
            var ischeckbill = isCheck.IsNullOrEmptyOrWhiteSpace() ? true : Convert.ToBoolean(isCheck);

            e.Rules.Add(new Validation_Check(ischeckbill));
        }
        
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            var incomeIds = e.DataEntitys.SelectMany(x => x["fentry"] as DynamicObjectCollection)
                                         .Select(x => Convert.ToString(x["fincomeid"]))
                                         .Where(x => false == string.IsNullOrWhiteSpace(x))
                                         .Distinct()
                                         .ToList();

            if (incomeIds == null || incomeIds.Count <= 0)
            {
                return;
            }

            var incomeForm = this.MetaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            var incomeDm = this.Container.GetService<IDataManager>();
            incomeDm.InitDbContext(this.Context, incomeForm.GetDynamicObjectType(this.Context));

            var incomeEntities = incomeDm.Select(incomeIds).OfType<DynamicObject>().ToArray();
            if (incomeEntities == null || incomeEntities.Length <= 0)
            {
                return;
            }

            var unVerificEntities = new List<DynamicObject>();
            var verificedEntities = new List<DynamicObject>();
            foreach (var incomeEntity in incomeEntities)
            {
                var statementStatus = Convert.ToString(incomeEntity["fstatementstatus"]);
                var tranId = Convert.ToString(incomeEntity["ftranid"]);
                if (statementStatus == "2")
                {
                    incomeEntity["fstatementstatus"] = "3";
                }
                //无需检验【核销状态】2023-04-07
                //var verificStatus = Convert.ToString(incomeEntity["fverificstatus"]);
                //switch (verificStatus)
                //{
                //    case "verificstatus_01":
                //    case "verificstatus_02":
                //        //    unVerificEntities.Add(incomeEntity);
                //        //    break;
                //        //case "verificstatus_02":
                //        //
                //        incomeEntity["fverificstatus"] = "verificstatus_02";
                //        verificedEntities.Add(incomeEntity);
                //        break;
                //    default:
                //        throw new BusinessException($"无法识别流水号为[{tranId}]的收支记录的核销状态!");
                //}
            }

            if (unVerificEntities.Count > 0)
            {
                var option = new Dictionary<string, object>
                {
                    { "originalOpNo","Audit"},
                    { "originalFormId","ydj_marketstatement"}
                };
                var result = this.Gateway.InvokeBillOperation(this.Context, incomeForm.Id, unVerificEntities, "Verific", option);
                result.ThrowIfHasError(true, "调用收支记录核销接口失败!");
            }

            //if (verificedEntities.Count > 0)
            //{
            //    var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
            //    prepareSaveDataService.PrepareDataEntity(this.Context, incomeForm, verificedEntities.ToArray(), OperateOption.Create());
            //    incomeDm.Save(verificedEntities);
            //}

            if (incomeEntities.Length > 0)
            {
                var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
                prepareSaveDataService.PrepareDataEntity(this.Context, incomeForm, incomeEntities, OperateOption.Create());
                incomeDm.Save(incomeEntities);
            }
        }
    }
}
