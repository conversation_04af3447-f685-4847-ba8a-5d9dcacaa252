using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SaleIntention
{
    /// <summary>
    /// 销售订单：获取销售结算信息
    /// </summary>
    [InjectService]
    [FormId("ydj_saleintention")]
    [OperationNo("LoadSettleInfo")]
    public class LoadSettleInfo : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var dataEntity = e.DataEntitys[0];

            //加载引用数据
            this.Context.Container.GetService<LoadReferenceObjectManager>()?.Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), dataEntity, false);

            var uiConverter = this.Context.Container.GetService<IUiDataConverter>();
            var customerField = this.HtmlForm.GetField("fcustomerid");
            var customer = dataEntity?["fcustomerid_ref"] as DynamicObject;

            Dictionary<string, object> dicSettle = new Dictionary<string, object>();
            dicSettle["fsourceformid"] = this.HtmlForm.Id;
            dicSettle["fsourceid"] = dataEntity?["id"] as string;
            dicSettle["fsourcenumber"] = dataEntity?["fbillno"] as string;
            dicSettle["fsourcetranid"] = dataEntity?["ftranid"] as string;
            dicSettle["fsettlemaintype"] = "ydj_customer";
            dicSettle["fsettlemainid"] = customer["id"];
            dicSettle["fsettlemainname"] = customer["fname"];
            dicSettle["fbilltypeid"] = dataEntity?["fbilltypeid"] as string;
            dicSettle["freceiptstatus"] = dataEntity?["freceiptstatus"] as string;
            dicSettle["fbizstatus"] = dataEntity?["fbizstatus"] as string;
            dicSettle["fcustomerid"] = uiConverter.PackageFieldData(this.Context, this.HtmlForm, customerField, dataEntity, null, false);
            setContactUnitInfo(dataEntity, dicSettle, uiConverter);

            //结算类型：收款，退款
            var settleType = this.GetQueryOrSimpleParam<string>("settleType", "");
            //销售参数的“销售单据（意向及合同）不审核可退款”:  是否勾选
            var sysparamService = this.Context.Container.GetService<ISystemProfile>();
            var fcanrefund = sysparamService.GetSystemParameter(this.Context, "bas_storesysparam", "fcanrefund", false);
            //销售参数的“销售单据（意向及合同）不审核可收款”:  是否勾选
            var fcanreceipt = sysparamService.GetSystemParameter(this.Context, "bas_storesysparam", "fcanreceipt", false);
            //业务类型
            var saleService = this.Container.GetService<ISaleIntentionService>();
            var isSync = saleService.CheckIsSync(this.Context, dataEntity);
            dicSettle["fissyn"] = isSync;
            if (isSync)
            {
                dicSettle["fsettledamount"] = dataEntity?["freceivedamount"];
                dicSettle["funsettleamount"] = dataEntity?["freceiptamount"];
                dicSettle["funconfirmamount"] = dataEntity?["fconfirmamount"];

                //协同方银行账号信息
                dicSettle["synBankNum"] = this.GetSynBankNum(customer, dataEntity);
            }
            else
            {
                this.CheckIsPushOrder(dataEntity?["fbillno"] as string);

                switch (settleType.ToLower())
                {
                    case "receipt":
                        var sysProfile = this.Container.GetService<ISystemProfile>();
                        var enableCollectAmount = sysProfile.GetSystemParameter<bool>(this.Context, "bas_storesysparam", "fenablecollectamount", false);
                        //如果未启用销售意向定金启用应收控制,允许超额收款
                        if (enableCollectAmount)
                        {
                            if (Convert.ToDecimal(dataEntity?["fcollectamount"]) <= 0)
                            {
                                throw new BusinessException($"应收定金小于等于0，无法收取定金！");
                            }
                            if (Convert.ToDecimal(dataEntity?["fcollectamount"]) <= Convert.ToDecimal(dataEntity?["fcollectedamount"]))
                            {
                                throw new BusinessException($"当前已收定金大于等于应收定金，无需收取定金！");
                            }
                        }
                        if (!fcanreceipt)//如果未勾择 |销售参数的“销售单据（意向及合同）不审核可收款”| => 则需要审核后才可收款
                        {
                            if (!Convert.ToString(dataEntity?["fstatus"]).Equals("E"))
                            {
                                throw new BusinessException($"当前单据未审核，无法收款！");
                            }
                        }
                        dicSettle["fsettletype"] = "收款";
                        // 已收款
                        dicSettle["fsettledamount"] = Convert.ToDecimal(dataEntity?["fcollectedamount"]).ToString("f2");
                        // 未收款
                        dicSettle["funsettleamount"] = (Convert.ToDecimal(dataEntity?["fcollectamount"]) - Convert.ToDecimal(dataEntity?["fcollectedamount"])).ToString("f2");
                        // 确认已收（小程序使用）
                        dicSettle["fconfirmedamount"] = Convert.ToDecimal(dataEntity?["fconfirmedamount"]).ToString("f2");
                        break;

                    case "refund":
                        if (Convert.ToDecimal(dataEntity?["fconfirmedamount"]) <= 0)
                        {
                            throw new BusinessException($"确认已收金额小于等于0，无法退款！");
                        }
                        if (!fcanrefund)//如果未勾择 销售参数的“销售单据（意向及合同）不审核可退款” => 则需要审核后才可退款
                        {
                            if (!Convert.ToString(dataEntity?["fstatus"]).Equals("E"))
                            {
                                throw new BusinessException($"当前单据未审核，无法退款！");
                            }
                        }
                        dicSettle["fdirection"] = "direction_01";
                        dicSettle["fbizdirection"] = "bizdirection_02";
                        dicSettle["fpurpose"] = "bizpurpose_06";
                        dicSettle["fsettletype"] = "退款";
                        dicSettle["fsettledamount"] = Convert.ToDecimal(dataEntity?["fconfirmedamount"]).ToString("f2");
                        dicSettle["funsettleamount"] = (Convert.ToDecimal(dataEntity?["fconfirmedamount"]) - Convert.ToDecimal(dataEntity?["factrefundamount"])).ToString("f2");
                        break;

                    default:
                        break;
                }
                // 应收定金（未知用途）
                dicSettle["funconfirmamount"] = Convert.ToDecimal(dataEntity?["fcollectamount"]).ToString("f2");
                // 应收定金（未知用途）
                dicSettle["freceivable"] = Convert.ToDecimal(dataEntity?["fcollectamount"]).ToString("f2");

                // 订单金额（小程序使用）
                dicSettle["fsumamount"] = Convert.ToDecimal(dataEntity?["ffbillamount"]).ToString("f2");
            }

            //获取客户账户信息
            var accountSynService = this.Container.GetService<ISynAccountBalanceService>();
            dicSettle["fallaccounts"] = accountSynService.GetAllAccountByCustomerId(this.Context, dataEntity["fcustomerid"] as string).ToList();

            this.Result.SrvData = dicSettle;
            this.Result.IsSuccess = true;
        }

        /// <summary>
        /// 设置代收单位信息
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="dicSettle"></param>
        /// <param name="uiConverter"></param>
        private void setContactUnitInfo(DynamicObject dataEntity, Dictionary<string, object> dicSettle, IUiDataConverter uiConverter)
        {
            if (dataEntity == null)
            {
                return;
            }
            var deptId = Convert.ToString(dataEntity["fdeptid"]);
            dicSettle["fdeptid"] = deptId;
            if (string.IsNullOrWhiteSpace(deptId))
            {
                return;
            }
            var deptForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_dept");
            var deptDm = this.Container.GetService<IDataManager>();
            deptDm.InitDbContext(this.Context, deptForm.GetDynamicObjectType(this.Context));

            var deptEntity = deptDm.Select(deptId) as DynamicObject;
            if (deptEntity == null)
            {
                return;
            }
            var isUnifiedCashier = Convert.ToBoolean(deptEntity["fisunifiedcashier"]);
            if (false == isUnifiedCashier)
            {
                return;
            }
            var enumForm = this.MetaModelService.LoadFormModel(this.Context, "bd_enum");
            var enumDm = this.Container.GetService<IDataManager>();
            enumDm.InitDbContext(this.Context, enumForm.GetDynamicObjectType(this.Context));
            var enumEntity = enumDm.Select("contactunittype_02") as DynamicObject;
            if (enumEntity == null)
            {
                return;
            }
            dicSettle["fcontactunittype"] = uiConverter.CreateUIDataObject(this.Context, enumForm, enumEntity)["uidata"];
            dicSettle["fcontactunitid"] = deptEntity["fmarketplace"];
            dicSettle["fisunifiedcashier"] = isUnifiedCashier;
        }

        /// <summary>
        /// 获取协同方的银行账号信息
        /// </summary>
        /// <param name="customer"></param>
        /// <returns></returns>
        private List<Dictionary<string, string>> GetSynBankNum(DynamicObject customer, DynamicObject dataEntity)
        {
            var responseResult = this.Gateway.Invoke(
                this.Context,
                new TargetSEP(customer["fcoocompanyid"] as string, customer["fcooproductid"] as string),
                new CommonBillDTO()
                {
                    FormId = "ydj_banknum",
                    OperationNo = "queryall",
                    BillData = "",
                    ExecInAsync = false,
                    SimpleData = new Dictionary<string, string>
                    {
                        { "sourceFormId",this.HtmlForm.Id},
                        { "tranId",Convert.ToString(dataEntity[this.HtmlForm.TranFldKey]) },
                        { "deptChaindataId",GetdeptChaindataId(Convert.ToString(dataEntity["fdeptid"])) }
                    }
                }) as CommonBillDTOResponse;

            var srvData = responseResult?.OperationResult?.SrvData as string;
            if (!srvData.IsNullOrEmptyOrWhiteSpace())
            {
                return srvData.FromJson<List<Dictionary<string, string>>>();
            }

            return null;
        }

        /// <summary>
        /// 获取销售部门云链Id
        /// </summary>
        /// <param name="depid"></param>
        /// <returns></returns>
        public string GetdeptChaindataId(string depid)
        {
            var sql = string.Format(@"select ffromchaindataid from t_bd_department where fid ='{0}' and fmainorgid ='{1}';", depid, this.Context.Company);
            var dbService = this.Context.Container.GetService<IDBService>();
            var sqlData = dbService.ExecuteDynamicObject(this.Context, sql);
            return sqlData.Select(p => Convert.ToString(p["ffromchaindataid"])).FirstOrDefault();
        }

        /// <summary>
        /// 检查是否已经下推销售合同
        /// </summary>
        /// <param name="sourceNumber"></param>
        private void CheckIsPushOrder(string sourceNumber)
        {
            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_order");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            string where = $@"fmainorgid=@fmainorgid and fsourcenumber=@fsourcenumber and fsourcetype='ydj_saleintention' ";
            var sqlParam = new SqlParam[]
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("fsourcenumber", System.Data.DbType.String, sourceNumber),
            };
            var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);
            var dataEntity = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
            if (dataEntity != null)
            {
                throw new BusinessException($"{this.HtmlForm.Caption}【{sourceNumber}】已经下推销售合同，不允许再次收款和退款！");
            }
        }
    }
}