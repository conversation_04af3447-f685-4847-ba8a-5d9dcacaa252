using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SaleIntention
{
    /// <summary>
    /// 销售订单：退单
    /// </summary>
    [InjectService]
    [FormId("ydj_saleintention")]
    [OperationNo("Chargeback")]
    public class Chargeback : UpdateBizStatus
    {
        public override string OperationName { get { return "退单"; } }

        /// <summary>
        /// 是否协同明细
        /// </summary>
        protected override bool IsSyncEntry
        {
            get
            {
                return true;
            }
        }

        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return Convert.ToString(newData["fchargebackstatus"]) == "0";
            }).WithMessage("编号为{0}的单据的退单不是正常状态，不允许退单!", (billObj, propObj) => propObj["fbillno"]));
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            var chargebackReason = this.GetQueryOrSimpleParam<string>("chargebackReason");
            if (string.IsNullOrWhiteSpace(chargebackReason))
            {
                throw new BusinessException("退单原因不能为空！");
            }

            base.BeginOperationTransaction(e);
            this.AddSetValueAction("fchargebackreason", chargebackReason);
            this.AddSetValueAction("fchargebackstatus", "1");
        }

        protected override string GetOperatedBizStatus(DynamicObject dataEntity)
        {
            return Convert.ToString(dataEntity["fbizstatus"]);
        }

        protected override void UpdateEntityBeforeSave(DynamicObject dataEntity)
        {
            base.UpdateEntityBeforeSave(dataEntity);

            if (dataEntity == null)
            {
                return;
            }

            var chargebackReason = this.GetQueryOrSimpleParam<string>("chargebackReason");
            dataEntity["fchargebackreason"] = chargebackReason;
            dataEntity["fchargebackstatus"] = "1";
        }

        protected override void UpdateParamaterBeforeSend(List<Dictionary<string, string>> orderParam)
        {
            base.UpdateParamaterBeforeSend(orderParam);

            if (orderParam == null || orderParam.Count <= 0)
            {
                return;
            }

            var chargebackReason = this.GetQueryOrSimpleParam<string>("chargebackReason");
            
            foreach(var item in orderParam)
            {
                item["chargebackReason"] = chargebackReason;
            }
        }
    }
}
