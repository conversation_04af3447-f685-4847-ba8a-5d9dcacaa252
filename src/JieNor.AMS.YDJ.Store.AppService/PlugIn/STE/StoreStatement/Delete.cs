using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.StoreStatement
{
    /// <summary>
    /// 店面结算对账单：删除
    /// </summary>
    [InjectService]
    [FormId("ydj_storestatement")]
    [OperationNo("Delete")]
    public class Delete: AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            var incomeIds = e.DataEntitys
                             .SelectMany(x => x["fincomeentry"] as DynamicObjectCollection)
                             .Select(x => Convert.ToString(x["fincomeid"]))
                             .Where(x => false == string.IsNullOrWhiteSpace(x))
                             .Distinct()
                             .ToList();

            if (incomeIds == null || incomeIds.Count <= 0)
            {
                return;
            }

            var incomeForm = this.MetaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            var incomeDm = this.Container.GetService<IDataManager>();
            incomeDm.InitDbContext(this.Context, incomeForm.GetDynamicObjectType(this.Context));
            var incomeEntities = incomeDm.Select(incomeIds).OfType<DynamicObject>().ToArray();
            if (incomeEntities == null || incomeEntities.Length <= 0)
            {
                return;
            }

            var invalidIncomeIds = new List<string>();
            foreach(var incomeEntity in incomeEntities)
            {
                var dealerStatus = Convert.ToString(incomeEntity["fdealerstatus"]);
                if (dealerStatus == "3")
                {
                    invalidIncomeIds.Add(Convert.ToString(incomeEntity["id"]));
                    continue;
                }
                if (dealerStatus == "2")
                {
                    incomeEntity["fdealerstatus"] = "1";
                }
            }

            var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
            prepareSaveDataService.PrepareDataEntity(this.Context, incomeForm, incomeEntities, OperateOption.Create());
            incomeDm.Save(incomeEntities);

            if (invalidIncomeIds == null || invalidIncomeIds.Count <= 0)
            {
                return;
            }

            var validEntites = new List<DynamicObject>();
            foreach(var dataEntity in e.DataEntitys)
            {
                var incomeEntries = dataEntity["fincomeentry"];
                var currentIncomeIds = incomeEntities.Select(x => Convert.ToString(x["fincomeid"]))
                                                     .Where(x => false == string.IsNullOrWhiteSpace(x))
                                                     .Distinct()
                                                     .ToList();
                if (currentIncomeIds == null || currentIncomeIds.Count <= 0)
                {
                    validEntites.Add(dataEntity);
                    continue;
                }
                if (currentIncomeIds.Any(x => invalidIncomeIds.Contains(x)))
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"编号[{dataEntity["fbillnno"]}]单据已有收支记录是经销商已对账状态不允许删除！");
                    continue;
                }
                validEntites.Add(dataEntity);
            }
            e.DataEntitys = validEntites.ToArray();
        }
    }
}
