using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Channel
{
    /// <summary>
    /// 取得渠道的客户信息
    /// </summary>
    [InjectService]
    [FormId("ste_channel")]
    [OperationNo("initbill")]
    public class Customer : AbstractOperationServicePlugIn
    {

        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);

            var billData = e.EventData as JObject;

            string fchannelid = billData["id"].ToString();
            List<SqlParam> sqlParams = new List<SqlParam>();
            sqlParams.Add(new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company));
            sqlParams.Add(new SqlParam("@fchannelid", System.Data.DbType.String, fchannelid));

            int recomCount = 0, tradedCount = 0, caseCount = 0;
            decimal tradedMoney = 0;
            //推荐数，成交数，成交金额，案例数
            string sql = @"select  (select isnull(count(fid),0) from t_ydj_customerrecord where fmainorgid=@fmainorgid and fchannelid=@fchannelid) as recomCount,
(select isnull(count(fid),0) from t_ydj_customer where fmainorgid=@fmainorgid and fchannelid=@fchannelid) tradedCount,
(select isnull(SUM(freceivable),0) from t_ydj_order where fmainorgid=@fmainorgid and fchannel=@fchannelid) tradedMoney,
(select isnull(count(fid),0) from t_ydj_case where fmainorgid=@fmainorgid and fchannel=@fchannelid and fsenstatus='send_status02') caseCount

";
            using (var dataReader = this.DBService.ExecuteReader(this.Context, sql.ToString(), sqlParams))
            {
                if (dataReader.Read())
                {
                    recomCount = dataReader.GetValue<int>("recomCount");
                    tradedCount = dataReader.GetValue<int>("tradedCount");
                    tradedMoney = dataReader.GetValue<decimal>("tradedMoney");
                    caseCount= dataReader.GetValue<int>("caseCount");
                }
            }

           var resultData=(new
            {
                recomCount,
                tradedCount,
                tradedMoney,
                caseCount
            });

            switch (e.EventName)
            {
                case "afterCreateUIData":
                    e.Result = resultData;
                    break;
            }
        }
        //public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        //{
        //    //获取请求JSON数据包
        //    string requestJsonStr = this.GetQueryOrSimpleParam<string>("params");
        //    JObject JParams = null;

        //    if (string.IsNullOrWhiteSpace(requestJsonStr) == false)
        //    {
        //        JParams = JObject.Parse(requestJsonStr);
        //    }

        //    if (JParams == null)
        //    {
        //        throw new BusinessException("请求参数有误或为空!");
        //    }

        //    List<SqlParam> sqlParams = new List<SqlParam>();
        //    sqlParams.Add(new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company));
        //    sqlParams.Add(new SqlParam("@fchannelid", System.Data.DbType.String, JParams["fchannelid"]));

        //    int recomCount = 0, tradedCount = 0;
        //    decimal tradedMoney = 0;
        //    string sql = @"--渠道ID,推荐数，成交数
        //                 select  (select isnull(count(fid),0) from t_ydj_leads where fmainorgid=@fmainorgid and fchannelid=@fchannelid) as recomCount,(select isnull(count(fid),0) from t_ydj_customer where fmainorgid=@fmainorgid and fchannelid=@fchannelid) tradedCount,(select isnull(SUM(freceivable),0) from t_ydj_order where fmainorgid=@fmainorgid and fchannel=@fchannelid) tradedMoney";
        //    using (var dataReader = this.DBService.ExecuteReader(this.Context, sql.ToString(), sqlParams))
        //    {
        //        if (dataReader.Read())
        //        {
        //            recomCount = dataReader.GetValue<int>("recomCount");
        //            tradedCount = dataReader.GetValue<int>("tradedCount");
        //            tradedMoney = dataReader.GetValue<decimal>("tradedMoney");
        //        }
        //    }

        //    this.Result.IsSuccess = true;
        //    this.Result.SrvData = new {
        //        recomCount,
        //        tradedCount,
        //        tradedMoney
        //    };
        //}
    }
}
