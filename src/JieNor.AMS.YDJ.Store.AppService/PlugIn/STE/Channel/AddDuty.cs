using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Channel
{

    /// <summary>
    /// 合作渠道：更改负责人
    /// </summary>
    [InjectService]
    [FormId("ste_channel")]
    [OperationNo("AddDuty")]
    public class AddDuty: AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 准备操作选项
        /// </summary>
        /// <param name="e"></param>
        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            return;
            //  e.OpCtlParam.IgnoreOpMessage = true;
        }

        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                throw new BusinessException($"请先保存后再执行新增负责人操作！");
            }
            var dataEntity = e.DataEntitys[0];

            this.Result.SrvData = new { costId = dataEntity["id"],issuccess=true };
            this.Result.IsSuccess = false;
        }
    }
}
