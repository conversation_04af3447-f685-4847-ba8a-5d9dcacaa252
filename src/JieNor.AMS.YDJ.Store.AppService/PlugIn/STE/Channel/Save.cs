using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Text;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Channel
{
    /// <summary>
    /// 合作渠道：保存
    /// </summary>
    [InjectService]
    [FormId("ste_channel")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            { 
                //如果是直营的话 才需要校验
                if (Convert.ToString(newData["fchanneltype"]).EqualsIgnoreCase("1") && this.Context.IsDirectSale && newData["fbanknumber"].IsNullOrEmptyOrWhiteSpace())
                    return false;
                return true;
            }).WithMessage("当前合作渠道为企业，银行卡号信息是必填项，请录入信息！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            { 
                if (Convert.ToString(newData["fchanneltype"]).EqualsIgnoreCase("1") && this.Context.IsDirectSale && newData["fbankcode"].IsNullOrEmptyOrWhiteSpace())
                    return false;
                return true;
            }).WithMessage("当前合作渠道为企业，银行代码信息是必填项，请录入信息！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //如果是直营的话 才需要校验
                if (Convert.ToString(newData["fchanneltype"]).EqualsIgnoreCase("0") && this.Context.IsDirectSale && newData["fidcard"].IsNullOrEmptyOrWhiteSpace())
                    return false;
                return true;
            }).WithMessage("身份证号是必填项，请录入信息！！"));

            var systemProfile = this.Context.Container.GetService<ISystemProfile>();
            //合作渠道地址必录
            var fchanneladdress = systemProfile.GetSystemParameter<bool>(this.Context, "bas_storesysparam", "fchanneladdress");

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (fchanneladdress && newData["fprovince"].IsNullOrEmptyOrWhiteSpace())
                    return false;
                return true;
            }).WithMessage("省市区不能为空！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (fchanneladdress && newData["faddress"].IsNullOrEmptyOrWhiteSpace())
                    return false;
                return true;
            }).WithMessage("地址不能为空！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return !newData["fname"].IsNullOrEmptyOrWhiteSpace();
            }).WithMessage("名称不能为空！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return !newData["ftype"].IsNullOrEmptyOrWhiteSpace();
            }).WithMessage("类型不能为空！"));
              
            var errorMsg = new StringBuilder();
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var result = true;
                var entries = newData["fentry"] as DynamicObjectCollection;
                if (this.Context.IsDirectSale) 
                {
                    if (entries == null || entries.Count <= 0)
                    {
                        errorMsg.AppendFormat("返佣比例明细不能为空！");
                        return false;
                    }
                }


                var preUpperLimit = 0;
                var number = Convert.ToString(newData[this.HtmlForm.NumberFldKey]);
                var formCaption = this.HtmlForm.Caption;
                var entryCaption = this.HtmlForm.GetEntryEntity("fentry").Caption;
                var ratioCaption = this.HtmlForm.GetField("fratio").Caption;
                var lowerLimitCaption = this.HtmlForm.GetField("flowerlimit").Caption;
                var upperLimitCaption = this.HtmlForm.GetField("fupperlimit").Caption;
                for (var i = 0; i < entries.Count; i++)
                {
                    var entry = entries[i];
                    var lowerLimit = Convert.ToInt32(entry["flowerlimit"]);
                    var upperLimit = Convert.ToInt32(entry["fupperlimit"]);
                    var ratio = Convert.ToDecimal(entry["fratio"]);

                    if (ratio <= 0)
                    {
                        errorMsg.AppendFormat("{0}[{1}]第{2}行{3}的{4}不能小于等于0!", formCaption, number, i + 1, entryCaption, ratioCaption);
                        errorMsg.AppendLine();
                        result = false;
                    }

                    if (lowerLimit < 0)
                    {
                        errorMsg.AppendFormat("{0}[{1}]第{2}行{3}的{4}不能小于0!", formCaption, number, i + 1, entryCaption, lowerLimitCaption);
                        errorMsg.AppendLine();
                        result = false;
                    }

                    if (upperLimit <= lowerLimit && upperLimit > 0)
                    {
                        errorMsg.AppendFormat("{0}[{1}]第{2}行{3}的{4}不能小于等于{5}!", formCaption, number, i + 1, entryCaption, upperLimitCaption, lowerLimitCaption);
                        errorMsg.AppendLine();
                        result = false;
                    }

                    if (i > 0 && lowerLimit < preUpperLimit)
                    {
                        errorMsg.AppendFormat("{0}[{1}]第{2}行{3}的{4}不能小于上一行的{5}!", formCaption, number, i + 1, entryCaption, lowerLimitCaption, upperLimitCaption);
                        errorMsg.AppendLine();
                        result = false;
                    }

                    if (i > 0 && preUpperLimit == 0)
                    {
                        errorMsg.AppendFormat("{0}[{1}]第{2}行{3}的{4}是0，表示无穷大，第{5}行起的数据已无意义，请删除!", formCaption, number, i, entryCaption, upperLimitCaption, i + 1);
                        errorMsg.AppendLine();
                        result = false;
                    }

                    preUpperLimit = upperLimit;
                }
                return result;
            }).WithMessage("{0}", (billObj, propObj) => errorMsg.ToString()));
        }

        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                throw new BusinessException($"地址不能为空，请先维护地址再进行保存操作！");
            }
            var dataEntity = e.DataEntitys[0]; 
            var dutyentry = dataEntity["fdutyentry"] as DynamicObjectCollection;
            if (dutyentry != null && dutyentry.Count > 0)
            {
                var removeEntrys = new List<DynamicObject>();
                foreach (var item in dutyentry)
                {
                    if (Convert.ToString(item["fdutyid"]).IsNullOrEmptyOrWhiteSpace())
                    {
                        removeEntrys.Add(item);
                    }
                    var joindate = Convert.ToDateTime(item["fjoindate"]);
                    if (joindate < Convert.ToDateTime("1900-01-01"))
                    {
                        var createtime = dataEntity["fcreatedate"];
                        item["fjoindate"] = createtime;
                    }
                }
                //移除负责人为空的明细行
                foreach (var rmEnt in removeEntrys)
                {
                    dutyentry.Remove(rmEnt);
                }
                //删除空负责人明细行之后判断是否还有负责人，没有则新增一行当前用户跟部门的数据
                if (dutyentry.Count <= 0)
                {
                    var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
                    var defaultDutyEntry = new DynamicObject(HtmlForm.GetEntryEntity("fdutyentry").DynamicObjectType);
                    defaultDutyEntry["fdutyid"] = baseFormProvider.GetMyStaff(this.Context)?.Id;
                    defaultDutyEntry["fdeptid"] = baseFormProvider.GetMyDepartment(this.Context)?.Id;
                    defaultDutyEntry["fjoindate"] = DateTime.Now;
                    dutyentry.Add(defaultDutyEntry);
                }
            }
            else
            {
                // 如果是系统创建，则忽略
                if (this.Context.UserId.EqualsIgnoreCase("sysadmin") == false)
                {
                    var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
                    var defaultDutyEntry = new DynamicObject(HtmlForm.GetEntryEntity("fdutyentry").DynamicObjectType);
                    defaultDutyEntry["fdutyid"] = baseFormProvider.GetMyStaff(this.Context)?.Id;
                    defaultDutyEntry["fdeptid"] = baseFormProvider.GetMyDepartment(this.Context)?.Id;
                    defaultDutyEntry["fjoindate"] = DateTime.Now;
                    dutyentry.Add(defaultDutyEntry);
                }
            }
        }
    }
}