using JieNor.AMS.YDJ.Core.DataEntity.Customer;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.CustomerRecord
{
    public class BaseCustomerRecordPlugin : AbstractOperationServicePlugIn
    {
        protected DynamicObject[] PushCustomers(DynamicObject[] dataEntities, IConvertService convertService)
        {
            //重新调一次保存，完成客户资料的自动关联
            this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, dataEntities, "save", new Dictionary<string, object>());

            //检查当前销售机会有没有所属客户
            var hasNotCustomers = dataEntities.Where(x => string.IsNullOrWhiteSpace(Convert.ToString(x["fcustomerid"]))).ToList();
            //如果没有则下推客户
            if (hasNotCustomers != null && hasNotCustomers.Count > 0)
            {
                var targetFormId = "ydj_customer";
                var result = convertService.Push(this.Context, new BillConvertContext()
                {
                    RuleId = "ydj_customerrecord2ydj_customer",
                    SourceFormId = this.HtmlForm.Id,
                    TargetFormId = targetFormId,
                    SelectedRows = hasNotCustomers.Select(x => new ConvertSelectedRow { PkValue = Convert.ToString(x["id"]) }),
                    Option = this.Option
                });
                var convertResult = result?.SrvData as ConvertResult;
                var targetDatas = convertResult?.TargetDataObjects?.ToArray();

                MergeResult(result);

                if (targetDatas != null && targetDatas.Length > 0)
                {
                    result = this.Gateway.InvokeBillOperation(this.Context, targetFormId, targetDatas, "save", new Dictionary<string, object>());
                    MergeResult(result);
                }
            }
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dataEntities = dm.Select(dataEntities.Select(x => Convert.ToString(x["id"]))).OfType<DynamicObject>().ToArray() ?? new DynamicObject[0];
            hasNotCustomers = dataEntities.Where(x => string.IsNullOrWhiteSpace(Convert.ToString(x["fcustomerid"]))).ToList();
            if (hasNotCustomers == null)
            {
                hasNotCustomers = new List<DynamicObject>();
            }
            foreach (var item in hasNotCustomers)
            {
                this.Result.ComplexMessage.ErrorMessages.Add($"编号{Convert.ToString(item[this.HtmlForm.NumberFldKey])}的机会转客户失败");
            }

            return dataEntities.Where(x => false == hasNotCustomers.Contains(x)).ToArray();
        }

        protected void MergeResult(IOperationResult result)
        {
            if (result == null)
            {
                return;
            }
            if (result.IsSuccess == false && result.IsShowMessage && false == string.IsNullOrWhiteSpace(result.SimpleMessage))
            {
                this.Result.ComplexMessage.ErrorMessages.Add(result.SimpleMessage);
            }
            if (result.ComplexMessage != null && result.ComplexMessage.ErrorMessages.Count > 0)
            {
                this.Result.ComplexMessage.ErrorMessages.AddRange(result.ComplexMessage.ErrorMessages);
            }
        }
    }
}
