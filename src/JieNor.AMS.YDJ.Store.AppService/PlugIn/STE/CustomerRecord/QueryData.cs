using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.CustomerRecord
{
    /// <summary>
    /// 销售机会：查询列表
    /// </summary>
    [InjectService]
    [FormId("ydj_customerrecord")]
    [OperationNo("QueryData")]
    public class QueryData: AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);
            switch (e.EventName)
            {
                case "afterListData":
                    afterListData(e);
                    break;
                case OnCustomServiceEventArgs.PrepareQueryBuilderParameter:
                    var queryPara = e.EventData as SqlBuilderParameter;
                    if (queryPara != null)
                    {
                        //页面是否仅查看公海数据
                        var publishStr = "fispublish=1";
                        var isPublish = queryPara.FilterString.Contains(publishStr);
                        if (isPublish)
                        {
                            queryPara.FilterString = string.Empty;
                            queryPara.EnableDataRowACL = false;
                            queryPara.FilterString = queryPara.FilterString.JoinFilterString(@"
                            exists(select fid from(
                            select u1.fid from t_ydj_customerrecord u1 where u1.fdutyid='' and fdeptid='' and u1.fclosestatus=0 and u1.fmainorgid=@currentCompanyId
                            union all
                            select u1.fid from t_ydj_customerrecord u1 where u1.fdutyid='' and fdeptid=@currentDeptId and u1.fclosestatus=0 and u1.fmainorgid=@currentCompanyId
                            ) u2 where u2.fid = fid)");
                            queryPara.AddParameter(new SqlParam("currentCompanyId", System.Data.DbType.String, this.Context.Company));
                            queryPara.AddParameter(new SqlParam("currentDeptId", System.Data.DbType.String, this.Context.GetCurrentDeptId()));
                        }
                        //else
                        //{
                        //    queryPara.FilterString = string.Empty;
                        //    queryPara.FilterString = queryPara.FilterString.JoinFilterString("fdutyid!=''");
                        //}
                    }
                    break;
            }
        }

        /// <summary>
        /// 处理列表查询的数据
        /// </summary>
        /// <param name="e"></param>
        private void afterListData(OnCustomServiceEventArgs e)
        {
            var listData = e.EventData as List<Dictionary<string, object>>;
            if (listData == null || listData.Count <= 0)
            {
                return;
            }

            //保护列表客户信息
            var protecteDataService = this.Container.GetService<IProtecteDataService>();
            protecteDataService.Init(this.Context, "bas_storesysparam", "fenableprotectecusinfo");
            protecteDataService.EmptyFields(new[] { "fwechat" }, listData);
            protecteDataService.MaskFields(new[] { "fphone" }, listData, 3, 4);
        }
    }
}
