using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.IoC;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.SaleIntention;
using JieNor.Framework;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.CustomerRecord
{
    /// <summary>
    /// 销售机会：上下联查
    /// </summary>
    [InjectService]
    [FormId("ydj_customerrecord")]
    [OperationNo("LinkFormSearch")]
    public class LinkFormSearch : LinkFormSearchBase
    {
        protected override void DealLinkForm(UserContext userContext, DynamicObject[] dataEntities, List<Dictionary<string, object>> linkFormDatas)
        {
            //意向单
            dealNextForm("fintentionno", "ydj_saleintention", dataEntities, linkFormDatas);

            //合同
            dealNextForm("forderno", "ydj_order", dataEntities, linkFormDatas);

            //跟进记录
            var followerRecordService = userContext.Container.GetService<IFollowerRecordService>();
            var metaModelService = userContext.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(userContext, "ydj_customerrecord");
            followerRecordService.DealLinkForm(userContext, this.HtmlForm, dataEntities, linkFormDatas, "fcustomerid");
        }

        private void dealNextForm(string numberKey, string formId, DynamicObject[] dataEntities, List<Dictionary<string, object>> linkFormDatas)
        {
            var numbers = dataEntities.Select(x => Convert.ToString(x[numberKey])).Where(x => false == string.IsNullOrWhiteSpace(x)).Distinct().ToList();

            var item = linkFormDatas.FirstOrDefault(x => Convert.ToString(x["formId"]) == formId && Convert.ToString(x["flag"]) == "nextForm");
            if (item != null)
            {
                string filterString = getFilterString(numbers);
                if (string.IsNullOrWhiteSpace(filterString))
                {
                    linkFormDatas.Remove(item);
                }
                else
                {
                    item["filterString"] = filterString;
                }
            }
        }

        private string getFilterString(List<string> numbers)
        {
            if (numbers == null || numbers.Count <= 0)
            {
                return string.Empty;
            }

            if (numbers.Count == 1)
            {
                return $" fbillno = '{numbers[0]}' "; 
            }

            return string.Format(" fbillno in ({0}) ", string.Join(",", numbers.Select(x => $"'{x}'")));
        }
    }
}
