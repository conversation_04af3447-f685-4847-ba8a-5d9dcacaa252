using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.CustomerRecord
{
    /// <summary>
    /// 销售机会：根据部门获取省市区
    /// </summary>
    [InjectService]
    [FormId("ydj_customerrecord")]
    [OperationNo("getareabydept")]
    public class GetAreaByDept: AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            var fdeptid= this.GetQueryOrSimpleParam<string>("fdeptid");
            ICustomerRecordService customerRecordService = this.Container.GetService<ICustomerRecordService>();
            var areaobj=customerRecordService.GetAreaByDept(this.Context, fdeptid);
            this.Result.IsSuccess = true;
            this.Result.SrvData = areaobj;
        }
    }


}
