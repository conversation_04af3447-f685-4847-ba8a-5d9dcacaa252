using JieNor.AMS.YDJ.Core.DataEntity.Customer;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Customer;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Customer
{
    ///<summary>
    /// 客户：检查是否重复与已成为负责人
    /// </summary>
    [InjectService]
    [FormId("ydj_customer")]
    [OperationNo("checkphone")]
    public class CheckPhone : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            //var id = this.GetQueryOrSimpleParam<string>("id", "");
            //var type = this.GetQueryOrSimpleParam<string>("type", "");
            //var phone = this.GetQueryOrSimpleParam<string>("phone", "");
            //var wechat = this.GetQueryOrSimpleParam<string>("wechat", "");
            //e.Rules.Add(new SaveValidation(false)); //因性能优化导入批量处理类 
        }
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var id = this.GetQueryOrSimpleParam<string>("id", "");
            var type = this.GetQueryOrSimpleParam<string>("type", "");
            var phone = this.GetQueryOrSimpleParam<string>("phone", "");
            var wechat = this.GetQueryOrSimpleParam<string>("wechat", "");
            var name = this.GetQueryOrSimpleParam<string>("name", "");
            if (!string.IsNullOrWhiteSpace(phone))
            {
                phone=ToDBC(phone);
                var dic = new Dictionary<string, string>();
                var paramSer = this.Container.GetService<ISystemProfile>();
                var fcustomerunique = paramSer.GetSystemParameter<string>(this.Context, "bas_storesysparam", "fcustomerunique");//获取客户报备唯一性控制规则
                if (fcustomerunique != null && fcustomerunique.IndexOf("store") > 0)
                {
                    var srcstoreid = this.GetQueryOrSimpleParam<string>("srcstoreid", "");
                    StringBuilder sbFilter = new StringBuilder();
                    sbFilter.Append(@"fphone='{0}' AND fsrcstoreid='{1}'".Fmt(phone, srcstoreid));
                    if (!id.IsNullOrEmptyOrWhiteSpace())
                    {
                        //修改操作需要排除自己
                        sbFilter.Append(@" AND fid<>'{0}' ".Fmt(id));
                    }
                    var customerObjs = this.Context.LoadBizDataByFilter("ydj_customer", sbFilter.ToString());
                    if (customerObjs != null && customerObjs.Any())
                    {
                        dic.Add("code", "0");
                        dic.Add("message", "当前客户手机号+来源门店已存在，您可以直接选择该客户直接下单。");
                        this.Result.IsSuccess = false;
                        this.Result.SrvData = dic;
                        return;
                    }
                }
                else
                {
                    var customerId = string.Empty;
                    var duplicate = IsCustomerDuplicate(id, type, phone, wechat, name, out customerId);
                    if (duplicate)
                    {
                        if (IsDuty(customerId))
                        {
                            dic.Add("code", "0");
                            dic.Add("message", "客户已存在，且您已经是客户的负责人了！");
                        }
                        else
                        {
                            var baseFormProvider = this.Context.Container.GetService<IBaseFormProvider>();
                            string dutyId = baseFormProvider.GetMyStaff(this.Context)?.Id;
                            string deptid = baseFormProvider.GetMyDepartment(this.Context)?.Id;
                            dic.Add("code", "10000");
                            dic.Add("message", "当前手机号已存在客户，是否成为该客户负责人？ ");
                            dic.Add("deptid", deptid);
                            dic.Add("customerid", customerId);
                            dic.Add("dutyid", dutyId);
                        }
                        this.Result.IsSuccess = false;
                        this.Result.SrvData = dic;
                        return;
                    }
                }
            }
            this.Result.IsSuccess = true;
        }

        /// <summary>
        /// 是或否客户重复且是负责人
        /// </summary>
        /// <param name="id"></param>
        /// <param name="type"></param>
        /// <param name="phone"></param>
        /// <param name="wechat"></param>
        /// <param name="name"></param>
        /// <returns></returns>
        private bool IsCustomerDuplicate(string id, string type, string phone, string wechat, string name, out string customerId)
        {
            customerId = string.Empty;
            var item = new CustomerUniqueItem(id)
            {
                Type = type,
                Name = name,
                Phone = phone,
                Wechat = wechat
            };
            var customerService = this.Container.GetService<ICustomerService>();
            return customerService.CheckCustomerUnique(this.Context, item, out customerId);
        }

        /// <summary>
        /// 负责人
        /// </summary>
        /// <param name="customerId"></param>
        /// <returns></returns>
        private bool IsDuty(string customerId)
        {
            var customerObj = this.Context.LoadBizDataById("ydj_customer", customerId);
            if (customerObj != null)
            {
                return IsThisCustomer(customerObj);
            }
            return false;
        }

        /// <summary>
        /// 用户是否存在当前负责人中
        /// </summary>
        /// <param name="customerObj"></param>
        /// <returns></returns>
        protected bool IsThisCustomer(DynamicObject customerObj)
        {
            var dutyEntry = (DynamicObjectCollection)customerObj["fdutyentry"];
            var baseFormProvider = this.Context.Container.GetService<IBaseFormProvider>();
            string dutyId = baseFormProvider.GetMyStaff(this.Context)?.Id;
            // 判断负责人是否存在于当前客户负责人中，如有，则返回
            if (dutyEntry.Any(s => Convert.ToString(s["fdutyid"]).EqualsIgnoreCase(dutyId)))
            {
                return true;
            }
            return false;
        }

        private bool DutyOnStaff(List<string> ids, string phone)
        {
            string sql = $"select COUNT(1) from t_bd_staff WITH(NOLOCK) where fid in({string.Join(",", ids)}) and fphone='{phone}' ";
            using (var reader = this.Container.GetService<IDBService>().ExecuteReader(this.Context, sql))
            {
                if (reader.Read())
                {
                    return Convert.ToInt32(reader[0]) > 0;
                }
            }
            return false;
        }

        /// <summary>
        /// 全角字符转为半角字符
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private String ToDBC(String input)
        {
            char[] c = input.ToCharArray();
            for (int i = 0; i < c.Length; i++)
            {
                if (c[i] == 12288)//全角空格
                {
                    c[i] = (char)32;//半角空格
                    continue;
                }
                if (c[i] > 65280 && c[i] < 65375)//全角字符范围
                    c[i] = (char)(c[i] - 65248);//全角与半角char相差65248
            }
            return new String(c);
        }
    }
}
