using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Clients.Ewc;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.CustomerLevel
{
    /// <summary>
    /// 会员等级：同步保存
    /// 这是由动态表单ydj_customerleveleditor编辑数据后同步保存到ydj_customerlevel
    /// </summary>
    [InjectService]
    [FormId("ydj_customerlevel")]
    [OperationNo("syncsave")]
    public class SyncSave : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var billData = this.GetQueryOrSimpleParam<string>("billData");

            List<Dictionary<string, string>> billObjs = null;
            try
            {
                billObjs = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(billData);
            }
            catch (Exception ex)
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "保存失败：数据包格式不正确！";
                return;
            }

            if (billObjs == null || billObjs.Count == 0)
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "请添加会员等级配置！";
                return;
            }

            // 获取所有会员等级
            var customerLevels = this.Context.LoadBizDataByFilter(this.HtmlForm.Id, " fforbidstatus<>'1' ");

            List<DynamicObject> saveCustomerLevels = new List<DynamicObject>();

            // 处理已有数据
            foreach (var customerLevel in customerLevels)
            {
                var item = billObjs.FirstOrDefault(s => s["fcustomerlevelid"] == Convert.ToString(customerLevel["id"]));
                // 如果在，则更新
                if (item != null)
                {
                    customerLevel["fname"] = item["fname"];
                    customerLevel["fcondition"] = item["fcondition"];
                }
                // 如果不在，则设置为禁用
                else
                {
                    customerLevel["fforbidstatus"] = true;
                    customerLevel["fforbiddate"] = DateTime.Now;
                    customerLevel["fforbidid"] = this.Context.UserId;
                }

                saveCustomerLevels.Add(customerLevel);
            }

            // 处理新数据
            var dt = this.HtmlForm.GetDynamicObjectType(this.Context);
            foreach (var newItem in billObjs.Where(s => s["fcustomerlevelid"].IsNullOrEmptyOrWhiteSpace()))
            {
                var newCustomerLevel = new DynamicObject(dt);
                newCustomerLevel["fname"] = newItem["fname"];
                newCustomerLevel["fcondition"] = Convert.ToDecimal(newItem["fcondition"]);

                saveCustomerLevels.Add(newCustomerLevel);
            }

            var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_customerlevel", saveCustomerLevels, "save", new Dictionary<string, object>());
            result.ThrowIfHasError(true, "保存会员等级失败！");

            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "保存成功！";
        }

        /// <summary>
        /// 更新客户
        /// </summary>
        /// <param name="customerId">客户id</param>
        private void UpdateCustomer(string customerId, string phone)
        {
            if (customerId.IsNullOrEmptyOrWhiteSpace()) return;

            DynamicObject customerObj = this.Context.LoadBizDataById("ydj_customer", customerId);

            if (customerObj == null) return;

            bool needUpdate = false;

            if (Convert.ToString(customerObj["fphone"]).IsNullOrEmptyOrWhiteSpace())
            {
                // 反写手机号
                customerObj["fphone"] = phone;
                needUpdate = true;
            }

            string cusNature = Convert.ToString(customerObj["fcusnature"]);
            if (cusNature.EqualsIgnoreCase("cusnature_02") == false || customerObj["ffirstordertime"] == null)
            {
                if (cusNature.EqualsIgnoreCase("cusnature_02") == false)
                {
                    // 客户设置为成交客户
                    customerObj["fcusnature"] = "cusnature_02";
                }

                if (customerObj["ffirstordertime"] == null)
                {
                    // 首次成单时间为空时，更新首次成单时间
                    customerObj["ffirstordertime"] = BeiJingTime.Now;
                }
                needUpdate = true;
            }

            if (needUpdate)
            {

            }
        }

        /// <summary>
        /// 添加跟进记录
        /// </summary>
        /// <param name="customerRecordObj"></param> 
        private void AddFollowerRecord(DynamicObject customerRecordObj)
        {
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_followerrecord");
            var formDt = htmlForm.GetDynamicObjectType(this.Context);

            List<DynamicObject> followerRecordObjs = new List<DynamicObject>();

            var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
            string deptId = baseFormProvider.GetMyDepartment(this.Context)?.Id;
            string staffId = baseFormProvider.GetMyStaff(this.Context)?.Id;

            var followerRecordObj = new DynamicObject(formDt);

            followerRecordObj["fcustomerid"] = customerRecordObj["fcustomerid"];
            followerRecordObj["fcontacts"] = customerRecordObj["fcustomername"];
            followerRecordObj["fphone"] = customerRecordObj["fphone"];
            followerRecordObj["ffollowtime"] = DateTime.Now;
            followerRecordObj["ffollowerid"] = this.Context.UserId;
            followerRecordObj["fdeptid"] = deptId;
            followerRecordObj["fstaffid"] = staffId;
            followerRecordObj["ftype"] = "6";       // 默认是其他
            followerRecordObj["fdescription"] = $"商机已手动成单关闭。";
            followerRecordObj["fobjecttype"] = "objecttype20";  // 成单关闭
            followerRecordObj["fobjectid"] = customerRecordObj["id"];
            followerRecordObj["fobjectno"] = customerRecordObj["fbillno"];
            followerRecordObj["frelatedbilltype"] = this.HtmlForm.Id;
            followerRecordObj["frelatedbillno"] = customerRecordObj["fbillno"];
            followerRecordObj["fsourcetype"] = this.HtmlForm.Id;
            followerRecordObj["fsourcenumber"] = customerRecordObj["fbillno"];
            followerRecordObj["ftranid"] = customerRecordObj["ftranid"];

            followerRecordObjs.Add(followerRecordObj);

            var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_followerrecord", followerRecordObjs, "save", new Dictionary<string, object>());
            result.ThrowIfHasError(true, "添加跟进记录失败！");
        }
    }
}