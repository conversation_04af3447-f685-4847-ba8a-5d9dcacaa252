using System;
using System.Linq;
using System.Collections.Generic;
using System.Data;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.DataTransferObject;
using System.Text;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore.FormOp;
using Senparc.Weixin.Work.Entities;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.AfterFeedback;

namespace JieNor.AMS.YDJ.Stock.AppService.STE.AfterFeedback
{
    /// <summary>
    /// 售后反馈单：保存
    /// </summary>
    [InjectService]
    [FormId("ste_afterfeedback")]
    [OperationNo("save")]
    public class CustomerSave : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 是否来源于中台下发
        /// </summary>
        private bool isMuSiSync { get; set; }
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            isMuSiSync = this.GetQueryOrSimpleParam<string>("IsMuSiSync", string.Empty).EqualsIgnoreCase("true");
            /*
                定义表头校验规则
            */
            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    if (!oldData["ffeedstatus"].IsNullOrEmptyOrWhiteSpace() && (oldData["ffeedstatus"].Equals("aft_service_03") || oldData["ffeedstatus"].Equals("aft_service_06")))
            //    {
            //        return false;
            //    }
            //    return true;
            //}).WithMessage("该单据售后状态为待总部审核或已关闭，禁止保存操作！"));

            e.Rules.Add(this.RuleFor("fproductentry", data => data)
                    .IsTrue((newData, oldData) =>
                    {
                        return !(Convert.ToDecimal(newData["fqty"])<= 0);
                    })
                    .WithMessage("明细行{0}：商品数量必须大于0！", (dy, dyObj) => dy["fseq"]));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["ffeedstatus"]).NotEmpty().WithMessage("售后状态不能为空！"));
            //e.Rules.Add(this.RuleFor("fbillhead", data => data["fdate"]).NotEmpty().WithMessage("反馈时间不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["flinkstaffid"]).NotEmpty().WithMessage("联系人不能为空！"));
            if (!this.Context.IsDirectSale)
            {
                e.Rules.Add(this.RuleFor("fbillhead", data => data["flinkmobile"]).NotEmpty().WithMessage("联系电话不能为空！"));
                e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
                {
                    if (newData["fzblinkaddress"].IsNullOrEmptyOrWhiteSpace() && (newData["fprovince"].IsNullOrEmptyOrWhiteSpace() || newData["fcity"].IsNullOrEmptyOrWhiteSpace()
                    || newData["fregion"].IsNullOrEmptyOrWhiteSpace()))
                    {
                        return false;
                    }
                    return true;
                }).WithMessage("区域中的省份、城市、区县或详细地址不能为空！"));
            }
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (newData["fdeliver"].IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("送达方不能为空！"));
            //e.Rules.Add(this.RuleFor("fbillhead", data => data["fprovince"]).NotEmpty().WithMessage("省市区中的省份不能为空！"));
            //e.Rules.Add(this.RuleFor("fbillhead", data => data["fcity"]).NotEmpty().WithMessage("省市区中的市不能为空！"));
            //e.Rules.Add(this.RuleFor("fbillhead", data => data["fregion"]).NotEmpty().WithMessage("省市区中的区县不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fquestiondesc"]).NotEmpty().WithMessage("问题描述不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (newData["fquestionimage"].IsNullOrEmptyOrWhiteSpace() && newData["fquestiontype"].Equals("afterquestion_type_01"))
                {
                    return false;
                }
                return true;
            }).WithMessage("问题图片不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {

                var fimageLen = 0; var fztimageLen = 0;
                if (!newData["fquestionimage"].IsNullOrEmptyOrWhiteSpace())
                {
                    fimageLen = newData["fquestionimage"].ToString().Split(',').Length;
                }
                if (!newData["fztimage"].IsNullOrEmptyOrWhiteSpace())
                {
                    fztimageLen = newData["fztimage"].ToString().Split('|').Length;
                }
                if (fimageLen + fztimageLen > 9)
                {
                    return false;
                }
                return true;
            }).WithMessage("问题图片附件(总部图片+经销商图片总张数)最多9张！"));
            var errorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!isMuSiSync&&Convert.ToString(newData["fhqhandleconclusion"]) == "fqres_type_08")
                {
                    if (Convert.ToString(newData["fisreturn"]) == "1" && newData["freturndate"].IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "返厂申请中【是否返厂】为【是】，请填写返厂时间！";
                        return false;
                    }
                    else if (newData["fauthcity"].IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "返厂申请中城市不能为空！";
                        return false;
                    }
                    //else if (newData["fdeliver"].IsNullOrEmptyOrWhiteSpace())
                    //{
                    //    errorMessage = "返厂申请中送达方不能为空！";
                    //    return false;
                    //}
                    else if (newData["fengineerid"].IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "返厂申请中工程师不能为空！";
                        return false;
                    }
                    else if (newData["fengineerphone"].IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "返厂申请中工程师电话不能为空！";
                        return false;
                    }
                    else if (newData["ffeedtype"].IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "返厂申请中返厂售后类型不能为空！";
                        return false;
                    }
                    else if (newData["freturnreson"].IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "返厂申请中产品返厂原因不能为空！";
                        return false;
                    }
                    else if (newData["fengineeraddress"].IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "返厂申请中退货地址不能为空！";
                        return false;
                    }
                    //else if (newData["fspecifica"].IsNullOrEmptyOrWhiteSpace())
                    //{
                    //    errorMessage = "返厂申请中尺寸规格不能为空！";
                    //    return false;
                    //}
                    //else if (newData["fprocount"].IsNullOrEmptyOrWhiteSpace() || Convert.ToInt32(newData["fprocount"]) <= 0)
                    //{
                    //    errorMessage = "返厂申请中商品数量不能<= 0)！";
                    //    return false;
                    //}
                    else if (newData["fengineeraddress"].IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "返厂申请中退货地址不能为空！";
                        return false;
                    }
                    else if (newData["fsapnumber"].IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = "返厂申请中SAP订单号不能为空！";
                        return false;
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                // 加载引用数据
                var refMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
                var formDt = this.HtmlForm.GetDynamicObjectType(this.Context);
                refMgr.Load(this.Context, formDt, newData, false);
                var fdeliver = newData["fdeliver_ref"] as DynamicObject;
                var seriesentry = fdeliver["fentry"] as DynamicObjectCollection;
                var productfentry = newData["fproductentry"] as DynamicObjectCollection;
                foreach (var item in productfentry)
                {
                    var materialid = item["fmaterialid_ref"] as DynamicObject;
                    var seriesid = materialid["fseriesid"];
                    bool correct = false;
                    foreach (var itemm in seriesentry)
                    {
                        if (itemm["fserieid"].Equals(seriesid))
                        {
                            correct = true;
                            break;
                        }
                    }

                    if(!correct) return false;
                }

                return true;
            }).WithMessage("对不起，当前所选送达方与售后商品系列不匹配，请重新选择！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fentry = newData["fproductentry"] as DynamicObjectCollection;
                var proCount = 0;
                if (fentry != null && fentry.Any())
                {
                    foreach (var item in fentry)
                    {
                        var fproductid = Convert.ToString(item["fmaterialid"]);
                        if (!fproductid.IsNullOrEmptyOrWhiteSpace())
                        {
                            proCount++;
                        }

                        if (proCount >= 2)
                            return false;
                    }
                }
                return true;
            }).WithMessage("对不起，问题商品只能选择1个！"));

            //判断商品明细行是否有填商品字段
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Checkfproductid(newData))
                {
                    return false;
                }
                return true;
            }).WithMessage("对不起，问题商品不能为空！"));
            e.Rules.Add(new Validation_Save());
        }
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            #region 原逻辑弃用，售后调整后需支持一个服务单按不同商品下推多个售后反馈单

            ////首先过滤当前保存集合中的售后状态非已关闭项中源单类型和编号已重复项
            //var distinctNos = e.DataEntitys.Where(x => !x["ffeedstatus"].Equals("aft_service_06") && !x["fsourcenumber"].IsNullOrEmptyOrWhiteSpace())
            //    .GroupBy(x => new { fsourcetype = x["fsourcetype"], fsourcenumber = x["fsourcenumber"] })
            //    .Where(g => g.Count() > 1).Select(x => x.Key.fsourcenumber.ToString());
            //if (distinctNos.Any())
            //{
            //    throw new BusinessException("当前提交的以下售后状态未关闭的保存单据中，对应源单有重复项，请检查！【" + string.Join(",", distinctNos) + "】");
            //}
            //var sourceTypes = e.DataEntitys.Where(x => !x["fsourcenumber"].IsNullOrEmptyOrWhiteSpace() && !x["ffeedstatus"].Equals("aft_service_06")).Select(x => Convert.ToString(x["fsourcetype"])).Distinct();
            //var sourceNos = e.DataEntitys.Where(x => !x["fsourcenumber"].IsNullOrEmptyOrWhiteSpace() && !x["ffeedstatus"].Equals("aft_service_06")).Select(x => Convert.ToString(x["fsourcenumber"])).Distinct();
            //if (sourceNos.Any())
            //{
            //    StringBuilder sqlWhere = new StringBuilder();
            //    List<SqlParam> param = new List<SqlParam>();
            //    sqlWhere.Append(string.Join(",", sourceNos.Select((x, i) => $"@fbillno{i}")));
            //    param.AddRange(sourceNos.Select((x, i) => new SqlParam($"@fbillno{i}", DbType.String, x)));
            //    param.AddRange(sourceTypes.Select((x, i) => new SqlParam($"@fsourcetype{i}", DbType.String, x)));
            //    param.Add(new SqlParam("@fmainorgid", DbType.String, this.Context.Company));
            //    var oldbills = new List<DynamicObject>();
            //    if (sourceTypes.Count() == 1)
            //    {
            //        oldbills = this.Context.LoadBizDataByFilter("ste_afterfeedback", "fsourcenumber in(" + sqlWhere + ")  and fsourcetype=@fsourcetype0 and fmainorgid=@fmainorgid and ffeedstatus!='aft_service_06'", false, param);
            //    }
            //    else
            //    {
            //        oldbills = this.Context.LoadBizDataByFilter("ste_afterfeedback", "fsourcenumber in(" + sqlWhere + ")  and fsourcetype in(" + string.Join(",", sourceTypes.Select((x, i) => $"@fsourcetype{i}")) + ") and fmainorgid=@fmainorgid and ffeedstatus!='aft_service_06'", false, param);
            //    }

            //    if (oldbills.Any())
            //    {
            //        StringBuilder msg = new StringBuilder();
            //        foreach (var item in e.DataEntitys)
            //        {
            //            if (!item["ffeedstatus"].Equals("aft_service_06"))
            //            {
            //                var temp = oldbills.Where(x => x["fsourcenumber"].Equals(item["fsourcenumber"]) && x["fsourcetype"].Equals(item["fsourcetype"]) && !x["fbillno"].Equals(item["fbillno"])).FirstOrDefault();
            //                //已关闭状态的不受影响
            //                if (!temp.IsNullOrEmpty())
            //                {
            //                    msg.Append("售后反馈单【{0}】对应上游单据存在未关闭售后反馈单【{1}】，请等待该单据售后状态为待关闭或删除后再进行此操作！<br/>".Fmt(item["fbillno"], temp["fbillno"]));
            //                }
            //            }
            //        }
            //        if (msg.Length > 0)
            //        {
            //            throw new BusinessException(msg.ToString());
            //        }
            //    }
            //}

            #endregion
            //防止导入时没选择来源渠道
            foreach (var item in e.DataEntitys)
            {
                if (item["fsourcecancl"].IsNullOrEmptyOrWhiteSpace())
                {
                    item["fsourcecancl"] = "1";
                }
                if (isMuSiSync)
                {
                    item["fistransfer"] = "1";
                }
            }
            BuildEntryRowNum(e.DataEntitys, "fproductentry");
            // 填充字段默认值
            var defCalulator = this.Container.GetService<IDefaultValueCalculator>();
            defCalulator.Execute(this.Context, this.HtmlForm, e.DataEntitys);

            // 保存前预处理
            var preService = this.Container.GetService<IPrepareSaveDataService>();
            preService.PrepareDataEntity(this.Context, this.HtmlForm, e.DataEntitys, Framework.SuperOrm.OperateOption.Create());
            var dm = this.GetDataManager();
            dm.InitDbContext(Context, this.HtmlForm.GetDynamicObjectType(Context));
            dm.Save(e.DataEntitys);
        }
        /// <summary>
        /// 生成明细行标记
        /// </summary>
        /// <param name="dataEntitys"></param>
        /// <remarks>
        /// 生成规则：ZD（终端）+tranid，比如：ZD886907999616237575
        /// </remarks>
        private void BuildEntryRowNum(DynamicObject[] dataEntitys, string entryId)
        {
            if (dataEntitys == null || dataEntitys.Length <= 0) return;

            foreach (var dataEntity in dataEntitys)
            {
                var entrys = dataEntity[entryId] as DynamicObjectCollection;
                if (entrys == null) continue;
                foreach (var entry in entrys)
                {
                    if (entry["fnumber"].IsNullOrEmptyOrWhiteSpace())
                    {
                        entry["fnumber"] = $"ZD{entry["ftranid"]}";
                    }
                }
            }
        }


        /// <summary>
        /// 执行操作事务后事件，通知插件对象执行其它事务无关的业务逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            if (e.DataEntitys.IsNullOrEmptyOrWhiteSpace() || !e.DataEntitys.Any()) return;

            //售后反馈单有源单（服务单）时
            if (!e.DataEntitys[0]["fsourcenumber"].IsNullOrEmptyOrWhiteSpace()
                && !e.DataEntitys[0]["fsourcetype"].IsNullOrEmptyOrWhiteSpace() 
                && e.DataEntitys[0]["fsourcetype"].Equals("ydj_service"))
            {
                var billno = e.DataEntitys[0]["id"];
                var proEntry = e.DataEntitys[0]["fproductentry"] as DynamicObjectCollection;

                if (!billno.IsNullOrEmptyOrWhiteSpace() && !proEntry.IsNullOrEmptyOrWhiteSpace() && proEntry.Count>0)
                {
                   
                    var propValueForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_service");

                    var datas = this.Context.LoadBizDataByACLFilter("ydj_service", $" fbillno='{e.DataEntitys[0]["fsourcenumber"]}'", true);

                    if (datas.IsNullOrEmptyOrWhiteSpace() || !datas.Any()) return;

                    var hqhandleentry = datas[0]["fhqhandleentry"] as DynamicObjectCollection;

                    bool hasChange = false;
                    foreach (var item in proEntry)
                    {
                        bool hasExist = false;
                        foreach (var itemm in hqhandleentry)
                        {
                            if (itemm["fbillno_e"].Equals(billno) && itemm["fmaterialid_e"].Equals(item["fmaterialid"]))
                            {
                                hasExist = true;
                            }
                        }

                        if (!hasExist)
                        {
                            var modelnew = new DynamicObject(hqhandleentry.DynamicCollectionItemPropertyType);
                            modelnew["fbillno_e"] = billno;
                            modelnew["fmaterialid_e"] = item["fmaterialid"];
                            hqhandleentry.Add(modelnew);
                            hasChange = true;
                        }
                    }

                    if (hasChange)
                    {
                        // 保存前预处理
                        var preSerivce = this.Container.GetService<IPrepareSaveDataService>();
                        preSerivce.PrepareDataEntity(this.Context, propValueForm, datas.ToArray(), OperateOption.Create());

                        var result = this.Gateway.InvokeBillOperation(Context, "ydj_service", datas, "save", new Dictionary<string, object>());

                        this.RefreshParentPage();
                    }
                }
            }


        }

        /// <summary>
        /// 刷新父页面
        /// </summary>
        private void RefreshParentPage()
        {
            var formParameter = this?.ParentPageSession?.FormParameter as FormParameter;

            // 刷新父页面的父页面
            var pageId = formParameter?.ParentPageId;

            // 刷新父页面
            var parentFormId = formParameter?.FormId ?? "";
            if (!parentFormId.EqualsIgnoreCase(this.HtmlForm.Id))
            {
                pageId = formParameter?.PageId ?? "";
            }

            if (!pageId.IsNullOrEmptyOrWhiteSpace())
            {
                this.AddRefreshPageAction(pageId);
            }
        }

        private bool Checkfproductid(DynamicObject newData)
        {
            var res = false;
            var Fentry = newData["fproductentry"] as DynamicObjectCollection;
            if (Fentry != null && Fentry.Any())
            {
                foreach (var item in Fentry)
                {
                    var fproductid = Convert.ToString(item["fmaterialid"]);
                    if (fproductid.IsNullOrEmptyOrWhiteSpace())
                    {
                        return true;
                    }
                }
            }
            else
            {
                res = true;
            }
            return res;
        }
    }
}