using System;
using System.Linq;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework;
using System.Collections.Generic;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Enums;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.AfterFeedback
{
    /// <summary>
    /// 售后反馈单：反作废
    /// </summary>
    [InjectService]
    [FormId("ste_afterfeedback")]
    [OperationNo("uncancel")]
    public class UnCancel : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            //错误消息
            var errorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (newData["ffeedstatus"].Equals("aft_service_03"))
                {
                    string billNo = Convert.ToString(newData["fbillno"]);
                    errorMessage = $"对不起，单据编号为【{billNo}】的售后反馈单的售后状态=总部待审核，不允许反作废!";
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

        }
    }
}