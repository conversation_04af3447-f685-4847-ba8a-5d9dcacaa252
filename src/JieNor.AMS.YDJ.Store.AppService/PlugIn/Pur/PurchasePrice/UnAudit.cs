using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchasePrice
{
    /// <summary>
    /// 采购价目：反审核
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseprice|ydj_selfpurchaseprice")]
    [OperationNo("UnAudit")]
    public class UnAudit : BaseValidationPlugin
    {
    }
}