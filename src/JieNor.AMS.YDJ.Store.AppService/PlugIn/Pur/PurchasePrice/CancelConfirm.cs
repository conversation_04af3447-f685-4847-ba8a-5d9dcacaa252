using JieNor.AMS.YDJ.Store.AppService.Plugin.Price;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchasePrice
{
    /// <summary>
    /// 采购价目：明细取消确认
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseprice|ydj_selfpurchaseprice")]
    [OperationNo("CancelConfirm")]
    public class CancelConfirm : SetConfirmStatus
    {
        protected override string ConfirmStatus
        {
            get
            {
                return "1";
            }
        }

        protected override string PromptFieldId
        {
            get
            {
                return this.HtmlForm.NumberFldKey;
            }
        }

        protected override string ProductIdKey
        {
            get
            {
                return "fproductid_e";
            }
        }

        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            e.Rules.Add(new ValidationOrgOperation(this.OperationName));
        }
    }
}
