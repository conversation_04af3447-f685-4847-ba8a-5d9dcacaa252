using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PurchaseOrder
{
    /// <summary>
    /// 采购订单：销售方撤销发送，该操作主要用于接收销售订单发起的撤销发送
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("SalRepealSynergy")]
    public class SalRepealSynergy : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            string tranId = this.GetQueryOrSimpleParam<string>("tranId");
            if (tranId.IsNullOrEmptyOrWhiteSpace()) return;

            //采购订单
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            string where = $"fmainorgid=@fmainorgid and ftranid=@ftranid";
            var sqlParam = new SqlParam[]
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("ftranid", System.Data.DbType.String, tranId)
            };
            var dataReader = this.Context.GetPkIdDataReader(this.HtmlForm, where, sqlParam);
            var dataEntity = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
            if (dataEntity != null)
            {
                if (!Convert.ToString(dataEntity["fbizstatus"]).EqualsIgnoreCase("business_status_02"))
                {
                    throw new BusinessException($"采购订单【{dataEntity["fbillno"]}】业务状态不是【待受理】，不允许撤销发送！");
                }

                //更新状态
                dataEntity["fbizstatus"] = "business_status_01";
                dataEntity["fpublishstatus"] = "publish_status_01";
                dataEntity["fpublishcompany"] = "";
                dataEntity["fpublishcompanyid"] = "";
                dataEntity["fpublishdate"] = null;
                dataEntity["fsupplierorderno"] = "";

                //删除销售方同步过来的图纸明细
                DynamicObjectCollection drawEntitys = dataEntity["fdrawentity"] as DynamicObjectCollection;
                if (drawEntitys != null)
                {
                    List<DynamicObject> toRemoveList = new List<DynamicObject>();
                    foreach (var drawEntity in drawEntitys)
                    {
                        if (!drawEntity["fsourceentryid"].IsNullOrEmptyOrWhiteSpace())
                        {
                            toRemoveList.Add(drawEntity);
                        }
                    }
                    foreach (var item in toRemoveList)
                    {
                        drawEntitys.Remove(item);
                    }
                }

                //保存
                dm.Save(dataEntity);

                //标记成功
                this.Result.IsSuccess = true;
            }
        }
    }
}