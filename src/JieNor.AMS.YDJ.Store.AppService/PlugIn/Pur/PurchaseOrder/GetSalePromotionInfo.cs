using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.Store.AppService.Model.PurchaseOrder;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单：获取促销活动
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("getsalepromotioninfo")]
    public class GetSalePromotionInfo : AbstractOperationServicePlugIn
    {

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            string type = "";
            List<string> matchCombine = new List<string>();
            string rows = this.GetQueryOrSimpleParam("rows", "");
            if (!rows.IsNullOrEmptyOrWhiteSpace())
            {
                var rowsObj = JArray.Parse(rows);
                if (rowsObj.Count > 0)
                {
                    var commonCombine = new CommonCombine(this.Context, this.Context.Container.GetService<IDBService>());
                    var billlist = rowsObj.Select(o => Convert.ToString(o)).ToList();
                    var purorders = this.Context.LoadBizDataById(this.HtmlForm.Id, billlist);
                    foreach (var item in purorders)
                    {
                        List<string> mustcombine = new List<string>();
                        (this.Result.IsSuccess, this.Result.SrvData, mustcombine) = commonCombine.GetSalePromotion(this.Context, item);
                        if (this.Result.IsSuccess)
                        {
                            type = "noCombine";
                            matchCombine.Add(Convert.ToString(item["fbillno"]));
                        }
                        if (mustcombine.Count > 0)
                        {
                            type = "hasCombine";
                            matchCombine.Add(Convert.ToString(item["fbillno"]));
                        }
                    }
                }
            }

            this.Result.SrvData = new { matchCombine, type };
            this.Result.IsSuccess = true;
        }





    }
}
