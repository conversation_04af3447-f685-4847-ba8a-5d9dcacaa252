using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单：通过送达方ID获取供应商
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("GetSupplierByDeliverId")]
    public class GetSupplierByDeliverId : AbstractOperationServicePlugIn
    {
        private const string DeliverFormId = "bas_deliver"; //送达方表单ID
        private const string SupplierFormId = "ydj_supplier"; //供应方表单ID

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var deliverId = this.GetQueryOrSimpleParam<string>("deliverid", "");
            if (deliverId == null) return;

            var deliver = this.Context.LoadBizDataById(DeliverFormId, deliverId);
            if (deliver == null) return;

            IPurchaseOrderService purchaseOrderService = Context.Container.GetService<IPurchaseOrderService>();

            var suppliers = purchaseOrderService.GetSupplierIdByDeliverId(Context, deliverId);
            //不调用数据隔离方法
            // var suppliers = this.Context.LoadBizDataByFilter(SupplierFormId, $" forgid='{saleOrgId}' ");
            if (suppliers.IsNullOrEmptyOrWhiteSpace()) return;

            //var supplierId = suppliers.Select(x => Convert.ToString(x["id"])).FirstOrDefault();
            this.Result.SrvData = suppliers;
            this.Result.IsSuccess = true;
        }
    }
}
