using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PurchaseOrder
{
    /// <summary>
    /// 采购订单：删除
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("Delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var purchaseOrderService = this.Container.GetService<IPurchaseOrderService>();

            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fbilltypeid"]).EqualsIgnoreCase("ydj_purchaseorder_zb"))
                {
                    return false;
                }
                return true;
            }).WithMessage("采购订单【{0}】为总部手工单，不允许删除！", (billObj, propObj) => propObj["fbillno"]));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fpublishstatus"]).EqualsIgnoreCase("publish_status_02"))
                {
                    return false;
                }
                return true;
            }).WithMessage("采购订单【{0}】已建立协同关系，不允许删除！", (billObj, propObj) => propObj["fbillno"]));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (purchaseOrderService.CheckIsSync(this.Context, newData) && !Convert.ToString(newData["fbizstatus"]).EqualsIgnoreCase("business_status_01"))
                {
                    return false;
                }
                return true;
            }).WithMessage("采购订单【{0}】的业务状态不是【未提交】，不允许删除！", (billObj, propObj) => propObj["fbillno"]));

            //检查是否存在下游收支记录
            string incomeErrorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var incomeDisburseService = this.Container.GetService<IIncomeDisburseService>();
                return !incomeDisburseService.CheckIsExistsSourceOrder(this.Context, this.HtmlForm, newData, out incomeErrorMessage);
            }).WithMessage("{0}", (billObj, propObj) => incomeErrorMessage));

            /*
             * 1.采购订单表头操作<删除>时，需校验商品行是否包含【非标审批状态】=“待审批”，如果存在则需提示：“当前订单包含非标商品待审批中，不允许删除！”。 关联任务：35168
             */
            incomeErrorMessage = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fentrys = newData["fentity"] as DynamicObjectCollection;
                if (fentrys.Any(t => Convert.ToString(t["funstdtypestatus"]) == "02"))
                {
                    incomeErrorMessage = "当前订单包含非标商品待审批中，不允许删除！";
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => incomeErrorMessage));

            //检查是否存在下游出入库记录
            //string downErrorMessage = "";
            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    var stockOutInService = this.Container.GetService<IStockOutInService>();
            //    return !stockOutInService.CheckIsExistsSourceOrder(this.Context, this.HtmlForm, newData, out downErrorMessage);
            //}).WithMessage("{0}", (billObj, propObj) => downErrorMessage));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var changeStatus = Convert.ToString(newData["fchangestatus"]);
                var status = Convert.ToString(newData["fstatus"]).ToLower();
                return !((status == "b" || status == "d" || status == "e") && (changeStatus == "1" || changeStatus == "2" || changeStatus == "3"));
            }).WithMessage("单据状态=创建/已提交/已审核 且 变更状态=变更中/变更已提交/已变更的单据，不允许删除！"));

            //1.删除控制：当执行<删除>按钮时，如果【第三方来源】不为空且【总部合同状态】不为空，则需控制不允许删除，并提示：“对不起，当前定制采购订单已提交过总部，禁止删除，仅能作废！”
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var thirdsource = Convert.ToString(newData["fthirdsource"]);
                var hqderstatus = Convert.ToString(newData["fhqderstatus"]);
                if (!string.IsNullOrWhiteSpace(thirdsource) && !string.IsNullOrWhiteSpace(hqderstatus))
                {
                    return false;
                }
                return true;
            }).WithMessage("对不起，当前定制采购订单已提交过总部，禁止删除，仅能作废！"));
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            //删除前先记录关联的合同ids,避免删除后联查不到数据无法更新。
            base.EndOperationTransaction(e);
            var entitys = e.DataEntitys.SelectMany(o => o["fentity"] as DynamicObjectCollection).ToList();
            var ids = entitys.Select(o => Convert.ToString(o?["fsoorderentryid"])).ToList();
            // 反写销售合同【已转采购数】
            Core.Helpers.OrderQtyWriteBackHelper.WriteBackTransPurQty(
                this.Context, this.HtmlForm, e.DataEntitys, this.OperationNo);
            // 反写销售合同【流程状态】
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, e.DataEntitys, ids);
        }
    }
}