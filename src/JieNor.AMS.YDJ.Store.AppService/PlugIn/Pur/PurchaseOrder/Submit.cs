using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.Store.AppService.Model.PurchaseOrder;
using JieNor.AMS.YDJ.Store.AppService.PDA.WmsCommon;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单：提交
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("Submit")]
    public class Submit : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            IPurchaseOrderService purchaseOrderService = this.Container.GetService<IPurchaseOrderService>();

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return new PurchaseOrderCommon(this.Context).CheckDeptByBillType(newData);
            }).WithMessage("采购部门不能为空！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fsupplierid = Convert.ToString(newData["fsupplierid"]);
                if (fsupplierid.IsNullOrEmptyOrWhiteSpace())
                    return false;
                else
                    return true;
            }).WithMessage("供应商不能为空！"));

            var qcBillType = this.Context.GetBillTypeByBizObject(this.HtmlForm.Id, "ydj_purchaseorder_qc");
            var qcBillTypeId = qcBillType.fid;

            string errorMsg = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fbilltype = Convert.ToString(newData["fbilltypeid"]);
                //如果是二级经销商，这边放开非标限制
                if (fbilltype == qcBillTypeId || fbilltype == "ydj_purchaseorder_zb"/*||this.Context.IsSecondOrg*/)
                {
                    return true;
                }
                var entry = newData["fentity"] as DynamicObjectCollection;
                return !CheckIsReplaceFlag(entry, out errorMsg);
            }).WithMessage("{0}", (billObj, propObj) => errorMsg));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //如果是二级经销商，这边放开非标限制
                if (Convert.ToString(newData["fbilltypeid"]) == "ydj_purchaseorder_zb" /*|| this.Context.IsSecondOrg*/)
                {
                    return true;
                }
                return purchaseOrderService.CheckUnstdStatus(this.Context, newData, out errorMsg);

            }).WithMessage("{0}", (billObj, propObj) => errorMsg));

            e.Rules.Add(new SubmitValidation());

            string errorMessage = "";
            //判断是否存在总部商品 业绩品牌为空的情况
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMessage = "";
                var entry = newData["fentity"] as DynamicObjectCollection;
                return !CheckIsExistTopProEmptyResultBrand(entry, out errorMessage);
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));
            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    errorMsg = "";
            //    if (Convert.ToString(newData["fbilltypeid"]) == "ydj_purchaseorder_zb")
            //    {
            //        return true;
            //    }
            //    var entry = newData["fentity"] as DynamicObjectCollection;
            //    this.Container.GetService<LoadReferenceObjectManager>().Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), newData, false);
            //    var isok = true;
            //    foreach (var item in entry)
            //    {
            //        var fbizqty = Convert.ToInt32(item["fbizqty"]);
            //        var fpackqty = Convert.ToInt32((item["fmaterialid_ref"] as DynamicObject)?["fpackqty"]);
            //        if (fpackqty > 0 && fbizqty % fpackqty != 0)
            //        {
            //            errorMsg += "第 " + Convert.ToString(item["fseq"]) + " 行 商品 :" + JNConvert.ToStringAndTrim((item["fmaterialid_ref"] as DynamicObject)?["fname"]) + " 设置了采购件数【" + fpackqty + "】,当前行采购数量为【" + fbizqty + "】,不满足采购件数的倍数！<br>";
            //            isok = false;
            //        }
            //    }
            //    return isok;
            //}).WithMessage("{0}", (billObj, propObj) => errorMsg));


            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    errorMsg = "";
            //    var entry = newData["fentity"] as DynamicObjectCollection;
            //    var isok = true;
            //    foreach (var item in entry)
            //    {
            //        if (Convert.ToInt32(item["fbizqty"]) <= 0)
            //        {
            //            errorMsg += "第 " + Convert.ToInt32(item["fseq"]) + " 行采购数量等于0 不允许提交！<br>";
            //            isok = false;
            //        }
            //    }
            //    return isok;
            //}).WithMessage("{0}", (billObj, propObj) => errorMsg));

        }


        //判断是否存在总部商品 业绩品牌为空的情况 存在返回true
        private bool CheckIsExistTopProEmptyResultBrand(DynamicObjectCollection entry, out string errorMsg)
        {
            UserContext userCtx = this.Context;
            errorMsg = "";
            var mainorgid = userCtx.IsTopOrg ? userCtx.Company : userCtx.TopCompanyId;
            // var entry = newData["fentry"] as DynamicObjectCollection;
            //至少要有一行商品明细
            if (entry == null || entry.Count <= 0) return false;

            foreach (DynamicObject item in entry)
            {
                //业绩品牌
                var resultBrandId = Convert.ToString(item["fresultbrandid"]);
                var productId = Convert.ToString(item["fmaterialid"]);
                var productObj = userCtx.LoadBizDataById("ydj_product", productId);
                //商品明细中存在总部商品
                if (!mainorgid.EqualsIgnoreCase(Convert.ToString(productObj["fmainorgid"])))
                {
                    continue;
                }
                else
                {
                    if (resultBrandId.IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMsg = $"第{Convert.ToInt32(item["fseq"])}行商品【{Convert.ToString(productObj["fname"])}】业绩品牌为空，无法保存！";
                        //errorMsg = "存在总部商品业绩品牌为空，无法保存！";
                        return true;
                    }
                }
            }
            return false;
        }

        //【补件标记】=“是”且 （【允许定制】=“是” 或 【允许选配】=“是”）且（【非标状态】!=“终审”或“审批通过”）
        private bool CheckIsReplaceFlag(DynamicObjectCollection entry, out string errorMsg)
        {
            UserContext userCtx = this.Context;
            errorMsg = "";
            //至少要有一行商品明细
            if (entry == null || entry.Count <= 0) return false;

            foreach (DynamicObject item in entry)
            {
                //var fisoutspot = Convert.ToBoolean(item["fisoutspot"]);
                //if (fisoutspot) continue;
                var productId = Convert.ToString(item["fmaterialid"]);
                var productObj = userCtx.LoadBizDataById("ydj_product", productId);
                //补件标记
                var freplaceflag = Convert.ToBoolean(productObj["freplaceflag"]);
                //允许定制
                var fcustom = Convert.ToBoolean(productObj["fcustom"]);
                //允许选配
                var fispresetprop = Convert.ToBoolean(productObj["fispresetprop"]);
                var funstdtypestatus = Convert.ToString(item["funstdtypestatus"]);
                //'01':'新建','02':'待审批','03':'审批通过','04':'驳回','05':'待定价','06':'终审'
                if (freplaceflag && (fcustom || fispresetprop) && !(funstdtypestatus.EqualsIgnoreCase("03") || funstdtypestatus.EqualsIgnoreCase("06")))
                {
                    errorMsg = $"商品 {Convert.ToString(productObj["fname"])} 行为补件产品, 必须进行非标审批 或 非标终审过后才可提交！";
                    return true;
                }
            }
            return false;
        }
        private UserContext ctx;

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var secendconfirm = this.GetQueryOrSimpleParam<string>("secendconfirm");
            if (e.DataEntitys == null || e.DataEntitys.Length < 1)
            {
                return;
            }
            List<DynamicObject> datas = e.DataEntitys.ToList();
            foreach (var item in e.DataEntitys)
            {
                //已提交总部的话，就不继续走套餐的逻辑
                if (!string.IsNullOrEmpty(item["fhqderstatus"].ToStringAndTrim()) && Convert.ToString(item["fhqderstatus"]) != "05")
                {
                    datas.Remove(item);
                }
            }

            if (e.DataEntitys.Count() > 1)
            {
                return;
            }
            if (!secendconfirm.EqualsIgnoreCase("1"))
            {
                if (e.DataEntitys[0]["fbilltypeid"].ToString() == "ydj_purchaseorder_zb" || Convert.ToBoolean(e.DataEntitys[0]["fpiecesendtag"]))
                {
                    //总部手工单，不凑单
                    return;
                }
                //批量的数据，不进这个逻辑
                var commonCombine = new CommonCombine(this.Context, this.Context.Container.GetService<IDBService>());
                List<MatchCombineActivity> entity = new List<MatchCombineActivity>();
                List<string> mustcombine = new List<string>();
                bool flag = false;
                (flag, this.Result.SrvData, mustcombine) = commonCombine.GetSalePromotion(this.Context, e.DataEntitys[0]);
                this.Result.OptionData.Add("SrvData", this.Result.SrvData);
                if (mustcombine.Count > 0)
                {
                    commonCombine.SaveCombineInfo1(this.Context, e.DataEntitys.ToList(), mustcombine, false);
                    //ModifyPromotion(e.DataEntitys.ToList(), mustcombine);
                }

                if (flag)
                {
                    e.DataEntitys = new DynamicObject[] { };
                }
            }
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;
            //提交时采购订单的采购单价、成交单价根据《综合价目表》取值联动计算金额。
            GetPrice_ReNewOrder(e.DataEntitys);

            base.BeginOperationTransaction(e);
        }

        /// <summary>
        /// 当勾选了 【焕新标记】的才需要再获取综合价目表价格并重算金额
        /// </summary>
        /// <param name="DataEntitys"></param>
        private void GetPrice_ReNewOrder(DynamicObject[] DataEntitys)
        {
            DynamicObject[] DataEntitys_ReNew = DataEntitys.Where(o => Convert.ToBoolean(o["frenewalflag"])).ToArray();
            List<JToken> productInfos = new List<JToken>();
            List<JObject> ls = new List<JObject>();
            if (!DataEntitys_ReNew.Any()) return;
            foreach (var orderData in DataEntitys_ReNew)
            {
                var Entrys = orderData["fentity"] as DynamicObjectCollection;
                foreach (var Entry in Entrys)
                {
                    productInfos.Add(new JObject
                    {
                        ["clientId"] = Entry["id"] as string,
                        ["productId"] = Entry["fmaterialid"] as string,
                        ["bizDate"] = DateTime.Now,
                        ["attrInfo"] = new JObject
                        {
                            ["id"] = Entry["fattrinfo"] as string,
                            ["entities"] = new JArray()
                        }
                    });
                    ls.Add(new JObject
                    {
                        { "clientId",Entry["id"] as string },
                        { "fproductid",Entry["fmaterialid"] as string },
                        { "fpurprice", 0.00 },
                        { "fsalprice",0.00},
                        { "fattrinfo", Entry["fattrinfo"] as string }
                    });
                }
            }
            IPriceService priceService = this.Context.Container.GetService<IPriceService>();
            //获取综合价目表 采购折前单价。
            priceService.PricesynThe(productInfos, ls, this.Context);

            foreach (var purchaseOrder in DataEntitys_ReNew)
            {
                Calcuate(this.Context, purchaseOrder, ls);
            }
            //最后再将重算的数据调用保存。
            var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, DataEntitys_ReNew, "draft", new Dictionary<string, object>());
            result.ThrowIfHasError();
        }

        public static void Calcuate(UserContext userCtx, DynamicObject purchaseOrder, List<JObject> ls)
        {
            var entities = purchaseOrder["fentity"] as DynamicObjectCollection;
            foreach (var entity in entities)
            {
                var entryid = Convert.ToString(entity["id"]);
                //获取对应行 商品维度在 综合价目表中匹配到的 采购单价（折前）
                var fpurfacprice = ls.Where(o => Convert.ToString(o["clientId"]).EqualsIgnoreCase(entryid)).Select(o => Convert.ToDecimal(o["fpurfacprice"]))?.FirstOrDefault() ?? 0M;

                decimal qty = Convert.ToDecimal(entity["fbizqty"]);
                decimal price = fpurfacprice;
                decimal distamount = 0M;
                decimal amount = qty * price;
                decimal dealprice = fpurfacprice;
                decimal dealamount = qty * dealprice;
                decimal distrate = 10;//（采购单价、成交单价相同 都取综合价目表 则折扣为1）

                entity["fprice"] = price;
                entity["famount"] = amount;
                entity["fdistrate"] = distrate;
                entity["fdistamount"] = distamount;
                entity["fdealamount"] = dealamount;
                entity["fdealprice"] = dealprice;
            }

            userCtx.Container.GetService<IPurchaseOrderService>().CalculateSettlement(userCtx, purchaseOrder);
        }
    }
}
