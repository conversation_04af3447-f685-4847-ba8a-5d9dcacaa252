using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MobileShop
{
    [InjectService]
    [FormId("ydj_msmenu")]
    [OperationNo("getdefaultmenus")]
    public class GetDefaultMenus : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            //根据当前用户id获取用户的自定义菜单id
            var userMenuForm = this.Container.GetService<IMetaModelService>()?.LoadFormModel(this.Context, "ydj_msusermenu");
            var userMenuDm = this.Container.GetService<IDataManager>();
            userMenuDm.InitDbContext(this.Context, userMenuForm.GetDynamicObjectType(this.Context));

            string where = $"fuserid=@fuserid and fappid=@fappid and fmainorgid=@fmainorgid";
            SqlParam[] sqlParams = new SqlParam[]
            {
                new SqlParam("fmainorgid",System.Data.DbType.String,this.Context.Company),
                new SqlParam("fuserid",System.Data.DbType.String,this.Context.UserId),
                new SqlParam("fappid",System.Data.DbType.String,this.Context.AppId)
            };

            var dataReader = this.Context.GetPkIdDataReader(userMenuForm, where, sqlParams);
            var userMenuIds = userMenuDm.SelectBy(dataReader).OfType<DynamicObject>().Select(x => Convert.ToString(x["fmenuid"])).ToList();

            List<DynamicObject> menuEntities = null;
            var menuDm = this.Container.GetService<IDataManager>();
            menuDm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            List<Dictionary<string, string>> srvData = null;

            if (userMenuIds != null && userMenuIds.Count > 0)
            {
                //获取自定义菜单
                List<DynamicObject> userMenuEntities = menuDm.Select(userMenuIds).OfType<DynamicObject>().ToList();
                menuEntities = new List<DynamicObject>();
                //按用户自定义的顺序排序
                foreach(string userMenuId in userMenuIds)
                {
                    menuEntities.Add(userMenuEntities.FirstOrDefault(x => Convert.ToString(x["id"]) == userMenuId));
                }
            }
            else
            {
                //如果用户没有自定义菜单获取首页默认的自定义菜单
                where = $"fisdefault='1' and fappid=@fappid";
                sqlParams = new SqlParam[]
                {
                    new SqlParam("fappid",System.Data.DbType.String,this.Context.AppId)
                };
                dataReader = this.Context.GetPkIdDataReader(this.HtmlForm, where, sqlParams);
                menuEntities = menuDm.SelectBy(dataReader).OfType<DynamicObject>().ToList();
                //按菜单设置的顺序排序
                menuEntities.Sort((x, y) => Convert.ToInt32(x["fmenusort"]) - Convert.ToInt32(y["fmenusort"]));
            }

            if (menuEntities != null)
            {
                srvData = menuEntities.Select(x => new Dictionary<string, string>
                {
                    { "id",Convert.ToString(x["id"])},
                    { "name",Convert.ToString(x["fname"])},
                    { "link",Convert.ToString(x["flink"])},
                    { "icon",string.Concat(this.Context.AppServer.ToString(),string.IsNullOrWhiteSpace(Convert.ToString(x["ficon"]))?string.Empty:Convert.ToString(x["ficon"]).Substring(1))},
                    { "parameter",Convert.ToString(x["fparameter"])}
                }).ToList();
            }

            if (srvData == null)
            {
                srvData = new List<Dictionary<string, string>>();
            }

            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "获取首页菜单成功!";
            this.Result.SrvData = srvData;

        }
    }
}
