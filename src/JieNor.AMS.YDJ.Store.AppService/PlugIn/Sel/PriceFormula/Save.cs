using JieNor.AMS.YDJ.Store.AppService.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sel.PriceFormula
{
    /// <summary>
    /// 选配计价公式：保存
    /// 作者;zpf
    /// 日期：2022-01-13
    /// </summary>
    [InjectService]
    [FormId("sel_priceformula")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*1.保存时校验单据体-约束信息【条件】与【公式】, 是否均符合表达式语法解析要求, 如果不符合语法解析要求时报错并提示错误信息 (哪行哪种错误)*/
            this.Result.MsgStyle = (int)Enu_MsgStyle.Dialog;  //弹窗提示
            var isValidationPasses = true;
            e.Rules.Add(this.RuleFor("fentity", d => d).IsTrue((n, o) =>
            {
                var fconstraintcondition = n["ffcontaint"].IsNullOrEmptyOrWhiteSpace() ? "" : n["ffcontaint"].ToString();//条件
                var fseq = n["fseq"];

                if (!string.IsNullOrWhiteSpace(fconstraintcondition))
                {
                    var parseResults = StringFormatValidation.StringRegularParsing(this.Context, fconstraintcondition, out isValidationPasses);
                    if (!isValidationPasses)
                        return false;
                }
                return true;
            }).WithMessage("【约束信息】明细行{0}【条件】格式有问题，参考格式“[属性名]=属性值”", (dy, dyObj) => dy["fseq"]));
            e.Rules.Add(this.RuleFor("fentity", d => d).IsTrue((n, o) =>
            {
                var fconstraintval = n["fpriceformula"].IsNullOrEmptyOrWhiteSpace() ? "" : n["fpriceformula"].ToString();//公式
                var fseq = n["fseq"];
                if (!string.IsNullOrWhiteSpace(fconstraintval))
                {
                    var parseResults = StringFormatValidation.StringRegularParsing(this.Context, fconstraintval, out isValidationPasses);
                    if (!isValidationPasses)
                        return false;
                }
                return true;
            }).WithMessage("【约束信息】明细行{0}【公式】格式有问题，参考格式“[属性名]=属性值”", (dy, dyObj) => dy["fseq"]));
        }
    }
}
