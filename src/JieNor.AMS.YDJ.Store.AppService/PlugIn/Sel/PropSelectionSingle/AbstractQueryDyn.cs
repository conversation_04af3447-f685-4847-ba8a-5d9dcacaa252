using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.Core.Interface;
using Newtonsoft.Json.Linq;
using JieNor.AMS.YDJ.Core;
using JieNor.Framework.MetaCore.FormModel.List;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sel.PropSelectionSingle
{
    /// <summary>
    /// 属性选配：动态列基础资料字段（模糊查询、弹窗查询）操作抽象基类
    /// </summary>
    public abstract class AbstractQueryDyn : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 准备操作选项时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            base.OnPrepareOperationOption(e);

            e.OpCtlParam.IgnoreOpMessage = true;
        }

        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName)
            {
                case "onAfterParseFilterString":
                    this.OnAfterParseFilterString(e);
                    break;
                case OnCustomServiceEventArgs.PrepareQueryBuilderParameter:
                    this.PrepareQueryBuilderParameter(e);
                    break;
                case "afterListData":
                    this.AfterListData(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 处理基础资料字段过滤条件解析后事件逻辑
        /// </summary>
        /// <param name="e"></param>
        private void OnAfterParseFilterString(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Tuple<string, string>;
            var fieldKey = eventData.Item1?.ToLowerInvariant(); //基础资料字段标识
            var fieldFilter = eventData.Item2; //基础资料字段过滤条件
            switch (fieldKey)
            {
                case "fpropvalue":

                    var productId = this.GetQueryOrSimpleParam<string>("productId", "");
                    var isNonStandard = this.GetQueryOrSimpleParam<bool>("isNonStandard", false);
                    var selCategoryId = this.GetQueryOrSimpleParam<string>("selCategoryId", "");
                    var selPropId = this.GetQueryOrSimpleParam<string>("selPropId", "");
                    var propSelService = this.Container.GetService<IPropSelectionService>();

                    //本插件提供的过滤条件
                    var filterStr = $"fpropid<>'' and fpropid='{selPropId}'";

                    if (isNonStandard)
                    {
                        //filterStr += " and fnosuit =1 ";
                        //构建选配范围过滤条件
                        var selRangeFilter = propSelService.BuildSelRangeFilter(this.Context, selCategoryId, selPropId);
                        if (!selRangeFilter.IsNullOrEmptyOrWhiteSpace())
                        {
                            if (!filterStr.IsNullOrEmptyOrWhiteSpace()) selRangeFilter = $" and {selRangeFilter}";
                            filterStr += selRangeFilter;
                        }

                        #region 非标属性值过滤现在也需要走选配约束

                        //构建选配范围属性值ID
                        var selRangePropValueIds = propSelService.BuildSelRangePropValueIds(this.Context, selCategoryId, selPropId, out string rangFilter);

                        //属性选配页面中的属性信息
                        var propEntryJson = this.GetQueryOrSimpleParam<string>("propList", "");
                        var propList = propEntryJson.FromJson<List<PropEntity>>();

                        var param = new SelectionConstraintMatchParam
                        {
                            SelCategoryId = selCategoryId,
                            SelRangePropValueIds = selRangePropValueIds,
                            PropList = propList
                        };
                        param.isNonStandard = true;
                        //构建选配约束过滤条件
                        var selConstraintFilter = propSelService.BuildSelConstraintFilter(this.Context, param);
                        if (!selConstraintFilter.IsNullOrEmptyOrWhiteSpace())
                        {
                            if (!filterStr.IsNullOrEmptyOrWhiteSpace()) selConstraintFilter = $" and {selConstraintFilter}";
                            filterStr += selConstraintFilter;
                        }

                        #endregion


                        //属性有勾选【支持非标库】时，才需要显示非标值
                        var propData = this.Context.LoadBizBillHeadDataById("sel_prop", selPropId, "fallownosuitlib");
                        var allowNoSuitLib = Convert.ToString(propData?["fallownosuitlib"]) == "1";
                        if (allowNoSuitLib)
                        {
                            //构建非标属性值过滤条件
                            var nonStandardFilter = propSelService.BuildNonStandardPropValueFilter(this.Context, productId, selPropId, param.Filter_NOTIN);
                            if (!nonStandardFilter.IsNullOrEmptyOrWhiteSpace())
                            {
                                //31194 ：如果是非标的情况从非标库映射获取选配范围时 不需要考虑前面的选配范围，因为选配范围里的非标可能是N，不满足业务
                                //filterStr = $"  (fpropid<>'' and fpropid='{selPropId}' and {nonStandardFilter})";
                                //还原回之前的效果
                                filterStr += $" or (fpropid<>'' and fpropid='{selPropId}' and {nonStandardFilter})";
                            }
                        }

                        //非标录入时创建的属性值要可以被选到
                        //filterStr += $" or (fpropid<>'' and fpropid='{selPropId}' and fnosuitcreate='1')";
                    }
                    else
                    {
                        //当前属性是否启用选配约束，如果没有启用，则可以选择选配范围中定义的所有属性值
                        var enableConstraint = this.GetQueryOrSimpleParam<bool>("enableConstraint", false);
                        //enableConstraint 会导致默认属性值不参与过滤，需要切换属性值后才会影响选配约束
                        if (true)
                        {
                            //构建选配范围属性值ID
                            var selRangePropValueIds = propSelService.BuildSelRangePropValueIds(this.Context, selCategoryId, selPropId,out string rangFilter);

                            //属性选配页面中的属性信息
                            var propEntryJson = this.GetQueryOrSimpleParam<string>("propList", "");
                            var propList = propEntryJson.FromJson<List<PropEntity>>();

                            var param = new SelectionConstraintMatchParam
                            {
                                SelCategoryId = selCategoryId,
                                SelRangePropValueIds = selRangePropValueIds,
                                PropList = propList
                            };

                            //构建选配约束过滤条件
                            var selConstraintFilter = propSelService.BuildSelConstraintFilter(this.Context, param);
                            if (!selConstraintFilter.IsNullOrEmptyOrWhiteSpace())
                            {
                                if (!filterStr.IsNullOrEmptyOrWhiteSpace()) selConstraintFilter = $" and {selConstraintFilter}";
                                filterStr += selConstraintFilter;
                            }
                        }
                        //else
                        //{
                        //    //构建选配范围过滤条件
                        //    var selRangeFilter = propSelService.BuildSelRangeFilter(this.Context, selCategoryId, selPropId);
                        //    if (!selRangeFilter.IsNullOrEmptyOrWhiteSpace())
                        //    {
                        //        if (!filterStr.IsNullOrEmptyOrWhiteSpace()) selRangeFilter = $" and {selRangeFilter}";
                        //        filterStr += selRangeFilter;
                        //    }
                        //}
                    }

                    e.Result = filterStr;
                    e.Cancel = true;

                    break;
            }
        }

        /// <summary>
        /// //列表准备查询过滤参数事件：可以在此处自定义额外的过滤条件
        /// </summary>
        /// <param name="e"></param>
        private void PrepareQueryBuilderParameter(OnCustomServiceEventArgs e)
        {
            var queryPara = e.EventData as SqlBuilderParameter;
            if (queryPara != null)
            {
                //如果属性选配已经明确了要查询【由非标录入时生成】为 true 的属性值时，则这里以属性选配时指定的条件为准，不再加下面的过滤条件
                if (queryPara.FilterString.Contains("fnosuitcreate='1'")) return;

                //只查询【由非标录入时生成】为 false 的属性值
                queryPara.FilterString = queryPara.FilterString.JoinFilterString("fnosuitcreate='0'");

                //如果是选配页面发起的属性值基础资料查询，则固定按属性值编码升序排序
                queryPara.OrderByString = "fnumber asc";
            }
        }

        /// <summary>
        /// 列表数据查询后触发的事件：可以干预列表数据
        /// </summary>
        /// <param name="e"></param>
        private void AfterListData(OnCustomServiceEventArgs e)
        {
            var listData = e.EventData as List<Dictionary<string, object>>;

            //排除重名的属性值
            PropValueHelper.ExcludeRepeatPropValue(listData);

            //if (listData == null || !listData.Any()) {
            //    var isNonStandard = this.GetQueryOrSimpleParam<bool>("isNonStandard", false);
            //    if (isNonStandard)
            //    {
            //        this.Result.ComplexMessage.WarningMessages.Add("请联系总部管理员维护商品非标类别");
            //    }
            //};
        }
    }
}
