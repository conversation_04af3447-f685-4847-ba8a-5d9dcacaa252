using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin
{

    public class LinkFormSearchBase : AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);
            switch (e.EventName)
            {
                case "dealLinkForm":
                    dealLinkForm(e);
                    break;
                default:
                    return;
            }
        }

        private void dealLinkForm(OnCustomServiceEventArgs e)
        {
            if (e.DataEntities == null || e.DataEntities.Length <= 0) return;

            var eventData = e.EventData as Dictionary<string, object>;
            var linkFormDatas = eventData["linkFormDatas"] as List<Dictionary<string, object>>;
            if (linkFormDatas == null)
            {
                return;
            }

            var userContext = eventData["userContext"] as UserContext;
            if (userContext == null)
            {
                return;
            }

            DealLinkForm(userContext, e.DataEntities, linkFormDatas);
        }

        protected virtual void DealLinkForm(UserContext userContext, DynamicObject[] dataEntities, List<Dictionary<string, object>> linkFormDatas)
        {

        }

        /// <summary>
        /// 联查预留单
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="dataEntities"></param>
        /// <param name="linkFormDatas"></param>
        public void LinkReserveBill(UserContext userContext, DynamicObject[] dataEntities, List<Dictionary<string, object>> linkFormDatas)
        {
            var pkids = dataEntities.Select(o => o["id"]?.ToString()).Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            if (pkids.Count <= 0) return;

            string formId = this.HtmlForm.Id;

            //预留单
            var metaModelService = userContext.Container.GetService<IMetaModelService>();
            var reserveForm = metaModelService.LoadFormModel(this.Context, "stk_reservebill");
            var filterStr = string.Empty;
            if (pkids.Count == 1)
            {
                filterStr = $"fsourcetype='{formId}' and fsourcepkid='{pkids[0]}'";
            }
            else
            {
                filterStr = $"fsourcetype='{formId}' and fsourcepkid in ({pkids.JoinEx(",", true)})";
            }

            linkFormDatas.Add(new Dictionary<string, object>
            {
                { "formId", reserveForm.Id },
                { "formCaption", reserveForm.Caption },
                { "flag", "nextForm" },
                { "filterString", filterStr },
            });
            //预留归档查询
            linkFormDatas.Add(new Dictionary<string, object>
            {
                { "formId", "stk_reservebill_history" },
                { "formCaption", "预留单-归档" },
                { "flag", "nextForm" },
                { "filterString", filterStr },
            });
        }
    }
}
