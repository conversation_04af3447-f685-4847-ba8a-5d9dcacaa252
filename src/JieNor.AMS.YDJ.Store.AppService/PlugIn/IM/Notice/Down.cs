using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.IM.Notice
{
    /// <summary>
    /// 公告:下一页
    /// </summary>
    [InjectService]
    [FormId("sys_dashboard")]
    [OperationNo("Down")]
    public class Down : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e); 
        } 

    }
}
