using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.BarcodeMaster
{
    /// <summary>
    /// 条码联查
    /// </summary>
    [InjectService]
    [FormId("stk_sostockreturn")]
    [OperationNo("searchbarcode")]
    public class SearchBarcode : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            var datas = this.GetQueryOrSimpleParam<string>("datas");
            var dm = this.GetDataManager();
            var meta = HtmlParser.LoadFormMetaFromCache("bcm_barcodemaster", this.Context);
            dm.InitDbContext(this.Context, meta.GetDynamicObjectType(this.Context));

            var rowids = new List<string>();
            if (!datas.IsNullOrEmptyOrWhiteSpace())
            {
                var rows = JArray.Parse(datas);
                foreach (var row in rows)
                {
                    rowids.Add(row["fentryid"].ToString());
                }
            }

            string sqlWhere = "";
            switch (this.HtmlForm.Id.ToLower())
            {
                case "stk_sostockreturn":
                    var data = e.DataEntitys.FirstOrDefault();
                    sqlWhere = $@"fid in (select  a.fid from t_bcm_barcodemaster a 
                                left join t_bcm_mastermtrlentry b on a.fid = b.fid
                                left join t_bcm_scanresult c on b.fid = c.fbarcode
                                left join t_stk_sostockreturnentry d on d.fmaterialid = b.fmaterialid
                                where c.fsourceformid = 'stk_sostockreturn' and c.fsourcebillno = '{Convert.ToString(data["fbillno"])}' and d.fentryid in ('{string.Join("','",rowids)}'))";
                    break;
                default:

                    break;
            }

            var action = this.Context.ShowListForm("bcm_barcodemaster", sqlWhere, this.CurrentPageId, Enu_OpenStyle.Modal);
            this.Result.HtmlActions.Add(action);
            this.Result.IsShowMessage = false;
        }

    }
}
