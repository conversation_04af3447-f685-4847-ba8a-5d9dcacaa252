using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.ReceptionScanTask
{
    /// <summary>
    /// 收货扫描任务：反作废
    /// </summary>
    [InjectService]
    [FormId("bcm_receptionscantask")]
    [OperationNo("uncancel")]
    public class UnCancel : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            // 反写采购订单、销售合同【总部已发货数】
            OrderQtyWriteBackHelper.WriteBackHqDeliveryQty(this.Context, this.HtmlForm, e.DataEntitys, this.OperationNo);
        }
    }
}
