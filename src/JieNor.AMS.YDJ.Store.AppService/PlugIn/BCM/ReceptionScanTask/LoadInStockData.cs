using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.ReceptionScanTask
{
    /// <summary>
    /// 收货扫描任务：加载采购入库数据
    /// </summary>
    [InjectService]
    [FormId("bcm_receptionscantask")]
    [OperationNo("LoadInStockData")]
    public class LoadInStockData : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                throw new BusinessException($"请先保存后再执行该操作！");
            }
            if (!this.Context.IsDirectSale)
            {
                throw new BusinessException($"当前经销商非直营经销商，不允许操作入库！");
            }
            var profileService = this.Container.GetService<ISystemProfile>();

            //是否启用条码管理                    
            var fenablebarcode = profileService.GetSystemParameter(this.Context, "stk_stockparam", "fenablebarcode", false);
            //总部发货后自动生成入库单
            var fautostockinorder = profileService.GetSystemParameter(this.Context, "stk_stockparam", "fautostockinorder", false);
            if (!fenablebarcode)
            {
                throw new BusinessException("当前未启用条码管理系统，不允许操作入库！");
            }
            if (!fautostockinorder)
            {
                throw new BusinessException("当前未启用总部发货后自动生成入库单，不允许操作入库！");
            }

            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);

            var dataEntity = e.DataEntitys[0];
            var entitys = dataEntity["ftaskentity"] as DynamicObjectCollection;
            var detailEntitys = entitys.Where(a => Convert.ToDecimal(a["fwaitworkqty"]) > 0).ToList();
            if (!detailEntitys.Any())
            {
                throw new BusinessException("所勾选的商品不满足入库条件，无法执行入库操作！");
            }
            foreach (var row in detailEntitys)
            {
                string sourceId = Convert.ToString(row["fsourcefid"]);
                if (string.IsNullOrWhiteSpace(sourceId))
                {
                    sourceId = this.Context.LoadBizDataByNo("ydj_purchaseorder", "fbillno", new List<string>() { Convert.ToString(row["fsourcebillno"]) }).FirstOrDefault()?["id"]?.ToString();
                    row["fsourcefid"] = sourceId;
                }
            }

            string fpoorderid = Convert.ToString(detailEntitys[0]["fsourcefid"]);
            var poEntryIds = detailEntitys.Where(a => selectRowIds.Select(s => s.Id).ToList().IndexOf(Convert.ToString(a["id"])) >= 0).Select(a => Convert.ToString(a["fsourceentryid"])).ToList();

            var poStockObj = this.Context.LoadBizDataByFilter("stk_postockin", $" EXISTS (select distinct fid from t_stk_postockinentry poinentry with(nolock) where fpoorderinterid ='{fpoorderid}' AND fpoorderentryid in ('{string.Join("','", poEntryIds.ToList())}') AND t_stk_postockin.fid=poinentry.fid ) AND  fcancelstatus=0 ");

            if (poStockObj.Any(a => !Convert.ToString(a["fstatus"]).Equals("E")))
            {
                throw new BusinessException("所勾选的商品存在未完结的入库单，不允许重复入库！");
            }

            decimal fwaitinstockqty = 0;
            decimal finstockqty = 0;
            var fentityList = new List<Dictionary<string, object>>();

            // 先构建entitys的索引，便于后续查找
            var entitysIndex = detailEntitys.ToDictionary(
                row => $"{dataEntity["id"]}_{row["id"]}",
                row => row
            );

            // 汇总每个entity的已入库数量
            var inStockQtyDict = new Dictionary<string, decimal>();
            foreach (var poStock in poStockObj)
            {
                var entrys = poStock["fentity"] as DynamicObjectCollection;
                if (entrys == null) continue;
                foreach (var entry in entrys)
                {
                    var taskInterId = Convert.ToString(entry["freceptionid"]);
                    var taskEntryInterId = Convert.ToString(entry["freceptionentryid"]);
                    var key = $"{taskInterId}_{taskEntryInterId}";
                    var qty = entry["fbizqty"] != null ? Convert.ToDecimal(entry["fbizqty"]) : 0m;
                    if (inStockQtyDict.ContainsKey(key))
                        inStockQtyDict[key] += qty;
                    else
                        inStockQtyDict[key] = qty;
                }
            }

            foreach (var row in detailEntitys)
            {
                if (!selectRowIds.Any(a => a.Id.Equals(Convert.ToString(row["id"])))) continue;
                var key = $"{dataEntity["id"]}_{row["id"]}";
                finstockqty = inStockQtyDict.ContainsKey(key) ? inStockQtyDict[key] : 0m;
                string sourceId = Convert.ToString(row["fsourcefid"]);
                if (string.IsNullOrWhiteSpace(sourceId))
                {
                    sourceId = this.Context.LoadBizDataByNo("ydj_purchaseorder", "fbillno", new List<string>() { Convert.ToString(row["fsourcebillno"]) }).FirstOrDefault()?["id"]?.ToString();
                }
                var entity = new Dictionary<string, object>
                {
                    ["freceptionid"] = dataEntity["id"],//表头ID
                    ["freceptionentryid"] = row["id"],//表体ID
                    ["fpoid"] = sourceId,
                    ["fbillno"] = row["fsourcebillno"],
                    ["fpoentryid"] = row["fsourceentryid"],
                    ["fmaterialid"] = row["fmaterialid"],
                    ["fattrinfo"] = row["fattrinfo"],
                    ["fattrinfo_e"] = row["fattrinfo_e"],
                    ["fcustomdesc"] = row["fcustomdesc"],
                    ["funitid"] = row["funitid"],
                    ["fstorehouseid"] = row["fstorehouseid"],
                    ["fstorelocationid"] = row["fstorelocationid"],
                    ["fcurrworkqty"] = row["fcurrworkqty"],//本次作业数量
                    ["fwaitinstockqty"] = Convert.ToDecimal(row["fcurrworkqty"]) - finstockqty,// fwaitinstockqty,//待入库数量
                    ["finstockqty"] = finstockqty,//已入库数量
                    ["fbizqty"] = 0
                };
                fentityList.Add(entity);
            }
            this.Result.SrvData = fentityList;
            this.Result.IsSuccess = true;
        }


        /// <summary>
        /// 联查采购退货单
        /// </summary>
        private void LinkPoStockReturn(DynamicObject[] dataEntities, IMetaModelService metaModelService, List<Dictionary<string, object>> linkFormDatas)
        {
            var id = dataEntities.Select(o => o["id"]).Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            var strSql = $@"select distinct fid from t_stk_postockreturnentry with(nolock) where ffeedbackinterid ='{id[0]}'";
            List<string> fids = new List<string>();
            using (var dr = this.Context.ExecuteReader(strSql, new List<SqlParam>() { }))
            {
                while (dr.Read())
                {
                    fids.Add(dr["fid"].ToString());
                }
            }

        }

    }
}