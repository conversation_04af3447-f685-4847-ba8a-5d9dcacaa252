using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.ReceptionScanTask
{
    /// <summary>
    /// 扫描收货任务：打包
    /// </summary>
    [InjectService]
    [FormId("bcm_receptionscantask")]
    [OperationNo("CommitInStock")]
    public class CommitInStock : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            this.Result.IsSuccess = false;
            base.BeginOperationTransaction(e);

            var scanDataStr = this.GetQueryOrSimpleParam<string>("scanData");
            List<Dictionary<string, string>> scanDatas = null;

            if (string.IsNullOrWhiteSpace(scanDataStr) == false)
            {
                scanDatas = Newtonsoft.Json.JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(scanDataStr);
            }

            checkArg(scanDatas);

            if (this.Result.ComplexMessage.ErrorMessages.Count > 0)
            {
                return;
            }

            //获取扫描记录
            var scanResultEntities = getScanResult(scanDatas);
            //更新条码主档，更新追溯记录和头仓库信息
            updateBarCodeMaster(scanDatas, ref scanResultEntities);
            //调用下推源单
            push(scanResultEntities);
            //更新扫描记录的状态为已扫描
            updateScanResult(scanResultEntities);
            //读取源单扫描状态返回客户端
            readScanStatus(scanResultEntities);
        }

        private void updateScanResult(List<DynamicObject> scanResultEntities)
        {
            if (scanResultEntities == null || scanResultEntities.Count <= 0)
            {
                return;
            }

            foreach (var scanResultEntity in scanResultEntities)
            {
                var currentTime = DateTime.Now;
                scanResultEntity["fbizstatus"] = "2";
                scanResultEntity["fdate"] = currentTime;
                scanResultEntity["foperatorid"] = this.Context.UserId;
                scanResultEntity["fopdatetime"] = currentTime;
                scanResultEntity["fdeviceid"] = string.Empty;
            }

            var metaModelService = this.Container.GetService<IMetaModelService>();
            var scanResultHtmlForm = metaModelService.LoadFormModel(this.Context, "bcm_scanresult");
            var dm = this.Container.GetService<IDataManager>();

            dm.InitDbContext(this.Context, scanResultHtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(scanResultEntities);
        }

        private void readScanStatus(List<DynamicObject> scanResultEntities)
        {
            if (scanResultEntities == null || scanResultEntities.Count <= 0)
            {
                return;
            }
            var groupDatas = scanResultEntities.GroupBy(x => Convert.ToString(x["fsourceformid"])).ToList();
            var scanStatus = new List<string>();
            var metaModelService = this.Container.GetService<IMetaModelService>();

            foreach(var groupData in groupDatas)
            {
                var htmlForm = metaModelService.LoadFormModel(this.Context, groupData.Key);
                var dm = this.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

                var ids = groupData.Select(x => Convert.ToString(x["fsourceinterid"])).Distinct().ToList();
                var dataEntities = dm.Select(ids).OfType<DynamicObject>().ToList();

                foreach(var dataEntity in dataEntities)
                {
                    var fscanstatus = Convert.ToString(dataEntity["fscanstatus"]);
                    fscanstatus = string.IsNullOrWhiteSpace(fscanstatus) ? "1" : fscanstatus;
                    scanStatus.Add(fscanstatus);
                }
            }

            //现在只返回第一个源单的扫描状态，待客户端支持多单时再返回多单的扫描状态
            this.Result.SrvData = scanStatus.Count > 0 ? scanStatus[0] : null;
        }

        /// <summary>
        /// 下推下游单据
        /// </summary>
        /// <param name="scanResultEntities"></param>
        private void push(List<DynamicObject> scanResultEntities)
        {
            var packOrderService = this.Container.GetService<IPackOrderService>();
            packOrderService.PushBySanResult(this.Context, scanResultEntities, this.Result.ComplexMessage.ErrorMessages);
        }

        /// <summary>
        /// 更新条码主档，更新追溯记录和头仓库信息
        /// </summary>
        /// <param name="scanDatas"></param>
        /// <param name="scanResultEntities"></param>
        private void updateBarCodeMaster(List<Dictionary<string,string>> scanDatas, ref List<DynamicObject> scanResultEntities)
        {
            var metaModelService = this.Container.GetService<IMetaModelService>();
            var barcodeMasterHtmlForm = metaModelService.LoadFormModel(this.Context, "bcm_barcodemaster");
            var dm = this.Container.GetService<IDataManager>();

            dm.InitDbContext(this.Context, barcodeMasterHtmlForm.GetDynamicObjectType(this.Context));
            var bsMaps = scanResultEntities.Select(x => new { b = Convert.ToString(x["fbarcode"]), s = Convert.ToString(x["id"]) })
                                               .Distinct(x => x.b).ToDictionary(k => k.b, v => v.s);
            var barcodeEntities = dm.Select(bsMaps.Keys).OfType<DynamicObject>().ToList();

            if (barcodeEntities == null || barcodeEntities.Count <= 0)
            {
                throw new BusinessException("没有找到任何关联的条码主档!");
            }

            var dbIds = barcodeEntities.Select(x => Convert.ToString(x["id"])).ToList();
            var noExistIds = bsMaps.Keys.Where(x => false == dbIds.Contains(x)).ToList();

            if (noExistIds != null && noExistIds.Count > 0)
            {
                foreach (var id in noExistIds)
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"没有找到主键为{id}的条码主档!");
                }
                var removeScanIds = noExistIds.Select(x => bsMaps[x]).ToList();
                scanResultEntities = scanResultEntities.Where(x => removeScanIds.Contains(Convert.ToString(x["id"]))).ToList();
            }

            foreach (var barCodeEntity in barcodeEntities)
            {
                var id = Convert.ToString(barCodeEntity["id"]);
                var scanData = scanDatas.FirstOrDefault(x => Convert.ToString(x["scanEntryId"]) == bsMaps[id]);

                var ftraceentities = barCodeEntity["ftraceentity"] as DynamicObjectCollection;
                var ftraceentity = ftraceentities.Last();
                var stockId = scanData.GetString("stockId");
                var stockPlaceId = scanData.GetString("stockPlaceId");
                //var stockStatusId = scanData.GetString("stockStatusId");

                if (ftraceentity != null)
                {
                    ftraceentity["fstorehouseid"] = stockId;
                    ftraceentity["fstorelocationid"] = stockPlaceId;
                    //ftraceentity["fstockstatus"] = stockStatusId;
                }

                barCodeEntity["fstorehouseid"] = stockId;
                barCodeEntity["fstorelocationid"] = stockPlaceId;
                //barCodeEntity["fstockstatus"] = stockStatusId;
            }

            dm.Save(barcodeEntities);
        }

        /// <summary>
        /// 获取扫描记录
        /// </summary>
        /// <param name="scanDatas"></param>
        /// <returns></returns>
        private List<DynamicObject> getScanResult(List<Dictionary<string, string>> scanDatas)
        {
            var metaModelService = this.Container.GetService<IMetaModelService>();
            var scanResultHtmlForm = metaModelService.LoadFormModel(this.Context, "bcm_scanresult");
            var dm = this.Container.GetService<IDataManager>();

            dm.InitDbContext(this.Context, scanResultHtmlForm.GetDynamicObjectType(this.Context));
            var scanResultIds = scanDatas.Select(x => Convert.ToString(x["scanEntryId"])).Distinct().ToList();
            var results = dm.Select(scanResultIds).OfType<DynamicObject>().ToList();

            if (results == null || results.Count <= 0)
            {
                throw new BusinessException("没有找到任何关联的扫描记录!");
            }

            var dbIds = results.Select(x => Convert.ToString(x["id"])).ToList();
            var noExistIds = scanResultIds.Where(x => dbIds.Contains(x) == false).ToList();

            if (noExistIds != null && noExistIds.Count > 0)
            {
                foreach(var id in noExistIds)
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"没有找到主键为{id}的扫描记录!");
                }
            }

            return results;
        }

        private void checkArg(List<Dictionary<string,string>> scanDatas)
        {
            if (scanDatas == null || scanDatas.Count <= 0)
            {
                throw new BusinessException("请传送scanData参数！");
            }

            var i = 1;
            foreach(var scanData in scanDatas)
            {
                var scanEntryId = scanData.GetString("scanEntryId");
                var stockId = scanData.GetString("stockId");
                var stockPlaceId = scanData.GetString("stockPlaceId");
                var stockStatusId = scanData.GetString("stockStatusId");

                if (string.IsNullOrWhiteSpace(scanEntryId))
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"第{i}个参数的扫描记录主键不能为空!");
                }

                if (string.IsNullOrWhiteSpace(stockId))
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"第{i}个参数的仓库内码不能为空!");
                }

                //if (string.IsNullOrWhiteSpace(stockPlaceId))
                //{
                //    this.Result.ComplexMessage.ErrorMessages.Add($"第{i}个参数的仓位内码不能为空!");
                //}

                if (string.IsNullOrWhiteSpace(stockStatusId))
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"第{i}个参数的库存状态内码不能为空!");
                }

                i++;
            }
        }
    }
}
