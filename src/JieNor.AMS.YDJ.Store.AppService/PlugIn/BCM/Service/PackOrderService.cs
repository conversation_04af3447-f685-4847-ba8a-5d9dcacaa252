using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.Service
{
    [InjectService]
    public class PackOrderService : IPackOrderService
    {
        public Dictionary<string, object> CreatePackage(UserContext userContext, List<Dictionary<string, object>> packData, List<string> errorMsg)
        {
            var productEnableSerialNos = getProductEnableSerialNo(userContext, packData);

            //checkPackData(packData, productEnableSerialNos, errorMsg);

            //if (errorMsg.Count > 0)
            //{
            //    return null;
            //}

            var seqService = userContext.Container.GetService<ISequenceService>();
            var resultPackData = new List<Dictionary<string, object>>();
            var resultPackedData = new List<Dictionary<string, object>>();
            #region 一包多件
            //var onePackToManyPiecesgb = packData.Where(x => Convert.ToString(x["packType"]) == "3").GroupBy(g => Convert.ToString(g["materialid"])).ToList();
            //一包多件在原先按照物料分组的基础上，加上仓库+库位
            var onePackToManyPiecesgb = packData.Where(x => Convert.ToString(x["packType"]) == "3").GroupBy(f => new
            {
                materialid = Convert.ToString(f["materialid"]).Trim(),
                stockId = Convert.ToString(f["stockId"]).Trim(),
                stockplaceId = Convert.ToString(f["stockplaceId"]).Trim(),
            }).ToList();
            foreach (var onePackToManyPieces in onePackToManyPiecesgb)
            {
                if (onePackToManyPieces.Any())
                {
                    //求出最小包
                    var minPack = -1;
                    foreach (var item in onePackToManyPieces)
                    {
                        var qty = Convert.ToDecimal(item["qty"]);
                        var packCount = Convert.ToDecimal(item["packCount"]);
                        var pack = (int)(qty / packCount);
                        minPack = minPack > pack || minPack < 0 ? pack : minPack;
                    }
                    //根据最小包求出真实打包数据
                    foreach (var item in onePackToManyPieces)
                    {
                        resultPackData.Add(new Dictionary<string, object>
                    {
                        { "sourceFormId",item["sourceFormId"]},
                        { "sourceInterId",item["sourceInterId"] },
                        { "sourceEntryId",item["sourceEntryId"]},
                        { "qty",minPack*Convert.ToDecimal(item["packCount"])}
                    });
                    }
                    //生成条码

                    for (var i = 0; i < minPack; i++)
                    {
                        //一包多件一行明细一个批次，BUG#34595
                        //var packGroup = $"3-{minPack}-{seqService.GetSequence<string>()}";
                        //int packno = 1;
                        foreach (var item in onePackToManyPieces)
                        {
                            //一包多件一行明细一个批次，BUG#34595
                            var packGroup = $"3-{minPack}-{seqService.GetSequence<string>()}";
                            int packno = 1;

                            var sourceFormId = Convert.ToString(item["sourceFormId"]);
                            var sourceInterId = Convert.ToString(item["sourceInterId"]);
                            var sourceEntryId = Convert.ToString(item["sourceEntryId"]);
                            var sourceseq = Convert.ToString(item["sourceseq"]);
                            var tjno = "";
                            var pjno = "";
                            var sfno = "";
                            if (item.ContainsKey("tjno"))
                                tjno = Convert.ToString(item["tjno"]);
                            if (item.ContainsKey("pjno"))
                                pjno = Convert.ToString(item["pjno"]);
                            if (item.ContainsKey("sfno"))
                                sfno = Convert.ToString(item["sfno"]);
                            var productInfo = productEnableSerialNos.FirstOrDefault(x => x["fsourceformid"] == sourceFormId &&
                                                                                         x["fsourceinterid"] == sourceInterId &&
                                                                                         x["fsourceentryid"] == sourceEntryId);
                            var packCount = Convert.ToDecimal(item["packCount"]);
                            var enableSerialNo = productInfo == null ? false : productInfo["fenableserialno"] == "1";

                            if (enableSerialNo)
                            {
                                for (var j = 0; j < packCount; j++)
                                {
                                    var serialNo = seqService.GetSequence<string>();
                                    resultPackedData.Add(new Dictionary<string, object>
                                {
                                    { "sourceFormId",sourceFormId},
                                    { "sourceInterId",sourceInterId },
                                    { "sourceEntryId",sourceEntryId},
                                    { "qty",1},
                                    { "packGroup",packGroup},
                                    { "serialNo",serialNo},
                                    { "stockUnitId",productInfo?["fstockunitid"]},
                                    { "allpacknum",1},
                                    { "packno",packno},
                                    { "tjno",tjno },
                                    { "pjno",pjno },
                                    { "sfno",sfno },
                                    { "packtype",item["packType"] },
                                    { "sourceseq",sourceseq},
                                    { "storehouseid", Convert.ToString(item["stockId"])},
                                    { "storelocationid", Convert.ToString(item["stockplaceId"])},
                                    { "entrynote", Convert.ToString(item["entrynote"])}
                                });
                                    packno++;
                                }
                            }
                            else
                            {
                                resultPackedData.Add(new Dictionary<string, object>
                            {
                                { "sourceFormId",sourceFormId},
                                { "sourceInterId",sourceInterId },
                                { "sourceEntryId",sourceEntryId},
                                { "qty",packCount},
                                { "packGroup",packGroup},
                                { "serialNo",string.Empty},
                                { "stockUnitId",productInfo?["fstockunitid"]},
                                { "allpacknum",1},
                                { "packno",packno},
                                { "tjno",tjno },
                                { "pjno",pjno },
                                { "sfno",sfno },
                                { "packtype",item["packType"] },
                                { "sourceseq",sourceseq} ,
                                { "storehouseid", Convert.ToString(item["stockId"])},
                                { "storelocationid", Convert.ToString(item["stockplaceId"])},
                                { "entrynote", Convert.ToString(item["entrynote"])}
                            });
                                packno++;
                            }
                        }
                    }
                }

            }
            #endregion

            #region 一件多包
            var onePieceToManyPacksgb = packData.Where(x => Convert.ToString(x["packType"]) == "2" || Convert.ToString(x["packType"]) == "1").GroupBy(g => Convert.ToString(g["materialid"])).ToList().ToList();
            foreach (var onePieceToManyPacks in onePieceToManyPacksgb)
            {
                if (onePieceToManyPacks.Any())
                {
                    foreach (var item in onePieceToManyPacks)
                    {
                        //求出包数
                        var sourceFormId = Convert.ToString(item["sourceFormId"]);
                        var sourceInterId = Convert.ToString(item["sourceInterId"]);
                        var sourceEntryId = Convert.ToString(item["sourceEntryId"]);
                        var sourceseq = Convert.ToString(item["sourceseq"]);
                        var tjno = "";
                        var pjno = "";
                        var sfno = "";
                        if (item.ContainsKey("tjno"))
                            tjno = Convert.ToString(item["tjno"]);
                        if (item.ContainsKey("pjno"))
                            pjno = Convert.ToString(item["pjno"]);
                        if (item.ContainsKey("sfno"))
                            sfno = Convert.ToString(item["sfno"]);
                        var productInfo = productEnableSerialNos.FirstOrDefault(x => x["fsourceformid"] == sourceFormId &&
                                                                                     x["fsourceinterid"] == sourceInterId &&
                                                                                     x["fsourceentryid"] == sourceEntryId);
                        var enableSerialNo = productInfo == null ? false : productInfo["fenableserialno"] == "1";
                        var serialNo = enableSerialNo ? seqService.GetSequence<string>() : string.Empty;
                        var qty = Convert.ToDecimal(item["qty"]);
                        var packCount = Convert.ToDecimal(item["packCount"]);
                        resultPackData.Add(new Dictionary<string, object>
                    {
                        { "sourceFormId",sourceFormId},
                        { "sourceInterId",sourceInterId },
                        { "sourceEntryId",sourceEntryId},
                        { "qty",qty}
                    });

                        for (var i = 0; i < qty; i++)
                        {
                            var packGroup = $"2-{packCount}-{seqService.GetSequence<string>()}";
                            for (var j = 0; j < packCount; j++)
                            {
                                resultPackedData.Add(new Dictionary<string, object>
                            {
                                { "sourceFormId",sourceFormId},
                                { "sourceInterId",sourceInterId },
                                { "sourceEntryId",sourceEntryId},
                                { "qty",1},
                                { "packGroup",packGroup},
                                { "serialNo",serialNo },
                                { "stockUnitId",productInfo?["fstockunitid"]},
                                { "allpacknum",packCount},
                                {"tjno",tjno },
                                {"pjno",pjno },
                                {"sfno",sfno },
                                { "packno",j+1},
                                {"packtype",item["packType"] },
                                { "sourceseq",sourceseq},
                                { "storehouseid", Convert.ToString(item["stockId"])},
                                { "storelocationid", Convert.ToString(item["stockplaceId"])},
                                { "entrynote", Convert.ToString(item["entrynote"])}
                            });
                            }
                        }
                    }
                }

            }
            #endregion

            return new Dictionary<string, object>
            {
                { "packData",resultPackData},
                { "packedData",resultPackedData}
            };
        }

        public void PushBySanResult(UserContext userContext, List<DynamicObject> scanResultEntities, List<string> errorMsg)
        {
            var groupSourceFormInfos = scanResultEntities.GroupBy(x => Convert.ToString(x["fsourceformid"]));
            var convertService = userContext.Container.GetService<IConvertService>();
            var metaModelService = userContext.Container.GetService<IMetaModelService>();

            foreach (var groupSourceFormInfo in groupSourceFormInfos)
            {
                var targetFormId = string.Empty;
                var ruleId = string.Empty;
                var htmlForm = metaModelService.LoadFormModel(userContext, groupSourceFormInfo.Key);
                var sourceIds = groupSourceFormInfo.Select(x => Convert.ToString(x["fsourceinterid"])).Distinct().ToList();
                var dm = userContext.Container.GetService<IDataManager>();
                dm.InitDbContext(userContext, htmlForm.GetDynamicObjectType(userContext));
                var sourceEntities = dm.Select(sourceIds).OfType<DynamicObject>().ToList();
                var gateway = userContext.Container.GetService<IHttpServiceInvoker>();

                var response = gateway.InvokeBillOperation(userContext, htmlForm.Id, sourceEntities, "audit", new Dictionary<string, object>());
                if (response == null || response.IsSuccess == false || response.ComplexMessage.ErrorMessages.Count > 0)
                {
                    if (response != null && response.ComplexMessage.ErrorMessages.Count > 0)
                    {
                        errorMsg.AddRange(response.ComplexMessage.ErrorMessages);
                    }
                    continue;
                }

                switch (htmlForm.Id)
                {
                    case "pur_receiptnotice":
                        ruleId = "pur_receiptnotice2stk_postockin";
                        targetFormId = "stk_postockin";
                        break;
                    case "stk_otherstockinreq":
                        ruleId = "stk_otherstockinreq2stk_otherstockin";
                        targetFormId = "stk_otherstockin";
                        break;
                    case "sal_returnnotice":
                        ruleId = "sal_returnnotice2stk_sostockreturn";
                        targetFormId = "stk_sostockreturn";
                        break;
                    default:
                        continue;
                }

                var option = OperateOption.Create();
                option.SetVariableValue("IsScanTaskCall", "true");
                var pushResult = convertService.Push(userContext, new BillConvertContext()
                {
                    RuleId = ruleId,
                    SourceFormId = htmlForm.Id,
                    TargetFormId = targetFormId,
                    SelectedRows = groupSourceFormInfo.Select(x => Convert.ToString(x["fsourceinterid"])).Distinct().Select(x => new SelectedRow
                    {
                        PkValue = x,
                    }).ToConvertSelectedRows(),
                    Option = option
                });
                var convertResult = pushResult.SrvData as ConvertResult;
                var targetDatas = convertResult?.TargetDataObjects?.ToList();
                if (targetDatas == null || targetDatas.Count <= 0)
                {
                    errorMsg.Add($"{htmlForm.Caption}下推失败!");
                    continue;
                }
                response = gateway.InvokeBillOperation(userContext, targetFormId, targetDatas, "save", new Dictionary<string, object>());
                if (response == null || response.IsSuccess == false || response.ComplexMessage.ErrorMessages.Count > 0)
                {
                    if (response != null && response.ComplexMessage.ErrorMessages.Count > 0)
                    {
                        errorMsg.AddRange(response.ComplexMessage.ErrorMessages);
                    }
                    continue;
                }
                writeBackSourceBill(userContext, targetDatas, htmlForm);
            }
        }

        private void writeBackSourceBill(UserContext userContext, List<DynamicObject> targetDatas, HtmlForm htmlForm)
        {
            var numbers = targetDatas.Select(x => Convert.ToString(x["fsourcenumber"])).Distinct().ToList();
            var dm = userContext.Container.GetService<IDataManager>();

            dm.InitDbContext(userContext, htmlForm.GetDynamicObjectType(userContext));
            var dataReader = userContext.GetPkIdDataReaderWithNumber(htmlForm, numbers);
            var dataEntities = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();

            foreach (var dataEntity in dataEntities)
            {
                dataEntity["fscanstatus"] = "3";
            }

            dm.Save(dataEntities);
        }

        private void checkPackData(List<Dictionary<string, object>> packDatas, List<Dictionary<string, string>> productEnableSerialNos, List<string> errorMsg)
        {
            var productInfos = productEnableSerialNos.Where(x => x["fenableserialno"] == "1").ToList();
            if (productInfos == null || productInfos.Count <= 0)
            {
                return;
            }
            foreach (var productInfo in productInfos)
            {
                var fsourceformid = productInfo["fsourceformid"];
                var fsourceinterid = productInfo["fsourceinterid"];
                var fproductname = productInfo["fproductname"];
                var pDatas = packDatas.Where(x => Convert.ToString(x["sourceFormId"]) == fsourceformid && Convert.ToString(x["sourceInterId"]) == fsourceinterid).ToList();
                if (pDatas == null || pDatas.Count <= 0)
                {
                    continue;
                }
                foreach (var pData in pDatas)
                {
                    if (string.IsNullOrWhiteSpace(Convert.ToString(pData["stockId"])))
                    {
                        errorMsg.Add($"商品[{fproductname}]所在行的仓库不能为空!");
                    }
                    if (string.IsNullOrWhiteSpace(Convert.ToString(pData["stockStatusId"])))
                    {
                        errorMsg.Add($"商品[{fproductname}]所在行的库存状态不能为空!");
                    }
                }
            }
        }

        private List<Dictionary<string, string>> getProductEnableSerialNo(UserContext userContext, List<Dictionary<string, object>> packData)
        {

            var sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid",System.Data.DbType.String,userContext.Company)
            };

            var dbService = userContext.Container.GetService<IDBService>();


            var sqls = new List<string>();
            var metaModelService = userContext.Container.GetService<IMetaModelService>();
            List<string> sourceInterId = new List<string>();
            List<string> sourceEntryId = new List<string>();
            var sourceFormId = "";
            var BillHeadTableName = "";
            var TableName = "";

            var htmlForm = metaModelService.LoadFormModel(userContext, Convert.ToString(packData[0]["sourceFormId"]));
            sourceFormId = Convert.ToString(packData[0]["sourceFormId"]);
            var entryName = "fentity";
            if (sourceFormId == "bcm_receptionscantask")//收货扫描任务特殊处理
            {
                entryName = "ftaskentity";
            }
            var htmlEntry = htmlForm.GetEntryEntity(entryName);
            BillHeadTableName = htmlForm.BillHeadTableName;
            TableName = htmlEntry.TableName;
            packData.ForEach((x, i) =>
            {
                sourceInterId.Add(Convert.ToString(x["sourceInterId"]));
                sourceEntryId.Add(Convert.ToString(x["sourceEntryId"]));
            });

            using (var tran = userContext.CreateTransaction())
            {
                var tmpTablesourceInterId = dbService.CreateTempTableWithDataList(userContext, sourceInterId,false);
                var tmpTablesourceEntryId = dbService.CreateTempTableWithDataList(userContext, sourceEntryId,false);

                var sqlFormat = string.Format(@"
                                select '{0}' as fsourceformid,t.fid as fsourceinterid,e.fentryid as fsourceentryid,m.fenableserialno,m.fstockunitid,m.fname as fproductname
                                from {1} t
                                inner join {2} e on e.fid=t.fid
                                inner join t_bd_material m on e.fmaterialid=m.fid and t.fmainorgid=m.fmainorgid
                                inner join {4} tmp1 on t.fid =tmp1.fid 
                                inner join {5} tmp2 on e.fentryid =tmp2.fid 
                                where t.fmainorgid='{3}'
                                ", sourceFormId, BillHeadTableName, TableName, userContext.Company, tmpTablesourceInterId, tmpTablesourceEntryId);

                var result = new List<Dictionary<string, string>>();
                using (var dataReader = dbService.ExecuteReader(userContext, sqlFormat))
                {
                    while (dataReader.Read())
                    {
                        result.Add(new Dictionary<string, string>
                    {
                        { "fsourceformid",dataReader.GetValueToString("fsourceformid")},
                        { "fsourceinterid",dataReader.GetValueToString("fsourceinterid")},
                        { "fsourceentryid",dataReader.GetValueToString("fsourceentryid")},
                        { "fenableserialno",dataReader.GetValueToString("fenableserialno")},
                        { "fstockunitid",dataReader.GetValueToString("fstockunitid")},
                        { "fproductname",dataReader.GetValueToString("fproductname")}
                    });
                    }
                }

                tran.Complete();

                dbService.DeleteTempTableByName(userContext, tmpTablesourceInterId, true);
                dbService.DeleteTempTableByName(userContext, tmpTablesourceEntryId, true);

                return result;
            }
        }
    }
}
