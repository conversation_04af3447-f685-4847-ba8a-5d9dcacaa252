using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BCM
{
    /// <summary>
    /// 扫描任务通用保存（用于处理因复制行引起的交易流水号问题）
    /// </summary>
    [InjectService]
    [FormId("bcm_receptionscantask|bcm_deliveryscantask|bcm_countscantask|bcm_transfertask|bcm_transferintask")]
    [OperationNo("Save")]
    public class CommonSave : AbstractOperationServicePlugIn
    {

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any())
            {
                return;
            }

            //统一处理商品体积数据
            var allMatIds = e.DataEntitys.SelectMany(t => t["ftaskentity"] as DynamicObjectCollection).Select(p => Convert.ToString(p["fmaterialid"])).Distinct().ToList();
            //加载所有商品的即时库存数据
            List<DynamicObject> invObjs = null;
            var dbService = this.Context.Container.GetService<IDBService>();
            using (var tran = Context.CreateTransaction())
            {
                //用临时表关联查询
                var tempTable = dbService.CreateTempTableWithDataList(this.Context, allMatIds, false);
                invObjs = this.Context.LoadBizDataByFilter("stk_inventorylist", $"fmaterialid in (select fid from {tempTable})", false);

                tran.Complete();

                //清理临时表
                dbService.DeleteTempTableByName(this.Context, tempTable, true);
            }

            var productObjList = invObjs.Select(t => new
            {
                fmaterialid = t["fmaterialid"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(t["fmaterialid"]).Trim(),
                fattrinfo = t["fattrinfo"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(t["fattrinfo"]).Trim(),
                fcustomdesc = t["fcustomdesc"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(t["fcustomdesc"]).Trim(),
                funitid = t["funitid"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(t["funitid"]).Trim(),
                fsinglevolume = t["fsinglevolume"].IsNullOrEmptyOrWhiteSpace() ? 0M : Convert.ToDecimal(t["fsinglevolume"]),
                fvolumeunit = t["fvolumeunit"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(t["fvolumeunit"]).Trim()
            }).ToList();

            //加载所有商品档案
            var allMaterialObjs = this.Context.LoadBizDataById("ydj_product", allMatIds);

            foreach (var data in e.DataEntitys)
            {
                var entrys = data["ftaskentity"] as DynamicObjectCollection;
                foreach (var ent in entrys)
                {
                    var singleVol = ent["fsinglevolume"].IsNullOrEmptyOrWhiteSpace() ? 0M : Convert.ToDecimal(ent["fsinglevolume"]);
                    if (singleVol > 0)
                    {
                        //不为0的不更新
                        continue;
                    }
                    var currMatId = ent["fmaterialid"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(ent["fmaterialid"]).Trim();
                    var currAttrInfo = ent["fattrinfo"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(ent["fattrinfo"]).Trim();
                    var currCusDesc = ent["fcustomdesc"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(ent["fcustomdesc"]).Trim();
                    var currUnitid = ent["fbizunitid"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(ent["fbizunitid"]).Trim();
                    var filterObj = productObjList.Where(t =>
                      currMatId.EqualsIgnoreCase(t.fmaterialid)
                      && currAttrInfo.EqualsIgnoreCase(t.fattrinfo)
                      && currCusDesc.EqualsIgnoreCase(t.fcustomdesc)
                      && currUnitid.EqualsIgnoreCase(t.funitid)
                    ).OrderByDescending(p => p.fsinglevolume).FirstOrDefault();
                    var saveSingleVol = 0M;
                    var saveVolUnit = "";
                    if (filterObj != null)
                    {
                        saveSingleVol = filterObj.fsinglevolume;
                        saveVolUnit = filterObj.fvolumeunit;
                    }
                    if (saveSingleVol == 0)
                    {
                        //如果没取到有效值，则使用商品档案上的体积
                        var currMatObj = allMaterialObjs.Find(t => Convert.ToString(t["id"]).EqualsIgnoreCase(currMatId));
                        saveSingleVol = currMatObj["fvolume"].IsNullOrEmptyOrWhiteSpace() ? 0M : Convert.ToDecimal(currMatObj["fvolume"]);
                        saveVolUnit = "M3";
                    }

                    var bizQty = Convert.ToDecimal(ent["fqty"]);

                    ent["fsinglevolume"] = saveSingleVol;
                    ent["ftotalvolume"] = saveSingleVol * bizQty;
                    ent["fvolumeunit"] = saveVolUnit;
                }
            }
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            //反写关联单据已生成PDA扫描任务=是
            var updateSqls = new List<string>();
            foreach (var item in e.DataEntitys)
            {
                var scanEntrys = item["ftaskentity"] as DynamicObjectCollection;
                if (scanEntrys == null) continue;

                var updateTableName = string.Empty;
                var updateField = "fcreatescantask";
                var groups = scanEntrys?.GroupBy(f => new { linkformid = Convert.ToString(f["flinkformid"]), linkbillno = Convert.ToString(f["flinkbillno"]) })
                        .Select(x => new UpdateSourceInfo { linkformid = x.Key.linkformid, linkbillno = x.Key.linkbillno }).ToList(); ;

                foreach (var group in groups)
                {
                    if (group.linkformid.IsNullOrEmptyOrWhiteSpace() || group.linkbillno.IsNullOrEmptyOrWhiteSpace())
                    {
                        continue;
                    }

                    switch (group.linkformid)
                    {
                        case "stk_sostockout"://销售出库单
                            updateTableName = "t_stk_sostockout";
                            break;
                        case "stk_otherstockout"://其它出库单
                            updateTableName = "t_stk_otherstockout";
                            break;
                        case "stk_postockreturn"://采购退货单
                            updateTableName = "t_stk_postockreturn";
                            break;
                        case "stk_otherstockin"://其他入库单
                            updateTableName = "t_stk_otherstockin";
                            break;
                        case "stk_sostockreturn"://销售退货单
                            updateTableName = "t_stk_sostockreturn";
                            break;
                        case "stk_inventoryverify"://盘点单
                            updateTableName = "t_stk_invverify";
                            break;
                        case "stk_inventorytransfer"://库存调拨单
                            updateTableName = "t_stk_invtransfer";
                            //如果是调出扫描任务，反写fcreateoutscantask，如果是调入扫描任务，反写fcreateinscantask
                            if (this.HtmlForm.Id == "bcm_transfertask")
                            {
                                updateField = "fcreateoutscantask";
                            }
                            else if (this.HtmlForm.Id == "bcm_transferintask")
                            {
                                updateField = "fcreateinscantask";
                            }
                            break;
                        default:
                            //其余类型单据无需反写
                            continue;
                            break;
                    }

                    updateSqls.Add($"update {updateTableName} set {updateField}='1' where fbillno='{group.linkbillno}' and fmainorgid='{item["fmainorgid"]}';");
                }
            }
            if (updateSqls.Any())
            {
                var dbServiceEx = this.Context.Container.GetService<IDBServiceEx>();
                dbServiceEx.ExecuteBatch(this.Context, updateSqls);
            }
        }

        private class UpdateSourceInfo
        {
            public string linkformid { get; set; }
            public string linkbillno { get; set; }
        }
    }
}
