using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.AMS.YDJ.Core;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auth
{
    /// <summary>
    /// 【仓库】数据权限隔离
    /// </summary>
    [InjectService]
    [FormId("ydj_storehouse")]
    public class StockDataRule : IDataQueryRule
    {


        /// <summary>
        /// 依据库存参数中的控制单据，确定仓库可选范围
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        public IEnumerable<FilterRowObject> GetDataRowACLFilter(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            var filter = new List<FilterRowObject>();
            //// 
            //if (rulePara==null || rulePara.SrcFormId.IsNullOrEmptyOrWhiteSpace () || rulePara.SrcPara ==null || !rulePara.SrcPara.ContainsKey("deptid") )
            //{ 
            //    return filter;
            //}

            //var deptId = rulePara.SrcPara["deptid"];
            //if(deptId .IsNullOrEmptyOrWhiteSpace ())
            //{
            //    return filter;
            //}

            //var ctrlFormObj = ctx.Container.GetService<ISystemProfile>().GetSystemParameter(ctx, "stk_stockparam");
            //var paraMeta = HtmlParser.LoadFormMetaFromCache("sec_adminaccess", ctx);
            //var formRows = ctrlFormObj["fctrlbillentity"] as DynamicObjectCollection;
            //if(formRows==null || formRows.Count ==0)
            //{
            //    return filter;
            //}

            //var selRow = formRows.FirstOrDefault(f => Convert.ToString(f["fctrlformid"]).EqualsIgnoreCase(rulePara.SrcFormId) 
            //                                        && Convert.ToBoolean(f["fisstockctrl"]));
            //if (selRow == null  )
            //{
            //    return filter;
            //}

            //var sql = @"select fstorehouseid from t_bd_department where fid='{0}' ".Fmt(deptId);
            //var storeIds = ctx.Container.GetService<IDBService>().ExecuteDynamicObject(ctx, sql);
            //if (storeIds == null || storeIds.Count == 0)
            //{
            //    return filter;
            //}

            //var ids = Convert.ToString(storeIds[0]["fstorehouseid"]);   
            //if(ids.IsNullOrEmptyOrWhiteSpace () )
            //{
            //    ids = "@#$";
            //}
            //filter.Add(new FilterRowObject()
            //{
            //    Id = "fid",
            //    Operator = "in",
            //    Value = ids,
            //    Memo = "/*部门上定义的仓库*/",
            //});

            return filter;
        }



    }
}