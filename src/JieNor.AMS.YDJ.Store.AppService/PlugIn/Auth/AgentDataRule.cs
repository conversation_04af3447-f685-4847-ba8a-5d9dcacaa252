using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.AMS.YDJ.Core;
using JieNor.Framework.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auth
{

    /// <summary>
    /// 【经销商】数据权限隔离
    /// </summary>
    [InjectService]
    [FormId("bas_agent")]
    public class AgentDataRule : IDataQueryRule
    {
        /// <summary>
        ///   
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        public IEnumerable<FilterRowObject> GetDataRowACLFilter(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            var filter = new List<FilterRowObject>();

            //总部用户，可以看所有数据，不加额外的过滤
            if (ctx.IsTopOrg)
            {
                filter.Add(new FilterRowObject()
                {
                    Id = "ftopcompanyid",
                    Operator = "=",
                    Value = ctx.TopCompanyId,
                });
                return filter;
            }

            //if (rulePara.SrcFormId.EqualsIgnoreCase("ydj_service")|| rulePara.SrcFormId.EqualsIgnoreCase("ste_afterfeedback") || rulePara.SrcFormId.EqualsIgnoreCase("ydj_vist"))
            //{
            //    var Agents = new List<string> { ctx.Company };
            //    var entryAgents = ctx.Container.GetService<Core.Interface.IAgentService>().GetCurrEntryAgentIds(ctx);
            //    if (entryAgents != null && entryAgents.Any())
            //    {
            //        Agents.AddRange(entryAgents);
            //    }
            //    filter.Add(new FilterRowObject()
            //    {
            //        Id = "fid",
            //        Operator = "in",
            //        Value = string.Join(",", Agents),
            //    });
            //}
            if (rulePara.SrcFormId.EqualsIgnoreCase("stk_inventorytransfer"))
                return filter;
            //如果是 总部费用池查询，经销商视角也可以查看全部经销商。
            else if (!rulePara.SrcFormId.EqualsIgnoreCase("rpt_costpool") && !rulePara.SrcFormId.EqualsIgnoreCase("rpt_costpoolmx")
               && !rulePara.SrcFormId.EqualsIgnoreCase("rpt_costpool_filter") && !rulePara.SrcFormId.EqualsIgnoreCase("rpt_costpoolmx_filter")
               && !rulePara.SrcFormId.EqualsIgnoreCase("rpt_deliverlist") && !rulePara.SrcFormId.EqualsIgnoreCase("rpt_deliverbalance"))
            {
                filter.Add(new FilterRowObject()
                {
                    Id = "fmainorgid",
                    Operator = "in",
                    Value = "{0}".Fmt(ctx.Company),
                });
            }
            else
            {
                //按 城市  获取当前用户对应的所有经销商组织
                var AgentInfos = new ProductDataIsolateHelper().GetCurrentUserAgentInfosByActualOwner(ctx);
                var Agents = AgentInfos.Select(o => o.Id).ToList();
                filter.Add(new FilterRowObject()
                {
                    Id = "fid",
                    Operator = "in",
                    Value = string.Join(",", Agents),
                });
            }
            return filter;
        }


    }
}