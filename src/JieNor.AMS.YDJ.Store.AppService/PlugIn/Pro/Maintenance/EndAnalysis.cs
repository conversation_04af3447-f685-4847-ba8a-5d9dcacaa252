using JieNor.AMS.YDJ.Store.AppService.Service;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pro.Maintenance
{
    /// <summary>
	/// 运维问题：结束分析
	/// </summary>
	[InjectService]
    [FormId("ydj_maintenance")]
    [OperationNo("endanalysis")]
    public class EndAnalysis : AbstractOperationServicePlugIn
    {


        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fquestionprogress"]).EqualsIgnoreCase("q_progress_02") && !Convert.ToString(newData["fprimaryanalysis"]).IsNullOrEmptyOrWhiteSpace())
                {
                    return true;
                }
                return false;
                
            }).WithMessage("对不起，只有问题进度=运维分析中且运维初步分析字段有值，才允许操作!"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fmaintenanceperson"]).IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("对不起，运维人员不能为空!"));
        }
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            e.DataEntitys[0]["fquestionprogress"] = "q_progress_03";
            e.DataEntitys[0]["fendanalysistime"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            var res = this.Gateway.InvokeBillOperation(this.Context, "ydj_maintenance", e.DataEntitys, "save", new Dictionary<string, object>() { });

            res.ThrowIfHasError();

            //var dm = this.GetDataManager();
            //dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(Context));
            //var prepareSerivce = this.Container.GetService<IPrepareSaveDataService>();
            //prepareSerivce.PrepareDataEntity(this.Context, this.HtmlForm, e.DataEntitys, OperateOption.Create());
            //dm.Save(e.DataEntitys);

            //刷新页面
            this.AddRefreshPageAction();
        }
    }
}
