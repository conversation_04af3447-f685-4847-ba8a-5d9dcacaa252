using JieNor.AMS.YDJ.Store.AppService.Service;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Store.AppService.Utils;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pro.Maintenance
{
    /// <summary>
	/// 运维问题：待观察
	/// </summary>
	[InjectService]
    [FormId("ydj_maintenance")]
    [OperationNo("tobeobserved")]
    public class ToBeObserved : AbstractOperationServicePlugIn
    {


        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fquestionprogress"]).EqualsIgnoreCase("q_progress_08"))
                {
                    return false;
                }
                return true;
            }).WithMessage("对不起，只问题进度≠待观察，才允许操作!"));

        }
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            var currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            e.DataEntitys[0]["fquestionprogress"] = "q_progress_08";
            e.DataEntitys[0]["fcloseid"] = "";
            e.DataEntitys[0]["fclosestatus"] = "0";
            e.DataEntitys[0]["fclosedate"] = null;
            e.DataEntitys[0]["fquestionclosetime"] = null;

            var res = this.Gateway.InvokeBillOperation(this.Context, "ydj_maintenance", e.DataEntitys, "save", new Dictionary<string, object>() { });

            res.ThrowIfHasError();

            //var dm = this.GetDataManager();
            //dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(Context));
            //var prepareSerivce = this.Container.GetService<IPrepareSaveDataService>();
            //prepareSerivce.PrepareDataEntity(this.Context, this.HtmlForm, e.DataEntitys, OperateOption.Create());
            //dm.Save(e.DataEntitys);


            //运维人员
            var maintenancePerson = Convert.ToString(e.DataEntitys[0]["fmaintenanceperson"]).Trim();

            var zenpathnumber = e.DataEntitys[0]["fzenpathnumber"];

            if (!zenpathnumber.IsNullOrEmptyOrWhiteSpace())
            {
                var dataBase = this.GetAppConfig("ms.zt.database"); //所属数据库
                if (dataBase.IsNullOrEmptyOrWhiteSpace())
                {
                    throw new BusinessException("对不起，未配置运维问题所属禅道数据库！请检查 host.config 配置 ms.zt.database！");
                }

                //更新禅道任务
                string updateMySql = string.Format($@"UPDATE {dataBase}.zt_task SET status='closed',lastEditedDate='{currentTime}' where id='{zenpathnumber}'");
                var init = MySqlHelper.Instance;
                MySqlHelper.ExecuteNonQuery(updateMySql);


                //更新禅道任务备注（业务待观察）
                var msg = "业务待观察";
                var insertMySql = string.Format($@"insert into  {dataBase}.zt_action(`objectType`,`objectID`,`project`,`execution`,`actor`,`action`,`date`,`comment`)
                select 'task',(select id from {dataBase}.zt_task where id='{zenpathnumber}'),(select project from {dataBase}.zt_task where id='{zenpathnumber}'),
                       (select execution from {dataBase}.zt_task where id='{zenpathnumber}'),'{maintenancePerson}','closed','{currentTime}','{msg}'
                ");
                MySqlHelper.ExecuteNonQuery(insertMySql);
            }

            //刷新页面
            this.AddRefreshPageAction();
        }
    }
}

