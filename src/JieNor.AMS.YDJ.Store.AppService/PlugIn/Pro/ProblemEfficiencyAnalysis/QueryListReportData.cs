using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pro.ProblemEfficiencyAnalysis
{
    [InjectService]
    [FormId("ydj_problemefficiencyanalysis")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {
        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            //目前运维问题只有总部视角下才允许操作，因此相关报表也只有在总部视角下才进行统计
            if (!this.Context.IsTopOrg)
            {
                return;
                //throw new BusinessException($"对不起，只有总部视角下才允许操作！");
            }

            this.ProfitListData();
        }

        /// <summary>
        /// 查询数据后往报表对应的临时表中插入数据
        /// </summary>
        protected void ProfitListData()
        {
            StringBuilder sbWhereSql = new StringBuilder();

            var sqlParam = new List<SqlParam>
            {
              new SqlParam("@fmainorgid",System.Data.DbType.String, this.Context.Company)
            };

            var sql = $@"select fquestionprogress,count(fquestionprogress) as ftotalqty,
                        sum((case when fpriority = '1'  then 1 else 0 end)) as fqty_o,
                        sum((case when fpriority = '1' and fdate >1  then 1 else 0 end)) as fdelayqty_o,
                        COALESCE((case when sum((case when fpriority = '1' and fdate is not null then 1 else 0 end))>0 
                        then  ROUND(sum((case when fpriority = '1' and fdate is not null then fdate else 0 end))/sum((case when fpriority = '1' and fdate is not null then 1 else 0 end)) , 2)
                        else 0 end), 0) as faveresolutedate_o, 
                        sum((case when fpriority = '2'  then 1 else 0 end)) as fqty_t,
                        sum((case when fpriority = '2' and fdate >3  then 1 else 0 end)) as fdelayqty_t,
                        COALESCE((case when sum((case when fpriority = '2' and fdate is not null then 1 else 0 end))>0 
                        then ROUND(sum((case when fpriority = '2' and fdate is not null then fdate else 0 end))/sum((case when fpriority = '2' and fdate is not null then 1 else 0 end)) , 2)
                        else 0 end), 0) as faveresolutedate_t, 
                        sum((case when fpriority = '3'  then 1 else 0 end)) as fqty_h,
                        sum((case when fpriority = '3' and fdate >7  then 1 else 0 end)) as fdelayqty_h,
                        COALESCE((case when sum((case when fpriority = '3' and fdate is not null then 1 else 0 end))>0 
                        then ROUND(sum((case when fpriority = '3' and fdate is not null  then fdate else 0 end))/sum((case when fpriority = '3' and fdate is not null then 1 else 0 end)) , 2)
                        else 0 end), 0) as faveresolutedate_h
                        from (select case 
                               when fquestionprogress = 'q_progress_01' then 'q_progress_02' 
	                           when fquestionprogress = 'q_progress_03' then 'q_progress_02' 
	                            when fquestionprogress = 'q_progress_09' then 'q_progress_05' 
	                           else fquestionprogress 
	                           end as fquestionprogress,fpriority,DATEDIFF(MINUTE, fcreatedate,factsettletime)*1.0/1440 as fdate,fmainorgid
                               from t_ydj_maintenance) t
                        where  fmainorgid=@fmainorgid
                        group by fquestionprogress ";

            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();

            var insertSql = $@"/*dialect*/ INSERT INTO {this.DataSourceTableName} (fid, fquestionprogress)
                                            SELECT '7', 'q_progress_02'
                                            UNION ALL
                                            SELECT '6', 'q_progress_04'
                                            UNION ALL
                                            SELECT '5', 'q_progress_05'
                                            UNION ALL
                                            SELECT '4', 'q_progress_06'
                                            UNION ALL
                                            SELECT '3', 'q_progress_10'
                                            UNION ALL
                                            SELECT '2', 'q_progress_07'
                                            UNION ALL
                                            SELECT '1', 'q_progress_08'";

            dbServiceExt.Execute(this.Context, insertSql);

            var updateSql = $@"/*dialect*/ update t0 set
                                           t0.ftotalqty = t1.ftotalqty,
                                           t0.fqty_o = t1.fqty_o,
                                           t0.fdelayqty_o = t1.fdelayqty_o,
                                           t0.faveresolutedate_o = t1.faveresolutedate_o,
                                           t0.fqty_t = t1.fqty_t,
                                           t0.fdelayqty_t = t1.fdelayqty_t,
                                           t0.faveresolutedate_t = t1.faveresolutedate_t,
                                           t0.fqty_h = t1.fqty_h,
                                           t0.fdelayqty_h = t1.fdelayqty_h,
                                           t0.faveresolutedate_h = t1.faveresolutedate_h
                                           from {this.DataSourceTableName} t0
                                           inner join (select * from ({sql}) rr) t1 on t1.fquestionprogress=t0.fquestionprogress";

            dbServiceExt.Execute(this.Context, updateSql, sqlParam);
        }
    }
}
