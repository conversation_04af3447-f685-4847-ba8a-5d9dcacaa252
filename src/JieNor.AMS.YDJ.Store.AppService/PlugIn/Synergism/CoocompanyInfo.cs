using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject.IncomeDisburse;
using JieNor.AMS.YDJ.DataTransferObject.Poco;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Synergism
{
    internal static class CoocompanyInfo
    {
        internal static DynamicObject IsExistCooRelation(UserContext ctx, string companyId, string coocompanyId, string type, string status)
        {
            if (ctx == null || string.IsNullOrWhiteSpace(companyId) || string.IsNullOrWhiteSpace(coocompanyId) || string.IsNullOrWhiteSpace(type))
                throw new BusinessException("查询企业协同关系请求参数错误！");

            var dm = ctx.Container.GetService<IDataManager>();
            var formMeta = ctx.Container.TryGetService<IMetaModelService>()?.LoadFormModel(ctx, "coo_company");
            dm.InitDbContext(ctx, formMeta.GetDynamicObjectType(ctx));
            string whereSql = string.Format("fmainorgid=@mainorgid and fmycompanyId=@myCompId and fcompanyId=@cooCompId and fservicetype=@servType {0}",
                  string.IsNullOrWhiteSpace(status) ? "" : string.Format("and fcoostatus = N'{0}'", status));
            var pkIdReader = ctx.GetPkIdDataReader(formMeta, whereSql,
                new SqlParam[]
                {
                    new SqlParam("@mainorgid", System.Data.DbType.String,ctx.Company),
                    new SqlParam("@myCompId", System.Data.DbType.String,companyId),
                    new SqlParam("@cooCompId", System.Data.DbType.String,coocompanyId),
                    new SqlParam("@servType", System.Data.DbType.String,type)
                });
            DynamicObject dyObj = dm.SelectBy(pkIdReader).OfType<DynamicObject>().FirstOrDefault();
            return dyObj;
        }

        /// <summary>
        /// 生成协同账户明细
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="customerForm"></param>
        /// <param name="customer"></param>
        internal static IEnumerable<AccountInfo> BuildCustomerSynAccountEntry(UserContext userCtx, HtmlForm customerForm, DynamicObject customer)
        {
            //读取 账户设置（经销商）设置信息
            var accountSynService = userCtx.Container.GetService<ISynAccountBalanceService>();
            var allAccountInfo = accountSynService.GetAllAccount(userCtx);

            var htmlEntry = customerForm.GetEntryEntity("fentry");
            var synEntrys = htmlEntry.DynamicProperty.GetValue<DynamicObjectCollection>(customer);
            var existEntrys = synEntrys.ToArray();
            synEntrys.Clear();
            var seq = 1;
            foreach (var accountInfo in allAccountInfo)
            {
                var existEntry = existEntrys.FirstOrDefault(o => accountInfo.AccountId.EqualsIgnoreCase(o["fpurpose"] as string));
                if (existEntry == null)
                {
                    existEntry = htmlEntry.DynamicObjectType.CreateInstance() as DynamicObject;
                    existEntry["fpurpose"] = accountInfo.AccountId;
                    existEntry["fisbalance"] = accountInfo.NoConfirmInPay;
                    existEntry["fispayment"] = accountInfo.CanUseInOrderPay;
                    existEntry["fiscash"] = accountInfo.IsCash;
                    existEntry["fcredit"] = accountInfo.CreditLimit;
                }
                existEntry["fseq"] = seq++;
                synEntrys.Add(existEntry);

                //反向设置一下账户余额信息
                accountInfo.Balance = Convert.ToDecimal(existEntry["fbalance_e"]);
            }

            //生成主键ID
            var pkService = userCtx.Container.GetService<IDataEntityPkService>();
            pkService.AutoSetPrimaryKey(userCtx, customer, customerForm.GetDynamicObjectType(userCtx));

            return allAccountInfo;
        }

        /// <summary>
        /// 生成供应商协同账户明细
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="supplierForm"></param>
        /// <param name="supplier"></param>
        internal static IEnumerable<AccountInfo> BuildSupplierSynAccountEntry(UserContext userCtx, HtmlForm supplierForm, DynamicObject supplier)
        {
            //读取 账户设置（经销商）设置信息
            var accountSynService = userCtx.Container.GetService<ISynAccountBalanceService>();
            var allAccountInfo = accountSynService.GetAllAccount(userCtx);

            var htmlEntry = supplierForm.GetEntryEntity("fentry");
            var synEntrys = htmlEntry.DynamicProperty.GetValue<DynamicObjectCollection>(supplier);
            var existEntrys = synEntrys.ToArray();
            synEntrys.Clear();
            var seq = 1;
            foreach (var accountInfo in allAccountInfo)
            {
                var existEntry = existEntrys.FirstOrDefault(o => accountInfo.AccountId.EqualsIgnoreCase(o["fpurpose"] as string));
                if (existEntry == null)
                {
                    existEntry = htmlEntry.DynamicObjectType.CreateInstance() as DynamicObject;
                    existEntry["fpurpose"] = accountInfo.AccountId;
                }
                //existEntry["fisbalance"] = accountInfo.NoConfirmInPay;
                //existEntry["fispayment"] = accountInfo.CanUserInOrderPay;
                //existEntry["fcredit"] = accountInfo.CreditLimit;
                existEntry["fseq"] = seq++;
                synEntrys.Add(existEntry);

                //反向设置一下账户余额信息
                accountInfo.Balance = Convert.ToDecimal(existEntry["fbalance_e"]);
            }

            //生成主键ID
            var pkService = userCtx.Container.GetService<IDataEntityPkService>();
            pkService.AutoSetPrimaryKey(userCtx, supplier, supplierForm.GetDynamicObjectType(userCtx));

            return allAccountInfo;
        }

        /// <summary>
        /// 创建客户或供应商的初始化余额调整收支明细记录
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="customerOrSupplierForm"></param>
        /// <param name="customerOrSupplierObj"></param>
        /// <param name="allAccountInfo"></param>
        /// <param name="eisRelationId"></param>
        internal static void CreateSynAccountAdjustRecord(UserContext userCtx, HtmlForm customerOrSupplierForm, DynamicObject customerOrSupplierObj, IEnumerable<AccountInfo> allAccountInfo, string eisRelationId = "")
        {
            //todo:先根据当前主体查出未确认的初始化金额
            var strNoConfirmAmountSql = @"
select faccount,fdirection,famount
from t_coo_incomedisburse 
where fbizstatus='bizstatus_01' and fpurpose='bizpurpose_07'
";
            switch (customerOrSupplierForm.Id.ToLower())
            {
                case "ydj_supplier":
                    strNoConfirmAmountSql += " and fsupplierid=@fid";
                    break;
                case "ydj_customer":
                    strNoConfirmAmountSql += " and fcustomerid=@fid";
                    break;
                default:
                    strNoConfirmAmountSql += " and 1=0";
                    break;
            }

            var lstSqlParas = new SqlParam[]
            {
                new SqlParam("fid", System.Data.DbType.String, customerOrSupplierObj["id"])
            };

            var allAdjustRecordObjs = userCtx.Container.GetService<IDBService>()
                .ExecuteDynamicObject(userCtx, strNoConfirmAmountSql, lstSqlParas);

            var htmlEntry = customerOrSupplierForm.GetEntryEntity("fentry");
            var synEntrys = htmlEntry.DynamicProperty.GetValue<DynamicObjectCollection>(customerOrSupplierObj);

            var existEntrys = synEntrys.ToArray();

            IncomeDisburseRecord idrIncomeRecord = new IncomeDisburseRecord(), idrDisburseRecord = new IncomeDisburseRecord();
            //收入
            idrIncomeRecord.Direction = "direction_01";
            //支出
            idrDisburseRecord.Direction = "direction_02";
            //初始化调整
            idrIncomeRecord.AutoConfirm = idrDisburseRecord.AutoConfirm = true;
            idrIncomeRecord.SceneType = idrDisburseRecord.SceneType = "bizpurpose_07";
            idrIncomeRecord.LinkFormId = idrDisburseRecord.LinkFormId = "coo_company";
            idrIncomeRecord.LinkBillId = idrDisburseRecord.LinkBillId = eisRelationId;
            idrIncomeRecord.TranFormId = idrDisburseRecord.TranFormId = customerOrSupplierForm.Id;
            idrIncomeRecord.TranBillId = idrDisburseRecord.TranBillId = customerOrSupplierObj["id"] as string;

            foreach (var accountInfo in allAccountInfo)
            {
                var existRecordObjs = allAdjustRecordObjs
                    .Where(o => Convert.ToString(o["faccount"]).EqualsIgnoreCase(accountInfo.AccountId))
                    .ToArray();

                decimal dAccountIncome = existRecordObjs.Where(o => Convert.ToString(o["fdirection"]).EqualsIgnoreCase("direction_01"))
                    .Sum(o => Convert.ToDecimal(o["famount"]));
                decimal dAccountDisburse = existRecordObjs.Where(o => Convert.ToString(o["fdirection"]).EqualsIgnoreCase("direction_02"))
                    .Sum(o => Convert.ToDecimal(o["famount"]));

                var existEntry = existEntrys.FirstOrDefault(o => accountInfo.AccountId.EqualsIgnoreCase(o["fpurpose"] as string));
                if (existEntry == null)
                {
                    existEntry = htmlEntry.DynamicObjectType.CreateInstance() as DynamicObject;
                    existEntry["fpurpose"] = accountInfo.AccountId;
                    existEntry["fseq"] = synEntrys.Count;
                    synEntrys.Add(existEntry);
                }
                //existEntry["fisbalance"] = accountInfo.NoConfirmInPay;
                //existEntry["fispayment"] = accountInfo.CanUserInOrderPay;
                //existEntry["fcredit"] = accountInfo.CreditLimit;
                var diffAmount = MathUtil.Round(Convert.ToDecimal(existEntry["fbalance_e"]) + dAccountIncome - dAccountDisburse - accountInfo.Balance, 2, RoundMode.AwayFromZero);

                if (diffAmount > 0)
                {
                    idrDisburseRecord.AccountSettleInfo.Add(new AccountSettleInfo()
                    {
                        AccountId = accountInfo.AccountId,
                        SettleType = "payway_09",
                        Amount = diffAmount,
                    });
                }
                if (diffAmount < 0)
                {
                    idrIncomeRecord.AccountSettleInfo.Add(new AccountSettleInfo()
                    {
                        AccountId = accountInfo.AccountId,
                        SettleType = "payway_09",
                        Amount = Math.Abs(diffAmount)
                    });
                }
            }

            //生成主键ID
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, customerOrSupplierForm.GetDynamicObjectType(userCtx));
            var pkService = userCtx.Container.GetService<IDataEntityPkService>();
            pkService.AutoSetPrimaryKey(userCtx, customerOrSupplierObj, customerOrSupplierForm.GetDynamicObjectType(userCtx));
            dm.Save(customerOrSupplierObj);

            var syncAccountService = userCtx.Container.GetService<ISynAccountBalanceService>();
            if (idrIncomeRecord.AccountSettleInfo.Any())
            {
                var syncResult = syncAccountService.CreateIncomeDisburseRecord(userCtx, idrIncomeRecord, OperateOption.Create());
                syncResult.ThrowIfHasError(true, "收支调整记录生成失败！");
            }
            if (idrDisburseRecord.AccountSettleInfo.Any())
            {
                var syncResult = syncAccountService.CreateIncomeDisburseRecord(userCtx, idrDisburseRecord, OperateOption.Create());
                syncResult.ThrowIfHasError(true, "收支调整记录生成失败！");
            }
        }

        /// <summary>
        /// 检查客户或供应商是否存在未确认的收支纪录 
        /// </summary>
        /// <param name="customerOrSupplierForm"></param>
        /// <param name="customerOrSupplierId"></param>
        /// <param name="notThrowError"></param>
        internal static bool CheckIncomeDisburseRecord(UserContext userCtx, HtmlForm customerOrSupplierForm, string customerOrSupplierId, bool notThrowError = false)
        {
            var strCheckSql = "";
            var tranFormId = customerOrSupplierForm.Id.ToLower();
            switch (tranFormId)
            {
                case "ydj_customer":
                    strCheckSql = $@"
select top 1 1 
from t_coo_incomedisburse 
where fbizstatus='bizstatus_01' and fmainorgid='{userCtx.Company}'
    and fcustomerid='{customerOrSupplierId}'
    and fpurpose<>'bizpurpose_07'";
                    break;
                case "ydj_supplier":
                    strCheckSql = $@"
select top 1 1 
from t_coo_incomedisburse 
where fbizstatus='bizstatus_01' and fmainorgid='{userCtx.Company}'
    and fsupplierid='{customerOrSupplierId}'
    and fpurpose<>'bizpurpose_07'";
                    break;
            }


            if (strCheckSql.IsNullOrEmptyOrWhiteSpace()) return true;
            var dbService = userCtx.Container.GetService<IDBService>();
            using (var idrItems = dbService.ExecuteReader(userCtx, strCheckSql))
            {
                if (idrItems.Read())
                {
                    if (notThrowError) return false;
                    throw new BusinessException($"{customerOrSupplierForm.Caption}方资料存在关联的未确认收支纪录，不允许解除协同，请确认完所有交易后再解除协同！");
                }
            }
            return true;
        }

        /// <summary>
        /// 批量检查供应商或客户是否存在未确认的收支纪录 
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="customerOrSupplierForm"></param>
        /// <param name="allCustomerOrSupplierIds"></param>
        /// <returns></returns>
        internal static Dictionary<string, bool> CheckIncomeDisburseRecord(UserContext userCtx, HtmlForm customerOrSupplierForm, IEnumerable<string> allCustomerOrSupplierIds)
        {
            if (allCustomerOrSupplierIds.IsEmpty()) return new Dictionary<string, bool>();

            Dictionary<string, bool> dctRet = new Dictionary<string, bool>();
            if (allCustomerOrSupplierIds.IsLessThan(2))
            {
                var isPass = CheckIncomeDisburseRecord(userCtx, customerOrSupplierForm, allCustomerOrSupplierIds.First(), true);
                dctRet[allCustomerOrSupplierIds.First()] = isPass;
                return dctRet;
            }

            using (var tran = userCtx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                var dbService = userCtx.Container.GetService<IDBService>();
                var tmpTable = dbService.CreateTempTableWithDataList(userCtx, allCustomerOrSupplierIds,false);

                var strCheckSql = "";
                var tranFormId = customerOrSupplierForm.Id.ToLower();
                switch (tranFormId)
                {
                    case "ydj_customer":
                        strCheckSql = $@"
                                select t0.fcustomerid fid,count(1) fcount
                                from t_coo_incomedisburse t0
                                inner join {tmpTable} temp on t0.fcustomerid=temp.fid
                                where fbizstatus='bizstatus_01' and fmainorgid='{userCtx.Company}' and fpurpose<>'bizpurpose_07'
                                group by t0.fcustomerid ";
                        break;
                    case "ydj_supplier":
                        strCheckSql = $@"
                                select t0.fsupplierid fid,count(1) fcount
                                from t_coo_incomedisburse t0
                                inner join {tmpTable} temp on t0.fsupplierid=temp.fid
                                where fbizstatus='bizstatus_01' and fmainorgid='{userCtx.Company}' and fpurpose<>'bizpurpose_07'
                                group by t0.fsupplierid";
                        break;
                }


                if (strCheckSql.IsNullOrEmptyOrWhiteSpace())
                {
                    tran.Complete();
                    return dctRet;
                }

                dctRet = allCustomerOrSupplierIds.Distinct().ToDictionary(o => o, o => true);
                using (var idrItems = dbService.ExecuteReader(userCtx, strCheckSql))
                {
                    while (idrItems.Read())
                    {
                        dctRet[idrItems["fid"] as string] = Convert.ToDecimal(idrItems["fcount"]) <= 0;
                    }
                }

                tran.Complete();

                dbService.DeleteTempTableByName(userCtx, tmpTable, true);

                return dctRet;
            }
        }
    }
}
