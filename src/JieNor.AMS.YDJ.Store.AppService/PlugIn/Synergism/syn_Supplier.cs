using JieNor.Framework.Interface;
using JieNor.Framework;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using System.IO;
using Newtonsoft.Json;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Synergism
{
    #region 获取已协同供应商
    [InjectService]
    [FormId("ydj_supplier")]
    [OperationNo("getSynSupplier")]
    public class GetSynSupplier : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            List<Dictionary<string, string>> list = new List<Dictionary<string, string>>();
            using (var reader = this.DBService.ExecuteReader(this.Context, $"select fid, fcoocompanyid, fname from t_ydj_supplier where fcoocompanyid !=' ' and fmainorgid='{this.Context.Company}' and fcoostate=N'已协同'"))
            {
                while (reader.Read())
                {
                    Dictionary<string, string> model = new Dictionary<string, string>();
                    model.Add("fid", reader["fcoocompanyid"] as string);
                    model.Add("id", reader["fid"] as string);
                    model.Add("fname", reader["fname"] as string);
                    list.Add(model);
                }
            }
            this.Result.SrvData = list;
        }
    }
    #endregion

    #region 协同供应商(临时关联)
    [InjectService]
    [FormId("ydj_supplier")]
    [OperationNo("synSupplier")]
    public class SynSupplier : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            var opmode = this.GetQueryOrSimpleParam<string>("opmode", "");
            string Suid = GetQueryOrSimpleParam<string>("customerid");//当前表单Id
            string Coid = GetQueryOrSimpleParam<string>("companyid");//选中企业Id
            string productId = GetQueryOrSimpleParam<string>("productid"); //选中企业的产品id
            if (productId.IsNullOrEmptyOrWhiteSpace())
            {
                productId = this.Context.Product;
            }
            if (Suid.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("请先保存供应商资料后再发送协同邀请！");
            }
            if (Coid.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("请选中一个需要协同的企业后再发送协同邀请！");
            }
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            DynamicObject dyObj = dm.Select(Suid) as DynamicObject;
            if (dyObj == null)
            {
                throw new BusinessException("供应商资料可能已被删除，请刷新后再操作！");
            }

            //检查本地客户是否满足协同条件
            CoocompanyInfo.CheckIncomeDisburseRecord(this.Context, this.HtmlForm, Suid);

            string thisCompany = this.Context.Company;
            var cooExistCompany = CoocompanyInfo.IsExistCooRelation(this.Context, thisCompany, Coid, "供应商", "");//"已协同"
            if (cooExistCompany != null) throw new BusinessException("已有本地供应商与该企业建立协同关系！");

            var gateWay = this.Container.GetService<IHttpServiceInvoker>();
            #region 请求企业是否符合协同的条件
            DynamicDTOWrapper dtoBill = new CommonBillDTO()
            {
                FormId = "eis_company",
                OperationNo = "getcompanydetail",
                SimpleData = new Dictionary<string, string>() { { "id", Coid }, { "company", thisCompany }, { "type", "供应商" } }
            };
            var resp = gateWay.Invoke(this.Context, TargetSEP.EisService, dtoBill);
            List<Dictionary<string, string>> result = new List<Dictionary<string, string>>();
            if (resp is DynamicDTOResponse)
            {
                var Srv = (resp as DynamicDTOResponse).OperationResult.SrvData as string;
                result = Srv?.FromJson<List<Dictionary<string, string>>>() ?? new List<Dictionary<string, string>>();
            }
            if (result.Any() && !result[0]["fentryid"].IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("已有本地供应商与该企业建立过关联关系");
            }
            #endregion

            Dictionary<string, string> _result = result.FirstOrDefault();
            #region 同步转发
            var thisCompanyObj = this.Context.Companys.FirstOrDefault(o => o.CompanyId.EqualsIgnoreCase(this.Context.Company));
            Dictionary<string, string> ms = new Dictionary<string, string>();
            ms.Add("fcompanyid", Coid);
            ms.Add("fproductid", productId);
            ms.Add("fcompanytype", "供应商");
            ms.Add("fsupplierid", Suid);
            ms.Add("fcompanyname", _result?["fname"] ?? "");
            ms.Add("fcontactphone", dyObj["fphone"] as string);
            ms.Add("fcontactname", dyObj["fcontacts"] as string);
            ms.Add("freceivestatus", "待处理");
            ms.Add("foperationmode", opmode);


            var ToEIS = new CommonBillDTO()
              .SetSimpleData("companyId", thisCompany)
              .SetSimpleData("companyName", thisCompanyObj?.CompanyName)
              .SetSimpleData("companyNumber", thisCompanyObj?.CompanyNumber)
              .SetSimpleData("companyRelation", new object[] { ms }.ToJson())
              .SetFormId("syn_company")
              .SetOperationNo("savecompanyrelation");
            resp = gateWay.Invoke(this.Context, TargetSEP.EisService, ToEIS);
            List<Dictionary<string, string>> EISresult = null;
            if (resp is DynamicDTOResponse)
            {
                var Srv = (resp as DynamicDTOResponse).OperationResult.SimpleData["relationData"];
                EISresult = Srv?.FromJson<List<Dictionary<string, string>>>() ?? new List<Dictionary<string, string>>();
            }
            else
            {
                throw new BusinessException("远程服务器返回错误");
            }
            #endregion
            #region 反写供应商资料中的协同业务字段
            if (dyObj["fcoocompany"].IsNullOrEmptyOrWhiteSpace())
            {
                dyObj["fcoocompany"] = _result?["fname"] ?? "";
                dyObj["fcoocompanyid"] = Coid;
                dyObj["fcoostate"] = "待处理";
                dyObj["foperationmode"] = opmode;
                //目标企业产品标识
                dyObj["fcooproductid"] = productId;
                //dyObj["fissyn"] = true;
                dm.Save(dyObj);
            }
            #endregion

            #region 写入协同企业表
            var formMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "coo_company");
            dm.InitDbContext(this.Context, formMeta.GetDynamicObjectType(this.Context));
            var synCompanyIdReader = this.Context.GetPkIdDataReader(formMeta, $"fcompanyId=@targetCompany and fmycompanyId=@thisCompany and fservicetype=N'供应商'",
                new SqlParam[] {
                    new SqlParam("targetCompany", System.Data.DbType.String,Coid),
                    new SqlParam("thisCompany", System.Data.DbType.String,thisCompany),
                });
            var pre = this.Container.GetService<IPrepareSaveDataService>();
            DynamicObject dyO = dm.SelectBy(synCompanyIdReader)
                .OfType<DynamicObject>().OrderByDescending(o => o["fmodifydate"]).FirstOrDefault();

            if (dyO == null)
            {
                dyO = new DynamicObject(formMeta.GetDynamicObjectType(this.Context));
                dyO["fissponsor"] = true;
            }

            dyO["fservicetype"] = "供应商";
            dyO["fname"] = _result?["fname"] ?? "";//目标企业name
            dyO["farea"] = (_result?["fprovince_txt"] ?? "") + (_result?["fcity_txt"] ?? "") + (_result?["fregion_txt"] ?? "");
            dyO["fcompanyId"] = Coid;//目标企业id
            dyO["fmycompanyId"] = thisCompany;
            dyO["fccrelationid"] = EISresult.FirstOrDefault()?["fccrelationid"] as string ?? "";
            dyO["fcoostatus"] = "协同处理中";
            dyO["fiswaiting"] = true;
            dyO["fcustomerorsupplierId"] = Suid;
            //目标企业产品标识
            dyO["fproductid"] = productId;

            pre.PrepareDataEntity(this.Context, formMeta, new DynamicObject[] { dyO }, this.Option);
            dm.Save(dyO);
            #endregion

            this.Result.SimpleMessage = "协同邀请成功！";
        }
    }
    #endregion

    #region 批量协供应商（临时关联）
    [InjectService]
    [FormId("ydj_supplier")]
    [OperationNo("synSupplierBatch")]
    public class SynSupplierBatch : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            string param = this.GetQueryOrSimpleParam<string>("param");
            List<Dictionary<string, string>> list = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(param);
            if (list == null || list.Count <= 0)
            {
                throw new BusinessException("请至少选中一个还没有协同的供应商资料！");
            }

            //将待协同的所有资料进行检查校验，仅对通过检查的项进行继续协同逻辑处理，其它返回错误信息
            var lstValidCooItems = this.CheckValidCooSupplierItems(list);

            var dm = this.GetDataManager();
            var formMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "coo_company");
            dm.InitDbContext(this.Context, formMeta.GetDynamicObjectType(this.Context));

            var dm1 = this.GetDataManager();
            dm1.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            List<DynamicObject> Companylist = new List<DynamicObject>();
            List<DynamicObject> Customerlist = new List<DynamicObject>();
            List<Dictionary<string, string>> toClientData = new List<Dictionary<string, string>>();
            foreach (var item in lstValidCooItems)
            {
                var productId = Convert.ToString(item["productid"]);

                var synCompanyIdReader = this.Context.GetPkIdDataReader(formMeta,
                    $"fcompanyId=@targetCompany and fmycompanyId=@thisCompany and fservicetype=N'供应商'",
                    new SqlParam[] {
                        new SqlParam("targetCompany", System.Data.DbType.String,item["companyid"]),
                        new SqlParam("thisCompany", System.Data.DbType.String,this.Context.Company),
                    });
                DynamicObject dyo = dm.SelectBy(synCompanyIdReader)
                    .OfType<DynamicObject>().OrderByDescending(o => o["fmodifydate"]).FirstOrDefault();


                #region 批量插入本地数据
                if (dyo == null)
                {
                    dyo = new DynamicObject(formMeta.GetDynamicObjectType(this.Context));
                    dyo["fissponsor"] = true;
                }
                dyo["fservicetype"] = "供应商";
                dyo["fname"] = item["companyname"];
                dyo["farea"] = item["area"];
                dyo["fcompanyId"] = item["companyid"];
                dyo["fmycompanyId"] = this.Context.Company;
                dyo["fcoostatus"] = "协同处理中";
                dyo["fiswaiting"] = true;
                dyo["fcustomerorsupplierId"] = item["supplierid"];
                //目标企业产品标识
                dyo["fproductid"] = productId;
                Companylist.Add(dyo);
                #endregion

                #region 反写供应商资料
                DynamicObject dyc = dm1.Select(item["supplierid"]) as DynamicObject;
                dyc["fcoocompany"] = item["companyname"];
                dyc["fcoocompanyid"] = item["companyid"];
                //协同企业产品标识
                dyc["fcooproductid"] = productId;
                dyc["fcoostate"] = "待处理";
                dyc["foperationmode"] = item["opmode"];
                //dyc["fissyn"] = true;
                Customerlist.Add(dyc);
                #endregion

                #region 转发至EIS站点的数据数据包
                Dictionary<string, string> ms = new Dictionary<string, string>();
                ms.Add("fcompanyid", item["companyid"]);
                ms.Add("fproductid", productId);
                ms.Add("fcompanytype", "供应商");
                ms.Add("fsupplierid", item["supplierid"]);
                ms.Add("fsuppliername", item["suppliername"]);
                ms.Add("fcontactphone", item["supphone"]);
                ms.Add("fcontactname", item["supcontacts"]);
                ms.Add("fcompanyname", item["companyname"]);
                ms.Add("freceivestatus", "待处理");
                ms.Add("foperationmode", item["opmode"]);
                toClientData.Add(ms);
                #endregion

            }

            if (toClientData.Any() == false) return;

            #region 同步转发
            var thisCompanyObj = this.Context.Companys.FirstOrDefault(o => o.CompanyId.EqualsIgnoreCase(this.Context.Company));
            var ToEIS = new CommonBillDTO()
             .SetSimpleData(new Dictionary<string, string>() { { "product", this.Context.Product } })
             .SetSimpleData("companyId", this.Context.Company)
             .SetSimpleData("companyName", thisCompanyObj?.CompanyName)
             .SetSimpleData("companyNumber", thisCompanyObj?.CompanyNumber)
             .SetSimpleData("companyRelation", toClientData.ToJson())
             .SetFormId("syn_company")
             .SetOperationNo("savecompanyrelation");
            var gateWay = this.Container.GetService<IHttpServiceInvoker>();
            var resp = gateWay.Invoke(this.Context, TargetSEP.EisService, ToEIS);
            var opResult = (resp as DynamicDTOResponse)?.OperationResult;
            List<Dictionary<string, string>> EISresult = null;
            if (opResult?.IsSuccess == true)
            {
                var Srv = opResult.SimpleData?.GetValue("relationData", "");
                EISresult = Srv?.FromJson<List<Dictionary<string, string>>>() ?? new List<Dictionary<string, string>>();
            }
            else
            {
                opResult.ThrowIfHasError(true, "系统出现未知异常，请查阅系统日志！");
            }
            #endregion
            foreach (var item in EISresult)
            {
                var temp = Companylist.Where(t => Convert.ToString(t["fcompanyId"]) == item["fcompanyid"]).FirstOrDefault();
                if (temp != null)
                {
                    temp["fccrelationid"] = item["fccrelationid"];
                }
            }
            var seqSrv = this.Container.GetService<IDataEntityPkService>();
            seqSrv.AutoSetPrimaryKey(this.Context, Companylist, dm.DataEntityType);
            var pre = this.Container.GetService<IPrepareSaveDataService>();
            pre.PrepareDataEntity(this.Context, formMeta, Companylist.ToArray(), this.Option);
            dm.Save(Companylist);
            dm1.Save(Customerlist);
            this.Result.SimpleMessage = "协同邀请成功！";
        }


        private IEnumerable<Dictionary<string, string>> CheckValidCooSupplierItems(List<Dictionary<string, string>> list)
        {
            if (list.IsNullOrEmpty()) return new Dictionary<string, string>[] { };
            string thisCompany = this.Context.Company;

            List<Dictionary<string, string>> lstValidCooItems = new List<Dictionary<string, string>>();
            foreach (var invite in list)
            {
                string strCheckMsg = "";

                var cooExistCompany = CoocompanyInfo.IsExistCooRelation(this.Context, this.Context.Company, invite["companyid"], "供应商", "");
                if (cooExistCompany != null) strCheckMsg = $"{invite["suppliername"]}协同失败，已有本地供应商与把选的企业【{invite["companyname"]}】建立了协同关系！";
                if (Convert.ToString(invite["companyid"]).EqualsIgnoreCase(thisCompany))
                {
                    strCheckMsg = $"{invite["suppliername"]}协同失败，不允许与自已进行协同。";
                }

                if (strCheckMsg.IsNullOrEmptyOrWhiteSpace())
                {
                    lstValidCooItems.Add(invite);
                }
                else
                {
                    this.Result.IsSuccess = false;
                    this.Result.ComplexMessage.ErrorMessages.Add(strCheckMsg);
                }
            }

            var dctCheckResult = CoocompanyInfo.CheckIncomeDisburseRecord(this.Context, this.HtmlForm, lstValidCooItems.Select(o => o["supplierid"]).Distinct());
            for (int i = lstValidCooItems.Count - 1; i >= 0; i--)
            {
                var supplierId = lstValidCooItems[i]["supplierid"];
                bool isOk = false;
                dctCheckResult.TryGetValue(supplierId, out isOk);
                if (!isOk)
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"{lstValidCooItems[i]["suppliername"]}协同失败，不允许存在未确认的收支明细。");
                    lstValidCooItems.RemoveAt(i);                    
                }
            }

            return lstValidCooItems;
        }
    }
    #endregion

    #region 修改协同供应商
    [InjectService]
    [FormId("ydj_supplier")]
    [OperationNo("updSynSupplier")]
    public class updSyn_Supplier : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            string relationid = this.GetQueryOrSimpleParam<string>("relationid");//eis协同分录id
            string companyId = this.GetQueryOrSimpleParam<string>("companyid");//对方企业id
            string supplierid = this.GetQueryOrSimpleParam<string>("supplierid");//供应商id
            bool status = this.GetQueryOrSimpleParam<bool>("status");//true关联 false 取消关联
            string mycompanyId = this.Context.Company;
            var formMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "coo_company");
            var dm1 = this.GetDataManager();
            dm1.InitDbContext(this.Context, formMeta.GetDynamicObjectType(this.Context));
            var pkIdReader = this.Context.GetPkIdDataReader(formMeta, "fccrelationid=@relationid", new SqlParam[]
                {
                    new SqlParam("relationid", System.Data.DbType.String, relationid)
                });
            DynamicObject dyObj = dm1.SelectBy(pkIdReader).OfType<DynamicObject>().FirstOrDefault();
            if (dyObj != null)
            {
                var dm = this.GetDataManager();
                dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
                if (status)
                {
                    #region 修改本地供应商协同信息
                    DynamicObject dyO = dm.Select(supplierid) as DynamicObject;
                    if (dyO != null)
                    {

                        dyO["fcoocompany"] = dyObj["fname"];
                        dyO["fcoocompanyid"] = dyObj["fcompanyId"];
                        dyO["fcoostate"] = "已协同";
                        //dyO["fissyn"] = true;
                        dm.Save(dyO);
                    }
                    dyObj["fcoostatus"] = "已协同";
                    #endregion
                }
                else
                {
                    #region 修改本地客户协同信息
                    pkIdReader = this.Context.GetPkIdDataReader(this.HtmlForm, "fcoocompanyid=@companyId", new SqlParam[]
                    {
                        new SqlParam("companyId", System.Data.DbType.String, companyId)
                    });
                    DynamicObject dyO = dm.SelectBy(pkIdReader).OfType<DynamicObject>().FirstOrDefault();
                    if (dyO != null)
                    {
                        dyO["fcoostate"] = "";
                        dyO["fcoocompany"] = "";
                        dyO["fcoocompanyid"] = "";
                        //dyO["fissyn"] = false;
                        dm.Save(dyO);
                    }
                    dyObj["fcoostatus"] = "解除协同";
                    dyObj["fcustomerorsupplierId"] = "";
                    #endregion
                }

                dm1.Save(dyObj);
            }
        }
    }
    #endregion
}

