using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.Registfee
{
    /// <summary>
    /// 费用申请：删除
    /// </summary>
    [InjectService]
    [FormId("ste_registfee")]
    [OperationNo("delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            string errorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var settledAmount = Convert.ToDecimal(newData["fsettledamount"]);
                var unConfirmAmount = Convert.ToDecimal(newData["funconfirmamount"]);
                if (settledAmount > 0 || unConfirmAmount > 0)
                {
                    errorMessage = $"{this.HtmlForm.Caption}【{newData["fbillno"]}】存在已结算金额或待确认金额，不允许删除！";
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));
            
            e.Rules.Add(new Validation_Check(true));
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            //移到反审核反写
            //this.UpdateSourceBillInfo(e.DataEntitys);
        }

        /// <summary>
        /// 更新源单信息（销售合同）
        /// </summary>
        /// <param name="dataEntitys"></param>
        public void UpdateSourceBillInfo(DynamicObject[] dataEntitys)
        {
            if (dataEntitys == null || dataEntitys.Length <= 0) return;

            foreach (var dataEntity in dataEntitys)
            {
                var sourceType = Convert.ToString(dataEntity["fsourcetype"]);
                var sourceNumber = Convert.ToString(dataEntity["fsourcenumber"]);
                if (!sourceType.IsNullOrEmptyOrWhiteSpace() && !sourceNumber.IsNullOrEmptyOrWhiteSpace() && sourceType.EqualsIgnoreCase("ydj_order"))
                {
                    var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, sourceType);

                    var dm = this.Container.GetService<IDataManager>();
                    dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                    string where = $"fmainorgid=@fmainorgid and {htmlForm.NumberFldKey}=@fnumber";
                    var sqlParam = new SqlParam[]
                    {
                        new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
                        new SqlParam("fnumber", System.Data.DbType.String, sourceNumber.Trim())
                    };
                    var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);
                    var sourceBillData = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
                    if (sourceBillData != null)
                    {
                        sourceBillData["fbrokerage"] = 0;
                        dm.Save(sourceBillData);
                    }
                }
            }
        }
    }
}