using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Flow;
using JieNor.Framework;
using JieNor.Framework.AppService.OperationService.ApprovalFlow;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.Registfee
{
    /// <summary>
    /// 费用应付单：提交
    /// </summary>
    [InjectService]
    [FormId("ste_registfee")]
    [OperationNo("Submit")]
    public class Submit : AbstractOperationServicePlugIn
    {
       
    }
}