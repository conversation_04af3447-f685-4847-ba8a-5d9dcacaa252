using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Extensions;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.Registfee
{
    public class Validation_Check : AbstractBaseValidation
    {
        private bool isCheck = true;

        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        public Validation_Check(bool isCheck)
        {
            this.isCheck = isCheck;
        }

        /// <summary>
        /// 校验结果
        /// </summary>
        private ValidationResult Result { get; set; } = new ValidationResult();

        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }

        /// <summary>
        /// 校验逻辑处理单元
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities,
            OperateOption option, string operationNo)
        {
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return this.Result;
            }

            if (this.isCheck)
            {
                var fmainorgids = dataEntities.Select(x => Convert.ToString(x["fmainorgid"])).ToList();
                var agentInfo = GetAgentInfo(userCtx, fmainorgids);

                CheckRegistfeeData(userCtx, dataEntities, agentInfo, operationNo);
            }

            return this.Result;
        }

        /// <summary>
        /// 校验费用应付单数据
        /// 当【创建该单据的经销商.经营类型=直营】并且【来往单位类型=合作渠道】不允许操作该按钮
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        /// <param name="agentInfo"></param>
        /// <param name="operationNo"></param>
        private void CheckRegistfeeData(UserContext userCtx, IEnumerable<DynamicObject> dataEntities,
            Dictionary<string, bool> agentInfo, string operationNo)
        {
            foreach (var dataEntity in dataEntities)
            {
                var fbillno = Convert.ToString(dataEntity["fbillno"]);
                var fcollaborativesharestatus = Convert.ToString(dataEntity["fcollaborativesharestatus"]); //协同共享状态
                var frelatetype = Convert.ToString(dataEntity["frelatetype"]); //往来单位类型

                //创建单据的经销商的经销类型-直营
                var fmainorgid = Convert.ToString(dataEntity["fmainorgid"]);
                var isfmainorgid = agentInfo.GetValue(fmainorgid);

                //撤销/删除-当【创建该单据的经销商.经营类型=直营】并且【协同共享状态=已提交共享、已审核4.已付款】并且【来往单位类型=合作渠道】不允许操作该按钮。
                if ((operationNo == "delete" || operationNo == "unsubmit" || operationNo == "cancel")
                    && isfmainorgid &&
                    frelatetype.EqualsIgnoreCase("ste_channel") && (fcollaborativesharestatus == "0" ||
                                                                    fcollaborativesharestatus == "1" ||
                                                                    fcollaborativesharestatus == "3"))
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"操作失败，编号：{fbillno}【经销商经销类型】为【直营】且【来往单位类型】为【合作渠道】,不允许操作！",
                        DataEntity = dataEntity,
                    });
                }
                //审核-反审核----当【创建该单据的经销商.经营类型=直营】并且【来往单位类型=合作渠道】不允许操作该按钮
                else if (isfmainorgid && (operationNo == "audit" || operationNo == "unaudit") &&
                         frelatetype.EqualsIgnoreCase("ste_channel"))
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"操作失败，编号：{fbillno}【经销商经销类型】为【直营】且【来往单位类型】为【合作渠道】,不允许操作！",
                        DataEntity = dataEntity,
                    });
                }
            }
        }

        /// <summary>
        /// 获取当前经销商相关字段信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="fmainorgids"></param>
        /// <returns></returns>
        private Dictionary<string, bool> GetAgentInfo(UserContext userCtx, List<string> fmainorgids)
        {
            var dic = new Dictionary<string, bool>();
            if (fmainorgids.Count == 0) return dic;

            var agentInfo = userCtx.LoadBizBillHeadDataById("bas_agent", fmainorgids,
                "fid,fmainorgid,fname,fnumber,actualownernumber,fmanagemodel").ToList();

            foreach (var item in agentInfo)
            {
                if (!dic.ContainsKey(Convert.ToString(item["fid"])))
                {
                    var fmanagemodel = Convert.ToString(item["fmanagemodel"]) == "1" ? true : false;
                    dic.Add(Convert.ToString(item["fid"]), fmanagemodel);
                }
            }

            return dic;
        }
    }
}