using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.Registfee
{
    /// <summary>
    /// 费用申请：加载合同的应付佣金
    /// </summary>
    [InjectService]
    [FormId("ste_registfee")]
    [OperationNo("loadorderprice")]
    public class LoadOrderPrice : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            var fsourcetype = this.GetQueryOrSimpleParam<string>("fsourcetype");
            var fsourcenumber = this.GetQueryOrSimpleParam<string>("fsourcenumber");
            if (string.IsNullOrWhiteSpace(fsourcetype) || string.IsNullOrWhiteSpace(fsourcenumber))
            {
                throw new BusinessException("源单不能为空!");
            }

            var fplanbrokerage = 0M;
            switch (fsourcetype)
            {
                case "ydj_order":
                    var order = this.Context.LoadBizDataByFilter(fsourcetype, $" fbillno='{fsourcenumber}'").FirstOrDefault();
                    if (order != null)
                    {
                        fplanbrokerage = Convert.ToDecimal(order["fplanbrokerage"]);
                    }
                    break;
            }
            this.Result.SrvData = fplanbrokerage;
            this.Result.IsSuccess = true;
        }
    }
}