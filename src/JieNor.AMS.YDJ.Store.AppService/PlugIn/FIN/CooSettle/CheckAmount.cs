using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.CooSettle
{

    /// <summary>
    /// 检查结算金额
    /// </summary>
    [InjectService]
    [FormId("coo_settledyn")]
    [OperationNo("checkamount")]
    public class CheckAmount : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            this.Result.IsSuccess = true;

            var orderAmount = this.GetQueryOrSimpleParam("orderAmount", "");//源单合同【未收款】的绝对值
            var orderNo = this.GetQueryOrSimpleParam("orderNo", ""); //源单合同编号
            var amount = this.GetQueryOrSimpleParam("amount", "");  //当前输入的【结算金额】

            if (!amount.IsNullOrEmptyOrWhiteSpace() && !orderNo.IsNullOrEmptyOrWhiteSpace())
            {
                var sql = $@"/*dialect*/ select ABS(funreceived) AS funreceived from t_ydj_order with(nolock) where fbillno='{orderNo}'";
                var funreceived = this.DBService.ExecuteDynamicObject(this.Context, sql).FirstOrDefault();



                decimal orderAmounts = 0;
                decimal amounts = 0;
                decimal sumAmounts = 0;

                if (!funreceived.IsNullOrEmptyOrWhiteSpace() && !Convert.ToString(funreceived["funreceived"]).IsNullOrEmptyOrWhiteSpace())
                {
                    Decimal.TryParse(Convert.ToString(funreceived["funreceived"]), out orderAmounts);
                }
                if (orderAmounts == 0)
                {
                    return;
                }

                Decimal.TryParse(amount, out amounts);

                //需要加上校验逻辑：销售合同退款界面，点击<立即结算>时，需判断：当前输入的【结算金额】，
                //加上【源单编号】=“当前销售合同号”且【数据状态】≠“已审核”且【作废状态】=“否”的《收款退款单》的【结算金额】，
                //如果大于【未收款】，则需提示：“对不起，你输入的退款金额已超出可退金额，请检查是否有未审核的退款单！”

                sql = $@"/*dialect*/ select sum(famount) sumamount from t_coo_incomedisburse with(nolock) where fsourcenumber='{orderNo}' and fstatus!='E' and fcancelstatus!='1'";

                var sumAmount = this.DBService.ExecuteDynamicObject(this.Context, sql).FirstOrDefault();

                if (!sumAmount.IsNullOrEmptyOrWhiteSpace() && !Convert.ToString(sumAmount["sumamount"]).IsNullOrEmptyOrWhiteSpace())
                {
                    Decimal.TryParse(Convert.ToString(sumAmount["sumamount"]), out sumAmounts);
                }

                if (amounts + sumAmounts > orderAmounts)
                {
                    this.Result.IsSuccess = false;
                    throw new BusinessException("对不起，你输入的退款金额已超出可退金额，请检查是否有未审核的退款单！");
                }


            }
        }
    }
}
