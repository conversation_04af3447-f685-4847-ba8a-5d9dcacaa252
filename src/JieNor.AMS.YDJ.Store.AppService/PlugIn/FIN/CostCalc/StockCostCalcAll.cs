using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.CostCalc
{

    /// <summary>
    /// 成本计算-重算
    /// </summary>
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("calccostall")]
    public class StockCostCalcAll : AbstractOperationServicePlugIn
    {

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            ReCalculateCost();

            this.AddRefreshPageAction();
        }
        /// <summary>
        /// 重算所有组织的成本信息
        /// </summary>
        private void ReCalculateCost()
        {
            var IsTopOrg = this.Context.Company == this.Context.TopCompanyId;
            var svc = this.Container.GetService<Core.Interface.StockUpdate.ICostCalulateService>();
            if (!IsTopOrg)
            {
                //当前组织是经销商，则只更新当前经销商
                try
                {
                    this.Result = svc.CostCalcByEnd_All(this.Context, this.Option);
                }
                catch (Exception ex)
                {
                    this.Result.IsSuccess = false;
                    this.Result.SimpleMessage = "成本计算过程有错误发生";
                    this.Result.ComplexMessage.ErrorMessages.Add(ex.Message);
                }
            }
            else
            {
                var sql = $@"select org.fnumber,t.fmainorgid,(select count(0) from t_stk_inventorybalance_repeat as a with (nolock) where a.fmainorgid = t.fmainorgid) as cc
                            from inventorybalance_1021 as t
                            inner join t_bas_organization as org with (nolock) on org.fid = t.fmainorgid
                            where org.fnumber not in ('1003639','1004030')
                            group by org.fnumber,t.fmainorgid 
                            order by cc  ";
                var OrgNeedRecal = this.DBService.ExecuteDynamicObject(this.Context, sql).Select(o => Convert.ToString(o["fnumber"])).ToList<string>();
                if (!OrgNeedRecal.Any()) return;

                //异步调用方法
                Task task = Task.Run(() =>
                {
                    try
                    {
                        foreach (var orgNo in OrgNeedRecal)
                        {
                            //总部组织，则重算所有经销商的 (有问题的经销商才需要重算，所有经销商太慢了。)
                            var dbInfo = this.GetAllCompanys().Values.FirstOrDefault(f => f.CompanyNumber == orgNo);
                            if (dbInfo == null) continue;
                            this.Result.ComplexMessage.SuccessMessages.Add($"经销商{orgNo}：开始进行成本计算......");
                            var ctx = this.CreateContextByCompanyInfo(dbInfo);
                            this.Result = svc.CostCalcByEnd_All(ctx, this.Option);
                        }
                    }
                    catch (Exception ex)
                    {
                        this.Result.IsSuccess = false;
                        this.Result.SimpleMessage = "成本计算过程有错误发生";
                        this.Result.ComplexMessage.ErrorMessages.Add(ex.Message);
                    } 
                }); 
            }
        }
    }

}
