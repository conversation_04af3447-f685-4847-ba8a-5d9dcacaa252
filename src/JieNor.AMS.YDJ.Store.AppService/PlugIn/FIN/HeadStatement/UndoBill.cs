using System;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.HeadStatement
{
    /// <summary>
    /// 总部对账单：对账撤销
    /// </summary>
    [InjectService]
    [FormId("ydj_headstatement")]
    [OperationNo("UndoBill")]
    public class UndoBill : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            string errorMsg = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
                {
                    string billStatus = Convert.ToString(newData["fbillstatus"]);
                    if (!billStatus.EqualsIgnoreCase("1"))
                    {
                        string statusText = this.HtmlForm.GetSimpleSelectItemText("fbillstatus",
                            Convert.ToString(newData["fbillstatus"]));
                        errorMsg = $"【对账状态】={statusText}，不允许执行对账撤销！";
                        return false;
                    }

                    DateTime fconfirmdate = Convert.ToDateTime(newData["fconfirmdate"]);
                    // 只能撤销当月对账相符的对账单
                    if (fconfirmdate.Year == DateTime.Now.Year && fconfirmdate.Month == DateTime.Now.Month)
                    {
                        return true;
                    }

                    errorMsg = $"【确认时间】={fconfirmdate:yyyy-MM-dd}，不是当月确认的，不允许执行对账撤销！";
                    return false;

                }).WithMessage("{0}", (newData, oldData) => errorMsg));
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;

            var statementService = this.Container.GetService<IStatementService>();

            statementService.UndoBill(this.Context, this.HtmlForm, e.DataEntitys);

            this.AddRefreshPageAction();
        }
    }
}
