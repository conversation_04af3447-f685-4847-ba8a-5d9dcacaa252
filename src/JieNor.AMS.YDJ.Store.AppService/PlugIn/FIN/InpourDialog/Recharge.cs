using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.Interface.CloudChain;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.InpourDialog
{
    /// <summary>
    /// 客户或供应商：充值
    /// </summary>
    [InjectService]
    [FormId("coo_inpourdialog")]
    [OperationNo("Recharge")]
    public class Recharge : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 客户或供应商是否已建立协同关系
        /// </summary>
        public bool IsSyn { get; set; }

        public override void CreateObjectIdemotency(CreateObjectIdemotencyEventArgs e)
        {
            base.CreateObjectIdemotency(e);

            if (e.DataEntitys.IsNullOrEmpty()) return;

            e.IdemotencyIds = new List<string>();

            //源单主键ID
            string sourceId = this.GetQueryOrSimpleParam<string>("fsourceid");
            string sourceFormId = this.GetQueryOrSimpleParam<string>("fsourceformid");

            e.IdemotencyIds.Add(SecurityUtil.HashString($"{sourceId}|{sourceFormId}"));
        }

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fusagetype"]).NotEmpty().WithMessage("请选择要充值的账户！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var spService = this.Container.GetService<ISystemProfile>();
                var enableMustInputBankId = spService.GetSystemParameter(this.Context, "bas_storesysparam", "fenablemustinputbankid", true);

                if ((Convert.ToString(newData["fway"]).EqualsIgnoreCase("payway_06") ||
                    (enableMustInputBankId && Convert.ToString(newData["fway"]).EqualsIgnoreCase("payway_11")))
                    && newData["fmybankid"].IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("请选择银行账号！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var spService = this.Container.GetService<ISystemProfile>();
                var enableMustInputBankId = spService.GetSystemParameter(this.Context, "bas_storesysparam", "fenablemustinputbankid", true);

                if ((Convert.ToString(newData["fway"]).EqualsIgnoreCase("payway_06") ||
                    (enableMustInputBankId && Convert.ToString(newData["fway"]).EqualsIgnoreCase("payway_11")))
                    && Convert.ToBoolean(newData["fissyn"])
                    && newData["fsynbankid"].IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("请选择对方银行！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                decimal money = 0;
                if (!decimal.TryParse(Convert.ToString(newData["fmoney"]), out money) || money < 0.01M)
                {
                    return false;
                }
                return true;
            }).WithMessage("充值金额必须大于0！"));
            //支付方式为“商场代收”，“代收单位”不能为空
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fway = Convert.ToString(newData["fway"]);
                var fcontactunitid = Convert.ToString(newData["fcontactunitid"]);
                if (fway.EqualsIgnoreCase("payway_13") && fcontactunitid.IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("支付方式为“商场代收”，“代收单位”不能为空!"));
            var payways = new List<string>() { "payway_01", "payway_05" };
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!newData["fcontactunitid"].IsNullOrEmptyOrWhiteSpace() && newData["fimage"].IsNullOrEmptyOrWhiteSpace() && !payways.Contains(newData["fway"].ToString()))
                {
                    return false;
                }
                return true;
            }).WithMessage("选择了代收单位，必须上传凭证！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var profileService = this.Container.GetService<ISystemProfile>();
                var fhasvoucherwhenreceivable = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fhasvoucherwhenreceivable", false); //收款必须上传凭证
                if (fhasvoucherwhenreceivable && newData["fimage"].IsNullOrEmptyOrWhiteSpace() && !payways.Contains(newData["fway"].ToString()))
                {
                    return false;
                }
                return true;
            }).WithMessage("必须上传凭证！"));

            string receiptMsg = "【收款小票号】必填！";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var profileService = this.Container.GetService<ISystemProfile>();
                var mustinvoicenumber = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fmustinvoicenumber", false); //收款必填小票号
                var receiptNo = Convert.ToString(newData["freceiptno"]);
                var way = Convert.ToString(newData["fway"]);//支付方式
                if (receiptNo.IsNullOrEmptyOrWhiteSpace() && way != "payway_01")
                {
                    //收款小票号为空且支付方式不为：账户支付
                    if (mustinvoicenumber)
                    {
                        receiptMsg = "【收款小票号】必填！";
                        return false;
                    }
                }
                //else
                //{
                //    decimal money = Convert.ToDecimal(newData["fmoney"]);
                //    if (money > 0.01M)
                //    {
                //        var incomeObjs = this.Context.LoadBizDataByFilter("coo_incomedisburse", $" freceiptno='{receiptNo}' AND famount='{money}'");
                //        if (incomeObjs != null && incomeObjs.Any())
                //        {
                //            receiptMsg = "该笔收款金额的“收款小票号”录入重复，请检查！";
                //            return false;
                //        }
                //    }
                //}
                return true;
            }).WithMessage(receiptMsg));


            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                DateTime date = DateTime.MinValue;
                if (!DateTime.TryParse(Convert.ToString(newData["fdate"]), out date))
                {
                    return false;
                }
                return true;
            }).WithMessage("充值日期格式错误！"));
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var topUpInfo = e.DataEntitys[0];
            string fcontactunitid = Convert.ToString(topUpInfo["fcontactunitid"]);

            //源单主键ID
            string sourceId = this.GetQueryOrSimpleParam<string>("fsourceid");
            if (sourceId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException($"源单主键ID为空，请检查！");
            string sourceFormId = this.GetQueryOrSimpleParam<string>("fsourceformid");
            if (sourceFormId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException($"源单FormId为空，请检查！");

            //设置原流水号，加盟商自动充值关联原收支记录
            var oldTranId = this.GetQueryOrSimpleParam<string>("foldtranid");

            //与之关联的源单信息（客户 或 供应商）
            var sourceForm = this.MetaModelService?.LoadFormModel(this.Context, sourceFormId);
            var sourceDm = this.Container.GetService<IDataManager>();
            sourceDm.InitDbContext(this.Context, sourceForm.GetDynamicObjectType(this.Context));
            var sourceOrder = sourceDm.Select(sourceId) as DynamicObject;
            if (sourceOrder == null)
            {
                throw new BusinessException($"主键ID为：{sourceId} 的{sourceForm.Caption}记录不存在，请检查！");
            }

            //协同账户余额服务
            var synAccountBalanceService = this.Container.GetService<ISynAccountBalanceService>();
            if (sourceFormId == "ydj_supplier" &&
                !synAccountBalanceService.CanRechargeBySupplierId(this.Context, Convert.ToString(sourceOrder["id"]), Convert.ToString(topUpInfo["fusagetype"])))
            {
                throw new BusinessException("当前账户不支持充值!");
            }

            //协同企业ID
            var cooCompanyId = Convert.ToString(sourceOrder["fcoocompanyid"]);
            var cooProductId = Convert.ToString(sourceOrder["fcooproductid"]);

            //客户或供应商的协同状态是否为“已协同”
            this.IsSyn = Convert.ToString(sourceOrder["fcoostate"]).Trim().EqualsIgnoreCase("已协同");
            if (this.IsSyn)
            {
                if (cooCompanyId.IsNullOrEmptyOrWhiteSpace())
                {
                    throw new BusinessException($"{sourceForm.Caption}【{sourceOrder["fname"]}】对应的协同企业ID为空，请检查！");
                }
                var isSyn = this.CheckCompanyIsSynergy(cooCompanyId);
                if (!isSyn)
                {
                    throw new BusinessException($"{sourceForm.Caption}【{sourceOrder["fname"]}】没有与当前企业建立协同关系，请检查！");
                }
            }


            //代收单位
            var contactUnit = synAccountBalanceService.GetContactUnitById(this.Context, fcontactunitid);
            if (contactUnit != null)
            {
                topUpInfo["fcontactunittype"] = contactUnit["ftype"];
            }
            //代收单位是否是“商场”
            var contactUnitIsMall = contactUnit != null && Convert.ToString(contactUnit["ftype"]).EqualsIgnoreCase("contactunittype_01");

            //收支记录模型
            var htmlForm = this.Container.GetService<IMetaModelService>()?.LoadFormModel(this.Context, "coo_incomedisburse");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            List<DynamicObject> incomeDisburses = new List<DynamicObject>();
            incomeDisburses.Add(this.GetIncomeDisburseObject(htmlForm, sourceForm, topUpInfo, sourceOrder, oldTranId));

            //保存前预处理
            var prepareService = this.Container.GetService<IPrepareSaveDataService>();
            prepareService?.PrepareDataEntity(this.Context, htmlForm, incomeDisburses.ToArray(), OperateOption.Create());

            ////如果代收单位是“商场”则自动确认收支记录
            //if (contactUnitIsMall)
            //{
            //    var logService = this.Container.GetService<ILogService>();

            //    foreach (var incomeDisburse in incomeDisburses)
            //    {
            //        incomeDisburse["fbizstatus"] = "bizstatus_02";
            //        incomeDisburse["fbalance"] = synAccountBalanceService.GetAccountBalance(this.Context, incomeDisburse);
            //        incomeDisburse["fmodifierid"] = this.Context.UserId;
            //        incomeDisburse["fmodifydate"] = DateTime.Now;

            //        //更新账户余额
            //        synAccountBalanceService.UpdateAccountBalance(this.Context, incomeDisburse);

            //        //调用服务生操作日志
            //        logService?.WriteLog(this.Context, new LogEntry()
            //        {
            //            BillFormId = htmlForm.Id,
            //            OpCode = "confirm",
            //            OpName = "确认",
            //            BillIds = incomeDisburse["id"] as string,
            //            Level = Enu_LogLevel.Info.ToString(),
            //            LogType = Enu_LogType.RecordType_02,
            //            Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete), //这个很关键，必须用这个值，平台才会帮你写入记录表
            //            Content = "",
            //            Detail = "" //这个可以不传，平台上用这个是为了后续便于分析本次请求中的明细信息，以便定位问题所在。
            //        });
            //    }
            //}

            //发送协同
            if (this.IsSyn)
            {
                //发布基础数据到云链系统
                var chainDataJson = "";
                var target = new TargetSEP(cooCompanyId, cooProductId);
                var chainDataSyncService = this.Container.GetService<IChainDataSyncService>();
                chainDataJson = chainDataSyncService.PublishDataToChainAndPack(
                    this.Context, htmlForm, incomeDisburses, target, new List<string> { "fcustomerid", "fway", "fbanknumid" });

                //this.SendSynIncomeDisburse(target, incomeDisburses, chainDataJson);

                var simpleData = new Dictionary<string, string>
                {
                    { "cooCompanyId", cooCompanyId }
                };
                //协同生成收支记录
                this.SynergismRecord(target, incomeDisburses, simpleData);
            }

            //模拟正常表单保存操作流程
            var result = this.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(this.Context,
                htmlForm.Id,
                incomeDisburses,
                "save",
                new Dictionary<string, object>());
            result?.ThrowIfHasError(true, $"充值失败！");
            if (this.IsSyn)
            {
                this.Result.SimpleMessage = "充值成功，等待对方确认！";
            }
            else
            {
                //if (contactUnitIsMall)
                //{
                //    this.Result.SrvData = new { refreshParent = true };
                //    this.Result.SimpleMessage = "充值成功！";
                //}
                //else
                //{
                this.Result.SimpleMessage = "充值成功，等待确认！";
                //}
            }
            //统一在收支记录保存时处理 收支记录 自动提交操作
            // 自动提交
            //if (incomeDisburses.Any())
            //{
            //    var result = this.Gateway.InvokeBillOperation(this.Context, "coo_incomedisburse", incomeDisburses, "submitflow",
            //        new Dictionary<string, object>());
            //    result.ThrowIfHasError(true, "收款自动提交失败!");
            //}
            this.Result.IsSuccess = true;

            //返回收支记录的id，以支持调用方自动确认该笔收支记录，例如在销售意向单或销售合同收款确认时，加盟商自动充值确认
            this.Result.SrvData = new Dictionary<string, object>
            {
                { "incomeDisburseIds",incomeDisburses.Select(x=>Convert.ToString(x["id"])).ToArray()}
            };
        }

        /// <summary>
        /// 协同生成收支记录（不分经销模式） 
        /// </summary>
        /// <param name="target"></param>
        /// <param name="incomeDisburses"></param>
        private void SynergismRecord(TargetSEP target, List<DynamicObject> incomeDisburses, Dictionary<string, string> simpleData)
        {
            //数据发送时采用异步消息模式发送，消息中指定回调类型
            var responseResult = this.Gateway.Invoke(
                this.Context,
                target,
                new CommonBillDTO()
                {
                    FormId = "coo_incomedisburse",
                    OperationNo = "SaveSynRecord",
                    BillData = this.GetIncomeDisburseBillDatas(incomeDisburses),
                    ExecInAsync = false,
                    AsyncMode = (int)Enu_AsyncMode.Background,
                    SimpleData = simpleData
                }) as CommonBillDTOResponse;
            responseResult?.OperationResult?.ThrowIfHasError(true, $"协同充值失败，对方系统未返回任何响应！");
        }

        /// <summary>
        /// 获取本地收支记录数据包
        /// </summary>
        /// <param name="htmlForm"></param>
        /// <param name="sourceForm"></param>
        /// <param name="topUpInfo"></param>
        /// <param name="sourceOrder"></param>
        /// <param name="oldTranId"></param>
        /// <returns></returns>
        private DynamicObject GetIncomeDisburseObject(HtmlForm htmlForm, HtmlForm sourceForm, DynamicObject topUpInfo, DynamicObject sourceOrder, string oldTranId)
        {
            var numberField = sourceForm.GetNumberField();
            var numberValue = numberField?.DynamicProperty?.GetValue(sourceOrder);

            DynamicObject billHead = new DynamicObject(htmlForm.GetDynamicObjectType(this.Context));
            billHead["fdate"] = topUpInfo["fdate"];
            billHead["faccount"] = topUpInfo["fusagetype"];

            //账户充值，支付方式如果为空，则默认为“线下支付”，否则以前端用户选择的为准
            if (topUpInfo["fway"].IsNullOrEmptyOrWhiteSpace())
            {
                billHead["fway"] = "payway_04";
            }
            else
            {
                billHead["fway"] = topUpInfo["fway"];
            }

            switch (sourceForm.Id.Trim().ToLower())
            {
                case "ydj_supplier":
                    billHead["fpurcompany"] = this.Context?.Companys?.FirstOrDefault(t => t.CompanyId.EqualsIgnoreCase(this.Context.Company))?.CompanyName;
                    billHead["fpurcompanyid"] = this.Context.Company;
                    billHead["fsalecompany"] = sourceOrder["fcoocompany"];
                    billHead["fsalecompanyid"] = sourceOrder["fcoocompanyid"];
                    billHead["fcreatecompany"] = billHead["fpurcompany"]; //创建方默认等于采购方
                    billHead["fcreatecompanyid"] = billHead["fpurcompanyid"];
                    billHead["fsupplierid"] = sourceOrder["id"];
                    billHead["fbizdirection"] = "bizdirection_02";
                    break;
                case "ydj_customer":
                    billHead["fpurcompany"] = sourceOrder["fcoocompany"];
                    billHead["fpurcompanyid"] = sourceOrder["fcoocompanyid"];
                    billHead["fsalecompany"] = this.Context?.Companys?.FirstOrDefault(t => t.CompanyId.EqualsIgnoreCase(this.Context.Company))?.CompanyName;
                    billHead["fsalecompanyid"] = this.Context.Company;
                    billHead["fcreatecompany"] = billHead["fsalecompany"]; //创建方默认等于销售方
                    billHead["fcreatecompanyid"] = billHead["fsalecompanyid"];
                    billHead["fcustomerid"] = sourceOrder["id"];
                    //billHead["fcustomerphone"] = sourceOrder["fphone"];
                    billHead["fbizdirection"] = "bizdirection_01";
                    break;
                default:
                    break;
            }

            //#34699<收款>和<退款>按钮，弹出收款面板，需新增【销售部门】必录字段,保存后该【销售部门】需记录在【收支记录.基本信息.销售部门】字段
            var fdeptid = topUpInfo.GetValue("fdeptid", "");
            if (!fdeptid.IsNullOrEmptyOrWhiteSpace())
                billHead["fdeptid"] = fdeptid;

            var staff = GetStaff();
            if (staff != null)
            {
                billHead["fstaffid"] = Convert.ToString(staff["id"]);
                if (fdeptid.IsNullOrEmptyOrWhiteSpace())
                {
                    //客户收款，销售部门取当前创建人的部门
                    if (sourceForm.Id.Trim().ToLower().Equals("ydj_customer", StringComparison.CurrentCultureIgnoreCase))
                        billHead["fdeptid"] = Convert.ToString(staff["fdeptid"]);
                }
            }
            //40936 客户充值默认携带业务员，允许业务员修改-后端开发
            var fstaffid = topUpInfo.GetValue("fstaffid", "");
            if (!fstaffid.IsNullOrEmptyOrWhiteSpace())
                billHead["fstaffid"] = fstaffid;

            //40994 子 【武汉肖婷丹】收款单界面增加【收款单小票号】字段 / 开发
            billHead["freceiptno"] = Convert.ToString(topUpInfo["freceiptno"]).ToLower();

            billHead["paymentdesc"] = topUpInfo["paymentdesc"];
            billHead["fpurpose"] = "bizpurpose_01";
            billHead["fbizstatus"] = "bizstatus_01";
            billHead["foldtranid"] = oldTranId;
            billHead["forderno"] = "";
            billHead["fdirection"] = "direction_01";
            billHead["fcontactunittype"] = topUpInfo["fcontactunittype"];
            billHead["fcontactunitid"] = topUpInfo["fcontactunitid"];
            billHead["fverificstatus"] = "verificstatus_01";
            billHead["famount"] = topUpInfo["fmoney"];
            billHead["fimage"] = topUpInfo["fimage"];
            billHead["fdescription"] = topUpInfo["fdescription"];
            billHead["fbankcard"] = topUpInfo["fbankcard"];

            billHead["fmybankid"] = topUpInfo["fmybankid"];
            billHead["fsynbankid"] = topUpInfo["fsynbankid"];
            billHead["fsynbankname"] = topUpInfo["fsynbankname"];
            billHead["fsynbanknum"] = topUpInfo["fsynbanknum"];
            billHead["fsynaccountname"] = topUpInfo["fsynaccountname"];

            //后台字段
            billHead["fcoocompanyid"] = sourceOrder["fcoocompanyid"];
            billHead["fcooproductid"] = sourceOrder["fcooproductid"];
            billHead["fsourcetranid"] = sourceOrder["ftranid"];
            billHead["fsourceid"] = sourceOrder["id"];
            billHead["fsourcenumber"] = sourceOrder["fnumber"];
            billHead["fsourceformid"] = sourceForm.Id;
            billHead["fisself"] = true;
            billHead["fissyn"] = this.IsSyn;
            //收款小票号
            billHead["freceiptno"] = topUpInfo["freceiptno"];

            // 设置联合开单信息
            SetDutyEntry(topUpInfo, billHead, htmlForm);

            return billHead;
        }

        /// <summary>
        /// 设置联合开单信息
        /// </summary>
        private void SetDutyEntry(DynamicObject source, DynamicObject target, HtmlForm targetForm)
        {
            DynamicObjectCollection sourceEntryCollection = source["fdutyentry"] as DynamicObjectCollection;
            DynamicObjectCollection targetEntryCollection = target["fdutyentry"] as DynamicObjectCollection;
            var targetEntryEntity = targetForm.GetEntryEntity("fdutyentry");

            if (sourceEntryCollection == null || sourceEntryCollection.Count <= 0 || targetEntryEntity == null || targetEntryCollection == null)
            {
                return;
            }

            var sourceList = sourceEntryCollection.Where(x => !string.IsNullOrWhiteSpace(Convert.ToString(x["fdutyid"]))).ToList();

            if (sourceList == null || sourceList.Count <= 0)
            {
                return;
            }

            foreach (var sourceItem in sourceList)
            {
                DynamicObject targetItem = new DynamicObject(targetEntryEntity.DynamicObjectType);
                targetItem["fismain"] = sourceItem["fismain"];
                targetItem["fdutyid"] = sourceItem["fdutyid"];
                targetItem["fdeptid_ed"] = sourceItem["fdeptid_ed"];
                targetItem["fratio"] = sourceItem["fratio"];
                targetItem["famount_ed"] = sourceItem["famount_ed"];
                targetItem["fdescription_ed"] = sourceItem["fdescription_ed"];
                var isContainsDeptPerfRatio = sourceItem.DynamicObjectType.Properties.ContainsKey("fdeptperfratio");
                if (isContainsDeptPerfRatio)
                {
                    targetItem["fdeptperfratio"] = sourceItem["fdeptperfratio"];
                }
                else
                {
                    targetItem["fdeptperfratio"] = 0M;
                }
                //如果只有一个销售员，当首款比例不等于100%，处理默认等于100%,分成金额不等于结算金额，处理默认等于结算金额
                if (sourceList.Count() == 1)
                {
                    if (Convert.ToDecimal(targetItem["fratio"]) != 100) targetItem["fratio"] = 100;
                    if (Convert.ToDecimal(targetItem["famount_ed"]) != Convert.ToDecimal(target["famount"]))
                    {
                        targetItem["famount_ed"] = Convert.ToDecimal(target["famount"]);
                    }
                }
                targetEntryCollection.Add(targetItem);
            }
        }

        /// <summary>
        /// 得到关联员工
        /// </summary>
        /// <returns></returns>
        private DynamicObject GetStaff()
        {
            var purForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_staff");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, purForm.GetDynamicObjectType(this.Context));
            var where = "flinkuserid=@flinkuserid";
            var reader = this.Context.GetPkIdDataReader(purForm, where, new List<SqlParam>() {
            new SqlParam("@flinkuserid",System.Data.DbType.String,this.Context.UserId)
            });
            var data = dm.SelectBy(reader).OfType<DynamicObject>().FirstOrDefault();
            if (data != null)
                return data;
            return null;
        }

        /// <summary>
        /// 获取对方收支记录数据包
        /// </summary>
        /// <param name="incomeDisburses"></param>
        /// <returns></returns>
        private string GetIncomeDisburseBillDatas(List<DynamicObject> incomeDisburses)
        {
            List<Dictionary<string, object>> billDatas = new List<Dictionary<string, object>>();

            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_banknum");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            //开户行
            var htmlFormBank = this.MetaModelService.LoadFormModel(this.Context, "ydj_bank");
            var dmBank = this.Container.GetService<IDataManager>();
            dmBank.InitDbContext(this.Context, htmlFormBank.GetDynamicObjectType(this.Context));

            foreach (var incomeDisburse in incomeDisburses)
            {
                Dictionary<string, object> billData = new Dictionary<string, object>();
                billData["ftranid"] = incomeDisburse["ftranid"];
                billData["fdate"] = incomeDisburse["fdate"];
                billData["fway"] = incomeDisburse["fway"];
                billData["faccount"] = incomeDisburse["faccount"];
                billData["fpurcompany"] = incomeDisburse["fpurcompany"];
                billData["fpurcompanyid"] = incomeDisburse["fpurcompanyid"];
                billData["fsalecompany"] = incomeDisburse["fsalecompany"];
                billData["fsalecompanyid"] = incomeDisburse["fsalecompanyid"];
                billData["fcreatecompany"] = incomeDisburse["fcreatecompany"];
                billData["fcreatecompanyid"] = incomeDisburse["fcreatecompanyid"];
                billData["fpurpose"] = incomeDisburse["fpurpose"];
                billData["fbizstatus"] = incomeDisburse["fbizstatus"];
                billData["foldtranid"] = incomeDisburse["foldtranid"];
                billData["forderno"] = "";
                billData["fcontactunittype"] = incomeDisburse["fcontactunittype"];
                billData["fcontactunitid"] = incomeDisburse["fcontactunitid"];
                billData["fverificstatus"] = incomeDisburse["fverificstatus"];
                billData["fdirection"] = incomeDisburse["fdirection"];
                billData["famount"] = incomeDisburse["famount"];
                billData["fimage"] = incomeDisburse["fimage"];
                billData["fdescription"] = incomeDisburse["fdescription"];

                //后台字段
                billData["fsupplierid"] = "";
                billData["fcustomerid"] = "";
                billData["fcoocompanyid"] = this.Context.Company;
                billData["fcooproductid"] = this.Context.Product;
                billData["fsourcetranid"] = "";
                billData["fsourceid"] = "";
                billData["fsourcenumber"] = "";

                switch (Convert.ToString(incomeDisburse["fsourceformid"]).Trim().ToLower())
                {
                    case "ydj_supplier":
                        billData["fsourceformid"] = "ydj_customer";
                        break;
                    case "ydj_customer":
                        billData["fsourceformid"] = "ydj_supplier";
                        break;
                    default:
                        break;
                }

                //对换银行账号信息
                var myBankId = Convert.ToString(incomeDisburse["fmybankid"]);

                if (!myBankId.IsNullOrEmptyOrWhiteSpace())
                {
                    var myBank = dm.Select(myBankId) as DynamicObject;
                    if (myBank != null)
                    {
                        billData["fsynbankname"] = myBank["fbankname"];//开户行名称
                        billData["fsynbanknum"] = myBank["fbanknum"];//银行卡号
                        billData["fsynaccountname"] = myBank["fname"];//账户名称

                        //开户行
                        var fbankid = Convert.ToString(myBank["fbankid"]);
                        if (fbankid != null)
                        {
                            var bank = dmBank.Select(fbankid) as DynamicObject;
                            if (bank != null)
                            {
                                billData["fsynbank"] = bank["fname"];//开户行
                            }
                        }
                    }
                }
                billData["fmybankid"] = incomeDisburse["fsynbankid"];
                billData["fsynbankid"] = myBankId;

                switch (Convert.ToString(incomeDisburse["fbizdirection"]).Trim().ToLower())
                {
                    case "bizdirection_01":
                        billData["fbizdirection"] = "bizdirection_02";
                        break;
                    case "bizdirection_02":
                        billData["fbizdirection"] = "bizdirection_01";
                        break;
                    default:
                        break;
                }

                billData["fisself"] = false;
                billData["fissyn"] = incomeDisburse["fissyn"];

                billDatas.Add(billData);
            }

            return billDatas.ToJson();
        }

        /// <summary>
        /// 发送协同收支记录
        /// </summary>
        /// <param name="target"></param>
        /// <param name="incomeDisburses"></param>
        private void SendSynIncomeDisburse(TargetSEP target, List<DynamicObject> incomeDisburses, string chainDataJson)
        {
            //数据发送时采用异步消息模式发送，消息中指定回调类型
            var responseResult = this.Gateway.Invoke(
                this.Context,
                target,
                new CommonBillDTO()
                {
                    FormId = "coo_incomedisburse",
                    OperationNo = "SaveSynergy",
                    BillData = this.GetIncomeDisburseBillDatas(incomeDisburses),
                    ExecInAsync = false,
                    AsyncMode = (int)Enu_AsyncMode.Background,
                    SimpleData = new Dictionary<string, string>
                    {
                        { "chainDataJson", chainDataJson }
                    }
                }) as CommonBillDTOResponse;
            responseResult?.OperationResult?.ThrowIfHasError(true, $"协同充值失败，对方系统未返回任何响应！");
        }

        /// <summary>
        /// 检查指定企业与我是否已建立协同
        /// </summary>
        /// <param name="cooCompanyId"></param>
        /// <returns></returns>
        private bool CheckCompanyIsSynergy(string cooCompanyId)
        {
            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "coo_company");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            string where = $"fmycompanyid=@fmycompanyid and fcompanyid=@fcompanyid and fcoostatus=N'已协同'";
            var sqlParam = new SqlParam[]
            {
                new SqlParam("fmycompanyid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("fcompanyid", System.Data.DbType.String, cooCompanyId)
            };
            var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);
            var dataEntity = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
            if (dataEntity == null)
            {
                return false;
            }
            return true;
        }
    }
}