//using JieNor.Framework;
//using JieNor.Framework.CustomException;
//using JieNor.Framework.Interface;
//using JieNor.Framework.IoC;
//using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
//using JieNor.Framework.MetaCore.Validator;
//using JieNor.Framework.SuperOrm;
//using JieNor.Framework.SuperOrm.DataEntity;
//using JieNor.Framework.SuperOrm.DataManager;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.IncomeDisburse
//{
//    /// <summary>
//    /// 收支记录：核销
//    /// </summary>
//    [InjectService]
//    [FormId("coo_incomedisburse")]
//    [OperationNo("UnVerific")]
//    public class UnVerific: AbstractOperationServicePlugIn
//    {
//        /// <summary>
//        /// 预处理校验规则
//        /// </summary>
//        /// <param name="e"></param>
//        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
//        {
//            base.PrepareValidationRules(e);

//            /*
//                定义表头校验规则
//            */

//            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
//            {
//                if (!Convert.ToString(newData["fverificstatus"]).EqualsIgnoreCase("verificstatus_02"))
//                {
//                    return false;
//                }
//                return true;

//            }).WithMessage("核销状态不是【已核销】，不允许反核销！"));

//            var originalFormId = this.GetQueryOrSimpleParam<string>("originalFormId");
//            var originalOpNo = this.GetQueryOrSimpleParam<string>("originalOpNo");
//            if (false == originalFormId.EqualsIgnoreCase("ydj_marketstatement") || false == originalOpNo.EqualsIgnoreCase("UnAudit"))
//            {
//                e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
//                {
//                    var statementStatus = Convert.ToString(newData["fstatementstatus"]);
//                    return statementStatus != "2" && statementStatus != "3";
//                }).WithMessage("收支记录处于卖场对账中或已对账状态，不允许反核销！"));
//            }
//        }

//        /// <summary>
//        /// 调用操作事物前触发的事件
//        /// </summary>
//        /// <param name="e"></param>
//        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
//        {
//            e.DataEntitys = checkStoreStatement(e.DataEntitys);
//            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
//            {
//                if (this.Result.ComplexMessage.ErrorMessages.Count <= 0)
//                {
//                    this.Result.ComplexMessage.WarningMessages.Add("请选择一行或多行数据！");
//                    this.Result.IsSuccess = false;
//                }
//                return;
//            }

//            updateSourceInfo(e.DataEntitys);

//            var dm = this.Container.GetService<IDataManager>();
//            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));

//            foreach (var dataEntity in e.DataEntitys)
//            {
//                //更新收支记录
//                dataEntity["fverificstatus"] = "verificstatus_01";
//                dataEntity["fverificdate"] = null;
//                dm.Save(dataEntity);
//            }

//            this.AddRefreshPageAction();
//            this.Result.IsSuccess = true;
//        }

//        /// <summary>
//        /// 反写源单数据
//        /// </summary>
//        private void updateSourceInfo(DynamicObject[] dataEntities)
//        {
//            if (dataEntities == null || dataEntities.Length <= 0)
//            {
//                return;
//            }
//            var groups = dataEntities.Where(x => false == Convert.ToBoolean(x["fissyn"]) &&
//                                                 false == string.IsNullOrWhiteSpace(Convert.ToString(x["fsourceformid"])) &&
//                                                 false == string.IsNullOrWhiteSpace(Convert.ToString(x["fsourceid"])))
//                                     .GroupBy(x => Convert.ToString(x["fsourceformid"]));
//            foreach (var group in groups)
//            {
//                switch (group.Key)
//                {
//                    case "ydj_order":
//                        updateOrderInfo(group);
//                        break;
//                    case "ydj_saleintention":
//                        updateSaleIntention(group);
//                        break;
//                }
//            }
//        }

//        /// <summary>
//        /// 反写销售合同信息
//        /// </summary>
//        /// <param name="group"></param>
//        private void updateOrderInfo(IGrouping<string, DynamicObject> group)
//        {
//            var orderIds = group.Select(x => Convert.ToString(x["fsourceid"]))
//                                .Where(x => false == string.IsNullOrEmpty(x))
//                                .Distinct()
//                                .ToList();
//            var orderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
//            var orderDm = this.Container.GetService<IDataManager>();
//            orderDm.InitDbContext(this.Context, orderForm.GetDynamicObjectType(this.Context));
//            var orderEntities = orderDm.Select(orderIds).OfType<DynamicObject>().ToArray();

//            if (orderEntities == null || orderEntities.Length <= 0)
//            {
//                return;
//            }

//            foreach (var orderEntity in orderEntities)
//            {
//                var orderId = Convert.ToString(orderEntity["id"]);
//                var incomeEntities = group.Where(x => Convert.ToString(x["fsourceid"]).EqualsIgnoreCase(orderId)).ToList();
//                if (incomeEntities == null || incomeEntities.Count <= 0)
//                {
//                    continue;
//                }
//                var amount = incomeEntities.Sum(x => Convert.ToDecimal(x["famount"]));
//                var verifyAmount = Convert.ToDecimal(orderEntity["fverifyamount"]);
//                orderEntity["fverifyamount"] = verifyAmount - amount;
//            }

//            var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
//            prepareSaveDataService.PrepareDataEntity(this.Context, orderForm, orderEntities, OperateOption.Create());
//            orderDm.Save(orderEntities);
//        }

//        /// <summary>
//        /// 反写销售意向单信息
//        /// </summary>
//        /// <param name="group"></param>
//        private void updateSaleIntention(IGrouping<string, DynamicObject> group)
//        {
//            var saleIds = group.Select(x => Convert.ToString(x["fsourceid"]))
//                               .Where(x => false == string.IsNullOrEmpty(x))
//                               .Distinct()
//                               .ToList();
//            var saleForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_saleintention");
//            var saleDm = this.Container.GetService<IDataManager>();
//            saleDm.InitDbContext(this.Context, saleForm.GetDynamicObjectType(this.Context));
//            var saleEntities = saleDm.Select(saleIds).OfType<DynamicObject>().ToArray();

//            if (saleEntities == null || saleEntities.Length <= 0)
//            {
//                return;
//            }

//            var orderIds = saleEntities.Select(x => Convert.ToString(x["forderid"]))
//                                       .Where(x => false == string.IsNullOrWhiteSpace(x))
//                                       .Distinct()
//                                       .ToList();

//            if (orderIds == null || orderIds.Count <= 0)
//            {
//                return;
//            }

//            var orderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
//            var orderDm = this.Container.GetService<IDataManager>();
//            orderDm.InitDbContext(this.Context, orderForm.GetDynamicObjectType(this.Context));
//            var orderEntities = orderDm.Select(orderIds).OfType<DynamicObject>().ToArray();

//            if (orderEntities == null || orderEntities.Length <= 0)
//            {
//                return;
//            }

//            foreach (var orderEntity in orderEntities)
//            {
//                var orderId = Convert.ToString(orderEntity["id"]);
//                var saleEntity = saleEntities.FirstOrDefault(x => Convert.ToString(x["forderid"]).EqualsIgnoreCase(orderId));
//                if (saleEntity == null)
//                {
//                    continue;
//                }
//                var saleId = Convert.ToString(saleEntity["id"]);
//                var incomeEntities = group.Where(x => Convert.ToString(x["fsourceid"]).EqualsIgnoreCase(saleId)).ToList();
//                if (incomeEntities == null || incomeEntities.Count <= 0)
//                {
//                    continue;
//                }
//                var amount = incomeEntities.Sum(x => Convert.ToDecimal(x["famount"]));
//                var verifyAmount = Convert.ToDecimal(orderEntity["fverifyamount"]);
//                orderEntity["fverifyamount"] = verifyAmount - amount;
//            }

//            var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
//            prepareSaveDataService.PrepareDataEntity(this.Context, orderForm, orderEntities, OperateOption.Create());
//            orderDm.Save(orderEntities);
//        }

//        /// <summary>
//        /// 检查收支记录是否加入店面结算对账单明细
//        /// </summary>
//        /// <param name="dataEntities"></param>
//        /// <returns></returns>
//        private DynamicObject[] checkStoreStatement(DynamicObject[] dataEntities)
//        {
//            if (dataEntities == null || dataEntities.Length <= 0)
//            {
//                return dataEntities;
//            }

//            var incomeIds = dataEntities.Select(x => Convert.ToString(x["id"])).ToList();
//            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_storestatement");
//            var multiValueQuertyService = this.Container.GetService<IMultiValueQueryService>();
//            var where = "fmainorgid=@fmainorgid";
//            var sqlParams = new List<SqlParam> { new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company) };
//            var storeEntities = multiValueQuertyService.Select(this.Context, where, sqlParams, htmlForm, "fincomeid", incomeIds);

//            if (storeEntities == null || storeEntities.Count <= 0)
//            {
//                return dataEntities;
//            }

//            incomeIds = storeEntities.SelectMany(x => x["fincomeentry"] as DynamicObjectCollection)
//                                     .Select(x => Convert.ToString(x["fincomeid"]))
//                                     .Where(x => false == string.IsNullOrWhiteSpace(x))
//                                     .Distinct()
//                                     .ToList();

//            if (incomeIds == null || incomeIds.Count <= 0)
//            {
//                return dataEntities;
//            }

//            var validEntities = new List<DynamicObject>();
//            foreach (var dataEntity in dataEntities)
//            {
//                var id = Convert.ToString(dataEntity["id"]);
//                if (incomeIds.Contains(id))
//                {
//                    this.Result.ComplexMessage.ErrorMessages.Add($"编号[{dataEntity["fbillno"]}]的收支记录已关联{htmlForm.Caption},不允许反核销!");
//                    continue;
//                }
//                validEntities.Add(dataEntity);
//            }
//            return validEntities.ToArray();
//        }
//    }
//}
