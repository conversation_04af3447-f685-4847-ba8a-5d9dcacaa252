using JieNor.AMS.YDJ.Store.AppService.Plugin.BAS;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Flow;
using JieNor.Framework.AppService.OperationService.ApprovalFlow;


namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.IncomeDisburse
{
    /// <summary>
    /// 收支记录：提交
    /// </summary>
    [InjectService]
    [FormId("coo_incomedisburse")]
    [OperationNo("Submit")]
    public class Submit : AbstractOperationServicePlugIn
    {
       
    }
}
