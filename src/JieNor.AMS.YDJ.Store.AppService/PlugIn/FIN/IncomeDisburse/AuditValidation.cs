using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Core.Extensions;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.IncomeDisburse
{
    /// <summary>
    /// 收支记录：审核校验器
    /// </summary>
    public class AuditValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);
        }

        /// <summary>
        /// 校验结果
        /// </summary>
        private ValidationResult Result { get; set; } = new ValidationResult();

        /// <summary>
        /// 校验逻辑处理单元
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            foreach (var dataEntity in dataEntities)
            {
                if (Convert.ToString(dataEntity["fway"]).EqualsIgnoreCase("payway_06")
                    && dataEntity["fmybankid"].IsNullOrEmptyOrWhiteSpace())
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"{formInfo.Caption}【{dataEntity["fbillno"]}】我方银行字段为必填项，请先填写我方银行保存后再执行该操作！",
                        DataEntity = dataEntity,
                    });
                }
            }

            //校验【收款退款单】上游销售合同【确认收款】是否小于 销售合同的【订单总额】x【单据类型.自定义参数.销售合同允许XX的最低金额比例】
            this.CheckOrderAmountLimit(userCtx, formInfo, dataEntities);

            // 检查采购订单是否超额退款
            this.CheckPurchaseOrderIsExcessRefund(userCtx, formInfo, dataEntities);

            // 检查销售合同是否超额收款
            this.CheckOrderIsExcessReceipt(userCtx, formInfo, dataEntities);

            // 检查销售合同是否超额退款
            this.CheckOrderIsExcessRefund(userCtx, formInfo, dataEntities);

            return this.Result;
        }

        /// <summary>
        /// 校验【收款退款单】上游销售合同【确认收款】是否小于 
        /// 销售合同的【订单总额】x【单据类型.自定义参数.销售合同允许XX的最低金额比例】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        private void CheckOrderAmountLimit(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities)
        {
            //已提交、已审核状态的【收款单】才校验
            var validDataEntitys = dataEntities?.Where(x =>
             Convert.ToString(x["fsourceformid"]) == "ydj_order" &&
             Convert.ToString(x["fbizdirection"]) == "bizdirection_02"
            );
            if (validDataEntitys == null || !validDataEntitys.Any()) return;

            var orderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            var orderIds = validDataEntitys.Select(x => Convert.ToString(x["fsourceid"])).ToList();
            var validStatus = new string[] { "D", "E" };
            var orderInfo = userCtx.LoadBizBillHeadDataById(orderForm.Id, orderIds, "fbilltype,fstatus,freceivable,fsumamount")?.Where(x => validStatus.Contains(Convert.ToString(x["fstatus"])));
            if (orderInfo == null || !orderInfo.Any()) return;

            var billTypeService = userCtx.Container.GetService<IBillTypeService>();
            var paramSetObjs = billTypeService.GetBillTypeParamSet(this.Context, orderForm, new HashSet<string>(from x in orderInfo select Convert.ToString(x["fbilltype"])));

            var validInfo = from a in orderInfo
                            join b in validDataEntitys on Convert.ToString(a["id"]) equals Convert.ToString(b["fsourceid"])
                            select new
                            {
                                dataEntity = b,
                                receivable = Convert.ToDecimal(a["freceivable"]),
                                sumamount = Convert.ToDecimal(a["fsumamount"]),
                                status = Convert.ToString(a["fstatus"]),
                                billtype = Convert.ToString(a["fbilltype"]),
                                orderid= Convert.ToString(a["id"])
                            };

            Dictionary<string, decimal> ordMapReceivable = new Dictionary<string, decimal>();

            foreach (var item in validInfo)
            {
                decimal minpercentage = 0;
                var paramSetObj = paramSetObjs[item.billtype];
                //if (item.status.EqualsIgnoreCase("E"))
                //{
                decimal.TryParse(Convert.ToString(paramSetObj?["fsubmitminpercentage"]), out minpercentage);
                //}
                //else if (item.status.EqualsIgnoreCase("D"))
                //{
                //    decimal.TryParse(Convert.ToString(paramSetObj?["fsubmitminpercentage"]), out minpercentage);
                //}
                var receivable = item.receivable;
                if (ordMapReceivable.ContainsKey(item.orderid))
                {
                    receivable = ordMapReceivable[item.orderid];
                }
                if (receivable - Convert.ToDecimal(item.dataEntity["famount"]) < item.sumamount * minpercentage / 100)
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"对不起，审核收款退款单将导致上游销售合同【确认收款】金额未达到销售合同允许提交的最低金额比例，请先{ (item.status.EqualsIgnoreCase("E") ? "变更减少商品或反审核" : "撤销") }销售合同并减少商品后再执行。",
                        DataEntity = item.dataEntity
                    });
                    continue;
                }
                else
                {
                    decimal.TryParse(Convert.ToString(paramSetObj?["fauditminpercentage"]), out minpercentage);
                    if (receivable - Convert.ToDecimal(item.dataEntity["famount"]) < item.sumamount * minpercentage / 100)
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"对不起，审核收款退款单将导致上游销售合同【确认收款】金额未达到销售合同允许提交的最低金额比例，请先{ (item.status.EqualsIgnoreCase("E") ? "变更减少商品或反审核" : "撤销") }销售合同并减少商品后再执行。",
                            DataEntity = item.dataEntity
                        });
                    }
                }
                //避免同时反审核一个合同下多笔收支记录的情况
                if (!ordMapReceivable.ContainsKey(item.orderid))
                {
                    ordMapReceivable.Add(item.orderid, item.receivable - Convert.ToDecimal(item.dataEntity["famount"]));
                }
                else
                {
                    ordMapReceivable[item.orderid] = ordMapReceivable[item.orderid] - Convert.ToDecimal(item.dataEntity["famount"]);
                }
            }
        }

        /// <summary>
        /// 检查采购订单是否超额退款
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        private void CheckPurchaseOrderIsExcessRefund(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities)
        {
            // 查找退款收支记录的采购订单ID
            var purOrderIds = dataEntities
                .Where(x =>
                {
                    return Convert.ToString(x["fsourceformid"]).EqualsIgnoreCase("ydj_purchaseorder")
                        && Convert.ToString(x["fpurpose"]).EqualsIgnoreCase("bizpurpose_06")
                        && !x["fsourceid"].IsNullOrEmptyOrWhiteSpace();
                })
                .Select(x => Convert.ToString(x["fsourceid"]))
                .Distinct()
                .ToList();
            if (!purOrderIds.Any()) return;

            var purDynObjs = userCtx.LoadBizBillHeadDataById("ydj_purchaseorder", purOrderIds, "fbillno");
            if (purDynObjs == null || !purDynObjs.Any()) return;

            // 批量加载采购订单关联的收支金额
            var sourceReceiptRefunds = IncomeDisburseHelper.LoadOrderReceiptRefundAmount(
                this.Context,
                "ydj_purchaseorder",
                purOrderIds,
                null,
                new List<string>
                {
                    IncomeTypeConsts.receiptConfirmed,
                    IncomeTypeConsts.refundConfirmed
                });

            foreach (var dataEntity in dataEntities)
            {
                if (Convert.ToString(dataEntity["fsourceformid"]).EqualsIgnoreCase("ydj_purchaseorder")
                    && Convert.ToString(dataEntity["fpurpose"]).EqualsIgnoreCase("bizpurpose_06"))
                {
                    var sourceId = Convert.ToString(dataEntity["fsourceid"]);
                    var purDynObj = purDynObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(sourceId));
                    var purOrderNo = Convert.ToString(purDynObj?["fbillno"]);

                    var receiptConfirmedSum = IncomeDisburseHelper.FindSourceReceiptRefundAmount(sourceReceiptRefunds, sourceId, IncomeTypeConsts.receiptConfirmed);
                    var refundConfirmedSum = IncomeDisburseHelper.FindSourceReceiptRefundAmount(sourceReceiptRefunds, sourceId, IncomeTypeConsts.refundConfirmed);

                    // 与当前收支记录同属一个单据的所有收款收支记录金额汇总
                    var receiptSum = dataEntities
                        .Where(o =>
                        {
                            // 必须是统计已提交状态的收支记录，因为未提交的不会被审核
                            return Convert.ToString(o["fsourceformid"]).EqualsIgnoreCase("ydj_purchaseorder")
                                && Convert.ToString(o["fpurpose"]).EqualsIgnoreCase("bizpurpose_02")
                                && Convert.ToString(o["fsourceid"]).EqualsIgnoreCase(sourceId)
                                && Convert.ToString(o["fstatus"]).EqualsIgnoreCase("D");
                        })
                        .Select(o => Convert.ToDecimal(o["famount"]))
                        .Sum();

                    // 与当前收支记录同属一个单据的所有退款收支记录金额汇总
                    var refundSum = dataEntities
                        .Where(o =>
                        {
                            // 必须是统计已提交状态的收支记录，因为未提交的不会被审核
                            return Convert.ToString(o["fsourceformid"]).EqualsIgnoreCase("ydj_purchaseorder")
                                && Convert.ToString(o["fpurpose"]).EqualsIgnoreCase("bizpurpose_06")
                                && Convert.ToString(o["fsourceid"]).EqualsIgnoreCase(sourceId)
                                && Convert.ToString(o["fstatus"]).EqualsIgnoreCase("D");
                        })
                        .Select(o => Convert.ToDecimal(o["famount"]))
                        .Sum();

                    var _receiptSum = receiptConfirmedSum + receiptSum;
                    var _refundSum = refundConfirmedSum + refundSum;
                    if (_refundSum > _receiptSum)
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"采购订单【{purOrderNo}】的退款总额 {_refundSum.Format()} 不能大于已结算总额 {_receiptSum.Format()}！",
                            DataEntity = dataEntity
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 检查销售合同是否超额收款
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        private void CheckOrderIsExcessReceipt(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities)
        {
            // 查找收款收支记录的销售合同ID
            var orderIds = dataEntities
                .Where(x =>
                {
                    return Convert.ToString(x["fsourceformid"]).EqualsIgnoreCase("ydj_order")
                        && Convert.ToString(x["fpurpose"]).EqualsIgnoreCase("bizpurpose_02")
                        && !x["fsourceid"].IsNullOrEmptyOrWhiteSpace();
                })
                .Select(x => Convert.ToString(x["fsourceid"]))
                .Distinct()
                .ToList();
            if (!orderIds.Any()) return;

            // 获取是否可以超额收款参数
            var profileService = userCtx.Container.GetService<ISystemProfile>();
            var canexcess = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fcanexcess", false);
            if (canexcess) return;

            // 如果未勾选超额收款参数，则需要校验单据的收款总金额
            var orderDynObjs = userCtx.LoadBizBillHeadDataById("ydj_order", orderIds, "fbillno,fsumamount");
            if (orderDynObjs == null || !orderDynObjs.Any()) return;

            // 批量加载销售合同关联的收支金额
            var sourceReceiptRefunds = IncomeDisburseHelper.LoadOrderReceiptRefundAmount(
                this.Context,
                "ydj_order",
                orderIds,
                null,
                new List<string>
                {
                    IncomeTypeConsts.receiptConfirmed,
                    IncomeTypeConsts.refundConfirmed
                });

            foreach (var dataEntity in dataEntities)
            {
                if (Convert.ToString(dataEntity["fsourceformid"]).EqualsIgnoreCase("ydj_order")
                    && Convert.ToString(dataEntity["fpurpose"]).EqualsIgnoreCase("bizpurpose_02"))
                {
                    var sourceId = Convert.ToString(dataEntity["fsourceid"]);
                    var orderDynObj = orderDynObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(sourceId));
                    var orderNo = Convert.ToString(orderDynObj?["fbillno"]);
                    var sumAmount = Convert.ToDecimal(orderDynObj?["fsumamount"] ?? 0); // 订单总额

                    var receiptConfirmedSum = IncomeDisburseHelper.FindSourceReceiptRefundAmount(sourceReceiptRefunds, sourceId, IncomeTypeConsts.receiptConfirmed);
                    var refundConfirmedSum = IncomeDisburseHelper.FindSourceReceiptRefundAmount(sourceReceiptRefunds, sourceId, IncomeTypeConsts.refundConfirmed);

                    // 与当前收支记录同属一个单据的所有收款收支记录金额汇总
                    var receiptSum = dataEntities
                        .Where(o =>
                        {
                            // 必须是统计已提交状态的收支记录，因为未提交的不会被审核
                            return Convert.ToString(dataEntity["fsourceformid"]).EqualsIgnoreCase("ydj_order")
                                && Convert.ToString(o["fpurpose"]).EqualsIgnoreCase("bizpurpose_02")
                                && Convert.ToString(o["fsourceid"]).EqualsIgnoreCase(sourceId)
                                && Convert.ToString(o["fstatus"]).EqualsIgnoreCase("D");
                        })
                        .Select(o => Convert.ToDecimal(o["famount"]))
                        .Sum();

                    var _receiptSum = (receiptConfirmedSum - refundConfirmedSum) + receiptSum;
                    if (_receiptSum > sumAmount)
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"销售合同【{orderNo}】的收款总额 {_receiptSum.Format()} 已大于订单总额 {sumAmount.Format()}，当前系统设置为不允许超额收款！",
                            DataEntity = dataEntity
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 检查销售合同是否超额退款
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        private void CheckOrderIsExcessRefund(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities)
        {
            // 查找退款收支记录的销售合同ID
            var orderIds = dataEntities
                .Where(x =>
                {
                    return Convert.ToString(x["fsourceformid"]).EqualsIgnoreCase("ydj_order")
                        && Convert.ToString(x["fpurpose"]).EqualsIgnoreCase("bizpurpose_06")
                        && !x["fsourceid"].IsNullOrEmptyOrWhiteSpace();
                })
                .Select(x => Convert.ToString(x["fsourceid"]))
                .Distinct()
                .ToList();
            if (!orderIds.Any()) return;

            var dynObjs = userCtx.LoadBizBillHeadDataById("ydj_order", orderIds, "fbillno");
            if (dynObjs == null || !dynObjs.Any()) return;

            // 批量加载销售合同关联的收支金额
            var sourceReceiptRefunds = IncomeDisburseHelper.LoadOrderReceiptRefundAmount(
                this.Context,
                "ydj_order",
                orderIds,
                null,
                new List<string>
                {
                    IncomeTypeConsts.receiptConfirmed,
                    IncomeTypeConsts.refundConfirmed
                });

            foreach (var dataEntity in dataEntities)
            {
                if (Convert.ToString(dataEntity["fsourceformid"]).EqualsIgnoreCase("ydj_order")
                    && Convert.ToString(dataEntity["fpurpose"]).EqualsIgnoreCase("bizpurpose_06"))
                {
                    var sourceId = Convert.ToString(dataEntity["fsourceid"]);
                    var dynObj = dynObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(sourceId));
                    var orderNo = Convert.ToString(dynObj?["fbillno"]);

                    var receiptConfirmedSum = IncomeDisburseHelper.FindSourceReceiptRefundAmount(sourceReceiptRefunds, sourceId, IncomeTypeConsts.receiptConfirmed);
                    var refundConfirmedSum = IncomeDisburseHelper.FindSourceReceiptRefundAmount(sourceReceiptRefunds, sourceId, IncomeTypeConsts.refundConfirmed);

                    // 与当前收支记录同属一个单据的所有收款收支记录金额汇总
                    var receiptSum = dataEntities
                        .Where(o =>
                        {
                            // 必须是统计已提交状态的收支记录，因为未提交的不会被审核
                            return Convert.ToString(o["fsourceformid"]).EqualsIgnoreCase("ydj_order")
                                && Convert.ToString(o["fpurpose"]).EqualsIgnoreCase("bizpurpose_02")
                                && Convert.ToString(o["fsourceid"]).EqualsIgnoreCase(sourceId)
                                && Convert.ToString(o["fstatus"]).EqualsIgnoreCase("D");
                        })
                        .Select(o => Convert.ToDecimal(o["famount"]))
                        .Sum();

                    // 与当前收支记录同属一个单据的所有退款收支记录金额汇总
                    var refundSum = dataEntities
                        .Where(o =>
                        {
                            // 必须是统计已提交状态的收支记录，因为未提交的不会被审核
                            return Convert.ToString(o["fsourceformid"]).EqualsIgnoreCase("ydj_order")
                                && Convert.ToString(o["fpurpose"]).EqualsIgnoreCase("bizpurpose_06")
                                && Convert.ToString(o["fsourceid"]).EqualsIgnoreCase(sourceId)
                                && Convert.ToString(o["fstatus"]).EqualsIgnoreCase("D");
                        })
                        .Select(o => Convert.ToDecimal(o["famount"]))
                        .Sum();

                    var _receiptSum = receiptConfirmedSum + receiptSum;
                    var _refundSum = refundConfirmedSum + refundSum;
                    if (_refundSum > _receiptSum)
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"销售合同【{orderNo}】的退款总额 {_refundSum.Format()} 不能大于收款总额 {_receiptSum.Format()}！",
                            DataEntity = dataEntity
                        });
                    }
                }
            }
        }
    }
}