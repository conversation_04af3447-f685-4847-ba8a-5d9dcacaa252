using System;
using System.Linq;
using System.Collections.Generic;
using System.Data;
using Newtonsoft.Json.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.SuperOrm.Serialization;
using JieNor.AMS.YDJ.DataTransferObject.IncomeDisburse;
using JieNor.Framework.SuperOrm;
using JieNor.AMS.YDJ.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.Interface.CloudChain;
using static JieNor.Framework.MetaCore.FormOp.OperateOptionExtentions;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework.Utils;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.IncomeDisburse
{
    /// <summary>
    /// 协同账户余额服务
    /// </summary>
    [InjectService]
    public class SynAccountBalanceService : ISynAccountBalanceService
    {
        /// <summary>
        /// 检查账户余额
        /// </summary>
        /// <param name="userCtx">登录用户上下文</param>
        /// <param name="customerOrSupplier">客户或供应商数据包</param>
        /// <param name="isCustomer">是否是客户</param>
        /// <param name="accountSettle">账户结算信息</param>
        public void CheckAccountBalance(UserContext userCtx, DynamicObject customerOrSupplier, bool isCustomer,
            List<AccountInfo> accountSettle)
        {
            //扣减金额都小于等于0，无需检查
            if (accountSettle == null || accountSettle.Count <= 0) return;

            //如果是客户
            if (isCustomer)
            {
                #region 如果是客户，那么直接取客户协同账户配置

                var lstAllAccount = this.GetAllAccountByCustomerId(userCtx, customerOrSupplier["id"] as string)
                    .ToList();
                foreach (var account in accountSettle)
                {
                    if (account.SettleAmount > 0)
                    {
                        var accountInfo =
                            lstAllAccount.FirstOrDefault(t => t.AccountId.EqualsIgnoreCase(account.AccountId));
                        if (accountInfo != null)
                        {
                            if (account.SettleAmount > accountInfo.Balance)
                            {
                                if (accountInfo.CreditLimit > 0)
                                {
                                    if (Math.Abs(accountInfo.Balance - account.SettleAmount) > accountInfo.CreditLimit)
                                    {
                                        throw new BusinessException(
                                            $"{accountInfo.AccountName}账户信用额度不足，无法完成扣款，当前信用额度为：{accountInfo.CreditLimit.ToString("f2")}！");
                                    }
                                }
                                else
                                {
                                    throw new BusinessException(
                                        $"{accountInfo.AccountName}账户余额不足，无法完成扣款，当前余额为：{accountInfo.Balance.ToString("f2")}！");
                                }
                            }
                        }
                    }
                }

                #endregion
            }

            //如果是供应商
            else
            {
                //客户或供应商的协同状态是否为“已协同”
                var isSyn = Convert.ToString(customerOrSupplier["fcoostate"]).Trim().EqualsIgnoreCase("已协同");
                if (isSyn)
                {
                    #region 如果是协同，则需要跨站获取销售方的协同账户配置

                    //同步发送：向销售方获取协同账户设置信息
                    var responseResult = userCtx.Container.GetService<IHttpServiceInvoker>().Invoke(
                        userCtx,
                        new TargetSEP(customerOrSupplier["fcoocompanyid"] as string,
                            customerOrSupplier["fcooproductid"] as string),
                        new CommonBillDTO()
                        {
                            FormId = "ydj_customer",
                            OperationNo = "PurGetSynAccount",
                            BillData = "",
                            ExecInAsync = false,
                            SimpleData = new Dictionary<string, string>
                            {
                                { "cooCompanyId", Convert.ToString(customerOrSupplier["fcoocompanyid"]) }
                            }
                        }) as CommonBillDTOResponse;
                    responseResult?.OperationResult?.ThrowIfHasError(true, $"获取销售方的账户设置信息失败！");

                    List<AccountInfo> lstAllAccount = null;
                    var srvData = responseResult?.OperationResult?.SrvData as string;
                    if (!srvData.IsNullOrEmptyOrWhiteSpace())
                    {
                        lstAllAccount = srvData.FromJson<List<AccountInfo>>();
                    }

                    if (lstAllAccount == null) throw new BusinessException("获取销售方的账户设置信息失败！");

                    foreach (var account in accountSettle)
                    {
                        if (account.SettleAmount > 0)
                        {
                            var accountInfo =
                                lstAllAccount.FirstOrDefault(t => t.AccountId.EqualsIgnoreCase(account.AccountId));
                            if (accountInfo != null)
                            {
                                if (account.SettleAmount > accountInfo.Balance)
                                {
                                    if (accountInfo.CreditLimit > 0)
                                    {
                                        if (Math.Abs(accountInfo.Balance - account.SettleAmount) >
                                            accountInfo.CreditLimit)
                                        {
                                            throw new BusinessException(
                                                $"{accountInfo.AccountName}账户信用额度不足，无法完成扣款，当前信用额度为：{accountInfo.CreditLimit.ToString("f2")}！");
                                        }
                                    }
                                    else
                                    {
                                        throw new BusinessException(
                                            $"{accountInfo.AccountName}账户余额不足，无法完成扣款，当前余额为：{accountInfo.Balance.ToString("f2")}！");
                                    }
                                }
                            }
                        }
                    }

                    #endregion
                }
                else
                {
                    #region 如果不是协同，那么直接取供应商账户信息

                    var lstAllAccount = this.GetAllAccountBySupplierId(userCtx, customerOrSupplier["id"] as string)
                        .ToList();
                    foreach (var account in accountSettle)
                    {
                        if (account.SettleAmount > 0)
                        {
                            var accountInfo =
                                lstAllAccount.FirstOrDefault(t => t.AccountId.EqualsIgnoreCase(account.AccountId));
                            if (accountInfo != null)
                            {
                                if (account.SettleAmount > accountInfo.Balance)
                                {
                                    throw new BusinessException(
                                        $"{accountInfo.AccountName}账户余额不足，无法完成扣款，当前余额为：{accountInfo.Balance.ToString("f2")}！");
                                }
                            }
                        }
                    }

                    #endregion
                }
            }
        }

        /// <summary>
        /// 更新账户余额
        /// </summary>
        /// <param name="userCtx">登录用户上下文</param>
        /// <param name="dataEntity">收支记录数据包</param>
        public void UpdateAccountBalance(UserContext userCtx, DynamicObject dataEntity)
        {
            //账户为空的收支记录不参与余额增减逻辑，因为这类收支记录是由“线下支付”入口生成的，不存在账户的概念
            var account = Convert.ToString(dataEntity["faccount"]);
            if (account.IsNullOrEmptyOrWhiteSpace()) return;

            //是否是协同收支记录
            var isSyn = Convert.ToBoolean(dataEntity["fissyn"]);
            var isSelf = Convert.ToBoolean(dataEntity["fisself"]);
            var operationMode = Convert.ToString(dataEntity["foperationmode"]);

            //是直营模式，且非我方发起的收支记录，无需更新总部方客户账户余额
            //（更新没意义，因为该类场景是有直营门店发起的收退款，更新的是门店终端客户账户余额）
            if (operationMode.EqualsIgnoreCase(((int)Enu_OperateMode.HQDirect).ToString()) == true
                && isSelf == false)
            {
                return;
            }

            //供应商或客户的表单模型
            HtmlForm htmlForm = null;
            string supplierOrCustomerId = "";
            var supplierId = Convert.ToString(dataEntity["fsupplierid"]);
            var customerId = Convert.ToString(dataEntity["fcustomerid"]);
            if (!supplierId.IsNullOrEmptyOrWhiteSpace())
            {
                htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, "ydj_supplier");
                supplierOrCustomerId = supplierId;
            }
            else if (!customerId.IsNullOrEmptyOrWhiteSpace())
            {
                htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, "ydj_customer");
                supplierOrCustomerId = customerId;
            }
            else
            {
                if (operationMode.EqualsIgnoreCase(((int)Enu_OperateMode.HQDirect).ToString()) == true)
                {
                    return;
                }

                throw new BusinessException("供应商Id 和 客户Id 为空，请检查！");
            }

            if (isSyn)
            {
                var cooCompanyId = Convert.ToString(dataEntity["fcoocompanyid"]);
                if (cooCompanyId.IsNullOrEmptyOrWhiteSpace())
                {
                    throw new BusinessException("协同企业Id为空，请检查！");
                }
            }

            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));
            var supplierOrCustomer = dm.Select(supplierOrCustomerId) as DynamicObject;
            if (supplierOrCustomer == null)
            {
                throw new BusinessException("供应商或客户不存在，请检查！");
            }

            if (isSyn)
            {
                if (operationMode.EqualsIgnoreCase(((int)Enu_OperateMode.HQDirect).ToString()) == false)
                {
                    //检查协同关系是否正常
                    if (supplierOrCustomer["fcoocompanyid"].IsNullOrEmptyOrWhiteSpace()
                        || !Convert.ToString(supplierOrCustomer["fcoostate"]).EqualsIgnoreCase("已协同"))
                    {
                        throw new BusinessException(
                            $"{htmlForm.Caption}【{supplierOrCustomer["fname"]}】的协同企业Id为空，或者已取消了协同关系，请检查！");
                    }
                }
            }

            //检查账户是否存在
            var accountEntrys = supplierOrCustomer["fentry"] as DynamicObjectCollection;
            if (accountEntrys == null || accountEntrys.Count <= 0)
            {
                throw new BusinessException($"{htmlForm.Caption}【{supplierOrCustomer["fname"]}】的账户不存在，请检查！");
            }

            var accountEntry =
                accountEntrys.LastOrDefault(t => Convert.ToString(t["fpurpose"]).EqualsIgnoreCase(account));
            if (accountEntry == null)
            {
                throw new BusinessException($"{htmlForm.Caption}【{supplierOrCustomer["fname"]}】的账户不存在，请检查！");
            }

            //金额
            var amount = Convert.ToDecimal(dataEntity["famount"]);

            //金额方向
            var direction = Convert.ToString(dataEntity["fdirection"]).Trim().ToLower();
            switch (direction)
            {
                //收
                case "direction_01":
                    //增加余额
                    accountEntry["fbalance_e"] = Convert.ToDecimal(accountEntry["fbalance_e"]) + amount;
                    break;
                //退
                case "direction_02":
                    //扣减余额
                    accountEntry["fbalance_e"] = Convert.ToDecimal(accountEntry["fbalance_e"]) - amount;
                    break;
                default:
                    break;
            }

            //更新账户余额
            dm.Save(supplierOrCustomer);
        }

        /// <summary>
        /// 获取收支记录确认后的账户余额
        /// </summary>
        /// <param name="userCtx">登录用户上下文</param>
        /// <param name="dataEntity">收支记录数据包</param>
        /// <returns>账户余额</returns>
        public decimal GetAccountBalance(UserContext userCtx, DynamicObject dataEntity)
        {
            decimal balance = 0;

            //账户为空的收支记录不参与余额增减逻辑，因为这类收支记录是由“线下支付”入口生成的，不存在账户的概念
            var account = Convert.ToString(dataEntity["faccount"]);
            if (account.IsNullOrEmptyOrWhiteSpace()) return balance;

            string accountTable = "";
            string supplierOrCustomerId = "";
            var supplierId = Convert.ToString(dataEntity["fsupplierid"]);
            var customerId = Convert.ToString(dataEntity["fcustomerid"]);
            if (!supplierId.IsNullOrEmptyOrWhiteSpace())
            {
                accountTable = "t_ydj_supplieraccount";
                supplierOrCustomerId = supplierId;
            }
            else if (!customerId.IsNullOrEmptyOrWhiteSpace())
            {
                accountTable = "t_ydj_customeraccount";
                supplierOrCustomerId = customerId;
            }
            else
            {
                if (Convert.ToString(dataEntity["foperationmode"])
                        .EqualsIgnoreCase(((int)Enu_OperateMode.HQDirect).ToString()) == true)
                {
                    return 0;
                }

                throw new BusinessException("供应商Id 和 客户Id 为空，请检查！");
            }

            //从数据库中实时取账户余额
            var sqlText = $"select fbalance_e from {accountTable} where fid=@fid and fpurpose=@fpurpose";
            List<SqlParam> paramList = new List<SqlParam>()
            {
                new SqlParam("fid", DbType.String, supplierOrCustomerId),
                new SqlParam("fpurpose", DbType.String, account)
            };
            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, sqlText.ToString(), paramList))
            {
                if (reader.Read())
                {
                    balance = Convert.ToDecimal(reader[0]);
                }
            }

            //金额
            var amount = Convert.ToDecimal(dataEntity["famount"]);

            //金额方向
            var direction = Convert.ToString(dataEntity["fdirection"]).Trim().ToLower();
            switch (direction)
            {
                //收
                case "direction_01":
                    balance = balance + amount;
                    break;
                //退
                case "direction_02":
                    balance = balance - amount;
                    break;
                default:
                    break;
            }

            return balance;
        }

        /// <summary>
        /// 获取账户余额
        /// </summary>
        /// <param name="userCtx">登录用户上下文</param>
        /// <param name="tranFormId">交易主体业务类型：ydj_customer 或 ydj_supplier</param>
        /// <param name="tranBillId">交易主体主键Id：客户主键Id 或 供应商主键Id</param>
        /// <param name="accountId">账户Id</param>
        /// <returns>账户余额</returns>
        public decimal GetAccountBalance(UserContext userCtx, string tranFormId, string tranBillId, string accountId)
        {
            decimal balance = 0;

            if (tranFormId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("交易主体业务类型不能为空！");
            if (tranBillId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("交易主体主键ID不能为空！");
            if (accountId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("账户Id不能为空！");

            var accountTabelName = "";
            if (tranFormId.EqualsIgnoreCase("ydj_customer")) accountTabelName = "t_ydj_customeraccount";
            if (tranFormId.EqualsIgnoreCase("ydj_supplier")) accountTabelName = "t_ydj_supplieraccount";
            if (accountTabelName.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("交易主体业务类型错误！");

            var sqlText = $"select fbalance_e from {accountTabelName} where fid=@fid and fpurpose=@fpurpose";
            List<SqlParam> paramList = new List<SqlParam>()
            {
                new SqlParam("fid", DbType.String, tranBillId),
                new SqlParam("fpurpose", DbType.String, accountId)
            };
            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, sqlText.ToString(), paramList))
            {
                if (reader.Read())
                {
                    balance = Convert.ToDecimal(reader[0]);
                }
            }

            return balance;
        }

        /// <summary>
        /// 创建收支记录
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="tranDto"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        public IOperationResult CreateIncomeDisburseRecord(UserContext userCtx, IncomeDisburseRecord tranDto,
            OperateOption option)
        {
            OperationResult result = new OperationResult();
            result.IsSuccess = true;

            if (tranDto == null) throw new BusinessException("收支记录实体对象为空，请检查！");
            var wayIds = new List<string>() { "payway_01", "payway_05" };
            var payways = tranDto.AccountSettleInfo.Where(t => !wayIds.Contains(t.SettleType)).ToList();
            if (!tranDto.SettleAgentId.IsNullOrEmptyOrWhiteSpace() && tranDto.AttachId.IsNullOrEmptyOrWhiteSpace() &&
                payways.Count > 0)
            {
                throw new BusinessException("选择了代收单位，必须上传凭证！");
            }

            if (tranDto.Direction.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("交易方向为空，请检查！");
            }

            if (tranDto.SceneType.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("支付场景类型为空，请检查！");
            }

            if (tranDto.AccountSettleInfo == null || tranDto.AccountSettleInfo.Count() <= 0)
            {
                throw new BusinessException("账户结算信息为空，请检查！");
            }

            //交易主体信息
            var settleMain = this.GetSettleMain(userCtx, tranDto);

            //交易主体是否是客户
            var isCustomer = tranDto.TranFormId.EqualsIgnoreCase("ydj_customer");

            //检查账户余额，如果是初始化，则不检查余额
            if (tranDto.Direction.EqualsIgnoreCase("direction_02") &&
                (tranDto.SceneType ?? "").EqualsIgnoreCase("bizpurpose_07") == false)
            {
                List<AccountInfo> accountSettle = new List<AccountInfo>();
                foreach (var settleInfo in tranDto.AccountSettleInfo)
                {
                    if (!settleInfo.AccountId.IsNullOrEmptyOrWhiteSpace())
                    {
                        accountSettle.Add(new AccountInfo
                        {
                            AccountId = settleInfo.AccountId,
                            AccountName = settleInfo.AccountName,
                            SettleAmount = settleInfo.Amount
                        });
                    }
                }

                this.CheckAccountBalance(userCtx, settleMain, isCustomer, accountSettle);
            }

            //代收单位
            var contactUnitType = tranDto.SettleAgentType;
            if (string.IsNullOrWhiteSpace(contactUnitType))
            {
                var contactUnit = this.GetContactUnitById(userCtx, tranDto.SettleAgentId);
                if (contactUnit != null)
                {
                    contactUnitType = contactUnit["ftype"] as string;
                }
            }

            //收支记录模型
            var htmlForm = userCtx.Container.GetService<IMetaModelService>()
                ?.LoadFormModel(userCtx, "coo_incomedisburse");

            //组装收支记录
            List<DynamicObject> incomeDisburses = new List<DynamicObject>();
            foreach (var settleInfo in tranDto.AccountSettleInfo)
            {
                if (settleInfo.Amount > 0 && settleInfo.Amount < 0.01M)
                {
                    if (!settleInfo.AccountName.IsNullOrEmptyOrWhiteSpace())
                    {
                        throw new BusinessException($"{settleInfo.AccountName}必须大于0！");
                    }
                    else
                    {
                        throw new BusinessException($"结算金额必须大于0！");
                    }
                }

                if (settleInfo.Amount > 0)
                {
                    incomeDisburses.Add(this.GetIncomeDisburseObject(
                        userCtx, htmlForm, settleMain, tranDto,
                        settleInfo.Amount, settleInfo.SettleType,
                        settleInfo.AccountId, contactUnitType));
                }
            }

            if (incomeDisburses.Count <= 0) throw new BusinessException($"结算金额不允许小于等于0，请检查！");

            //保存前预处理
            var prepareService = userCtx.Container.GetService<IPrepareSaveDataService>();
            prepareService?.PrepareDataEntity(userCtx, htmlForm, incomeDisburses.ToArray(), OperateOption.Create());

            //模拟正常表单保存操作流程
            var saveResult = userCtx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(userCtx,
                htmlForm.Id,
                incomeDisburses,
                "save",
                new Dictionary<string, object>());
            saveResult?.ThrowIfHasError(true, "收支记录保存出现错误，请检查当前用户的权限！");
            result.MergeResult(saveResult);

            //是否自动确认收支记录
            if (tranDto.AutoConfirm)
            {
                var confirmResult = userCtx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(userCtx,
                    htmlForm.Id,
                    incomeDisburses,
                    "confirm",
                    new Dictionary<string, object>());
                result.MergeResult(confirmResult);
            }

            return result;
        }

        /// <summary>
        /// 获取本地收支记录数据包
        /// </summary>
        private DynamicObject GetIncomeDisburseObject(UserContext userCtx, HtmlForm htmlForm,
            DynamicObject settleMain,
            IncomeDisburseRecord tranDto,
            decimal amount,
            string way,
            string account = "",
            string contactUnitType = "")
        {
            DynamicObject billHead = new DynamicObject(htmlForm.GetDynamicObjectType(userCtx));
            billHead["faccount"] = account;
            billHead["famount"] = amount;
            billHead["fway"] = way;
            billHead["fdate"] = tranDto.SettleDate;
            billHead["fpurpose"] = tranDto.SceneType;
            billHead["fbizstatus"] = "bizstatus_01";
            billHead["forderno"] = tranDto.LinkBillNo;
            billHead["fdirection"] = tranDto.Direction;
            billHead["fbizdirection"] = tranDto.BizDirection;
            billHead["fcontactunittype"] = contactUnitType;
            billHead["fcontactunitid"] = tranDto.SettleAgentId;
            billHead["fverificstatus"] = "verificstatus_01";
            billHead["fimage"] = tranDto.AttachId;
            billHead["fdescription"] = tranDto.Description;
            billHead["fwithin"] = tranDto.fwithin;
            billHead["paymentdesc"] = tranDto.paymentdesc;
            if (tranDto.AccountSettleInfo != null && tranDto.AccountSettleInfo.Count > 0)
            {
                billHead["fbankcard"] = tranDto.AccountSettleInfo[0].BankCardNo;
            }

            billHead["fmybankid"] = tranDto.MyBankId;
            billHead["fsynbankid"] = tranDto.SynBankId;
            billHead["fsynbankname"] = tranDto.SynBankName;
            billHead["fsynbanknum"] = tranDto.SynBankNum;
            billHead["fsynaccountname"] = tranDto.SynAccountName;
            billHead["fdeptid"] = tranDto.DeptId;
            billHead["fmarketsettleno"] = tranDto.MarketSettleNo;
            billHead["fotherpartyaccount"] = tranDto.OtherPartyAccount;

            //后台字段
            switch (tranDto.TranFormId.Trim().ToLower())
            {
                case "ydj_customer":
                    billHead["fcustomerid"] = settleMain["id"];
                    billHead["fpurcompany"] = settleMain["fcoocompany"];
                    billHead["fpurcompanyid"] = settleMain["fcoocompanyid"];
                    billHead["fsalecompany"] = userCtx?.Companys
                        ?.FirstOrDefault(t => t.CompanyId.EqualsIgnoreCase(userCtx.Company))?.CompanyName;
                    billHead["fsalecompanyid"] = userCtx.Company;
                    billHead["fcreatecompany"] = billHead["fsalecompany"];
                    billHead["fcreatecompanyid"] = billHead["fsalecompanyid"];
                    break;
                case "ydj_supplier":
                    billHead["fsupplierid"] = settleMain["id"];
                    billHead["fpurcompany"] = userCtx?.Companys
                        ?.FirstOrDefault(t => t.CompanyId.EqualsIgnoreCase(userCtx.Company))?.CompanyName;
                    billHead["fpurcompanyid"] = userCtx.Company;
                    billHead["fsalecompany"] = settleMain["fcoocompany"];
                    billHead["fsalecompanyid"] = settleMain["fcoocompanyid"];
                    billHead["fcreatecompany"] = billHead["fpurcompany"];
                    billHead["fcreatecompanyid"] = billHead["fpurcompanyid"];
                    break;
                default:
                    break;
            }

            //源单类型为销售合同，收支明细销售员为销售合同的销售员
            if (tranDto.LinkFormId.Trim().ToLower().Equals("ydj_order", StringComparison.CurrentCultureIgnoreCase))
            {
                var linkMain = GetLinkFormMain(userCtx, tranDto, "fstaffid");
                billHead["fstaffid"] = linkMain["fstaffid"];

                // 设置联合开单信息
                SetDutyEntry(tranDto.JoinStaffs, billHead, htmlForm);
            }

            //源单类型为其他应收单，收支明细销售员为其他应收单的员工
            if (tranDto.LinkFormId.Trim().ToLower()
                .Equals("ydj_collectreceipt", StringComparison.CurrentCultureIgnoreCase))
            {
                var linkMain = GetLinkFormMain(userCtx, tranDto, "frelatemanid");
                billHead["fstaffid"] = linkMain["frelatemanid"];

                // 设置联合开单信息
                SetDutyEntry(tranDto.JoinStaffs, billHead, htmlForm);
            }

            //源单类型为销售退货单，收支明细销售员为销售退货单的销售员
            if (tranDto.LinkFormId.Trim().ToLower()
                .Equals("stk_sostockreturn", StringComparison.CurrentCultureIgnoreCase))
            {
                var linkMain = GetLinkFormMain(userCtx, tranDto, "fsostaffid");
                billHead["fstaffid"] = linkMain["fsostaffid"];
            }

            billHead["fcoocompanyid"] = "";
            billHead["fcooproductid"] = "";
            billHead["fsourcetranid"] = tranDto.LinkTranId;
            billHead["fsourceid"] = tranDto.LinkBillId;
            billHead["fsourcenumber"] = tranDto.LinkBillNo;
            billHead["fsourceformid"] = tranDto.LinkFormId;
            billHead["fissyn"] = false;
            billHead["fisself"] = true;
            billHead["foperationmode"] = (int)Enu_OperateMode.None;
            //billHead["fcustomerphone"] = tranDto.Phone;
            billHead["fcusacount"] = tranDto.Cusacount;
            //收款小票号
            billHead["freceiptno"] = tranDto.ReceiptNo;

            var orderBillTypeid = tranDto?.OrderBilltype;
            if (!orderBillTypeid.IsNullOrEmptyOrWhiteSpace())
            {
                var svc = userCtx.Container.GetService<IBillTypeService>();
                var billTypeInfo = svc.GetBillTypeInfor(userCtx, orderBillTypeid);
                //单据类型
                billHead["forderbilltype"] = billTypeInfo.fname;

                //var sqlParam = new List<SqlParam>
                //{
                //    new SqlParam("@fid", System.Data.DbType.String, orderBillTypeid)
                //};
                //var sqlText = $@"select fname from t_bd_billtype where fid=@fid";
                //var dbService = userCtx.Container.GetService<IDBService>();
                //using (var reader = dbService.ExecuteReader(userCtx, sqlText, sqlParam))
                //{
                //    if (reader.Read())
                //    {
                //        //单据类型
                //        billHead["forderbilltype"] = reader.GetValueToString("fname");
                //    }
                //}
            }

            //业务日期
            if (!tranDto.OrderDate.IsNullOrEmptyOrWhiteSpace())
                billHead["forderdate"] = tranDto?.OrderDate;

            billHead["flinkorderno"] = tranDto?.OrderNo;

            //退款业务类型
            switch (tranDto.LinkFormId.Trim().ToLower())
            {
                case "ydj_collectreceipt":
                    //其他应收-默认赋值为销售退差
                    billHead["frefundbusiness"] = "refund_sr";
                    break;
                //销售退货-默认赋值为售后退货退款
                case "stk_sostockreturn":
                    billHead["frefundbusiness"] = "refund_arr";
                    break;
                //销售合同-销售变更退款
                case "ydj_order":
                    billHead["frefundbusiness"] = "refund_sar";
                    break;
            }

            return billHead;
        }

        /// <summary>
        /// 设置联合开单信息
        /// </summary>
        private void SetDutyEntry(List<OrderJoinStaffModel> source, DynamicObject target, HtmlForm targetForm)
        {
            DynamicObjectCollection targetEntryCollection = target["fdutyentry"] as DynamicObjectCollection;
            var targetEntryEntity = targetForm.GetEntryEntity("fdutyentry");

            if (source == null || source.Count <= 0 || targetEntryEntity == null || targetEntryCollection == null)
            {
                return;
            }

            var sourceList = source.Where(x => !string.IsNullOrWhiteSpace(x.DutyId)).ToList();
            if (sourceList == null || sourceList.Count <= 0)
            {
                return;
            }

            foreach (var sourceItem in sourceList)
            {
                DynamicObject targetItem = new DynamicObject(targetEntryEntity.DynamicObjectType);
                targetItem["fismain"] = sourceItem.IsMain;
                targetItem["fdutyid"] = sourceItem.DutyId;
                targetItem["fdeptid_ed"] = sourceItem.DeptId;
                targetItem["fratio"] = sourceItem.Ratio;
                targetItem["famount_ed"] = sourceItem.Amount;
                targetItem["fdescription_ed"] = sourceItem.Description;
                targetItem["fdeptperfratio"] = sourceItem.DeptPerfRatio;
                //如果只有一个销售员，当首款比例不等于100%，处理默认等于100%,分成金额不等于结算金额，处理默认等于结算金额
                if (sourceList.Count() == 1)
                {
                    if (Convert.ToDecimal(targetItem["fratio"]) != 100) targetItem["fratio"] = 100;
                    if (Convert.ToDecimal(targetItem["famount_ed"]) != Convert.ToDecimal(target["famount"]))
                    {
                        targetItem["famount_ed"] = Convert.ToDecimal(target["famount"]);
                    }
                }

                targetEntryCollection.Add(targetItem);
            }
        }

        /// <summary>
        /// 获取交易主体信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="tranDto"></param>
        /// <returns></returns>
        private DynamicObject GetSettleMain(UserContext userCtx, IncomeDisburseRecord tranDto)
        {
            if (tranDto.TranFormId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("交易主体业务类型为空，请检查参数！");
            if (tranDto.TranBillId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("交易主体主键为空，请检查参数！");

            // 按需加载，性能优化
            var settleMain = userCtx.LoadBizBillHeadDataById(tranDto.TranFormId, tranDto.TranBillId,
                "fcoocompany,fcoocompanyid,fcooproductid,fcoostate");
            if (settleMain == null) throw new BusinessException($"结算主体信息不存在！");

            return settleMain;
        }

        /// <summary>
        /// 获取关联业务单据主体信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="tranDto"></param>
        /// <returns></returns>
        private DynamicObject GetLinkFormMain(UserContext userCtx, IncomeDisburseRecord tranDto, string filedName)
        {
            if (tranDto.TranFormId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("关联业务单据类型为空，请检查参数！");
            if (tranDto.TranBillId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("交易关联业务单据内码为空，请检查参数！");

            // 按需加载，性能优化
            var settleMain = userCtx.LoadBizBillHeadDataById(tranDto.LinkFormId, tranDto.LinkBillId, filedName);
            if (settleMain == null) throw new BusinessException($"交易关联业务单据主体信息不存在！");

            return settleMain;
        }

        /// <summary>
        /// 获取所有账户信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public IEnumerable<AccountInfo> GetAllAccount(UserContext userCtx)
        {
            List<AccountInfo> lstAllAccount = new List<AccountInfo>();
            var metaModelService = userCtx.Container.GetService<IMetaModelService>();
            //读取 账户设置（经销商）设置信息
            var dealerSetupMeta = metaModelService.LoadFormModel(userCtx, "sal_dealersetup");

            var spService = userCtx.Container.GetService<ISystemProfile>();
            var sysProfileValue = spService?.GetProfile(userCtx, "fw", $"sal_dealersetup_parameter");

            DynamicObject sysProfileObj = null;
            if (!sysProfileValue.IsNullOrEmptyOrWhiteSpace())
            {
                List<DynamicObject> sysProfileObjs = new List<DynamicObject>();
                var billItems = new JArray();
                billItems.Add(JObject.Parse(sysProfileValue));
                var dcSerializer = userCtx.Container.GetService<IDynamicSerializer>();
                dcSerializer.Sync(dealerSetupMeta.GetDynamicObjectType(userCtx), sysProfileObjs, billItems);
                if (sysProfileObjs.Count > 0)
                {
                    sysProfileObj = sysProfileObjs[0];
                }
            }

            var billDataService = userCtx.Container.GetService<IBillDataService>();
            //加载帐户辅助资料数据
            var assistResult = billDataService.LoadAssistantData(userCtx, "结算单账户类型");
            if (assistResult.SrvData is List<BaseDataSummary> && assistResult.IsSuccess)
            {
                foreach (var assistData in assistResult.SrvData as List<BaseDataSummary>)
                {
                    var accountInfo = new AccountInfo();
                    accountInfo.AccountId = assistData.Id;
                    accountInfo.AccountName = assistData.Name;

                    //只有返利账户不是现金账户，其他都为现金账户
                    accountInfo.IsCash = accountInfo.AccountId.EqualsIgnoreCase("settleaccount_type_02") ? false : true;

                    accountInfo.CanRecharge =
                        true; // accountInfo.AccountId == "settleaccount_type_01" || accountInfo.AccountId == "settleaccount_type_03";
                    accountInfo.CanUseInOrderPay = true;
                    lstAllAccount.Add(accountInfo);

                    if (sysProfileObj != null)
                    {
                        var paraEntryObjs = sysProfileObj["fentry"] as DynamicObjectCollection;

                        var existAccountInfo = paraEntryObjs.FirstOrDefault(o =>
                            accountInfo.AccountId.EqualsIgnoreCase(o["fpurpose"] as string));
                        if (existAccountInfo != null)
                        {
                            accountInfo.CanUseInOrderPay = (bool)existAccountInfo["fispayment"];
                            accountInfo.CreditLimit = (decimal)existAccountInfo["fcredit"];
                            accountInfo.NoConfirmInPay = (bool)existAccountInfo["fisbalance"];
                            accountInfo.CanRecharge = (bool)existAccountInfo["fisrecharge"];
                        }
                    }
                }
            }

            return lstAllAccount;
        }

        /// <summary>
        /// 获取客户帐户信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="customerId"></param>
        /// <returns></returns>
        public IEnumerable<AccountInfo> GetAllAccountByCustomerId(UserContext userCtx, string customerId)
        {
            if (customerId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException($"customerId 参数为空，请检查！");
            List<AccountInfo> lstAllAccount = new List<AccountInfo>();

            var htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, "ydj_customer");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));
            var dataEntity = dm.Select(customerId) as DynamicObject;
            if (dataEntity != null)
            {
                //加载引用数据
                userCtx.Container.GetService<LoadReferenceObjectManager>()?
                    .Load(userCtx, htmlForm.GetDynamicObjectType(userCtx), dataEntity, false);

                var entrys = dataEntity["fentry"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    lstAllAccount.Add(new AccountInfo
                    {
                        AccountId = Convert.ToString(entry["fpurpose"]),
                        AccountName = Convert.ToString((entry["fpurpose_ref"] as DynamicObject)?["fenumitem"]),
                        CanUseInOrderPay = Convert.ToBoolean(entry["fispayment"]),
                        NoConfirmInPay = Convert.ToBoolean(entry["fisbalance"]),
                        BalanceInUnConfirm = Convert.ToBoolean(entry["fisbalanceinunconfirm"]),
                        IsCash = Convert.ToBoolean(entry["fiscash"]),
                        CreditLimit = Convert.ToDecimal(entry["fcredit"]),
                        Balance = Convert.ToDecimal(entry["fbalance_e"]),
                        CanRecharge = Convert.ToBoolean(entry["fisrecharge"])
                    });
                }
            }

            return lstAllAccount;
        }

        /// <summary>
        /// 获取供应商帐户信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="supplierId"></param>
        /// <returns></returns>
        public IEnumerable<AccountInfo> GetAllAccountBySupplierId(UserContext userCtx, string supplierId)
        {
            if (supplierId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException($"supplierId 参数为空，请检查！");
            List<AccountInfo> lstAllAccount = new List<AccountInfo>();

            var htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, "ydj_supplier");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));
            var dataEntity = dm.Select(supplierId) as DynamicObject;
            if (dataEntity != null)
            {
                //加载引用数据
                userCtx.Container.GetService<LoadReferenceObjectManager>()?
                    .Load(userCtx, htmlForm.GetDynamicObjectType(userCtx), dataEntity, false);

                var entrys = dataEntity["fentry"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    lstAllAccount.Add(new AccountInfo
                    {
                        AccountId = Convert.ToString(entry["fpurpose"]),
                        AccountName = Convert.ToString((entry["fpurpose_ref"] as DynamicObject)?["fenumitem"]),
                        Balance = Convert.ToDecimal(entry["fbalance_e"]),
                        //可结算金额 取该账户可用金额
                        AvailableCredit = Convert.ToDecimal(entry["fbalance_e"]),
                        CanUseInOrderPay = Convert.ToBoolean(entry["fispayment"])
                    });
                }
            }

            return lstAllAccount;
        }

        /// <summary>
        /// 根据供应商id判断账户是否可充值
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="supplierId"></param>
        /// <param name="accountId"></param>
        /// <returns></returns>
        public bool CanRechargeBySupplierId(UserContext userCtx, string supplierId, string accountId)
        {
            if (supplierId.IsNullOrEmptyOrWhiteSpace() || accountId.IsNullOrEmptyOrWhiteSpace())
            {
                return false;
            }

            return GetRechargeBySupplierId(userCtx, supplierId).Contains(accountId);
        }

        /// <summary>
        /// 根据供应商id获取可充值的账户
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="supplierId"></param>
        /// <param name="accountId"></param>
        /// <returns></returns>
        public string[] GetRechargeBySupplierId(UserContext userCtx, string supplierId)
        {
            var result = Array.Empty<string>();
            if (supplierId.IsNullOrEmptyOrWhiteSpace())
            {
                return result;
            }

            var htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, "ydj_supplier");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));
            var dataEntity = dm.Select(supplierId) as DynamicObject;

            if (dataEntity == null)
            {
                return result;
            }

            var fcoostate = Convert.ToString(dataEntity["fcoostate"]);
            //不是协同供应商可以充值
            if (string.IsNullOrWhiteSpace(fcoostate))
            {
                return this.GetAllAccountBySupplierId(userCtx, supplierId).Select(x => x.AccountId).ToArray();
            }

            //是协同供应商，但当前状态不是已协同状态不能充值
            if (fcoostate != "已协同")
            {
                return result;
            }

            var fcoocompanyid = Convert.ToString(dataEntity["fcoocompanyid"]);
            var fcooproductid = Convert.ToString(dataEntity["fcooproductid"]);
            var target = new TargetSEP(fcoocompanyid, fcooproductid);
            var geteway = userCtx.Container.GetService<IHttpServiceInvoker>();
            var responseResult = geteway.Invoke(
                userCtx,
                target,
                new CommonBillDTO()
                {
                    FormId = "ydj_customer",
                    OperationNo = "CanRechargeSynergy",
                    ExecInAsync = false,
                    AsyncMode = (int)Enu_AsyncMode.Background,
                    SimpleData = new Dictionary<string, string>()
                    {
                        { "cooCompanyId", target.CompanyId }
                    }
                }) as CommonBillDTOResponse;
            responseResult?.OperationResult?.ThrowIfHasError(true, $"协同充值账号失败，对方系统未返回任何响应！");

            var accountIdInfos = responseResult.OperationResult.SrvData?.ToString();
            if (string.IsNullOrWhiteSpace(accountIdInfos))
            {
                return result;
            }

            return accountIdInfos.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
        }

        /// <summary>
        /// 根据代收单位Id获取代收单位信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="contactUnitId"></param>
        /// <returns></returns>
        public Dictionary<string, object> GetContactUnitById(UserContext userCtx, string contactUnitId)
        {
            if (contactUnitId.IsNullOrEmptyOrWhiteSpace()) return null;

            Dictionary<string, object> contactUnit = null;

            var sqlText = "select top 1 * from v_ydj_contactunit where fmainorgid=@fmainorgid and fid=@fid";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("fid", System.Data.DbType.String, contactUnitId)
            };
            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    contactUnit = new Dictionary<string, object>();
                    for (int iCol = 0; iCol < reader.FieldCount; iCol++)
                    {
                        var objValue = reader[iCol];
                        var colName = reader.GetName(iCol);
                        contactUnit[colName] = objValue.IsNullOrEmptyOrWhiteSpace() ? "" : objValue;
                    }
                }
            }

            return contactUnit;
        }
    }
}