using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System.Collections.Generic;
using System.Linq;
using System;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.IncomeDisburse;
using JieNor.Framework.SuperOrm;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.IncomeDisburse
{
    /// <summary>
    /// 收支记录：保存
    /// </summary>
    [InjectService]
    [FormId("coo_incomedisburse")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var errorMessage = "";

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMessage = "";
                var fcreatorid = Convert.ToString(newData["fcreatorid"]); //创建人
                if (this.Context.IsDirectSale) 
                {
                    var staff = this.Context
                     .LoadBizDataByNo("ydj_staff", "flinkuserid", new List<string> { fcreatorid })
                     ?.FirstOrDefault();
                    var EmpNo = staff != null ? Convert.ToString(staff["fehrcode"]) : "";
                    if (EmpNo.IsNullOrEmptyOrWhiteSpace()) 
                    {
                        errorMessage = $"当前单据创建人关联的【EHR工号】为空，请前往《员工》维护";
                        return false;
                    }
                }

                if (Convert.ToString(newData["fway"]).EqualsIgnoreCase("payway_01"))
                {
                    if (newData["faccount"].IsNullOrEmptyOrWhiteSpace())
                    {
                        errorMessage = $"{this.HtmlForm.Caption}【{newData["fbillno"]}】的【支付方式】=“账户支付”,【账户】字段必填！";
                        return false;
                    }
                }
              
                var fcontactunitid = Convert.ToString(newData["fcontactunitid"]);
                if (Convert.ToString(newData["fway"]).EqualsIgnoreCase("payway_13") && fcontactunitid.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMessage = $"支付方式为“商场代收” “代收单位”不能为空!";
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var profileService = this.Container.GetService<ISystemProfile>();
                var hasvoucherwhenreceivable = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fhasvoucherwhenreceivable", false); //收款必填小票号
                var image = Convert.ToString(newData["fimage"]);
                if (hasvoucherwhenreceivable && image.IsNullOrEmptyOrWhiteSpace())
                {
                    var bizdirection = Convert.ToString(newData["fbizdirection"]);//业务方向
                    var way = Convert.ToString(newData["fway"]);//支付方式
                    //支付方式 == 账户支付、现金时不需要验证凭证上传
                    if (bizdirection == "bizdirection_01" && way != "payway_01" && way != "payway_05") return false;
                }
                return true;
            }).WithMessage("收款必须上传凭证！"));

            string receiptMsg = "【收款小票号】必填！";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var profileService = this.Container.GetService<ISystemProfile>();
                var mustinvoicenumber = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fmustinvoicenumber", false); //收款必填小票号
                var receiptNo = Convert.ToString(newData["freceiptno"]);
                var way = Convert.ToString(newData["fway"]);//支付方式
                if (mustinvoicenumber && receiptNo.IsNullOrEmptyOrWhiteSpace() && way != "payway_01")
                {
                    //收款小票号为空且支付方式不为：账户支付，账户方向不为：增
                    var direction = Convert.ToString(newData["fdirection"]);//账户方向
                    var fsourceformid = Convert.ToString(newData["fsourceformid"]);//源单类型
                    if (fsourceformid == "ydj_customer" && direction == "direction_01")
                    {
                        receiptMsg = "【收款小票号】必填！";
                        return false;
                    }
                    else if (fsourceformid != "ydj_customer" && direction == "direction_02")
                    {
                        receiptMsg = "【收款小票号】必填！";
                        return false;
                    }
                }
                //else
                //{
                //    decimal money = Convert.ToDecimal(newData["famount"]);
                //    if (money > 0.01M)
                //    {
                //        var incomeObjs = this.Context.LoadBizDataByFilter("coo_incomedisburse", $" freceiptno='{receiptNo}' AND famount='{money}'");
                //        if (incomeObjs != null && incomeObjs.Any())
                //        {
                //            receiptMsg = "该笔收款金额的“收款小票号”录入重复，请检查！";
                //            return false;
                //        }
                //    }
                //}
                return true;
            }).WithMessage(receiptMsg));

            e.Rules.Add(new Validation_CheckExcess());
            e.Rules.Add(new SaveValidation());
        }

        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            if (this.HtmlForm.GetField("frenewalflag") != null)
            {
                // 判断源单如果是销售合同，则将【焕新订单标记】同步更新收支记录
                var incomeDisburseFromOrders =
                    e.DataEntitys.Where(s => Convert.ToString(s["fsourceformid"]).EqualsIgnoreCase("ydj_order"));
                if (incomeDisburseFromOrders.Any())
                {
                    var orderMeta = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
                    if (orderMeta.GetField("frenewalflag") != null)
                    {

                        var orderIds = incomeDisburseFromOrders.Select(s => Convert.ToString(s["fsourceid"])).ToList();
                        var orderObjs = this.Context.LoadBizBillHeadDataById("ydj_order", orderIds, "frenewalflag");
                        foreach (var incomeDisburse in incomeDisburseFromOrders)
                        {
                            var orderId = Convert.ToString(incomeDisburse["fsourceid"]);
                            var orderObj = orderObjs.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(orderId));
                            if (orderObj != null)
                            {
                                incomeDisburse["frenewalflag"] =
                                    Convert.ToString(orderObj["frenewalflag"]).EqualsIgnoreCase("1");
                            }
                        }
                    }
                }
            }
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var targetEntryEntity = this.HtmlForm.GetEntryEntity("fdutyentry");
            foreach (var item in e.DataEntitys)
            {
                var dutyEntry = item["fdutyentry"] as DynamicObjectCollection;
                if (dutyEntry == null || dutyEntry.Count == 0)
                {
                    //当判断到没有符合条件的数据，默认预置当前销售员为联合开单的销售员
                    DynamicObject targetItem = new DynamicObject(targetEntryEntity.DynamicObjectType);
                    targetItem["fismain"] = "1";
                    targetItem["fdutyid"] = item["fstaffid"];
                    targetItem["fdeptid_ed"] = item["fdeptid"];
                    targetItem["fratio"] = "100";
                    targetItem["famount_ed"] = item["famount"];
                    targetItem["fdescription_ed"] = item["fdescription"];
                    dutyEntry.Add(targetItem);
                }
                CalculateStaff(this.Context,item);
            }

        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            // 初始化收支记录
            var incomeDisburseService = this.Container.GetService<IIncomeDisburseService>();
            incomeDisburseService.InitIncomeDisburse(this.Context, e.DataEntitys);

            // 反写源单字段
            this.BackWriteSourceBill(e.DataEntitys);

            // 自动提交
            if (e.DataEntitys.Any())
            {
                var notAutoSubmit = this.GetQueryOrSimpleParam<bool>("NotAutoSubmit");
                if (!notAutoSubmit)
                {
                    var result = this.Gateway.InvokeBillOperation(
                        this.Context,
                        "coo_incomedisburse",
                        e.DataEntitys,
                        "submitflow",
                        new Dictionary<string, object>());
                    result.ThrowIfHasError(true, "收支记录自动提交失败！");
                }
            }
        }

        /// <summary>
        /// 反写源单字段
        /// </summary>
        /// <param name="dataEntities"></param>
        private void BackWriteSourceBill(DynamicObject[] dataEntitys)
        {
            var sourceFormIds = new string[] { "ydj_order", "ydj_purchaseorder" };
            var groups = dataEntitys
                .Where(x =>
                {
                    var _sourceFormId = Convert.ToString(x["fsourceformid"]);
                    var purpose = Convert.ToString(x["fpurpose"]);
                    var bizStatus = Convert.ToString(x["fbizstatus"]);

                    // 是否需要处理
                    var needProc =
                        !bizStatus.EqualsIgnoreCase("bizstatus_03") &&
                        !purpose.EqualsIgnoreCase("bizpurpose_04") &&
                        !purpose.EqualsIgnoreCase("bizpurpose_06");

                    return !_sourceFormId.IsNullOrEmptyOrWhiteSpace()
                    && sourceFormIds.Contains(_sourceFormId, StringComparer.OrdinalIgnoreCase)
                    && needProc;
                }).GroupBy(x => Convert.ToString(x["fsourceformid"]).ToLower());
            if (!groups.Any()) return;

            var dm = this.Container.GetService<IDataManager>();
            foreach (var group in groups)
            {
                var sourceFormId = group.Key;
                var sourceForm = this.MetaModelService?.LoadFormModel(this.Context, sourceFormId);
                dm.InitDbContext(this.Context, sourceForm.GetDynamicObjectType(this.Context));

                // 源单ID集合
                var sourceIds = group
                    .Select(x => Convert.ToString(x["fsourceid"]))
                    .Where(x => !x.IsNullOrEmptyOrWhiteSpace())
                    .Distinct()
                    .ToList();
                if (sourceIds == null || !sourceIds.Any()) return;

                // 源单数据包
                var sourceBills = dm.Select(sourceIds).OfType<DynamicObject>().ToArray();
                if (sourceBills == null || !sourceBills.Any()) return;

                // 批量加载源单关联的收支金额
                var sourceReceiptRefunds = IncomeDisburseHelper.LoadOrderReceiptRefundAmount(
                    this.Context,
                    sourceFormId,
                    sourceIds,
                    null,
                    new List<string>
                    {
                    IncomeTypeConsts.receiptUnConfirmed
                    });

                foreach (var incomeDisburse in group)
                {
                    switch (sourceFormId)
                    {
                        case "ydj_order":
                            this.BackWriteOrder(incomeDisburse, sourceBills, sourceReceiptRefunds);
                            break;
                        case "ydj_purchaseorder":
                            this.BackPurOrder(incomeDisburse, sourceBills, sourceReceiptRefunds);
                            break;
                    }
                }

                // 保存源单数据
                dm.Save(sourceBills);
            }
        }

        /// <summary>
        /// 反写销售合同
        /// </summary>
        /// <param name="incomeDisburse">收支记录数据包</param>
        /// <param name="sourceBills">销售合同数据包</param>
        /// <param name="sourceReceiptRefunds">销售合同收退款金额</param>
        private void BackWriteOrder(
            DynamicObject incomeDisburse,
            DynamicObject[] sourceBills,
            DynamicObjectCollection sourceReceiptRefunds)
        {
            var sourceId = Convert.ToString(incomeDisburse["fsourceid"]);
            var sourceBill = sourceBills?.FirstOrDefault(x => Convert.ToString(x["id"]).EqualsIgnoreCase(sourceId));
            if (sourceBill == null) return;

            var receiptUnConfirmedSum = IncomeDisburseHelper.FindSourceReceiptRefundAmount(sourceReceiptRefunds, sourceId, IncomeTypeConsts.receiptUnConfirmed);


            this.Logger.WriteLog(this.Context, new LogEntry
            {
                BillIds = incomeDisburse["id"] as string,
                BillNos = incomeDisburse["fbillno"] as string,
                BillFormId = this.HtmlForm.Id,
                OpName = "收支记录保存",
                OpCode = this.OperationNo,
                Content = "执行了【保存】操作，更新销售合同【收款待确认】。销售合同编号：{2}。原值：{0}。新值：{1}".Fmt(sourceBill["freceivabletobeconfirmed"], receiptUnConfirmedSum, sourceBill["fbillno"]),
                DebugData = "执行了【保存】操作，更新销售合同【收款待确认】。销售合同编号：{2}。原值：{0}。新值：{1}".Fmt(sourceBill["freceivabletobeconfirmed"], receiptUnConfirmedSum, sourceBill["fbillno"]),
                Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                Level = Enu_LogLevel.Info.ToString(),
                LogType = Enu_LogType.RecordType_03
            });
            // 更新合同【收款待确认】
            sourceBill["freceivabletobeconfirmed"] = receiptUnConfirmedSum;


            // 订单服务
            var orderService = this.Container.GetService<IOrderService>();
            orderService.CalculateUnreceived(this.Context, new[] { sourceBill });
        }

        /// <summary>
        /// 反写采购订单
        /// </summary>
        /// <param name="incomeDisburse">收支记录数据包</param>
        /// <param name="sourceBills">销售合同数据包</param>
        /// <param name="sourceReceiptRefunds">采购订单收退款金额</param>
        private void BackPurOrder(
            DynamicObject incomeDisburse,
            DynamicObject[] sourceBills,
            DynamicObjectCollection sourceReceiptRefunds)
        {
            var sourceId = Convert.ToString(incomeDisburse["fsourceid"]);
            var sourceBill = sourceBills?.FirstOrDefault(x => Convert.ToString(x["id"]).EqualsIgnoreCase(sourceId));
            if (sourceBill == null) return;

            var receiptUnConfirmedSum = IncomeDisburseHelper.FindSourceReceiptRefundAmount(sourceReceiptRefunds, sourceId, IncomeTypeConsts.receiptUnConfirmed);

            // 采购订单【收款待确认】
            sourceBill["fconfirmamount"] = receiptUnConfirmedSum;
            //【待结算金额】= 【成交金额】-【已付金额】-【待确认金额】
            sourceBill["fpayamount"] = Convert.ToDecimal(sourceBill["ffbillamount"]) - Convert.ToDecimal(sourceBill["fsettleamount"]) - receiptUnConfirmedSum;
        }
        
        /// <summary>
        /// 计算导购员信息(联合开单)
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="order"></param>
        private void CalculateStaff(UserContext userCtx, DynamicObject incomeDisburseDy)
        {
            var dutyEntrys = incomeDisburseDy["fdutyentry"] as DynamicObjectCollection;

            if (dutyEntrys == null || dutyEntrys.Count <= 0) { return; }
            decimal sumAmount = Convert.ToDecimal(incomeDisburseDy["famount"]);

            foreach (var entry in dutyEntrys)
            {
                var ratio = Convert.ToDecimal(entry["fratio"]);
                if (ratio <= 0)
                {
                    entry["famount_ed"] = 0;
                    continue;
                }
                //金额 = 订单总额 * 行所占百分比
                entry["famount_ed"] = decimal.Round(sumAmount * ratio / 100, 2);
            }
        }
    }
}