using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MT.SelectionForm
{
    /// <summary>
    /// 采购订单：审核
    /// </summary>
    [InjectService]
    [FormId("mt_selectionform")]
    [OperationNo("showselection")]
    public class ShowSelection : AbstractOperationServicePlugIn
    {
        private bool CheckXPM()
        {
            var materialid = this.GetQueryOrSimpleParam<string>("materialid");
            if (materialid.IsNullOrEmptyOrWhiteSpace())
            {
                return false;
            }
            var sql = @"SELECT TOP 1 1 FROM T_BD_AUXPROPVALUEMAP a
INNER JOIN t_bd_auxproperty c ON a.fauxpropid = c.fid
WHERE a.fmaterialid = @fmaterialid AND a.fmainorgid = @fmainorgid AND c.fnumber = 'XPM'";
            return this.DBService.ExecuteDynamicObject(this.Context, sql, new[]
            {
                new SqlParam("fmaterialid", System.Data.DbType.String, materialid),
                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company)
            }).Count > 0;
        }

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            if (!CheckXPM())
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add("商品没有启用选配码辅助属性！");
                return;
            }

            var rowid = this.GetQueryOrSimpleParam<string>("rowid");
            var materialid = this.GetQueryOrSimpleParam<string>("materialid");

            var meta = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "mt_selectionform");
            var parentId = this.CurrentPageId.IsNullOrEmptyOrWhiteSpace() ? Guid.NewGuid().ToString() : this.CurrentPageId;

            var suiteselectionid = this.GetQueryOrSimpleParam<string>("suiteselectionid", "");
            var selectionnumber = this.GetQueryOrSimpleParam<string>("selectionnumber", "");
            //选配产品是否套件
            var issuite = this.GetQueryOrSimpleParam<string>("issuite", "") == "1";
            //是否套件选配
            var issuiteselection = true;
            //是否返回了套件
            var isusesuiteselection = true;
            if (selectionnumber.IsNullOrEmptyOrWhiteSpace())
            {
                //没有选配码，根据产品是否套件判断是套件选配还是子件选配。
                isusesuiteselection = issuite;
                issuiteselection = issuite;
            }
            else
            {
                //选配码不为空，代表是修改
                if (!issuite)
                {
                    if (suiteselectionid.IsNullOrEmptyOrWhiteSpace())
                    {
                        //产品是子件，也没有所属套件，代表：
                        //是子件选配
                        issuiteselection = false;
                        //只能非返回套件
                        isusesuiteselection = false;
                    }
                    else
                    {
                        //产品是子件，有所属套件，代表：
                        //是套件选配
                        issuiteselection = true;
                        //非返回套件
                        isusesuiteselection = false;
                        //是套件
                        issuite = true;
                    }
                }
            }

            DynamicObjectCollection partsselections;

            if (issuiteselection)
            {
                //套件选配，获取套件相关的子件内容作为模板
                var sql = @"SELECT b.fpartproductid, c.fname AS productname, c.fnumber AS productnumber, 
b.fpartsselectionid, ISNULL(d.fname, '') AS partsname, ISNULL(d.fnumber, '') AS partsnumber,
c.fselectioncategoryid, e.fname AS categoryname, e.fnumber AS categorynumber,
b.fqty, b.fwhetherdefault
FROM T_MT_SUITE a
INNER JOIN T_MT_SUITEENTRY b ON a.fid = b.fid
INNER JOIN T_BD_MATERIAL c ON b.fpartproductid = c.fid
INNER JOIN T_MT_SELECTIONCATEGORY e ON e.fid = c.fselectioncategoryid
LEFT JOIN T_MT_PARTSSELECTION d ON b.fpartsselectionid = d.fid
WHERE a.fproductid = @fproductid AND e.fmainorgid = @orgid";
                partsselections = this.DBService.ExecuteDynamicObject(this.Context, sql, new[] { 
                    new SqlParam("fproductid", System.Data.DbType.String, materialid),
                    new SqlParam("orgid", System.Data.DbType.String, this.Context.Company)
                });
            }
            else
            {
                //子件选配，获取子件的信息
                var sql = @"SELECT c.fid AS fpartproductid, c.fname AS productname, c.fnumber AS productnumber, 
c.fselectioncategoryid, e.fname AS categoryname, e.fnumber AS categorynumber,
d.fid AS fpartsselectionid, d.fname AS partsname, d.fnumber AS partsnumber,
1 as fqty
FROM T_BD_MATERIAL c
INNER JOIN T_MT_SELECTIONCATEGORY e ON e.fid = c.fselectioncategoryid
LEFT JOIN (
SELECT fid, fname, fnumber, fproductid FROM T_MT_PARTSSELECTION
WHERE fnumber = @fnumber
) d ON d.fproductid = c.fid
WHERE c.fid = @fproductid AND e.fmainorgid = @orgid";
                partsselections = this.DBService.ExecuteDynamicObject(this.Context, sql, new[] {
                    new SqlParam("fproductid", System.Data.DbType.String, materialid),
                    new SqlParam("fnumber", System.Data.DbType.String, selectionnumber.IsNullOrEmptyOrWhiteSpace() ? "X" : selectionnumber), //子件选配码为空时，使用不会存在的“X”作为筛选
                    new SqlParam("orgid", System.Data.DbType.String, this.Context.Company)
                });
            }

            //如果是套件重新选配时，使用原来的物料和套件选配的信息
            if (issuiteselection)
            {
                var sql = "SELECT TOP 1 fid, fproductid, fnumber FROM T_MT_SUITESELECTION WHERE fmainorgid = @orgid AND ";
                List<SqlParam> param = new List<SqlParam>();
                param.Add(new SqlParam("orgid", System.Data.DbType.String, this.Context.Company));
                if (suiteselectionid.IsNullOrEmptyOrWhiteSpace())
                {
                    //套件选配，且所属套件ID为空时，使用选配码确认获取原来的选配信息
                    sql += "fnumber = @fnumber";
                    param.Add(new SqlParam("fnumber", System.Data.DbType.String, selectionnumber));
                }
                else
                {
                    //套件选配，且所属套件ID不为空时，是返回子件的选配，使用所属套件ID确认获取原来的选配信息
                    sql += "fid = @fid";
                    param.Add(new SqlParam("fid", System.Data.DbType.String, suiteselectionid));
                }
                
                var es = this.DBService.ExecuteDynamicObject(this.Context, sql, param)
                        .FirstOrDefault();
                if (es != null)
                {
                    suiteselectionid = es["fid"].ToString();
                    materialid = es["fproductid"].ToString();
                    selectionnumber = es["fnumber"].ToString();
                }
            }

            var selectionRule = GetSelectionRule(materialid);

            var action = this.Context.ShowSpecialForm(
                meta,
                null,
                true,
                parentId,
                Enu_OpenStyle.Modal,
                Enu_DomainType.Dynamic,
                null,
                (formPara) =>
                {
                    formPara.DomainType = Enu_DomainType.Dynamic;
                    formPara.OpenStyle = Enu_OpenStyle.Modal;
                    formPara.CustomParameter["rowid"] = rowid;
                    formPara.CustomParameter["materialid"] = materialid;
                    formPara.CustomParameter["partsselections"] = partsselections.ToJson();
                    formPara.CustomParameter["parentpageid"] = parentId;
                    formPara.CustomParameter["selectionnumber"] = selectionnumber;
                    formPara.CustomParameter["suiteselectionid"] = suiteselectionid;
                    formPara.CustomParameter["isusesuiteselection"] = isusesuiteselection ? 1 : 0;
                    formPara.CustomParameter["issuiteselection"] = issuiteselection ? 1 : 0;
                    formPara.CustomParameter["issuite"] = issuite ? 1 : 0;
                    formPara.CustomParameter["selectionrule"] = selectionRule;
                }
            );
            this.Result.IsSuccess = true;
            this.Result.ComplexMessage?.SuccessMessages.Clear();
            this.Result.HtmlActions.Add(action);

        }

        private string GetSelectionRule(string productId)
        {
            var sql = @"SELECT d.frule
FROM T_MT_SELECTIONRANGE a
INNER JOIN T_MT_SRPRODUCTENTRY b ON a.fid = b.fid
INNER JOIN T_MT_SELECTIONRULE c ON a.fid = c.fselectionrangeid
INNER JOIN T_MT_SELECTIONRULEENTRY d ON c.fid = d.fid
WHERE b.fproductid = @fproductid AND d.frowauditstatus = 'A' AND c.fstatus = 'E' AND a.fmainorgid = @orgid
ORDER BY d.fpriority, d.fentryid ASC";
            var rules = this.DBService.ExecuteDynamicObject(this.Context, sql, new[] { 
                new SqlParam("fproductid", System.Data.DbType.String, productId),
                new SqlParam("orgid", System.Data.DbType.String, this.Context.Company)
            });
            return rules.ToJson();
        }
    }
}
