using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sec
{
    /// <summary>
    /// 保存角色权限信息到认证站点：用于角色授权界面
    /// </summary>
    [InjectService]
    [FormId("sec_role")]
    [OperationNo("QueryControlFieldPermission")]
    public class QueryControlFieldPermission : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var agentObj = this.Context.LoadBizBillHeadDataById("bas_agent", this.Context.Company, "fiscarrysupnoprice");
            var isContrl = false;
            if (agentObj != null)
            {
                isContrl = Convert.ToInt32(agentObj["fiscarrysupnoprice"]) == 1;
            }

            this.Result.IsSuccess = true;
            this.Result.SrvData = isContrl;
        }
    }
}
