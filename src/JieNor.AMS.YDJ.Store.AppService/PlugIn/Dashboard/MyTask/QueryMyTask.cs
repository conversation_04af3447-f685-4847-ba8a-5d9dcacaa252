using System;
using System.Collections.Generic;
using System.Diagnostics.Eventing.Reader;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;
using JieNor.Framework.Interface.BfTask;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.Validator;
using System.Dynamic;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.AMS.YDJ.Store.AppService.Helper;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Dashboard.MyTask
{
    /// <summary>
    /// 仪表盘：我的待办任务
    /// </summary>
    /// <remarks>
    /// 1.我的任务
    /// 任务状态=执行中，且任务执行人=当前登录用户
    /// 2.我发起的
    /// 任务状态 = 执行中，且任务创建人=当前登录用户
    /// 3.我参与的
    /// 任务状态 = 执行中，且任务的参与人包含当前登录用户
    /// 4.我处理过的
    /// 审批流实例 从最新开始节点以后去获得 通过、驳回、终止的记录。
    /// </remarks>
    /// 
    [InjectService]
    [FormId("dashboard_mytask")]
    [OperationNo("QueryMyTask")]
    public class QueryMyTask : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 查询类型：
        /// mytask：我的待办任务
        /// myallot：我分配的任务
        /// mytakepartin：我参与的任务
        /// mydonetask 我处理过的任务
        /// </summary>
        public string SearchType { get; set; }

        /// <summary>
        /// 操作执行结束处理单元
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var searchType = this.GetQueryOrSimpleParam<string>("searchType", "");
            var orderByString = this.GetQueryOrSimpleParam<string>("orderByString", "");
            var pageIndex = this.GetQueryOrSimpleParam<int>("pageIndex", 1);
            var pageSize = this.GetQueryOrSimpleParam<int>("pageSize", 10);
            // 搜索范围：all：全部；auidtflow：审批；task：任务
            var searchScope = this.GetQueryOrSimpleParam<string>("searchScope", "all");

            var searchKeyword = this.GetQueryOrSimpleParam<string>("searchKeyword", "");
            var dataScope = this.GetQueryOrSimpleParam<string>("dataScope", "");
            var KeywordStr = string.Empty;

            if (searchType.IsNullOrEmptyOrWhiteSpace()) searchType = "mytask";
            if (pageIndex <= 0) pageIndex = 1;
            if (pageSize <= 0) pageSize = 10;
            this.SearchType = searchType.ToLower();

            var searchFilter = this.GetQueryOrSimpleParam<string>("searchFilter", "");

            //查询条件
            var where1 = "";
            var where2 = "";
            switch (this.SearchType)
            {
                //我待处理的任务
                case "mytask":
                    where1 = $" and tk.fcreatorid='{this.Context.UserId}'";
                    where2 = $" and fdealuser='{this.Context.UserName}' and fdatatype=1";

                    if (orderByString.IsNullOrEmptyOrWhiteSpace()) orderByString = "fcreatedate asc";
                    break;
                //我参与的任务
                case "mytakepartin":
                    where1 = $" and charindex('{this.Context.UserId}', fjoiner)>0";
                    where2 = $" and fdealuser='{this.Context.UserName}' and fdatatype=2";

                    if (orderByString.IsNullOrEmptyOrWhiteSpace()) orderByString = "ftaskstatus asc,fcreatedate desc";
                    break;
                //我分配的任务（审批流创建人/发起人）
                case "myallot":
                    where1 = $" and tk.fexcuter='{this.Context.UserId}'";
                    where2 = $" and (fdealuser='{this.Context.UserName}' or fcreatorid='{this.Context.UserId}') and fdatatype=3";

                    if (orderByString.IsNullOrEmptyOrWhiteSpace()) orderByString = "ftaskstatus asc,fcreatedate desc";
                    break;
                case "myhasdone":
                    if (orderByString.IsNullOrEmptyOrWhiteSpace()) orderByString = "ftaskstatus asc,fcreatedate desc";
                    break;
                default:
                    if (orderByString.IsNullOrEmptyOrWhiteSpace()) orderByString = "fcreatedate desc";
                    where2 = $"and fdealuser='{this.Context.UserName}'";
                    break;
            }

            // 查询分页数据
            // 特殊处理：增加搜索范围判断
            var selectDataSqlSections = new List<string>();
            var selectCountSqlSections = new List<string>();
            if (searchScope == "all" || searchScope == "task")
            {
                selectDataSqlSections.Add($@"
select 
	tk.fid as ftaskid,
	tk.ftaskname,
	tk.fnote,
	su.fname as fexcuter,
	tk.ftaskstatus,
	ee.fenumitem as ftaskstatustxt,
	tk.fenddate,
	u.fname as fcreator,
    dp.fname as fdeptname,
	tk.fcreatedate,
	ftaskprocessor,
	tk.ftasksource as fbizformid,
	tk.ftaskman as fbizbillpkid,
	'' as fbizform,
	'' as fbizbillno, 
	u.fname as fsubmitter, 
	u.fname as foperator
from t_bf_task tk with(nolock) 
left join t_sec_user u with(nolock) on tk.fcreatorid=u.fid
left join t_sec_user su with(nolock) on tk.fexcuter=su.fid 
left join t_bd_enumdataentry ee with(nolock) on tk.ftaskstatus=fentryid 
left join t_bd_staff as sf with(nolock) on sf.flinkuserid = u.fid 
left join t_bd_department as dp with(nolock) on dp.fid = sf.fdeptid 
where tk.fmainorgid='{this.Context.Company}' and tk.ftaskprocessor<>'' and tk.ftaskstatus='taskstatus_02' {where1}");

                selectCountSqlSections.Add($@"select fid from t_bf_task tk with(nolock) 
                where fmainorgid='{this.Context.Company}' and ftaskprocessor<>'' and ftaskstatus='taskstatus_02' {where1}");
            }

            if (searchScope == "all" || searchScope == "auidtflow")
            {
                var bizFormIds = BusinessApprovalHelper.LoadBusinessApprovalBizFormIds(this.Context);

                var fiFilter = this.BuildFlowInstanceIdFilter(bizFormIds);

                var aclFilter = this.BuildDataRowAclFilter(bizFormIds);

                //根据小程序【审批】搜索栏过滤
                if (!searchKeyword.IsNullOrEmptyOrWhiteSpace())
                {
                    KeywordStr = " (fbizbillno like  '%" + searchKeyword + "%' " +
                                    " or fflowname like  '%" + searchKeyword + "%' " +
                                    " or finitiator like  '%" + searchKeyword + "%' " +
                                    " or fbizform like  '%" + searchKeyword + "%') and ";

                    if (this.SearchType.EqualsIgnoreCase("myhasdone") && dataScope != "all")
                    {
                        KeywordStr = " (a.fbizbillno like  '%" + searchKeyword + "%' " +
                                    " or a.fflowname like  '%" + searchKeyword + "%' " +
                                    " or a.finitiator like  '%" + searchKeyword + "%' " +
                                    " or a.fbizformid like  '%" + searchKeyword + "%') and ";
                    }
                }


                // 数据范围   根据小程序【审批】 我的 / 所有 过滤
                switch (dataScope?.ToLowerInvariant())
                {
                    case "all":
                        switch (this.SearchType)
                        {
                            //待办
                            case "mytask":
                                where2 = $" and fdatatype=2";
                                orderByString = "fcreatedate asc";
                                break;
                            //已办
                            case "myhasdone":
                                where2 = $" and fflowstatus='flowinstancestatus_02'";
                                orderByString = "fcreatedate desc";
                                break;
                            //我提交
                            case "myallot":
                                where2 = $" and fdatatype=1";
                                orderByString = "fcreatedate desc";
                                break;
                        }
                        break;
                }

                selectDataSqlSections.Add($@"
select distinct 
	fid as ftaskid,
	'auditflow' as ftaskname,
	fcontent as fnote,
	fexecutors as fexcuter,
	fflowstatus as ftaskstatus,
	fflowstatustxt as ftaskstatustxt,
	NULL as fenddate,
	fexeccreteor as fcreator,
    fdeptname,
	fcreatedate,
	'approvalflow' as ftaskprocessor,
	fbizformid,
	fbizbillpkid,
	fbizform,
	fbizbillno, 
	fsubmitter,
	foperator
from v_bpm_flowmsg with(nolock)
where {KeywordStr} fmainorgid='{this.Context.Company}' {where2} {fiFilter} {aclFilter}");

                selectCountSqlSections.Add($@"select distinct fid from v_bpm_flowmsg with(nolock) 
		        where {KeywordStr} fmainorgid='{this.Context.Company}'  {where2} {fiFilter} {aclFilter}");
            }

            var selectDataSql = $@"
            select top {pageSize} * from 
            (
	            select *,row_number() over(order by {orderByString}) as fjnidentity from 
	            (
		            {string.Join("\r\n union all \r\n", selectDataSqlSections)}
	            ) t1
            ) t2 where fjnidentity>{pageSize * (pageIndex - 1)}";


            //查询总记录数
            var selectCountSql = $@"
            select count(1) from 
	        (
                {string.Join("\r\n union all \r\n", selectCountSqlSections)}		        
	        ) t1";

            #region 如果是我的消息，则直接查询 ftaskprocessor 字段为空的任务
            if (this.SearchType.EqualsIgnoreCase("mymsg"))
            {
                var msgWhere = "";
                var readStatus = this.GetQueryOrSimpleParam<string>("readStatus", "");
                if (!readStatus.IsNullOrEmptyOrWhiteSpace())
                {
                    msgWhere = readStatus.EqualsIgnoreCase("read") ? "已读" : readStatus.EqualsIgnoreCase("unread") ? "未读" : "";
                    if (!msgWhere.IsNullOrEmptyOrWhiteSpace())
                    {
                        msgWhere = $" where ftaskstatustxt='{msgWhere}' ";
                    }
                }

                selectDataSql = $@"
                select top {pageSize} * from 
                (
	                select *,row_number() over(order by ftaskstatustxt,{orderByString}) as fjnidentity from 
	                (
                        select * from 
                        (
	                        select 
                                tk.fid as ftaskid,
                                tk.ftaskname,
                                tk.fnote,'' as fexcuter,
                                tk.ftaskstatus,
                                (case tr.freadstatus when '2' then '已读' else '未读' end) as ftaskstatustxt,
                                tr.freaddate,
                                tk.fenddate,
                                u.fname as fcreator,
                                tk.fcreatedate,
                                ftaskprocessor,
                                tk.ftasksource as fbizformid,
                                tk.ftaskman as fbizbillpkid,
                                u.fname as foperator
                            from t_bf_task tk with(nolock) 
                            left join t_bf_taskreadmsg tr with(nolock) on tk.fid=tr.fid and tk.fexcuter=tr.freaderid 
                            left join t_sec_user u with(nolock) on tk.fcreatorid=u.fid 
                            where tk.fmainorgid='{this.Context.Company}' and tk.fexcuter='{this.Context.UserId}' 
                            and (tk.ftaskprocessor='' or tk.ftaskprocessor='dynamicremind')
                        ) t {msgWhere}
	                ) t1
                ) t2 where fjnidentity>{pageSize * (pageIndex - 1)} order by ftaskstatustxt";

                selectCountSql = $@"
                select count(1) from 
                (
                    select (case tr.freadstatus when '2' then '已读' else '未读' end) as ftaskstatustxt from t_bf_task tk with(nolock)
                    left join t_bf_taskreadmsg tr with(nolock) on tk.fid=tr.fid and tk.fexcuter=tr.freaderid 
                    where tk.fmainorgid='{this.Context.Company}' and tk.fexcuter='{this.Context.UserId}' 
                    and (tk.ftaskprocessor='' or tk.ftaskprocessor='dynamicremind')
                ) t {msgWhere}";
            }
            #endregion

            #region 我处理过的任务，特殊处理，这里改变 selectDataSql 以及 selectCountSql
            if (this.SearchType.EqualsIgnoreCase("myhasdone") && dataScope != "all")
            {
                selectDataSql = $@"
                select top {pageSize} * from 
                (
	                select *,row_number() over(order by {orderByString}) as fjnidentity from (
	                    -- 终止：我有执行过，审批流状态为未提交
	                    select 
		                    a.fid as ftaskid,
		                    'auditflow' ftaskname,
		                    (
			                    select top 1 fcontent 
			                    from t_bpm_sectionmsg with(nolock) 
			                    where fsectionid in (select b.fid from t_bpm_execution b with(nolock) where b.finstanceid = a.fid) 
			                    and freceivernumber='{this.Context.UserName}'
		                    ) fnote,
		                    (
			                    select top 1 fexecutors_txt 
			                    from t_bpm_executionentry with(nolock) 
			                    where fid in (select b.fid from t_bpm_execution b with(nolock) where b.finstanceid = a.fid) 
			                    order by fentryid desc
		                    ) as fexcuter, 
		                    'flowinstancestatus_01' as ftaskstatus,
		                    '处理中' as ftaskstatustxt,
		                    NULL as fenddate,
		                    u.fname as fcreator,
		                    a.fcreatedate,
		                    'approvalflow' as ftaskprocessor,
		                    a.fbizformid,
		                    a.fbizbillpkid,
		                    s.fname as fbizform,
		                    a.fbizbillno,
		                    a.finitiator as fsubmitter,
		                    a.foperator 
	                    from t_bpm_flowinstance a with(nolock)
		                    left join t_sys_bizobject s with(nolock) on s.fid = fbizformid
		                    left join t_sec_user u with(nolock) on u.fid=a.fcreatorid
	                    where {KeywordStr} fflowstatus='flowinstancestatus_03' and a.fid in (
		                    select distinct finstanceid 
		                    from t_bpm_execution with(nolock) 
		                    where fmainorgid='{this.Context.Company}'
		                    and fid in 
		                    (
			                    select fid from t_bpm_executionentry with(nolock)  where fexecnumber ='{this.Context.UserName}'
		                    )
		                    and fopname <> '提交审批流'
	                    )
	                    union all
	                    -- 处理中：我有执行过，审批流状态为处理中，且当前节点不需要我执行（待办和已办，只会同时存在一个）
	                    select 
		                    a.fid as ftaskid,
		                    'auditflow' ftaskname,
		                    (
			                    select top 1 fcontent 
			                    from t_bpm_sectionmsg with(nolock) 
			                    where fsectionid in (select b.fid from t_bpm_execution b with(nolock) where b.finstanceid = a.fid) 
			                    and freceivernumber='{this.Context.UserName}'
			                    order by fid desc
		                    ) fnote,
		                    (
			                    select top 1 fexecutors_txt 
			                    from t_bpm_executionentry with(nolock) 
			                    where fid in (select b.fid from t_bpm_execution b with(nolock) where b.finstanceid = a.fid) 
			                    order by fentryid desc
		                    ) as fexcuter, 
		                    'flowinstancestatus_01' as ftaskstatus,
		                    '处理中' as ftaskstatustxt,
		                    NULL as fenddate,
		                    u.fname as fcreator,
		                    a.fcreatedate,
		                    'approvalflow' as ftaskprocessor,
		                    a.fbizformid,
		                    a.fbizbillpkid,
		                    s.fname as fbizform,
		                    a.fbizbillno,
		                    a.finitiator as fsubmitter,
		                    a.foperator 
	                    from t_bpm_flowinstance a with(nolock)
		                    left join t_sys_bizobject s with(nolock) on s.fid = fbizformid
		                    left join t_sec_user u with(nolock) on u.fid=a.fcreatorid
	                    where fflowstatus='flowinstancestatus_01' and a.fid in (
		                    select distinct finstanceid 
		                    from t_bpm_execution with(nolock) 
		                    where fmainorgid='{this.Context.Company}'
		                    and fid in 
		                    (
			                    select fid from t_bpm_executionentry with(nolock) where fexecnumber ='{this.Context.UserName}'
		                    )
		                    and fopname <> '提交审批流'
		                    and exists (select fentryid from t_bpm_executionentry with(nolock) where fid=a.fcurrentnode and fexecutors not like '%{this.Context.UserName}%') 
	                    )
	                    union all
	                    -- 已完成：我有执行过，审批流状态为已完成
	                    select 
		                    a.fid as ftaskid,
		                    'auditflow' ftaskname,
		                    (
			                    select top 1 fcontent 
			                    from t_bpm_sectionmsg with(nolock) 
			                    where fsectionid in (select b.fid from t_bpm_execution b with(nolock) where b.finstanceid = a.fid) 
			                    and freceivernumber='{this.Context.UserName}'
			                    order by fid desc
		                    ) fnote,
		                    (
			                    select top 1 fexec 
			                    from t_bpm_executionentry with(nolock) 
			                    where fid in (select b.fid from t_bpm_execution b with(nolock) where b.finstanceid = a.fid) 
			                    order by fentryid desc
		                    ) as fexcuter, 
		                    'flowinstancestatus_02' as ftaskstatus,
		                    '已完成' as ftaskstatustxt,
		                    NULL as fenddate,
		                    u.fname as fcreator,
		                    a.fcreatedate,
		                    'approvalflow' as ftaskprocessor,
		                    a.fbizformid,
		                    a.fbizbillpkid,
		                    s.fname as fbizform,
		                    a.fbizbillno,
		                    a.finitiator as fsubmitter,
		                    a.foperator 
	                    from t_bpm_flowinstance a with(nolock)
		                    left join t_sys_bizobject s with(nolock) on s.fid = fbizformid
		                    left join t_sec_user u with(nolock) on u.fid=a.fcreatorid
	                    where fflowstatus='flowinstancestatus_02' and a.fid in (
		                    select distinct finstanceid 
		                    from t_bpm_execution with(nolock) 
		                    where fmainorgid='{this.Context.Company}'
		                    and fid in 
		                    (
			                    select fid from t_bpm_executionentry with(nolock) where fexecnumber ='{this.Context.UserName}'
		                    )
		                    and fopname <> '提交审批流'
	                    )
                    ) t1
                ) t2 where fjnidentity>{pageSize * (pageIndex - 1)}";
                //查询总记录数
                selectCountSql = $@"
                select count(1) from 
	            (
	                    -- 终止：我有执行过，审批流状态为未提交
	                    select 
		                    a.fid
	                    from t_bpm_flowinstance a with(nolock)
	                    where {KeywordStr} fflowstatus='flowinstancestatus_03' and a.fid in (
		                    select distinct finstanceid 
		                    from t_bpm_execution with(nolock) 
		                    where fmainorgid='{this.Context.Company}'
		                    and fid in 
		                    (
			                    select fid from t_bpm_executionentry with(nolock) where fexecnumber ='{this.Context.UserName}'
		                    )
		                    and fopname <> '提交审批流'
	                    )
	                    union all
	                    -- 处理中：我有执行过，审批流状态为处理中，且当前节点不需要我执行（待办和已办，只会同时存在一个）
	                    select 
		                    a.fid
	                    from t_bpm_flowinstance a with(nolock)
	                    where fflowstatus='flowinstancestatus_01' and a.fid in (
		                    select distinct finstanceid 
		                    from t_bpm_execution with(nolock) 
		                    where fmainorgid='{this.Context.Company}'
		                    and fid in 
		                    (
			                    select fid from t_bpm_executionentry with(nolock) where fexecnumber ='{this.Context.UserName}'
		                    )
		                    and fopname <> '提交审批流'
		                    and exists (select fentryid from t_bpm_executionentry with(nolock) where fid=a.fcurrentnode and fexecutors not like '%{this.Context.UserName}%') 
	                    )
	                    union all
	                    -- 已完成：我有执行过，审批流状态为已完成
	                    select 
		                    a.fid
	                    from t_bpm_flowinstance a with(nolock)
	                    where fflowstatus='flowinstancestatus_02' and a.fid in (
		                    select distinct finstanceid 
		                    from t_bpm_execution with(nolock) 
		                    where fmainorgid='{this.Context.Company}'
		                    and fid in 
		                    (
			                    select fid from t_bpm_executionentry with(nolock) where fexecnumber ='{this.Context.UserName}'
		                    )
		                    and fopname <> '提交审批流'
	                    )
                ) t1";
            }
            #endregion

            //先对表结构进行构建，以免后面执行sql语句报某个表不存在的错误
            var taskForm = this.MetaModelService.LoadFormModel(this.Context, "bf_task");
            var taskDm = this.GetDataManager();
            taskDm.InitDbContext(this.Context, taskForm.GetDynamicObjectType(this.Context));

            List<Dictionary<string, object>> dataList = new List<Dictionary<string, object>>();
            using (var reader = this.DBService.ExecuteReader(this.Context, selectDataSql))
            {
                while (reader.Read())
                {
                    Dictionary<string, object> data = new Dictionary<string, object>();
                    bool isMatchFilter = searchFilter.IsNullOrEmptyOrWhiteSpace();//是否匹配搜索条件
                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        var objValue = reader[i];
                        var colName = reader.GetName(i);
                        if (colName.EqualsIgnoreCase("fenddate") && !objValue.IsNullOrEmptyOrWhiteSpace())
                        {
                            objValue = Convert.ToDateTime(objValue).ToString("yyyy-MM-dd");
                        }
                        else if (colName.EqualsIgnoreCase("ftaskname") && Convert.ToString(objValue).EqualsIgnoreCase("auditflow"))
                        {
                            objValue = $"{reader.GetString("fbizform")}审批【{reader.GetString("fbizbillno")}】";
                        }
                        else if (colName.EqualsIgnoreCase("fexcuter") && objValue.IsNullOrEmptyOrWhiteSpace())
                        {
                            objValue = reader["foperator"];
                        }
                        //判断是否包含搜索条件
                        var cvtValue = objValue.ToString();
                        if (colName.EqualsIgnoreCase("fcreatedate"))
                        {
                            cvtValue = Convert.ToDateTime(objValue).ToString("yyyy-MM-dd HH:mm:ss");
                        }
                        if (!isMatchFilter && cvtValue.Contains(searchFilter))
                        {
                            isMatchFilter = true;
                        }
                        data[colName] = objValue.IsNullOrEmptyOrWhiteSpace() ? "" : objValue;
                    }
                    if (isMatchFilter)
                    {
                        dataList.Add(data);
                    }
                }
            }

            var records = 0;
            using (var reader = this.DBService.ExecuteReader(this.Context, selectCountSql))
            {
                if (reader.Read())
                {
                    records = Convert.ToInt32(reader[0]);
                }
            }

            //构建任务操作按钮
            this.BuildTaskOpMetaInfo(dataList);

            this.Result.SrvData = new
            {
                dataList = dataList,
                pageIndex = pageIndex,
                pageCount = decimal.ToInt64(Math.Ceiling(records * 1.0m / pageSize)),
                records = records
            };
        }

        /// <summary>
        /// 构建业务表单流程实例ID的过滤条件
        /// </summary>
        private string BuildFlowInstanceIdFilter(List<string> bizFormIds)
        {
            var filters = new List<string>();

            foreach (var bizFormId in bizFormIds)
            {
                var bizForm = this.MetaModelService.LoadFormModel(this.Context, bizFormId);
                if (bizForm.BillHeadTableName.IsNullOrEmptyOrWhiteSpace()) continue;

                if (!bizForm.ApprovalFlow) continue;

                if (bizForm.ElementType != HtmlElementType.HtmlForm_BaseForm
                    && bizForm.ElementType != HtmlElementType.HtmlForm_BillForm) continue;

                filters.Add($"exists(select 1 from {bizForm.BillHeadTableName} with(nolock) where fflowinstanceid=v_bpm_flowmsg.fid)");
            }

            var filter = "";
            if (filters.Any())
            {
                filter = $" and ({string.Join(" or ", filters)}) ";
            }

            return filter;
        }

        /// <summary>
        /// 构建数据范围的过滤条件（数据权限隔离）
        /// </summary>
        private string BuildDataRowAclFilter(List<string> bizFormIds)
        {
            if (!bizFormIds.Any()) return "";

            // 当前用户能查看的业务数据ID
            var bizDataIds = new List<object>();

            foreach (var bizFormId in bizFormIds)
            {
                var bizForm = this.MetaModelService.LoadFormModel(this.Context, bizFormId);
                if (bizForm.BillHeadTableName.IsNullOrEmptyOrWhiteSpace()) continue;

                if (!bizForm.ApprovalFlow) continue;

                if (bizForm.ElementType != HtmlElementType.HtmlForm_BaseForm
                    && bizForm.ElementType != HtmlElementType.HtmlForm_BillForm) continue;

                var queryParam = new SqlBuilderParameter(this.Context, bizForm.Id);
                queryParam.ReadDirty = true;
                queryParam.QueryUserFieldOnly = true;
                queryParam.NoColorSetting = true;
                queryParam.PageIndex = -1;
                queryParam.PageCount = -1;
                queryParam.SelectedFieldKeys.Add(bizForm.BillPKFldName);

                var queryObj = QueryService.BuilQueryObject(queryParam);

                if (bizFormIds.Count == 1)
                {
                    // 如果当前业务表单没有数据范围的过滤条件，则不处理
                    if (!queryParam.WhereString.Any(o => o.Memo.EqualsIgnoreCase("/*角色授权中定义的数据范围*/")))
                    {
                        return "";
                    }
                }

                // 目前此处只能循环挨个查，因为每个业务表单的查询参数都可能不一样，且无法干预平台的参数名和参数值
                var queryText = $@"{queryObj.SqlSelect} {queryObj.SqlFrom} {queryObj.SqlWhere}";
                var dynObjs = this.DBService.ExecuteDynamicObject(this.Context, queryText, queryParam.DynamicParams);

                bizDataIds.AddRange(dynObjs.Select(o => o["fbillhead_id"]));
            }

            if (!bizDataIds.Any())
            {
                // 如果没有任何可查看的数据，则返回一个不存在的过滤条件，目的是为了查不到数据
                return " and fbizbillpkid='###'";
            }

            if (bizDataIds.IsGreaterThan(50))
            {
                var tempTbl = this.DBService.CreateTempTableWithDataList(Context, bizDataIds);

                this.DBService.DeleteTempTableByName(this.Context, tempTbl, 5);

                return $" and exists(select 1 from {tempTbl} where fid=v_bpm_flowmsg.fbizbillpkid) ";
            }

            if (bizDataIds.Count() == 1)
            {
                return $" and fbizbillpkid='{bizDataIds.First()}'";
            }

            return $" and fbizbillpkid in('{string.Join("','", bizDataIds)}')";
        }

        /// <summary>
        /// 构建任务操作按钮
        /// </summary>
        /// <param name="dataList"></param>
        private void BuildTaskOpMetaInfo(List<Dictionary<string, object>> dataList)
        {
            //当前系统中所有的任务处理器
            Dictionary<string, ITaskProcessor> dicTaskProcessor = new Dictionary<string, ITaskProcessor>(StringComparer.OrdinalIgnoreCase);
            var allTaskProcessors = this.Container.GetAllServiceWithMeta<ITaskProcessor>();
            foreach (var task in allTaskProcessors)
            {
                object taskprocessor;
                task.Item2.TryGetValue("taskprocessor", out taskprocessor);

                if (!taskprocessor.IsNullOrEmptyOrWhiteSpace())
                {
                    dicTaskProcessor[Convert.ToString(taskprocessor).ToLower()] = task.Item1;
                }
            }

            foreach (var data in dataList)
            {
                var taskOpList = new List<TaskOperation>();

                ITaskProcessor _taskProcessor = null;
                var taskProcessor = Convert.ToString(data["ftaskprocessor"]).ToLower();
                if (!taskProcessor.IsNullOrEmptyOrWhiteSpace())
                {
                    if (dicTaskProcessor.ContainsKey(taskProcessor))
                    {
                        _taskProcessor = dicTaskProcessor[taskProcessor];
                    }
                }
                if (_taskProcessor != null)
                {
                    switch (taskProcessor)
                    {
                        case "approvalflow":
                            if (this.SearchType.EqualsIgnoreCase("mytask"))
                            {
                                taskOpList.AddRange(_taskProcessor.TaskOperations);
                            }
                            else
                            {
                                taskOpList.Add(_taskProcessor.TaskOperations.FirstOrDefault(o => o.Id.EqualsIgnoreCase("view")));
                            }
                            break;
                        default:
                            taskOpList.AddRange(_taskProcessor.TaskOperations);
                            break;
                    }
                }

                //如果没有任何操作，则默认加一个“查看”操作
                if (taskOpList.Count <= 0)
                {
                    taskOpList.Add(new TaskOperation { Id = "view", Caption = "查看" });
                }

                data["ftaskop"] = taskOpList;
            }
        }
    }

    /// <summary>
    /// 业务审批：保存
    /// </summary>
    [InjectService]
    [FormId("bpm_businessapproval")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        //业务审批流程Id
        private string bizobjid { get; set; }

        //业务审批Id 对应 名称、业务对象(修改 前)
        private string pre_name { get; set; }
        private string pre_bizform { get; set; }

        //业务审批Id 对应 名称、业务对象(修改 后)
        private string aft_name { get; set; }
        private string aft_bizform { get; set; }

        /// <summary>
        /// 执行操作事务后事件，通知插件对象执行其它事务无关的业务逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            try
            {
                if (!e.DataEntitys.IsNullOrEmptyOrWhiteSpace())
                {
                    var opName = "";
                    var content = "";
                    var sqlText = "";
                    var fopname = "";


                    var where1 = " where fid =(select top 1 fid from t_bpm_businessapproval_lg where fbizobjid = '" + Convert.ToString(e.DataEntitys[0]["Id"]) + "' order by fcreatedate desc) ";

                    var dbSvcEX = this.Context.Container.GetService<IDBServiceEx>();

                    sqlText = " select fopname  from  t_bpm_businessapproval_lg " + where1;

                    using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
                    {
                        if (reader.Read())
                        {
                            fopname = Convert.ToString(reader["fopname"]);
                        }
                    }


                    if (fopname == "" || fopname == "创建")
                    {
                        opName = "业务审批-创建";
                        content = "执行了 [创建] 操作!";
                        WriteLogHelper.WriteLog(OperationContext, content, Convert.ToString(e.DataEntitys[0][0]), Convert.ToString(e.DataEntitys[0]["fnumber"]), "", opName);
                    }
                    else
                    {
                        var sqlT = " select tbb.fname name,tsb.fname bizform " +
                                   " from t_sys_bizobject tsb,t_bpm_businessapproval tbb " +
                                   " where tbb.fid = '" + Convert.ToString(e.DataEntitys[0]["Id"]) + "' and tbb.fbizformid = tsb.fId ";

                        using (var reader = this.DBService.ExecuteReader(this.Context, sqlT))
                        {
                            if (reader.Read())
                            {
                                aft_name = Convert.ToString(reader["name"]);
                                aft_bizform = Convert.ToString(reader["bizform"]);
                            }
                        }

                        opName = "业务审批-修改";

                        if (pre_name != aft_name && pre_bizform != aft_bizform)
                        {
                            content = "执行了“" + pre_name + "”[修改]为“" + aft_name + "”操作!";
                            WriteLogHelper.WriteLog(OperationContext, content, Convert.ToString(e.DataEntitys[0]["Id"]), Convert.ToString(e.DataEntitys[0]["fnumber"]), "", opName);

                            content = "执行了“" + pre_bizform + "”对象[修改]为“" + aft_bizform + "”操作!";
                            WriteLogHelper.WriteLog(OperationContext, content, Convert.ToString(e.DataEntitys[0]["Id"]), Convert.ToString(e.DataEntitys[0]["fnumber"]), "", opName);
                        }
                        else if (pre_name != aft_name)
                        {
                            content = "执行了“" + pre_name + "”[修改]为“" + aft_name + "”操作!";

                            WriteLogHelper.WriteLog(OperationContext, content, Convert.ToString(e.DataEntitys[0]["Id"]), Convert.ToString(e.DataEntitys[0]["fnumber"]), "", opName);
                        }
                        else if (pre_bizform != aft_bizform)
                        {
                            content = "执行了“" + pre_bizform + "”对象[修改]为“" + aft_bizform + "”操作!";

                            WriteLogHelper.WriteLog(OperationContext, content, Convert.ToString(e.DataEntitys[0]["Id"]), Convert.ToString(e.DataEntitys[0]["fnumber"]), "", opName);
                        }

                    }

                    //where1 = " where fid in (select fid from t_bpm_businessapproval_lg where fbizobjid = '" + Convert.ToString(e.DataEntitys[0][0]) + "')";
                    //sqlText = " delete from  t_bpm_businessapproval_lg " + where1 + " and (fopname='修改' or fopname='创建')";
                    //dbSvcEX.Execute(this.Context, sqlText);

                }
            }
            catch
            {

            }

            BusinessApprovalHelper.RemoveBizFormIdCache(this.Context);
        }


        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            try
            {
                if (!e.DataEntitys.IsNullOrEmptyOrWhiteSpace())
                {
                    var sqlT = " select tbb.fname name,tsb.fname bizform " +
                           " from t_sys_bizobject tsb,t_bpm_businessapproval tbb " +
                           " where tbb.fid = '" + Convert.ToString(e.DataEntitys[0]["Id"]) + "' and tbb.fbizformid = tsb.fId ";

                    using (var reader = this.DBService.ExecuteReader(this.Context, sqlT))
                    {
                        if (reader.Read())
                        {
                            pre_name = Convert.ToString(reader["name"]);
                            pre_bizform = Convert.ToString(reader["bizform"]);
                        }
                    }
                }
            }
            catch
            {

            }
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
        }
    }

    /// <summary>
    /// 业务审批：删除
    /// </summary>
    [InjectService]
    [FormId("bpm_businessapproval")]
    [OperationNo("Delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 执行操作事务后事件，通知插件对象执行其它事务无关的业务逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            BusinessApprovalHelper.RemoveBizFormIdCache(this.Context);
        }

       
    }

    

    /// <summary>
    /// 业务审批帮助类
    /// </summary>
    public static class BusinessApprovalHelper
    {
        /// <summary>
        /// 加载配置了审批流的业务表单标识
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public static List<string> LoadBusinessApprovalBizFormIds(UserContext userCtx)
        {

            var bizFormIds = GetBizFormIdCache(userCtx);
            if (bizFormIds == null || !bizFormIds.Any())
            {
                var sqlText = $"select fbizformid from t_bpm_businessapproval with(nolock) where fmainorgid='{userCtx.Company}'";

                var dbService = userCtx.Container.GetService<IDBService>();
                bizFormIds = dbService.ExecuteDynamicObject(userCtx, sqlText)
                    .Select(o => Convert.ToString(o["fbizformid"]))
                    .ToList();

                if (bizFormIds.Any())
                {
                    SetBizFormIdCache(userCtx, bizFormIds);
                }
            }

            return bizFormIds;
        }

        /// <summary>
        /// 获取业务表单标识缓存
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        private static List<string> GetBizFormIdCache(UserContext userCtx)
        {
            var key = userCtx.Company;
            var redisCache = userCtx.Container.GetService<IRedisCache>();
            (redisCache as ISupportRegion).SetRegionId("Approve");
            return redisCache.Get<List<string>>(userCtx, key);
        }

        /// <summary>
        /// 设置业务表单标识缓存
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="bizFormIds"></param>
        private static void SetBizFormIdCache(UserContext userCtx, List<string> bizFormIds)
        {
            var key = userCtx.Company;
            var redisCache = userCtx.Container.GetService<IRedisCache>();
            (redisCache as ISupportRegion).SetRegionId("Approve");
            redisCache.Set(userCtx, key, bizFormIds, TimeSpan.FromDays(3)); // 默认缓存3天
        }

        /// <summary>
        /// 移除业务表单标识缓存
        /// </summary>
        /// <param name="userCtx"></param>
        public static void RemoveBizFormIdCache(UserContext userCtx)
        {
            var key = userCtx.Company;
            var redisCache = userCtx.Container.GetService<IRedisCache>();
            (redisCache as ISupportRegion).SetRegionId("Approve");
            redisCache.Remove(userCtx, key);
        }
    }



    /// <summary>
    /// 业务审批：流程 启用/停用
    /// </summary>
    [InjectService]
    [FormId("bpm_businessapproval")]
    [OperationNo("Optype")]
    public class Optype : AbstractOperationServicePlugIn
    {
        //业务审批流程Id
        private string bizobjid { get; set; }

        /// <summary>
        /// 执行操作事务后事件，通知插件对象执行其它事务无关的业务逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            try
            {
                if (!e.DataEntitys.IsNullOrEmptyOrWhiteSpace())
                {
                    var opName = string.Empty;
                    var fflowtitle = string.Empty;
                    var fverno = string.Empty;
                    var content = string.Empty;
                    var sqlText = string.Empty;

                    //var array = ((JieNor.Framework.DataTransferObject.DynamicDTOWrapper)Context.CurrentRequestObject).SimpleData.Values.ToArray();
                    bizobjid = SimpleData["rowid"];

                    sqlText = " select fflowtitle,fverno from t_bpm_businessflow where fentryid = '" + bizobjid + "'";

                    using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
                    {
                        if (reader.Read())
                        {
                            fflowtitle = Convert.ToString(reader["fflowtitle"]);
                            fverno = Convert.ToString(reader["fverno"]);
                        }
                    }

                    switch (OperationContext.SimpleData.GetString("optype"))
                    {
                        case "on":
                            opName = "流程信息-启用";
                            content = "执行了“" + fflowtitle + "”" + fverno + "版本 [启用]操作！";
                            WriteLogHelper.WriteLog(OperationContext, content, Convert.ToString(e.DataEntitys[0]["Id"]), Convert.ToString(e.DataEntitys[0]["fnumber"]), "", opName);
                            break;
                        case "off":
                            opName = "流程信息-停用";
                            content = "执行了“" + fflowtitle + "”" + fverno + "版本 [停用]操作！";
                            WriteLogHelper.WriteLog(OperationContext, content, Convert.ToString(e.DataEntitys[0]["Id"]), Convert.ToString(e.DataEntitys[0]["fnumber"]), "", opName);
                            break;
                    }
                }
            }
            catch
            {

            }

        }
    }


    /// <summary>
    /// 业务审批：流程版本信息 设置为主版本
    /// </summary>
    [InjectService]
    [FormId("bpm_businessapproval")]
    [OperationNo("Vnow")]
    public class Vnow : AbstractOperationServicePlugIn
    {
        //业务审批流程Id
        private string detailid { get; set; }

        /// <summary>
        /// 执行操作事务后事件，通知插件对象执行其它事务无关的业务逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            try
            {
                if (!e.DataEntitys.IsNullOrEmptyOrWhiteSpace())
                {
                    var opName = string.Empty;
                    var fflowtitle = string.Empty;
                    var fverno = string.Empty;
                    var content = string.Empty;
                    var sqlText = string.Empty;
                    var createdate = string.Empty;
                    var distributedate = string.Empty;
                    var fentryid = string.Empty;


                    //var array = ((JieNor.Framework.DataTransferObject.DynamicDTOWrapper)Context.CurrentRequestObject).SimpleData.Values.ToArray();

                    detailid = SimpleData["rowid"];

                    sqlText = "  select tbf.fflowname,tbf.fvername,tbf.fentryid,tbf.fdistributedate,tbf.fcreatedate from t_bpm_flowversion tbf where tbf.fdetailid = '" + detailid + "'";

                    using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
                    {
                        if (reader.Read())
                        {
                            fflowtitle = Convert.ToString(reader["fflowname"]);
                            fverno = Convert.ToString(reader["fvername"]);
                            fentryid = Convert.ToString(reader["fentryid"]);
                            distributedate = Convert.ToString(reader["fdistributedate"]);
                            createdate = Convert.ToString(reader["fcreatedate"]);
                        }
                    }

                    opName = "流程信息-版本记录";
                    content = "执行了“" + fflowtitle + "”" + fverno + "版本 [设为主版本]操作！";
                    WriteLogHelper.WriteLog(OperationContext, content, Convert.ToString(e.DataEntitys[0]["Id"]), Convert.ToString(e.DataEntitys[0]["fnumber"]), "", opName);

                    var dbSvcEX = this.Context.Container.GetService<IDBServiceEx>();
                    sqlText = " update t_bpm_businessflow set fdistributedate='" + distributedate + "',fcreatedate='" + createdate + "'where fentryid='" + fentryid + "'";
                    dbSvcEX.Execute(this.Context, sqlText);
                }
            }
            catch
            {

            }


        }
    }


    /// <summary>
    /// 业务审批：新增流程保存 or 流程修改
    /// </summary>
    [InjectService]
    [FormId("bpm_prodesign")]
    [OperationNo("Designsave")]
    public class DesignSave : AbstractOperationServicePlugIn
    {

        protected ILogService LogService
        {
            get;
            set;
        }

        //业务审批Id
        private string fbizobjid { get; set; }

        //业务审批流程Id
        private string bizobjid { get; set; }

        //业务审批流程Id 对应 审批流程 数量(审批流程 保存 前)
        private int pre_count { get; set; }

        //业务审批流程Id 对应 审批流程 数量(审批流程 保存 后)
        private int aft_count { get; set; }

        /// <summary>
        /// 执行操作事务后事件，通知插件对象执行其它事务无关的业务逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            try
            {
                var opName = string.Empty;
                var content = string.Empty;
                var fflowtitle = string.Empty;
                var fverno = string.Empty;
                var fnumber = string.Empty;


                var sqlText = " select count(*) count from t_bpm_businessflow where fid = '" + fbizobjid + "'";

                using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
                {
                    if (reader.Read())
                    {
                        aft_count = Convert.ToInt32(reader["count"]);
                    }
                }


                sqlText = " select fnumber from t_bpm_businessapproval where fid = '" + fbizobjid + "'";

                using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
                {
                    if (reader.Read())
                    {
                        fnumber = Convert.ToString(reader["fnumber"]);
                    }
                }

                //var array = ((JieNor.Framework.DataTransferObject.DynamicDTOWrapper)Context.CurrentRequestObject).SimpleData.Values.ToArray();

                bizobjid = SimpleData["flowId"];

                if (aft_count > pre_count)
                {
                    opName = "流程信息-创建";

                    sqlText = " select top 1 fflowtitle,fverno from t_bpm_businessflow where fid = '" + fbizobjid + "' order by fcreatedate desc";

                    using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
                    {
                        if (reader.Read())
                        {
                            fflowtitle = Convert.ToString(reader["fflowtitle"]);
                            fverno = Convert.ToString(reader["fverno"]);
                        }
                    }

                    content = "执行了“" + fflowtitle + "”V1.0版本 [创建]操作！";
                }
                else
                {
                    opName = "流程信息-修改";

                    sqlText = " select fflowtitle,fverno from t_bpm_businessflow where fentryid = '" + bizobjid + "'";

                    using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
                    {
                        if (reader.Read())
                        {
                            fflowtitle = Convert.ToString(reader["fflowtitle"]);
                            fverno = Convert.ToString(reader["fverno"]);
                        }
                    }

                    fverno = fverno.IsNullOrEmptyOrWhiteSpace() ? "v1.0" : fverno;
                    content = "执行了“" + fflowtitle + "”" + fverno + "版本 [修改]操作！";
                }

                WriteLogHelper.WriteLog(OperationContext, "bpm_businessapproval", "Designsave", content, fbizobjid, fnumber, "", opName);

            }
            catch (Exception ex)
            {
                throw (ex);
            }

        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            try
            {

                var designDataValue = SimpleData["designData"];
                fbizobjid = designDataValue.Split((char)'"')[3];

                var sqlText = " select count(*) count from t_bpm_businessflow where fid = '" + fbizobjid + "'";

                using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
                {
                    if (reader.Read())
                    {
                        pre_count = Convert.ToInt32(reader["count"]);
                    }
                }
            }
            catch(Exception ex)
            {
                throw (ex);
            }

        }


    }


    /// <summary>
    /// 业务审批：流程发布
    /// </summary>
    [InjectService]
    [FormId("bpm_prodesign")]
    [OperationNo("Distributeflow")]
    public class Distributeflow : AbstractOperationServicePlugIn
    {

        protected ILogService LogService
        {
            get;
            set;
        }

        //业务审批Id
        private string fbizobjid { get; set; }

        //业务审批流程Id
        private string bizobjid { get; set; }

        /// <summary>
        /// 执行操作事务后事件，通知插件对象执行其它事务无关的业务逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            try
            {
                var opName = string.Empty;
                var content = string.Empty;
                var fflowtitle = string.Empty;
                var fverno = string.Empty;
                var fnumber = string.Empty;

                bizobjid =  SimpleData["verId"];

                var sqlText = "  select fflowname,fvername from t_bpm_flowversion  where fdetailid = '" + bizobjid + "'";

                using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
                {
                    if (reader.Read())
                    {
                        fflowtitle = Convert.ToString(reader["fflowname"]);
                        fverno = Convert.ToString(reader["fvername"]);
                    }
                }
                if (((JieNor.Framework.DataTransferObject.CommonFormDTO)this.Context.CurrentRequestObject).BillData == null) {

                    WriteLogHelper.WriteLog(OperationContext, "bpm_businessapproval", "Distributeflow", "BillData为NULL", fbizobjid, fnumber, "", opName);
                    return;
                }
                var billdataJson = Convert.ToString(((JieNor.Framework.DataTransferObject.CommonFormDTO)this.Context.CurrentRequestObject).BillData);
                var data = JArray.Parse(billdataJson);
                fbizobjid = data[0]["pkid"].ToString();
                //fbizobjid = ((JieNor.Framework.DataTransferObject.CommonFormDTO)this.Context.CurrentRequestObject).BillData.Split((char)'"')[7];
                

                sqlText = " select fnumber from t_bpm_businessapproval where fid = '" + fbizobjid + "'";

                using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
                {
                    if (reader.Read())
                    {
                        fnumber = Convert.ToString(reader["fnumber"]);
                    }
                }

                opName = "流程信息-发布";
                fverno = fverno.IsNullOrEmptyOrWhiteSpace() ? "v1.0" : fverno;
                content = "执行了“" + fflowtitle + "”" + fverno + "版本 [发布]操作！";

                WriteLogHelper.WriteLog(OperationContext, "bpm_businessapproval", "Distributeflow", content, fbizobjid, fnumber, "", opName);


                //var IsMain = ((JieNor.Framework.DataTransferObject.CommonFormDTO)this.Context.CurrentRequestObject).SimpleData.Values.ToArray()[4];

                var IsMain = SimpleData["fsetpreset"];

                if (IsMain.Equals("true"))
                {
                    opName = "流程信息-启用";
                    content = "执行了“" + fflowtitle + "”" + fverno + "版本 [启用]操作！";
                    WriteLogHelper.WriteLog(OperationContext, "bpm_businessapproval", "Distributeflow", content, fbizobjid, fnumber, "", opName);

                }
            }
            catch(Exception ex)
            {
                throw (ex);
            }
        }


    }


    /// <summary>
    /// 业务审批：查询
    /// </summary>
    [InjectService]
    [FormId("bpm_businessapproval")]
    [OperationNo("querydata")]
    public class Query : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 执行操作事务后事件，通知插件对象执行其它事务无关的业务逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            try
            {
                var dbSvcEX = this.Context.Container.GetService<IDBServiceEx>();
                var sqlText = " update t_bpm_businessapproval_lg set fopname='业务审批-删除'where fopname='删除'";
                dbSvcEX.Execute(this.Context, sqlText);
            }
            catch
            {

            }

        }


    }


    /// <summary>
    /// 业务审批：保存后刷新
    /// </summary>
    [InjectService]
    [FormId("bpm_businessapproval")]
    [OperationNo("refresh")]
    public class Refresh : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 执行操作事务后事件，通知插件对象执行其它事务无关的业务逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            //try
            //{
            //    var bizobjid = ((JieNor.Framework.DataTransferObject.DynamicDTOWrapper)Context.CurrentRequestObject).SelectedRows.ToArray()[0].PkValue;
            //    var where1 = " where fid in (select fid from t_bpm_businessapproval_lg where fbizobjid = '" + bizobjid + "')";
            //    var sqlText = " delete from  t_bpm_businessapproval_lg " + where1 + " and (fopname='修改' or fopname='创建')";
            //    var dbSvcEX = this.Context.Container.GetService<IDBServiceEx>();
            //    dbSvcEX.Execute(this.Context, sqlText);
            //}
            //catch
            //{

            //}


        }

    }

    /// <summary>
    /// 业务审批：查看日志
    /// </summary>
    [InjectService]
    [FormId("bpm_businessapproval")]
    [OperationNo("operatelog")]
    public class Operatelog : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 执行操作事务后事件，通知插件对象执行其它事务无关的业务逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            try
            {
                var dbSvcEX = this.Context.Container.GetService<IDBServiceEx>();
                var sqlText = " update t_bpm_businessapproval_lg set fopname='业务审批-删除'where fopname='删除'";
                dbSvcEX.Execute(this.Context, sqlText);

                //var selectrows = ((JieNor.Framework.DataTransferObject.DynamicDTOWrapper)Context.CurrentRequestObject).SelectedRows;

                //if (selectrows.Count() == 1)
                //{
                //    var bizobjid = ((JieNor.Framework.DataTransferObject.DynamicDTOWrapper)Context.CurrentRequestObject).SelectedRows.ToArray()[0].PkValue;
                //    var where1 = " where fid in (select fid from t_bpm_businessapproval_lg where fbizobjid = '" + bizobjid + "')";
                //    sqlText = " delete from  t_bpm_businessapproval_lg " + where1 + " and (fopname='修改' or fopname='创建')";
                //    dbSvcEX.Execute(this.Context, sqlText);
                //}
            }
            catch
            {

            }


        }

    }

}