using System;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.OtherStockIn
{
    /// <summary>
    /// 其他入库单：审核
    /// </summary>
    [InjectService]
    [FormId("stk_otherstockin")]
    [OperationNo("audit")]
    public class Audit : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var nowDate = DateTime.Now;
            foreach (var dataEntity in e.DataEntitys)
            {
                //task 70392,【其他入库单】以当前日期更新至【入库日期】
                dataEntity["fdate"] = nowDate;
            }
        }
    }
}