using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.PurReceiptNotice
{
    /// <summary>
    /// 采购收货通知单：关闭
    /// </summary>
    [InjectService]
    [FormId("pur_receiptnotice")]
    [OperationNo("bizclose")]
    public class BizClose: AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);
            switch (e.EventName)
            {
                //行关闭事件
                case "closeRowEvent":
                    closeRowEvent(e);
                    break;
            }
        }

        private void closeRowEvent(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Dictionary<string, object>;
            if (eventData == null)
            {
                return;
            }

            var entries = eventData["entries"] as List<DynamicObject>;
            var entryKey = eventData["entryKey"] as string;
            var userContext = eventData["userContext"] as UserContext;
            var dataEntity = eventData["dataEntity"] as DynamicObject;

            if (false == "fentity".EqualsIgnoreCase(entryKey) || entries == null || entries.Count <= 0 || userContext == null || dataEntity == null)
            {
                return;
            }

            var metaModelService = userContext.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(userContext, "stk_postockin");
            var htmlEntry = htmlForm.GetEntryEntity("fentity");
            var sql = new StringBuilder();
            sql.AppendFormat(@"
select distinct t.fbillno,t.fstatus from {0} as t
inner join {1} as e on t.fid=e.fid
where e.fsourceformid='pur_receiptnotice' and t.fmainorgid=@fmainorgid and e.fsourceinterid=@fsourceinterid and fsourceentryid
", htmlForm.BillHeadTableName, htmlEntry.TableName);
            var sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid",System.Data.DbType.String,userContext.Company),
                new SqlParam("@fsourceinterid",System.Data.DbType.String,Convert.ToString(dataEntity["id"]))
            };

            if (entries.Count == 1)
            {
                sql.Append(" = @fsourceentryid");
                sqlParams.Add(new SqlParam("@fsourceentryid", System.Data.DbType.String, Convert.ToString(entries[0]["id"])));
            }
            else
            {
                sql.Append(" in (");
                sql.Append(string.Join(",", entries.Select((x, i) => $"@fsourceentryid{i}")));
                sql.Append(")");
                sqlParams.AddRange(entries.Select((x, i) => new SqlParam($"@fsourceentryid{i}", System.Data.DbType.String, x["id"])));
            }

            var dbResults = new Dictionary<string, string>();
            var dbService = userContext.Container.GetService<IDBService>();
            using (var dataReader = dbService.ExecuteReader(userContext, sql.ToString(), sqlParams))
            {
                while (dataReader.Read())
                {
                    dbResults.Add(dataReader.GetValueToString("fbillno"), dataReader.GetValueToString("fstatus"));
                }
            }

            if (dbResults != null && dbResults.Count > 0)
            {
                dbResults = dbResults.Where(x => false==x.Value.EqualsIgnoreCase("E")).ToDictionary(k => k.Key, v => v.Value);
                if (dbResults.Count > 0)
                {
                    throw new BusinessException($"{this.HtmlForm.Caption}[{Convert.ToString(dataEntity["fbillno"])}]存在下游{htmlForm.Caption}[{string.Join(",", dbResults.Keys)}]未审核，不允许关闭!");
                }
            }

            foreach (var entry in entries)
            {
                entry["fcloseqty"] = Convert.ToDecimal(entry["fqty"]) - Convert.ToDecimal(entry["finstockqty"]);
                entry["fqty"] = entry["finstockqty"];
            }

            var unitConvertService = this.Container.GetService<IUnitConvertService>();
            unitConvertService.ConvertByBasQty(this.Context, this.HtmlForm, new[] { dataEntity }, OperateOption.Create());
        }
    }
}
