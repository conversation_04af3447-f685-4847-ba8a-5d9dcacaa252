using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using Senparc.Weixin.Helpers.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.InventoryVerify
{
    /// <summary>
    /// 盘点单：撤销
    /// </summary>
    [InjectService]
    [FormId("stk_inventoryverify")]
    [OperationNo("terminateflow")]
    public class TerminateFlow : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);


            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fmanagemodel"]).Equals("1"))
                {
                    return string.IsNullOrWhiteSpace(Convert.ToString(newData["fchstatus"]));
                }
                return true;
            }).WithMessage("已提交总部，不允许撤回！"));
        }

    }
}
