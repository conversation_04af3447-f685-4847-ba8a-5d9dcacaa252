using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.InventoryVerify
{
    /// <summary>
    /// 盘点单：删除
    /// </summary>
    [InjectService]
    [FormId("stk_inventoryverify")]
    [OperationNo("Delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
                {
                    return Convert.ToString(newData["finventbase"]).IsNullOrEmptyOrWhiteSpace();
                }).WithMessage("{0}单号有上游单据不允许删除！",
                (billObj, propObj) => propObj["fbillno"]));


            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fmanagemodel"]).Equals("1"))
                {
                    return string.IsNullOrWhiteSpace(Convert.ToString(newData["fchstatus"]));
                }return true;
            }).WithMessage("已提交总部，不允许删除！"));
        }
    }
}
