using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System.Linq;
using JieNor.Framework.MetaCore.FormOp.FormService;


namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.InventoryVerify
{
    /// <summary>
    /// 盘点单：更新初始库存余额
    /// </summary>
    [InjectService]
    [FormId("stk_inventoryverify")]
    [OperationNo("SetIniStockBal")]
    public class SetIniStockBal : AbstractOperationServicePlugIn
    {

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            if(e.DataEntitys ==null || e.DataEntitys.Length ==0)
            {
                return;
            }

            if (e.DataEntitys.Any(f => f.DataEntityState.FromDatabase == false))
            {
                throw new Exception("请保存盘点单数据后再进行初始库存余额更新");
            }

            this.Container.GetService<LoadReferenceObjectManager>().Load(this.Context,e.DataEntitys[0].DynamicObjectType, e.DataEntitys, false );

            var bills = e.DataEntitys.Where(f =>  f["fbilltype_ref"] !=null && (f["fbilltype_ref"] as DynamicObject )["fnumber"] .ToString().EqualsIgnoreCase("QCPDD_SYS_02")).ToList();
            if (bills == null || bills.Count  == 0)
            {
                throw new Exception("单据类型为【期初盘点单】的盘点单才有期初账存金额，请选择期初盘点单进行更新初始库存余额");
            }

            var billIds = bills.Select(f => f["Id"].ToString()).ToList();
            var svc = this.Container.GetService<Core.Interface.StockUpdate.ICostCalulateService>();
            svc.UpdateIniStockBalanceByVerify(this.Context, billIds, this.Option);
             
        }



    }




}