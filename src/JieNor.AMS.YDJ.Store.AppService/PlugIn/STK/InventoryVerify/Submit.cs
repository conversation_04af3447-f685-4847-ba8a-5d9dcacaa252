using JieNor.Framework;
using JieNor.Framework.AppService.OperationService.ApprovalFlow;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.InventoryVerify
{
    /// <summary>
    /// 盘点单：提交
    /// </summary>
    [InjectService]
    [FormId("stk_inventoryverify")]
    [OperationNo("Submit")]
    public class Submit : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var entitys = newData["fentity"] as DynamicObjectCollection;
                if (entitys == null || entitys.Count <= 0)
                {
                    return false;
                }
                foreach (var entity in entitys)
                {
                    if (entity["fmaterialid"].IsNullOrEmptyOrWhiteSpace())
                    {
                        return false;
                    }
                }
                return true;
            }).WithMessage("盘点单没有盘点明细数据，不能提交!"));
        }



        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (!this.Context.IsDirectSale) return;
            foreach (DynamicObject data in e.DataEntitys)
            {
                // 2. 判断是否配置CRM业务审批流程
                bool hasCrmFlow = HasCrmApprovalFlow(data);
                if (!hasCrmFlow)
                {
                    bool success = PushToMuSi(data);
                    if (success)
                    {
                        data["fsubmithtime"] = DateTime.Now;
                        data["fchstatus"] = "1"; // 协同SAP状态=已提交SAP
                    }
                }
            }
        }

        /// <summary>
        /// 判断是否配置CRM业务审批流程
        /// </summary>
        private bool HasCrmApprovalFlow(DynamicObject data)
        {
            // 示例：可根据实际业务通过审批流服务或配置表判断
            // 伪代码：return ApprovalFlowService.HasFlow("stk_inventoryverify", data["fid"].ToString());
            // 这里只做简单返回，实际请替换为真实判断
            var flowService = this.Container.GetService<IApprovalFlowService>();
            if (flowService == null)
            {
                return false; // 审批流服务不可用，认为未启用
            }

            var startFlowList = flowService.GetBillStartFlowList(this.Context, this.HtmlForm);

            return (startFlowList != null && startFlowList.Count > 0);
        }

        /// <summary>
        /// 推送盘点单到中台
        /// </summary>
        private bool PushToMuSi(DynamicObject data)
        {
            // 示例：调用盘点单的同步操作
            var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, new[] { data }, "submitheadquart",
                   new Dictionary<string, object>
                   {
                                { "IgnoreCheckPermssion", true }, { "IgnoreValidateDataEntities", true }
                   });
            result?.ThrowIfHasError();
            return result.IsSuccess;
        }
    }
}

