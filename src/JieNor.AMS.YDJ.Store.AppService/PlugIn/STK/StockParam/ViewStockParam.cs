using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Serialization;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.StockParam
{
    [InjectService]
    [FormId("stk_stockparam")]
    [OperationNo("ViewParam")]
    public class ViewStockParam : AbstractOperationServicePlugIn
    {
        ///// <summary>
        ///// 打开库存参数设置页面时，加载默认需要设置部门仓库控制的单据列表
        ///// </summary>
        ///// <param name="e"></param>
        //public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        //{
        //    //事件名称
        //    switch (e.EventName)
        //    {
        //        case "afterCreateUIData":

        //            var uiData = e.EventData as JObject;
        //            if (uiData == null) uiData = new JObject();
        //            var billItems = new JArray(uiData);

        //            List<DynamicObject> lstDataEntities = new List<DynamicObject>();
        //            var dcSerializer = this.Container.GetService<IDynamicSerializer>();

        //            dcSerializer.Sync(this.HtmlForm.GetDynamicObjectType(this.Context), lstDataEntities, billItems, (propKey) =>
        //          {
        //              var el = this.OperationContext.HtmlForm?.GetElement(propKey);
        //              if (el is HtmlField) return (el as HtmlField).DynamicProperty;
        //              if (el is HtmlEntryEntity) return (el as HtmlEntryEntity).DynamicProperty;

        //              return null;
        //          });
        //            this.OperationContext.DataEntities = lstDataEntities;

        //            var para = lstDataEntities.FirstOrDefault();
        //            UpdateCtrlFormInfo(para);
                     
        //            var uiConverter = this.Container.GetService<IUiDataConverter>();
        //            var billJsonData = uiConverter.CreateUIDataObject(Context, this.HtmlForm, para);

        //            e.EventData = billJsonData.GetJsonValue<JObject>("uiData", new JObject()); ;

        //            break;
        //        default:
        //            break;
        //    }
        //}


        ///// <summary>
        ///// 加载默认需要设置部门仓库控制的单据列表
        ///// </summary>
        ///// <param name="paraObj"></param>
        //private void UpdateCtrlFormInfo(DynamicObject paraObj)
        //{
        //    var lstForms = new Dictionary<string, string>();
        //    lstForms.Add("stk_otherstockin", "其它入库单");
        //    lstForms.Add("stk_otherstockout", "其它出库单");
        //    lstForms.Add("stk_postockin", "采购入库单");
        //    lstForms.Add("stk_sostockout", "销售出库单");
        //    lstForms.Add("stk_inventorytransfer", "库存调拨单"); 

        //    var ens = paraObj["fctrlbillentity"] as DynamicObjectCollection;
        //    RemoveNotExits(ens, lstForms);

        //    foreach (var moduleInfo in lstForms)
        //    {
        //        var formIdRow = ens.FirstOrDefault(f => moduleInfo.Key.EqualsIgnoreCase(f["fctrlformid"]?.ToString()));
        //        if (formIdRow == null)
        //        {
        //            formIdRow = ens.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
        //            formIdRow["fctrlformid"] = moduleInfo.Key;
        //            formIdRow["fctrlformtitle"] = moduleInfo.Value;
        //            ens.Add(formIdRow);
        //        }                 
        //    } 
        //}

         


        ///// <summary>
        ///// 已经被删除的模块，从参数里面去掉
        ///// </summary>
        ///// <param name="paraObj"></param>
        ///// <param name="group"></param>
        ///// <returns></returns>
        //private static void  RemoveNotExits(DynamicObjectCollection ens, Dictionary<string, string> group)
        //{ 
        //    var beDel = new List<DynamicObject>(); 
        //    foreach (var item in ens)
        //    {
        //        var formId = item["fctrlformid"]?.ToString();
        //        if (!group.Any(f => f.Key.EqualsIgnoreCase(formId)))
        //        {
        //            beDel.Add(item);
        //        }
        //    }
        //    foreach (var item in beDel)
        //    {
        //        ens.Remove(item);
        //    }             
        //}



    }
}
