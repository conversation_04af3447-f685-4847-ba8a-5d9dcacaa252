using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Helper;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Helpers;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SoStockOut
{
    /// <summary>
    /// 销售出库单：反审核
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("UnAudit")]
    public class UnAudit : AbstractOperationServicePlugIn
    {
        private List<DynamicObject> auditBeforeOrder { get; set; }
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            //base.EndOperationTransaction(e);
            this.Result.IsSuccess = false;
            var notifyService = this.Context.Container.GetService<IStockOutNotifyService>();
            notifyService.Notify(this.Context, e.DataEntitys, this.HtmlForm, this.OperationNo);
            /*10.如果发货方经销商将转单的《销售合同》出库生成《销售出库单》时 (经过发货通知单也算), 《销售出库单》审核之后要更新出库数量
            1)更新 接单方 与 发货方《转单申请单》商品明细对应商品的【已发货数量】, 并且如果是完全出库的话更新《转单申请单》基本信息的【发货完成日期】(用出库日期反写)
            2)更新 接单方《销售合同》商品明细的【销售已出库数】
            3)更新 发货方《销售合同》商品明细的【销售已出库数】(已有标准功能)
            4)同样的如果后来该《销售出库单》反审核, 要扣减上述所更新的数量 与 清空发货完成日期
            */
            AuditOrUnAuditHelper.OrderReviewAuditOrUnAudit(this.HtmlForm.Id,e.DataEntitys,this.Context);

            // 反写合同关联的二级经销商采购订单【一级合同状态】为“订单已审核”，且自动重新计算二级销售合同和采购订单关闭状态
            ResellerHelper.WriteBackPurchaseOrdersBySoStockOuts(
                this.OperationContext,
                e.DataEntitys,
                OneLvOrderSratusConst.FinalAudited);

            // 反写销售合同【流程状态】
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, e.DataEntitys);


            ProductDelistingHelper.DealDelistingDataByStockOut(this.Context, this.HtmlForm,  this.auditBeforeOrder, e.DataEntitys.ToList(), "stockoutunaudit");
            this.Result.IsSuccess = true;
        }        

        /*
         * 2、反审核《销售出库单》时，通过全流程判断上游的《转单申请单》的转单状态是否是【已经算】状态，如果是【已结算】状态无法反审核《销售出库单》；如转单状态不等于【已结算】的，反审核《销售出库单》时同时删除《转单申请单》下推的《其他应收单》和《其他应付单》。
         */
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            
            //获取上游单据--销售合同
            var allOrderNos = e.DataEntitys.Select(d => d["fsourcenumber"].ToString()).ToList();
            var allMainOrgIds = e.DataEntitys.Select(d => d["fmainorgid"].ToString()).Distinct().ToList();
            var sourceOrders = this.Context.LoadBizDataByFilter("ydj_order", $" fbillno in ('{string.Join("','", allOrderNos)}') and fmainorgid in ('{string.Join("','", allMainOrgIds)}')");//加载当前销售合同数据
            var orderIds = new List<string>();
            //关联的销售合同Id
            var relevanceOrderIds = sourceOrders.Where(t=>!t["freceivercontractid"].IsNullOrEmptyOrWhiteSpace()).Select(t=>t["freceivercontractid"].ToString()).ToList();
            if (relevanceOrderIds.Count > 0)
            {
                var relevanceOrders = this.Context.LoadBizDataById("ydj_order", relevanceOrderIds);
                if (!relevanceOrders.IsNullOrEmptyOrWhiteSpace())
                {
                    relevanceOrders.ForEach(t => orderIds.Add(t["id"].ToString())); ;
                }
            }
            
            if (sourceOrders.Count() > 0 && !sourceOrders.IsNullOrEmptyOrWhiteSpace())
            {
                //获取销售合同的下游单据转单申请单
                orderIds.AddRange(sourceOrders.Select(o => o["id"].ToString()).ToList());
                var queryTransferOrderApplysBySourceNumberSql = $"select ftransferstatus,fbillno from t_ydj_transferorderapply where freceivercontractid in ('{string.Join("','", orderIds.Distinct())}') or fshippercontractid in ('{string.Join("','", orderIds.Distinct())}')";//加载转单申请单数据
                var allTransferorderapplys = this.DBService.ExecuteDynamicObject(this.Context, queryTransferOrderApplysBySourceNumberSql, null).ToList();
                var settledTransferorderapplys = allTransferorderapplys.Where(t => t["ftransferstatus"].ToString() == "4").Select(t => t["fbillno"].ToString()).ToList();//已结算的转单申请单编号
                if (settledTransferorderapplys.Count > 0)
                {
                    throw new BusinessException($"上游单据转单申请单{string.Join(",", settledTransferorderapplys)}转单状态为已结算，无法反审核");
                }
                //var notSettledtransferorderapplyNos = allTransferorderapplys.Where(t => t["ftransferstatus"].ToString() != "4").Select(t => t["fbillno"].ToString()).ToList();//未结算的转单申请单编号

                ////获取待删除的其他应收/付单
                //var querySql = @"select fid,'ydj_payreceipt' as fbilltype from t_ydj_payreceipt
                //            where fsourcenumber in ('{0}')
                //            union all 
                //            select fid,'collectreceipt' as fbilltype from t_ydj_collectreceipt
                //            where fsourcenumber in ('{0}')".Fmt(string.Join("','", notSettledtransferorderapplyNos));
                //var queryDatas = this.DBService.ExecuteDynamicObject(this.Context, querySql).ToList();
                //var payReceiptIds = queryDatas.Where(q => q["fbilltype"].ToString() == "ydj_payreceipt").Select(t => t["fid"].ToString()).ToList();
                //var collectReceiptIds = queryDatas.Where(q => q["fbilltype"].ToString() == "collectreceipt").Select(t => t["fid"].ToString()).ToList();

                ////处理反写流程数据
                //var deleteSql = new List<string>();
                //var dm = this.Container.GetService<IDataManager>();
                //dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
                ////最后保存上游单据相关数据状态
                //if (payReceiptIds.Count > 0)
                //    deleteSql.Add($"DELETE FROM t_ydj_payreceipt WHERE FID IN ('{string.Join("','", payReceiptIds)}')");
                //if (collectReceiptIds.Count > 0)
                //    deleteSql.Add($"DELETE FROM t_ydj_collectreceipt WHERE FID IN ('{string.Join("','", collectReceiptIds)}')");
                //if (deleteSql.Count > 0)
                //    this.Container.GetService<IDBServiceEx>().ExecuteBatch(this.Context, deleteSql);
                this.Result.IsSuccess = true;
            }
        }
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            DirectHelper.UnAudit(this.Context, e.DataEntitys);

            this.auditBeforeOrder = ProductDelistingHelper.GetOrderData(this.Context, e.DataEntitys.ToList(), this.HtmlForm);
        }
    }
}