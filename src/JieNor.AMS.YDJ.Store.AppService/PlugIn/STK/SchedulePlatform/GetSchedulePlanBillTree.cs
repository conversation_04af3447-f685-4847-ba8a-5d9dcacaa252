using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SchedulePlatform
{
    /// <summary>
    /// 发货排单平台：获取排单计划单树形结点
    /// </summary>
    [InjectService]
    [FormId("stk_scheduleplatform")]
    [OperationNo("GetSchedulePlanBillTree")]
    public class GetSchedulePlanBillTree: AbstractOperationServicePlugIn
    {
        const string FDATE= "fdate";
        const string FNUMBER = "fnumber";
        const string FCARID = "fcarid";
        const string FID = "fid";
        const string FSTARTTIME = "fstarttime";
        const string FENDTIME = "fendtime";

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            string orderBy = this.GetQueryOrSimpleParam<string>("orderBy");
            string bizStatus = this.GetQueryOrSimpleParam<string>("bizStatus");
            string beginDate = this.GetQueryOrSimpleParam<string>("beginDate");
            string endDate = this.GetQueryOrSimpleParam<string>("endDate");
            string beginTime = this.GetQueryOrSimpleParam<string>("beginTime");
            string endTime = this.GetQueryOrSimpleParam<string>("endTime");

            if (string.IsNullOrWhiteSpace(orderBy))
            {
                orderBy = FDATE;
            }

            if (string.IsNullOrWhiteSpace(bizStatus))
            {
                bizStatus = "0";
            }

            StringBuilder sql = new StringBuilder(@"
select b.fid,b.fdate,b.fstarttime,b.fendtime,c.fnumber,b.fcarid from t_stk_scheduleplanbill b
inner join t_ydj_carinformation c on b.fcarid=c.fid
where b.fbizstatus=@fbizstatus and b.fmainorgid=@fmainorgid ");
            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid",System.Data.DbType.String,this.Context.Company),
                new SqlParam("@fbizstatus",System.Data.DbType.String,bizStatus)
            };

            if (bizStatus == "1")
            {
                if (string.IsNullOrWhiteSpace(beginDate) == false)
                {
                    sqlParams.Add(new SqlParam("@beginDate", System.Data.DbType.DateTime, DateTime.Parse(beginDate)));
                    sql.Append(" and datediff(d,@beginDate,b.fdate)>-1  ");
                }
                if (string.IsNullOrWhiteSpace(endDate) == false)
                {
                    sqlParams.Add(new SqlParam("@endDate", System.Data.DbType.DateTime, DateTime.Parse(endDate)));
                    sql.Append(" and datediff(d,b.fdate,@endDate)>-1  ");
                }
                if (string.IsNullOrWhiteSpace(beginDate) == false && string.IsNullOrWhiteSpace(endDate) == false && (DateTime.Parse(endDate) - DateTime.Parse(beginDate)).Days < 0)
                {
                    throw new BusinessException("结束日期不能小于开始日期");
                }
                if (string.IsNullOrWhiteSpace(beginTime) == false)
                {
                    sqlParams.Add(new SqlParam("@beginTime", System.Data.DbType.String, beginTime));
                    sql.Append(" and b.fstarttime>=@beginTime ");
                }
                if (string.IsNullOrWhiteSpace(endTime) == false)
                {
                    sqlParams.Add(new SqlParam("@endTime", System.Data.DbType.String, endTime));
                    sql.Append(" and b.fendtime<=@endTime ");
                }
                if (string.IsNullOrWhiteSpace(beginTime) == false && string.IsNullOrWhiteSpace(endTime) == false && string.Compare(endTime, beginTime) < 0)
                {
                    throw new BusinessException("结束时间不能小于开始时间");
                }
                sql.Append(@"
and exists(select 1 from t_stk_schedulebillentry e where e.fid=b.fid and e.fscheduleqty>
(
case e.fsourceformid
when 'ydj_order' then (
    select isnull(sum(soe.fqty),0) from t_stk_sostockoutentry soe
	inner join t_stk_sostockout so on so.fmainorgid=b.fmainorgid and soe.fid=so.fid and so.fcancelstatus='0'
	inner join t_sal_deliverynoticeentry sde on soe.fsourceentryid=sde.fentryid and soe.fsourceinterid=sde.fid and soe.fsourceformid='sal_deliverynotice'
	inner join t_sal_deliverynotice sd on sd.fid=sde.fid and sd.fmainorgid=b.fmainorgid and sd.fschedulebillno=b.fbillno and sde.fsourceformid=e.fsourceformid and sde.fsourceinterid=e.fsourceinterid and sde.fsourceentryid=e.fsourceentryid and sd.fcancelstatus='0'
	)
when 'stk_scheduleapply' then (
	select case when (sa.fapplytype='0' or sa.fapplytype='1') then (
	            select isnull(sum(sre.fqty),0) from t_stk_sostockreturnentry sre
				inner join t_stk_sostockreturn sr on sr.fmainorgid=b.fmainorgid and sre.fid=sr.fid and sr.fcancelstatus='0'
				inner join t_sal_returnnoticeentry rne on sre.fsourceentryid=rne.fentryid and sre.fsourceinterid=rne.fid and sre.fsourceformid='sal_returnnotice'
				inner join t_sal_returnnotice rn on rn.fid=rne.fid and rn.fmainorgid=b.fmainorgid and rn.fschedulebillno=b.fbillno and rne.fsourceformid=e.fsourceformid and rne.fsourceinterid=e.fsourceinterid and rne.fsourceentryid=e.fsourceentryid and rn.fcancelstatus='0'
			)
		        when (sa.fapplytype='2' or sa.fapplytype='4') then (
				select isnull(sum(ive.fqty),0) from t_stk_invtransferentry ive
				inner join t_stk_invtransfer iv on iv.fmainorgid=b.fmainorgid and iv.fid=ive.fid and iv.fcancelstatus='0'
		        inner join t_stk_invtransferreqentry ite on ive.fsourceentryid=ite.fentryid and ive.fsourceinterid=ite.fid and ive.fsourceformid='stk_inventorytransferreq' 
				inner join t_stk_invtransferreq it on it.fid=ite.fid and it.fmainorgid=b.fmainorgid and it.fschedulebillno=b.fbillno and ite.fsourceformid=e.fsourceformid and ite.fsourceinterid=e.fsourceinterid and ite.fsourceentryid=e.fsourceentryid and it.fcancelstatus='0'
		   )
		        when sa.fapplytype='3' then (
				select isnull(sum(ote.fqty),0) from t_stk_otherstockoutentry ote
				inner join t_stk_otherstockout ot on ot.fmainorgid=b.fmainorgid and ot.fid=ote.fid and ot.fcancelstatus='0'
				inner join t_stk_otherstockoutreqentry ose on ote.fsourceentryid=ose.fentryid and ote.fsourceinterid=ose.fid and ote.fsourceformid='stk_otherstockoutreq'
				inner join t_stk_otherstockoutreq os on os.fid=ose.fid and os.fmainorgid=b.fmainorgid and os.fschedulebillno=b.fbillno and ose.fsourceformid=e.fsourceformid and ose.fsourceinterid=e.fsourceinterid and ose.fsourceentryid=e.fsourceentryid and os.fcancelstatus='0'
		   )
		   else 9999999999
		   end
	from t_stk_scheduleapply sa where sa.fid=e.fsourceinterid and sa.fmainorgid=b.fmainorgid
)
else 9999999999
end
))
");
            }

            List<Dictionary<string, object>> dbResults = new List<Dictionary<string, object>>();
            var dbService = this.Container.GetService<IDBService>();
            using (var dataReader = dbService.ExecuteReader(this.Context, sql.ToString(), sqlParams))
            {
                while (dataReader.Read())
                {
                    var dbResult = new Dictionary<string, object>();
                    dbResults.Add(dbResult);
                    dbResult[FID] = dataReader.GetValueToString(FID);
                    dbResult[FDATE] = string.Concat(dataReader.GetValueToDateTime(FDATE).ToString("yyyy-MM-dd"), " ", dataReader.GetValueToString(FSTARTTIME), " - ", dataReader.GetValueToString(FENDTIME));
                    dbResult[FCARID] = dataReader.GetValueToString(FCARID);
                    dbResult[FNUMBER] = dataReader.GetValueToString(FNUMBER);
                }
            }

            var result = new List<Dictionary<string, object>>();

            if (dbResults != null && dbResults.Count > 0)
            {
                result = dbResults.OrderBy(x => x[orderBy]).GroupBy(x => x[orderBy]).Select((x, i) =>
                {
                    object pCaption = x.Key;
                    string sCaptionKey = FNUMBER;
                    string sIdkey = FCARID;

                    if (orderBy == FCARID)
                    {
                        pCaption = x.FirstOrDefault()[FNUMBER];
                        sCaptionKey = FDATE;
                        sIdkey = FDATE;
                    }

                    return new Dictionary<string, object>
                          {
                            { "id",x.Key},
                            { "name",pCaption},
                            { "pid",0},
                            { "billId", string.Empty},
                            { "order",i},
                            { "children",x.OrderBy(y=>y["fcarid"]).Select((y,j)=> new Dictionary<string,object>
                                {
                                    { "id",y[sIdkey] },
                                    { "name",y[sCaptionKey] },
                                    { "pid", x.Key },
                                    { "billId", y[FID] },
                                    { "order", j},
                                    { "children", Array.Empty<object>()}
                                })
                            }
                          };
                }).ToList();
            }

            this.Result.SrvData = result;
            this.Result.IsSuccess = true;
        }
    }
}
