using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SchedulePlatform
{
    /// <summary>
    /// 发货排单平台：确认排单计划
    /// </summary>
    [InjectService]
    [FormId("stk_scheduleplatform")]
    [OperationNo("ConfirmSchedulePlan")]
    public class ConfirmSchedulePlan: AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            this.Result.IsSuccess = false;
            base.BeginOperationTransaction(e);
            var billIdString = this.GetQueryOrSimpleParam<string>("billIds");
            var billIds = billIdString?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            if (billIds == null || billIds.Length <= 0)
            {
                throw new BusinessException("billIds参数不能为为空");
            }

            var metaModelService = this.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "stk_scheduleplanbill");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            var dataEntities = dm.Select(billIds).OfType<DynamicObject>().ToArray();
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                throw new BusinessException("没有找到相应的排单计划单");
            }

            var ids = new List<string>();
            foreach (var dataEntity in dataEntities)
            {
                if (Convert.ToString(dataEntity["fbizstatus"]) == "1")
                {
                    string billNo = Convert.ToString(dataEntity["fbillno"]);
                    this.Result.ComplexMessage.WarningMessages.Add($"编号为{billNo}的排单计划单已确认");
                }
                ids.Add(Convert.ToString(dataEntity["id"]));
            }

            foreach(var billId in billIds)
            {
                if (ids.Contains(billId) == false)
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"id为{billId}的排单计划单没有找到或已被删除");
                }
            }

            if (Result.ComplexMessage.ErrorMessages.Count > 0)
            {
                return;
            }

            if (Result.ComplexMessage.WarningMessages.Count > 0)
            {
                dataEntities = dataEntities.Where(x =>
                  {
                      var fbizstatus = Convert.ToString(x["fbizstatus"]);
                      if (string.IsNullOrWhiteSpace(fbizstatus) || fbizstatus == "0")
                      {
                          return true;
                      }
                      return false;
                  }).ToArray();
            }

            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            var loadReferenceObjectManager = this.Container.GetService<LoadReferenceObjectManager>();
            loadReferenceObjectManager.Load(this.Context, dataEntities, false, htmlForm, new List<string> { "fcarid" });
            metaModelService = this.Container.GetService<IMetaModelService>();
            var applyHtmlForm = metaModelService.LoadFormModel(this.Context,"stk_scheduleapply");
            var applyDM = this.Container.GetService<IDataManager>();
            applyDM.InitDbContext(this.Context, applyHtmlForm.GetDynamicObjectType(this.Context));
            foreach (var dataEntity in dataEntities)
            {
                //下推商品
                var isSuccess = pushEntities(dataEntity,applyDM);

                if (isSuccess)
                {
                    dataEntity["fbizstatus"] = "1";
                }
                else
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"编号为{Convert.ToString(dataEntity["fbillno"])}的排单计划单确认失败!");
                }
            }

            if (this.Result.ComplexMessage.ErrorMessages.Count > 0)
            {
                return;
            }

            //var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
            //prepareSaveDataService.PrepareDataEntity(this.Context, htmlForm, dataEntities, OperateOption.Create());

            //dm.Save(dataEntities);

            //提交计划单
            var option = new Dictionary<string, object>();
            var result = this.Gateway.InvokeBillOperation(this.Context, htmlForm.Id, dataEntities, "submit", option);
            result.ThrowIfHasError(true, "计划单提交失败!");
            //审核计划单
            result = this.Gateway.InvokeBillOperation(this.Context, htmlForm.Id, dataEntities, "audit", option);
            result.ThrowIfHasError(true, "计划单审核失败!");

            this.Result.IsSuccess = true;
        }

        private bool pushEntities(DynamicObject dataEntity,IDataManager applyDM)
        {
            var fentries = dataEntity["fentity"] as DynamicObjectCollection;
            if (fentries == null || fentries.Count <= 0)
            {
                return true;
            }
            var isSuccess = true;
            fentries.Select(x =>
            {
                var fsourceformid = Convert.ToString(x["fsourceformid"]);
                var fsourceinterid = Convert.ToString(x["fsourceinterid"]);
                if (fsourceformid == "ydj_order")
                {
                    return new
                    {
                        fsourceformid = fsourceformid,
                        fsourcebillno = Convert.ToString(x["fsourcebillno"]),
                        fsourceinterid = fsourceinterid,
                        fsourceentryid = Convert.ToString(x["fsourceentryid"]),
                        fscheduleqty = Convert.ToDecimal(x["fscheduleqty"]),
                        fstorehouseidfrom = Convert.ToString(x["fstorehouseidfrom"]),
                        fstorehouseidto = Convert.ToString(x["fstorehouseidto"]),
                        fstockdateto = Convert.ToDateTime(x["fstockdateto"]),
                        fstockdeptidto = Convert.ToString(x["fstockdeptidto"]),
                        fstockstaffidto = Convert.ToString(x["fstockstaffidto"]),
                        fstockdate = Convert.ToDateTime(x["fstockdate"]),
                        fstockdeptid = Convert.ToString(x["fstockdeptid"]),
                        fstockstaffid = Convert.ToString(x["fstockstaffid"]),
                        fapplytype = string.Empty
                    };
                }
                var applyObject = applyDM.Select(fsourceinterid) as DynamicObject;
                if (applyObject == null)
                {
                    throw new BusinessException($"没有找到id为{fsourceinterid}的排单申请单");
                }
                return new
                {
                    fsourceformid = fsourceformid,
                    fsourcebillno = Convert.ToString(x["fsourcebillno"]),
                    fsourceinterid = fsourceinterid,
                    fsourceentryid = Convert.ToString(x["fsourceentryid"]),
                    fscheduleqty = Convert.ToDecimal(x["fscheduleqty"]),
                    fstorehouseidfrom = Convert.ToString(x["fstorehouseidfrom"]),
                    fstorehouseidto = Convert.ToString(x["fstorehouseidto"]),
                    fstockdateto = Convert.ToDateTime(x["fstockdateto"]),
                    fstockdeptidto = Convert.ToString(x["fstockdeptidto"]),
                    fstockstaffidto = Convert.ToString(x["fstockstaffidto"]),
                    fstockdate = Convert.ToDateTime(x["fstockdate"]),
                    fstockdeptid = Convert.ToString(x["fstockdeptid"]),
                    fstockstaffid = Convert.ToString(x["fstockstaffid"]),
                    fapplytype = Convert.ToString(applyObject["fapplytype"])
                };
            }).GroupBy(x => new
            {
                fsourceformid = x.fsourceformid,
                fsourceinterid = x.fsourceinterid,
                fapplytype = x.fapplytype
            }).ForEach(x =>
            {
                var targetFormId = string.Empty;
                var ruleId = string.Empty;
                var entityKey = string.Empty;
                var billNo = Convert.ToString(dataEntity["fbillno"]);
                var sourceFormId = x.Key.fsourceformid;

                switch (sourceFormId)
                {
                    case "ydj_order":
                        ruleId = "ydj_order2sal_deliverynotice";
                        targetFormId = "sal_deliverynotice";
                        entityKey = "fentry";

                        var stockStaffIdGroups = x.GroupBy(z => z.fstockstaffid);
                        foreach (var stockStaffIdGroup in stockStaffIdGroups)
                        {
                            var orderDatas = stockStaffIdGroup.GroupBy(y => y.fsourceentryid).Select(y => new Dictionary<string, object>
                            {
                                { "fsourceentryid",y.Key },
                                { "fscheduleqty",y.Sum(z=>z.fscheduleqty)},
                                { "fstorehouseidfrom",y.FirstOrDefault().fstorehouseidfrom},
                                { "fstorehouseidto",y.FirstOrDefault().fstorehouseidto},
                                { "fstockdateto", y.FirstOrDefault().fstockdateto },
                                { "fstockdeptidto", y.FirstOrDefault().fstockdeptidto },
                                { "fstockstaffidto", y.FirstOrDefault().fstockstaffidto},
                                { "fstockdate", y.FirstOrDefault().fstockdate },
                                { "fstockdeptid", y.FirstOrDefault().fstockdeptid },
                                { "fstockstaffid", y.FirstOrDefault().fstockstaffid },
                                { "fapplytype",y.FirstOrDefault().fapplytype}
                            }).ToList();

                            var selectedOrderRows = stockStaffIdGroup.Select(y => new SelectedRow
                            {
                                PkValue = y.fsourceinterid,
                                EntityKey = entityKey,
                                EntryPkValue = y.fsourceentryid
                            }).ToConvertSelectedRows();

                            isSuccess = pushEntities(ruleId, sourceFormId, targetFormId, billNo, orderDatas, selectedOrderRows);
                            if (false == isSuccess)
                            {
                                return;
                            }
                        }

                        break;
                    case "stk_scheduleapply":
                        switch (x.Key.fapplytype)
                        {
                            case "0":
                            case "1":
                                targetFormId = "sal_returnnotice";
                                ruleId = "stk_scheduleapply2sal_returnnotice";
                                break;
                            case "2":
                            case "4":
                                targetFormId = "stk_inventorytransferreq";
                                ruleId = "stk_scheduleapply2stk_inventorytransferreq";
                                break;
                            case "3":
                                targetFormId = "stk_otherstockoutreq";
                                ruleId = "stk_scheduleapply2stk_otherstockoutreq";
                                break;
                        }
                        entityKey = "fentity";

                        var applyDatas = x.GroupBy(y => y.fsourceentryid).Select(y => new Dictionary<string, object>
                        {
                            { "fsourceentryid",y.Key },
                            { "fscheduleqty",y.Sum(z=>z.fscheduleqty)},
                            { "fstorehouseidfrom",y.FirstOrDefault().fstorehouseidfrom},
                            { "fstorehouseidto",y.FirstOrDefault().fstorehouseidto},
                            { "fstockdateto", y.FirstOrDefault().fstockdateto },
                            { "fstockdeptidto", y.FirstOrDefault().fstockdeptidto },
                            { "fstockstaffidto", y.FirstOrDefault().fstockstaffidto},
                            { "fstockdate", y.FirstOrDefault().fstockdate },
                            { "fstockdeptid", y.FirstOrDefault().fstockdeptid },
                            { "fstockstaffid", y.FirstOrDefault().fstockstaffid },
                            { "fapplytype",y.FirstOrDefault().fapplytype}
                        }).ToList();
                        var selectedApplyRows = x.Select(y => new SelectedRow
                        {
                            PkValue = y.fsourceinterid,
                            EntityKey = entityKey,
                            EntryPkValue = y.fsourceentryid
                        }).ToConvertSelectedRows();
                        isSuccess = pushEntities(ruleId, sourceFormId, targetFormId, billNo, applyDatas, selectedApplyRows);
                        if (false == isSuccess)
                        {
                            return;
                        }
                        break;
                }
            });
            return isSuccess;
        }

        private bool pushEntities(string ruleId, 
                                  string sourceFormId, 
                                  string targetFormId, 
                                  string billNo, 
                                  List<Dictionary<string, object>> entityDatas, 
                                  IEnumerable<ConvertSelectedRow> selectedRows)
        {
            var convertService = this.Container.GetService<IConvertService>();
            var option = OperateOption.Create();
            option.SetVariableValue("schedulePlanNumber", billNo);
            option.SetVariableValue("entityDatas", entityDatas);
            var result = convertService.Push(this.Context, new BillConvertContext()
            {
                RuleId = ruleId,
                SourceFormId = sourceFormId,
                TargetFormId = targetFormId,
                SelectedRows = selectedRows,
                Option = option
            });
            var convertResult = result.SrvData as ConvertResult;
            var targetDatas = convertResult.TargetDataObjects?.ToList();
            if (targetDatas == null || targetDatas.Count <= 0)
            {
                return false;
            }
            var response = this.Gateway.InvokeBillOperation(this.Context, targetFormId, targetDatas, "save", new Dictionary<string, object>());
            if (response == null || response.IsSuccess == false || response.ComplexMessage.ErrorMessages.Count > 0)
            {
                if (response != null && response.ComplexMessage.ErrorMessages.Count > 0)
                {
                    this.Result.ComplexMessage.ErrorMessages.AddRange(response.ComplexMessage.ErrorMessages);
                }
                return false;
            }
            return true;
        }
    }
}
