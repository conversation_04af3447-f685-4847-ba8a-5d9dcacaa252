using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.CustomEventData;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.PoStockReturn
{
    /// <summary>
    /// 采购退货单：选单
    /// </summary>
    [InjectService]
    [FormId("stk_postockreturn")]
    [OperationNo("Pull")]
    public class Pull : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName)
            {
                case OnCustomServiceEventArgs.BeforeGetConvertRule:
                    this.BeforeGetConvertRule(e);
                    break;
                default:
                    break;
            }
        }

        private void BeforeGetConvertRule(OnCustomServiceEventArgs e)
        {
            var dataEntities = e.DataEntities;

            if (dataEntities == null || dataEntities.Length == 0) return;

            var eventData = e.EventData as BeforeGetConvertRuleData;
            if (eventData == null) return;

            var rule = this.MetaModelService.LoadConvertRule(this.Context, eventData.RuleId);
            if (rule == null) return;

            // 源单不是采购入库单，跳过
            if (!rule.SourceFormId.EqualsIgnoreCase("stk_postockin")) return;

            var pkids = eventData.SelectedRows?.Where(s => !s.PkValue.IsNullOrEmptyOrWhiteSpace()).Select(s => s.PkValue).Distinct();
            if (pkids == null || pkids.Count() == 0) return;

            var sourceDataEntities = this.Context.LoadBizDataById(rule.SourceFormId, pkids);
            if (sourceDataEntities == null || sourceDataEntities.Count == 0) return;

            var hasDifferent = sourceDataEntities.Any(x => Convert.ToBoolean(x["frenewalflag"])) && sourceDataEntities.Any(x => !Convert.ToBoolean(x["frenewalflag"]));
            if (hasDifferent)
            {
                throw new BusinessException("仅允许全部选焕新单据或者全部选非焕新单据，请重新选择！");
            }

            //筛选出采购入库单商品明细中的套件头
            // 所有的商品ID
            var allEntrys = sourceDataEntities?.SelectMany(t => t["fentity"] as DynamicObjectCollection);
            var materialIds = allEntrys.Select(entry => Convert.ToString(entry["fmaterialid"])).Where(materialid => !materialid.IsNullOrEmptyOrWhiteSpace())?.Distinct()?.ToList();
            var suiteHeadEntryIds = new List<string>();
            if (materialIds != null && materialIds.Any())
            {
                var productObjs = this.Context.LoadBizBillHeadDataById("ydj_product", materialIds, "fsuiteflag");
                var suiteHeadIds = productObjs.Where(t => Convert.ToString(t["fsuiteflag"]).ToLower() == "true" || Convert.ToString(t["fsuiteflag"]) == "1")
                                              .Select(m => Convert.ToString(m["id"])).ToList();//套件头商品ID
                suiteHeadEntryIds = allEntrys.Where(t => suiteHeadIds.Contains(Convert.ToString(t["fmaterialid"])))
                                             .Select(f => Convert.ToString(f["id"])).ToList();//查询套件头商品所在行的明细行内码
                //将选中行中为套件头的数据清除
                var unHeadSelect = eventData.SelectedRows.Where(t => string.IsNullOrWhiteSpace(t.EntryPkValue) || (!string.IsNullOrWhiteSpace(t.EntryPkValue) && !suiteHeadEntryIds.Contains(t.EntryPkValue)));
                eventData.SelectedRows = unHeadSelect;
            }
            if (!eventData.SelectedRows.Any())
            {
                throw new BusinessException("采购退货单选单无需选择套件头商品，请重新选择！");
            }

            foreach (var dataEntity in dataEntities)
            {
                var renewalflag = Convert.ToBoolean(dataEntity["frenewalflag"]);
                string supplierId = Convert.ToString(dataEntity["fsupplierid"]);
                //仅允许全部选焕新单据或者全部选非焕新单据
                var entities = dataEntity["fentity"] as DynamicObjectCollection;
                var isAllNotEmpty = !supplierId.IsNullOrEmptyOrWhiteSpace()
                                    && (entities != null && entities.Any());
                if (!isAllNotEmpty)
                {
                    var sourceDataEntity = sourceDataEntities.FirstOrDefault();
                    renewalflag = Convert.ToBoolean(sourceDataEntity["frenewalflag"]);
                }

                foreach (var sourceDataEntity in sourceDataEntities)
                {
                    var sourcerenewalflag = Convert.ToBoolean(sourceDataEntity["frenewalflag"]);
                    if (renewalflag != sourcerenewalflag)
                    {
                        throw new BusinessException("仅允许全部选焕新单据或者全部选非焕新单据，请重新选择！");
                    }
                }
            }
        }
    }
}
