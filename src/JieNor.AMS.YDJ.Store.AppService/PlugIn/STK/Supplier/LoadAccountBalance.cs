using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject.Poco;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Supplier
{
    /// <summary>
    /// 供应商：加载协同账户余额
    /// </summary>
    [InjectService]
    [FormId("ydj_supplier")]
    [OperationNo("LoadAccountBalance")]
    public class LoadAccountBalance : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var dataEntity = e.DataEntitys[0];

            var accountSynService = this.Container.GetService<ISynAccountBalanceService>();
            IEnumerable<AccountInfo> lstAllAccount = accountSynService.GetAllAccountBySupplierId(this.Context, dataEntity["id"] as string);

            this.Result.SrvData = lstAllAccount;
            this.Result.IsSuccess = true;
        }
    }
}