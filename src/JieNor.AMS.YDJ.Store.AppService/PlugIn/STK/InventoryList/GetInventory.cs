using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;
using JieNor.Framework.CustomException;
using JieNor.Framework.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using System.Data;
using JieNor.Framework.DataTransferObject;
using Newtonsoft.Json;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Inventory
{
    /// <summary>
    /// 获取商品库存
    /// </summary>
    [InjectService]
    [FormId("stk_inventorylist")]
    [OperationNo("getinventory")]
    public class GetInventory: AbstractOperationServicePlugIn
    {
        public string type = "0";
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            string productInfoStr = this.GetQueryOrSimpleParam<string>("productInfos");
            if (string.IsNullOrWhiteSpace(productInfoStr))
            {
                return;
            }
            JArray productInfos = JArray.Parse(productInfoStr);
            if (productInfos == null || productInfos.Count <= 0)
            {
                return;
            }
            foreach (var productInfo in productInfos)
            {
                if (string.IsNullOrWhiteSpace((string)productInfo["productId"]))
                {
                    throw new BusinessException(string.Format("clientId:{0}所在的行的商品Id为空", (string)productInfo["clientId"]));
                }
            }

            string idType = this.GetQueryOrSimpleParam<string>("idType", "localDataId");
            if (string.IsNullOrWhiteSpace(idType))
            {
                idType = "localDataId";
            }

            List<JToken> results = new List<JToken>();

            byte modeType = this.GetQueryOrSimpleParam<byte>("modeType", byte.MaxValue);

            if (modeType == 0)
            {
                modeType = byte.MaxValue;
            }

            if ((modeType & 1) > 0)
            {
                StringBuilder sql = new StringBuilder();
                //List<SqlParam> sqlParams = new List<SqlParam>
                //{
                //    new SqlParam("@fmainorgid",System.Data.DbType.String,this.Context.Company)
                //};

                //for (var i = 0; i < productInfos.Count; i++)
                //{
                //    var productInfo = productInfos[i] as JObject;
                //    var productId = (string)productInfo["productId"];
                //    JArray attrInfos = productInfo["attrInfos"] as JArray;
                //    var customDesc = (string)productInfo["customDesc"];
                //    if (string.IsNullOrWhiteSpace(customDesc))
                //    {
                //        customDesc = string.Empty;
                //    }

                //    if (attrInfos == null || attrInfos.Count <= 0)
                //    {
                //        //调用无辅助属性的方法
                //        buildQueryParameter(productId, customDesc, idType, sql, sqlParams, i);
                //        continue;
                //    }
                //    //校验辅助属性
                //    var attrInfoList = attrInfos.Where(x => !string.IsNullOrWhiteSpace((string)x["auxPropId"]) && !string.IsNullOrWhiteSpace((string)x["valueId"]))
                //                                .Distinct(x => (string)x["auxPropId"]).ToList();
                //    if (attrInfoList == null || attrInfoList.Count <= 0)
                //    {
                //        //调用无辅助属性的方法
                //        buildQueryParameter(productId, customDesc, idType, sql, sqlParams, i);
                //        continue;
                //    }
                //    //调用有辅助属性的方法
                //    buildQueryParameter(productId, customDesc, idType,sql, sqlParams, i, attrInfoList);
                //}
                
                var filter = "";
                for (var i = 0; i < productInfos.Count; i++)
                {
                    //this.type = "0";
                    var productInfo = productInfos[i] as JObject;
                    var productId = (string)productInfo["productId"];
                    JArray attrInfos = productInfo["attrInfos"] as JArray;
                    this.type = Convert.ToString(productInfo["type"]);
                    var customDesc = (string)productInfo["customDesc"];
                    //找出物料编码 
                    var product = this.Context.LoadBizDataById("ydj_product", productId);
                    if (product.IsNullOrEmptyOrWhiteSpace())
                    {
                        continue;
                    }
                    if (string.IsNullOrWhiteSpace(customDesc))
                    {
                        customDesc = string.Empty;
                    }

                    if (attrInfos == null || attrInfos.Count <= 0)
                    {
                        ////调用无辅助属性的方法
                        //buildQueryParameter(productId, customDesc, idType, sql, sqlParams, i);
                        //filter = buildQueryReportParameter(product, filter, i);
                        //如果type 1，则表示如果传的辅助属性为空,则表示按空辅助属性("fattrinfo=''")去匹配库存 而不是忽略辅助属性条件 直接取商品级。且要根据定制说明过滤
                        if (this.type.EqualsIgnoreCase("1"))
                        {
                            filter= buildQueryReportParameter(product, filter, i, customDesc);
                            filter += " and fattrinfo='' ";
                        }
                        else {
                            filter = buildQueryReportParameter(product, filter, i);
                        }
                        continue;
                    }
                    //校验辅助属性
                    var attrInfoList = attrInfos.Where(x => !string.IsNullOrWhiteSpace((string)x["auxPropId"]) && !string.IsNullOrWhiteSpace((string)x["valueId"]))
                                                .Distinct(x => (string)x["auxPropId"]).ToList();
                    if (attrInfoList == null || attrInfoList.Count <= 0)
                    {
                        ////调用无辅助属性的方法
                        //buildQueryParameter(productId, customDesc, idType, sql, sqlParams, i);
                        filter = buildQueryReportParameter(product, filter, i);
                        continue;
                    }
                    //查询 库存综合查询报表 过滤条件 
                    filter = buildQueryReportParameter(product, filter, i, attrInfoList,customDesc);
                }
                var parentid = this.OperationContext.PageId.IsNullOrEmptyOrWhiteSpace() ? Guid.NewGuid().ToString() : this.OperationContext.PageId;
                var listDto = new CommonListDTO()
                   //.SetPageId(parentid)
                   .SetFormId("rpt_stocksynthesize")
                   .SetOperationNo("querydata")
                   .SetSimpleData("filterString", filter);
                var resp = this.Gateway.InvokeLocal<CommonListDTOResponse>(this.Context, listDto, Enu_HttpMethod.Post);

                JArray dataList = null;
                var srvDataJson = resp?.OperationResult?.SrvData?.ToJson();
                if (!srvDataJson.IsNullOrEmptyOrWhiteSpace())
                {
                    var srvData = JObject.Parse(srvDataJson);
                    dataList = srvData?["data"] as JArray;
                }
                if (dataList != null)
                {
                    var rowIndex = 0;
                    foreach (var data in dataList)
                    {
                        var item = new JObject();
                        item["storeHouseId"] = Convert.ToString(data["fstorehouseid"]);
                        item["storeHouseName"] = Convert.ToString(data["fstorehouseid_fname"]);
                        item["customDesc"] = Convert.ToString(data["fcustomdesc"]);
                        item["qty"] = Convert.ToString(data["fqty"]);    // 库存量 
                        item["reserveQty"] = Convert.ToString(data["freserveqty"]); // 预留量
                        item["usableQty"] = Convert.ToString(data["fusableqty"]); // 可用量
                        item["intransitQty"] = Convert.ToString(data["fintransitqty"]); // 在途量
                        item["storeLocationId"] = Convert.ToString(data["fstorelocationid"]);
                        item["storeLocationName"] = Convert.ToString(data["fstorelocationid_fname"]);
                        item["stockStatus"] = Convert.ToString(data["fstockstatus"]);
                        item["stockStatusName"] = Convert.ToString(data["fstockstatus_fname"]);
                        item["mtono"] = Convert.ToString(data["fmtono"]);
                        var productobj = productInfos.Where(n => Convert.ToString(n["productId"]).EqualsIgnoreCase(Convert.ToString(data["fmaterialid"]))).FirstOrDefault();
                        if (!productobj.IsNullOrEmptyOrWhiteSpace())
                        {
                            item["clientId"] = Convert.ToString(productobj["clientId"]);
                        }
                        item["success"] = true;
                        rowIndex++;
                        results.Add(item);
                    }
                }

                //    using (var dataReader = this.DBService.ExecuteReader(this.Context, sql.ToString(), sqlParams))
                //{
                //    while (dataReader.Read())
                //    {
                //        var item = new JObject();
                //        item["storeHouseId"] = dataReader.GetValueToString("fstorehouseid");
                //        item["storeHouseName"] = dataReader.GetValueToString("fstorehousename");
                //        item["customDesc"] = dataReader.GetValueToString("fcustomdesc");
                //        item["qty"] = dataReader.GetValueToDecimal("fqty");
                //        item["companyName"] = dataReader.GetValueToString("fcompanyname");
                //        item["storeLocationId"] = dataReader.GetValueToString("fstorelocationid");
                //        item["storeLocationName"] = dataReader.GetValueToString("fstorelocationname");
                //        item["stockStatus"] = dataReader.GetValueToString("fstockstatus");
                //        item["stockStatusName"] = dataReader.GetValueToString("fstockstatusname");
                //        item["mtono"] = dataReader.GetValueToString("fmtono");
                //        int rowIndex = Convert.ToInt32(dataReader["frowindex"]);
                //        item["clientId"] = (string)productInfos[rowIndex]["clientId"];
                //        item["success"] = true;
                //        results.Add(item);
                //    }
                //}
            }

            if ((modeType & 2) > 0)
            {
                //直营模式下跨公司查询库存
                results.AddRange(queryInventoryCrossCompany(productInfos, idType).Where(x => (bool)x["success"]));
            }

            productInfos.Where(x => !results.Any(y => (string)x["clientId"] == (string)x["clientId"])).ForEach(x =>
            {
                results.Add(new JObject
                {
                    { "clientId",x["clientId"]},
                    { "success",false}
                });
            });

            this.Result.IsSuccess = true;
            this.Result.SrvData = results;
        }
        private String buildQueryReportParameter(DynamicObject product, string sql, int i,string fcustomdesc="")
        {
            if (i > 0)
            {
                sql += " union all ";
            }
            var fmaterialid = Convert.ToString(product["id"]);
            sql += "fmaterialid = '{0}' ".Fmt(fmaterialid);
            sql += $" and fcustomdesc = '{fcustomdesc}' and fmtono = '' and fstockusableqty >0";
            return sql;
        }

        private String buildQueryReportParameter(DynamicObject product,string sql,int i, List<JToken> attrInfoList,string fcustomdesc="")
        {
            if (i > 0)
            {
                sql += " union all ";
            }
            var fmaterialid = Convert.ToString(product["id"]);
            sql += "fmaterialid = '{0}' ".Fmt(fmaterialid);
            //辅助属性
            for (var j = 0; j < attrInfoList.Count(); j++)
            {
                var attrInfo = attrInfoList[j];
              
                sql+= " and exists (select 1 as c from t_bd_auxpropvalueentry apve1 inner join t_sel_prop ap on ap.fid = apve1.fauxpropid " +
                      "where ap.fid = '{0}' and apve1.fvaluename = '{1}' and apve1.fid = fattrinfo)".Fmt(Convert.ToString(attrInfo["auxPropId"]),Convert.ToString(attrInfo["fvaluename"]));
            }
            sql += $" and fcustomdesc = '{fcustomdesc}' and fmtono = '' and fstockusableqty >0";
            return sql;
        }
        private List<JToken> queryInventoryCrossCompany(JArray productInfos, string idType)
        {
            List<JToken> results = new List<JToken>();

            if (productInfos == null || productInfos.Count <= 0)
            {
                return results;
            }

            //检查调用跳数，超出3跳直接返回不再调用
            var hopCount = this.GetQueryOrSimpleParam<int>("hopCount", 0);
            if (hopCount > 3)
            {
                return results;
            }
            if (hopCount < 0)
            {
                hopCount = 0;
            }
            //找出productids
            var productIds = productInfos.Select(x => (string)x["productId"]).Distinct().ToList();
            if (productIds == null || productIds.Count <= 0)
            {
                return results;
            }
            //检查直营模式下的下载商品
            StringBuilder sql = new StringBuilder(string.Format(@"select m.fid as productid,m.fpublishcid as publishcid,
m.fpublishcid_pid as publishpid,m.ffromchaindataid as  fromchaindataid, s.fname as suppliername, m.fchaindataid as chaindataid
from t_bd_material m 
inner join t_ydj_supplier s on m.fpublishcid=s.fcoocompanyid and m.fpublishcid_pid=s.fcooproductid and m.fmainorgid=s.fmainorgid
where m.FFormId='ydj_product' and m.fdataorigin='下载' and s.foperationmode='1' and m.fmainorgid=@fmainorgid and m.{0} ", getFieldIdByIdType(idType)));

            List<Dictionary<string, string>> productDbInfos = dataReader(sql, productIds);

            if (productDbInfos == null || productDbInfos.Count <= 0)
            {
                return results;
            }

            var auxPropIds = productInfos.Where(x =>
            {
                var attrInfos = x["attrInfos"] as JArray;
                return attrInfos != null && attrInfos.Count > 0;
            }).SelectMany(x => (x["attrInfos"]).Select(y => (string)y["auxPropId"])).Where(x => !string.IsNullOrEmpty(x)).Distinct().ToList();

            sql = new StringBuilder(string.Format(@"select ap.fid as auxpropid,ap.fpublishcid as publishcid,ap.fpublishcid_pid as publishpid,ap.ffromchaindataid as fromchaindataid
from t_sel_prop ap where ap.fdataorigin='下载' and ap.fmainorgid=@fmainorgid and ap.{0} ", getFieldIdByIdType(idType)));

            List<Dictionary<string, string>> auxPropDbInfos = dataReader(sql, auxPropIds);


            //调用商品供应商接口
            productDbInfos.GroupBy(x => string.Join(",", x["publishcid"], x["publishpid"])).ForEach(x =>
            {
                TargetSEP target = null;
                foreach (var item in x)
                {
                    if (target == null)
                    {
                        target = new TargetSEP(item["publishcid"], item["publishpid"]);
                    }
                    var postData = productInfos.DeepClone().Where(y =>
                    {
                        if ((string)y["productId"] != item["productid"] || string.IsNullOrWhiteSpace(item["fromchaindataid"]))
                        {
                            return false;
                        }
                        y["productId"] = item["fromchaindataid"];
                        y["lot"] = "";//批次号。K3以此作为新旧接口兼容的参数。
                        var attrInfos = y["attrInfos"] as JArray;
                        if (attrInfos == null || attrInfos.Count <= 0)
                        {
                            return true;
                        }
                        foreach (var attrInfo in attrInfos)
                        {
                            var auxPropId = (string)attrInfo["auxPropId"];
                            var auxPopItem = auxPropDbInfos.FirstOrDefault(z => z["auxpropid"] == auxPropId &&
                                                                                z["publishcid"] == item["publishcid"] &&
                                                                                z["publishpid"] == item["publishpid"]);
                            if (auxPopItem == null)
                            {
                                return false;
                            }
                            attrInfo["auxPropId"] = auxPopItem["fromchaindataid"];
                        }
                        return true;
                    });
                    var response = this.Gateway.Invoke(
                                        this.Context,
                                        target,
                                        new CommonBillDTO()
                                        {
                                            FormId = this.HtmlForm.Id,
                                            OperationNo = this.OperationNo,
                                            ExecInAsync = false,
                                            AsyncMode = (int)Enu_AsyncMode.Background,
                                            SimpleData = new Dictionary<string, string>
                                            {
                                                { "productInfos", Newtonsoft.Json.JsonConvert.SerializeObject(postData) },
                                                { "hopCount",(++hopCount).ToString()},
                                                { "idType","chainDataId"},
                                                { "modeType",byte.MaxValue.ToString()}
                                            }
                                        }.SetOptionFlag((long)Enu_OpFlags.TPSRequest)
                                    ) as CommonBillDTOResponse;

                    var srvData = response.OperationResult.SrvData;
                    if (srvData != null)
                    {
                        if (srvData is string)
                        {
                            results.AddRange(JArray.Parse(srvData.ToString()));
                        }
                        else
                        {
                            results.AddRange(JArray.FromObject(srvData));
                        }
                    }
                }
            });
            foreach(var item in results)
            {
                if (((bool)item["success"]) && (string)item["companyName"] == "自有仓库")
                {
                    string clientId = (string)item["clientId"];
                    var productInfo = productInfos.FirstOrDefault(x => (string)x["clientId"] == clientId);
                    if (productInfo == null)
                    {
                        continue;
                    }

                    var productId = (string)productInfo["productId"];
                    var idName = isLocalDataId(idType) ? "productid" : "chaindataid";
                    var productDbInfo = productDbInfos.FirstOrDefault(x => (string)x[idName] == productId);

                    if (productDbInfo == null || string.IsNullOrWhiteSpace(productDbInfo["suppliername"]))
                    {
                        continue;
                    }

                    item["companyName"] = productDbInfo["suppliername"];
                }

                item["expectedArrival"] = item["deliverydate"] ?? "";
                var status = (item["status"] ?? "").ToString();
                switch (status)
                {
                    case "1":
                        item["stockStatusName"] = "可销库存";
                        break;
                    case "2":
                        item["stockStatusName"] = "采购在途";
                        break;
                    default:
                        item["stockStatusName"] = item["stockStatusName"] ?? "";
                        break;
                }
                item["status"] = status;
            }
            return results;
        }

        private List<Dictionary<string, string>> dataReader(StringBuilder sql, List<string> ids)
        {
            List<Dictionary<string, string>> results = new List<Dictionary<string, string>>();
            if (ids == null || ids.Count <= 0)
            {
                return results;
            }

            List<SqlParam> sqlParams = new List<SqlParam>()
            {
                new SqlParam("@fmainorgid",DbType.String,this.Context.Company)
            };

            if (ids.Count == 1)
            {
                sql.Append(" = @id");
                sqlParams.Add(new SqlParam("@id", DbType.String, ids[0]));
            }
            else
            {
                sql.Append(" in (");
                sql.Append(string.Join(",", ids.Select((x, i) => string.Format("@id{0}", i))));
                sql.Append(")");
                sqlParams.AddRange(ids.Select((x, i) => new SqlParam(string.Format("@id{0}", i), DbType.String, x)));
            }

            using (var dataReader = this.DBService.ExecuteReader(this.Context, sql.ToString(), sqlParams))
            {
                while (dataReader.Read())
                {
                    Dictionary<string, string> result = new Dictionary<string, string>();
                    for (var i = 0; i < dataReader.FieldCount; i++)
                    {
                        result[dataReader.GetName(i)] = Convert.ToString(dataReader.GetValue(i));
                    }
                    results.Add(result);
                }
            }
            return results;
        }

        private void buildQueryParameter(string idType, StringBuilder sql, List<SqlParam> sqlParams, List<JToken> attrInfoList)
        {
            string sqlFormat = @"
select 1 as c from t_bd_auxpropvalueentry apve1 
inner join t_sel_prop ap on ap.fid=apve1.fauxpropid
where ap.{1}=@fauxpropid{0} and apve1.fvalueid=@fvalueid{0} and apve1.fid=l.fattrinfo
";
            foreach (var attrInfo in attrInfoList)
            {
                int count = sqlParams.Count;
                if (sql.Length > 0)
                {
                    sql.AppendLine(" union all ");
                }
                sql.AppendFormat(sqlFormat, count, getFieldIdByIdType(idType));
                sqlParams.AddRange(new[]
                {
                    new SqlParam(string.Format("@fauxpropid{0}",count),System.Data.DbType.String,(string)attrInfo["auxPropId"]),
                    new SqlParam(string.Format("@fvalueid{0}",count),System.Data.DbType.String,(string)attrInfo["valueId"])
                });
            }
        }

        private void buildQueryParameter(string productId, string customDesc, string idType, StringBuilder sql, List<SqlParam> sqlParams, int rowIndex, List<JToken> attrInfoList)
        {
            string sqlFormat = @"
select t.fstorehouseid,h.fname as fstorehousename,t.fqty,{0} as frowindex,t.fcustomdesc,
(
case when h.fstorehousetype='storehouse_type_02'
	 then (select fname from t_ydj_supplier s where s.fid=h.fsupplierid )
	 else 
	 (select fenumitem from t_bd_enumdataentry ede where ede.fentryid=h.fstorehousetype)
	 end
) as fcompanyname,
t.fstorelocationid,hl.flocname as fstorelocationname,t.fstockstatus,ss.fname as fstockstatusname,t.fmtono
from (
select l.fstorehouseid,sum(l.fqty) as fqty,l.fstorelocationid,l.fstockstatus,l.fmtono,l.fcustomdesc
from t_stk_inventorylist l
inner join t_bd_material m on l.fmaterialid=m.fid and l.fmainorgid=m.fmainorgid
where m.{4}=@fmaterialid{1} and l.fmainorgid=@fmainorgid and l.fstockstatus='311858936800219137' and l.fcustomdesc=@fcustomdesc{1} and
(select count(1) from t_bd_auxpropvalueentry apve where apve.fid=l.fattrinfo)={2} and
(select count(1) from(
{3}
) d)={2}
group by l.fstorehouseid,l.fstorelocationid,l.fstockstatus,l.fmtono,l.fcustomdesc
)t
left join t_ydj_storehouse h on h.fid=t.fstorehouseid
left join t_ydj_storehouselocation hl on hl.fentryid=fstorelocationid
left join t_ydj_stockstatus ss on ss.fid=t.fstockstatus
";

            StringBuilder subSql = new StringBuilder();
            buildQueryParameter(idType, subSql, sqlParams, attrInfoList);
            int count = sqlParams.Count;
            if (sql.Length > 0)
            {
                sql.AppendLine(" union all ");
            }
            sql.AppendFormat(sqlFormat, rowIndex, count, attrInfoList.Count, subSql.ToString(), getFieldIdByIdType(idType));
            sqlParams.AddRange(new[]
            {
                new SqlParam(string.Format("@fmaterialid{0}",count),System.Data.DbType.String,productId),
                new SqlParam(string.Format("@fcustomdesc{0}",count),System.Data.DbType.String,customDesc)
            });
        }

        private void buildQueryParameter(string productId, string customDesc, string idType, StringBuilder sql, List<SqlParam> sqlParams, int rowIndex)
        {
            string sqlFormat = @"
select t.fstorehouseid,h.fname as fstorehousename,t.fqty,{0} as frowindex,t.fcustomdesc,
(
case when h.fstorehousetype='storehouse_type_02'
	 then (select fname from t_ydj_supplier s where s.fid=h.fsupplierid )
	 else 
	 (select fenumitem from t_bd_enumdataentry ede where ede.fentryid=h.fstorehousetype)
	 end
) as fcompanyname,
t.fstorelocationid,hl.flocname as fstorelocationname,t.fstockstatus,ss.fname as fstockstatusname,t.fmtono
from (
select l.fstorehouseid,sum(l.fqty) as fqty,l.fstorelocationid,l.fstockstatus,l.fmtono,l.fcustomdesc 
from t_stk_inventorylist l 
inner join t_bd_material m on l.fmaterialid=m.fid and l.fmainorgid=m.fmainorgid
where m.{2}=@fmaterialid{1} and l.fmainorgid=@fmainorgid and l.fstockstatus='311858936800219137' and l.fattrinfo='' and l.fcustomdesc=@fcustomdesc{1}
group by l.fstorehouseid,l.fstorelocationid,l.fstockstatus,l.fmtono,l.fcustomdesc
)t
left join t_ydj_storehouse h on h.fid=t.fstorehouseid
left join t_ydj_storehouselocation hl on hl.fentryid=fstorelocationid
left join t_ydj_stockstatus ss on ss.fid=t.fstockstatus
";
            int count = sqlParams.Count;
            if (sql.Length > 0)
            {
                sql.AppendLine(" union all ");
            }
            sql.AppendFormat(sqlFormat, rowIndex, count, getFieldIdByIdType(idType));
            sqlParams.AddRange(new[]
            {
                new SqlParam(string.Format("@fmaterialid{0}",count),System.Data.DbType.String,productId),
                new SqlParam(string.Format("@fcustomdesc{0}",count),System.Data.DbType.String,customDesc)
            });
        }

        static string getFieldIdByIdType(string idType)
        {
            return isLocalDataId(idType) ? "fid" : "fchaindataid";
        }

        static bool isLocalDataId(string idType)
        {
            return idType == "localDataId";
        }
    }
}
