using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SaleIntention
{
    /// <summary>
    /// 即使库存：盘点单的“设置盘点范围”操作时根据即时库存主键Id返回数据
    /// </summary>
    [InjectService]
    [FormId("stk_inventorylist")]
    [OperationNo("LoadInventoryList")]
    public class LoadInventoryList : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            //加载引用数据
            var refObjMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), e.DataEntitys, false);

            var uiConverter = this.Context.Container.GetService<IUiDataConverter>();

            JArray jaUiData = new JArray();
            foreach (var dataEntity in e.DataEntitys)
            {
                var joUiData = uiConverter.CreateUIDataObject(this.Context, this.HtmlForm, dataEntity);
                jaUiData.Add(joUiData["uidata"]);
            }
            
            this.Result.SrvData = jaUiData;
            this.Result.IsSuccess = true;
        }
    }
}