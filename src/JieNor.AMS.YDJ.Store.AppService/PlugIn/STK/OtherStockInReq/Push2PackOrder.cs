using JieNor.AMS.YDJ.Store.AppService.Plugin.STK.PurReceiptNotice;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.OtherStockInReq
{
    /// <summary>
    /// 其它入库通知单:下推包装清单
    /// </summary>
    [InjectService]
    [FormId("stk_otherstockinreq")]
    [OperationNo("push2packorder")]
    public class Push2PackOrder : BasePush2PackOrder
    {
    }
}
