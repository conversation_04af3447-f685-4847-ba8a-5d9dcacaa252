using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.Framework.MetaCore.FormOp;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PoStockIn
{
    /// <summary>
    /// 采购入库单：反审核时检查关联的预留信息
    /// </summary>
    [InjectService]
    [FormId("stk_postockin")]
    [OperationNo("CheckLinkReserveInfo")]
    public class CheckLinkReserveInfo : AbstractOperationServicePlugIn
    {

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            List<string> ids = new List<string>();
            var str = this.GetQueryOrSimpleParam<string>("pkid");
            if(str.IsNullOrEmptyOrWhiteSpace ()==false)
            ids.Add(str);
            if (this.SelectedRows != null)
            {
                ids.AddRange(this.SelectedRows.Select(f => f.PkValue));
            }

            CheckExistReserveInfo(ids);
        }



        private void CheckExistReserveInfo( List <string > ids)
        { 
            var sql = @"select distinct * from
                        (
                        /*销售合同----采购订单----采购入库*/
                        select distinct a.fid as forderid,a.fbillno as forderno,b.fentryid as forderenid,b.fqty as fqty_so,b.fbizqty as fbizqty_so,
	                        x.fid as fstkinid,x.fbillno as fstkinno,y.fentryid as fstkinenid,y.fqty as fqty_in,y.fbizqty as fbizqty_in,
                            y.fstorehouseid,y.fstorelocationid,y.fstockstatus,b.fclosestatus,y.fseq,rsv.fbillno as freserveno, mat.fnumber,mat.fname
                        from t_ydj_order a
                        inner join t_ydj_orderentry b on a.fid=b.fid and b.fclosestatus<>'3' and b.fclosestatus<>'4'
                        inner join t_ydj_poorderentry d on d.fsourceformid='ydj_order' and d.fsourceinterid =a.fid and d.fsourceentryid=b.fentryid 
                        inner join t_ydj_purchaseorder c on c.fid=d.fid
                        inner join t_stk_postockinentry y on y.fsourceformid='ydj_purchaseorder' and y.fsourcebillno =c.fbillno and y.fsourceentryid=d.fentryid 
                        inner join t_stk_postockin x on x.fid=y.fid and x.fsourcetype ='ydj_purchaseorder' 
                        inner join t_stk_reservebillentry r on b.fentryid=r.fsourceentryid  and b.fid=r.fsourceinterid and r.fqty>0
                        inner join t_stk_reservebilldetail p on r.fentryid=p.fentryid and p.ffromformid='stk_postockin'
                        inner join t_stk_reservebill rsv on rsv.fid=r.fid 
                        inner join t_bd_material mat on y.fmaterialid=mat.fid 
                        where x.fid in ({0}) 

                        union all

                        /*销售合同----采购订单----收货通知单----采购入库*/
                        select distinct a.fid as forderid,a.fbillno as forderno,b.fentryid as forderenid,b.fqty as fqty_so,b.fbizqty as fbizqty_so,
	                        x.fid as fstkinid,x.fbillno as fstkinno,y.fentryid as fstkinenid,y.fqty as fqty_in,y.fbizqty as fbizqty_in ,
                            y.fstorehouseid,y.fstorelocationid,y.fstockstatus,b.fclosestatus,y.fseq,rsv.fbillno as freserveno, mat.fnumber,mat.fname
                        from t_ydj_order a
                        inner join t_ydj_orderentry b on a.fid=b.fid   and b.fclosestatus<>'3' and b.fclosestatus<>'4'
                        inner join t_ydj_poorderentry d on d.fsourceformid='ydj_order' and d.fsourceinterid =a.fid and d.fsourceentryid=b.fentryid 
                        inner join t_ydj_purchaseorder c on c.fid=d.fid
                        inner join t_pur_receiptnoticeentry j on   j.fsourceformid='ydj_purchaseorder' and j.fsourcebillno =c.fbillno and j.fsourceentryid=d.fentryid 
                        inner join t_pur_receiptnotice k on   k.fid=j.fid 
                        inner join t_stk_postockinentry y on y.fsourceformid='pur_receiptnotice' and y.fsourcebillno =k.fbillno and y.fsourceentryid=j.fentryid 
                        inner join t_stk_postockin x on x.fid=y.fid and x.fsourcetype ='pur_receiptnotice' 
                        inner join t_stk_reservebillentry r on b.fentryid=r.fsourceentryid  and b.fid=r.fsourceinterid  and r.fqty>0
                        inner join t_stk_reservebilldetail p on r.fentryid=p.fentryid and p.ffromformid='stk_postockin'
                        inner join t_stk_reservebill rsv on rsv.fid=r.fid 
                        inner join t_bd_material mat on y.fmaterialid=mat.fid 
                        where x.fid in ({0})


						union all

						/*销售合同----预留单----采购入库*/
                        select distinct a.fid as forderid,a.fbillno as forderno,b.fentryid as forderenid,b.fqty as fqty_so,b.fbizqty as fbizqty_so,
	                        x.fid as fstkinid,x.fbillno as fstkinno,y.fentryid as fstkinenid,y.fqty as fqty_in,y.fbizqty as fbizqty_in,
                            y.fstorehouseid,y.fstorelocationid,y.fstockstatus,b.fclosestatus,y.fseq,rsv.fbillno as freserveno, mat.fnumber,mat.fname
                        from t_ydj_order a
                        inner join t_ydj_orderentry b on a.fid=b.fid and b.fclosestatus<>'3' and b.fclosestatus<>'4'
                        inner join t_stk_reservebillentry r on b.fentryid=r.fsourceentryid  and b.fid=r.fsourceinterid and r.fqty>0
                        inner join t_stk_reservebilldetail p on r.fentryid=p.fentryid and p.ffromformid='stk_postockin'
                        inner join t_stk_reservebill rsv on rsv.fid=r.fid 
                        inner join t_stk_postockinentry y on y.fentryid=p.ffrombillpkid and p.ffromformid='stk_postockin' 
                        inner join t_stk_postockin x on x.fid=y.fid
                        inner join t_bd_material mat on y.fmaterialid=mat.fid 
                        where x.fid in ({0}) 
                        ) t
                        ".Fmt(ids.JoinEx(",", true));
            var mapDatas = this.Container.GetService<IDBService>().ExecuteDynamicObject(Context, sql)?.ToList ();
            if (mapDatas != null && mapDatas.Count > 0)
            {
                mapDatas = mapDatas.OrderBy(f => Convert.ToString(f["fstkinno"])).ThenBy(f => Convert.ToInt32(f["fseq"])).ToList();
                this.Result.SrvData = mapDatas.Select(f => @"第{0}行入库商品【{1}】存在销售合同 {2} 生成的预留单 {3}，反审核会执行删除关联的预留数据，是否继续？".Fmt(f["fseq"], f["fname"], f["forderno"], f["freserveno"])).JoinEx("<br/>", false);
            }
            else
            {
                this.Result.SrvData = "";
            }
                 
        }


            


    }
}