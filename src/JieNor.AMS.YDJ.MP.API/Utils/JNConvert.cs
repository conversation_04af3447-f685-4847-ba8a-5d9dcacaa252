using JieNor.AMS.YDJ.MP.API.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Utils
{
    public static class JNConvert
    {
        public static string ToStringAndTrim(this object value)
        {
            return Convert.ToString(value).Trim();
        }

        /// <summary>
        /// 保留有效小数
        /// </summary>
        /// <param name="value">数值</param>
        /// <param name="decimals">有效小数位</param>
        /// <returns></returns>
        public static decimal ToNumber(this decimal value, int decimals = 2)
        {
            return decimal.Round(value, decimals);
        }

        public static ComboDataModel GetStatusMdl(string status)
        {
            ComboDataModel model = new ComboDataModel();
            model.Id = status;
            switch (status)
            {
                case "A":
                    model.Name = "关联暂存";
                    break;
                case "B":
                    model.Name = "创建";
                    break;
                case "C":
                    model.Name = "重新审核";
                    break;
                case "D":
                    model.Name = "已提交";
                    break;
                case "E":
                    model.Name = "已审核";
                    break;
            }
            return model;
        }
    }
}
