using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 多类别基础资料取数接口，比如：客户、员工、部门 等等
    /// </summary>
    [Api("多类别基础资料取数接口")]
    [Route("/mpapi/mulclasstypedata/list")]
    [Authenticate]
    public class MulClassTypeDataListDTO : BaseListPageDTO
    {
        /// <summary>
        /// 表单标识，比如：预留单 stk_reservebill
        /// </summary>
        public string FormId { get; set; }

        /// <summary>
        /// 字段标识，比如：预留对象 freserveobjectid
        /// </summary>
        public string FieldKey { get; set; }

        /// <summary>
        /// 多类别基础资料类型Id
        /// </summary>
        public string MulClassTypeId { get; set; }
    }
}