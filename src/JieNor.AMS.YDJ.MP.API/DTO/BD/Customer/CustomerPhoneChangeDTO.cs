using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.BD.Customer
{
    /// <summary>
    /// 客户保存成功后更新下游相关单据客户手机号
    /// </summary>
    [Api("客户保存成功后更新下游相关单据客户手机号")]
    [Route("/mpapi/customer/phonechange")]
    [Authenticate]
    public class CustomerPhoneChangeDTO : BaseDetailDTO
    {
    }
}
