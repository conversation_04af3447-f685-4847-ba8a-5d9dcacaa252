using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.BD.Customer
{
    /// <summary>
    /// 客户详情取数接口
    /// </summary>
    [Api("客户详情取数接口")]
    [Route("/mpapi/customer/detail")]
    [Authenticate]
    public class CustomerCheckMergeDTO
    {
        /// <summary>
        /// 客户id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 客户电话
        /// </summary>
        public string Phone { get; set; }

    }
}
