using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 商品辅属性取数接口
    /// </summary>
    [Api("商品辅属性取数接口")]
    [Route("/mpapi/product/subAuxProp")]
    [Authenticate]
    public class ProductSubAuxPropDTO : BaseDetailDTO
    {
        /// <summary>
        /// 辅助属性值
        /// </summary>
        public List<Dictionary<string, string>> AuxPropVals { get; set; }

        /// <summary>
        /// 选中的主属性值
        /// </summary>
        public Dictionary<string, string> MainAuxPropVal { get; set; }
    }
}