using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.Utils;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 商品扫码取数接口
    /// </summary>
    [Api("商品扫码取数接口")]
    [Route("/mpapi/product/scancode")]
    [Authenticate]
    public class ProductScanCodeDTO
    {
        private string _CodeType = "qrcode";
        /// <summary>
        /// 条码类型：barcode 条形码，qrcode 二维码，默认为 qrcode
        /// </summary>
        public string CodeType
        {
            get { return _CodeType; }
            set
            {
                if (string.IsNullOrWhiteSpace(value))
                {
                    value = "qrcode";
                }
                _CodeType = value;
            }
        }

        private string _CodeContent = string.Empty;
        /// <summary>
        /// 条码内容
        /// </summary>
        public string CodeContent
        {
            get { return _CodeContent; }
            set
            {
                //对条码内容进行Url解码，因为条码内容中可能存在中文字符，而中文字符在条码中是以Url编码后的方式存在的
                if (!string.IsNullOrWhiteSpace(value))
                {
                    _CodeContent = value.Replace("+", "%2b").UrlDecode();
                }
            }
        }
    }
}