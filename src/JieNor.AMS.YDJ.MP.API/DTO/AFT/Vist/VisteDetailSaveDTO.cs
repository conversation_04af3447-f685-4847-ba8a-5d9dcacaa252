using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using ServiceStack;
using System;
using System.Collections.Generic;
using JieNor.AMS.YDJ.MP.API.Filter;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 服务单主动评价保存接口数据传输对象
    /// </summary>
    [Api("服务单保存接口")]
    [Route("/mpapi/vist/detailsave")]
    [Authenticate]
    [RepeatedSubmitFilter("ydj_vist")]
    public class VisteDetailSaveDTO
    {
        /// <summary>
        /// 回访单id
        /// </summary>
        public string vistId { get; set; }

        /// <summary>
        /// 评价内容
        /// </summary>
        public string VistResult { get; set; }

        /// <summary>
        /// 投诉建议
        /// </summary>
        public string CustomerComplaint { get; set; }

        /// <summary>
        /// 是否愿意推荐我们的产品
        /// </summary>
        public bool IsRecommend { get; set; }

        #region 增值服务评价
        /// <summary>
        /// 服务评价
        /// </summary>
        public string ServiceScore { get; set; }
        #endregion

        #region 送装服务评价
        /// <summary>
        /// 产品质量评价
        /// </summary>
        public string Score { get; set; }

        /// <summary>
        /// 销售服务评价
        /// </summary>
        public string SaleScore { get; set; }

        /// <summary>
        /// 送装服务评价
        /// </summary>
        public string InstallScore { get; set; }
        #endregion

        #region 售后服务评价
        /// <summary>
        /// 售后是否有及时联系
        /// </summary>
        public bool InTime { get; set; }
        /// <summary>
        /// 售后师傅处理服务是否满意
        /// </summary>
        public bool IsSatisfy { get; set; }
        #endregion
    }
}

