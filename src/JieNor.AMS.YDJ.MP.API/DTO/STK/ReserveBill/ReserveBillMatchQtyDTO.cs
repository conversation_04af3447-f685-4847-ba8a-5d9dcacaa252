using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 预留数量取数接口
    /// </summary>
    [Api("预留数量取数接口")]
    [Route("/mpapi/reservebill/matchqty")]
    [Authenticate]
    public class ReserveBillMatchQtyDTO
    {
        /// <summary>
        /// 源单类型ID，比如：ydj_saleintention 销售意向、ydj_order 销售合同
        /// </summary>
        public string SourceFormId { get; set; }

        /// <summary>
        /// 源单单据ID，比如：销售意向ID、销售合同ID
        /// </summary>
        public string SourceOrderId { get; set; }

        /// <summary>
        /// 预留对象类型
        /// </summary>
        public string ReserveObjectType { get; set; }

        /// <summary>
        /// 预留对象
        /// </summary>
        public string ReserveObjectId { get; set; }

        /// <summary>
        /// 明细数据
        /// </summary>
        public List<ReserveBillMatchQtyEntryDTO> EntryList { get; set; }
    }

    /// <summary>
    /// 预留数量明细模型
    /// </summary>
    public class ReserveBillMatchQtyEntryDTO
    {
        /// <summary>
        /// 源单明细ID
        /// </summary>
        public string SourceEntryId { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        public string StoreHouseId { get; set; }

        /// <summary>
        /// 仓位
        /// </summary>
        public string StoreLocationId { get; set; }
    }
}