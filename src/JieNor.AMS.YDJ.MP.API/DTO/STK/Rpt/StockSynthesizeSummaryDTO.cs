using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.STK.Rpt
{
    /// <summary>
    /// 库存汇总查询取数接口
    /// </summary>
    [Api("库存汇总查询取数接口")]
    [Route("/mpapi/stocksynthesize/summary")]
    [Authenticate]
    public class StockSynthesizeSummaryDTO : BaseListPageDTO
    {
        /// <summary>
        /// 品类ID
        /// </summary>
        public string CategoryId { get; set; }

        /// <summary>
        /// 品牌ID集合
        /// </summary>
        public List<string> BrandIds { get; set; }

        /// <summary>
        /// 风格ID集合
        /// </summary>
        public List<string> StyleIds { get; set; }

        /// <summary>
        /// 空间ID集合
        /// </summary>
        public List<string> SpaceIds { get; set; }

        /// <summary>
        /// 系列id
        /// </summary>
        public string SeriesId { get; set; }



        /// <summary>
        /// 来源业务单据标识
        /// </summary>
        public string SrcFormId { get; set; }


        /// <summary>
        /// 单据类型名称
        /// </summary>
        public string BillTypeName { get; set; }

        /// <summary>
        /// 单据类型编码
        /// </summary>
        public string BillTypeNo { get; set; }


    }
}
