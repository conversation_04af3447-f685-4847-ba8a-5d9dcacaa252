using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.MP.API.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.MP.API.DTO.SEL.Suite
{
    /// <summary>
    /// 套件默认DTO
    /// </summary>
    public class SuiteBaseDTO
    {
        /// <summary>
        /// 商品信息
        /// </summary>
        public BaseDataDTO Product { get; set; }

        /// <summary>
        /// 子件列表
        /// </summary>
        public List<PartsBaseDTO> Parts { get; set; }

        /// <summary>
        /// 商品图片列表
        /// </summary>
        public List<BaseImageModel> ImageList { get; set; }


        /// <summary>
        /// 当前商品类别 所有层级是否含有沙发类
        /// </summary>
        public bool IsSofaCategory { get; set; }

        /// <summary>
        /// "套件总件数"属性
        /// </summary>
        public BaseDataDTO SuiteSumQtyProp { get; set; }
    }

    /// <summary>
    /// 子件默认DTO
    /// </summary>
    public class PartsBaseDTO
    {
        /// <summary>
        /// 商品信息
        /// </summary>
        public BaseDataDTO Product { get; set; }

        /// <summary>
        /// 是否默认
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// 非标产品
        /// </summary>
        public bool IsNonStandard { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal Qty { get; set; }

        /// <summary>
        /// 销售价
        /// </summary>
        public decimal SalPrice { get; set; }

        /// <summary>
        /// 总部零售价
        /// </summary>
        public decimal HqSalPrice { get; set; }

        /// <summary>
        /// 品牌指导价
        /// </summary>
        public decimal GuidePrice { get; set; }

        /// <summary>
        /// 子件描述
        /// </summary>
        public string Desc { get; set; }

        /// <summary>
        /// 商品分类
        /// </summary>
        public BaseDataDTO ProductCategory { get; set; }

        /// <summary>
        /// 属性列表
        /// </summary>
        public List<PropEntity> AuxPropVals { get; set; } = new List<PropEntity>();

        /// <summary>
        /// 商品图片列表
        /// </summary>
        public List<BaseImageModel> ImageList { get; set; }

        [IgnoreDataMember]
        public string ProductCategoryId { get; set; }

        /// <summary>
        /// 定制说明
        /// </summary>
        public string customdes { get; set; }


        /// <summary>
        /// 当前商品类别 所有层级是否含有沙发类
        /// </summary>
        public bool IsSofaCategory { get; set; }

        /// <summary>
        /// 子件属性
        /// </summary>
        public BaseDataDTO PartProp { get; set; }

        /// <summary>
        /// 是否允许选配
        /// </summary>
        public bool IsPresetProp { get; set; }

        /// <summary>
        /// 商品促销id
        /// </summary>
        public JObject fpromotionid { get; set; }

        /// <summary>
        /// 促销规则
        /// </summary>
        public string fpromotionrule { get; set; }

        /// <summary>
        /// 特价销售价
        /// </summary>
        public decimal fpromotionsalprice { get; set; }

        /// <summary>
        /// 特价最低价
        /// </summary>
        public decimal fpromotionlowestprice { get; set; }

    }

    //public class PropEntity
    //{
    //    public string PropId { get; set; }
    //    public string PropName { get; set; }
    //    public string PropNumber { get; set; }
    //    public string PropValueDataType { get; set; }
    //    public string ValueId { get; set; }
    //    public string ValueName { get; set; }
    //    public string ValueNumber { get; set; }
    //    public bool IsMust { get; set; }
    //}
}
