using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 用户更新信息接口
    /// </summary>
    [Api("用户更新信息接口")]
    [Route("/mpapi/user/updateprofile")]
    [Authenticate]
    public class UpdateUserProfileDTO
    {
        /// <summary>
        /// 订单数据范围（意向单/合同单）
        /// </summary>
        public string OrderDataScope { get; set; }
    }
}