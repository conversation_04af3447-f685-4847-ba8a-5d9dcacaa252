using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 审批同意接口
    /// </summary>
    [Api("审批同意接口")]
    [Route("/mpapi/approval/audit")]
    [Authenticate]
    public class ApprovalAuditDTO : BaseDTO
    {
        /// <summary>
        /// 源单id
        /// </summary>
        public string SourceId { get; set; }

        /// <summary>
        /// 源单类型
        /// </summary>
        public string SourceType { get; set; }

        /// <summary>
        /// 同意原因
        /// </summary>
        public string Reason { get; set; }
    }
}