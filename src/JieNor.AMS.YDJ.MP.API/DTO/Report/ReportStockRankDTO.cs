using ServiceStack;
using System;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
	/// <summary>
	/// 微信小程序：小程序-统计-库存模块统计
	/// </summary>
	[Api("小程序-统计-库存模块")]
	[Route("/mpapi/stockrank/reportinfo")]
	[Authenticate]
	public class ReportStockRankDTO : BaseDTO
	{
		/// <summary>
		/// 数据类型
		/// StockInfo：库存概览
		/// StockTypeTop10：库存品类数量TOP10
		/// StockAmountZB：库存品类金额占比
		/// StockSeriesZB：库存系列金额占比
		/// SelTypeAmountTop10：下钻型号金额top10
		/// SelTypeCountTop10：下钻型号数量top10
		/// </summary>
		public string ReportType { get; set; }
		/// <summary>
		/// 仓库类型
		/// </summary>
		public string WarehouseType { get; set; } = string.Empty;
		/// <summary>
		/// 仓库id
		/// </summary>
		public string WarehouseId { get; set; } = string.Empty;
		/// <summary>
		/// 品类Id
		/// </summary>
		public string CategoryId { get; set; } = string.Empty;
		/// <summary>
		/// 系列Id
		/// </summary>
		public string SeriesId { get; set; } = string.Empty;
	}
}
