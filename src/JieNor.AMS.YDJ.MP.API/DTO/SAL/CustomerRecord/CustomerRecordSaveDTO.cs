using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using ServiceStack;
using System;
using System.Collections.Generic;
using JieNor.AMS.YDJ.MP.API.Filter;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 商机保存接口
    /// </summary>
    [Api("商机保存接口")]
    [Route("/mpapi/customerrecord/save")]
    [Authenticate]
    [RepeatedSubmitFilter("ydj_customerrecord")]
    public class CustomerRecordSaveDTO : RepeatedSubmitDTO
    {
        #region 商机相关

        /// <summary>
        /// 意向商品
        /// </summary>
        public List<ProductModel> ProdList { get; set; }

        /// <summary>
        /// 企业微信用户编码
        /// </summary>
        public string WorkWxUserid { get; set; }

        /// <summary>
        /// 负责人
        /// </summary>
        public string DutyId { get; set; }

        /// <summary>
        /// 门店
        /// </summary>
        public string DeptId { get; set; }

        /// <summary>
        /// 商机类型，默认是个人
        /// </summary>
        public string TypeId { get; set; } = "2";

        /// <summary>
        /// 接待时间
        /// </summary>
        public DateTime GoshopDate { get; set; } = BeiJingTime.Today;

        /// <summary>
        /// 需求等级Id
        /// </summary>
        public string DemandId { get; set; }

        /// <summary>
        /// 预算范围
        /// </summary>
        public string Budget { get; set; }

        /// <summary>
        /// 预计成交日期
        /// </summary>
        public DateTime? ExpectDate { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 房屋面积Id
        /// </summary>
        public string AreaId { get; set; }

        /// <summary>
        /// 室Id
        /// </summary>
        public string RoomId { get; set; }

        /// <summary>
        /// 厅Id
        /// </summary>
        public string HallId { get; set; }

        /// <summary>
        /// 卫Id
        /// </summary>
        public string ToiletId { get; set; }

        /// <summary>
        /// 阳台Id
        /// </summary>
        public string BalconyId { get; set; }

        /// <summary>
        /// 空间Id
        /// </summary>
        public string SpaceId { get; set; }

        /// <summary>
        /// 风格Id
        /// </summary>
        public string StyleId { get; set; }

        /// <summary>
        /// 装修进度Id
        /// </summary>
        public string RenovationId { get; set; }

        /// <summary>
        /// 需求类型
        /// </summary>
        public string RequirementTypeId { get; set; } 

        /// <summary>
        /// 产品尺寸
        /// </summary>
        public string ProductSize { get; set; }

        /// <summary>
        /// 是否有意向
        /// </summary>
        public bool IntentionSign { get; set; }

        /// <summary>
        /// 是否愿意到店
        /// </summary>
        public bool WillVisitType { get; set; }

        /// <summary>
        /// 是否到店
        /// </summary>
        public bool VisitType { get; set; }

        /// <summary>
        /// 是否紧急
        /// </summary>
        public bool UrgencyType { get; set; }

        /// <summary>
        /// 是否已交房
        /// </summary>
        public bool DeliveryType { get; set; }

        /// <summary>
        /// 是否添加微信
        /// </summary>
        public bool AddWechatType { get; set; }


        #endregion

        #region 客户相关

        /// <summary>
        /// 客户Id
        /// </summary>
        public string CustomerId { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// 客户手机号
        /// </summary>
        public string CustomerPhone { get; set; }

        /// <summary>
        /// 微信号
        /// </summary>
        public string Wechat { get; set; }

        /// <summary>
        /// 性别Id
        /// </summary>
        public string GenderId { get; set; }

        /// <summary>
        /// 年龄段Id
        /// </summary>
        public string AgeId { get; set; }

        /// <summary>
        /// 楼盘Id
        /// </summary>
        public string HouseId { get; set; }

        /// <summary>
        /// 省Id
        /// </summary>
        public string ProvinceId { get; set; }

        /// <summary>
        /// 市Id
        /// </summary>
        public string CityId { get; set; }

        /// <summary>
        /// 区域Id
        /// </summary>
        public string RegionId { get; set; }

        /// <summary>
        /// 详细地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 客户来源Id
        /// </summary>
        public string CustomerSourceId { get; set; }

        /// <summary>
        /// 渠道Id
        /// </summary>
        public string CustomerChannelId { get; set; }

        #endregion

        /// <summary>
        /// 附件
        /// </summary>
        public List<BaseImageModel> Images { get; set; } = new List<BaseImageModel>();

        /// <summary>
        /// 联系人（公司报备使用）
        /// </summary>
        public string Contact { get; set; }

        /// <summary>
        ///  国家ID
        /// </summary>
        public string CountryId { get; set; }

        /// <summary>
        /// 防撞组别ID
        /// </summary>
        public string SaleCategoryId { get; set; }

        /// <summary>
        /// 意向品类Name
        /// </summary>
        public string SaleCategoryName { get; set; }

        /// <summary>
        /// 意向品类
        /// </summary>
        public string ProductTypeId { get; set; }

        /// <summary>
        /// 意向品类Name
        /// </summary>
        public string ProductTypeName { get; set; }


        /// <summary>
        /// 推荐人
        /// </summary>
        public string Referrer { get; set; }

    }

    public class ProductModel
    {
        public string Goods_id { get; set; }
        public string Fprice { get; set; }
        public string Fattention_num { get; set; }
        public string SuitCombNumber { get; set; }
    }
}