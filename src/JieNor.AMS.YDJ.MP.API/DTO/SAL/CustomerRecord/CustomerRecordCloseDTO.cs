using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 商机关闭接口
    /// </summary>
    [Api("商机关闭接口")]
    [Route("/mpapi/customerrecord/close")]
    [Authenticate]
    public class CustomerRecordCloseDTO : BaseDTO
    {
        public string Id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 原因枚举ID
        /// </summary>
        public string CloseReasonId { get; set; }

        /// <summary>
        /// 原因枚举名称
        /// </summary>
        public string CloseReasonName { get; set; }
    }
}