using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Filter;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 商机编辑取数接口
    /// </summary>
    [Api("商机编辑取数接口")]
    [Route("/mpapi/customerrecord/edit")]
    [Authenticate]
    [ApplyBillLockFilter("ydj_customerrecord")]
    public class CustomerRecordEditDTO : BaseDetailDTO, IApplyBillLockDTO
    {

    }
}