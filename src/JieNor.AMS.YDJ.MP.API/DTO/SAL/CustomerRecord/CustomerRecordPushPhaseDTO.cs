using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 商机推进阶段接口
    /// </summary>
    [Api("商机推进阶段接口")]
    [Route("/mpapi/customerrecord/pushphase")]
    [Authenticate]
    public class CustomerRecordPushPhaseDTO : BaseDTO
    {
        /// <summary>
        /// 唯一标识
        /// </summary>
        public string Id { get; set; }
    }
}