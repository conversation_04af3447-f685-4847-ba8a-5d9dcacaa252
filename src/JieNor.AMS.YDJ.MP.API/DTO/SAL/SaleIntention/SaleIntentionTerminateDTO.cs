using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.SAL.SaleIntention
{
    /// <summary>
    /// 意向单撤销接口
    /// </summary>
    [Api("意向单撤销接口")]
    [Route("/mpapi/saleintention/terminate")]
    [Authenticate]
    public class SaleIntentionTerminateDTO: BaseDetailDTO
    {
        /// <summary>
        /// 撤销原因
        /// </summary>
        public string Reason { get; set; }
    }
}
