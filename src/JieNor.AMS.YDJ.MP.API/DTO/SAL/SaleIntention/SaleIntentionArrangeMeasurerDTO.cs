using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 销售意向指派量尺员接口
    /// </summary>
    [Api("销售意向指派量尺员接口")]
    [Route("/mpapi/saleintention/arrange/measurer")]
    [Authenticate]
    public class SaleIntentionArrangeMeasurerDTO : BaseDetailDTO
    {
        /// <summary>
        /// 量尺员Id
        /// </summary>
        public string MeasurerId { get; set; }
    }
}