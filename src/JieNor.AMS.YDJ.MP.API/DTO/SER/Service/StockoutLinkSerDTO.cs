using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.SER.Service
{
    /// <summary>
    /// 服务单/扫描销售出库单获取该服务单 将该服务单的【服务人员】设置为当前登录员工。
    /// </summary>
    [Api("服务单/扫描销售出库单获取该服务单接口")]
    [Route("/mpapi/service/stockoutlinkser")]
    [Authenticate]
    public class StockoutLinkSerDTO
    {
        /// <summary>
        /// 销售出库单编号
        /// </summary>
        public string SostockoutNo { get; set; }
        /// <summary>
        /// 员工
        /// </summary>
        public string MasteridId { get; set; }

        /// <summary>
        /// 当前组织
        /// </summary>
        public string Mainorgid { get; set; }

    }
}
