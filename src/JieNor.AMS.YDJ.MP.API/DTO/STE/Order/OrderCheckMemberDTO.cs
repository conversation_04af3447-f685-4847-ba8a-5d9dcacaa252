using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.STE.Order
{
    /// <summary>
    /// 合同会员ID 相关验证
    /// </summary>
    [Api("合同优惠券发券列表取数接口")]
    [Route("/mpapi/order/ordercheckmember")]
    [Authenticate]
    public class OrderCheckMemberDTO
    {
        //验证方式：0 字段改换 ， 1 单据提交
        public string type { get; set; } = "0";
        public string deptId { get; set; }
        public string customerId { get; set; }

        public string billName { get; set; }
    }

    public class MemberMdl
    {
        public bool Res { get; set; } = false;
        public string memberId { get; set; }
    }
}
