using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Filter;

namespace JieNor.AMS.YDJ.MP.API.DTO.STE.Order
{
    /// <summary>
    /// 合同单提交变更接口
    /// </summary>
    [Api("合同单提交变更接口")]
    [Route("/mpapi/order/Submitchange")]
    [Authenticate]
    [CheckBillLockFilter("ydj_order", "submitchange")]
    public class OrderUnSubmitchangeDTO : CheckBillLockDTO
    {
        /// <summary>
        /// 变更原因
        /// </summary>
        public string changeReason { get; set; }
    }
}
