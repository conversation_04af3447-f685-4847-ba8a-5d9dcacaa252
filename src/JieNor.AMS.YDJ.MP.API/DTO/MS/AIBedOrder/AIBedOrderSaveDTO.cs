using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using ServiceStack;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 床垫选配单保存接口
    /// </summary>
    [Api(" 床垫选配单保存接口")]
    [Route("/mpapi/aibedorder/save")]
    [Authenticate]
    public class AIBedOrderSaveDTO : BaseDTO
    {
        public JObject BillData { get; set; } = new JObject();
    }
}
