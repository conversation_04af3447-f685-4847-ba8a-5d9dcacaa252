using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 床垫选配单详情接口
    /// </summary>
    [Api("床垫选配单详情接口")]
    [Route("/mpapi/aibedorder/detail")]
    [Authenticate]
    public class AIBedOrderDetailDTO : BaseDTO
    {
        public string Id { get; set; }
    }
}