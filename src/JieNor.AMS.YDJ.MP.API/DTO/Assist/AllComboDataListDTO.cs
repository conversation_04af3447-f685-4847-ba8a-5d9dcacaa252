using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 所有下拉框（辅助资料、简单枚举下拉框、单据类型）列表取数接口
    /// </summary>
    [Api("所有下拉框（辅助资料、简单枚举下拉框、单据类型）列表取数接口")]
    [Route("/mpapi/allcombo/list")]
    [Authenticate]
    public class AllComboDataListDTO : BillTypeListDTO
    {
        /// <summary>
        /// 字段标识，多个标识之间用“,”逗号分隔
        /// </summary>
        public string FieldKey { get; set; }
    }
}