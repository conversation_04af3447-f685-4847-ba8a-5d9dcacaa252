using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    public class ServiceItemListModel
    {
        /// <summary>
        /// 服务项目
        /// </summary>
        public BaseDataSimpleModel SerItem { get; set; }
        /// <summary>
        /// 服务类型
        /// </summary>
        public ComboDataModel SerItemType { get; set; }
        /// <summary>
        /// 计价单位
        /// </summary>
        public ComboDataModel Unit { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// 是否默认带出
        /// </summary>
        public bool Bring { get; set; }
    }
}
