using System.Collections.Generic;
using JieNor.AMS.YDJ.MP.API.DTO.MS.AIProduct;

namespace JieNor.AMS.YDJ.MP.API.Model.MS.AIProduct
{
    public class AiProductsModel
    {
        public List<AiProductModel> AiProducts { set; get; }

        /*/// <summary>
        /// 根据第一个问题回答的答案来选择推荐的床垫编号
        /// </summary>
        public string RecommendBed;*/

        /*/// <summary>
        /// 根据第一个问题回答的答案来选择推荐的床垫名字
        /// </summary>
        public string RecommendBedName;*/



        public AiProductsModel()
        {
            AiProducts = new List<AiProductModel>();
        }
    }
}