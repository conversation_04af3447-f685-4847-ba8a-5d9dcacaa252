using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 基础资料数据模型基类
    /// </summary>
    public class BaseDataModel : BaseModel
    {
        /// <summary>
        /// 编码
        /// </summary>
        public string Number { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public string Name { get; set; }

        public override string ToString()
        {
            return $"{this.Id} {this.Number} {this.Name}";
        }

        public BaseDataModel()
        {

        }

        public BaseDataModel(DynamicObject data)
        {
            Id = JNConvert.ToStringAndTrim(data?["id"]);
            Name = JNConvert.ToStringAndTrim(data?["fname"]);
            Number = JNConvert.ToStringAndTrim(data?["fnumber"]);
        }
    }
}