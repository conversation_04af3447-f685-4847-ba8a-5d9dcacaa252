using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using System.Runtime.Serialization;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 客户收货地址编辑数据模型
    /// </summary>
    public class CustomerAddressEditModel
    {
        public string Id { get; set; }

        /// <summary>
        /// 客户手机号
        /// </summary>
        public string Phone { get; set; }

        public string Contact { get; set; }

        /// <summary>
        /// 所在区域 - 省份
        /// </summary>
        public ComboDataModel Province { get; set; } = new ComboDataModel();

        /// <summary>
        /// 所在区域 - 城市
        /// </summary>
        public ComboDataModel City { get; set; } = new ComboDataModel();

        /// <summary>
        /// 所在区域 - 区县
        /// </summary>
        public ComboDataModel Region { get; set; } = new ComboDataModel();

        /// <summary>
        /// 详细地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 完整地址
        /// </summary>
        public string District { get; set; }

        /// <summary>
        /// 默认地址
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// 客户id
        /// </summary>
        public string CustomerId { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Description { get; set; }
    }
}