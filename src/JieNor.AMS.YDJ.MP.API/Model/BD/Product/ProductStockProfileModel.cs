using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 商品库存属性数据模型
    /// </summary>
    public class ProductStockProfileModel
    {
        [IgnoreDataMember]
        public string ProductId { get; set; }

        [IgnoreDataMember]
        public string ClientId { get; set; }

        /// <summary>
        /// 即时库存量
        /// </summary>
        public string RealStockQty { get; set; } = "0";

        /// <summary>
        /// 在途量
        /// </summary>
        public string IntransitQty { get; set; }

        /// <summary>
        /// 预留量
        /// </summary>
        public string ReserveQty { get; set; }

        /// <summary>
        /// 可用库存量
        /// </summary>
        public string UsableStockQty { get; set; } = "0";

        /// <summary>
        /// 清库存
        /// </summary>
        public bool IsClearStock { get; set; }
    }
}