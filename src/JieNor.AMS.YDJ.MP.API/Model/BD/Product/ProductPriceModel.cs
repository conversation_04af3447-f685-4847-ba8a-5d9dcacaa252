using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 商品价目数据模型
    /// </summary>
    public class ProductPriceModel
    {
        public string ProductId { get; set; }

        /// <summary>
        /// 销售价
        /// </summary>
        public decimal SalPrice { get; set; }

        /// <summary>
        /// 销售价是否来源于经销商自己的价目表
        /// </summary>
        public bool FromAgent { get; set; }

        /// <summary>
        /// 销售价是否忽略选配计价公式
        /// </summary>
        public bool IgnorePriceFormula { get; set; }

        /// <summary>
        /// 品牌指导价
        /// </summary>
        public decimal GuidePrice { get; set; }

        /// <summary>
        /// 经销价
        /// </summary>
        public decimal SellPrice { get; set; }
        /// <summary>
        /// 商品促销id
        /// </summary>
        public string fpromotionid { get; set; }

        /// <summary>
        /// 促销规则
        /// </summary>
        public string fpromotionrule { get; set; }

        /// <summary>
        /// 特价销售价
        /// </summary>
        public decimal fpromotionsalprice { get; set; }

        /// <summary>
        /// 特价最低价
        /// </summary>
        public decimal fpromotionlowestprice { get; set; }

        public string PriceMsg { get; set; }
    }
}
