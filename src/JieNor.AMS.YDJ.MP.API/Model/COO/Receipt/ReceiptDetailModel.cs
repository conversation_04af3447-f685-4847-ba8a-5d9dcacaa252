using JieNor.AMS.YDJ.MP.API.Model.BAS.CollectingUnit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model.COO.Receipt
{
    public class ReceiptDetailModel
    {
        /// <summary>
        /// 数据ID
        /// </summary>
        public string Id { get; set; }

        //焕新订单标记
        public bool RenewalFlag { get; set; }

        /// <summary>
        /// 收款单号
        /// </summary>
        public string RecBillNo { get; set; }
        /// <summary>
        /// 收款类型(1.线下收款-默认 2.在线收款)
        /// </summary>
        public string WayType { get; set; }
        /// <summary>
        /// 收款金额(实收)
        /// </summary>
        public decimal DealAmount { get; set; }
        /// <summary>
        /// 收款人（发起收款的员工）
        /// </summary>
        public string Staff { get; set; }
        /// <summary>
        /// 支付日期、结算日期
        /// </summary>
        public DateTime Fdate { get; set; }
        /// <summary>
        /// 收款备注
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// 结算方式 直接返回收款类型名称。 银行转账、支付宝 、微信 等，具体名称视客户在麦浩系统的配置情况
        /// </summary>
        public string Way { get; set; }
        /// <summary>
        /// 收款账号
        /// </summary>
        public string AccountNumber { get; set; }
        /// <summary>
        /// 账户名称
        /// </summary>
        public string AccountName { get; set; }
        /// <summary>
        /// 收款凭证
        /// </summary>
        public List<string> ReceiptImages { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; }
        /// <summary>
        /// 客户联系电话
        /// </summary>
        public string CustomerPhone { get; set; }
        /// <summary>
        /// 客户地址
        /// </summary>
        public string CustomerAddress { get; set; }
        /// <summary>
        /// 合同、意向单号
        /// </summary>
        public string BillNo { get; set; }
        /// <summary>
        /// 合同、意向金额
        /// </summary>
        public decimal Amount { get; set; }
        /// <summary>
        /// 合同、意向签单日期
        /// </summary>
        public DateTime BillDate { get; set; }
        /// <summary>
        /// 创建时间（在线付款单的）暂时不用
        /// </summary>
        public DateTime CreateDate { get; set; }
        /// <summary>
        /// 付款时间（在线付款单的）暂时不用
        /// </summary>
        public DateTime PayTime { get; set; }
        /// <summary>
        /// 客户Id
        /// </summary>
        public string CustomerId { get; set; }
        /// <summary>
        /// 交易单号  （在线付款单的）暂时不用
        /// </summary>
        public string TransactionNumber { get; set; }
        /// <summary>
        /// 付款单状态 （业务状态fbizstatus）1待收款 2待确认 3已确认 
        /// </summary>
        public int RecStatus { get; set; }
        /// <summary>
        /// 合同、意向单id
        /// </summary>
        public string BillId { get; set; }
        /// <summary>
        /// 负责人
        /// </summary>
        public string Director { get; set; }

        /// <summary>
        ///  单据类型 0.其他 1.意向单 2.合同
        /// </summary>
        public int BillType { get; set; }

        /// <summary>
        /// 数据状态
        /// </summary>
        public ComboDataModel Status { get; set; } = new ComboDataModel();

        /// <summary>
        ///  数据审核状态 
        /// </summary>
        public bool AuditStatus { get; set; }

        /// <summary>
        /// 用途
        /// </summary>
        public string Purpose { get; set; }
        /// <summary>
        /// 业务方向
        /// </summary>
        public string Bizdirection { get; set; }
        /// <summary>
        /// 费用项目
        /// </summary>
        public List<ReceiptExpenseModel> ExpenseList { get; set; } = new List<ReceiptExpenseModel>();
        /// <summary>
        /// 已扣佣金
        /// </summary>
        public decimal ReducedBrokerage { get; set; }
        /// <summary>
        /// 代收单位
        /// </summary>
        public List<CollectionUnitModel> CollectionUnit { get; set; } = new List<CollectionUnitModel>();
        /// <summary>
        /// 货款金额
        /// </summary>
        public decimal LoanAmount { get; set; }

        /// <summary>
        /// 款项说明
        /// </summary>
        public PaymentData PaymentDesc { get; set; }
        /// <summary>
        /// 销售员
        /// </summary>
        public string Seller { get; set; }
        /// <summary>
        /// 销售部门
        /// </summary>
        public string SaleDepartment { get; set; }

        /// <summary>
        /// 数据来源
        /// </summary>
        public string Dataorigin { get; set; }

        /// <summary>
        /// 收款小票号
        /// </summary>
        public string ReceiptNo { get; set; }
        public string cusacount { get; set; }
        /// <summary>
        /// 提交总部时间
        /// </summary>
        public DateTime Submitsaptime { get; set; }
        /// <summary>
        /// 协同总部状态
        /// </summary>
        public ComboDataModel Sapstatus { get; set; } 
        /// <summary>
        /// 更新状态时间
        /// </summary>
        public DateTime Updatestatustime { get; set; }

        /// <summary>
        /// SAP过账凭证号
        /// </summary>
        public string Sappostno { get; set; }
        /// <summary>
        /// SAP过账时间
        /// </summary>
        public DateTime Sapposttime { get; set; }
        /// <summary>
        /// 总部同步消息
        /// </summary>
        public string Sapsyncmessage { get; set; } 
        /// <summary>
        /// 退款业务类型
        /// </summary>
        public string RefundbusinessName { get; set; }
        /// <summary>
        /// 关联合同号
        /// </summary>
        public string Linkorderno { get; set; }
        /// <summary>
        /// 总部审批人
        /// </summary>
        public string Sharedapprover { get; set; }
        /// <summary>
        /// 共享单号
        /// </summary>
        public string Sharedtrackno { get; set; }
        /// <summary>
        /// 退款银行回单号
        /// </summary>
        public string Refundreceiptno { get; set; }
        /// <summary>
        ///共享退款时间
        /// </summary>
        public DateTime Sharedrefundtime { get; set; }
        /// <summary>
        /// SAP借贷项单号
        /// </summary>
        public string Rreditnotenumber { get; set; }

        /// <summary>
        /// 联合开单
        /// </summary>
        public List<OrderJoinStaffModel> JoinStaffs { get; set; } = new List<OrderJoinStaffModel>();
    }

    public class PaymentData
    {
        public PaymentData() { }

        public PaymentData(string Id, string Name)
        {
            this.Id = Id;
            this.Name = Name;
        }

        public string Id { get; set; }
        public string Name { get; set; }
    }

    /// <summary>
    /// 收款费用项目数据模型
    /// </summary>
    public class ReceiptExpenseModel
    {
        /// <summary>
        /// 费用项目
        /// </summary>
        public BaseDataSimpleModel ExpenseItem { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Description { get; set; }
    }
}
