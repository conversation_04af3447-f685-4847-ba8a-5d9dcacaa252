using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model.STE.Channel
{
    public class ChannelDetailDataModel:BaseModel
    {
        /// <summary>
        /// 报备商机数
        /// </summary>
        public int RepCustomerrecordCount { get; set; }
        /// <summary>
        /// 跟进中的商机数
        /// </summary>
        public int FollowUpCustomerrecordCount { get; set; }
        /// <summary>
        /// 已成交的商机数
        /// </summary>
        public int DealCustomerrecordCount { get; set; }
        /// <summary>
        /// 已成交合同
        /// </summary>
        public int DealOrder { get; set; }
        /// <summary>
        /// 成交金额
        /// </summary>
        public decimal DealAmount { get; set; }
        /// <summary>
        /// 客单价
        /// </summary>
        public decimal PerTicketSales { get; set; }
    }
}
