using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    public class AfterFeedBackListModel : BaseDataModel
    {
        //客户头像
        public string CusImg { get; set; }
        public ComboDataModel Customer { get; set; }
        /// <summary>
        /// 联系人
        /// </summary>
        public string LinkStaff { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string LinkMobile { get; set; }
        /// <summary>
        /// 省
        /// </summary>
        public ComboDataModel province { get; set; }
        /// <summary>
        /// 市
        /// </summary>
        public ComboDataModel city { get; set; }
        /// <summary>
        /// 区
        /// </summary>
        public ComboDataModel fregion { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        /// 
        public string Address { get; set; }
        /// <summary>
        /// 反馈时间
        /// </summary>
        public string Date { get; set; }
        /// <summary>
        /// 问题类别
        /// </summary>
        public ComboDataModel QuestionType { get; set; }
        /// <summary>
        /// 售后人员
        /// </summary>
        public ComboDataModel Staff { get; set; }
        /// <summary>
        /// 转总部时间
        /// </summary>
        public string TransferDate { get; set; }
        /// <summary>
        /// 总部审核时间
        /// </summary>
        public string HqAuditDate { get; set; }
        /// <summary>
        /// 完成时间
        /// </summary>
        public string FinishDate { get; set; }
        /// <summary>
        /// 关闭时间
        /// </summary>
        public string CloseDate { get; set; }
        /// <summary>
        /// 售后状态
        /// </summary>
        public ComboDataModel FeedStatus { get; set; }
        /// <summary>
        /// 是否总部，false：总部下发，true 自建
        /// </summary>
        public bool isUserCreate { get; set; }


        /// <summary>
        /// 问题商品
        /// </summary>
        public List<BaseDataSimpleModel> QuestionProduct { get; set; }

        /// <summary>
        /// 总部售后工程师
        /// </summary>
        public ComboDataModel Engineer { get; set; }

        /// <summary>
        /// 内部处理结论
        /// </summary>
        public ComboDataModel HandleconClusion { get; set; }

        /// <summary>
        /// 总部处理结论
        /// </summary>
        public ComboDataModel HqHandleConclusion { get; set; }
        
    }
}
