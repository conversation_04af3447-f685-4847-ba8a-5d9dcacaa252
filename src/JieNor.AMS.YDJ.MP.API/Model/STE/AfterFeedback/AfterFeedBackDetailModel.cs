using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    public class AfterFeedBackDetailModel : BaseDataModel
    {
        //客户头像
        public string CusImg { get; set; }
        /// <summary>
        /// 客户
        /// </summary>
        public CustomerSimpleModel Customer { get; set; }
        /// <summary>
        /// 联系人
        /// </summary>
        public string LinkStaff { get; set; }
        /// <summary>
        /// 联系人电话
        /// </summary>
        public string LinkMobile { get; set; }
        /// <summary>
        /// 省
        /// </summary>
        public ComboDataModel Province { get; set; } = new ComboDataModel();

        /// <summary>
        /// 市
        /// </summary>
        public ComboDataModel City { get; set; } = new ComboDataModel();

        /// <summary>
        /// 区域
        /// </summary>
        public ComboDataModel Region { get; set; } = new ComboDataModel();
        /// <summary>
        /// 地址
        /// </summary>
        /// 
        public string Address { get; set; }
        /// <summary>
        /// 反馈时间
        /// </summary>
        public string Date { get; set; }
        /// <summary>
        /// 售后状态
        /// </summary>
        public ComboDataModel FeedStatus { get; set; }
        /// <summary>
        /// 粗略基本信息
        /// </summary>
        public AfterFeedBackInfo BaseInfo { get; set; }
        ///// <summary>
        ///// 问题商品
        ///// </summary>
        //public BaseDataSimpleModel Product { get; set; }
        /// <summary>
        /// 问题类别
        /// </summary>
        public ComboDataModel QuestionType { get; set; }
        /// <summary>
        /// 问题描述
        /// </summary>
        public string QuestionDesc { get; set; }
        /// <summary>
        /// 问题图片
        /// </summary>
        public List<BaseImageModel> QuestionImgs { get; set; }

        /// <summary>
        /// 总部图片(url集合)
        /// </summary>
        public List<string> ZTImages { get; set; } = new List<string>();

        /// <summary>
        /// 责任单位
        /// </summary>
        public ComboDataModel WorkInfo { get; set; }
        /// <summary>
        /// 责任单位类型
        /// </summary>
        public ComboDataModel WorkType { get; set; }
        /// <summary>
        /// 内部处理结论
        /// </summary>
        public ComboDataModel Conclusion { get; set; }
        /// <summary>
        /// 内部处理方式
        /// </summary>
        public string Scheme { get; set; }
        /// <summary>
        /// 是否转总部
        /// </summary>
        public bool IsTransfer { get; set; }
        /// <summary>
        /// 总部处理结论
        /// </summary>
        public ComboDataModel HqConclusion { get; set; }
        /// <summary>
        /// 总部处理方式
        /// </summary>
        public string HqScheme { get; set; }
        /// <summary>
        /// 返厂申请
        /// </summary>
        public FeedBackReturnInfo ReturnApplication { get; set; }
        /// <summary>
        /// 商品信息
        /// </summary>
        public List<AftProductEditModel> Products { get; set; }
    }
    /// <summary>
    /// 售后服务单粗略基本信息
    /// </summary>
    public class AfterFeedBackInfo
    {
        #region 粗略基本信息
        /// <summary>
        /// 招商经销商
        /// </summary>
        public BaseDataSimpleModel Agent { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 反馈时间
        /// </summary>
        public string Date { get; set; }
        /// <summary>
        /// 售后人员
        /// </summary>
        public ComboDataModel Staff { get; set; }
        /// <summary>
        /// 售后人员电话
        /// </summary>
        public string StaffPhone { get; set; }
        /// <summary>
        /// 售后部门
        /// </summary>
        public ComboDataModel Dept { get; set; }
        /// <summary>
        /// 门店
        /// </summary>
        public ComboDataModel Store { get; set; }
        /// <summary>
        /// 合同信息
        /// </summary>
        public ComboDataModel Order { get; set; }
        /// <summary>
        /// 客户
        /// </summary>
        public CustomerSimpleModel Customer { get; set; }
        /// <summary>
        /// 省
        /// </summary>
        public ComboDataModel Province { get; set; } = new ComboDataModel();

        /// <summary>
        /// 市
        /// </summary>
        public ComboDataModel City { get; set; } = new ComboDataModel();

        /// <summary>
        /// 区域
        /// </summary>
        public ComboDataModel Region { get; set; } = new ComboDataModel();
        /// <summary>
        /// 联系人
        /// </summary>
        public string LinkStaff { get; set; }
        /// <summary>
        /// 联系人电话
        /// </summary>
        public string LinkMobile { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }
        ///// <summary>
        ///// 售后状态
        ///// </summary>
        //public ComboDataModel FeedStatus { get; set; }
        /// <summary>
        /// 源单类型
        /// </summary>
        public ComboDataModel SourceType { get; set; }
        /// <summary>
        /// 源单编号
        /// </summary>
        public string SourceNumber { get; set; }

        /// <summary>
        /// 售后工程师
        /// </summary>
        public ComboDataModel Engineer { get; set; }
        /// <summary>
        /// 商品品类
        /// </summary>
        public ComboDataModel Category { get; set; }

        /// <summary>
        /// 受理时间
        /// </summary>
        public string AcceptDate { get; set; }
        /// <summary>
        /// 转总部时间
        /// </summary>
        public string TransferDate { get; set; }
        /// <summary>
        /// 总部审核时间
        /// </summary>
        public string HqAuditDate { get; set; }
        /// <summary>
        /// 完成时间
        /// </summary>
        public string FinishDate { get; set; }
        /// <summary>
        /// 关闭时间
        /// </summary>
        public string CloseDate { get; set; }
        /// <summary>
        /// 送达方
        /// </summary>
        public ComboDataModel Deliver { get; set; }
        #endregion
    }
    /// <summary>
    /// 售后服务单返厂申请
    /// </summary>
    public class FeedBackReturnInfo
    {
        /// <summary>
        /// 是否返厂
        /// </summary>
        public bool IsReturn { get; set; }
        /// <summary>
        /// 返厂日期
        /// </summary>
        public string ReturnDate { get; set; }
        /// <summary>
        /// 授权城市
        /// </summary>
        public BaseDataSimpleModel AuthCity { get; set; } = new BaseDataSimpleModel();
        ///// <summary>
        ///// 城市
        ///// </summary>
        //public string City { get; set; }
        /// <summary>
        /// 工程师电话
        /// </summary>
        public string EngineerTel { get; set; }
        ///// <summary>
        ///// 产品型号
        ///// </summary>
        //public string SeltypeName { get; set; }
        ///// <summary>
        ///// 尺寸规格
        ///// </summary>
        //public string Specifica { get; set; }
        ///// <summary>
        ///// 商品数量
        ///// </summary>
        //public string ProCount { get; set; }
        /// <summary>
        /// 返厂售后类型
        /// </summary>
        public ComboDataModel FeedType { get; set; }
        /// <summary>
        /// 产品返厂原因
        /// </summary>
        public string ReturnReson { get; set; }
        /// <summary>
        /// 退货地址
        /// </summary>
        public string EngineerAddress { get; set; }
        /// <summary>
        /// 详细退货地址
        /// </summary>
        public string CusreturnAdd { get; set; }
        /// <summary>
        /// SAP订单号
        /// </summary>
        public string SapNumber { get; set; }
        /// <summary>
        /// 到货日期
        /// </summary>
        public string ArrivalDate { get; set; }
        ///// <summary>
        ///// 总部审核日期--基本信息已给
        ///// </summary>
        //public string HqauditDate { get; set; }
        /// <summary>
        /// 送客户家日期
        /// </summary>
        public string ServiceDate { get; set; }
        /// <summary>
        /// 客户使用时长
        /// </summary>
        public string CususeTime { get; set; }
        /// <summary>
        /// 返厂物流单号
        /// </summary>
        public string ReturnlogNum { get; set; }
    }
    /// <summary>
    /// 商品信息
    /// </summary>
    public class AftProductModel
    {
        /// <summary>
        /// 商品图片
        /// </summary>
        public BaseImageModel Img { get; set; }
        /// <summary>
        /// Id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 商品id
        /// </summary>
        public BaseDataSimpleModel Product { get; set; } = new BaseDataSimpleModel();
        /// <summary>
        /// 商品分类
        /// </summary>
        public BaseDataModel ProductCategory { get; set; } = new BaseDataModel();
        /// <summary>
        /// 辅助属性fname
        /// </summary>
        public string AuxPropValName { get; set; }
        ///// <summary>
        ///// 辅助属性组合值ID
        ///// </summary>
        //public string AuxPropValId { get; set; }
        ///// <summary>
        ///// 辅助属性
        ///// </summary>
        //public List<Dictionary<string, string>> AuxPropVals { get; set; }
        /// <summary>
        /// 品牌
        /// </summary>
        public ComboDataModel Brand { get; set; } = new ComboDataModel();

        /// <summary>
        /// 系列
        /// </summary>
        public ComboDataModel Series { get; set; } = new ComboDataModel();
        /// <summary>
        /// 定制说明
        /// </summary>
        public string CustomDesc { get; set; }
        /// <summary>
        /// 基本单位
        /// </summary>
        public BaseDataSimpleModel Unit { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 基本单位数量
        /// </summary>
        public int Qty { get; set; }
        /// <summary>
        /// 成交金额
        /// </summary>
        public decimal Amount { get; set; }
        public AftProductModel(DynamicObject data)
        {
            var materialObj = data["fmaterialid_ref"] as DynamicObject;
            var brandObj = materialObj?["fbrandid_ref"] as DynamicObject;   // 品牌
            var seriesObj = materialObj?["fseriesid_ref"] as DynamicObject; // 系列
            var categoryObj = materialObj?["fcategoryid_ref"] as DynamicObject; // 商品分类

            Id = JNConvert.ToStringAndTrim(data["id"]);
            Product = new BaseDataSimpleModel(data["fmaterialid_ref"] as DynamicObject);
            ProductCategory = new BaseDataModel(categoryObj);
            //AuxPropVals = ProductUtil.PackAuxPropFieldValue(setNumber);
            AuxPropValName = JNConvert.ToStringAndTrim(data["fattrinfo"]);
            CustomDesc = JNConvert.ToStringAndTrim(data["fcustomdesc"]);
            Unit = new BaseDataSimpleModel(data["funitid_ref"] as DynamicObject);
            ProductCategory = new BaseDataModel(categoryObj);
            Qty = Convert.ToInt32(data["fqty"]);
            Brand = new ComboDataModel
            {
                Id = Convert.ToString(brandObj?["id"]),
                Name = Convert.ToString(brandObj?["fname"])
            };

            Series = new ComboDataModel
            {
                Id = Convert.ToString(seriesObj?["id"]),
                Name = Convert.ToString(seriesObj?["fname"])
            };
            Amount = Convert.ToDecimal(data["famount"]);
        }
    }
}
