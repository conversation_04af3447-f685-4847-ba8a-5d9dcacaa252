using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using System.Runtime.Serialization;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 跟进记录列表数据模型
    /// </summary>
    public class FollowerRecordListModel : BaseDataModel
    {
        /// <summary>
        /// 数据状态：该属性不需要被序列化到前端
        /// </summary>
        [IgnoreDataMember]
        public new ComboDataModel Status { get; set; }

        [IgnoreDataMember]
        public new string Name { get; set; }
        
        [IgnoreDataMember]
        public new DateTime CreateDate { get; set; }

        /// <summary>
        /// 跟进时间
        /// </summary>
        public DateTime FollowTime { get; set; }

        /// <summary>
        /// 跟进人
        /// </summary>
        public FollowerModel Follower { get; set; }

        /// <summary>
        /// 跟进详情
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 跟进记录对象类型Id
        /// </summary>
        public string ObjectTypeId { get; set; }

        /// <summary>
        /// 跟进记录对象类型
        /// </summary>
        public string ObjectType { get; set; }

        /// <summary>
        /// 跟进记录对象Id
        /// </summary>
        public string ObjectId { get; set; }

        /// <summary>
        /// 跟进记录对象单号
        /// </summary>
        public string ObjectNo { get; set; }

        /// <summary>
        /// 附件Id数组字符串，以英文逗号隔开
        /// </summary>
        public string AttachmentIds { get; set; }

        /// <summary>
        /// 附件名称数组字符串，以英文逗号隔开
        /// </summary>
        public string AttachmentNames { get; set; }

        /// <summary>
        /// 附件Url数组字符串，以英文逗号隔开
        /// </summary>
        public string AttachmentUrls { get; set; }
    }

    public class FollowerModel 
    {
        public string Id { get; set; }

        public string Name { get; set; }

        public string Phone { get; set; }

        public string Position { get; set; }

        public string ImageId { get; set; }


        public string ImageUrl { get; set; }
    }
}