using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.MP.API.Controller
{
    /// <summary>
    /// 微信小程序：小程序-统计-库存模块统计
    /// </summary>
    public class ReportStockRankController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ReportStockRankDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>
            {
                Data = new object()
            };
            if (dto.ReportType.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "请补全数据类型参数！";
                resp.Success = false;
                return resp;
            }
            DynamicObjectCollection result;
            string sqlStr = string.Empty;
            List<SqlParam> sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("@topcompanyid", System.Data.DbType.String, this.Context.TopCompanyId)
            };
            List<object> data = new List<object>();
            switch (dto.ReportType.ToLower())
            {
                // StockInfo：库存概览
                case "stockinfo":
                    sqlStr = @"/*dialect*/select sum(t1.fstockqty) StockQty, sum(t1.fstockreserveqty) ReserveQty
                                        ,sum(t1.fstockqty*t1.fpurfacprice/10000) StockCostPrice,sum(t1.fstockqty*t1.funifysaleprice/10000) StockSalePrice 
                                        ,sum(t1.fstockreserveqty*t1.fpurfacprice/10000) ReserveCostPrice,sum(t1.fstockreserveqty*t1.funifysaleprice/10000) ReserveSalePrice 
                                        from v_stk_inventorylist t1 with(nolock)
                                        where t1.fqty>0 {0}";
                    AppendWhere(sqlParam, ref sqlStr, dto);
                    result = this.DBService.ExecuteDynamicObject(Context, sqlStr, sqlParam);
                    if (!result.IsNullOrEmpty() && result.Any())
                    {
                        resp.Data = new
                        {
                            StockQty = Convert.ToDecimal(result.FirstOrDefault()?["StockQty"]),//库存量
                            ReserveQty = Convert.ToDecimal(result.FirstOrDefault()?["ReserveQty"]),//预留量
                            ReserveCostPrice = Convert.ToDecimal(result.FirstOrDefault()?["ReserveCostPrice"]),//预留成本总额/万
                            ReserveSalePrice = Convert.ToDecimal(result.FirstOrDefault()?["ReserveSalePrice"]),//预留销售总额/万
                            StockCostPrice = Convert.ToDecimal(result.FirstOrDefault()?["StockCostPrice"]),//库存成本总额/万
                            StockSalePrice = Convert.ToDecimal(result.FirstOrDefault()?["StockSalePrice"])//库存销售总额/万
                        };
                    }
                    break;
                // StockTypeTop10：在库库存品类TOP10
                case "stocktypetop10":
                    sqlStr = @"/*dialect*/select t3.Fid,isnull(t3.fname,'未定义') Fname,sum(t1.fstockqty) StockQty
                                            from v_stk_inventorylist t1 with(nolock)
                                            left join t_bd_material t2 with(nolock) on t1.fmaterialid=t2.fid
                                            left join ser_ydj_category t3 with(nolock) on t2.fcategoryid=t3.fid
                                            where t1.fqty>0 {0} 
                                            group by t3.fid,t3.fname order by sum(t1.fstockqty) desc";
                    AppendWhere(sqlParam, ref sqlStr, dto);
                    result = this.DBService.ExecuteDynamicObject(Context, sqlStr, sqlParam);
                    resp.Data = DealCategory(result, dto.ReportType.ToLower());
                    break;
                // StockAmountZB：库存品类金额占比
                case "stockamountzb":
                    sqlStr = @"/*dialect*/select t3.Fid,isnull(t3.fname,'未定义') Fname,sum(t1.fstockqty*t1.fpurfacprice/10000) FAmount
                                            from v_stk_inventorylist t1 with(nolock)
                                            left join t_bd_material t2 with(nolock) on t1.fmaterialid=t2.fid
                                            left join ser_ydj_category t3 with(nolock) on t2.fcategoryid=t3.fid
                                            where t1.fqty>0 {0} 
                                            group by t3.fid,t3.fname order by sum(t1.fstockqty*t1.fpurfacprice/10000) desc";
                    AppendWhere(sqlParam, ref sqlStr, dto);
                    result = this.DBService.ExecuteDynamicObject(Context, sqlStr, sqlParam);
                    var newData = DealCategory(result, dto.ReportType.ToLower());
                    if (!newData.IsNullOrEmpty() && newData.Any())
                    {
                        var sumAmount = newData.Select(x => x.Amount).Sum();
                        foreach (var item in newData)
                        {
                            data.Add(new
                            {
                                CategoryId = item.CategoryId,//品类Id
                                Name = item.Name,//品类名称
                                Amount = item.Amount,//金额
                                Percent = sumAmount == 0 ? 0 + "%" : Math.Round(item.Amount / sumAmount * 100, 2) + "%"//百分比
                            });
                        }
                    }
                    resp.Data = data;
                    break;
                // StockSeriesZB：库存系列金额占比
                case "stockserieszb":
                    sqlStr = @"/*dialect*/select top 10 t3.Fid,isnull(t3.fname,'未定义') Fname,sum(t1.fstockqty*t1.fpurfacprice/10000) FAmount
                                            from v_stk_inventorylist t1 with(nolock)
                                            left join t_bd_material t2 with(nolock) on t1.fmaterialid=t2.fid
                                            left join t_ydj_series t3 with(nolock) on t2.fseriesid=t3.fid
                                            where t1.fqty>0 {0} 
                                            group by t3.fid,t3.fname order by sum(t1.fstockqty*t1.fpurfacprice/10000) desc";
                    AppendWhere(sqlParam, ref sqlStr, dto);
                    result = this.DBService.ExecuteDynamicObject(Context, sqlStr, sqlParam);
                    if (!result.IsNullOrEmpty() && result.Any())
                    {
                        var sumAmount = result.Select(x => Convert.ToDecimal(x["FAmount"])).Sum();
                        foreach (var item in result)
                        {
                            data.Add(new
                            {
                                SeriesId = Convert.ToString(item["Fid"]),//系列Id
                                Name = Convert.ToString(item["Fname"]),//系列名称
                                Amount = Convert.ToDecimal(item["FAmount"]),//金额
                                Percent = sumAmount == 0 ? 0 + "%" : Math.Round(Convert.ToDecimal(item["FAmount"]) / sumAmount * 100, 2) + "%"//百分比
                            });
                        }
                    }
                    resp.Data = data;
                    break;
                // SelTypeAmountTop10：下钻型号金额top10
                case "seltypeamounttop10":
                    sqlStr = @"/*dialect*/select top 10 isnull(t3.fname,'未定义') Fname,sum(t1.fstockqty*t1.fpurfacprice/10000) FAmount
                                            from v_stk_inventorylist t1 with(nolock)
                                            left join t_bd_material t2 with(nolock) on t1.fmaterialid=t2.fid
                                            left join t_sel_type t3 with(nolock) on t2.fseltypeid=t3.fid
                                            where t1.fqty>0 {0} 
                                            group by t3.fname order by sum(t1.fstockqty*t1.fpurfacprice/10000) desc";
                    AppendWhere(sqlParam, ref sqlStr, dto);
                    result = this.DBService.ExecuteDynamicObject(Context, sqlStr, sqlParam);
                    if (!result.IsNullOrEmpty() && result.Any())
                    {
                        foreach (var item in result)
                        {
                            data.Add(new
                            {
                                Name = Convert.ToString(item["Fname"]),//型号名称
                                Amount = Convert.ToDecimal(item["FAmount"])//金额
                            });
                        }
                    }
                    resp.Data = data;
                    break;
                // SelTypeCountTop10：下钻型号数量top10
                case "seltypecounttop10":
                    sqlStr = @"/*dialect*/select top 10 isnull(t3.fname,'未定义') Fname,sum(t1.fstockqty) StockQty
                                            from v_stk_inventorylist t1 with(nolock)
                                            left join t_bd_material t2 with(nolock) on t1.fmaterialid=t2.fid
                                            left join t_sel_type t3 with(nolock) on t2.fseltypeid=t3.fid
                                            where t1.fqty>0 {0} 
                                            group by t3.fname order by sum(t1.fstockqty) desc";
                    AppendWhere(sqlParam, ref sqlStr, dto);
                    result = this.DBService.ExecuteDynamicObject(Context, sqlStr, sqlParam);
                    if (!result.IsNullOrEmpty() && result.Any())
                    {
                        foreach (var item in result)
                        {
                            data.Add(new
                            {
                                Name = Convert.ToString(item["Fname"]),//型号名称
                                Qty = Convert.ToDecimal(item["StockQty"])//数量
                            });
                        }
                    }
                    resp.Data = data;
                    break;
                default:
                    resp.Message = "您请求的数据类型暂未支持！";
                    resp.Success = false;
                    return resp;
            }
            resp.Message = "操作成功！";
            resp.Success = true;
            return resp;
        }

        private void AppendWhere(List<SqlParam> sqlParam, ref string sqlStr, ReportStockRankDTO dto)
        {
            string strWhere = string.Empty;
            if (!this.Context.Company.Equals(this.Context.TopCompanyId))
            {
                strWhere += "  and t1.fmainorgid=@fmainorgid ";
            }
            else
            {
                //查询需要排除的组织
                List<string> testCompanys = this.Context.GetTestCompanyIds();
                if (testCompanys != null && testCompanys.Count() > 0)
                {
                    strWhere += $@" and t1.fmainorgid not in ('{string.Join("','", testCompanys)}')";
                }
            }

            if (!dto.WarehouseId.IsNullOrEmptyOrWhiteSpace())
            {
                strWhere += $@" and t1.fstorehouseid = '{dto.WarehouseId}'";
            }
            else if (!dto.WarehouseType.IsNullOrEmptyOrWhiteSpace())
            {
                var storehouseids = new List<string>();
                var sql = $@"select fid from t_ydj_storehouse with(nolock) where fwarehousetype='{dto.WarehouseType}'";
                var result = this.DBService.ExecuteDynamicObject(Context, sql);
                if (!result.IsNullOrEmpty() && result.Any())
                {
                    foreach (var item in result)
                    {
                        storehouseids.Add(Convert.ToString(item["fid"]));
                    }
                }
                strWhere += $@" and t1.fstorehouseid in ('{string.Join("','", storehouseids)}')";
            }

            //下钻数据查询
            switch (dto.ReportType.ToLower())
            {
                case "seltypeamounttop10":
                case "seltypecounttop10":
                    if (!dto.CategoryId.IsNullOrEmptyOrWhiteSpace())
                    {
                        var _fcategoryid1 = getCategorys(dto.CategoryId);
                        strWhere += $@" and t2.fcategoryid in ('{string.Join("','", _fcategoryid1)}')";
                    }
                    if (!dto.SeriesId.IsNullOrEmptyOrWhiteSpace())
                    {
                        sqlParam.Add(new SqlParam("@fseriesid", System.Data.DbType.String, dto.SeriesId));
                        strWhere += " and t2.fseriesid=@fseriesid ";
                    }
                    break;
                default:
                    break;
            }

            sqlStr = string.Format(sqlStr, strWhere);
        }

        /// <summary>
        /// 处理品类汇总
        /// </summary>
        /// <param name="dynamicObjects"></param>
        /// <returns></returns>
        private List<Data> DealCategory(DynamicObjectCollection dynamicObjects, string reportType)
        {
            //固定取产品类10
            string sql = $@"select fid, fname from dbo.ser_ydj_category with(nolock) where fparentid =(select fid from ser_ydj_category with(nolock) 
                            where fname='产品类' and fnumber='10') and fmainorgid = '{ this.Context.TopCompanyId }'";
            var cats = this.DBService.ExecuteDynamicObject(this.Context, sql);

            var fcategoryids = new List<Category>();
            foreach (var item in cats)
            {
                fcategoryids.Add(new Category
                {
                    Id = Convert.ToString(item["fid"]),
                    Name = Convert.ToString(item["fname"])
                });
            }

            var catSql = $@"select fid,fnumber,fname,fparentid from ser_ydj_category with(nolock)";
            var catdy = this.DBService.ExecuteDynamicObject(this.Context, catSql);

            //类别id合集
            Dictionary<string, List<string>> fcategoryid_dics = new Dictionary<string, List<string>>();
            List<string> exceptOther = new List<string>();

            var result = new List<Data>();
            if (fcategoryids.Count() > 0)
            {
                foreach (var item in fcategoryids)
                {
                    var _fcategoryid1 = getAllChildrenCategorys(catdy, item.Id).Select(x => x.fid).Distinct().ToList();
                    if (_fcategoryid1.Any())
                    {
                        exceptOther.AddRange(_fcategoryid1);

                        var data = dynamicObjects.Where(x => _fcategoryid1.Contains(Convert.ToString(x["fid"]))).ToList();

                        result.Add(new Data
                        {
                            CategoryId = item.Id,//大品类Id
                            Name = item.Name,//名称
                            Amount = reportType == "stockamountzb" ? data.Sum(x => Convert.ToDecimal(x["famount"])) : 0,//金额
                            Qty = reportType == "stocktypetop10" ? data.Sum(x => Convert.ToInt32(x["StockQty"])) : 0 //数量
                        });
                    }
                }
            }

            result.Add(new Data
            {
                CategoryId = "Other",//大品类Id
                Name = "其他类",//名称
                Qty = reportType == "stocktypetop10" ? dynamicObjects.Select(x => Convert.ToInt32(x["StockQty"])).Sum() - result.Select(x => x.Qty).Sum() : 0, //数量
                Amount = reportType == "stockamountzb" ? dynamicObjects.Select(x => Convert.ToDecimal(x["famount"])).Sum() - result.Select(x => x.Amount).Sum() : 0 //金额
            });

            if (reportType == "stockamountzb")
            {
                result = result.OrderByDescending(x => x.Amount).Take(10).ToList();
            }
            else
            {
                result = result.OrderByDescending(x => x.Qty).Take(10).ToList();
            }
            return result;
        }

        #region 帮助类
        /// <summary>
        /// 获取商品类别及其子类别（含其他类）
        /// </summary>
        /// <param name="categoryid"></param>
        /// <returns></returns>
        private List<string> getCategorys(string categoryid)
        {
            //固定取产品类10
            string sql = $@"select fid, fname from dbo.ser_ydj_category with(nolock) where fparentid =(select fid from ser_ydj_category with(nolock) 
                            where fname='产品类' and fnumber='10') and fmainorgid = '{ this.Context.TopCompanyId }'";
            var cats = this.DBService.ExecuteDynamicObject(this.Context, sql);

            var catSql = $@"select fid,fnumber,fname,fparentid from ser_ydj_category with(nolock)";
            var catdy = this.DBService.ExecuteDynamicObject(this.Context, catSql);

            var result = new List<string>();
            if (categoryid != "Other")
            {
                result = getAllChildrenCategorys(catdy, categoryid).Select(x => x.fid).Distinct().ToList();
            }
            else
            {
                //类别id合集
                List<string> exceptOther = new List<string>();
                if (cats.Count() > 0)
                {
                    foreach (var item in cats)
                    {
                        var _fcategoryid1 = getAllChildrenCategorys(catdy, Convert.ToString(item["fid"])).Select(x => x.fid).Distinct().ToList();
                        if (_fcategoryid1.Any())
                        {
                            exceptOther.AddRange(_fcategoryid1);
                        }
                    }
                }
                result = catdy.Select(x => (string)x["fid"]).Except(exceptOther).ToList();
            }
            return result;
        }

        /// <summary>
        /// 获取商品类别及其子类
        /// </summary>
        /// <param name="dynamicObjects"></param>
        /// <param name="fcategoryid"></param>
        /// <returns></returns>
        private List<keyValue> getAllChildrenCategorys(DynamicObjectCollection dynamicObjects, string fcategoryid)
        {
            var allcatyKeyValues = dynamicObjects.Select(x => new keyValue()
            {
                fid = Convert.ToString(x["fid"]),
                fparentid = Convert.ToString(x["fparentid"]),
            }).ToList();
            List<keyValue> result = new List<keyValue>();
            GetCategoryTreeNodes(allcatyKeyValues, fcategoryid, fcategoryid, ref result);
            return result;
        }

        /// <summary>
        /// 获取组织结构树(包含本身)
        /// </summary>
        /// <param name="list"></param>
        /// <param name="id"></param>
        /// <param name="treeNodes"></param>
        /// <returns></returns>
        static void GetCategoryTreeNodes(List<keyValue> list, string id, ref List<keyValue> treeNodes)
        {
            treeNodes.Add(new keyValue() { fid = id, fparentid = id });

            if (list == null || string.IsNullOrWhiteSpace(id))
                return;

            List<keyValue> sublist = null;
            if (!string.IsNullOrWhiteSpace(id))
            {
                sublist = list.Where(t => t.fparentid == id).ToList();
            }
            if (!sublist.Any())
                return;
            foreach (var item in sublist)
            {
                treeNodes.Add(new keyValue() { fid = item.fid, fparentid = item.fparentid });
                GetCategoryTreeNodes(list, item.fid, ref treeNodes);
            }
        }

        /// <summary>
        /// 获取组织结构树(包含本身)
        /// </summary>
        /// <param name="list"></param>
        /// <param name="id"></param>
        /// <param name="treeNodes"></param>
        /// <returns></returns>
        static void GetCategoryTreeNodes(List<keyValue> list, string id, string fparentid, ref List<keyValue> treeNodes)
        {
            treeNodes.Add(new keyValue() { fid = id, fparentid = fparentid });

            if (list == null || string.IsNullOrWhiteSpace(id))
                return;

            List<keyValue> sublist = null;
            if (!string.IsNullOrWhiteSpace(id))
            {
                sublist = list.Where(t => t.fparentid == fparentid).ToList();
            }
            if (!sublist.Any())
                return;
            foreach (var item in sublist)
            {
                treeNodes.Add(new keyValue() { fid = item.fid, fparentid = item.fparentid });
                GetCategoryTreeNodes(list, item.fid, ref treeNodes);
            }
        }

        private class keyValue
        {
            public string fparentid { get; set; }
            public string fid { get; set; }
        }

        private class Category
        {
            public string Id { get; set; }
            public string Name { get; set; }
        }

        private class Data
        {
            public string CategoryId { get; set; }
            public string Name { get; set; }
            public decimal Amount { get; set; }
            public int Qty { get; set; }
        }

        #endregion
    }
}