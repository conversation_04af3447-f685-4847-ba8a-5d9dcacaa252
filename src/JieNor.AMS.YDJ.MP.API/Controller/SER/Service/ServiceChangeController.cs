using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.SuperOrm.DataEntity;
using ServiceStack.Web;

namespace JieNor.AMS.YDJ.MP.API.Controller.SER.Service
{
    /// <summary>
    /// 微信小程序：服务单申请补价
    /// </summary>
    public class ServiceChangeController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ServiceChangeDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>
            {
                Data = new object()
            };

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "参数 Id 不能为空！";
                resp.Success = false;
                return resp;
            }

            if (dto.Products == null || dto.Products.Count == 0)
            {
                resp.Message = "参数 Products 不能为空！";
                resp.Success = false;
                return resp;
            }

            var data = this.Context.GetBizDataById("ydj_service", dto.Id, true);

            //dto.SourceType = "ydj_service";//源单类型
            dto.CustomerId = Convert.ToString(data["fcustomerid"]);//对应服务单的商户名
            dto.Contacts = Convert.ToString(data["fmasterid"]);
            return AddServiceFollowerRecord_Re(this.Request, dto, data);
        }

        private object AddServiceFollowerRecord_Re(IRequest request, ServiceChangeDTO dto, DynamicObject dataobj)
        {
            var billData = BuildBillData(dto, dataobj);
            // 调用麦浩的接口
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                request,
                new CommonBillDTO()
                {
                    FormId = "ydj_servicechange",
                    OperationNo = "save",
                    BillData = billData,
                    Id = dto.Id
                });
            var result = response?.OperationResult;

            return result.ToResponseModel<BaseDataModel>();
        }

        private string BuildBillData(ServiceChangeDTO dto, DynamicObject dataobj)
        {
            var productObjs = dataobj["fproductentry"] as DynamicObjectCollection;

            Dictionary<string, object> data = new Dictionary<string, object>();
            data.Add("id", dto.Id);
            //客户
            data.Add("fcustomerid", dataobj["fcustomerid"]);
            //部门
            data.Add("fdeptid", dataobj["fdeptid"]);
            //服务类型
            data.Add("fservicetype", dataobj["fservicetype"]);
            //“业务日期”
            data.Add("fdate", BeiJingTime.Now);
            data.Add("fdealernumber", dataobj["fbillno"]);
            data.Add("fsourcenumber", dataobj["Id"]);

            data.Add("fteamid", dataobj["fteamid"]);
            data.Add("fcaptainid", dataobj["fcaptainid"]);
            //关联员工、关联用户
            data.Add("fstaffid_link", dataobj["fstaffid_link"]);
            data.Add("fdeptid_link", dataobj["fdeptid_link"]);

            data.Add("fmasterid", dataobj["fmasterid"]);
            data.Add("fcustname", dataobj["fname"]);
            data.Add("fphone", dataobj["fphone"]);
            data.Add("fprovince", dataobj["fprovince"]);
            data.Add("fcity", dataobj["fcity"]);
            data.Add("fregion", dataobj["fregion"]);
            //小程序申请补价生成服务变更单 直接是审核中（已提交）的状态
            data.Add("fstatus", "D");
            // 服务项目明细 
            var porductItems = new List<Dictionary<string, object>>();
            foreach (var expenseItem in dto.Products)
            {
                var ps = productObjs.Where(s => Convert.ToString(s["Id"]) == expenseItem.id).ToList();
                porductItems.Add(new Dictionary<string, object>
                {
                    { "fseritemid", Convert.ToString(ps[0]?["fseritemid"]) },
                    //关键的反写关联字段不能丢
                    { "fsourceentryid", Convert.ToString(ps[0]?["id"]) },
                    { "funitid", Convert.ToString(ps[0]?["funitid"]) },
                    { "fqty", Convert.ToInt32(ps[0]?["fqty"]) },
                    { "fqty_chg", expenseItem.qty },
                    { "fprice", expenseItem.price },
                    { "famount", Convert.ToDecimal(ps[0]?["famount"]) },
                    { "famount_chg", expenseItem.amount }
                });
            }
            data.Add("fentity", porductItems);
            data.Add("ftotalamount", dataobj["fexpectamount"]);
            data.Add("ftotalamount_chg",dto.TotalAmount);
            var billData = (new List<Dictionary<string, object>> { data }).ToJson();
            return billData;
        }

    }
}