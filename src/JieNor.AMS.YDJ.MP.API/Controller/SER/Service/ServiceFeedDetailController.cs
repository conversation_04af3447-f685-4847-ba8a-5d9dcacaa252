using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace JieNor.AMS.YDJ.MP.API.Controller.SER.Service
{
    public class ServiceFeedDetailController : BaseServiceFeedController
    {
        public HtmlForm ServiceFeedForm;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ServiceFeedDetailDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<ServiceFeedDetailModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }

            //直接根据唯一标识获取数据
            this.ServiceFeedForm = this.MetaModelService.LoadFormModel(this.Context, "ser_servicefeed");
            var ServiceFeedObj = this.ServiceFeedForm.GetBizDataById(this.Context, dto.Id, true);

            if (ServiceFeedObj == null)
            {
                resp.Message = "服务反馈不存在或已被删除！";
                resp.Success = false;
                return resp;
            }
            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Code = 200;
            resp.Data = MapTo(ServiceFeedObj);
            return resp;
        }

        private ServiceFeedDetailModel MapTo(DynamicObject ServiceFeedObj)
        {
            ServiceFeedDetailModel model = new ServiceFeedDetailModel
            {
                Id = JNConvert.ToStringAndTrim(ServiceFeedObj["Id"]),
                CreateDate = Convert.ToDateTime(ServiceFeedObj["fcreatedate"]),
                Fprodesript = JNConvert.ToStringAndTrim(ServiceFeedObj["fprodesript"]),
                Number = JNConvert.ToStringAndTrim(ServiceFeedObj["fbillno"]),
                ServiceID = JNConvert.ToStringAndTrim(ServiceFeedObj["fsourceser"]),
            };
            //反馈人(师傅)
            var fname= ServiceFeedObj["ffeeder_ref"] as DynamicObject;
            model.Ffeeder = JNConvert.ToStringAndTrim(fname["fname"]);
            //处理状态
            var fhandlestatu = ServiceFeedObj["fhandlestatus_ref"] as DynamicObject;
            model.Fhandlestatus = JNConvert.ToStringAndTrim(fhandlestatu["fenumitem"]);
            //问题类别 
            var fsprotype = ServiceFeedObj["fsprotype_ref"] as DynamicObject;
            model.Fsprotype = JNConvert.ToStringAndTrim(fsprotype["fenumitem"]);

            //分拆图片
            var ImageList = JNConvert.ToStringAndTrim(ServiceFeedObj["fprograph"]).Split(',');
            List<ServiceFeedDetailModel.Image> Images = new List<ServiceFeedDetailModel.Image>();
            foreach (var item in ImageList)
            {
                Images.Add(new ServiceFeedDetailModel.Image()
                {
                    ImageUrl = item.GetSignedFileUrl(),
                    ImageThumbUrl = item.GetSignedFileUrl(true)
                });
            }
            model.Images = Images;
            return model;
        }
    }
}
