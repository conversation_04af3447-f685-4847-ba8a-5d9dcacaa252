using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using ServiceStack;
using System.Collections.Generic;
using JieNor.Framework.DataTransferObject.Poco;
using ServiceStack.Web;
using System;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataManager;
using System.Data;
using System.Linq;

namespace JieNor.AMS.YDJ.MP.API.Controller.SER.Service
{
    /// <summary>
    /// 微信小程序：服务单 师傅接单接口
    /// </summary>
    public class ServiceReceiveController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ServiceReceiveDTO dto)
        {
            base.InitializeOperationContext(dto);
            //回写服务单状态为 待预约
            BaseServiceController.RewriteServiceState("sersta04", dto.Id, this.Request);

            var data = this.Context.GetBizDataById("ydj_service", dto.Id, true);
            var masterobj = data["fmasterid_ref"] as DynamicObject;
            //写入跟进记录
            var resp = new BaseResponse<object>();
            ServiceFollowSaveDTO dtoss = new ServiceFollowSaveDTO
            {
                Description = Convert.ToString(masterobj["fname"] + "师傅已确认接单"),//跟进描述
                SourceType = "ydj_service",//来源单
                SourceNumber = Convert.ToString(data["fbillno"]),//来源单据编号
                Contacts = Convert.ToString(masterobj["fname"]),//师傅名
                CustomerId = Convert.ToString(data["fcustomerid"]),//客户id 新
                FollowTime = DateTime.Now,
                ObjectType = "objecttype27" //"跟进记录" 类型
            };
            if ((this.Context.AddServiceFollowerRecord(this.Request, dtoss)).Success)
            {
                resp.Message = "操作成功！";
                resp.Success = true;
                resp.Code = 200;
            }
            else
            {
                resp.Message = "跟进记录保存失败！";
                resp.Success = false;
            }
            return resp;
        }
    }
}