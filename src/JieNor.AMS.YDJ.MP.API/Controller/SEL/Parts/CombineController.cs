using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.DTO.SEL.Combine;
using JieNor.AMS.YDJ.MP.API.DTO.SEL.Suite;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.SEL.Parts
{
    /// <summary>
    /// 配件接口
    /// </summary>
    public class CombineController : BaseController
    {
        public BaseResponse<CombineBaseDTO> Any(CombineGetDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<CombineBaseDTO>();

            // "气压杆" "支撑杆" "床箱底板"

            var combine = new CombineBaseDTO();
            combine.Parts = new List<PartsBaseDTO>();

            var product = this.Context.LoadBizDataById("ydj_product", dto.ProductId, true);
            if (product == null)
            {
                return resp.Error("未找到商品信息");
            }

            var category = product["fcategoryid_ref"] as DynamicObject;
            if (category == null)
            {
                return resp.Error("未找到商品的分类信息");
            }

            var attrlist = ChecksuitAttr(dto.ProductId);

            //是否可添加配件
            var fbedpartflag = Convert.ToBoolean(product["fbedpartflag"]);
            var fispartflag = Convert.ToBoolean(product["fispartflag"]);
            if (attrlist.Count == 0 && (!fispartflag && !fbedpartflag))
            {
                return resp.Error("当前商品不允许添加配件");
            }

            List<DynamicObject> list = new List<DynamicObject>();

            //数据隔离规则SQL
            var authPara = new DataQueryRuleParaInfo()
            {
                SrcFormId = dto.SrcFormId,
            };
            authPara.SrcPara.Add("billtypeName", dto.BillTypeName);
            authPara.SrcPara.Add("billtypeNo", dto.BillTypeNo);
            var allProductView = Context.GetAuthProductDataPKID(authPara);

            //选配配件服务
            var selPartsService = this.Container.GetService<ISelectionPartsService>();
            var tgc = selPartsService.LoadPartsMapingEntryList(this.Context, dto.ProductId, dto.PropList, allProductView);
            list.AddRange(tgc);

            //如果是勾选了配件标记但是 未勾选允许选配（即标准品）带出铁架床配件逻辑
            var fispresetprop = Convert.ToBoolean(product["fispresetprop"]);
            //如果是勾选了配件标记但是 未勾选允许选配（即标准品）带出铁架床配件逻辑
            if (fispartflag && !fispresetprop)
            {
                //兼容选配配件映射配置了多个相同配件的情况。
                string sql = $@"SELECT DISTINCT t_sel_fittingsmapentry.fmaterialid,product.fname AS 'fmaterialname',product.fnumber AS 'fmaterialnumber',product.funstdtype
                            ,t_sel_fittingsmap.fcategoryid,SER_YDJ_CATEGORY.fname as fcategoryname,SER_YDJ_CATEGORY.fnumber as fcategorynumber,t_sel_fittingsmapentry.fpropid AS 'fattrinfo',fqty FROM t_sel_fittingsmap 
                            INNER JOIN t_sel_fittingsmapentry with(nolock) ON t_sel_fittingsmapentry.fid = t_sel_fittingsmap.fid and t_sel_fittingsmapentry.fdisable != 1
                            INNER JOIN SER_YDJ_CATEGORY with(nolock) ON SER_YDJ_CATEGORY.fid =t_sel_fittingsmap.fcategoryid
                            INNER JOIN dbo.T_BD_MATERIAL product with(nolock) ON product.fid = t_sel_fittingsmapentry.fmaterialid
                            WHERE SER_YDJ_CATEGORY.fname ='铁架床' AND fmatchbyproduct = '1' AND fmaterialidnew  = '{dto.ProductId}'";
                var data = this.DBService.ExecuteDynamicObject(this.Context, sql).ToList();
                combine.Parts.AddRange(
                    data.Select(x =>
                    {
                        return GetPartsDTO(x);
                    })
                );
            }

                //气压杆
            if (ChecksuitAttr_qyg(dto.ProductId))
            {
                //如果满足了定制品属性 【属性 - 床架类别】 = "标准气压床架" 则直接在商品档案里找对应 【商品类别】为气压杆的商品
                string sql = $@"SELECT product.fid AS fmaterialid,product.fname as fmaterialname,product.fnumber as fmaterialnumber,product.funstdtype,
                                category.fid as fcategoryid,category.fname as fcategoryname,category.fnumber as fcategorynumber, 1 as fqty
                                FROM T_BD_MATERIAL product with(nolock) 
                                INNER JOIN ser_ydj_category category with(nolock) ON category.fid = product.fcategoryid {DataRowACLHelper.GetDataRowACLFilter(this.Context, "category.")}
                                WHERE category.fname ='气压杆' {DataRowACLHelper.GetDataRowACLFilter(this.Context, "product.")}
                                      and exists (select FPKId from ({allProductView})acl where product.fid=FPKId)
                                ";
                var data = this.DBService.ExecuteDynamicObject(this.Context, sql);
                combine.Parts.AddRange(
                    data.Select(x =>
                    {
                        return GetPartsDTO(x);
                    })
                );
            }

            List<PropEntity> propEntityLists = GetProp(attrlist);
            //支撑杆
            var zcg = LoadPartsMapingEntryList(this.Context, dto.ProductId, propEntityLists, "支撑杆", allProductView);
            list.AddRange(zcg);

            //床箱底板
            var cxdb = LoadPartsMapingEntryList(this.Context, dto.ProductId, propEntityLists, "床箱底板", allProductView);
            list.AddRange(cxdb);

            combine.Parts.AddRange(
                list.Select(x =>
                {
                    return GetZCParts(x);
                })
            );

            combine.Parts.ForEach(x =>
            {
                ProductProfileDTO pp = new ProductProfileDTO
                {
                    Id = x.Product.Id,
                    PropList = x.AuxPropVals,
                    CustomDesc = ""
                };

                var profile = this.Gateway.Send<BaseResponse<ProductProfileModel>>(pp);
                if (profile.Success)
                {
                    x.ImageList = profile.Data.ImageList;
                    x.SalPrice = profile.Data.SalPrice;
                    x.HqSalPrice = profile.Data.HqSalPrice;
                    x.GuidePrice = profile.Data.GuidePrice;
                }
            });

            combine.IsSofaCategory = ProductUtil.HaveAnyCategory(this.Context, "沙发类", dto.ProductId);

            resp.Data = combine;
            resp.Success = true;
            return resp;
        }

        private PartsBaseDTO GetZCParts(DynamicObject x)
        {
            PartsBaseDTO po = GetPartsDTO(x);

            var attrinfoobj = this.Context.LoadBizDataById("bd_auxpropvalueset", Convert.ToString(x?["fattrinfo"]));
            var setEntrys = attrinfoobj?["fentity"] as DynamicObjectCollection;
            //辅助属性组合值键值对
            List<PropEntity> auxPropKv = new List<PropEntity>();
            if (setEntrys != null && setEntrys.Any())
            {
                foreach (var item in setEntrys)
                {
                    var sel_propobj = this.Context.LoadBizDataById("sel_prop", Convert.ToString(Convert.ToString(item["fauxpropid"])));
                    auxPropKv.Add(new PropEntity()
                    {
                        PropId = Convert.ToString(item["fauxpropid"]),
                        PropName = Convert.ToString(sel_propobj["fname"]),
                        PropNumber = Convert.ToString(sel_propobj["fnumber"]),
                        PropValueDataType = (PropValueDataTypeEnum)Convert.ToInt32(sel_propobj["fdatatype"]),
                        ValueId = Convert.ToString(item["fvalueid"]),
                        ValueName = Convert.ToString(item["fvaluename"]),
                        ValueNumber = Convert.ToString(item["fvaluenumber"])
                    });
                }
            }
            po.AuxPropVals = auxPropKv;

            return po;
        }

        private PartsBaseDTO GetPartsDTO(DynamicObject x)
        {
            return new PartsBaseDTO
            {
                Product = new DTO.BaseDataDTO
                {
                    Id = Convert.ToString(x["fmaterialid"]),
                    Name = Convert.ToString(x["fmaterialname"]),
                    Number = Convert.ToString(x["fmaterialnumber"])
                },
                ProductCategory = new DTO.BaseDataDTO
                {
                    Id = Convert.ToString(x["fcategoryid"]),
                    Name = Convert.ToString(x["fcategoryname"]),
                    Number = Convert.ToString(x["fcategorynumber"])
                },
                IsNonStandard = Convert.ToString(x["funstdtype"]) == "1",
                AuxPropVals = ProductUtil.LoadPropEntityList(this.Context, Convert.ToString(x?["fattrinfo"])),
                Qty = Convert.ToDecimal(x["fqty"] ?? "1")
            };
        }

        /// <summary>
        /// 根据传过来的满足条件的 辅助属性attrinfo 封装成辅助属性对象
        /// </summary>
        /// <param name="attrlist"></param>
        /// <returns></returns>
        private List<PropEntity> GetProp(List<DynamicObject> attrlist)
        {
            List<PropEntity> propLists = new List<PropEntity>();
            foreach (var i in attrlist)
            {
                var attrinfoobj = this.Context.LoadBizDataById("bd_auxpropvalueset", Convert.ToString(i?["fattrinfo"]));
                var setEntrys = attrinfoobj?["fentity"] as DynamicObjectCollection;
                if (setEntrys != null && setEntrys.Any())
                {
                    var attrIds = setEntrys.Select(o => Convert.ToString(o["fauxpropid"])).ToList<string>();
                    var attrobjs = this.Context.LoadBizBillHeadDataByACLFilter("sel_prop", $"fid in ('{string.Join("','", attrIds)}')", "fid,fname,fnumber,fdatatype").ToList();
                    var auxPropKv1 = new Dictionary<string, string>();
                    foreach (var item in setEntrys)
                    {
                        var selpropobj = attrobjs.Where(o => Convert.ToString(o["fid"]).EqualsIgnoreCase(Convert.ToString(item["fauxpropid"]))).FirstOrDefault();
                        var fdatatype = Convert.ToString(selpropobj["fdatatype"]);
                        PropValueDataTypeEnum PropValueDataType = PropValueDataTypeEnum.Char; ;
                        switch (fdatatype)
                        {
                            case "1":
                                PropValueDataType = PropValueDataTypeEnum.Char;
                                break;
                            case "2":
                                PropValueDataType = PropValueDataTypeEnum.Numeric;
                                break;
                            default:
                                break;
                        }
                        propLists.Add(new PropEntity
                        {
                            PropId = Convert.ToString(item["fauxpropid"]),
                            PropName = Convert.ToString(selpropobj?["fname"]),
                            PropNumber = Convert.ToString(selpropobj["fnumber"]),
                            PropValueDataType = PropValueDataType,
                            ValueId = Convert.ToString(item["fvalueid"]),
                            ValueName = Convert.ToString(item["fvaluename"]),
                            ValueNumber = Convert.ToString(item["fvaluenumber"])
                        });
                    }
                }
            }
            return propLists;
        }

        private bool ChecksuitAttr_qyg(string productid)
        {
            string sql = $@" SELECT attr.fname FROM T_SEL_SUITEMAPENTRY suit with(nolock)
                         INNER  JOIN t_bd_auxpropvalue attr with(nolock) ON suit.fattrinfo = attr.fid
                          WHERE suit.fmaterialid = '{productid}' AND (attr.fname LIKE '%床架类别:标准气压床架%') ";
            return this.DBService.ExecuteDynamicObject(this.Context, sql).Count > 0;
        }

        /// <summary>
        /// 判断标准品映射中 对应商品 【定制商品辅助属性】里的属性名与属性值是否满足条件
        /// </summary>
        /// <param name="productid"></param>
        /// <returns></returns>
        private List<DynamicObject> ChecksuitAttr(string productid)
        {
            //string sql = $@" SELECT attr.fname,suit.fattrinfo FROM T_SEL_SUITEMAPENTRY suit
            //             INNER  JOIN t_bd_auxpropvalue attr ON suit.fattrinfo = attr.fid
            //              WHERE suit.fmaterialid = '{productid}' AND (attr.fname LIKE '%床架类别:标准气压床架%' or attr.fname LIKE '%床架类别:可调标准床架%') ";
            //后面变更去除 “床架类别:标准气压床架”或者 “床架类别:可调标准床架”的条件。
            string sql = $@" SELECT attr.fname,suit.fattrinfo FROM T_SEL_SUITEMAPENTRY suit with(nolock)
                         INNER  JOIN t_bd_auxpropvalue attr with(nolock) ON suit.fattrinfo = attr.fid
                          WHERE suit.fmaterialid = '{productid}'";
            return this.DBService.ExecuteDynamicObject(this.Context, sql).ToList();
        }

        //应对添加配件时 根据辅助属性过滤条件 和【选配配件服务】逻辑不一致 所以单独处理
        private List<DynamicObject> LoadPartsMapingEntryList(UserContext userCtx, string productId, List<PropEntity> propList, string partsname,string allProductView)
        {
            var partsEntrys = new List<DynamicObject>();
            //如果属性中不存在配件属性，则不用处理
            var propIds = propList
                .Select(o => o.PropId)
                .ToList();
            //var partsPropIds = this.MatchPartsPropIds(userCtx, propIds);
            //if (!partsPropIds.Any()) return partsEntrys;

            ////配件属性名称
            //var partsPropNames = new List<string>();
            //foreach (var partsPropId in partsPropIds)
            //{
            //    foreach (var prop in propList)
            //    {
            //        if (partsPropId.EqualsIgnoreCase(prop.PropId))
            //        {
            //            partsPropNames.Add(prop.PropName);
            //            break;
            //        }
            //    }
            //}

            //加载配件映射明细列表
            //var partsMapingEntrys = this.LoadPartsMapingEntrys(userCtx, productId, partsname);
            //if (partsMapingEntrys == null || !partsMapingEntrys.Any()) return partsEntrys;

            List<string> partsTypeNames = new List<string>();
            partsTypeNames.Add(partsname);

            var selPartsService = this.Container.GetService<ISelectionPartsService>();
            //调用通用加载配件映射明细列表接口
            var partsMapingEntrys = selPartsService.LoadPartsMapingEntrys(userCtx, productId, partsTypeNames, allProductView);
            if (partsMapingEntrys == null || !partsMapingEntrys.Any()) return partsEntrys;

            //解析条件公式中的属性
            var formulas = new List<string>();
            foreach (var item in partsMapingEntrys)
            {
                var condition = Convert.ToString(item?["fconditions"]);
                formulas.Add(condition);
            }
            var formulaService = userCtx.Container.GetService<IFormulaService>();
            var propDatas = formulaService.ParseFormulaPropDatas(userCtx, formulas);

            //执行条件公式
            foreach (var item in partsMapingEntrys)
            {
                var mapingName = Convert.ToString(item?["fname"]);
                var condition = Convert.ToString(item?["fconditions"]);
                if (condition.IsNullOrEmptyOrWhiteSpace()) continue;

                //调用公式服务：判断传入的【属性】和【属性值】是否满足条件
                var errorMessage = "";
                var isOk = formulaService.TryParseAndExecutePropFormula<bool>(userCtx, condition, propList, out errorMessage, propDatas);
                if (!isOk)
                {
                    if (!errorMessage.IsNullOrEmptyOrWhiteSpace())
                    {
                        //throw new BusinessException($"选配配件映射【{mapingName}】条件公式【{condition}】解析失败：{errorMessage}");
                    }
                    //条件不满足，不处理
                    continue;
                }

                //将满足条件的配件明细返回
                partsEntrys.Add(item);
            }

            return partsEntrys;
        }

        /// <summary>
        /// 加载配件映射明细列表 区别： 不需要根据辅助属性中的要有配件的属性, 获取其【属性名】查询到匹配所有《选配配件映射》里单据头的【配件类型】
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="partsPropNames">配件属性名称集合</param>
        /// <returns>返回配件映射明细列表</returns>
        private DynamicObjectCollection LoadPartsMapingEntrys(UserContext userCtx, string productId, string partsname)
        {
            if (productId.IsNullOrEmptyOrWhiteSpace()) return null;

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("@fproductid", System.Data.DbType.String, productId)
            };

            var aclFilter_FM = DataRowACLHelper.GetDataRowACLFilter(this.Context, "fm.");
            var aclFilter_M = DataRowACLHelper.GetDataRowACLFilter(this.Context, "m.");
            var aclFilter_C = DataRowACLHelper.GetDataRowACLFilter(this.Context, "c.");
            var aclFilter_CATE = DataRowACLHelper.GetDataRowACLFilter(this.Context, "cate.");
            var aclFilter_P = DataRowACLHelper.GetDataRowACLFilter(this.Context, "p.");

            var sqlText = $@"
            select t.fid,t.fname,fme.fconditions,fme.fpropid fattrinfo,
                p.fid as fmaterialid,p.fname as fmaterialname,p.fnumber as fmaterialnumber,p.funstdtype,
                cate.fid as fcategoryid,cate.fname as fcategoryname,cate.fnumber as fcategorynumber
            from 
            (
	            select fm.fid,fm.fname
                from t_sel_fittingsmap fm with(nolock) 
	            inner join ser_ydj_category c with(nolock) on c.fid=fm.fcategoryid {aclFilter_C}
	            inner join t_bd_material m with(nolock) on m.fcategoryid=fm.fcategoryid_d and m.fid=@fproductid {aclFilter_M}
	            where fm.fforbidstatus='0' and fmatchbymodel='0' AND c.fname = '{partsname}' {aclFilter_FM}
	            union
	            select fm.fid,fm.fname
                from t_sel_fittingsmap fm with(nolock) 
	            inner join ser_ydj_category c with(nolock) on c.fid=fm.fcategoryid {aclFilter_C}
	            inner join t_bd_material m with(nolock) on m.fcategoryid=fm.fcategoryid_d and m.fid=@fproductid {aclFilter_M}
	            inner join t_sel_dimensionmodelentry dme with(nolock) on dme.fseltypeid=m.fseltypeid 
	            where  fm.fforbidstatus='0' and fmatchbymodel='1' AND c.fname = '{partsname}' {aclFilter_FM}
            ) t 
            inner join t_sel_fittingsmapentry fme with(nolock) on fme.fid=t.fid and fme.fdisable != 1
            inner join t_bd_material p with(nolock) on fme.fmaterialid = p.fid {aclFilter_P}
            left join ser_ydj_category cate with(nolock) on cate.fid=p.fcategoryid {aclFilter_CATE}";

            var partsMapEntrys = this.DBService.ExecuteDynamicObject(userCtx, sqlText, sqlParam);

            return partsMapEntrys;
        }

        private List<string> MatchPartsPropIds(UserContext userCtx, List<string> propIds)
        {
            var partsPropIds = new List<string>();

            if (!propIds.Any()) return partsPropIds;

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company)
            };

            var sqlText = @"select fid from t_sel_prop with(nolock) where fisparts='1'";

            if (propIds.Count == 1)
            {
                sqlText += " and fid=@fid";
                sqlParam.Add(new SqlParam("@fid", System.Data.DbType.String, propIds[0]));
            }
            else
            {
                var paramNames = new List<string>();
                for (int i = 0; i < propIds.Count; i++)
                {
                    paramNames.Add($"@fid{i}");
                    sqlParam.Add(new SqlParam($"@fid{i}", System.Data.DbType.String, propIds[i]));
                }
                sqlText += $" and fid in({string.Join(",", paramNames)})";
            }

            using (var reader = this.DBService.ExecuteReader(userCtx, sqlText, sqlParam))
            {
                while (reader.Read())
                {
                    var partsPropId = reader.GetValueToString("fid");
                    partsPropIds.Add(partsPropId);
                }
            }

            return partsPropIds;
        }
    }
}
