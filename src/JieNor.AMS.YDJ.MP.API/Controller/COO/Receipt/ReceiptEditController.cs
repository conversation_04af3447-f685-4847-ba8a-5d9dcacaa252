using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MP.API.DTO.COO.Receipt;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Model.COO.Receipt;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.COO.Receipt
{
    /// <summary>
    /// 微信小程序：收款单编辑接口
    /// </summary>
    public class ReceiptEditController : BaseController
    {
        public override string FormId { get; } = "coo_incomedisburse";

#warning 等在线付款和麦浩合并付款功能完成后才能编写此接口
        public object Any(ReceiptEditDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<ReceiptModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "合同尚未签订！";
                resp.Success = false;
                return resp;
            }

            // 直接根据唯一标识获取数据 
            var data = this.HtmlForm.GetBizDataById(this.Context, dto.Id, true);
            if (data == null)
                return resp;

            var fid = JNConvert.ToStringAndTrim(data["fsourceid"]);
            var fsourceformid = JNConvert.ToStringAndTrim(data["fsourceformid"]);
            // 确认已收
            string fconfirmedamount = "0";
            //收款待确认
            string freceivabletobeconfirmed = "0";
            // 待结算金额
            decimal funsettleamount = 0;
            //货款金额
            decimal loanAmount = 0;
            if (!fid.IsNullOrEmptyOrWhiteSpace() && !fsourceformid.IsNullOrEmptyOrWhiteSpace())
            {
                if (fsourceformid == "ydj_order")
                {
                    // 直接根据唯一标识获取数据
                    var orderForm = this.MetaModelService.LoadFormModel(this.Context, fsourceformid);
                    var orderdata = orderForm.GetBizDataByWhere(this.Context, "fid=@fid", new List<SqlParam> { new SqlParam("@fid", System.Data.DbType.String, fid) }).FirstOrDefault();

                    // 确认已收
                    fconfirmedamount = Convert.ToDecimal(orderdata?["freceivable"]).ToString("f2");
                    //收款待确认
                    freceivabletobeconfirmed = Convert.ToDecimal(orderdata?["freceivabletobeconfirmed"]).ToString("f2");
                    // 待结算金额
                    funsettleamount = this.Container.GetService<IOrderService>()
                        .GetUnsettleAmount(this.Context, orderdata);

                    //获取货款金额
                    loanAmount = GetLoanAmount(fid);
                }
                else if (fsourceformid == "ydj_saleintention")
                {
                    // 直接根据唯一标识获取数据
                    var orderForm = this.MetaModelService.LoadFormModel(this.Context, fsourceformid);
                    var saledata = orderForm.GetBizDataByWhere(this.Context, "fid=@fid", new List<SqlParam> { new SqlParam("@fid", System.Data.DbType.String, fid) }).FirstOrDefault();
                    fconfirmedamount = Convert.ToString(saledata?["fconfirmamount"]);
                    freceivabletobeconfirmed = Convert.ToString(saledata?["freceivedamount"]);
                    funsettleamount = Convert.ToDecimal(saledata?["freceiptamount"]);
                }
            }

            resp.Data.Id = JNConvert.ToStringAndTrim(data["id"]);
            resp.Data.BillNo = JNConvert.ToStringAndTrim(data["fbillno"]);
            resp.Data.Way = JNConvert.ToStringAndTrim(data["fway"]);
            resp.Data.BizStatus = JNConvert.ToStringAndTrim(data["fbizstatus"]);
            resp.Data.AuditStatus = JNConvert.ToStringAndTrim(data["fstatus"]) == "E";
            resp.Data.Customer = JNConvert.ToStringAndTrim(data["fcustomerid"]);
            resp.Data.Mybankid = JNConvert.ToStringAndTrim(data["fmybankid"]);
            var customerObj = data["fcustomerid_ref"] as DynamicObject;
            resp.Data.Phone = JNConvert.ToStringAndTrim(customerObj?["fphone"]);
            resp.Data.Amount = Convert.ToDecimal(data["famount"]);
            resp.Data.ConfirmedAmount = fconfirmedamount;
            resp.Data.ReceivableToBeConfirmed = Convert.ToDecimal(freceivabletobeconfirmed) - resp.Data.Amount;
            if (Convert.ToString(data["fdirection"]).Equals("direction_01")&& Convert.ToString(data["fbizdirection"]).Equals("bizdirection_02"))
            {
                resp.Data.UnsettleAmount = funsettleamount;
            }
            else
            {
                resp.Data.UnsettleAmount = funsettleamount + resp.Data.Amount;
            }
            resp.Data.Creator = JNConvert.ToStringAndTrim(data["fcreatorid"]);
            resp.Data.Date = Convert.ToDateTime(data["fdate"]);
            resp.Data.Purpose = JNConvert.ToStringAndTrim(data["fpurpose"]);
            resp.Data.PaymentDesc = JNConvert.ToStringAndTrim(data["paymentdesc"]);
            resp.Data.Images = ImageFieldUtil.ParseImages(JNConvert.ToStringAndTrim(data["fimage"]), JNConvert.ToStringAndTrim(data["fimage_txt"]));
            resp.Data.Description = JNConvert.ToStringAndTrim(data["fdescription"]);
            resp.Data.Deptid = JNConvert.ToStringAndTrim(data["fdeptid"]);
            //40936 客户充值默认携带业务员，允许业务员修改-后端开发
            resp.Data.Staffid = JNConvert.ToStringAndTrim(data["fstaffid"]);
            //收款小票号
            resp.Data.ReceiptNo = Convert.ToString(data["freceiptno"]);
            resp.Data.cusacount = Convert.ToString(data["fcusacount"]);
            // 获取合同单结算的辅助资料和简单下拉框
            resp.Data.ComboData = this.MetaModelService.LoadFormModel(this.Context, "ydj_ordersettledyn")
                .GetComboDataSource(this.Context, "fway");

            // 临时处理：屏蔽【账户支付】
            HideWay(resp);

            var spService = this.Container.GetService<ISystemProfile>();
            string sysProfileValue = spService.GetProfile(this.Context, "fw", "bas_storesysparam_parameter");
            if (!sysProfileValue.IsNullOrEmptyOrWhiteSpace())
            {
                var storeSysParam = Newtonsoft.Json.Linq.JObject.Parse(sysProfileValue);
                if (storeSysParam != null && storeSysParam["fcustomerunique"] != null)
                {
                    var customerunique = Convert.ToString(storeSysParam["fcustomerunique"]);
                    if (customerunique.IndexOf("store") >= 0)
                    {
                        //来源门店
                        resp.Data.CustomerSrcStoreId = JNConvert.ToStringAndTrim(customerObj["fsrcstoreid"]);
                    }
                }
            }

            #region 销售员
            var dutyObjs = data["fdutyentry"] as DynamicObjectCollection;
            if (dutyObjs != null)
            {
                foreach (var dutyObj in dutyObjs)
                {
                    var obj = dutyObj["fdutyid_ref"] as DynamicObject;
                    var dept = dutyObj["fdeptid_ed_ref"] as DynamicObject;
                    resp.Data.JoinStaffs.Add(new OrderJoinStaffModel
                    {
                        Id = JNConvert.ToStringAndTrim(dutyObj["id"]),
                        Duty = new BaseDataSimpleModel
                        {
                            Id = JNConvert.ToStringAndTrim(dutyObj["fdutyid"]),
                            Name = JNConvert.ToStringAndTrim(obj?["fname"]),
                            Number = JNConvert.ToStringAndTrim(obj?["fnumber"]),
                        },
                        Ratio = Convert.ToDecimal(dutyObj["fratio"]),
                        Amount = Convert.ToDecimal(dutyObj["famount_ed"]),
                        Description = JNConvert.ToStringAndTrim(dutyObj["fdescription_ed"]),
                        IsMain = Convert.ToBoolean(dutyObj["fismain"]),
                        DeptPerfRatio = Convert.ToDecimal(dutyObj["fdeptperfratio"]),
                        Dept = new BaseDataSimpleModel()
                        {
                            Id = JNConvert.ToStringAndTrim(dutyObj["fdeptid_ed"]),
                            Name = JNConvert.ToStringAndTrim(dept?["fname"]),
                            Number = JNConvert.ToStringAndTrim(dept?["fnumber"]),
                        }
                    });
                }
            }
            #endregion

            resp.Data.LoanAmount = loanAmount;
            // 获取所有银行账号
            resp.Data.ComboData.Merge(this.Context.GetBankDataSource());
            //代收单位
            resp.Data.UnitType = JNConvert.ToStringAndTrim(data["fcontactunittype"]);
            resp.Data.Unitid = JNConvert.ToStringAndTrim(data["fcontactunitid"]);
            resp.Data.CollectionUnit = this.Context.CollectionUnitList();
            resp.Message = "操作成功！";
            resp.Success = true;
            return resp;
        }

        /// <summary>
        /// 获取货款金额
        /// </summary>
        /// <returns></returns>
        private decimal GetLoanAmount(string id)
        {
            //根据唯一标识获取数据
            var form = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            DynamicObject customerObj = form.GetBizDataById(this.Context, id, true);

            if (customerObj == null)
            {
                return 0;
            }

            var customerId = Convert.ToString(customerObj["fcustomerid"]);
            string strSql = string.Format($@"select fbalance_e from t_ydj_CustomerAccount t1 with(nolock)
where fpurpose='settleaccount_type_01' and t1.fid='{customerId}'");
            decimal loanAmount = 0;
            using (var dr = this.DBService.ExecuteReader(this.Context, strSql))
            {
                if (dr.Read())
                {
                    loanAmount = Convert.ToDecimal(dr["fbalance_e"]);
                }
            }
            return decimal.Round(loanAmount, 2);
        }

        /// <summary>
        /// 屏蔽【账户支付】
        /// </summary>
        /// <param name="resp"></param>
        private void HideWay(BaseResponse<ReceiptModel> resp)
        {

            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_supplier");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            string where = $@"fmainorgid=@fmainorgid";
            var sqlParam = new SqlParam[]
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company)
            };
            var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);
            var suppliers = dm.SelectBy(dataReader).OfType<DynamicObject>();
            var isDel = false;
            foreach (var supplier in suppliers)
            {
                var synEntrys = supplier["fentry"] as DynamicObjectCollection;
                var existEntry = synEntrys.FirstOrDefault(o => Convert.ToString(o["fpurpose"]).EqualsIgnoreCase("settleaccount_type_01" as string));

                if (existEntry != null && existEntry["fispayment"].ToString() == "False")
                {
                    isDel = true;
                }
            }

            var wayDS = new List<Dictionary<string, object>>();

            foreach (var item in resp.Data.ComboData["fway"])
            {
                if (Convert.ToString(item["id"]).EqualsIgnoreCase("payway_01") && isDel)
                {
                    continue;
                }

                wayDS.Add(item);
            }
            resp.Data.ComboData["fway"] = wayDS;
        }
    }
}
