using JieNor.AMS.YDJ.MP.API.DTO.STE.Order;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.Order
{

    /// <summary>
    /// 微信小程序：合同单反审核接口
    /// </summary>
    public class OrderRejectController : BaseOrderController
    {
        public HtmlForm OrderForm;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(OrderRejectDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }

            //直接根据唯一标识获取数据
            this.OrderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            var orderObj = this.OrderForm.GetBizDataById(this.Context, dto.Id, true);
            if (orderObj == null)
            {
                resp.Success = false;
                resp.Message = $"未找到销售合同！";
                return resp;
            }

            var fentrys = orderObj["fentry"] as DynamicObjectCollection;
            if (!fentrys.IsNullOrEmpty() && fentrys.Count > 0)
            {
                //明细行【转单状态】全部为空或者【审批驳回】才可以反审核
                if (fentrys.Any(x => !Convert.ToString(x["ftransferorderstatus"]).IsNullOrEmptyOrWhiteSpace() && Convert.ToString(x["ftransferorderstatus"]) != "3"))
                {
                    resp.Success = false;
                    resp.Message = $"销售合同【{orderObj["fbillno"]}】商品明细行中存在待审批或已审批通过的转单申请，不允许反审核！";
                    return resp;
                }
            }

            //调用通用反审核方法
            return ApprovalHelper.Reject(this.Context, this.Request, "ydj_order", dto.Id, dto.Reason, dto.Terminate);
        }
    }
}
