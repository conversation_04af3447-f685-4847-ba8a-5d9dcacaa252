using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using System.Collections.Generic;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.Order
{
    /// <summary>
    /// 微信小程序：合同单提交接口
    /// </summary>
    public class OrderSubmitController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(OrderSubmitDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }
            if (!Valid(dto, resp)) return resp;

            return ApprovalHelper.Submit(this.Context, this.Request, "ydj_order", dto.Id);
        }

        private bool Valid(OrderSubmitDTO dto, BaseResponse<BaseDataModel> resp)
        {
            List<string> errorMsgs = null;
            var metaModelService = this.Context.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "ydj_order");
            errorMsgs = BaseOrderController.CheckDeptForbidStatus(this.Context, htmlForm, dto.Id);

            if (!errorMsgs.IsNullOrEmpty())
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", errorMsgs);
                resp.Success = false;
                return false;
            }
            return true;
        }
    }
}