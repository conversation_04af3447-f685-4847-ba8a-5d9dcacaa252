using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MP.API.DTO.STE.Order;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.Order
{
    /// <summary>
    /// 销售合同：商品档案勾选了<允许选配>或<非标产品>校验合同辅助属性和定制说明
    /// </summary>
    public class CheckOrderProductsController : BaseController
    {
        public object Any(CheckOrderProductsDto dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<Dictionary<string, string>>();
            if (dto.Products == null && dto.Products.Count == 0)
            {
                resp.Message = "商品不能为空！";
                resp.Success = false;
                return resp;
            }

            var status = "";
            if (!dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                //直接根据唯一标识获取数据
                var OrderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
                var orderObj = OrderForm.GetBizDataById(this.Context, dto.Id, false);
                if (orderObj == null)
                {
                    resp.Message = "合同单不存在或已被删除！";
                    resp.Success = false;
                    return resp;
                }
                status = Convert.ToString(orderObj["fstatus"]);
            }

            // 向麦浩系统发送请求
            DynamicDTOResponse response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ydj_order",
                    OperationNo = "CheckProducts",
                    Option = new Dictionary<string, object>().AddMSAPIDefaultParams(),
                    Id = dto.Id,
                    SimpleData = new Dictionary<string, string>
                    {
                        { "status",status },
                        { "entry", BuildProduct(dto) }
                    }
                });
            var result = response?.OperationResult;
            if (result.IsSuccess)
            {
                resp.Success = true;
                resp.Data = new Dictionary<string, string> { { "msg", result.SrvData?.ToString() } };
            }
            return resp;
        }

        private string BuildProduct(CheckOrderProductsDto dto)
        {
            var product = new List<SimpleProducts>();
            var index = 0;
            foreach (var entry in dto.Products)
            {
                index++;
                product.Add(new SimpleProducts
                {
                    fproductid = entry.Id,
                    fseq = index,
                    fattrinfo = entry.AttrinfoName,
                    fcustomdesc = entry.CustomDesc,
                });
            }
            return JsonConvert.SerializeObject(product);
        }

        public class SimpleProducts
        {
            public string fproductid { get; set; }
            public int fseq { get; set; }
            public string fattrinfo { get; set; }
            public string fcustomdesc { get; set; }
        }
    }
}
