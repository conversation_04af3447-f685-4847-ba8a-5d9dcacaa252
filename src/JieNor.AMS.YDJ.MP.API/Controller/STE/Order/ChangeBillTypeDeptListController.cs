using System;
using System.Collections.Generic;
using System.Text;
using JieNor.Framework;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.Interface.QueryBuilder;
using System.Linq;
using JieNor.Framework.Interface;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.Order
{
    /// <summary>
    /// 微信小程序：合同切换单据类型 返回对应部门（之前部门是走的缓存，但是新需求要根据单据类型来联动所以需要走接口）
    /// </summary>
    public class ChangeBillTypeDeptListController : BaseController
    {
        public HtmlForm OrderForm;
        protected HtmlForm ProductForm { get; set; }
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ChangeBillTypeDeptListDTO dto)
        {
            base.InitializeOperationContext(dto);
            UserContext userCtx = this.Context;

            var resp = new BaseResponse<BaseListPageData<ChangeBillTypeDeptListModel>>
            {
                Data = new BaseListPageData<ChangeBillTypeDeptListModel>(dto.PageSize)
            };
            var dataSource = new Dictionary<string, List<Dictionary<string, object>>>();
            //参数对象
            SqlBuilderParameter param = new SqlBuilderParameter(userCtx, "ydj_dept");
            param.ReadDirty = true;
            param.NoColorSetting = true;
            param.SrcFormId = dto.SrcFormId;
            param.SrcFldId = dto.SrcFidId.IsNullOrEmptyOrWhiteSpace() ? "fdeptid" : dto.SrcFidId;
            //当前要查询的字段列表
            var fieldKeys = new string[] { "id", "fnumber", "fname", "fsalecategories" };
            foreach (var fieldKey in fieldKeys)
            {
                var field = param.HtmlForm.GetField(fieldKey);
                var columnList = field.ToListColumn(userCtx);
                foreach (var column in columnList)
                {
                    param.SelectedFieldKeys.Add(column.Id);
                }
            }

            //列表构建器
            var listBuilder = userCtx.Container.GetService<IListSqlBuilder>();

            //设置数据隔离方案的过滤条件
            var accessFilter = listBuilder.GetListAccessControlFilter(userCtx, param.HtmlForm.Id);
            param.SetFilter(accessFilter);

            //查询对象
            var queryObj = listBuilder.GetQueryObject(userCtx, param);

            //获取分页数据
            var listDatas = listBuilder.GetQueryData(userCtx, param, queryObj);

            dataSource.Add("fdept", listDatas.Select(s => new Dictionary<string, object>
            {
                { "id",  s["fbillhead_id"] },
                { "number",  s["fnumber"] },
                { "name",  s["fname"] },
                { "salecategories", JNConvert.ToStringAndTrim(s["fsalecategories"]).Split(new string[]{","}, StringSplitOptions.RemoveEmptyEntries) }
            }).ToList());
            List<Dictionary<string, object>> deptObj = null;
            dataSource.TryGetValue("fdept", out deptObj);

            deptObj = FilterDept(userCtx, deptObj,dto.billtype.Name);

            resp.Message = "操作成功！";
            resp.Success = true;
            //resp.Data.TotalRecord = GetTotalRecord(dto, ResultBrandFilter);
            var models = new List<ChangeBillTypeDeptListModel>();
            foreach (var item in deptObj)
            {
                models.Add(new ChangeBillTypeDeptListModel
                {
                    Id = JNConvert.ToStringAndTrim(item["id"]),
                    Number = JNConvert.ToStringAndTrim(item["number"]),
                    Name = JNConvert.ToStringAndTrim(item["name"])
                });
            }
            resp.Data.List = models;

            return resp;
        }

        //给部门列表添加过滤。
        private List<Dictionary<string, object>> FilterDept(UserContext userCtx, List<Dictionary<string, object>> deptObj,string billtype)
        {
            List<string> deptList = deptObj.Select(o => Convert.ToString(o["id"])).ToList<string>();
           
            string sql = $@"  SELECT d.fid AS fdeptid  FROM t_bd_department d with(nolock) 
                             LEFT join t_bas_store c with(nolock) on d.fstore=c.fid /*fstoreid 为门店的编码*/
                             WHERE d.fid in ('{string.Join("','", deptList)}') AND c.fisnewchannel =1  ";
            //if (billtype == "大客户销售合同")
            //{
            //    //大客户添加慕思经典门店匹配
            //    sql += $@" or exists(SELECT 1 FROM t_ydj_productauth t1 with (nolock) 
            //                INNER JOIN t_ydj_productauthbs t2 with(nolock) ON t1.fid = t2.fid
            //                inner join t_ydj_series t3 with(nolock) on CHARINDEX(t3.fid, t2.fserieid) > 0
            //                where d.fstore = t1.forgid and CHARINDEX('慕思经典', t3.fname) > 0) ";
            //    //勾选了新零售的门店对应的部门
            //    List<string> result = userCtx.Container.GetService<IDBService>().ExecuteDynamicObject(userCtx, sql).Select(o => Convert.ToString(o["fdeptid"])).ToList();
            //    var InterList = result.Intersect(deptList).ToList();
            //    //取交集
            //    deptObj = deptObj.Where(o => InterList.Contains(o["id"].ToString())).ToList<Dictionary<string, object>>();
            //    //model.ComboData["fdept"] = deptObj;
            //}
            //else 
            if (billtype != "大客户销售合同" && billtype != "期初销售合同")
            {
                //勾选了新零售的门店对应的部门
                List<string> result = userCtx.Container.GetService<IDBService>().ExecuteDynamicObject(userCtx, sql).Select(o => Convert.ToString(o["fdeptid"])).ToList();
                //取差集 deptList存在 但是result不存在的 即取没勾选新零售的门店对应的部门 或者没有关联门店的部门
                var ExceptList = deptList.Except(result).ToList();

                deptObj = deptObj.Where(o => ExceptList.Contains(o["id"].ToString())).ToList<Dictionary<string, object>>();
                //model.ComboData["fdept"] = deptObj;
            }
            return deptObj;
        }

    }
}