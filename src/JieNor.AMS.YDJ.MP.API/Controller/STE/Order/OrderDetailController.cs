using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.Order
{
    /// <summary>
    /// 微信小程序：合同单详情取数接口
    /// </summary>
    public class OrderDetailController : BaseController
    {
        public HtmlForm OrderForm;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(OrderDetailDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<OrderDetailModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }

            //直接根据唯一标识获取数据
            this.OrderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            var orderObj = this.OrderForm.GetBizDataById(this.Context, dto.Id, true);

            if (orderObj == null)
            {
                resp.Message = "合同单不存在或已被删除！";
                resp.Success = false;
                return resp;
            }

            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Data = MapTo(orderObj);

            #region 判断商品能否编辑零售价
            var isresellorder = Convert.ToBoolean(orderObj["fisresellorder"]);
            //是自建商品，则获取经销商参数【自建商品零售价可编辑】判断
            var profileService = this.Context.Container.GetService<ISystemProfile>();
            var isorderusable = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fisorderusable", false);//参数【二级分销合同零售价可编辑】
            if (isresellorder && isorderusable)
            {
                foreach (var item in resp.Data.Products)
                {
                    item.IsEditPrice = true;
                }
            }
            else
            {
                var owneditprice = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fowneditprice", false);//参数【自建商品零售价可编辑】

                var productIds = resp.Data.Products.Select(x => x.ProductId);
                var sql = $@"select t1.fid,t1.fnumber,t1.fmainorgid,t2.feditprice from t_bd_material t1 with(nolock)
                        left join ser_ydj_category t2 with(nolock) on t1.fcategoryid=t2.fid
                        where t1.fid in ({productIds.JoinEx(",", true)})";
                var dbService = this.Context.Container.GetService<IDBService>();
                var productinfo = dbService.ExecuteDynamicObject(this.Context, sql);

                foreach (var item in resp.Data.Products)
                {
                    var result = productinfo.Where(x => Convert.ToString(x["fid"]) == item.ProductId).FirstOrDefault();
                    var fmainorgid = Convert.ToString(result["fmainorgid"]);//商品数据来源
                    //判断商品是自建商品还是总部商品
                    if (fmainorgid == this.Context.Company)
                    {
                        if (owneditprice) item.IsEditPrice = true;

                    }
                    else
                    {
                        //是总部商品，则判断商品.商品类别.零售价可编辑=“是”
                        if (Convert.ToString(result["feditprice"]) == "1")
                        {
                            item.IsEditPrice = true;
                        }
                    }
                }
            }
            #endregion

            return resp;
        }

        /// <summary>
        /// 获取指定的基础资料名称字段值
        /// </summary>
        /// <param name="formId"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        private string GetBillTypeName(string billTypeId)
        {
            if (billTypeId.IsNullOrEmptyOrWhiteSpace()) return "";

            var svc = Context.Container.GetService<IBillTypeService>();
            var dynObj = svc.GetBillTypeInfor(Context, billTypeId);

            //var dynObj = this.Context.LoadBizBillHeadDataById(formId, id, fieldName);

            if (dynObj != null)
            {
                return dynObj.fname;
            }

            return "";
        }

        private OrderDetailModel MapTo(DynamicObject orderObj)
        {
            var statusObj = orderObj["fstatus_ref"] as DynamicObject;
            var staffObj = orderObj["fstaffid_ref"] as DynamicObject;
            var designerObj = orderObj["fstylistid_ref"] as DynamicObject;
            var channelObj = orderObj["fchannel_ref"] as DynamicObject;
            var deptObj = orderObj["fdeptid_ref"] as DynamicObject;
            var storeObj = orderObj["fstore_ref"] as DynamicObject;
            var customerObj = orderObj["fcustomerid_ref"] as DynamicObject;
            var secCustomerObj = orderObj["fterminalcustomer_ref"] as DynamicObject;
            var receiptStatusObj = orderObj["freceiptstatus_ref"] as DynamicObject;
            var buildingObj = orderObj["fbuildingid_ref"] as DynamicObject;
            var activityObj = orderObj["factivityid_ref"] as DynamicObject;
            var customersourceObj = orderObj["fcustomersource_ref"] as DynamicObject;
            OrderDetailModel model = new OrderDetailModel
            {
                Id = JNConvert.ToStringAndTrim(orderObj["id"]),
                Number = JNConvert.ToStringAndTrim(orderObj["fbillno"]),
                CreateDate = Convert.ToDateTime(orderObj["fcreatedate"]),
                DeliveryDate = Convert.ToDateTime(orderObj["fdeliverydate"]),
                OrderDate = Convert.ToDateTime(orderObj["forderdate"]),
                LogisticsItems = JNConvert.ToStringAndTrim(orderObj["flogisticsitems"]),
                Description = JNConvert.ToStringAndTrim(orderObj["fdescription"]),

                SumAmount = Convert.ToDecimal(orderObj["fsumamount"]),
                Sumreceivable = Convert.ToDecimal(orderObj["fsumreceivable"]),
                DealAmount = Convert.ToDecimal(orderObj["fdealamount"]),
                ReceivableAmount = Convert.ToDecimal(orderObj["freceivable"]),
                UnreceivedAmount = Convert.ToDecimal(orderObj["funreceived"]),
                DistSumRate = Convert.ToDecimal(orderObj["fdistsumrate"]),
                NoPromotionDistRate = Convert.ToDecimal(orderObj["fnopromotiondistrate"]),
                DistAmount = Convert.ToDecimal(orderObj["fdistamount"]),
                FaceAmount = Convert.ToDecimal(orderObj["ffaceamount"]),
                ProductCost = Convert.ToDecimal(orderObj["fproductcost"]),
                ReceivableToBeConfirmed = Convert.ToDecimal(orderObj["freceivabletobeconfirmed"]),
                Within = JNConvert.ToStringAndTrim(orderObj["fwithin"]),
                SumSellAmount = Convert.ToDecimal(orderObj["fsumsellamount"]),
                NeedTransferOrder = Convert.ToBoolean(orderObj["fneedtransferorder"]),
                //是否销售转单
                IsSaletransferorder = Convert.ToBoolean(orderObj["fissaletransferorder"]),
                IsSecOrder = Convert.ToBoolean(orderObj["fisresellorder"]),
                RenewalFlag = Convert.ToBoolean(orderObj["frenewalflag"]),
                IszbRefund = Convert.ToBoolean(orderObj["fiszbrefund"]),
                SettlProgress = new ComboDataModel
                {
                    Id = Convert.ToString(orderObj["fsettlprogress"]),
                    Name = GetSettlProgressName(Convert.ToString(orderObj["fsettlprogress"]))
                },
                CloseState = new ComboDataModel
                {
                    Id = Convert.ToString(orderObj["fclosestate"]),
                    Name = Convert.ToString(orderObj["fclosestate"]) == "1" ? "已关闭" : "未关闭"
                },
                BillTypeId = orderObj["fbilltype"] as string,
                BillTypeName = GetBillTypeName(orderObj["fbilltype"] as string),
                BillTypeNo = JNConvert.ToStringAndTrim(orderObj["FFormId"]),
                FmallOrderno = JNConvert.ToStringAndTrim(orderObj["fmallorderno"]),
                PaymentRatios = Convert.ToDecimal(orderObj["fpaymentratios"]),
                Actrefundamount = Convert.ToDecimal(orderObj["factrefundamount"]),
                CustomerSource = new ComboDataModel(customersourceObj)
            };
            model.Activity = new BaseDataSimpleModel(activityObj);
            model.Building = new BaseDataSimpleModel(buildingObj);
            model.Status = new ComboDataModel(statusObj);
            model.AuditStatus = Convert.ToString(statusObj?["fenumitem"]) == "E";
            model.Staff = new StaffSimpleModel(staffObj);
            model.Dept = new BaseDataSimpleModel(deptObj);
            model.Store = new BaseDataSimpleModel(storeObj);
            model.Designer = new StaffListModel(designerObj);
            model.Channel = new BaseDataSimpleModel(channelObj);
            if (model.IsSecOrder)
            {
                model.MemberNo = JNConvert.ToStringAndTrim(secCustomerObj?["fmemberno"]);
            }
            else
            {
                model.MemberNo = JNConvert.ToStringAndTrim(customerObj?["fmemberno"]);
            }
            model.ReceiptStatus = new ComboDataModel(receiptStatusObj);
            model.CancelStatus = Convert.ToBoolean(orderObj["fcancelstatus"]);
            model.ReserveBill = ReserveBillHelper.GetSourceReserveBill(this.Context, this.OrderForm, model.Id);
            model.Customer = new CustomerSimpleModel(customerObj);
            model.TerminalCustomer = new CustomerSimpleModel(secCustomerObj);
            model.Stricttrack = Convert.ToBoolean(orderObj["fstricttrack"]);
            model.Changestatus = Convert.ToInt32(orderObj["fchangestatus"]);
            model.MemberDesc = JNConvert.ToStringAndTrim(orderObj["fmemberdesc"]);//未注册会员原因
            #region 商品明细
            var productObjs = orderObj["fentry"] as DynamicObjectCollection;
            // 加载引用数据
            var refMg = this.Container.GetService<LoadReferenceObjectManager>();
            var productForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
            refMg.Load(this.Context, productForm.GetDynamicObjectType(this.Context), productObjs?.Select(s => s["fproductid_ref"] as DynamicObject), false);
            model.Products = OrderProductModel.Init(productObjs, this.OrderForm, this.Context);

            //加载辅助属性信息
            var auxPropValIds = model.Products.Select(o => o.AuxPropValId).ToList();
            var auxPropValsKv = ProductUtil.PackAuxPropFieldValue(this.Context, auxPropValIds);
            foreach (var item in model.Products)
            {
                item.AuxPropVals = auxPropValsKv.GetValue(item.AuxPropValId) ?? new List<Dictionary<string, string>>();
            }

            #endregion

            #region 销售员

            var dutyObjs = orderObj["fdutyentry"] as DynamicObjectCollection;
            if (dutyObjs != null)
            {
                foreach (var dutyObj in dutyObjs)
                {
                    var obj = dutyObj["fdutyid_ref"] as DynamicObject;
                    var deptDy = dutyObj["fdeptid_ref"] as DynamicObject;
                    model.JoinStaffs.Add(new OrderJoinStaffModel
                    {
                        Id = JNConvert.ToStringAndTrim(dutyObj["id"]),
                        Duty = new BaseDataSimpleModel
                        {
                            Id = JNConvert.ToStringAndTrim(dutyObj["fdutyid"]),
                            Name = JNConvert.ToStringAndTrim(obj?["fname"]),
                            Number = JNConvert.ToStringAndTrim(obj?["fnumber"]),
                        },
                        Ratio = Convert.ToDecimal(dutyObj["fratio"]),
                        Amount = Convert.ToDecimal(dutyObj["famount"]),
                        Description = JNConvert.ToStringAndTrim(dutyObj["fdescription"]),
                        IsMain = Convert.ToBoolean(dutyObj["fismain"]),
                        DeptPerfRatio = Convert.ToDecimal(dutyObj["fdeptperfratio"]),
                        Dept = new BaseDataSimpleModel()
                        {
                            Id = JNConvert.ToStringAndTrim(deptDy["id"]),
                            Name = JNConvert.ToStringAndTrim(deptDy["fname"]),
                            Number = JNConvert.ToStringAndTrim(deptDy["fnumber"])
                        }
                    });
                }
            }

            #endregion

            #region 费用项目

            // 费用收入
            var expenseentryObjs = orderObj["fexpenseentry"] as DynamicObjectCollection;
            decimal expenseAmount = 0;
            foreach (var expenseentryObj in expenseentryObjs)
            {
                expenseAmount += Convert.ToDecimal(expenseentryObj?["famount"]);
            }
            model.ExpenseAmount = expenseAmount;

            // 费用支出
            var disburseentryObjs = orderObj["fdisburseentry"] as DynamicObjectCollection;
            decimal disburseAmount = 0;
            foreach (var disburseentryObj in disburseentryObjs)
            {
                disburseAmount += Convert.ToDecimal(disburseentryObj?["famount"]);
            }
            model.DisburseAmount = disburseAmount;

            #endregion

            #region 合同附件

            model.Attachments = GetAttachmentList(orderObj);

            var image = Convert.ToString(orderObj["fimage"]);
            var imageTxt = Convert.ToString(orderObj["fimage_txt"]);
            var images = ImageFieldUtil.ParseImages(image, imageTxt, true);
            model.ImageList = images;
            #endregion

            #region 开票信息

            SetInvoiceInfo(orderObj, model);

            #endregion

            #region 设置Sap信息

            SetSapInfo(orderObj, model);

            #endregion

            //根据单据类型处理销售部门默认值（创新新渠道逻辑）
            //DealDeptByBillType(this.Context, model);
            model.piecesendtag = Convert.ToBoolean(orderObj["fpiecesendtag"]);
            //model.relevanceorderno = Convert.ToString(orderObj["frelevanceorderno"]);

            model.relevanceorderno = new ComboDataModel
            {
                Id = Convert.ToString(orderObj["frelevanceorderno"]),
                Name = this.Context.LoadBizDataById("ydj_order", Convert.ToString(orderObj["frelevanceorderno"]))?["fbillno"]?.ToString()
            };

            return model;
        }

        private static void DealDeptByBillType(UserContext userCtx, OrderDetailModel model)
        {
            var billtype = model.BillTypeName;
            var dept = model.Dept.Id;
            //部门对应门店是否创新渠道
            bool Ischennal = IsChannelDept(userCtx, dept, billtype);
            //if (billtype == "大客户销售合同")
            //{
            //    if (!Ischennal)
            //    {
            //        model.Dept = new BaseDataSimpleModel
            //        {
            //            Id = "",
            //            Name = "",
            //            Number = ""
            //        };
            //    }
            //}
            //else 
            if (billtype != "大客户销售合同" && billtype != "期初销售合同")
            {
                if (Ischennal)
                {
                    model.Dept = new BaseDataSimpleModel
                    {
                        Id = "",
                        Name = "",
                        Number = ""
                    };
                }
            }
        }
        /// <summary>
        /// 判断部门对应门店是否勾选创新渠道标记
        /// </summary>
        /// <param name="fdeptid"></param>
        /// <returns></returns>
        private static bool IsChannelDept(UserContext userCtx, string fdeptid, string billtype)
        {
            if (!fdeptid.IsNullOrEmptyOrWhiteSpace())
            {
                var sql = $@" SELECT 1 FROM t_bd_department with(nolock)
                            WHERE EXISTS (SELECT 1 FROM t_bas_store c with(nolock) WHERE fstore=c.fid AND ISNULL(fisnewchannel,0) =1) AND fid = '{fdeptid}'";
                var count = userCtx.Container.GetService<IDBService>().ExecuteDynamicObject(userCtx, sql).Count;
                //跟pc保持一致 3640，单据类型="大客户订单"时, 门店如果是经典的就不用清空
                if (count > 0)
                {
                    return true;
                }
                else if (billtype == "大客户销售合同")
                {
                    //判断部门是不是慕思经典的店门
                    sql = $@"select 1 from  t_bd_department with(nolock) where fid = '{fdeptid}' and exists(SELECT 1 FROM t_ydj_productauth t1 with (nolock) 
                            INNER JOIN t_ydj_productauthbs t2 with(nolock) ON t1.fid = t2.fid
                            inner join t_ydj_series t3 with(nolock) on CHARINDEX(t3.fid,t2.fserieid) > 0
                            where fstore = t1.forgid and CHARINDEX('慕思经典', t3.fname) > 0 AND t1.fforbidstatus = 0) ";
                    return userCtx.Container.GetService<IDBService>().ExecuteDynamicObject(userCtx, sql).Count > 0;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        private List<OrderAttachmentModel> GetAttachmentList(DynamicObject orderObj)
        {
            var attachments = new List<OrderAttachmentModel>();

            var attachFormMeta = this.MetaModelService.LoadFormModel(this.Context, "bd_attachlist");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, attachFormMeta.GetDynamicObjectType(this.Context));
            var pkIdReader = this.Context.GetPkIdDataReader(attachFormMeta, "flinkformid=@linkFormId and flinkbillinterid=@linkBillId", new SqlParam[]
            {
                new SqlParam("linkFormId", System.Data.DbType.String, this.OrderForm.Id),
                new SqlParam("linkBillId", System.Data.DbType.String, orderObj["id"]),
            });

            var linkAttachBillObj = dm.SelectBy(pkIdReader)
                .OfType<DynamicObject>()
                .FirstOrDefault();
            if (linkAttachBillObj == null) return attachments;

            var entrys = (DynamicObjectCollection)linkAttachBillObj["fdrawentity"];
            foreach (var entry in entrys)
            {
                attachments.Add(new OrderAttachmentModel
                {
                    Id = Convert.ToString(entry["id"]),
                    Image = ImageFieldUtil.ParseImage(Convert.ToString(entry["ffileid"]), Convert.ToString(entry["ffilename"]), true)
                });
            }

            return attachments;
        }

        /// <summary>
        /// 结算进度
        /// '0':'待发起','1':'收款中','2':'已收款','3':'已退款'
        /// </summary>
        /// <param name="settlprogress"></param>
        /// <returns></returns>
        private string GetSettlProgressName(string settlprogress)
        {
            var name = string.Empty;
            switch (settlprogress)
            {
                case "0":
                    name = "待发起";
                    break;
                case "1":
                    name = "收款中";
                    break;
                case "2":
                    name = "已收款";
                    break;
                case "3":
                    name = "已退款";
                    break;
                default:
                    break;
            }
            return name;
        }

        /// <summary>
        /// 设置开票信息
        /// </summary>
        /// <param name="orderObj"></param>
        /// <param name="resp"></param>
        private static void SetInvoiceInfo(DynamicObject orderObj, OrderDetailModel model)
        {
            model.IsInvoiceNeed = Convert.ToBoolean(Convert.ToInt32(orderObj["fisinvoiceneed"]));
            if (model.InvoiceInfo == null)
            {
                model.InvoiceInfo = new InvoiceEntry();
            }

            model.InvoiceInfo.InvoiceType = JNConvert.ToStringAndTrim(orderObj["finvoicetype"]);
            model.InvoiceInfo.BuyerFullName = JNConvert.ToStringAndTrim(orderObj["fbuyerfullname"]);
            model.InvoiceInfo.TaxpayerIdentify = JNConvert.ToStringAndTrim(orderObj["ftaxpayeridentify"]);
            model.InvoiceInfo.InvoiceEmail = JNConvert.ToStringAndTrim(orderObj["finvoiceemail"]);
            model.InvoiceInfo.InvoiceAddress = JNConvert.ToStringAndTrim(orderObj["finvoiceaddress"]);
            model.InvoiceInfo.InvoicePhone = JNConvert.ToStringAndTrim(orderObj["finvoicephone"]);
            model.InvoiceInfo.DepositBankName = JNConvert.ToStringAndTrim(orderObj["fdepositbankname"]);
            model.InvoiceInfo.BankAccount = JNConvert.ToStringAndTrim(orderObj["fbankaccount"]);
        }

        private static void SetSapInfo(DynamicObject orderObj, OrderDetailModel model)
        {
            //提交SAP时间
            var fsubmithtime = Convert.ToDateTime(orderObj["fsubmithtime"]);

            if (fsubmithtime != null && fsubmithtime != default(DateTime))
            {
                model.SubmitHeadTime = fsubmithtime;
            }
            else
            {
                model.SubmitHeadTime = null;
            }

            //协同SAP状态
            model.SapStatus = JNConvert.ToStringAndTrim(orderObj["fchstatus"]);

            //SAP合同类型
            model.HeadContractType = JNConvert.ToStringAndTrim(orderObj["fheadcontracttype"]);

            //SAP合同号
            model.HeadQuartNo = JNConvert.ToStringAndTrim(orderObj["fheadquartno"]);

            //SAP终审时间
            var fheadquartfrtime = Convert.ToDateTime(orderObj["fheadquartfrtime"]);

            if (fheadquartfrtime != null && fheadquartfrtime != default(DateTime))
            {
                model.SapFinalTime = fheadquartfrtime;
            }
            else
            {
                model.SapFinalTime = null;
            }

            model.HeadQuartSyncMessage = JNConvert.ToStringAndTrim(orderObj["fheadquartsyncmessage"]);
        }
    }
}