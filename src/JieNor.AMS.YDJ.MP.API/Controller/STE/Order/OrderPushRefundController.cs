using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Model.BAS.CollectingUnit;
using JieNor.AMS.YDJ.MP.API.Response.YDJ.STE.Order.LoadSettleInfo;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using DynamicObject = JieNor.Framework.SuperOrm.DataEntity.DynamicObject;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.Order
{
    /// <summary>
    /// 微信小程序：合同单退款取数接口
    /// </summary>
    public class OrderPushRefundController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(OrderPushRefundDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<OrderPushRefundModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "合同尚未签订！";
                resp.Success = false;
                return resp;
            }

            // 直接根据唯一标识获取数据
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            var data = htmlForm.GetBizDataById(this.Context, dto.Id, true);

            // 如果合同没有签订，提示：合同尚未签订。
            if (data == null)
            {
                resp.Message = "合同尚未签订！";
                resp.Success = false;
                return resp;
            }

            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, CommonBillDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ydj_order",
                    OperationNo = "LoadSettleInfo",
                    SelectedRows = new List<SelectedRow> { new SelectedRow { PkValue = dto.Id } },
                    PageId = Guid.NewGuid().ToString("N"),
                    SimpleData = new Dictionary<string, string>
                    {
                        { "settleType", "refund" }
                    }
                });
            var result = response?.OperationResult;
            resp = result.ToResponseModel<OrderPushRefundModel>(false);
            if (!resp.Success)
            {
                return resp;
            }

            var srvData = result?.SrvData as string;

            if (srvData.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = "下推退款单失败！";
                return resp;
            }

            LoadSettleInfoResponse loadSettleInfoResponse =
                JsonConvert.DeserializeObject<LoadSettleInfoResponse>(srvData);

            Push(resp.Data, loadSettleInfoResponse);

            // 获取合同单结算的辅助资料和简单下拉框
            resp.Data.ComboData = this.MetaModelService.LoadFormModel(this.Context, "ydj_ordersettledyn")
                .GetComboDataSource(this.Context, "fway");

            // 临时处理：屏蔽【账户支付】
            HideWay(resp);

            //销售员比例
            var staffObj = data["fstaffid_ref"] as DynamicObject;
            var deptObj = data["fdeptid_ref"] as DynamicObject;
            resp.Data.JoinStaffs.Add(new OrderJoinStaffModel
            {
                Id = "",
                Duty = new BaseDataSimpleModel
                {
                    Id = JNConvert.ToStringAndTrim(staffObj?["id"]),
                    Name = JNConvert.ToStringAndTrim(staffObj?["fname"]),
                    Number = JNConvert.ToStringAndTrim(staffObj?["fnumber"]),
                },
                Ratio = 100,
                Amount = Convert.ToDecimal(data["fsumamount"]),
                IsMain = true,
                Dept = new BaseDataSimpleModel
                {
                    Id = JNConvert.ToStringAndTrim(deptObj?["id"]),
                    Name = JNConvert.ToStringAndTrim(deptObj?["fname"]),
                    Number = JNConvert.ToStringAndTrim(deptObj?["fnumber"]),
                },
            });

            //获取货款金额
            var loanAmount = GetLoanAmount(dto.Id);
            resp.Data.cusacount = GetCusAccount(dto.Id);
            // 获取所有银行账号
            resp.Data.ComboData.Merge(this.Context.GetBankDataSource());
            // 获取协同方银行账号信息下拉框数据源
            resp.Data.ComboData.Merge(GetSynBankNum(loadSettleInfoResponse));
            // 获取费用项目
            resp.Data.ComboData.Merge(GetExpensenItem());
            //代收单位
            resp.Data.CollectionUnit = this.Context.CollectionUnitList();
            resp.Data.LoanAmount = loanAmount;
            resp.Message = "操作成功！";
            resp.Success = true;

            return resp;
        }

        /// <summary>
        /// 获取默认账户
        /// </summary>
        /// <returns></returns>
        private string GetCusAccount(string id)
        {
            //根据唯一标识获取数据
            var form = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            DynamicObject customerObj = form.GetBizDataById(this.Context, id, true);

            if (customerObj == null)
            {
                return "";
            }
            var customerId = Convert.ToString(customerObj["fcustomerid"]);
            var cusItem = this.Context.LoadBizDataById("ydj_customer", customerId);
            if (cusItem != null) return Convert.ToString(cusItem["fbanknumber"]);
            return "";
        }

        /// <summary>
        /// 获取货款金额
        /// </summary>
        /// <returns></returns>
        private decimal GetLoanAmount(string id)
        {
            //根据唯一标识获取数据
            var form = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            DynamicObject customerObj = form.GetBizDataById(this.Context, id, true);

            if (customerObj == null)
            {
                return 0;
            }

            var customerId = Convert.ToString(customerObj["fcustomerid"]);
            string strSql = string.Format($@"select fbalance_e from t_ydj_CustomerAccount t1 with(nolock)
where fpurpose='settleaccount_type_01' and t1.fid='{customerId}'");
            decimal loanAmount = 0;
            using (var dr = this.DBService.ExecuteReader(this.Context, strSql))
            {
                if (dr.Read())
                {
                    loanAmount = Convert.ToDecimal(dr["fbalance_e"]);
                }
            }
            return decimal.Round(loanAmount, 2);
        }

        /// <summary>
        /// 获取协同银行账号
        /// </summary>
        /// <param name="loadSettleInfoResponse"></param>
        /// <returns></returns>
        private IDictionary<string, List<Dictionary<string, object>>> GetSynBankNum(LoadSettleInfoResponse loadSettleInfoResponse)
        {
            Dictionary<string, List<Dictionary<string, object>>> res = new Dictionary<string, List<Dictionary<string, object>>>();
            var synBankNumDic = new List<Dictionary<string, object>>();
            if (loadSettleInfoResponse.synBankNum != null && loadSettleInfoResponse.synBankNum.Count > 0)
            {
                synBankNumDic = (loadSettleInfoResponse.synBankNum.ConvertAll<Dictionary<string, object>>(o => new Dictionary<string, object> { { "id", o.accountId }, { "name", o.bankNum + " " + o.accountName + " " + o.bankName }, { "accountName", o.accountName }, { "number", o.bankNum }, { "bankName", o.bankName } }));
            }
            res.Add("fsynbank", synBankNumDic);
            return res;
        }

        /// <summary>
        /// 获取费用项目
        /// </summary>
        /// <returns></returns>
        private Dictionary<string, List<Dictionary<string, object>>> GetExpensenItem()
        {
            Dictionary<string, List<Dictionary<string, object>>> dataSource = new Dictionary<string, List<Dictionary<string, object>>>();

            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company)
            };

            // 【费用类型】=费用收入
            string sqlText = $"select fid,fnumber,fname from t_ydj_expenseitem with(nolock) where fmainorgid=@fmainorgid and FForbidStatus='0' and ftype='expensetype_01'";

            var list = this.DBService.ExecuteDynamicObject(this.Context, sqlText, sqlParams);

            dataSource["fexpenseitem"] = list.Select(s => new Dictionary<string, object>
            {
                { "id",  s["fid"] },
                { "number",  s["fnumber"] },
                { "name",  s["fname"] },
                { "disable",  false }
            }).ToList();

            return dataSource;
        }

        /// <summary>
        /// 屏蔽【账户支付】
        /// </summary>
        /// <param name="resp"></param>
        private void HideWay(BaseResponse<OrderPushRefundModel> resp)
        {

            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_supplier");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            string where = $@"fmainorgid=@fmainorgid";
            var sqlParam = new SqlParam[]
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company)
            };
            var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);
            var suppliers = dm.SelectBy(dataReader).OfType<DynamicObject>();
            var isDel = false;
            foreach (var supplier in suppliers)
            {
                var synEntrys = supplier["fentry"] as DynamicObjectCollection;
                var existEntry = synEntrys.FirstOrDefault(o => Convert.ToString(o["fpurpose"]).EqualsIgnoreCase("settleaccount_type_01" as string));

                if (existEntry != null && existEntry["fispayment"].ToString() == "False")
                {
                    isDel = true;
                }
            }

            var wayDS = new List<Dictionary<string, object>>();

            foreach (var item in resp.Data.ComboData["fway"])
            {
                if (Convert.ToString(item["id"]).EqualsIgnoreCase("payway_01") && isDel)
                {
                    continue;
                }

                wayDS.Add(item);
            }
            resp.Data.ComboData["fway"] = wayDS;
        }

        private void Push(OrderPushRefundModel model, LoadSettleInfoResponse resp)
        {
            model.SourceId = resp.fsourceid;
            model.SourceType = resp.fsourceformid;
            model.SourceNumber = resp.fsourcenumber;
            model.DeptId = resp.fdeptid;
            model.SettledAmount = Convert.ToDecimal(resp.fsettledamount);
            model.UnsettleAmount = Convert.ToDecimal(resp.funsettleamount);
            model.ConfirmedAmount = Convert.ToDecimal(resp.fconfirmedamount);
            model.Amount = Convert.ToDecimal(resp.amount);
            model.Customer = new CustomerSimpleModel
            {
                Id = resp.fcustomerid.id,
                Name = resp.fcustomerid.fname,
                Number = resp.fcustomerid.fnumber,
                Phone = resp.fcustomerid.fphone,
            };
            model.IsSyn = resp.fissyn;
            model.RefundToBeConfirmed = Convert.ToDecimal(resp.freceivabletobeconfirmed);//退款待确认
            model.BrokerageBalance = Convert.ToDecimal(resp.fbrokeragebalance);
        }

    }
}