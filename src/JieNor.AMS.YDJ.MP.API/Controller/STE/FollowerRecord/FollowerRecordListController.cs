using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ServiceStack;
using Newtonsoft.Json;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.FollowerRecord
{
    /// <summary>
    /// 微信小程序：跟进记录列表取数接口
    /// </summary>
    public class FollowerRecordListController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(FollowerRecordListDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseListPageData<FollowerRecordListModel>>
            {
                Data = new BaseListPageData<FollowerRecordListModel>(dto.PageSize)
            };

            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("@fsourcetype", System.Data.DbType.String, dto.SourceType),
                new SqlParam("@fsourcenumber", System.Data.DbType.String, dto.SourceNumber),
            };

            string sqlWhere =
                "where fr.fmainorgid=@fmainorgid and ((fr.frelatedbilltype=@fsourcetype and fr.frelatedbillno=@fsourcenumber) or (fr.fsourcetype=@fsourcetype and fr.fsourcenumber=@fsourcenumber))";

            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Data.TotalRecord = GetTotalRecord(sqlWhere, sqlParams);
            resp.Data.List = MapTo(dto, sqlWhere, sqlParams);

            return resp;
        }

        private int GetTotalRecord(string sqlWhere, List<SqlParam> sqlParams)
        {
            //查询总记录数
            var totalRecord = 0;
            string sql = $@"select count(1) as totalRecord from t_ydj_followerrecord fr with(nolock) {sqlWhere}";
            using (var reader = this.DBService.ExecuteReader(this.Context, sql, sqlParams))
            {
                if (reader.Read())
                {
                    totalRecord = Convert.ToInt32(reader["totalRecord"]);
                }
            }

            return totalRecord;
        }

        private List<FollowerRecordListModel> MapTo(FollowerRecordListDTO dto, string sqlWhere, List<SqlParam> sqlParams)
        {
            //默认按跟进日期降序，fid降序
            var orderBy = "fr.ffollowtime desc, fr.fid desc";
            int pageSize = dto.PageSize;
            int pageIndex = dto.PageIndex;

            //查询分页数据
            var sqlText = $@"
            select top {pageSize} * from 
            (
	            select row_number() over(order by {orderBy}) rownum,
                    fr.fid,
                    fr.fbillno,
	                fr.fstaffid as ffollowerid, 
	                fr.ffollowtime, 
	                follower.fname as ffollowername,
	                follower.fmainpositionid as ffollowerpositionid,
	                fr.fdescription,
                    u.fimage as ffollowerimage,
                    u.fphone as ffollowerphone,
                    fr.fobjectid,
                    fr.fobjectno,
                    fr.fobjecttype,
                    v_bd_enum.fenumitem as fobjecttypename,
                    fr.fimage,
                    fr.fimage_txt
                from t_ydj_followerrecord as fr with(nolock) 
                left join t_bd_staff as follower with(nolock) on fr.fstaffid = follower.fid
                left join t_sec_user as u with(nolock) on follower.flinkuserid = u.fid
                left join v_bd_enum with(nolock) on  fr.fobjecttype = v_bd_enum.fid
                {sqlWhere}
            ) p 
            where rownum > {pageSize} * ({pageIndex} - 1)";

            var list = this.DBService.ExecuteDynamicObject(this.Context, sqlText, sqlParams);

            var models = new List<FollowerRecordListModel>();
            foreach (var item in list)
            {
                var model = new FollowerRecordListModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fid"]),
                    FollowTime = Convert.ToDateTime(item["ffollowtime"]),
                    Description = JNConvert.ToStringAndTrim(item["fdescription"]),
                    ObjectId = JNConvert.ToStringAndTrim(item["fobjectid"]),
                    ObjectTypeId = JNConvert.ToStringAndTrim(item["fobjecttype"]),
                    ObjectType = JNConvert.ToStringAndTrim(item["fobjecttypename"]),
                    ObjectNo = JNConvert.ToStringAndTrim(item["fobjectno"]),
                    Number = JNConvert.ToStringAndTrim(item["fbillno"])
                };

                model.Follower = new FollowerModel
                {
                    Id = JNConvert.ToStringAndTrim(item["ffollowerid"]),
                    Name = JNConvert.ToStringAndTrim(item["ffollowername"]),
                    ImageId = JNConvert.ToStringAndTrim(item["ffollowerimage"]),
                    ImageUrl = JNConvert.ToStringAndTrim(item["ffollowerimage"]).GetSignedFileUrl(false),
                    Phone = JNConvert.ToStringAndTrim(item["ffollowerphone"]),
                    Position = JNConvert.ToStringAndTrim(item["ffollowerpositionid"])
                };

                if (model.ObjectTypeId.IsNullOrEmptyOrWhiteSpace())
                {
                    model.ObjectTypeId = "objecttype01";
                    model.ObjectType = "跟进记录";
                }

                if (model.ObjectTypeId == "objecttype01")
                {
                    model.ObjectId = model.Id;
                    model.ObjectNo = model.Number;
                }

                model.AttachmentIds = JNConvert.ToStringAndTrim(item["fimage"]);
                model.AttachmentNames = JNConvert.ToStringAndTrim(item["fimage_txt"]);
                if (!model.AttachmentIds.IsNullOrEmptyOrWhiteSpace())
                {
                    model.AttachmentUrls = string.Join(",", model.AttachmentIds
                        .Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries)
                        .Select(s => s.GetSignedFileUrl(false))
                    );
                }

                models.Add(model);
            }

            return models;
        }
    }
}