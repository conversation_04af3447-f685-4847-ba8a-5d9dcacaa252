using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using System;
using System.Collections.Generic;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using ServiceStack;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.Channel
{
    /// <summary>
    /// 微信小程序：合作渠道列表取数接口
    /// </summary>
    public class ChannelListController : BaseController
    {
        public HtmlForm ChannelForm;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ChannelListDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseListPageData<ChannelListModel>>
            {
                Data = new BaseListPageData<ChannelListModel>(dto.PageSize)
            };

            this.ChannelForm = this.MetaModelService.LoadFormModel(this.Context, "ste_channel");

            SetResponseData(dto, resp);

            return resp;
        }

        /// <summary>
        /// 设置响应数据包
        /// 采用平台标准的列表取数方法，以便和PC端的取数条件保持一致（主要是涉及到数据隔离的问题）
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        private void SetResponseData(ChannelListDTO dto, BaseResponse<BaseListPageData<ChannelListModel>> resp)
        {
            //参数对象
            var param = new SqlBuilderParameter(this.Context, this.ChannelForm);
            param.ReadDirty = true;
            param.NoColorSetting = true;
            param.PageCount = dto.PageSize;
            param.PageIndex = dto.PageIndex;

            string sortBy = dto.Sortby;
            if (sortBy.IsNullOrEmptyOrWhiteSpace())
            {
                sortBy = "fcreatedate";
            }
            param.OrderByString = $"{sortBy} {dto.Sortord}";

            if (dto.Type.IsNullOrEmptyOrWhiteSpace() == false)
            {
                //过滤条件
                param.FilterString = param.FilterString.JoinFilterString(@"ftype = @ftype");

                //条件参数
                param.AddParameter(new SqlParam("@ftype", System.Data.DbType.String, dto.Type));
            }

            if (dto.Keyword.IsNullOrEmptyOrWhiteSpace() == false)
            {
                //过滤条件
                param.FilterString = param.FilterString.JoinFilterString(@"fname like @keyword or fphone like @keyword");

                //条件参数
                param.AddParameter(new SqlParam("@keyword", System.Data.DbType.String, $"%{dto.Keyword}%"));
            }

            //当前要查询的字段列表
            var fieldKeys = new string[] { "fname", "ftype", "fcooperation", "fphone", "fsumbillamount", "fcreatedate" ,"fstatus"};
            foreach (var fieldKey in fieldKeys)
            {
                var field = param.HtmlForm.GetField(fieldKey);
                var columnList = field.ToListColumn(this.Context);
                foreach (var column in columnList)
                {
                    param.SelectedFieldKeys.Add(column.Id);
                }
            }

            //列表构建器
            var listBuilder = this.Container.GetService<IListSqlBuilder>();

            //设置数据隔离方案的过滤条件
            var accessFilter = listBuilder.GetListAccessControlFilter(this.Context, param.HtmlForm.Id);
            param.SetFilter(accessFilter);

            //查询对象
            var queryObj = listBuilder.GetQueryObject(this.Context, param);

            //获取分页数据
            var listData = listBuilder.GetQueryData(this.Context, param, queryObj);

            //获取分页信息（总纪录数、总页数、每页条数、单据数）
            var listDesc = listBuilder.GetListDesc(this.Context, param, queryObj);

            //将平台通用的列表数据结构转换为API数据结构
            var list = new List<ChannelListModel>();
            foreach (var item in listData)
            {
                list.Add(new ChannelListModel
                {
                    Id = Convert.ToString(item["fbillhead_id"]),
                    Cooperation = new ComboDataModel
                    {
                        Id = Convert.ToString(item["fcooperation"]),
                        Name = Convert.ToString(item["fcooperation_fenumitem"]),
                    },
                    CreateDate = Convert.ToDateTime(item["fcreatedate"]),
                    Name = Convert.ToString(item["fname"]),
                    Phone = Convert.ToString(item["fphone"]),
                    SumBillAmount = Convert.ToDecimal(item["fsumbillamount"]),
                    Status = new ComboDataModel
                    {
                        Id = JNConvert.ToStringAndTrim(item["fstatus"]),
                        Name = JNConvert.ToStringAndTrim(item["fstatus_fenumitem"])
                    },
                    Type = new ComboDataModel
                    {
                        Id = Convert.ToString(item["ftype"]),
                        Name = Convert.ToString(item["ftype_fenumitem"]),
                    }
                });
            }

            //设置响应数据包
            resp.Data.List = list;
            resp.Data.TotalRecord = (int)listDesc.Rows;
            resp.Message = "取数成功！";
            resp.Success = true;
        }
}
}