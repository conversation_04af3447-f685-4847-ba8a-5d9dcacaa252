using JieNor.AMS.YDJ.MP.API.DTO.STE.Channel;
using JieNor.AMS.YDJ.MP.API.Model.STE.Channel;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.Channel
{
    /// <summary>
    /// 微信小程序：合作渠道详情合作信息接口
    /// </summary>
    public class ChannelDetailDataController:BaseController
    {
        public object Any(ChannelDetailDataDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<ChannelDetailDataModel>();
            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }
            if (dto.DateType.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 DateType 不能为空！";
                return resp;
            }

            var pars = new List<SqlParam>();
            pars.Add(new SqlParam("@fchannelid", System.Data.DbType.String, dto.Id));
            pars.Add(new SqlParam("@fmainorgid", System.Data.DbType.String, Context.Company));

            //计算日期范围
            var daterangesql = GetDateRangesql(dto);

            //统计已报备商机
            var sql = $@"select count(1) as total  from T_YDJ_CUSTOMERRECORD with(nolock) where fchannelid=@fchannelid and fcancelstatus='0' and fmainorgid=@fmainorgid ";
            if (!daterangesql.IsNullOrEmptyOrWhiteSpace())
            {
                sql = $@"{sql} and fcreatedate between {daterangesql}";
            }
            var res = DBService.ExecuteDynamicObject(Context, sql, pars);
            if (res!=null&&res.Any())
            {
                resp.Data.RepCustomerrecordCount = Convert.ToInt32(res[0]["total"]);//报备商机数
               
            }
            //统计跟进中商机 已关闭商机：已成单或关闭状态为失效关闭
            var sql4 = @"select count(1) as colsetotal from T_YDJ_CUSTOMERRECORD with(nolock) where fcancelstatus='0'   and fchannelid=@fchannelid and fmainorgid=@fmainorgid and (fphase='customerrecord_phase_05' or fclosestatus=1)";
            if (!daterangesql.IsNullOrEmptyOrWhiteSpace())
            {
                sql4 = $@"{sql4} and fcreatedate between  {daterangesql}";
            }
            var res4 = DBService.ExecuteDynamicObject(Context, sql4, pars);
            if (res4!=null&&res4.Any())//跟进中商机=报备商机总数-已关闭商机（成单关闭+失效关闭）
            {
                resp.Data.FollowUpCustomerrecordCount = resp.Data.RepCustomerrecordCount - Convert.ToInt32(res4[0]["colsetotal"]);//跟进中商机
            }

            //统计已成交商机 (商机阶段为成单关闭)
            var sql2 = @"select count(1) as dealtotal from T_YDJ_CUSTOMERRECORD with(nolock) where fphase='customerrecord_phase_05' and fcancelstatus='0' and fchannelid=@fchannelid and fmainorgid=@fmainorgid ";
            if (!daterangesql.IsNullOrEmptyOrWhiteSpace())
            {
                sql2 = $@"{sql2} and fcreatedate between  {daterangesql}";
            }
            var res2 = DBService.ExecuteDynamicObject(Context, sql2, pars);
            if (res2!=null && res2.Any())
            {
                resp.Data.DealCustomerrecordCount = Convert.ToInt32(res2[0]["dealtotal"]);//已成交商机数
                //resp.Data.FollowUpCustomerrecordCount = resp.Data.RepCustomerrecordCount - resp.Data.DealCustomerrecordCount;//跟进中商机数
            }

            //统计已成交合同
            var sql3 = @"select COUNT(1) ordertotal,SUM(fdealamount) as dealamount from T_YDJ_ORDER with(nolock) where fcancelstatus='0' and fstatus='E' and fchannel=@fchannelid and fmainorgid=@fmainorgid";
            if (!daterangesql.IsNullOrEmptyOrWhiteSpace())
            {
                sql3 = $@"{sql3}  and fcreatedate between {daterangesql}";
            }
            var res3 = DBService.ExecuteDynamicObject(Context, sql3, pars);
            if (res3 != null && res3.Any())
            {
                resp.Data.DealOrder = Convert.ToInt32(res3[0]["ordertotal"]);//已成交合同数
                resp.Data.DealAmount = Convert.ToDecimal(res3[0]["dealamount"]);//成交金额
                if (resp.Data.DealOrder==0 || resp.Data.DealAmount==0)
                {
                    resp.Data.PerTicketSales = 0;
                }
                else
                {
                    resp.Data.PerTicketSales = resp.Data.DealAmount / resp.Data.DealOrder;//客单价
                }               
            }
            resp.Success = true;
            return resp;
        }

        /// <summary>
        /// 获取日期范围 。如果日期类型为：3全部，则返回空字符串
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="startdate"></param>
        /// <param name="enddate"></param>
        public string GetDateRangesql(ChannelDetailDataDTO dto)
        {
            var yearsql = "";
            string startdate = "";
            string enddate = "";
            switch (dto.DateType)
            {
                case 0:
                    startdate = BeiJingTime.Now.AddYears(-1).DayBegin().ToString();
                    enddate = BeiJingTime.Now.DayEnd().ToString();
                    yearsql = $@"'{startdate}' and '{enddate}'";
                    break;
                case 1:
                    yearsql = BeiJingTime.Now.ThisYearSql();
                    break;
                case 2:
                    yearsql= BeiJingTime.Now.LastYearSql();
                    break;
                default:
                    break;
            }
            return yearsql;
        }
    }

}
