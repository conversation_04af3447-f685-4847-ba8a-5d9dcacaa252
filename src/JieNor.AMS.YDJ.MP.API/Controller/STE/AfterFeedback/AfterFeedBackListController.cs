using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.MP.API.Controller
{
    public class AfterFeedBackListController : BaseController
    {
        public object Any(AfterFeedBackListDTO dto)
        {
            base.InitializeOperationContext(dto);

            return DoExecute(this.Context,this.Request,dto);
        }

        public static BaseResponse<BaseListPageData<AfterFeedBackListModel>> DoExecute(UserContext usCtx, ServiceStack.Web.IRequest request, AfterFeedBackListDTO dto)
        {
            var resp = new BaseResponse<BaseListPageData<AfterFeedBackListModel>>
            {
                Data = new BaseListPageData<AfterFeedBackListModel>(dto.PageSize)
            };
            BuildSqlWhereAndParams(usCtx,dto, out var param, out var listBuilder);

            dto.Keyword = dto.Keyword?.Trim();
            if (!dto.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                param.AppendFilterString(" t0.flinkstaffid like @keyword or t0.flinkmobile like @keyword or t0.fbillno like @keyword");
                param.AddParameter(new SqlParam("@keyword", System.Data.DbType.String, "%" + dto.Keyword + "%"));
            }
            if (!dto.serNumber.IsNullOrEmptyOrWhiteSpace())
            {
                param.AppendFilterString(" t0.fsourcetype='ydj_service' and fsourcenumber=@serNumber");
                param.AddParameter(new SqlParam("@serNumber", System.Data.DbType.String, dto.serNumber));
            }
            if (!dto.cancelStatus.IsNullOrEmptyOrWhiteSpace())
            {
                if (dto.cancelStatus=="1")
                {
                    param.AppendFilterString(" t0.fcancelstatus='1'");
                }
                else if (dto.cancelStatus=="0")
                {
                    param.AppendFilterString(" t0.fcancelstatus!='1'");
                }
            }

            param.PageCount = dto.PageSize;
            param.PageIndex = dto.PageIndex;

            //排序  
            var orderBy = "fcreatedate";
            switch (dto.Sortby)
            {
                case "fcreatedate": //创建时间
                    orderBy = "fcreatedate";
                    break;
                case "fdate": //反馈时间
                    orderBy = "fdate";
                    break;
                case "ftransferdate": //转总部时间
                    orderBy = "ftransferdate";
                    break;
                case "fhqauditdate"://总部审核时间
                    orderBy = "fhqauditdate";
                    break;
                case "ffinishdate"://完成时间
                    orderBy = "ffinishdate";
                    break;
                case "fclosedate"://关闭时间
                    orderBy = "fclosedate";
                    break;
            }
            param.OrderByString = $"{orderBy} {dto.Sortord}";

            //查询对象
            var queryObj = listBuilder.GetQueryObject(usCtx, param);

            //获取分页数据
            var listData = listBuilder.GetQueryData(usCtx, param, queryObj);

            //获取分页信息（总纪录数、总页数、每页条数、单据数）
            var listDesc = listBuilder.GetListDesc(usCtx, param, queryObj);

            //将平台通用的列表数据结构转换为API数据结构
            var list = new List<AfterFeedBackListModel>();
            var dbService = usCtx.Container.GetService<IDBService>();
            foreach (var item in listData)
            {
                var model = new AfterFeedBackListModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fbillhead_id"]),
                    Number = JNConvert.ToStringAndTrim(item["fbillno"]),
                    Address = JNConvert.ToStringAndTrim(item["flinkaddress"]),
                    Date = Convert.ToDateTime(item["fdate"]).ToString("yyyy-MM-dd HH:mm"),
                    isUserCreate = Convert.ToString(item["fsourcecancl"]).Equals("1")
                };
                if (item.ContainsKey("ffinishdate") && !item["ffinishdate"].IsNullOrEmptyOrWhiteSpace())
                {
                    model.FinishDate = Convert.ToDateTime(item["ffinishdate"]).ToString("yyyy-MM-dd HH:mm");
                }
                if (item.ContainsKey("fclosedate") && !item["fclosedate"].IsNullOrEmptyOrWhiteSpace())
                {
                    model.CloseDate = Convert.ToDateTime(item["fclosedate"]).ToString("yyyy-MM-dd HH:mm");
                }
                if (item.ContainsKey("ftransferdate") && !item["ftransferdate"].IsNullOrEmptyOrWhiteSpace())
                {
                    model.TransferDate = Convert.ToDateTime(item["ftransferdate"]).ToString("yyyy-MM-dd HH:mm");
                }
                if (item.ContainsKey("fhqauditdate") && !item["fhqauditdate"].IsNullOrEmptyOrWhiteSpace())
                {
                    model.HqAuditDate = Convert.ToDateTime(item["fhqauditdate"]).ToString("yyyy-MM-dd HH:mm");
                }

                model.Status = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fstatus"]),
                    Name = JNConvert.ToStringAndTrim(item["fstatus_fenumitem"])
                };

                model.QuestionType = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fquestiontype"]),
                    Name = JNConvert.ToStringAndTrim(item["fquestiontype_fenumitem"])
                };

                model.Customer = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fcustomerid"]),
                    Name = JNConvert.ToStringAndTrim(item["fcustomerid_fname"])
                };
                model.LinkStaff = JNConvert.ToStringAndTrim(item["flinkstaffid"]);
                model.LinkMobile = JNConvert.ToStringAndTrim(item["flinkmobile"]);

                model.province = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fprovince"]),
                    Name = JNConvert.ToStringAndTrim(item["fprovince_fenumitem"])
                };
                model.city = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fcity"]),
                    Name = JNConvert.ToStringAndTrim(item["fcity_fenumitem"])
                };
                model.fregion = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fregion"]),
                    Name = JNConvert.ToStringAndTrim(item["fregion_fenumitem"])
                };
                if (model.province.IsNullOrEmptyOrWhiteSpace() || model.city.IsNullOrEmptyOrWhiteSpace() || model.fregion.IsNullOrEmptyOrWhiteSpace())
                {
                    model.Address = JNConvert.ToStringAndTrim(item["fzblinkaddress"]);
                }
                model.Staff = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fstaffid"]),
                    Name = JNConvert.ToStringAndTrim(item["fstaffid_fname"]),
                };
                model.FeedStatus = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(item["ffeedstatus"]),
                    Name = JNConvert.ToStringAndTrim(item["ffeedstatus_fenumitem"])
                };

                model.QuestionProduct = new List<BaseDataSimpleModel>();
                if (!model.Id.IsNullOrEmptyOrWhiteSpace() )
                {
                    var sqlText = $@"select  tbm.fid,tbm.fnumber,tbm.fname 
                                     from t_bd_material tbm 
                                     inner join t_ste_aftproduct tsa on tbm.fid=tsa.fmaterialid 
                                     where tsa.fid='{model.Id}' ";
                    
                    using (var reader = dbService.ExecuteReader(usCtx, sqlText))
                    {
                        while (reader.Read())
                        {
                            var queryProduct = new BaseDataSimpleModel();
                            queryProduct.Id = Convert.ToString(reader["fid"]);
                            queryProduct.Number = Convert.ToString(reader["fnumber"]);
                            queryProduct.Name = Convert.ToString(reader["fname"]);
                            model.QuestionProduct.Add(queryProduct);
                        }
                    }
                }


                model.Engineer = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fengineerid"]),
                    Name = JNConvert.ToStringAndTrim(item["fengineerid_fname"])
                };

                model.HandleconClusion = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fhandleconclusion"]),
                    Name = JNConvert.ToStringAndTrim(item["fhandleconclusion_fenumitem"])
                };

                model.HqHandleConclusion = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fhqhandleconclusion"]),
                    Name = JNConvert.ToStringAndTrim(item["fhqhandleconclusion_fenumitem"])
                };
                
                model.CreateDate = Convert.ToDateTime(item["fcreatedate"]);

                list.Add(model);
            }

            LoadExtendColumn(usCtx,list);

            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Data.TotalRecord = (int)listDesc.Rows;
            resp.Data.List = list;

            return resp;
        }

        private static void BuildSqlWhereAndParams(UserContext usCtx, AfterFeedBackListDTO dto, out SqlBuilderParameter param, out IListSqlBuilder listBuilder)
        {
            //参数对象
            param = new SqlBuilderParameter(usCtx, "ste_afterfeedback");
            param.ReadDirty = true;
            param.NoColorSetting = true;

            //当前要查询的字段列表
            var fieldKeys = new string[] { "flinkstaffid", "flinkmobile", "fstatus","fcustomerid", "fbillno", "fprovince", "fcity", "fregion", "fzblinkaddress", 
                                           "flinkaddress", "fdate", "fquestiontype", "fstaffid", "fstaffid_ref", "fmasterid_ref", "ftransferdate", "fhqauditdate", 
                                           "ffinishdate", "fclosedate", "ffeedstatus", "fsourcecancl" , "fengineerid","fhandleconclusion","fhqhandleconclusion","fcreatedate" };
            foreach (var fieldKey in fieldKeys)
            {
                var field = param.HtmlForm.GetField(fieldKey);
                var columnList = field.ToListColumn(usCtx);
                foreach (var column in columnList)
                {
                    param.SelectedFieldKeys.Add(column.Id);
                }
            }
            switch (dto.status)
            {  //43493需求调整后只剩 3：总部待审核、4：总部已审核  两个
                case "1":
                    param.AppendFilterString("ffeedstatus = 'aft_service_01' ");
                    break;
                case "2":
                    param.AppendFilterString("ffeedstatus = 'aft_service_02' ");
                    break;
                case "3":
                    param.AppendFilterString("ffeedstatus = 'aft_service_03' ");
                    break;
                case "4":
                    param.AppendFilterString("ffeedstatus = 'aft_service_04' ");
                    break;
                case "5":
                    param.AppendFilterString("ffeedstatus = 'aft_service_05' ");
                    break;
                case "6":
                    param.AppendFilterString("ffeedstatus = 'aft_service_06' ");
                    break;
                default:
                    break;
            }
            //问题类别（1：产品问题；2：安装问题；3：送货问题；4：销售问题；5：服务问题）
            switch (dto.type)
            {
                case "1":
                    param.AppendFilterString("fquestiontype = 'afterquestion_type_01' ");
                    break;
                case "2":
                    param.AppendFilterString("fquestiontype = 'afterquestion_type_02' ");
                    break;
                case "3":
                    param.AppendFilterString("fquestiontype = 'afterquestion_type_03'");
                    break;
                case "4":
                    param.AppendFilterString("fquestiontype = 'afterquestion_type_04'");
                    break;
                case "5":
                    param.AppendFilterString("fquestiontype = 'afterquestion_type_05'");
                    break;
                default:
                    break;
            }
            if (!dto.bgnDate.IsNullOrEmpty() && !dto.bgnDate.Equals(DateTime.MinValue))
            {
                param.AppendFilterString("t0.fdate>=@bgnDate");
                param.AddParameter(new SqlParam("@bgnDate", System.Data.DbType.String, dto.bgnDate.ToString("yyyy-MM-dd 00:00:00")));
            }
            if (!dto.endDate.IsNullOrEmpty() && !dto.endDate.Equals(DateTime.MinValue))
            {
                param.AppendFilterString("t0.fdate<@endDate");
                param.AddParameter(new SqlParam("@endDate", System.Data.DbType.String, dto.endDate.ToString("yyyy-MM-dd 23:59:59")));
            }
            if (!dto.staffName.IsNullOrEmptyOrWhiteSpace())
            {
                param.AppendFilterString(@"
exists(
    select 1 from t_bd_staff c with(nolock) 
    inner join t_bd_staffentry d with(nolock) on c.fid=d.fid 
    where c.fid=t0.fstaffid and c.fname like @staffName and (d.fbiztype='7' or d.fbiztype='0')
)");
                param.AddParameter(new SqlParam("@staffName", System.Data.DbType.String, "%" + dto.staffName.Trim() + "%"));
            }
            if (!dto.teamName.IsNullOrEmptyOrWhiteSpace())
            {
                param.AppendFilterString(@"
exists(
    select 1 from t_bd_department c with(nolock) 
    where c.fid=t0.fdeptid and c.fname like @teamName
)");
                param.AddParameter(new SqlParam("@teamName", System.Data.DbType.String, "%" + dto.teamName.Trim() + "%"));
            }

            //列表构建器
            listBuilder =usCtx.Container.GetService<IListSqlBuilder>();
            //设置数据隔离方案的过滤条件
            var accessFilter = listBuilder.GetListAccessControlFilter(usCtx, param.HtmlForm.Id);
            param.SetFilter(accessFilter);

        }

        /// <summary>
        /// 加载扩展字段
        /// </summary>
        /// <param name="list"></param>
        private static void LoadExtendColumn(UserContext usCtx,List<AfterFeedBackListModel> list)
        {
            if (list == null || list.Count == 0) return;

            // 加载客户
            var customerIds = list.Select(s => s.Customer.Id).Distinct();
            if (customerIds.Any())
            {
                var sqlText = string.Format(@"select fid,fheadimgurl from t_ydj_customer with(nolock) where fid in ({0}) ", string.Join(",", customerIds.Select(s => $"'{s}'")));
                var dbService = usCtx.Container.GetService<IDBService>();
                using (var reader = dbService.ExecuteReader(usCtx, sqlText))
                {
                    while (reader.Read())
                    {
                        string customerId = reader.GetValueToString("fid");
                        string headimg = reader.GetValueToString("fheadimgurl");
                        foreach (var model in list.Where(s => s.Customer.Id == customerId))
                        {
                            model.CusImg = headimg;
                        }
                    }
                }
            }
        }

        //public static List<DataRowAuthInfo> GetDataRowACLPermDataByUser(UserContext ctx, string formId)
        //{
        //    var cacheKey = CST_CacheKey_BizDataRowACL.Fmt(ctx.Company, ctx.UserId, formId);
        //    var redisCache = ctx.Container.GetService<IRedisCache>();
        //    var sAuth = redisCache.Get<string>(ctx, cacheKey);
        //    if (!sAuth.IsNullOrEmptyOrWhiteSpace())
        //    {
        //        if (sAuth.EqualsIgnoreCase("none"))
        //        {
        //            return new List<DataRowAuthInfo>();
        //        }
        //        return sAuth.FromJson<List<DataRowAuthInfo>>();
        //    }

        //    Dictionary<string, DataRowAuthInfo> auth = new Dictionary<string, DataRowAuthInfo>(StringComparer.OrdinalIgnoreCase);
        //    string strSql = $@" select distinct t0.froleid, fbizobjid,ffiltertype,fexpress,fdesc 
        //                        from t_sec_roledatarowacl t0 
        //                        inner join t_sec_roleuser t1 on t0.froleId=t1.froleId
        //                        where t1.fuserid=@fuserid 
        //                            and t0.fcompanyid=@fcompanyid 
        //                            and t0.fbizobjid=@fbizobjid ";
        //    List<SqlParam> lstSqlParas = new List<SqlParam>()
        //    {
        //        new SqlParam("@fuserid", System.Data.DbType.String,ctx.UserId),
        //        new SqlParam("@fcompanyid", System.Data.DbType.String,ctx.Company),
        //        new SqlParam("@fbizobjid", System.Data.DbType.String,formId),
        //    };

        //    List<DataRowAuthInfo> drAuth = new List<DataRowAuthInfo>();
        //    var svc = ctx.Container.GetService<IDBService>();
        //    using (var reader = svc.ExecuteReader(ctx, strSql, lstSqlParas))
        //    {
        //        while (reader.Read())
        //        {
        //            var authInfo = new DataRowAuthInfo()
        //            {
        //                RoleId = reader.GetValue<string>("froleid"),
        //                FormId = reader.GetValue<string>("fbizobjid"),
        //                FilterType = reader.GetValue<string>("ffiltertype"),
        //                Express = reader.GetValue<string>("fexpress"),
        //                Desc = reader.GetValue<string>("fdesc"),
        //            };

        //            drAuth.Add(authInfo);
        //        }
        //    }

        //    if (drAuth.Count == 0)
        //    {
        //        redisCache.Set(ctx, cacheKey, "none");
        //    }
        //    else
        //    {
        //        redisCache.Set(ctx, cacheKey, drAuth.ToJson());
        //    }

        //    return drAuth;
        //}
    }
}
