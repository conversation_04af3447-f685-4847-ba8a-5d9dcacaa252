using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace JieNor.AMS.YDJ.MP.API.Controller
{
    public class AfterFeedBackDetailController : BaseController
    {
        public HtmlForm FeedBackForm;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(AfterFeedBackDetailDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<AfterFeedBackDetailModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }

            //直接根据唯一标识获取数据
            this.FeedBackForm = this.MetaModelService.LoadFormModel(this.Context, "ste_afterfeedback");
            var feedBackObj = this.FeedBackForm.GetBizDataById(this.Context, dto.Id, true);

            if (feedBackObj == null)
            {
                resp.Message = "售后反馈单不存在或已被删除！";
                resp.Success = false;
                return resp;
            }
            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Code = 200;
            resp.Data = MapTo(feedBackObj);
            return resp;
        }

        private AfterFeedBackDetailModel MapTo(DynamicObject feedBackObj)
        {
            var statusObj = feedBackObj["fstatus_ref"] as DynamicObject;
            var staffObj = feedBackObj["fstaffid_ref"] as DynamicObject;
            //var billtypeObj = feedBackObj["fbilltypeid_ref"] as DynamicObject;
            var sourcetypeObj = feedBackObj["fsourcetype_ref"] as DynamicObject;
            var deptObj = feedBackObj["fdeptid_ref"] as DynamicObject;
            var storeObj = feedBackObj["fstore_ref"] as DynamicObject;
            AfterFeedBackDetailModel model = new AfterFeedBackDetailModel
            {
                Id = JNConvert.ToStringAndTrim(feedBackObj["id"]),
                Number = JNConvert.ToStringAndTrim(feedBackObj["fbillno"]),
                Customer = new CustomerSimpleModel(feedBackObj["fcustomerid_ref"] as DynamicObject),
                LinkStaff = JNConvert.ToStringAndTrim(feedBackObj["flinkstaffid"]),
                LinkMobile = JNConvert.ToStringAndTrim(feedBackObj["flinkmobile"]),
                Province = new ComboDataModel(feedBackObj["fprovince_ref"] as DynamicObject),
                City = new ComboDataModel(feedBackObj["fcity_ref"] as DynamicObject),
                Region = new ComboDataModel(feedBackObj["fregion_ref"] as DynamicObject),
                Address = JNConvert.ToStringAndTrim(feedBackObj["flinkaddress"]),
                Date = Convert.ToDateTime(feedBackObj["fdate"]).ToString("yyyy-MM-dd HH:mm"),
                FeedStatus = new ComboDataModel(feedBackObj["ffeedstatus_ref"] as DynamicObject),
                //Product = new BaseDataSimpleModel(feedBackObj["fquestionproduct_ref"] as DynamicObject),
                QuestionType = new ComboDataModel(feedBackObj["fquestiontype_ref"] as DynamicObject),
                QuestionDesc = JNConvert.ToStringAndTrim(feedBackObj["fquestiondesc"]),
                WorkType = new ComboDataModel(feedBackObj["finstitutiontype_ref"] as DynamicObject),
                Conclusion = new ComboDataModel(feedBackObj["fhandleconclusion_ref"] as DynamicObject),
                Scheme = JNConvert.ToStringAndTrim(feedBackObj["fscheme"]),
                IsTransfer = Convert.ToBoolean(feedBackObj["fistransfer"]),
                Status = new ComboDataModel(statusObj)
            };
            if (model.Province.IsNullOrEmptyOrWhiteSpace() || model.City.IsNullOrEmptyOrWhiteSpace() || model.Region.IsNullOrEmptyOrWhiteSpace())
            {
                model.Address = JNConvert.ToStringAndTrim(feedBackObj["fzblinkaddress"]);
            }

            model.HqConclusion = new ComboDataModel(feedBackObj["fhqhandleconclusion_ref"] as DynamicObject);
            model.HqScheme = JNConvert.ToStringAndTrim(feedBackObj["fhqscheme"]);
            model.CreateDate = Convert.ToDateTime(feedBackObj["fcreatedate"]);

            DynamicObject work = null;
            switch (model.WorkType.Id)
            {
                case "dutyunit_type_01"://供应商
                    work = feedBackObj["fdutysupplierid_ref"] as DynamicObject;
                    break;
                case "dutyunit_type_02"://客户
                    work = feedBackObj["fdutycustomerid_ref"] as DynamicObject;
                    break;
                case "dutyunit_type_03"://员工
                    work = feedBackObj["fdutystaffid_ref"] as DynamicObject;
                    break;
                case "dutyunit_type_04"://部门
                    work = feedBackObj["fdutydeptid_ref"] as DynamicObject;
                    break;
            }
            model.WorkInfo = new ComboDataModel
            {
                Id = JNConvert.ToStringAndTrim(work?["id"]),
                Name = JNConvert.ToStringAndTrim(work?["fname"]),
            };
            model.QuestionImgs = ImageFieldUtil.ParseImages(feedBackObj, "fquestionimage", true);
            model.ZTImages = JNConvert.ToStringAndTrim(feedBackObj["fztimage"]).Split('|').ToList();
            model.BaseInfo = new AfterFeedBackInfo
            {
                Agent = new BaseDataSimpleModel(feedBackObj["fagentid_ref"] as DynamicObject),
                //AuthCity = new BaseDataSimpleModel(feedBackObj["fauthcity_ref"] as DynamicObject),
                Order = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(feedBackObj["forderno"]),
                    Name = JNConvert.ToStringAndTrim((feedBackObj["forderno_ref"] as DynamicObject)?["fbillno"])
                },
                SourceType = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(sourcetypeObj?["id"]),
                    Name = JNConvert.ToStringAndTrim(sourcetypeObj?["fname"]),
                },
                //源单编号
                SourceNumber = JNConvert.ToStringAndTrim(feedBackObj["fsourcenumber"]),
                Staff = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(staffObj?["id"]),
                    Name = JNConvert.ToStringAndTrim(staffObj?["fname"]),
                },
                StaffPhone = JNConvert.ToStringAndTrim(feedBackObj["fphone"]),
                Dept = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(deptObj?["id"]),
                    Name = JNConvert.ToStringAndTrim(deptObj?["fname"]),
                },
                Store = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(storeObj?["id"]),
                    Name = JNConvert.ToStringAndTrim(storeObj?["fname"]),
                },
                Engineer = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(feedBackObj["fengineerid"]),
                    Name = JNConvert.ToStringAndTrim((feedBackObj["fengineerid_ref"] as DynamicObject)?["fname"]),
                },
                Category = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(feedBackObj["fcategoryid"]),
                    Name = JNConvert.ToStringAndTrim(feedBackObj["fcategoryid"])/*JNConvert.ToStringAndTrim((feedBackObj["fcategoryid_ref"] as DynamicObject)?["fname"])*/,
                },
                Deliver = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(feedBackObj["fdeliver"]),
                    Name = JNConvert.ToStringAndTrim((feedBackObj["fdeliver_ref"] as DynamicObject)?["fname"]),
                }
            };
            if (!feedBackObj["facceptdate"].IsNullOrEmptyOrWhiteSpace())
            {
                model.BaseInfo.AcceptDate = Convert.ToDateTime(feedBackObj["facceptdate"]).ToString("yyyy-MM-dd HH:mm");
            }
            if (!feedBackObj["ftransferdate"].IsNullOrEmptyOrWhiteSpace())
            {
                model.BaseInfo.TransferDate = Convert.ToDateTime(feedBackObj["ftransferdate"]).ToString("yyyy-MM-dd HH:mm");
            }
            if (!feedBackObj["fhqauditdate"].IsNullOrEmptyOrWhiteSpace())
            {
                model.BaseInfo.HqAuditDate = Convert.ToDateTime(feedBackObj["fhqauditdate"]).ToString("yyyy-MM-dd HH:mm");
            }
            if (!feedBackObj["ffinishdate"].IsNullOrEmptyOrWhiteSpace())
            {
                model.BaseInfo.FinishDate = Convert.ToDateTime(feedBackObj["ffinishdate"]).ToString("yyyy-MM-dd HH:mm");
            }
            if (!feedBackObj["fclosedate"].IsNullOrEmptyOrWhiteSpace())
            {
                model.BaseInfo.CloseDate = Convert.ToDateTime(feedBackObj["fclosedate"]).ToString("yyyy-MM-dd HH:mm");
            }
            //var proCount = Convert.ToInt32(feedBackObj["fprocount"]).ToString();
            model.ReturnApplication = new FeedBackReturnInfo
            {
                IsReturn = Convert.ToString(feedBackObj["fisreturn"]) == "1" ? true : false,
                ReturnDate = feedBackObj["freturndate"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToDateTime(feedBackObj["freturndate"]).ToString("yyyy-MM-dd HH:mm:ss"),
                AuthCity = new BaseDataSimpleModel(feedBackObj["fauthcity_ref"] as DynamicObject),
                //City = JNConvert.ToStringAndTrim(feedBackObj["fcountry"]),
                FeedType = new ComboDataModel(feedBackObj["ffeedtype_ref"] as DynamicObject),
                EngineerTel = JNConvert.ToStringAndTrim(feedBackObj["fengineerphone"]),
                //SeltypeName = JNConvert.ToStringAndTrim(feedBackObj["fseltypename"]),
                //Specifica = JNConvert.ToStringAndTrim(feedBackObj["fspecifica"]),
                //ProCount = proCount.Equals("0") ? "" : proCount,
                EngineerAddress = JNConvert.ToStringAndTrim(feedBackObj["fengineeraddress"]),
                CusreturnAdd = JNConvert.ToStringAndTrim(feedBackObj["fcusreturnadd"]),
                ReturnReson = JNConvert.ToStringAndTrim(feedBackObj["freturnreson"]),
                SapNumber = JNConvert.ToStringAndTrim(feedBackObj["fsapnumber"]),
                CususeTime = JNConvert.ToStringAndTrim(feedBackObj["fcususetime"]),
                ReturnlogNum = JNConvert.ToStringAndTrim(feedBackObj["freturnlognum"])
            };
            var productObjs = feedBackObj["fproductentry"] as DynamicObjectCollection;
            model.Products = productObjs?.Select(s => new AftProductEditModel(s, this.Context)).ToList() ?? new List<AftProductEditModel>();
            ////加载辅助属性信息
            //var auxPropValIds = model.ServiceProduct.Select(o => o.AuxPropValId).ToList();
            //var auxPropValsKv = ProductUtil.PackAuxPropFieldValue(this.Context, auxPropValIds);
            ////详情商品属性
            //foreach (var item in model.ServiceProduct)
            //{
            //    item.AuxPropVals = auxPropValsKv.GetValue(item.AuxPropValId) ?? new List<Dictionary<string, string>>();
            //}
            Dictionary<string, ProOtherInfo> product = new Dictionary<string, ProOtherInfo>();
            foreach (var item in model.Products.Where(x => !x.Product.Id.IsNullOrEmptyOrWhiteSpace()))
            {
                var key = item.Product.Id + "-"/* + item.AuxPropValId*/;
                if (!product.ContainsKey(key))
                {
                    //此处不严格按钮辅助属性去匹配了，因为可能总部下发过来的辅助属性，匹配不到
                    product.Add(key, new ProOtherInfo
                    {
                        auxPropValId = string.Empty,
                        AuxPropVals = null,
                        customDesc = item.CustomDesc
                    });
                }
            }
            var productImgs = ProductUtil.GetImages(this.Context, product);
            foreach (var item in model.Products)
            {
                if (productImgs.ContainsKey(item.Product.Id + "-" /*+ item.AuxPropValId*/))
                {
                    item.Img = productImgs[item.Product.Id + "-" /*+ item.AuxPropValId*/] == null ? new BaseImageModel() : productImgs[item.Product.Id + "-" /*+ item.AuxPropValId*/].FirstOrDefault();
                }
                else
                {
                    item.Img = new BaseImageModel();
                }
            }
            if (!feedBackObj["farrivaldate"].IsNullOrEmptyOrWhiteSpace())
            {
                model.ReturnApplication.ArrivalDate = Convert.ToDateTime(feedBackObj["farrivaldate"]).ToString("yyyy-MM-dd");
            }
            if (!feedBackObj["fservicedate"].IsNullOrEmptyOrWhiteSpace())
            {
                model.ReturnApplication.ServiceDate = Convert.ToDateTime(feedBackObj["fservicedate"]).ToString("yyyy-MM-dd");
            }
            model.BillTypeNo = JNConvert.ToStringAndTrim((feedBackObj["fbilltype_ref"] as DynamicObject)?["id"]);
            model.BillTypeName = JNConvert.ToStringAndTrim((feedBackObj["fbilltype_ref"] as DynamicObject)?["fname"]);
            model.hqfeedbillno = JNConvert.ToStringAndTrim(feedBackObj["fhqfeedbillno"]);
            model.hqprogress = JNConvert.ToStringAndTrim(feedBackObj["fhqprogress"]);
            this.LoadExtendColumn(model);
            return model;
        }

        /// <summary>
        /// 加载扩展字段
        /// </summary>
        /// <param name="list"></param>
        private void LoadExtendColumn(AfterFeedBackDetailModel cus)
        {

            // 加载客户
            var customerId = cus.Customer.Id;
            if (customerId.Any())
            {
                var sqlText = string.Format(@"select fheadimgurl from t_ydj_customer with(nolock) where fid='{0}' ", customerId);
                using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
                {
                    while (reader.Read())
                    {
                        string img = reader.GetValueToString("fheadimgurl");
                        cus.CusImg = img;
                    }
                }
            }
        }
    }
}
