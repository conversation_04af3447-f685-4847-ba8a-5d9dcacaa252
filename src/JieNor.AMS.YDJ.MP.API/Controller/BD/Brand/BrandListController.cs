using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.AMS.YDJ.MP.API.Utils;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Category
{
    /// <summary>
    /// 微信小程序：品牌列表取数接口
    /// </summary>
    public class BrandListController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(BrandListDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseListPageData<BrandListModel>>
            {
                Data = new BaseListPageData<BrandListModel>(dto.PageSize)
            };

            //获取分页数据
            var list = new List<BrandListModel>();

            //获取分页总记录数
            var totalRecord = 0;

            GetBrandDatasByPage(dto,out list,out totalRecord);

            //设置响应数据包
            resp.Data.List = list;
            resp.Data.TotalRecord = totalRecord;
            resp.Message = "取数成功！";
            resp.Success = true;

            return resp;
        }

        /// <summary>
        /// 获取品牌分页数据
        /// 作者：zpf
        /// 日期：2022-04-15
        /// </summary>
        /// <param name="dto">请求参数</param>
        /// <param name="list">品牌数据</param>
        /// <param name="totalRecord">总记录数</param>
        private void GetBrandDatasByPage(BrandListDTO dto, out List<BrandListModel> list, out int totalRecord)
        {
            list = new List<BrandListModel>();
            //参数对象
            SqlBuilderParameter param = new SqlBuilderParameter(this.Context, "ydj_brand");
            param.ReadDirty = true;
            param.NoColorSetting = true;
            param.PageCount = dto.PageSize;
            param.PageIndex = dto.PageIndex;
            param.SrcFormId = "fbrandid";

            var fieldKeys = new string[] { "id", "fname" };
            param.AddParameter(new SqlParam("@ftoporgid", System.Data.DbType.String, this.Context.TopCompanyId));
            if (!dto.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                param.AppendFilterString(" fname like @keyword");
                param.AddParameter(new SqlParam("@keyword", System.Data.DbType.String, $"%{dto.Keyword}%"));
            }

            //授权品牌
            var authView = this.Context.GetAgenAuthBrandDataPKID(new DataQueryRuleParaInfo());
            if (!authView.Item2.IsNullOrEmptyOrWhiteSpace())
            {
                if(authView.Item1 == "=")
                {
                    param.AppendFilterString(" t0.fid = '{0}' ".Fmt(authView.Item2));
                }
                else if (authView.Item1.EqualsIgnoreCase("in"))
                {
                    param.AppendFilterString(" t0.fid in ( {0}) ".Fmt(authView.Item2));
                }
                else if (authView.Item1.EqualsIgnoreCase ("exists"))
                {
                    param.AppendFilterString(" exists ( select 1 from ({0}) TMPXX where TMPXX.FPKId=t0.fid )".Fmt(authView.Item2));
                }                
            }

            //列表构建器
            var listBuilder = this.Context.Container.GetService<IListSqlBuilder>();

            //设置数据隔离方案的过滤条件
            var accessFilter = listBuilder.GetListAccessControlFilter(this.Context, param.HtmlForm.Id);
            param.SetFilter(accessFilter);

            //查询对象
            var queryObj = listBuilder.GetQueryObject(this.Context, param);

            //获取分页数据
            var listDatas = listBuilder.GetQueryData(this.Context, param, queryObj);

            //获取分页信息（总纪录数、总页数、每页条数、单据数）
            var listDesc = listBuilder.GetListDesc(this.Context, param, queryObj);
            foreach (var item in listDatas)
            {
                var staff = new BrandListModel()
                {
                    Id = JNConvert.ToStringAndTrim(item["fbillhead_id"]),
                    Name = JNConvert.ToStringAndTrim(item["fname"]),
                };
                list.Add(staff);
            }
            totalRecord = Convert.ToInt32(listDesc.Rows);
        }

        /// <summary>
        /// 获取分页数据
        /// </summary>
        private List<BrandListModel> GetList(BrandListDTO dto, List<SqlParam> sqlParam, StringBuilder sqlWhere)
        {
            var list = new List<BrandListModel>();

            //默认按创建日期降序
            var orderBy = "";
            switch (dto.Sortby)
            {
                default:
                    orderBy = "fcreatedate";
                    break;
            }
            orderBy += " " + dto.Sortord;

            var sqlText = $@"
            select top {dto.PageSize} * from 
            (
	            select row_number() over(order by {orderBy}) rownum,
                m.fid,m.fname from t_ydj_brand m with(nolock) 
                where {sqlWhere}
            ) p 
            where rownum > {dto.PageSize} * ({dto.PageIndex} - 1)";

            using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, sqlParam))
            {
                while (reader.Read())
                {
                    list.Add(new BrandListModel
                    {
                        Id = reader.GetValueToString("fid"),
                        Name = reader.GetValueToString("fname")
                    });
                }
            }

            return list;
        }

        /// <summary>
        /// 获取分页总记录数
        /// </summary>
        private int GetTotalRecord(List<SqlParam> sqlParam, StringBuilder sqlWhere)
        {
            var totalRecord = 0;

            var sqlText = $@"
            select count(0) totalRecord from t_ydj_brand m with(nolock) 
            where {sqlWhere}";

            using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    totalRecord = Convert.ToInt32(reader["totalRecord"]);
                }
            }

            return totalRecord;
        }
    }
}