using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Controller.STE.Order;
using JieNor.AMS.YDJ.MP.API.DTO.BD.BillType;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.BillType
{
    /// <summary>
    /// 微信小程序：获取单据类型扩展参数接口
    /// </summary>
    public class GetUiBillTypeParamController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(GetUiBillTypeParamDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataSimpleModel>();


            if (dto.BillTypeId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Data.ExtendParam = string.Empty.ToJson();
                resp.Message = $"单据类型id参数 BillTypeId 不能为空！";
                return resp;
            }

            var res = this.HttpGateway.InvokeBillOperation(this.Context, "bd_billtype", null, "getuibillTypeparam", new Dictionary<string, object>() { { "billTypeId", dto.BillTypeId } });


            if (res.IsSuccess)
            {
                resp.Success = true;
                resp.Data.ExtendParam = res.SrvData.ToJson();
                resp.Message = $"获取单据类型扩展参数成功！";
            }
            else
            {
                resp.Success = false;
                resp.Data.ExtendParam = res.SrvData.ToJson();
                resp.Message = $"获取单据类型扩展参数失败！ " + res.SimpleMessage;
            }

            return resp;
        }
    }
}
