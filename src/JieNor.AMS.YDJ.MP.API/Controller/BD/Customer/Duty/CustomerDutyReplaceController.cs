using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Customer.Duty
{
    /// <summary>
    /// 微信小程序：更换客户负责人接口
    /// </summary>
    public class CustomerDutyReplaceController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(CustomerDutyReplaceDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>
            {
                Data = new object()
            };

            if (dto.CustomerId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "参数 customerId 不能为空！";
                resp.Success = false;
                return resp;
            }

            if (dto.DutyId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "参数 dutyId 不能为空！";
                resp.Success = false;
                return resp;
            }

            if (dto.DeptId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "参数 deptId 不能为空！";
                resp.Success = false;
                return resp;
            }

            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ydj_customer",
                    OperationNo = "replaceduty",
                    SimpleData = new Dictionary<string, string>
                    {
                        { "id", dto.Id },
                        { "dutyid", dto.DutyId },
                        { "deptid", dto.DeptId },
                        { "customerid", dto.CustomerId },
                    }
                });
            var result = response?.OperationResult;

            return result.ToResponseModel<object>();
        }
    }
}