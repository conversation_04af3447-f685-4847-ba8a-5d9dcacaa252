using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework;
using JieNor.Framework.Interface;

namespace JieNor.AMS.YDJ.MP.API.Controller.Common
{
    /// <summary>
    /// 微信小程序：释放锁接口
    /// </summary>
    public class ReleaseLockController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ReleaseLockDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            var billLockService = this.Container.GetService<IBillLockService>();

            billLockService.ReleaseLock(this.Context, dto.FormId, dto.Id, dto.LockToken);

            resp.Message = "释放成功！";
            resp.Success = true;

            return resp;
        }
    }
}