using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.DTO.Common.Duty;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.Common.Duty
{
    /// <summary>
    /// 微信小程序：负责人/协助人/销售成员保存接口
    /// </summary>
    public class DutyAddController : BaseController
    {
        public object Any(DutyAddDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>
            {
                Data = new object()
            };

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "参数 Id 不能为空！";
                resp.Success = false;
                return resp;
            }

            if (dto.DutyId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "参数 dutyId 不能为空！";
                resp.Success = false;
                return resp;
            }

            if (dto.DeptId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "参数 deptId 不能为空！";
                resp.Success = false;
                return resp;
            }

            if (dto.FormId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "参数 formId 不能为空！";
                resp.Success = false;
                return resp;
            }
            //如果是客户添加多负责人
            if (dto.FormId.EqualsIgnoreCase("ydj_customer"))
            {
                // 向麦浩系统发送请求
                var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                    this.Request,
                    new CommonBillDTO()
                    {
                        FormId = "ydj_customer",
                        OperationNo = "addduty",
                        SimpleData = new Dictionary<string, string>
                        {
                        { "dutyid", dto.DutyId },
                        { "deptid", dto.DeptId },
                        { "customerid", dto.Id },
                        { "description", dto.Description },
                        }
                    });
                var result = response?.OperationResult;

                return result.ToResponseModel<object>();
            }
            //如果是商机添加销售成员
            if (dto.FormId.EqualsIgnoreCase("ydj_customerrecord"))
            {
                return CustomerRecordAddDuty(dto);
            }

            //如果是合作渠道添加多负责人
            if (dto.FormId.EqualsIgnoreCase("ste_channel"))
            {
                return ChannelAddDuty(dto);
            }

            //返回结果
            resp.Message = "参数formId不存在！";
            resp.Success = false;
            return resp;
        }

        //销售渠道添加多负责人
        private object ChannelAddDuty(DutyAddDTO dto)
        {
            var resp = new BaseResponse<object>
            {
                Data = new object()
            };
            List<Dictionary<string, object>> fdutyentry = new List<Dictionary<string, object>>();
            var obj = this.Context.GetBizDataById("ste_channel", dto.Id, true);
            if (obj==null)
            {
                resp.Message = "服务器繁忙请稍后再试！";
                resp.Success = false;
                return resp;
            }
            var fdutyentryObjs = obj["fdutyentry"] as DynamicObjectCollection;

            var tuple = CheckObject(fdutyentryObjs, dto.DutyId);
            Dictionary<string, object> modelnew = new Dictionary<string, object>();
            
            if (!tuple.Item2)//如果有该联系人，则返回
            {
                resp.Message = "联系人已经存在！";
                resp.Success = true;
                return resp;
            }
            else
            {
                fdutyentry = tuple.Item1;
                modelnew.Add("fdutyid", dto.DutyId);
                modelnew.Add("fdeptid", dto.DeptId);
                modelnew.Add("fjoindate", BeiJingTime.Now);
                modelnew.Add("fnote", dto.Description);
                fdutyentry.Add(modelnew);
            }

            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ste_channel",
                    OperationNo = "save",
                    BillData = BuildBillData(obj, fdutyentry),
                    Id = dto.Id
                });
            var result = response?.OperationResult;
            return result.ToResponseModel<object>();

        }

        /// <summary>
        /// 查询已有负责人 item1 数组，item2为是否允许添加
        /// </summary>
        /// <param name="objs"></param>
        /// <param name="staffid"></param>
        /// <returns></returns>
        public Tuple<List<Dictionary<string, object>>, bool> CheckObject(DynamicObjectCollection objs, string staffid)
        {
            var isallowadd = true;
            List<Dictionary<string, object>> list = new List<Dictionary<string, object>>();
            foreach (var item in objs)
            {
                Dictionary<string, object> model = new Dictionary<string, object>();
                if (Convert.ToString(item["fdutyid"]) == staffid)//如果有该负责人，则返回false，不做新增处理
                {
                    isallowadd = false;
                    break;
                }
                else
                {
                    model.Add("fdutyid",Convert.ToString(item["fdutyid"]));
                    model.Add("fdeptid",Convert.ToString(item["fdeptid"]));
                    model.Add("fjoindate", Convert.ToDateTime(item["fjoindate"]));
                    model.Add("fnote", Convert.ToString(item["fnote"]));
                    model.Add("id", Convert.ToString(item["id"]));
                }
                list.Add(model);
            }
           
            return new Tuple<List<Dictionary<string, object>>, bool>(list,isallowadd);
        }

        private string BuildBillData(DynamicObject obj, List<Dictionary<string, object>> fdutyentry = null)
        {
            var data =
                new Dictionary<string, object>
                {
                    { "id",  Convert.ToString(obj["id"]) },
                    { "fdescription", Convert.ToString(obj["fdescription"])},
                    { "fname", Convert.ToString(obj["fname"]) },
                    { "fphone",Convert.ToString(obj["fphone"]) },
                    { "ftype", Convert.ToString(obj["ftype"]) },
                    { "fcooperation", Convert.ToString(obj["fcooperation"])},
                    { "fcompany", Convert.ToString(obj["fcompany"]) },

                    { "fprovince", Convert.ToString(obj["fprovince"]) },
                    { "fcity",  Convert.ToString(obj["fcity"]) },
                    { "fregion", Convert.ToString(obj["fregion"]) },
                    { "faddress", Convert.ToString(obj["faddress"]) },

                    // 收款信息
                    { "faccountname", Convert.ToString(obj["faccountname"]) },
                    { "fbank", Convert.ToString(obj["fbank"])},
                    { "fbanknumber", Convert.ToString(obj["fbanknumber"]) },

                    { "fcontacts",Convert.ToString(obj["fcontacts"]) }

                };
            if (fdutyentry != null && fdutyentry.Any())
            {
                data.Add("fdutyentry", fdutyentry);
            }

            var billData = (new List<Dictionary<string, object>> { data }).ToJson();

            return billData;
        }

        /// <summary>
        /// 客户增加负责人
        /// </summary>
        public object CustomerRecordAddDuty(DutyAddDTO dto)
        {
            var resp = new BaseResponse<object>();
            var data = this.Context.GetBizDataById("ydj_customerrecord", dto.Id, true);
            if (data == null)
            {
                resp.Message = "服务器繁忙,请稍后再试！";
                resp.Success = false;
                return resp;
            }
            //检查权限
            var maindutyid = Convert.ToString(data["fdutyid"]);//主负责人
            var curdutyid = this.Context.GetCurrentStaffId();//当前员工
            var deptObject = data["fdeptid_ref"] as DynamicObject;
            var leaderid = Convert.ToString(deptObject["fleaderid"]);//部门 负责人
            if (curdutyid != maindutyid && curdutyid != leaderid)
            {
                resp.Success = false;
                resp.Message = $"销售成员仅商机负责人或部门负责人才能添加 ！";
                return resp;
            }
            //检查负责人数据合法性
            if (!checkduty(data, dto))
            {
                resp.Success = true;
                resp.Message = $"销售员工重复添加！";
                return resp;
            }

            resp = InvokeCustomerRecordSave(data, dto);

            if (resp.Success)
            {

                string customerNumber = Convert.ToString((data["fcustomerid_ref"] as DynamicObject)["fnumber"]);
                //添加客户跟进记录
                FollowerRecordSaveDTO followerRecordSaveDto2 = new FollowerRecordSaveDTO();
                followerRecordSaveDto2.CustomerId = Convert.ToString(data["fcustomerid"]);
                followerRecordSaveDto2.Contacts = Convert.ToString(data["fcustomername"]);
                followerRecordSaveDto2.Phone = Convert.ToString(data["fphone"]);
                followerRecordSaveDto2.Description = "商机添加销售员工";
                followerRecordSaveDto2.ObjectType = "objecttype22";//跟进记录
                followerRecordSaveDto2.ObjectId = dto.Id;//商机id
                followerRecordSaveDto2.ObjectNo = Convert.ToString(data["fbillno"]);//商机编号
                followerRecordSaveDto2.SourceType = "ydj_customerrecord";//源单类型
                followerRecordSaveDto2.SourceNumber = Convert.ToString(data["fbillno"]);
                this.Context.AddFollowerRecord(this.Request, followerRecordSaveDto2);
                
            }
            return resp;
        }

        /// <summary>
        /// 检查销售员工是否重复 重复返回false 不重复为true
        /// </summary>
        /// <param name="dataObj"></param>
        /// <param name="dto"></param>
        /// <returns></returns>
        public bool checkduty(DynamicObject dataObj, DutyAddDTO dto)
        {
            var dutyentrys = dataObj["fdutyentry"] as DynamicObjectCollection;
            foreach (var item in dutyentrys)
            {
                var dutyid = Convert.ToString(item["fdutyid"]);
                if (dutyid == dto.DutyId)
                {
                    return false;
                }
            }
            return true;
        }

        private string BuildCustomerRecordBillData(DynamicObject dataObj, DutyAddDTO dto)
        {
            var fgoshopdate = Convert.ToString(dataObj["fgoshopdate"]);
            if (fgoshopdate.IsNullOrEmptyOrWhiteSpace())//默认设置为当前日期
            {
                fgoshopdate = BeiJingTime.Now.ToString();
            }
            var data = new Dictionary<string, object>
                {
                    { "id", Convert.ToString(dataObj["id"]) },
                    { "fdescription", Convert.ToString(dataObj["fdescription"]) },
                    { "fgoshopdate", fgoshopdate },
                    { "ftype", Convert.ToString( dataObj["ftype"]) },
                    { "fdemand", Convert.ToString(dataObj["fdemand"]) },
                    { "fspace", Convert.ToString(dataObj["fspace"]) },
                    { "fstyle",Convert.ToString(dataObj["fstyle"] )},
                    { "froom_enum", Convert.ToString(dataObj["froom_enum"] )},
                    { "fhall_enum", Convert.ToString(dataObj["fhall_enum"]) },
                    { "ftoilet_enum", Convert.ToString(dataObj["ftoilet_enum"] )},
                    { "fbalcony_enum", Convert.ToString(dataObj["fbalcony_enum"] )},
                    { "farea", Convert.ToString(dataObj["farea"]) },
                    { "frenovation", Convert.ToString(dataObj["frenovation"]) },
                    { "fexpectdate",Convert.ToDateTime( dataObj["fexpectdate"] )},
                    { "fbudget", Convert.ToString(dataObj["fbudget"]) },

                    { "fcustomerid", Convert.ToString(dataObj["fcustomerid"] )},
                    { "fcustomername", Convert.ToString(dataObj["fcustomername"]) },
                    { "fcontacts", Convert.ToString(dataObj["fcontacts"] )},
                    { "fphone", Convert.ToString(dataObj["fphone"] )},
                    { "fwechat", Convert.ToString(dataObj["fwechat"] )},
                     {"fcountry",Convert.ToString( dataObj["fcountry"] )},
                    { "fprovince",Convert.ToString( dataObj["fprovince"] )},
                    { "fcity",Convert.ToString( dataObj["fcity"]) },
                    { "fregion",Convert.ToString( dataObj["fregion"] )},
                    { "faddress", Convert.ToString(dataObj["faddress"] )},
                    { "fgender",Convert.ToString( dataObj["fgender"] )},
                    { "fage",Convert.ToString( dataObj["fage"]) },
                    { "fcustomersource", Convert.ToString(dataObj["fcustomersource"] )},
                    { "fbuildingid",Convert.ToString( dataObj["fbuildingid"] )},
                    { "fchannelid", Convert.ToString(dataObj["fchannelid"] )},

                    { "fdeptid",Convert.ToString( dataObj["fdeptid"] )}, //所属门店默认为“当前登录用户所在的门店”
                    { "fdutyid", Convert.ToString( dataObj["fdutyid"] )}, //负责人默认为“当前登录用户所关联的员工”
                    { "fsalecategory", Convert.ToString(dataObj["fsalecategory"]) },
                    { "fsalecategory_txt", Convert.ToString(dataObj["fsalecategory_txt"])},
                    {"fphase", Convert.ToString(dataObj["fphase"]) },
                    {"fimage",Convert.ToString(dataObj["fimage"]) },
                    {"fimage_txt", Convert.ToString(dataObj["fimage_txt"])}
                };
            var dutyentrys = dataObj["fdutyentry"] as DynamicObjectCollection;
            var dutys = new List<Dictionary<string, object>>();

            foreach (var item in dutyentrys)
            {
                var joindate = Convert.ToDateTime(item["fjoindate"]);
                if (joindate<Convert.ToDateTime("1800-1-1"))
                {
                    joindate = Convert.ToDateTime("1800-1-1");
                }
                var oldduty = new Dictionary<string, object> {
                     { "fismain", Convert.ToBoolean(item["fismain"]) },
                    { "fdutyid", Convert.ToString(item["fdutyid"]) },
                    {"fdeptid",Convert.ToString(item["fdeptid"]) },
                    { "fjoindate",joindate},
                    {"id",Convert.ToString(item["id"]) },
                    {"fdescription",Convert.ToString(item["fdescription"]) }
                };
                dutys.Add(oldduty);
            }
            dutys.Add(new Dictionary<string, object>
                {
                    { "fismain", false },
                    { "fdutyid", dto.DutyId },
                    {"fdeptid",dto.DeptId },
                    { "fjoindate",BeiJingTime.Now},
                    {"fdescription",dto.Description }
                });
            data.Add("fdutyentry", dutys);

            var billData = (new List<Dictionary<string, object>> { data }).ToJson();
            return billData;
        }

        private BaseResponse<object> InvokeCustomerRecordSave(DynamicObject dataObj, DutyAddDTO dto)
        {
            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ydj_customerrecord",
                    OperationNo = "save",
                    BillData = BuildCustomerRecordBillData(dataObj, dto),
                    Id = dto.Id
                });
            var result = response?.OperationResult;

            return result.ToResponseModel<object>();
        }
    }
}
