using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ServiceStack;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Config;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.Utils;
using JieNor.AMS.YDJ.MP.API.Utils;

namespace JieNor.AMS.YDJ.MP.API.Controller.Auth
{
    /// <summary>
    /// 微信小程序：切换企业接口
    /// </summary>
    public class ToggleCompanyController : ValidataAgentController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ToggleCompanyDTO dto)
        {
            this.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            //校验参数
            if (!this.ValidateParam(dto, resp)) return resp;


            //todo：1、解绑原有企业

            //解绑时获取企业微信用户信息
            var ewcData = this.GetQyWxUserInfoForUnbind(dto, resp);
            if (!resp.Success) return resp;

            //解绑AC系统企业微信用户
            this.UnBindQyWxUser(ewcData, resp);
            if (!resp.Success) return resp;

            //解绑运维系统企业微信用户
            this.UnBindOpsQyWxUser(ewcData, resp);
            if (!resp.Success) return resp;


            //todo：2、绑定现有企业

            //绑定时获取企业微信用户信息
            var ewcDataBind = this.GetQyWxUserInfoForBind(dto, resp);
            if (!resp.Success) return resp;

            //绑定AC系统企业微信用户
            this.BindQyWxUser(dto, ewcDataBind, resp);
            if (!resp.Success) return resp;

            //绑定运维系统企业微信用户
            this.BindOpsQyWxUser(ewcDataBind, resp);
            if (!resp.Success) return resp;


            //todo：3、自动登录现有企业

            //绑定后自动登录
            //this.AutoLogin(dto, ewcDataBind, resp);
            //if (!resp.Success) return resp;


            resp.Message = "切换成功！";
            resp.Success = true;
            return resp;
        }

        /// <summary>
        /// 解绑时获取企业微信用户信息
        /// </summary>
        private EwcCode2SessionData GetQyWxUserInfoForUnbind(ToggleCompanyDTO dto, BaseResponse<object> resp)
        {
            //调用企业微信临时登录凭证校验接口
            var ewcRes = EwcJsonClient.Invoke<EwcCode2SessionData, EwcCode2SessionDTO>(
                new EwcCode2SessionDTO()
                {
                    JS_Code = dto.Code
                });
            var ewcData = ewcRes.Data;
            if (!ewcRes.Success)
            {
                var errMsg = $"解绑用户时调用企业微信API失败：{ewcRes.Code}-{ewcRes.Msg}";
                this.LogService.Error(errMsg);
                resp.Message = errMsg;
                resp.Success = false;
                return ewcData;
            }

            if (ewcData.CorpId.IsNullOrEmptyOrWhiteSpace()
                || ewcData.Open_UserId.IsNullOrEmptyOrWhiteSpace()
                || ewcData.Session_Key.IsNullOrEmptyOrWhiteSpace())
            {
                this.LogService.Error($"解绑用户时调用企业微信API失败：{ewcData.ToJson()}");
                resp.Message = $"解绑用户时调用企业微信API失败：返回的企业微信用户信息为空！";
                resp.Success = false;
                return ewcData;
            }

            resp.Success = true;
            return ewcData;
        }

        /// <summary>
        /// 解绑AC系统企业微信用户
        /// </summary>
        private void UnBindQyWxUser(EwcCode2SessionData ewcData, BaseResponse<object> resp)
        {
            //此处根据企业微信用户的 openId 解绑
            //向认证站点发送请求
            var gateway = this.Container.GetService<IHttpServiceInvoker>();
            var acResponse = gateway.Invoke(null, TargetSEP.AuthService,
                new CommonFormDTO()
                {
                    FormId = "auth_user",
                    OperationNo = "UnBindQyWxUser",
                    SimpleData = new Dictionary<string, string>
                    {
                        { "userName", this.Context.UserName },
                        { "qyWxCustomerId", this.Context.QyWxCustomerId },
                        { "qyWxCompanyId", this.Context.Company },
                        { "qyWxCorpId", ewcData.CorpId },
                        { "qyWxOpenUserId", ewcData.Open_UserId }
                    }
                }) as DynamicDTOResponse;
            var result = acResponse.OperationResult;
            if (!result.IsSuccess)
            {
                var message = result.SimpleMessage;
                if (result.ComplexMessage.ErrorMessages.Any())
                {
                    message += "，" + string.Join("，", result.ComplexMessage.ErrorMessages);
                }
                resp.Message = $"解绑失败：{message}";
                resp.Success = false;
                return;
            }
            resp.Success = true;
        }

        /// <summary>
        /// 解绑运维系统企业微信用户
        /// </summary>
        private void UnBindOpsQyWxUser(EwcCode2SessionData ewcData, BaseResponse<object> resp)
        {
            //调用运维系统解绑微信用户
            var opsRes = OpsJsonClient.Post<object>(
                new OpsUnBindUserDTO()
                {
                    OpenId = ewcData.Open_UserId,
                    Code = this.Context.QyWxCustomerId
                });
            if (!opsRes.Success)
            {
                var errMsg = $"解绑用户时调用运维系统失败：{opsRes.Code}-{opsRes.Msg}";
                this.LogService.Error(errMsg);
                resp.Message = errMsg;
                resp.Success = false;
                return;
            }
            resp.Success = true;
        }

        /// <summary>
        /// 绑定时获取企业微信用户信息
        /// </summary>
        private EwcCode2SessionData GetQyWxUserInfoForBind(ToggleCompanyDTO dto, BaseResponse<object> resp)
        {
            //调用企业微信临时登录凭证校验接口
            var ewcRes = EwcJsonClient.Invoke<EwcCode2SessionData, EwcCode2SessionDTO>(
                new EwcCode2SessionDTO()
                {
                    JS_Code = dto.Code,
                    Code = this.Context.QyWxCustomerId
                });
            var ewcData = ewcRes.Data;
            if (!ewcRes.Success)
            {
                var errMsg = $"绑定用户时调用企业微信API失败：{ewcRes.Code}-{ewcRes.Msg}";
                this.LogService.Error(errMsg);
                resp.Message = errMsg;
                resp.Success = false;
                return ewcData;
            }

            if (ewcData.UserId.IsNullOrEmptyOrWhiteSpace()
                || ewcData.CorpId.IsNullOrEmptyOrWhiteSpace()
                || ewcData.Open_UserId.IsNullOrEmptyOrWhiteSpace()
                || ewcData.Session_Key.IsNullOrEmptyOrWhiteSpace())
            {
                this.LogService.Error($"绑定用户时调用企业微信API失败：{ewcData.ToJson()}");
                resp.Message = $"绑定用户时调用企业微信API失败：返回的企业微信用户信息为空！";
                resp.Success = false;
                return ewcData;
            }

            resp.Success = true;
            return ewcData;
        }

        /// <summary>
        /// 绑定AC系统企业微信用户
        /// </summary>
        private void BindQyWxUser(ToggleCompanyDTO dto, EwcCode2SessionData ewcData, BaseResponse<object> resp)
        {
            //执行绑定逻辑
            //向认证站点发送请求
            var gateway = this.Container.GetService<IHttpServiceInvoker>();
            var acResponse = gateway.Invoke(null, TargetSEP.AuthService,
                new CommonFormDTO()
                {
                    FormId = "auth_user",
                    OperationNo = "BindQyWxUser",
                    SimpleData = new Dictionary<string, string>
                    {
                        { "userName", this.Context.UserName },
                        { "qyWxCustomerId", this.Context.QyWxCustomerId },
                        { "qyWxCompanyId", dto.CompanyId },
                        { "qyWxUserId", ewcData.UserId },
                        { "qyWxCorpId", ewcData.CorpId },
                        { "qyWxOpenUserId", ewcData.Open_UserId }
                    }
                }) as DynamicDTOResponse;
            var result = acResponse.OperationResult;
            if (!result.IsSuccess)
            {
                var message = result.SimpleMessage;
                if (result.ComplexMessage.ErrorMessages.Any())
                {
                    message += "，" + string.Join("，", result.ComplexMessage.ErrorMessages);
                }
                resp.Message = $"绑定失败：{message}";
                resp.Success = false;
                return;
            }
            resp.Success = true;
        }

        /// <summary>
        /// 绑定运维系统企业微信用户
        /// </summary>
        private void BindOpsQyWxUser(EwcCode2SessionData ewcData, BaseResponse<object> resp)
        {
            //调用运维系统绑定微信用户
            var opsRes = OpsJsonClient.Post<object>(
                new OpsBindUserDTO(ewcData)
                {
                    Code = this.Context.QyWxCustomerId
                });
            if (!opsRes.Success)
            {
                var errMsg = $"绑定用户时调用运维系统失败：{opsRes.Code}-{opsRes.Msg}";
                this.LogService.Error(errMsg);
                resp.Message = errMsg;
                resp.Success = false;
                return;
            }
            resp.Success = true;
        }

        /// <summary>
        /// 绑定后自动登录
        /// </summary>
        private void AutoLogin(ToggleCompanyDTO dto, EwcCode2SessionData ewcData, BaseResponse<object> resp)
        {
            AuthenticateResponse response = null;
            try
            {
                //根据 userName 和 openId 实现自动登陆逻辑，并且返回用户登陆信息
                //此处把密码参数用来传递 openId，以便在现有登陆体系下，通过在参数尾部追加微信登陆标记来快速实现微信登陆
                //向麦浩系统发送登录请求
                response = JsonClient.Invoke<Authenticate, AuthenticateResponse>(
                    this.Request,
                    new Authenticate
                    {
                        UserName = this.Context.UserName,
                        Password = $"{WeiXinConsts.QyWxLoginSign}{ewcData.UserId}|{ewcData.CorpId}|{ewcData.Open_UserId}",
                        Meta = new Dictionary<string, string>
                        {
                            { "X-AppId", "qywxminiprogram" }, //企业微信小程序登录
                            { "X-Bind-QyWxCompanyId", dto.CompanyId } //小程序登录时如果有选择麦皓企业，则登录选择的企业
                        }
                    });
            }
            catch (Exception ex)
            {
                var stackTrace = ex.StackTrace;
                var message = ex.Message;
                var innerEx = ex.InnerException;
                if (innerEx != null)
                {
                    stackTrace = innerEx.StackTrace;
                    message = innerEx.Message;
                    if (innerEx is WebServiceException)
                    {
                        var wsEx = (innerEx as WebServiceException);
                        stackTrace = wsEx.StackTrace;
                        message = wsEx.ErrorMessage;
                    }
                }
                this.LogService.Error($"切换组织后自动登录失败：{stackTrace}");
                resp.Message = $"切换组织后自动登录失败：{message}";
                resp.Success = false;
                return;
            }

            resp.Success = true;
            resp.Data = new
            {
                userId = response?.UserId ?? "",
                userName = response?.UserName ?? "",
                displayName = response?.DisplayName ?? "",
                bearerToken = response?.BearerToken ?? "",
                sessionId = response?.SessionId ?? "",
                meta = response?.Meta ?? new Dictionary<string, string>()
            };
        }

        /// <summary>
        /// 校验参数
        /// </summary>
        private bool ValidateParam(ToggleCompanyDTO dto, BaseResponse<object> resp)
        {
            if (dto.Code.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = $"切换失败：微信用户登录凭证 {nameof(dto.Code)} 为空！";
                return false;
            }
            if (dto.CompanyId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = $"切换失败：当前要切换的企业标识 {nameof(dto.CompanyId)} 为空！";
                return false;
            }
            if (this.Context.QyWxCustomerId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = $"切换失败：Context.QyWxCustomerId 为空！";
                return false;
            }

            //获取AC系统用户信息
            var userAuth = UserHelper.GetCustomUserAuthByUserName(this.Context, this.Context.UserName);
            var company = userAuth?.Companys
                ?.FirstOrDefault(o => string.Equals(o.CompanyId, dto.CompanyId, StringComparison.OrdinalIgnoreCase));
            if (company == null)
            {
                resp.Message = $"切换失败：当前切换的企业 {dto.CompanyId} 不存在或者用户 {this.Context.UserName} 未加入该企业！";
                return false;
            }

            //检查当前用户在切换的企业中是否已被禁用
            var localUser = this.DBService.ExecuteDynamicObject(
                this.Context,
                "select top 1 fforbidstatus from t_sec_user where fmainorgid=@fmainorgid and fnumber=@fnumber",
                new SqlParam[]
                {
                    new SqlParam("@fmainorgid", System.Data.DbType.String, dto.CompanyId),
                    new SqlParam("@fnumber", System.Data.DbType.String, this.Context.UserName.Trim())
                })?.FirstOrDefault();
            if (localUser != null && Convert.ToString(localUser["fforbidstatus"]) == "1")
            {
                resp.Message = $"切换失败：用户 {this.Context.UserName} 在 {company.CompanyName} 中已禁用，请联系系统管理员进行处理！";
                return false;
            }

            //登录授权检查
            this.Context.LoginAuthorizationCheck();

            //验证是否子经销商
            var mainAgent = GetMaingAgent(this.Context, dto.CompanyId);
            if (!mainAgent.IsNullOrEmptyOrWhiteSpace()) 
            {
                resp.Message = $"温馨提示：当前登录组织为子经销商，仅允许查看数据！";
                resp.IsShowMessage = true;
            }

            return true;
        }
    }
}