using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using ServiceStack;
using System.Collections.Generic;
using JieNor.Framework.DataTransferObject.Poco;
using System.Data;
using JieNor.Framework.Interface;
using System.Linq;
using JieNor.Framework.SuperOrm;
using System;

namespace JieNor.AMS.YDJ.MP.API.Controller.BAS.Case
{
    public class CaseCopyController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(CaseCopyDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }
            //新建
            List<Dictionary<string, object>> fcaseentry = new List<Dictionary<string, object>>();
            var OldCaseObj = this.Context.GetBizDataById("ydj_case", dto.Id, true);//获取服务单模型
            if (OldCaseObj == null)
            {
                resp.Message = "案例单不存在或已被删除！";
                resp.Success = false;
                return resp;
            }
            string strSql = "select fid from t_ydj_case where fsourcenumber = @fnumber";
            var pars = new List<SqlParam>
                {
                    new SqlParam("@fnumber", DbType.String, Convert.ToString(OldCaseObj["fnumber"]))
                };
            var followFids = this.DBService.ExecuteDynamicObject(this.Context, strSql, pars);
            if (followFids.Count != 0)
            {
                resp.Message = "此案例已共享，不可重复共享！";
                resp.Success = false;
                return resp;
            }
            //获取案例单(案例详情)单据体
            var reservation = OldCaseObj["fcaseentry"] as DynamicObjectCollection;
            foreach (var item in reservation)
            {
                Dictionary<string, object> modelnew = new Dictionary<string, object>
                {
                    //写入图片
                    ["ffilestype"] = Convert.ToString(item["ffilestype"]),//文件类型
                    ["ffilename"] = Convert.ToString(item["ffilename"]),//文件名
                    ["fpicdescription"] = Convert.ToString(item["fpicdescription"]),//图片说明
                    ["foperationid"] = Convert.ToString(item["foperationid"]),//操作人
                    ["fuploaderid"] = Convert.ToString(item["fuploaderid"]),// 上传人id
                    ["ffileid"] = Convert.ToString(item["ffileid"]),// 文件id
                    ["flastupdate"] = Convert.ToString(item["flastupdate"]),// 最后更新时间
                    ["ffilesize"] = Convert.ToString(item["ffilesize"])// 大小
                };
                fcaseentry.Add(modelnew);
            }
            // 调用麦浩的接口
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ydj_case",
                    OperationNo = "save",
                    BillData = BuildBillData(OldCaseObj, fcaseentry),
                    Id = dto.Id
                });
            var result = response?.OperationResult;
            if (!result.IsSuccess)
            {
                resp.Message = "共享失败！";
                resp.Success = false;
                return resp;
            }
            resp.Message = "共享成功！";
            resp.Success = true;
            return resp;
        }

        private string BuildBillData(DynamicObject obj, List<Dictionary<string, object>> fcaseentry = null)
        {
            var data =
                new Dictionary<string, object>
                {
                    //{"fmainorgid", 0 },
                    //基本信息
                    { "fname", Convert.ToString(obj["fname"])+"(集团)"},
                    { "fstylistid", Convert.ToString(obj["fstylistid"]) },
                    { "fcustomerid",Convert.ToString(obj["fcustomerid"]) },
                    { "fdeptid", Convert.ToString(obj["fdeptid"]) },
                    { "fbuildingid", Convert.ToString(obj["fbuildingid"])},
                    { "fchannel", Convert.ToString(obj["fchannel"]) },
                    { "fremark", Convert.ToString(obj["fremark"]) },
                    { "fkeywords", Convert.ToString(obj["fkeywords"]) },
                    //户型
                    { "froom_enum", Convert.ToString(obj["froom_enum"])},
                    { "fhall_enum", Convert.ToString(obj["fhall_enum"]) },
                    { "ftoilet_enum", Convert.ToString(obj["ftoilet_enum"]) },
                    { "fbalcony_enum", Convert.ToString(obj["fbalcony_enum"]) },

                    //风格、空间、面积
                    { "fstyle", Convert.ToString(obj["fstyle"]) },
                    { "fspace", Convert.ToString(obj["fspace"]) },
                    { "farea", Convert.ToString(obj["farea"]) },

                    { "fkeywords", Convert.ToString(obj["fkeywords"]) },//关键字
                    { "flikenums", Convert.ToString(obj["flikenums"]) },//收藏数
                    { "fcasetype", "集团案例" },//案例类型
                    { "fvrlink", Convert.ToString(obj["fvrlink"]) },//vr地址
                    { "fsourcenumber", Convert.ToString(obj["fnumber"]) },//来源单码
                    { "fcasecategory", Convert.ToString(obj["fcasecategory"]) },//案例品类
                    { "fsenstatus", "send_status01" },//发布状态
                    //{ "FImageId", Convert.ToString(obj["FImageId"]) },//首页图片
                };
            if (fcaseentry != null && fcaseentry.Any())
            {
                data.Add("fcaseentry", fcaseentry);
            }

            var billData = (new List<Dictionary<string, object>> { data }).ToJson();

            return billData;
        }
    }
}
