using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.MP.API.Model;

namespace JieNor.AMS.YDJ.MP.API.Controller.BAS.StoreHouse
{
    /// <summary>
    /// 微信小程序：仓库列表取数接口
    /// </summary>
    public class StoreHouseListController : BaseController
    {
        /// <summary>
        /// 源单映射配置列表
        /// </summary>
        private Dictionary<string, Dictionary<string, string>> SourceFormMappings = new Dictionary<string, Dictionary<string, string>>
        {
            {
                "ydj_saleintention",
                new Dictionary<string, string>
                {
                    { "activeEntityKey", "fentity" },
                    { "productFieldKey", "fmaterialid" }
                }
            },
            {
                "ydj_order",
                new Dictionary<string, string>
                {
                    { "activeEntityKey", "fentry" },
                    { "productFieldKey", "fproductid" }
                }
            }
        };

        /// <summary>
        /// 当前预留操作关联的源单映射配置
        /// </summary>
        private Dictionary<string, string> SourceFormMapping { get; set; }

        /// <summary>
        /// 源单模型
        /// </summary>
        private HtmlForm SourceForm { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(StoreHouseListDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseListPageData<StoreHouseListModel>>();
            resp.Data.List = new List<StoreHouseListModel>();

            //获取仓库数据列表
            DynamicObjectCollection storeHouseList = GetStoreHouseList(dto);

            //获取总记录数
            int total = GetTotal(dto);

            if (storeHouseList == null || !storeHouseList.Any())
            {
                resp.Success = true;
                resp.Message = "暂无仓库数据！";
                return resp;
            }

            var storeHouses = new List<StoreHouseListModel>();
            foreach (var storeHouse in storeHouseList)
            {
                //默认库存状态
                var fstockid = Convert.ToString(storeHouse["fstockid"]);

                BaseDataSimpleModel stockStatusModel = new BaseDataSimpleModel();
                if (!fstockid.IsNullOrEmptyOrWhiteSpace())
                {
                    var stockStatusObj = this.Context.GetBizDataById("ydj_stockstatus", fstockid);
                    stockStatusModel.Id = Convert.ToString(stockStatusObj["id"]);
                    stockStatusModel.Name = Convert.ToString(stockStatusObj["fname"]);
                }


                //仓库
                storeHouses.Add(new StoreHouseListModel
                {
                    Id = Convert.ToString(storeHouse["fid"]),
                    Number = Convert.ToString(storeHouse["fnumber"]),
                    Name = Convert.ToString(storeHouse["fname"]),
                    StockStatus = stockStatusModel

                });
            }

            resp.Data.List = storeHouses;

            ////加载每个仓库仓位的库存量和可预留量
            //this.LoadStoreHouseQty(dto, resp);

            //if (!resp.Success) return resp;

            resp.Message = "取数成功！";
            resp.Success = true;
            resp.Data.TotalRecord = total;
            resp.Data.PageSize = dto.PageSize;
            return resp;
        }

        /// <summary>
        /// 获取总记录数
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        private int GetTotal(StoreHouseListDTO dto)
        {
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
            };
            var wheresql = "";
            if (!dto.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                wheresql += $@" and (fname like @key or fnumber like @key)";
                sqlParam.Add(new SqlParam("@key", System.Data.DbType.String, $@"%{dto.Keyword}%"));
            }
            var sql = $@"select count(1) total from T_YDJ_STOREHOUSE with(nolock) where fmainorgid=@fmainorgid and fforbidstatus='0' {wheresql} ";

            var res = this.DBService.ExecuteDynamicObject(Context, sql, sqlParam);
            if (res != null && res.Any())
            {
                return Convert.ToInt32(res[0]["total"]);
            }
            else
            {
                return 0;
            }
        }

        //获取仓库列表
        private DynamicObjectCollection GetStoreHouseList(StoreHouseListDTO dto)
        {
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
            };
            var wheresql = "";
            if (!dto.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                wheresql += $@" and (fname like @key or fnumber like @key)";
                sqlParam.Add(new SqlParam("@key", System.Data.DbType.String, $@"%{dto.Keyword}%"));
            }
            var sql = $@" select rownum,fid,fnumber,fname,fstockid from (select  ROW_NUMBER() over(order by fid) rownum,fid,fnumber,fname,fstockid from T_YDJ_STOREHOUSE with(nolock)  where fmainorgid=@fmainorgid and fforbidstatus='0' {wheresql})t where t.rownum between {(dto.PageIndex - 1) * dto.PageSize + 1} and {dto.PageIndex * dto.PageSize}";

            var res = this.DBService.ExecuteDynamicObject(Context, sql, sqlParam);
            return res;
        }

        ///// <summary>
        ///// 加载每个仓库仓位的库存量和可预留量
        ///// </summary>
        //private void LoadStoreHouseQty(StoreHouseListDTO dto, BaseResponse<BaseListPageData<StoreHouseListModel>> resp)
        //{
        //    if (dto.SourceFormId.IsNullOrEmptyOrWhiteSpace()
        //        || dto.SourceOrderId.IsNullOrEmptyOrWhiteSpace()
        //        || dto.SourceEntryId.IsNullOrEmptyOrWhiteSpace())
        //    {
        //        resp.Success = true;
        //        return;
        //    }

        //    Dictionary<string, string> sourceFormMapping = null;
        //    this.SourceFormMappings.TryGetValue(dto.SourceFormId, out sourceFormMapping);
        //    if (sourceFormMapping == null)
        //    {
        //        resp.Success = false;
        //        resp.Message = $"业务单据 {dto.SourceFormId} 暂不支持预留，无法匹配可预留量！";
        //        return;
        //    }
        //    this.SourceFormMapping = sourceFormMapping;

        //    this.SourceForm = this.MetaModelService.LoadFormModel(this.Context, dto.SourceFormId);
        //    var sourceFormDt = this.SourceForm.GetDynamicObjectType(this.Context);

        //    //源单数据包
        //    var dm = this.GetDataManager();
        //    dm.InitDbContext(this.Context, sourceFormDt);
        //    var sourceBill = dm.Select(dto.SourceOrderId) as DynamicObject;
        //    if (sourceBill == null)
        //    {
        //        resp.Success = false;
        //        resp.Message = $"{this.SourceForm.Caption}【{dto.SourceOrderId}】不存在或已被删除，无法匹配可预留量！";
        //        return;
        //    }

        //    //构建预留需求动态表单数据包
        //    var demandObj = this.BuildReserveDemandData(dto, sourceBill, resp);
        //    if (demandObj == null) return;

        //    //调用麦浩PC端已有的接口
        //    var response = JsonClient.Invoke<CommonBillDTO, CommonBillDTOResponse>(
        //        this.Request,
        //        new CommonBillDTO()
        //        {
        //            FormId = "stk_reservedialog",
        //            OperationNo = "loadstockqty",
        //            BillData = new List<DynamicObject> { demandObj }.ToJson()
        //        });
        //    var result = response?.OperationResult;
        //    if (!result.IsSuccess)
        //    {
        //        var errMsg = "麦浩预留加载库存数量接口出错！";
        //        var errMsgs = result.ComplexMessage.ErrorMessages;
        //        if (errMsgs.Count > 0)
        //        {
        //            errMsg = string.Join("，", errMsgs);
        //        }
        //        resp.Message = errMsg;
        //        resp.Success = false;
        //        return;
        //    }

        //    //数据结构转换
        //    var srvData = (result.SrvData as string).FromJson<Dictionary<string, List<Dictionary<string, object>>>>();
        //    var entrys = srvData.GetValue("fentry");
        //    if (entrys != null)
        //    {
        //        foreach (var house in resp.Data.List)
        //        {
        //            //每个仓库的库存量和可预留量
        //            var houseEntrys = entrys.Where(o => Convert.ToString(o["fstorehouseid"]).EqualsIgnoreCase(house.Id));
        //            house.StockQty = houseEntrys.Sum(o => Convert.ToDecimal(o["finventoryqty"]));
        //            house.CanReserveQty = houseEntrys.Sum(o => Convert.ToDecimal(o["fcanreserveqty"]));
        //        }
        //    }

        //    resp.Success = true;
        //}

        ///// <summary>
        ///// 构建预留需求动态表单数据包
        ///// </summary>
        //private DynamicObject BuildReserveDemandData(StoreHouseListDTO dto, DynamicObject sourceBill, BaseResponse<BaseListPageData<StoreHouseListModel>> resp)
        //{
        //    var sourceEntity = this.SourceForm.GetEntryEntity(this.SourceFormMapping["activeEntityKey"]);
        //    var productFieldKey = this.SourceFormMapping["productFieldKey"];

        //    //源单明细
        //    var sourceEntrys = sourceEntity.DynamicProperty?.GetValue<DynamicObjectCollection>(sourceBill);
        //    var sourceEntry = sourceEntrys.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(dto.SourceEntryId));
        //    if (sourceEntry == null)
        //    {
        //        resp.Success = false;
        //        resp.Message = $"业务单据 {dto.SourceFormId} 明细行不存在 {dto.SourceEntryId}，无法匹配可预留量！";
        //        return null;
        //    }

        //    //预留需求动态表单模型
        //    var reserveDemandForm = this.MetaModelService.LoadFormModel(this.Context, "stk_reservedialog");
        //    var demandDt = reserveDemandForm.GetDynamicObjectType(this.Context);
        //    var demandEntity = reserveDemandForm.GetEntryEntity("fentry");

        //    //预留对象默认取源单上的客户
        //    var demandObj = new DynamicObject(demandDt);
        //    demandObj["freserveobjecttype"] = "ydj_customer";
        //    demandObj["freserveobjectid"] = Convert.ToString(sourceBill["fcustomerid"]);
        //    demandObj["fsourcetype"] = dto.SourceFormId;
        //    demandObj["fsourcenumber"] = sourceBill["fbillno"];

        //    //需求明细
        //    var demandEntrys = demandEntity.DynamicProperty?.GetValue<DynamicObjectCollection>(demandObj);

        //    foreach (var house in resp.Data.List)
        //    {
        //        var demandEntry = demandEntity.DynamicObjectType.CreateInstance() as DynamicObject;
        //        demandEntry["fmaterialid"] = sourceEntry[$"{productFieldKey}"];
        //        demandEntry["fattrinfo"] = sourceEntry["fattrinfo"];
        //        demandEntry["fcustomdesc"] = sourceEntry["fcustomdes_e"];
        //        demandEntry["funitid"] = sourceEntry["funitid"];
        //        demandEntry["fstorehouseid"] = house.Id;
        //        demandEntry["fstockstatus"] = sourceEntry["fstockstatus"];
        //        demandEntry["fmtono"] = sourceEntry["fmtono"];
        //        demandEntry["fsourceentryid"] = sourceEntry["id"];
        //        demandEntrys.Add(demandEntry);
        //    }

        //    return demandObj;
        //}
    }
}