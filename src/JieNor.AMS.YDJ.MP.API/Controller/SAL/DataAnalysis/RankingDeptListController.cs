using JieNor.AMS.YDJ.MP.API.DTO.SAL.DataAnalysis;
using JieNor.AMS.YDJ.MP.API.Model.SAL.DataAnalysis;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.SAL.DataAnalysis
{
    /// <summary>
    /// 统计分析-部门排名列表
    /// </summary>
    public class RankingDeptListController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(RankingDeptListDto dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            //参数验证
            if (!CalibrationParas(dto, resp))
                return resp;

            //执行SQL
            StringBuilder strSql = new StringBuilder();
            //RowsSQL
            StringBuilder strRowsSql = new StringBuilder();

            //查询SQL组装
            GetSql(dto, strSql, strRowsSql);

            var list = new List<RankingDeptListModel>();
            var item = this.DBService.ExecuteDynamicObject(this.Context, strSql.ToString());
            foreach (var val in item)
            {
                list.Add(new RankingDeptListModel(
                    JNConvert.ToStringAndTrim(val["fstaffid"]),
                    JNConvert.ToStringAndTrim(val["fname"]),
                     JNConvert.ToStringAndTrim(val["deptname"]),
                      Convert.ToDecimal(val["amount_audit"]),
                      Convert.ToDecimal(val["amount_noaudit"]),
                      JNConvert.ToStringAndTrim(val["fimage"]).GetSignedFileUrl(true)
                      ));
            }

            int rows = Convert.ToInt32(this.DBService.ExecuteDynamicObject(this.Context, strRowsSql.ToString())[0]["count"]);

            int totalPage = rows > 0 ? 1 : 0;
            if (rows > dto.PageSize && dto.PageSize > 0)
                totalPage = rows % dto.PageSize != 0 ? (rows / dto.PageSize) + 1 : rows / dto.PageSize;

            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Data = new { TotalRecord = rows, totalPage = totalPage, List = list };

            return resp;
        }

        /// <summary>
        /// 查询组装
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="strSql"></param>
        /// <param name="joinTb1"></param>
        /// <param name="joinTb2"></param>
        /// <param name="strWhere"></param>
        private void GetSql(RankingDeptListDto dto, StringBuilder strSql, StringBuilder strRowsSql)
        {
            //条件
            StringBuilder whereT2 = new StringBuilder();
            StringBuilder whereA2 = new StringBuilder();
            //时间表1
            StringBuilder sT1 = new StringBuilder();
            StringBuilder sA1 = new StringBuilder();
            sT1.Append(string.Format(@" and t1.forderdate>='{0}' and t1.forderdate<='{1}'", dto.StartDate, dto.EndDate));
            sA1.Append(string.Format(@" and a1.forderdate>='{0}' and a1.forderdate<='{1}'", dto.StartDate, dto.EndDate));

            //分页条件
            StringBuilder pageWhere = new StringBuilder();
            pageWhere.Append($@" and fid>={dto.PageIndex * dto.PageSize - dto.PageSize + 1} and fid<={dto.PageIndex * dto.PageSize}");

            strSql.Append($@"select * from (select row_number() over(order by rowid) fid,* from (
					select tb.*,s1.fname,u.fimage,s2.fname deptname from (
					select row_number() over(order by sum(t1.fdealamount) desc) rowid ,t1.fstaffid,t1.fdeptid,
					sum(t1.fdealamount) amount_audit,
					(select isnull(sum(a1.fdealamount),0) from t_ydj_order a1 with(nolock)
					where a1.fstatus!='E' and a1.fcancelstatus=0 {sA1} {whereA2}
					) amount_noaudit
					from t_ydj_order t1 with(nolock) where t1.fstatus='E' and t1.fcancelstatus=0 {sT1} {whereT2}
					group by t1.fstaffid,t1.fdeptid) tb 
					left join t_bd_staff s1 with(nolock) on tb.fstaffid=s1.fid
					left join t_sec_user u with(nolock) on u.fid=s1.flinkuserid
					left join T_BD_DEPARTMENT s2 with(nolock) on tb.fdeptid = s2.fid
					) ta) ss where 1=1 {pageWhere}");

            strRowsSql.Append($@"select count(1) count from (
					select tb.*,s1.fname,u.fimage,s2.fname deptname from (
					select row_number() over(order by sum(t1.fdealamount) desc) rowid ,t1.fstaffid,t1.fdeptid,
					sum(t1.fdealamount) amount_audit,
					(select isnull(sum(a1.fdealamount),0) from t_ydj_order a1 with(nolock)
					where a1.fstatus!='E' and a1.fcancelstatus=0 {sA1} {whereA2}
					) amount_noaudit
					from t_ydj_order t1 with(nolock) where t1.fstatus='E' and t1.fcancelstatus=0 {sT1} {whereT2}
					group by t1.fstaffid,t1.fdeptid) tb 
					left join t_bd_staff s1 with(nolock) on tb.fstaffid=s1.fid
					left join t_sec_user u with(nolock) on u.fid=s1.flinkuserid
					left join T_BD_DEPARTMENT s2 with(nolock) on tb.fdeptid = s2.fid
					) ta");
        }

        /// <summary>
        /// 参数验证
        /// </summary>
        /// <returns></returns>
        private bool CalibrationParas(RankingDeptListDto dto, BaseResponse<object> resp)
        {

            if (dto.TimeType.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"时间类型 TimeType 不能为空！";
                return false;
            }

            DateTime t1 = DateTime.Now;
            DateTime t2 = DateTime.Now;
            if (dto.TimeType != "自定义")
            {
                TimeHelp.GetDateByType(dto.TimeType, out t1, out t2);
                dto.StartDate = t1;
                dto.EndDate = t2;
                return true;
            }


            if (!DateTime.TryParse(dto.StartDate.ToString(), out t1))
            {
                resp.Success = false;
                resp.Message = $"开始日期 StartDate 不能为空！";
                return false;
            }

            if (!DateTime.TryParse(dto.EndDate.ToString(), out t2))
            {
                resp.Success = false;
                resp.Message = $"结束日期 EndDate 不能为空！";
                return false;
            }

            return true;
        }
    }
}