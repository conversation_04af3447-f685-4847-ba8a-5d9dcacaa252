using JieNor.AMS.YDJ.MP.API.Config;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using ServiceStack;
using ServiceStack.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Filter
{
    /// <summary>
    /// 企业微信请求过滤器：用于拦截来自企业微信WebAPI发起的请求
    /// </summary>
    public class EnterpriseWeChatFilter : RequestFilterAttribute
    {
        /// <summary>
        /// 执行过滤器逻辑
        /// </summary>
        /// <param name="req"></param>
        /// <param name="res"></param>
        /// <param name="requestDto"></param>
        public override void Execute(IRequest req, IResponse res, object requestDto)
        {
            //todo:对请求进行验权
            var ewcDto = requestDto as EwcBaseDTO;
            if (ewcDto == null)
            {
                res.StatusCode = (int)HttpStatusCode.BadRequest;
                res.StatusDescription = "当前请求的 DTO 不是 EwcBaseDTO 的派生类，请检查！";
                return;
            }
            if (ewcDto.Signature.IsNullOrEmptyOrWhiteSpace()
                || ewcDto.Timestamp.IsNullOrEmptyOrWhiteSpace()
                || ewcDto.Nonce.IsNullOrEmptyOrWhiteSpace()
                || ewcDto.Code.IsNullOrEmptyOrWhiteSpace())
            {
                res.StatusCode = (int)HttpStatusCode.BadRequest;
                res.StatusDescription = $"请求参数（signature、nonce、timestamp、companyid）错误，请检查！";
                return;
            }

            //客户密钥
            var secret = ewcDto.Code.GetCustomerSecretKey();
            if (secret.IsNullOrEmptyOrWhiteSpace())
            {
                res.StatusCode = (int)HttpStatusCode.BadRequest;
                res.StatusDescription = $"根据 {nameof(ewcDto.Code)} 参数值 {ewcDto.Code} 获取不到客户密钥，请检查 customer.json 配置文件！";
                return;
            }

            //验证签名
            if (!CheckSignature.Check(ewcDto.Signature, ewcDto.Timestamp, ewcDto.Nonce, secret))
            {
                res.StatusCode = (int)HttpStatusCode.Unauthorized;
                res.StatusDescription = $"签名认证失败，请检查！";
                return;
            }
        }
    }
}