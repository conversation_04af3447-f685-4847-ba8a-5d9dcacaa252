using JieNor.Framework;
using JieNor.Framework.Interface.Cache;
using ServiceStack;
using ServiceStack.Web;
using System;
using System.Linq;
using System.Net;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using Newtonsoft.Json.Linq;
using ServiceStack.Caching;

namespace JieNor.AMS.YDJ.MP.API.Filter
{
    /// <summary>
    /// 重复提交过滤器：用于拦截重复提交并根据流水号返回单据
    /// 注：DTO 要继承 RepeatedSubmitDTO 类 
    /// </summary>
    public class RepeatedSubmitFilter : BaseRequestFilter
    {
        /// <summary>
        /// 单据标识
        /// </summary>
        protected string FormId { get; set; }

        public RepeatedSubmitFilter(string formId)
        {
            FormId = formId;
        }

        /// <summary>
        /// 执行过滤器逻辑
        /// </summary>
        /// <param name="req"></param>
        /// <param name="res"></param>
        /// <param name="requestDto"></param>
        public override void Execute(IRequest req, IResponse res, object requestDto)
        {
            var repeatedSubmitDto = requestDto as RepeatedSubmitDTO;
            // 如果不是 RepeatedSubmitDTO 或者 不是新建（即Id参数不为空）跳过
            if (repeatedSubmitDto == null || !repeatedSubmitDto.Id.IsNullOrEmptyOrWhiteSpace()) return;

            base.Execute(req, res, requestDto);

            string hash, cacheKey, cacheValue;

            // 流水号
            string tranId = req.Headers["YDJ-TranId"];
            if (tranId.IsNullOrEmptyOrWhiteSpace())
            {
                // 先到缓存里找
                hash = requestDto.ToJson().HashString();
                cacheKey = GetCacheKey(hash);
                cacheValue = this.CacheClient.Get<string>(cacheKey);

                // 如果缓存里没有，就生成一个
                if (cacheValue.IsNullOrEmptyOrWhiteSpace())
                {
                    // 获取流水号
                    ISequenceService seqSvc = this.Container.GetService<ISequenceService>();
                    tranId = seqSvc.GetSequence<string>();

                    // 缓存起来，10分钟
                    this.CacheClient.Set(cacheKey, tranId, new TimeSpan(0, 0, 10, 0));

                    // 初次提交 或 已超过缓存时间 跳出
                    repeatedSubmitDto.TranId = tranId;
                    repeatedSubmitDto.Hash = hash;
                    return;
                }

                // 将缓存值赋值给 tranId
                tranId = cacheValue;
            }
            else
            {
                hash = tranId;

                // 判断缓存是否在，如果不在，就跳出
                cacheKey = GetCacheKey(hash);
                cacheValue = this.CacheClient.Get<string>(cacheKey);

                // 如果缓存里没有，就缓存起来
                if (cacheValue.IsNullOrEmptyOrWhiteSpace())
                {
                    // 缓存起来，10分钟
                    this.CacheClient.Set(cacheKey, tranId, new TimeSpan(0, 0, 10, 0));

                    this.LogService.Info($"【{FormId}】【tranId：{tranId}】：初始提交");

                    // 初次提交 或 已超过缓存时间 跳出
                    repeatedSubmitDto.TranId = tranId;
                    repeatedSubmitDto.Hash = hash;
                    return;
                }
            }

            // 判断单据是否存在
            var htmlForm = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, FormId);
            var tranIdField = htmlForm.GetField(htmlForm.TranFldKey);
            var billObj = this.Context.LoadBizDataByFilter(FormId, $" fmainorgid='{this.Context.Company}' and {tranIdField.FieldName}='{tranId}'").FirstOrDefault();
            if (billObj != null)
            {
                // 如果存在单据，则返回成功
                this.Response.StatusCode = (int)HttpStatusCode.OK;
                this.Response.ContentType = "application/json; charset=utf-8";

                var resp = new BaseResponse<BaseDataModel>();
                resp.Data = new BaseDataModel
                {
                    Id = Convert.ToString(billObj["Id"]),
                    Number = Convert.ToString(billObj[htmlForm.GetField(htmlForm.NumberFldKey).FieldName]),
                    CreateDate = Convert.ToDateTime(billObj["fcreatedate"]),
                    Status = new ComboDataModel
                    {
                        Id = Convert.ToString(billObj["fstatus"])
                    }
                };
                resp.Success = true;
                resp.Message = "数据已保存！";

                res.ContentType = "application/json; charset=utf-8";
                res.Write(resp.ToJson());
                res.EndRequest(false);

                this.LogService.Info($"【{FormId}】【tranId：{tranId}】：{resp.Message}");
            }
            else // 说明还在缓存
            {
                res.StatusCode = (int)HttpStatusCode.BadRequest;
                res.StatusDescription = "数据不允许重复提交，请等待系统处理，请稍后再试！";

                var resp = new BaseResponse<object>();
                resp.Message = "数据不允许重复提交，请等待系统处理，请稍后再试！";

                res.ContentType = "application/json; charset=utf-8";
                res.Write(resp.ToJson());
                res.EndRequest(false);

                this.LogService.Info($"【{FormId}】【tranId：{tranId}】：{resp.Message}");
            }
        }

        private string GetCacheKey(string hash)
        {
            string cacheKey = $"MPAPI:RepeatedSubmit:{this.Context.Company}:{FormId}:{hash}";

            return cacheKey;
        }
    }

    /// <summary>
    /// 重复提交响应过滤器：用于错误时，清空缓存，不影响下次请求
    /// </summary>
    public class RepeatedSubmitResponseFilter : ResponseFilterAttribute
    {
        /// <summary>
        /// 单据标识
        /// </summary>
        protected string FormId { get; set; }

        /// <summary>
        /// 服务容器
        /// </summary>
        protected IServiceContainer Container { get; private set; }

        /// <summary>
        /// 用户上下文
        /// </summary>
        protected UserContext Context { get; private set; }

        /// <summary>
        /// 日志服务：直接写文件
        /// </summary>
        protected ILogServiceEx LogService { get; private set; }

        /// <summary>
        /// Redis缓存服务
        /// </summary>
        protected IRedisCache CacheClient { get; private set; }

        protected IRequest Request { get; set; }

        protected IResponse Response { get; set; }

        public RepeatedSubmitResponseFilter(string formId)
        {
            FormId = formId;
        }

        public override void Execute(IRequest req, IResponse res, object responseDto)
        {
            this.Request = req;
            this.Response = res;

            var repeatedSubmitDto = this.Request.Dto as RepeatedSubmitDTO;
            // 非 RepeatedSubmitDTO 跳过
            if (repeatedSubmitDto == null) return;

            if (!(responseDto is Exception))
            {
                var responseJson = JObject.FromObject(responseDto);
                // 操作成功 跳过
                if (responseJson.GetJsonValue("Success", false) || responseJson.GetJsonValue("success", false)) return;
            }

            //请求的生命周期标识
            var lifetimeScopeId = Guid.NewGuid().ToString();

            this.Container = Service.GlobalResolver.TryResolve<IServiceContainer>().BeginLifetimeScope(lifetimeScopeId);

            //初始化用户上下文
            this.Context = InitUserContext(this.Request.Dto);

            //设置请求标识
            this.Context.RequestId = lifetimeScopeId;

            this.CacheClient = this.Container.GetService<IRedisCache>();
            this.LogService = this.Container.GetService<ILogServiceEx>();

            string hash = repeatedSubmitDto.Hash;
            string cacheKey = GetCacheKey(hash);

            // 清除缓存
            this.CacheClient.Remove(cacheKey);

            this.LogService.Info($"【{FormId}】【tranId：{hash}】：清空缓存！");
        }

        private string GetCacheKey(string hash)
        {
            string cacheKey = $"MPAPI:RepeatedSubmit:{this.Context.Company}:{FormId}:{hash}";

            return cacheKey;
        }

        /// <summary>
        /// 初始化用户上下文
        /// </summary>
        /// <param name="reqDto"></param>
        /// <returns></returns>
        protected UserContext InitUserContext(object reqDto)
        {
            var cacheClient = this.Container.GetService<ICacheClient>();
            var session = SessionFeature.GetOrCreateSession<UserAuthTicket>(cacheClient, this.Request, this.Response);//this.SessionAs<UserAuthTicket>();
            if (session == null)
            {
                throw HttpError.Unauthorized("您没有权限访问此链接！");
            }
            session.CurrentRequestObject = reqDto;
            session.Device = "qywxminiprogram"; // 小程序端

            var userCtx = new UserContext();
            userCtx.Container = this.Container;
            userCtx.SetUserSession(session);

            if (this.Request.UrlReferrer?.AbsoluteUri?.IndexOf("/shell.html?") > 0)
            {
                userCtx.IsTempToken = true;
            }
            else
            {
                userCtx.IsTempToken = false;
            }

            return userCtx;
        }
    }
}