该项目为易到家企业微信小程序API专用项目。

以下是对项目目录结构的简要说明：

JieNor.AMS.YDJ.MP.API
	|
	|--Config 存放配置相关类
	|
	|--Controller 存放小程序接口控制器
		|
		|--Assist 辅助资料相关，比如：辅助资料、简单下拉框
		|
		|--Auth 认证授权相关，比如：登录、注册
		|
		|--BD 基础资料相关
			|
			|--Activity 营销活动
			|
			|--BillType 单据类型
			|
			|--Casus 经典案例
			|
			|--Channel 渠道伙伴
			|
			|--Customer 客户
			|
			|--Dept 部门
			|
			|--House 小区楼盘
			|
			|--Product 商品
			|
			|--Staff 员工
			|
		|--EWC 企业微信发起的请求接口控制器，EnterpriseWeChat 简写为 EWC
			|
		|--IM 即时通讯
			|
			|--Notice 企业公告
			|
		|--Report 报表相关，比如：今日数据、本月数据、业绩目标
			|
		|--BaseController.cs 小程序接口控制器基类
			|
	|
	|--DTO 存放小程序接口数据传输对象
		|
		|--Auth 认证授权相关
		|
		|--BD 基础资料相关
	|
	|--Filter 存放过滤器
	|
	|--Model 存放小程序接口数据模型
	|
	|--Plugin 存放小程序麦浩后端表单插件
	|
	|--Response 存放小程序接口响应对象
	|
	|--Utils 存放通用工具类