using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.MP.API.Plugin.BD.Product
{
    /// <summary>
    /// 微信小程序：商品详情取数接口
    /// </summary>
    [InjectService]
    [FormId("ydj_product")]
    [OperationNo("getproductdetail")]
    public class GetProductDetailPlugIn : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            //唯一标识
            var id = this.GetQueryOrSimpleParam<string>("id");
            if (id.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentException("参数 id 为空，请检查！");
            }

            this.Result.SrvData = JsonConvert.SerializeObject(new 
            {

            });
            this.Result.IsSuccess = true;
        }
    }
}