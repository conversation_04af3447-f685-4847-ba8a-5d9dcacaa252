using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Response.YDJ.STE.Order.LoadSettleInfo
{
    public class LoadSettleInfoResponse
    {
        public string fsourceformid { get; set; }
        public string fsourceid { get; set; }
        public string fsourcenumber { get; set; }
        public string fsourcetranid { get; set; }
        public string fsettlemaintype { get; set; }
        public string fsettlemainid { get; set; }
        public string fsettlemainname { get; set; }
        public Fcustomerid fcustomerid { get; set; }
        //public decimal fconfirmamount { get; set; }
        public string fdeptid { get; set; }
        public bool fissyn { get; set; }
        /// <summary>
        /// 已收款
        /// </summary>
        public string fsettledamount { get; set; }
        /// <summary>
        /// 未收款
        /// </summary>
        public string funsettleamount { get; set; }
        /// <summary>
        /// 确认已收
        /// </summary>
        public string fconfirmedamount { get; set; }
        /// <summary>
        /// 订单金额
        /// </summary>
        public string fsumamount { get; set; }
        //public Fallaccount[] fallaccounts { get; set; }
        public List<synBankNumModel> synBankNum { get; set; }

        public string freceivabletobeconfirmed { get; set; }

        /// <summary>
        /// 已扣佣金
        /// </summary>
        public string freducedbrokerage { get; set; }

        /// <summary>
        /// 佣金余额，【已扣佣金】不能超过【佣金余额】
        /// </summary>
        public string fbrokeragebalance { get; set; }
        public decimal amount { get; set; }
    }

    public class Fcustomerid
    {
        public string fphone { get; set; }
        public string femail { get; set; }
        public string flegalperson { get; set; }
        public string fcardid { get; set; }
        public string fbank { get; set; }
        public string faccountname { get; set; }
        public string fbanknumber { get; set; }
        public string fcontacts { get; set; }
        public string id { get; set; }
        public string fnumber { get; set; }
        public string fname { get; set; }
    }

    public class Fallaccount
    {
        public string accountId { get; set; }
        public string accountName { get; set; }
        public bool canUseInOrderPay { get; set; }
        public bool canRecharge { get; set; }
        public bool noConfirmInPay { get; set; }
        public bool balanceInUnConfirm { get; set; }
        public bool isCash { get; set; }
        public string creditLimit { get; set; }
        public string balance { get; set; }
        public string settleAmount { get; set; }
        public string availableCredit { get; set; }
        public string settlementRatio { get; set; }
    }

    public class synBankNumModel
    {
        public string accountId { get; set; }
        public string accountName { get; set; }
        public string bankNum { get; set; }
        public string bankName { get; set; }
    }

}
