using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.DataTransferObject.Sys;

namespace JieNor.AMS.YDJ.MP.API
{
    /// <summary>
    /// 接口响应结果基类
    /// </summary>
    /// <typeparam name="T">服务端数据泛型类型</typeparam>
    public class BaseResponse<T> : BaseResponse where T : class, new()
    {
        /// <summary>
        /// 接口响应结果基类
        /// </summary>
        public BaseResponse()
        {
            this.Data = new T();
        }

        public BaseResponse(string error)
        {
            this.Message = error;
            this.Success = false;
        }

        public BaseResponse(string error, int code)
        {
            this.Message = error;
            this.Success = false;
            this.Code = code;
        }

        /// <summary>
        /// 服务端数据：数据结构由实际的接口确定。
        /// </summary>
        public T Data { get; set; }

        public BaseResponse<T> Error(string error, int code = 0)
        {
            this.Success = false;
            this.Message = error;
            this.Code = code;

            return this;
        }
    }

    public class BaseResponse
    {
        /// <summary>
        /// 成功标记：成功为 true，不成功为 false。
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 服务端消息：可以是普通消息、警告消息、成功消息、错误消息 等其它消息。
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 是否需要显示消息结果
        /// </summary>
        public bool IsShowMessage { get; set; }

        /// <summary>
        /// 服务端状态码：可能是HTTP状态码或者业务状态码。
        /// </summary>
        public int Code { get; set; }

        /// <summary>
        /// 锁token
        /// </summary>
        public LockTokenInfo LockTokenInfo { get; set; }

        /// <summary>
        /// 是否锁定
        /// </summary>
        public bool IsLock { get; set; }
    }
}