#短消息服务配置，内部开发可以放开以下参数进行测试
aliyun:sms.accessid LTAIS3oJhs3uaUCQ
aliyun:sms.accesskey aOZnWOg9igyjbEBEYP4nylJblhiaZF
aliyun:sms.endpoint http://1038880401653050.mns.cn-shenzhen.aliyuncs.com/
aliyun:sms.topic sms.topic-cn-shenzhen
aliyun:sms.signname 易到家
aliyun:sms.signname.zys 左右手
aliyun:sms.templatecode SMS_61665244
#服务单【师傅接单确认】短信模板标识
aliyun:sms.service.setstatus03 SMS_76480038
#服务单【师傅预约确认】短信模板标识
aliyun:sms.service.setstatus07 SMS_76455037
#服务单【上门提醒操作】短信模板标识
#aliyun:sms.service.smtx XXXXXX

#配置缓存的读写分离信息
fw:redis.masterhost <EMAIL>:6382
fw:redis.slavehost <EMAIL>:6382

#消息队列的主机名用户及密码配置
fw:rabbitmq.host www.jienor.com
fw:rabbitmq.username jienor
fw:rabbitmq.password jienor.com

#站点email信使配置
fw:mail.pop3 pop.exmail.qq.com
fw:mail.smtp smtp.exmail.qq.com
fw:mail.username <EMAIL>
fw:mail.password Jn@12345

#默认登录页面地址
fw.defaultpage /views/account/login.html
#默认站点页面视图文件存放目录
fw.defaultviewdir /views
#默认站点页面模型文件存放目录
fw.defaultmdldir /mdl
#默认站点页面控件文件存放目录
fw.defaultjsdir /fw/js

#微服务参数配置：网关，消息中继模式（central或p2p）
ms.gateway http://eis.yidaohome.com/
#微服务网关访问验证码
ms.gateway.authcode ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

#用于微信公众平台的配置
ms.wx.serverappid.ydj wxffc52eaf22135e99
ms.wx.serverappsecret.ydj dcc2f047491256ad17c22591e98a56ee
ms.wx.encodingaeskey.ydj Va35REeR0bwiEaWIbub67iX2SRfjpHeCcWfHZsKFw68
ms.wx.wxtoken.ydj yidaohome

#用于微信前端调用后端接口的临时令牌
ms.xauth.type wx
ms.xauth.alias.xxxk3k3ssk33k5k6tk3oks2slsl2xxksd ydj
ms.wx.server.ydj http://m.jienor.cn
ms.wx.token.ydj ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
ms.wx.company.ydj yidaohome.com

#站点访问目录的黑名单文件
ms.site.blacklistfile .\blacklist.config

#文件服务器调用方系统标识及验证密钥
ms.fileserver.syscode yidaohome
ms.fileserver.authcode eWlkYW9ob21lOnlkajc4OWww

#站点默认版本号
ms.site.version 20170724.001
#当前站点使用的域名（cookie将于此绑定）
#ms.site.domain localhost:58359

#定义消息系统登录成功后显示的欢迎信息
im.signalr.welcome {0}，你好，欢迎登录易到家门店管理系统！

#业务产品标识，此参数通常由未来的自动部署系统自动生成
ms.product.number store
ms.product.id ydj.store
ms.product.name 易到家门店管理系统
ms.product.alias store

#当前站点允许使用的企业信息:默认站点
ms.company.icon logo-ydj.png
ms.company.title 深圳易到家科技有限公司

#service stack 框架所依赖的配置项
servicestack:license ydj-e1JlZjp5ZGosTmFtZTrmt7HlnLPmmJPliLDlrrbnp5HmioDmnInpmZDlhazlj7gsVHlwZTpFbnRlcnByaXNlLEhhc2g6SjBMcHFNS01wR0ZRbWhJMk0zQXVvS1FFQVdjb1R4K1NYZXlTSkFDVkpoQVhGT3o5bC81K2hySWhFVkJSRXlKa1ZPSmdDbStXRXRTMlUxbWVDeW9DN1VQUWVGSkZKWmkwdlp5RHFoSG1seWpiMklVV3lPaUlvTzh3OHR0T2RZdUplbTJtMll6V0MyQ2lDeUI4dnRTRDdIS0J1dk9BZ0Jhd3YvR2N2akxOVFcwPSxFeHBpcnk6OTk5OS0xMi0zMVQyMzo1OTo1OS45OTk5OTk5KzA4OjAwfQ==
jwt.AuthKeyBase64 d2FuZ2xpbg==
jwt.PrivateKeyXml <RSAKeyValue><Modulus>8nFads748/pcVhOjk78jaerOrfJJZ5d8h0UHUzXKzuQ4PUoBds7cSoZI735k5/Wd1yjb32D2D/Udj2XgVgKMh6WQQXGl5bklLLtd0wm9PRbY44wr4xMDMbpaPWac2jkcycU5BPudM6N8dMImRvvM2c/TYVzY8u3e75pxUmNKesE=</Modulus><Exponent>AQAB</Exponent><P>9ZAuQXQLdUcJKghYK7MSruVoVPXK0yFXIoORrleNzgG7Y8IClYxxE7CIGbVw//lOR+iOc+X35kgfpIDR9XmvFw==</P><Q>/L84q0+7AmHJnNXiRzPcvqBIzXEiPvVvHbx6ueLKVC4hNR+daC3rRRgEKi4MX02AyG8qWKTC+MQyn0s2lnSL5w==</Q><DP>BW/Sxlo2YrtW5ZpXsWf+kua7X54thm0ni7kUAaMonZYGOxgxjHQWkNQIv2D4BoLTi94HQWOnEG8qXC8J9wBhsw==</DP><DQ>HPxhJ2Rxcgh88rukw72y5znCn60F3GqRX7up53+W7KiNrmX9y2DYxofGkn/Nc3CUAGifVqMuqRD3LUWW8RJUqQ==</DQ><InverseQ>aloRXdZsZIXNpK5UmP0XabyEJqnf4YB0MdFOEclQwU7xtE7yn44ttuTs9CZBhjBTbqPF/nBR7NgfNsNGS2icwA==</InverseQ><D>Fupc7R7qDBGNebtYekYWQFQn3ewpQQfiBt0g8J4EgeKzaCOfGM47pzWjHjP5sK0YErazdZ0yFNcLyLqN5kNOafLD/gOSZfboHI6OIuua7PNEwF6b9c6nig8b+6aHxBkLou1Og2a18riGYcLRaEIJr2li9E9TfVv1T1bZv62B9Cs=</D></RSAKeyValue>
#jwt.PublicKeyXml <RSAKeyValue><Modulus>8nFads748/pcVhOjk78jaerOrfJJZ5d8h0UHUzXKzuQ4PUoBds7cSoZI735k5/Wd1yjb32D2D/Udj2XgVgKMh6WQQXGl5bklLLtd0wm9PRbY44wr4xMDMbpaPWac2jkcycU5BPudM6N8dMImRvvM2c/TYVzY8u3e75pxUmNKesE=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>
jwt.HashAlgorithm RS256
#令牌过期时间为一周
jwt.ExpireTokensInDays 7
#本机jwt令牌听众标识
jwt.Audience abcd@1234
#本机jwt令牌发行者信息
jwt.issuer fw.abcd