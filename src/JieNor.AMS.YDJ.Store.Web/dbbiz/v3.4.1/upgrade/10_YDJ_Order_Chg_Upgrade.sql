--销售合同变更单升级脚本
--杨茂衡  2019.07.03


if not exists(select * from syscolumns where id=object_id('T_YDJ_ORDEREXPENSE_CHG') and name='fentrychange')
alter table T_YDJ_ORDEREXPENSE_CHG add [fentrychange] [nvarchar](100) NOT NULL default(' ')

if not exists(select * from syscolumns where id=object_id('T_YDJ_ORDEREXPENSE_CHG') and name='fparenttranid')
alter table T_YDJ_ORDEREXPENSE_CHG add [fparenttranid] [nvarchar](100) NOT NULL default(' ')

if not exists(select * from syscolumns where id=object_id('T_YDJ_ORDEREXPENSE_CHG') and name='ftoptranid')
alter table T_YDJ_ORDEREXPENSE_CHG add [ftoptranid] [nvarchar](100) NOT NULL default(' ')

if not exists(select * from syscolumns where id=object_id('T_YDJ_ORDEREXPENSE_CHG') and name='fdataorigin')
alter table T_YDJ_ORDEREXPENSE_CHG add [fdataorigin] [nvarchar](100) NOT NULL default(' ')

if not exists(select * from syscolumns where id=object_id('t_ydj_orderentry_chg') and name='fisoutspot_chg')
alter table t_ydj_orderentry_chg add [fisoutspot_chg] [nvarchar](1) NOT NULL default(' ')

if not exists(select * from syscolumns where id=object_id('t_ydj_orderentry_chg') and name='fdeliverymode_chg')
alter table t_ydj_orderentry_chg add [fdeliverymode_chg] [nvarchar](200) NOT NULL default(' ')

if not exists(select * from syscolumns where id=object_id('t_ydj_orderentry_chg') and name='fexdeliverydate_chg')
alter table t_ydj_orderentry_chg add [fexdeliverydate_chg] [datetime] NULL



--销售合同变更单升级脚本
--杨茂衡  2020.02.11
declare @fcreatedate datetime;
--找出最后一个新货品原值字段大于0的创建时间
select top 1 @fcreatedate=fcreatedate from t_ydj_order_chg where ffaceamount_chg>0 order by fcreatedate desc
--找出所有需求修复记录的fid
select fid into #tabletmp from t_ydj_order_chg where fcreatedate>@fcreatedate and ffaceamount_chg=0

--更新新货品原值
update t set t.ffaceamount_chg=(select isnull(sum(famount_chg),0) from t_ydj_orderentry_chg e where e.fid=t.fid and fentrychange in ('entrychange_01','entrychange_02'))
from t_ydj_order_chg t
inner join #tabletmp tmp on t.fid=tmp.fid

---更新新成交金额
update t set t.fdealamount_chg=(select isnull(sum(fdealamount_chg),0) from t_ydj_orderentry_chg e where e.fid=t.fid and fentrychange in ('entrychange_01','entrychange_02'))
from t_ydj_order_chg t
inner join #tabletmp tmp on t.fid=tmp.fid

--更新新折扣额
update t set t.fdistamount_chg=(select isnull(sum(fdistamount_chg),0) from t_ydj_orderentry_chg e where e.fid=t.fid and fentrychange in ('entrychange_01','entrychange_02'))
from t_ydj_order_chg t
inner join #tabletmp tmp on t.fid=tmp.fid

--更新新费用
update t set t.fexpense_chg=(select isnull(sum(famount_chg),0) from t_ydj_orderexpense_chg e where e.fid=t.fid and fentrychange in ('entrychange_01','entrychange_02'))
from t_ydj_order_chg t
inner join #tabletmp tmp on t.fid=tmp.fid

--更新新订单总额
update t set t.fsumamount_chg=t.fdealamount_chg+t.fexpense_chg
from t_ydj_order_chg t
inner join #tabletmp tmp on t.fid=tmp.fid

--更新新确认已收
update t set t.freceivable_chg=(select isnull(sum((case c.fpurpose when 'bizpurpose_02' then c.famount when 'bizpurpose_06' then -c.famount when 'bizpurpose_04' then case c.fdirection when 'direction_01' then -c.famount else c.famount end else 0 end)),0) from t_coo_incomedisburse c where c.fsourceformid='ydj_order' and c.fsourcenumber=t.fsourcenumber and c.fcreatedate<t.fcreatedate and c.fbizstatus in ('bizstatus_02','bizstatus_03'))
from t_ydj_order_chg t
inner join #tabletmp tmp on t.fid=tmp.fid

--更新新未收
update t set t.funreceived_chg=t.fsumamount_chg-t.freceivable_chg-t.frefundamount
from t_ydj_order_chg t
inner join #tabletmp tmp on t.fid=tmp.fid

drop table #tabletmp



