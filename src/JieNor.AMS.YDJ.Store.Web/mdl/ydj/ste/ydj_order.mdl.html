
<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="ydj_order" basemodel="bill_basetmpl" el="1" cn="销售合同" approvalflow="true" nmfk="fbillno" ubl="1" fqfks="fcustomername,fphone" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_order" pn="fbillhead" cn="销售合同">

        <!--重写基类模型中的部分字段属性-->
        <input id="fstatus" el="122" lock="-1" lix="8" />
        <select el="152" ek="fbillhead" visible="-1" id="fchangestatus" fn="fchangestatus" pn="fchangestatus" cn="变更状态"
                vals="'0':'正常','1':'变更中','2':'变更完成','3':'变更已提交'" xlsin="0" copy="0" lix="266" align="center" lock="-1"></select>

        <select el="160" ek="fbillhead" visible="-1" id="fclosestatus" fn="fclosestatus" pn="fclosestatus" cn="关闭状态" defval="'0'" xlsin="0" copy="0" lix="270" lock="-1"></select>
        <!--备注-->
        <input id="fdescription" el="100" visible="1150" canchange="true" lix="200" len="1000" lock="0" uaul="true" notrace="false" />
        <input id="ftranid" el="100" cn="交易流水号" visible="0" lock="-1" />
        <input group="基本信息" el="116" ek="fbillhead" visible="0" id="fissaletransferorder" fn="fissaletransferorder" pn="fissaletransferorder" cn="是否销售转单" />
        <input group="基本信息" el="116" ek="fbillhead" visible="0" id="fisautogenerate" fn="fisautogenerate" pn="fisautogenerate" cn="是接口自动创建" />

        <!--基本信息-->
        <select id="fstatus" el="122" refId="bd_enum" dfld="fenumitem" cg="数据状态" ek="fbillhead" fn="fstatus" pn="fstatus" cn="订单状态" visible="1150" lix="27" defls="true"></select>
        <input el="119" ek="fbillhead" id="fcreatedate" fn="fcreatedate" cn="创建日期" width="130" visible="1150" copy="0" lix="251" />
        <input el="118" ek="fbillhead" id="fcreatorid" fn="fcreatorid" cn="创建人" width="130" visible="1150" copy="0" lix="251" />
        <input group="基本信息" el="108" ek="fbillhead" visible="-1" id="fbillno" fn="fbillno" pn="fbillno" cn="合同编号" lix="1" width="145" apipn="billNo" />
        <input group="基本信息" el="112" ek="fbillhead" visible="-1" id="forderdate" fn="forderdate" pn="forderdate" cn="业务日期" defval="@currentshortdate" lix="3" width="105" format="yyyy-MM-dd" copy="0" apipn="orderDate" canchange="true" must="1" />
        <input group="基本信息" el="107" ek="fbillhead" visible="1150" id="fmemberno" fn="fmemberno" pn="fmemberno" cn="客户会员ID" ctlfk="fcustomerid" dispfk="fmemberno" lix="24" />
        <input group="基本信息" el="107" ek="fbillhead" visible="1150" id="fsecmemberno" fn="fsecmemberno" pn="fsecmemberno" cn="终端客户会员ID" ctlfk="fterminalcustomer" dispfk="fmemberno" lix="24" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fmemberdesc" fn="fmemberdesc" pn="fmemberdesc" cn="未注册会员原因" len="1000" width="100" canchange="true" />
        <input group="基本信息" el="107" ek="fbillhead" visible="1150" id="fcustomernumber" fn="fcustomernumber" pn="fcustomernumber" cn="客户编码" ctlfk="fcustomerid" dispfk="fnumber" lix="4" />

        <input group="基本信息" el="107" ek="fbillhead" visible="-1" id="fcustomername" fn="fcustomername" pn="fcustomername" cn="客户名称" ctlfk="fcustomerid" dispfk="fname" lix="4" />

        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fcustomerid" fn="fcustomerid" pn="fcustomerid" cn="客户" refid="ydj_customer" lix="5"
               apipn="customer" dfld="fsource,fphone,femail,flegalperson,fcardid,fbank,faccountname,fbanknumber,fcontacts,fcustomerlevel,fsumamount,faddress,fsrcstoreid" />

        <select group="基本信息" el="152" ek="fbillhead" visible="1150" id="fcustomertype" fn="fcustomertype" pn="fcustomertype" cn="客户类型" vals="'customertype_00':'个人','customertype_01':'公司'" copy="0" defval="" lock="-1"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fcustomersource" fn="fcustomersource" pn="fcustomersource" cn="客户来源" cg="来源渠道" refid="bd_enum" dfld="fenumitem" lix="40" ctlfk="fcustomerid" dispfk="fsource" must="-1" lock="-1"></select>

        <input group="基本信息" el="107" ek="fbillhead" visible="0" id="fcustomersrcstoreid" fn="fcustomersrcstoreid" pn="fcustomersrcstoreid" cn="客户来源门店" ctlfk="fcustomerid" dispfk="fsrcstoreid" lix="4" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fphone" fn="fphone" pn="fphone" cn="手机号" apipn="phone" lix="7" sbm="true" lock="-1" />
        <input group="基本信息" el="107" ek="fbillhead" visible="1150" id="fdeptnumber" fn="fdeptnumber" pn="fdeptnumber" cn="部门编码" ctlfk="fdeptid" dispfk="fnumber" lix="8" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fdeptid" fn="fdeptid" pn="fdeptid" cn="销售部门" notrace="false" refid="ydj_dept" reflvt="1" defVal="@currentDeptId" apipn="saleDept" canchange="true" lix="9" defls="true" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fstaffid" fn="fstaffid" pn="fstaffid" cn="销售员" refid="ydj_staff" defVal="@currentStaffId" apipn="saleMan"
               dfld="fname" canchange="true" lix="11" notrace="false" />
        <input group="基本信息" el="112" ek="fbillhead" visible="-1" id="fdeliverydate" fn="fdeliverydate" pn="fdeliverydate" cn="交货日期" lix="13"
               width="105" format="yyyy-MM-dd" copy="0" apipn="deliveryDate" canchange="true" lock="0" uaul="true" notrace="false" />
        <input group="基本信息" el="107" ek="FBillHead" id="flinkstaffid" fn="flinkstaffid" pn="flinkstaffid" visible="-1" cn="收货人" ctlfk="fcustomercontactid" dispfk="fcontact_a"
               lock="-1" copy="1" lix="15" notrace="false" ts="" sbm="true" />
        <input group="基本信息" el="106" ek="fbillhead" id="fcustomercontactid" fn="fcustomercontactid" pn="fcustomercontactid" visible="0" cn="客户联系人Id" refid="ydj_customercontact"
               lock="-1" copy="1" lix="15" ts="" sbm="true" />

        <select group="基本信息" el="123" ek="fbillhead" visible="1150" id="fbilltype" fn="fbilltype" pn="fbilltype" cn="单据类型" refid="bd_billtype" lix="27" width="90" apipn="billType" must="1" notrace="false"
                tips="通过设置不同的单据类型实现字段的必录和锁定效果，以满足不同的业务场景需求"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="ftype" fn="ftype" pn="ftype" cn="业务类型" cg="业务类型" refid="bd_enum" dfld="fenumitem" defval="'order_type_01'" lix="29" width="90" apipn="type"></select>
        <select group="基本信息" el="152" ek="fbillhead" visible="1150" id="findenttype" fn="findenttype" pn="findenttype" cn="订单类型"
                vals="'L':'零售单','H':'售后单','G':'工程单','Z':'增补单','C':'材料单','Y':'样品单'" defval="'L'" lix="30" width="90" apipn="indenttype" copy="0"></select>
        <select group="基本信息" el="152" ek="fbillhead" visible="1150" id="flinkindenttype" fn="flinkindenttype" pn="flinkindenttype" cn="关联订单类型"
                vals="'L':'零售单','H':'售后单','G':'工程单','Z':'增补单','C':'材料单','Y':'样品单'" defval="''" lix="30" width="90" lock="-1" apipn="linkindenttype"></select>

        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="frelevanceorderno" fn="frelevanceorderno" pn="frelevanceorderno" cn="关联合同号"
               refid="ydj_order" apipn="relevanceorderno" canchange="true" lix="32" copy="0" />

        <!--<input group="基本信息" el="107" ek="fbillhead" visible="0" id="frelevancefindenttype" fn="frelevancefindenttype" pn="frelevancefindenttype" cn="关联合同订单类型"
    ctlfk="frelevanceorderno" dispfk="findenttype" />-->

        <select group="基本信息" el="152" ek="fbillhead" visible="1150" id="facceptstatus" fn="facceptstatus" pn="facceptstatus" cn="受理状态" vals="'0':'未受理','1':'已受理'" copy="0" defval="'0'" lix="31"></select>

        <input group="基本信息" el="124" ek="fbillhead" visible="1150" id="facceptperson" fn="facceptperson" pn="facceptperson" cn="受理人" apipn="acceptperson" lix="33" refid="sec_user" />

        <input group="基本信息" el="163" ek="FBillHead" id="fsign" fn="fsign" pn="fsign" visible="0" cn="客户签名" lock="0" copy="0" lix="35" ts="" />
        <input group="基本信息" el="112" ek="fbillhead" visible="0" id="facceptdate" fn="facceptdate" pn="facceptdate" cn="受理日期" lix="37" width="105" lock="-1" format="yyyy-MM-dd" copy="0"
               apipn="acceptDate" />

        <select group="基本信息" el="122" ek="fbillhead" visible="1124" id="fprovince" fn="fprovince" pn="fprovince" cn="省" cg="省" refid="bd_enum" dfld="fenumitem" apipn="province" lock="-1" lix="39"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="1124" id="fcity" fn="fcity" pn="fcity" cn="市" cg="市" refid="bd_enum" dfld="fenumitem" apipn="city" lock="-1" lix="14"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="1124" id="fregion" fn="fregion" pn="fregion" cn="区" cg="区" refid="bd_enum" dfld="fenumitem" apipn="region" lock="-1" lix="15"></select>

        <input group="基本信息" el="106" type="text" id="fbuildingid" ek="fbillhead" fn="fbuildingid" pn="fbuildingid" refid="ydj_building" ts="" visible="1150" cn="楼盘" sbm="true" lix="15" />

        <input group="基本信息" el="100" ek="fbillhead" visible="1124" id="faddress" fn="faddress" pn="faddress" cn="详细地址" apipn="address" canchange="true" lix="16" sbm="true" uaul="true" notrace="false" />

        <select group="基本信息" el="122" ek="fbillhead" visible="0" gt="2" id="fprovince_gb" fn="fprovince_gb" pn="fprovince_gb" cn="国补省" cg="省" copy="0" refid="bd_enum" dfld="fenumitem" lix="39"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="0" gt="2" id="fcity_gb" fn="fcity_gb" pn="fcity_gb" cn="国补市" cg="市" copy="0" refid="bd_enum" dfld="fenumitem" lix="14"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="0" gt="2" id="fregion_gb" fn="fregion_gb" pn="fregion_gb" cn="国补区" cg="区" copy="0" refid="bd_enum" dfld="fenumitem" lix="15"></select>
        <input group="基本信息" el="100" ek="fbillhead" visible="0" id="faddress_gb" fn="faddress_gb" pn="faddress_gb" cn="国补详细地址" copy="0" lix="16" uaul="true" />

        <input group="基本信息" el="106" ek="fbillhead" visible="0" id="fbrandid_e" fn="fbrandid_e" pn="fbrandid_e" cn="品牌" refid="ydj_brand" width="90" lix="7" apipn="brand" />

        <input group="基本信息" el="100" ek="FBillHead" id="flogisticsitems" fn="flogisticsitems" pn="flogisticsitems" visible="1150" cn="跟单备注" copy="1" lix="20" notrace="false" ts="" canchange="true" sbm="true" len="500" uaul="true" />

        <!--根据门店动态改变-->
        <input group="基本信息" el="107" ek="fbillhead" visible="-1" id="fstorename" fn="fstorename" pn="fstorename" cn="门店简称" lock="-1" copy="1" notrace="true" ts="" ctlfk="fstore" dispfk="fshortname" lix="9" defls="true" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fstore" fn="fstore" pn="fstore" cn="门店名称" notrace="false" refid="bas_store" lock="-1" canchange="true" lix="25" defls="true" />
        <input group="基本信息" el="107" ek="fbillhead" visible="-1" id="fstoreid" fn="fstoreid" pn="fstoreid" cn="门店编码" lock="-1" copy="1" notrace="true" ts="" ctlfk="fstore" dispfk="fnumber" lix="9" defls="true" />
        <!--<input group="基本信息" el="107" ek="fbillhead" visible="0" id="fsname" fn="fsname" pn="fsname" cn="门店简称-移除" ctlfk="fstore" dispfk="fshortname" lix="9" />-->
        <!--根据固定了不会跟随门店改变 移除-->
        <input group="基本信息" el="106" ek="fbillhead" visible="1124" id="fchannel" fn="fchannel" pn="fchannel" cn="合作渠道" refid="ste_channel" dfld="ftype" apipn="channel" canchange="true" lix="35" uaul="true" />
        <select group="基本信息" el="122" ek="fbillhead" visible="0" id="fchanneltype" fn="fchanneltype" pn="fchanneltype" cn="渠道类型" cg="渠道类型" refid="bd_enum" dfld="fenumitem" lix="5" width="70"></select>
        <input group="基本信息" el="106" ek="fbillhead" visible="1150" id="fstylistid" fn="fstylistid" pn="fstylistid" cn="设计师" filter="fbiztype = '5'" refid="ydj_staff" apipn="designer" lix="40" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fdesignscheme" fn="fdesignscheme" pn="fdesignscheme" cn="设计方案" lock="-1" copy="0" apipn="designScheme" lix="45" />
        <input group="基本信息" el="141" ek="fbillhead" visible="1150" id="fscalerecord" fn="fscalerecord" pn="fscalerecord" cn="量尺记录" lock="-1" copy="0" ctlfk="#ydj_scalerecord" apipn="scaleRecord" lix="50" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1124" id="foldorderno" fn="foldorderno" pn="foldorderno" cn="源合同号" apipn="oldOrderNo" lix="55" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fmallorderno" fn="fmallorderno" pn="fmallorderno" cn="商场合同号" lix="56" canchange="true" />
        <input group="基本信息" el="100" ek="fbillhead" visible="32" id="fhqderno" fn="fhqderno" pn="fhqderno" lock="-1" cn="总部合同号" lix="57" />

        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fwithin" fn="fwithin" pn="fwithin" cn="手工单号" width="150" uaul="true" canchange="true" />
        <input group="基本信息" el="106" ek="FBillHead" id="finnercustomerid" fn="finnercustomerid" pn="finnercustomerid" visible="0" cn="加盟商"
               lock="-1" copy="0" lix="58" notrace="false" ts="" refid="ydj_customer" filter="" reflvt="0" dfld="" />

        <input el="100" ek="fbillhead" id="fscancode" pn="fscancode" cn="商品扫码" visible="96" copy="0" editmode="1" />

        <input group="基本信息" el="100" ek="fbillhead" visible="1124" id="fcommercebillno" fn="fcommercebillno" pn="fcommercebillno" cn="电商订单号" lix="58" apipn="commerceBillNo" />
        <input group="基本信息" el="140" ek="fbillhead" visible="1150" id="fsourcetype" copy="0" lix="60" cn="源单类型" />
        <input group="基本信息" el="141" ek="fbillhead" visible="1150" id="fsourcenumber" copy="0" lix="61" cn="源单编号" />

        <input el="116" ek="fbillhead" visible="1150" id="fisresellorder" fn="fisresellorder" pn="fisresellorder" cn="二级分销合同" lock="-1" copy="0" width="100" />
        <input group="基本信息" el="165" ek="fbillhead" visible="0" id="fsourceid" fn="fsourceid" pn="fsourceid" copy="0" lix="62" cn="源单ID" desc="由二级分销商采购订单的【提交一级经销】生成合同时反写" />

        <input group="基本信息" el="116" ek="fbillhead" id="fstricttrack" fn="fstricttrack" pn="fstricttrack" visible="0" cn="严格跟单"
               lock="0" copy="1" lix="0" notrace="false" ts="" defval="false" />

        <input group="基本信息" el="116" ek="fbillhead" id="fisapplypur" fn="fisapplypur" pn="fisapplypur" visible="0" cn="启用申购"
               lock="0" copy="1" lix="0" notrace="false" ts="" defval="false" />

        <input group="基本信息" el="116" ek="fbillhead" id="fissubjoin" fn="fissubjoin" pn="fissubjoin" visible="0" cn="增补单"
               lock="-1" copy="1" lix="0" notrace="false" ts="" defval="false" />

        <input group="基本信息" el="116" ek="fbillhead" id="fneedtransferorder" fn="fneedtransferorder" pn="fneedtransferorder" visible="1150" cn="需转单"
               copy="1" lix="0" notrace="false" ts="" defval="false" canchange="true" />

        <input group="基本信息" el="106" ek="fbillhead" id="factivityid" fn="factivityid" pn="factivityid" visible="-1" cn="活动" uaul="true" copy="0" notrace="false" ts="" refid="ydj_activity" filter="" reflvt="0" dfld="" />

        <input group="基本信息" el="135" ek="fbillhead" visible="1150" id="fimage" fn="fimage" pn="fimage" cn="合同附件" lix="17" canchange="true" uaul="true" copy="0" />

        <!--开启发货通知，退货通知-->
        <input group="基本信息" el="116" ek="fbillhead" id="fenablenotice" fn="fenablenotice" pn="fenablenotice" visible="0" cn="启用通知"
               lock="0" copy="0" lix="0" notrace="false" ts="" defval="false" />

        <select group="基本信息" el="152" ek="fbillhead" id="flockstate" fn="flockstate" pn="flockstate" cn="锁定状态" visible="0"
                vals="'0':'未锁定','1':'已锁定'" defval="'0'" xlsin="0" copy="0" lix="310" align="center" lock="-1"></select>
        <input group="基本信息" el="113" ek="fbillhead" id="flockdate" fn="flockdate" pn="flockdate" cn="锁定日期" visible="0" xlsin="0" copy="0" lix="311" lock="-1" />
        <input group="基本信息" el="106" ek="fbillhead" id="flockpeople" fn="flockpeople" pn="flockpeople" cn="锁定人" refId="ydj_staff" apipn="saleMan" visible="0" xlsin="0" copy="0" lix="312" lock="-1" />
        <!-- 是否锁定 隐藏效验(销售员 = 锁定人)  是否锁定 ='0' 效验规则取消锁定    -->
        <input el="100" ek="fbillhead" id="fisitlocked" fn="fisitlocked" pn="fisitlocked" cn="是否锁定" visible="0" lock="-1" copy="0" />

        <input group="基本信息" ek="fbillhead" el="152" id="fchangeapplystatus" fn="fchangeapplystatus" pn="fchangeapplystatus" cn="变更申请状态" vals="'01':'变更中','02':'变更完成','03':'驳回'" visible="-1" copy="0" lock="-1" />

        <select el="152" ek="fbillhead" id="fclosestate" fn="fclosestate" pn="fclosestate" visible="0" cn="关闭状态" vals="'0':'未关闭','1':'已关闭'" defval="'0'" xlsin="0" copy="0" lix="270" lock="-1"></select>
        <input el="106" ek="fbillhead" id="fcloseid" fn="fcloseid" pn="fcloseid" visible="0" refId="Sec_User" dfld="FName" cn="关闭人" xlsin="0" copy="0" lix="300" lock="-1" />
        <input el="113" ek="fbillhead" id="fclosedate" fn="fclosedate" pn="fclosedate" cn="关闭日期" visible="-1" xlsin="0" copy="0" lix="301" lock="-1" />
        <input el="100" ek="fbillhead" id="fsharecostbillno" fn="fsharecostbillno" pn="fsharecostbillno" cn="共享费用计提单号" visible="-1" lock="-1" copy="0">
        <!-- 协同信息 (隐藏不用)-->
        <input group="协同信息" el="100" ek="fbillhead" visible="0" id="fdept" fn="fdept" cn="门店(协同)" width="140" copy="0" />
        <input group="协同信息" el="100" ek="fbillhead" visible="0" id="fstaff" fn="fstaff" cn="导购员(协同)" width="80" copy="0" />
        <input group="协同信息" el="100" ek="fbillhead" visible="0" id="fstylist" fn="fstylist" cn="设计师(协同)" width="80" copy="0" />
        <!--终端客户-->
        <input group="终端客户" el="106" ek="fbillhead" visible="-1" id="fterminalcustomer" fn="fterminalcustomer" pn="fterminalcustomer" cn="终端客户" refid="ydj_customer"
               width="90" copy="0" lock="0" dfld="fphone,fcontacts,faddress,fnumber" />
        <input group="终端客户" el="100" ek="fbillhead" visible="32" id="fcoophone" fn="fcoophone" cn="终端收货人电话" width="80" copy="0" lock="0" />
        <input group="终端客户" el="100" ek="fbillhead" visible="1150" id="fterminalcustomernumber" fn="fterminalcustomernumber" cn="终端客户编码" width="80" copy="0" lock="0" />
        <input group="终端客户" el="100" ek="fbillhead" visible="0" id="fprovincecityregion" fn="fprovincecityregion" cn="省市区(协同)" width="180" copy="0" />
        <select group="终端客户" groupType="city2" el="122" ek="fbillhead" visible="1150" id="fprovince_c" fn="fprovince_c" pn="fprovince_c" cn="省" cg="省" refid="bd_enum" dfld="fenumitem" lix="10" apipn="province"></select>
        <select group="终端客户" groupType="city2" el="122" ek="fbillhead" visible="1150" id="fcity_c" fn="fcity_c" pn="fcity_c" cn="市" cg="市" refid="bd_enum" dfld="fenumitem" lix="11" apipn="city"></select>
        <select group="终端客户" groupType="city2" el="122" ek="fbillhead" visible="-1" id="fregion_c" fn="fregion_c" pn="fregion_c" cn="地区" cg="区" refid="bd_enum" dfld="fenumitem" apipn="region" lix="12"></select>
        <input group="终端客户" el="100" ek="fbillhead" visible="1150" id="fcontacts_c" fn="fcontacts_c" pn="fcontacts_c" cn="联系人" lix="13" lock="0" />


        <input group="终端客户" el="100" ek="fbillhead" visible="32" id="fcooaddress" fn="fcooaddress" cn="详细地址(协同)" width="180" copy="0" lock="0" />

        <!--收货信息-->
        <!--<input group="收货信息" el="100" ek="fbillhead" visible="-1" id="fproductnumber" fn="fproductnumber" pn="fproductnumber" cn="物料编码" copy="0" />
    <input group="收货信息" el="100" ek="fbillhead" visible="-1" id="fproductname" fn="fproductname" pn="fproductname" cn="物料名称" copy="0" />
    <input group="收货信息" el="101" ek="fbillhead" visible="-1" id="fbiqty" fn="fbiqty" pn="fbiqty" cn="数量" copy="0" />
    <input group="收货信息" el="100" ek="fbillhead" visible="-1" id="funit" fn="funit" pn="funit" cn="单位" copy="0" />
    <input group="收货信息" el="100" ek="fbillhead" id="fconsignee" fn="fconsignee" pn="fconsignee" visible="-1" cn="收货人"
           lock="0" copy="0" notrace="false" ts="" canchange="true" sbm="true" />
    <input group="收货信息" el="100" ek="fbillhead" visible="-1" id="ftakephone" fn="ftakephone" pn="ftakephone" cn="手机号" copy="0" />
    <input group="收货信息" el="100" ek="fbillhead" visible="1124" id="ftakeaddress" fn="ftakeaddress" pn="ftakeaddress" cn="详细地址" width="180" copy="0" />-->
        <!--销售员信息-->
        <table id="fdutyentry" el="52" pk="fentryid" tn="t_ydj_orderduty" pn="fdutyentry" cn="销售员信息" kfks="fdutyid,fratio" apipn="dutyEntry">
            <tr>
                <th el="116" ek="fdutyentry" id="fismain" fn="fismain" pn="fismain" cn="主要负责" width="80" visible="1124" lock="-1" apipn="isMain" lix="10"></th>
                <th el="106" ek="fdutyentry" id="fdutyid" fn="fdutyid" pn="fdutyid" cn="联合开单销售员" refid="ydj_staff" sformid="" width="110" visible="1124" apipn="saleMan" lix="20"></th>
                <th el="106" ek="fdutyentry" id="fdeptid_ed" fn="fdeptid" pn="fdeptid" cn="联合开单销售部门" refid="ydj_dept" sformid="" width="120" visible="1150" lix="30" />
                <th el="102" ek="fdutyentry" id="fratio" fn="fratio" pn="fratio" cn="销售员分成比例%" width="200" visible="1124" format="%" apipn="ratio" canchange="true" lix="40"></th>
                <th el="105" ek="fdutyentry" id="famount_ed" fn="famount" pn="famount" cn="销售员分成金额" width="200" lock="0" visible="1124" format="0,000.00" apipn="amount" canchange="true" lix="50"></th>
                <th el="100" ek="fdutyentry" id="fdescription_ed" fn="fdescription" pn="fdescription" cn="备注" width="170" visible="1124" apipn="description" lix="70"></th>
                <th el="102" ek="fdutyentry" id="fdeptperfratio" fn="fdeptperfratio" pn="fdeptperfratio" cn="部门业绩比例%" visible="1124" format="%" canchange="true" width="200" lix="60"></th>
                <!--<th el="102" ek="fdutyentry" id="fdeptratio" fn="fdeptratio" pn="fdeptratio" cn="部门分成比例%" visible="1124" format="%" canchange="true" copy="0"></th>-->
                <!--<th el="105" ek="fdutyentry" id="fdeptratioamount" fn="fdeptratioamount" pn="fdeptratioamount" cn="部门分成金额" visible="1124" format="0,000.00" canchange="true" copy="0"></th>

            <th el="105" ek="fdutyentry" id="fdeptperfamount" fn="fdeptperfamount" pn="fdeptperfamount" cn="部门业绩金额" visible="1124" format="0,000.00" canchange="true" copy="0"></th>
            <th el="102" ek="fdutyentry" id="fstaffperfratio" fn="fstaffperfratio" pn="fstaffperfratio" cn="销售员业绩比例%" visible="1124" format="%" canchange="true" copy="0"></th>
            <th el="105" ek="fdutyentry" id="fstaffperfamount" fn="fstaffperfamount" pn="fstaffperfamount" cn="销售员业绩金额" visible="1124" format="0,000.00" canchange="true" copy="0"></th>-->
            </tr>
        </table>

        <!--增补信息-->
        <select group="增补信息" el="122" ek="fbillhead" visible="1124" id="fduty" fn="fduty" pn="fduty" cn="责任方" cg="责任方" refid="bd_enum" dfld="fenumitem" apipn="dutyParty"></select>
        <input group="增补信息" el="100" ek="fbillhead" visible="1124" id="fsubjoincause" fn="fsubjoincause" pn="fsubjoincause" cn="增补原因" len="300" apipn="subjoinCause" />
        <table id="fsubjoinentry" el="52" pk="fentryid" tn="t_ydj_ordersubjoin" pn="fsubjoinentry" cn="增补明细" kfks="fdutyid_ezb,fratio_ezb" apipn="subjoinEntry">
            <tr>
                <th el="106" ek="fsubjoinentry" id="fdutyid_ezb" fn="fdutyid" pn="fdutyid" cn="销售员" refid="ydj_staff" sformid="" width="90" visible="1124" apipn="saleMan"></th>
                <th el="106" ek="fsubjoinentry" id="fdeptid_ezb" fn="fdeptid" pn="fdeptid" cn="所属门店/部门" refid="ydj_dept" sformid="" width="130" visible="1124" apipn="saleDept"></th>
                <th el="102" ek="fsubjoinentry" id="fratio_ezb" fn="fratio" pn="fratio" cn="承担比例%" width="90" visible="1124" format="%" apipn="ratio"></th>
                <th el="105" ek="fsubjoinentry" id="famount_ezb" fn="famount" pn="famount" cn="金额" width="85" lock="-1" visible="1124" format="0,000.00" apipn="amount"></th>
                <th el="100" ek="fsubjoinentry" id="fdescription_ezb" fn="fdescription" pn="fdescription" cn="备注" width="130" visible="1124" apipn="description"></th>
            </tr>
        </table>


        <!--折扣设置-->
        <input group="折扣设置" el="116" ek="fbillhead" visible="32" id="fiswholedis" fn="fiswholedis" pn="fiswholedis" cn="整单折" width="100" copy="0" apipn="isWholeDis" canchange="true" notrace="false" />
        <select group="折扣设置" el="122" ek="fbillhead" visible="32" id="fdisopt" fn="fdisopt" pn="fdisopt" cn="快捷折扣选项" cg="快捷折扣选项" copy="0" apipn="disopt" notrace="false"></select>
        <input group="折扣设置" el="102" ek="fbillhead" visible="32" id="fdiscscale" fn="fdiscscale" pn="fdiscscale" cn="折扣系数" defval="10" copy="0" apipn="discScale" format="0,000.000000" dformat="0,000.00" notrace="false" />
        <input group="折扣设置" el="102" ek="fbillhead" visible="0" id="fdiscscale_temp" fn="fdiscscale_temp" pn="fdiscscale_temp" cn="手工录入折扣系数" copy="0" apipn="discScaleTemp" canchange="true" format="0,000.000000" dformat="0,000.00" notrace="false" />


        <!--优惠设置-->
        <!-- <input group="优惠设置" el="116" ek="fbillhead" visible="32" id="fisfavor" fn="fisfavor" pn="fisfavor" cn="申请优惠" copy="0" pipn="isFavor" canchange="true" /> -->
        <!-- <select group="优惠设置" el="122" ek="fbillhead" visible="32" id="ffavorway" fn="ffavorway" pn="ffavorway" cn="优惠方式" cg="优惠方式" defval="'favorway_01'" copy="0" apipn="favorWay"></select> -->
        <select group="优惠设置" el="122" ek="fbillhead" visible="32" id="ffavoropt" fn="ffavoropt" pn="ffavoropt" cn="优惠抹尾差选项" cg="优惠抹尾差选项" copy="0" apipn="favorOpt" notrace="false"></select>
        <input group="优惠设置" el="116" ek="fbillhead" visible="32" id="fisremovetail" fn="fisremovetail" pn="fisremovetail" cn="抹尾差" copy="0" apipn="isRemovetail" canchange="true" notrace="false" />
        <input group="优惠设置" el="116" ek="fbillhead" visible="32" id="fisfixedprice" fn="fisfixedprice" pn="fisfixedprice" cn="一口价" copy="0" apipn="isFixedprice" notrace="false" />
        <input group="优惠设置" el="102" ek="fbillhead" visible="96" id="ffixedprice" fn="" pn="ffixedprice" cn="一口价" copy="0" format="0,000.00" apipn="fixedPrice" notrace="false" />


        <!--财务信息-->
        <input group="财务信息" el="105" ek="fbillhead" visible="-1" id="fdealamount_h" fn="fdealamount" pn="fdealamount" cn="成交总额" lix="17" width="130" lock="-1" copy="0" format="0,000.00" apipn="dealAmount" notrace="false" />
        <input group="财务信息" el="105" ek="fbillhead" visible="-1" id="freceivable" fn="freceivable" pn="freceivable" cn="确认收款" lix="19" width="130" copy="0" lock="-1" format="0,000.00" apipn="receivable" notrace="false" />
        <input group="财务信息" el="105" ek="fbillhead" visible="-1" id="funreceived" fn="funreceived" pn="funreceived" cn="未收款" lix="21" width="130" copy="0" lock="-1" format="0,000.00" apipn="unreceived" notrace="false" />
        <select group="财务信息" el="122" ek="fbillhead" visible="-1" id="freceiptstatus" fn="freceiptstatus" cn="结算状态" refid="bd_enum" cg="收款状态" dfld="fenumitem" apipn="receiptStatus" defval="'receiptstatus_type_01'" lock="-1" copy="0" lix="22" width="90" align="center" notrace="false"></select>
        <input group="财务信息" el="116" ek="fbillhead" visible="0" id="fdontreflect" fn="fdontreflect" cn="销售合同实退不体现未收" desc="该字段仅在前端使用，用于规则计算的前置条件" defval="false" />
        <input group="财务信息" el="105" ek="fbillhead" visible="1124" id="fdistsumamount" fn="fdistsumamount" pn="fdistsumamount" cn="货款总折扣额" lock="-1" copy="0" format="0,000.00" apipn="distSumAmount" />
        <input group="财务信息" el="102" ek="fbillhead" visible="1124" id="fdistsumrate" fn="fdistsumrate" pn="fdistsumrate" cn="货款总折扣率" defval="1" lock="-1" copy="0" apipn="distSumRate" format="0,000.000000" dformat="0,000.00" />
        <input group="财务信息" el="105" ek="fbillhead" visible="1124" id="fdistamount" fn="fdistamount" pn="fdistamount" cn="折扣金额" lock="-1" copy="0" format="0,000.00" apipn="distAmount" notrace="false" />
        <input group="财务信息" el="105" ek="fbillhead" visible="1124" id="ffaceamount" fn="ffaceamount" pn="ffaceamount" cn="货品原值" lock="-1" copy="0" format="0,000.00" apipn="faceAmount" notrace="false" />
        <input group="财务信息" el="105" ek="fbillhead" visible="0" desc="该字段已废弃" id="fsumsellamount" fn="fsumsellamount" pn="fsumsellamount" cn="整单经销总价" lock="-1" copy="0" format="0,000.00" apipn="sumSellAmount" />
        <input group="财务信息" el="105" ek="fbillhead" visible="1150" id="freceivabletobeconfirmed" fn="freceivabletobeconfirmed" pn="freceivabletobeconfirmed" cn="收款待确认" lix="11" width="130" copy="0" lock="-1" format="0,000.00" apipn="receivableToBeConfirmed" notrace="false" />
        <input group="财务信息" el="105" ek="fbillhead" visible="0" desc="该字段已废弃" id="fsumreceivable" fn="fsumreceivable" pn="fsumreceivable" cn="收款金额" lix="11" width="130" copy="0" lock="-1" format="0,000.00" apipn="sumReceivable" notrace="false" />
        <input group="财务信息" el="105" ek="fbillhead" visible="1124" id="fexpense" fn="fexpense" pn="fexpense" cn="费用" lock="-1" format="0,000.00" apipn="expense" copy="0" notrace="false" />
        <input group="财务信息" el="105" ek="fbillhead" id="frefundamount" fn="frefundamount" pn="frefundamount" visible="1150" cn="申请退货金额"
               lock="-1" copy="0" lix="80" notrace="false" ts="" roundType="0" format="0,000.00" />
        <input group="财务信息" el="105" ek="fbillhead" id="factrefundamount" fn="factrefundamount" pn="factrefundamount" visible="1150" cn="实退金额"
               lock="-1" copy="0" lix="80" notrace="false" ts="" roundType="0" format="0,000.00" />

        <input group="财务信息" el="102" ek="fbillhead" visible="1150" id="fpaymentratios" fn="fpaymentratios" pn="fpaymentratios" cn="收款比例(%)" defval="0" lix="17" width="130" lock="1" copy="0" format="0,000.00" />

        <input group="财务信息" el="102" ek="fbillhead" visible="1150" id="fnopromotiondistrate" fn="fnopromotiondistrate" pn="fnopromotiondistrate" cn="非促销折扣" defval="1" width="130" lock="-1" copy="0" format="0,000.000000" dformat="0,000.00" />

        <!-- 敬天增加字段，《销售合同》财务信息增加【核算折扣】从成本核算中【核算折扣】获取 -->
        <input group="财务信息" el="102" ek="fbillhead" visible="1124" id="faccountscale" fn="faccountscale" pn="faccountscale" cn="核算折扣" defval="1" lock="-1" copy="0" format="0,000.00" desc="成本核算保存时反写" />

        <!--暂时无用：已废弃-->
        <input group="财务信息" el="105" id="fconfirmamount" ek="fbillhead" fn="fconfirmamount" ts="" visible="0" cn="待确认金额" lock="-1" copy="0" width="105" apipn="confirmAmount" />

        <!-- 销售合同增加字段【应收订金】，【已收定金】，金额字段，锁定，只能从销售意向携带过来，只读 -->
        <input group="财务信息" el="105" ek="fbillhead" visible="1150" id="fcollectamount" fn="fcollectamount" cn="应收定金" lock="-1" copy="0" apipn="collectAmount" />
        <input group="财务信息" el="105" ek="fbillhead" visible="1150" id="fcollectedamount" fn="fcollectedamount" cn="已收定金" lock="-1" copy="0" apipn="collectedAmount" />
        <input group="财务信息" el="105" ek="fbillhead" visible="1150" id="fbrokerage" fn="fbrokerage" cn="实付佣金" lock="-1" copy="0" apipn="brokerage" />
        <input group="财务信息" el="105" ek="fbillhead" visible="1150" id="fplanbrokerage" fn="fplanbrokerage" cn="应付佣金" lock="-1" copy="0" />

        <input group="财务信息" el="105" ek="fbillhead" visible="0" desc="该字段已废弃" id="finvoiceamount" fn="finvoiceamount" cn="开票金额" lock="-1" copy="0" />
        <input group="财务信息" el="105" ek="fbillhead" visible="1150" id="ffirstamount" fn="ffirstamount" cn="首款额" lock="-1" copy="0" />

        <input group="财务信息" el="116" ek="fbillhead" visible="1150" id="fisinvoiceneed" fn="fisinvoiceneed" pn="fisinvoiceneed" cn="需开票" copy="0">
        <input group="财务信息" el="152" ek="fbillhead" visible="1150" id="finvoicetype" fn="finvoicetype" pn="finvoicetype" cn="开票类型" copy="0" vals="'01':'增值税专用发票','02':'增值税普通发票'">
        <input group="财务信息" el="100" ek="fbillhead" visible="1150" id="fbuyerfullname" fn="fbuyerfullname" pn="fbuyerfullname" cn="购买方全称" copy="0">
        <input group="财务信息" el="100" ek="fbillhead" visible="1150" id="ftaxpayeridentify" fn="ftaxpayeridentify" pn="ftaxpayeridentify" cn="纳税人识别号" copy="0">
        <input group="财务信息" el="100" ek="fbillhead" visible="1150" id="finvoiceemail" fn="finvoiceemail" pn="finvoiceemail" cn="电子邮箱" copy="0">
        <input group="财务信息" el="100" ek="fbillhead" visible="1150" id="finvoiceaddress" fn="finvoiceaddress" pn="finvoiceaddress" cn="地址" copy="0">
        <input group="财务信息" el="100" ek="fbillhead" visible="1150" id="finvoicephone" fn="finvoicephone" pn="finvoicephone" cn="电话" copy="0">
        <input group="财务信息" el="100" ek="fbillhead" visible="1150" id="fdepositbankname" fn="fdepositbankname" pn="fdepositbankname" cn="开户银行" copy="0">
        <input group="财务信息" el="100" ek="fbillhead" visible="1150" id="fbankaccount" fn="fbankaccount" pn="fbankaccount" cn="银行账户" copy="0">

        <!--R-BL-BG035收款协同-已扣佣金/费用明细-->
        <input group="财务信息" el="105" ek="fbillhead" visible="0" desc="该字段已废弃" id="freducedbrokerage" fn="freducedbrokerage" pn="freducedbrokerage" cn="已扣佣金" lock="-1" format="0,000.00" apipn="reducedBrokerage" copy="0" />

        <input group="基本信息" el="116" ek="fbillhead" visible="1150" id="frenewalflag" fn="frenewalflag" pn="frenewalflag" cn="焕新订单标记" lock="0" copy="0" notrace="false" ts="" defval="false" />
        <select group="基本信息" el="152" ek="fbillhead" visible="1150" id="fsettlprogress" fn="fsettlprogress" pn="fsettlprogress" cn="结算进度" vals="'0':'待发起','1':'收款中','2':'已收款','3':'已退款'" defval="'0'" xlsin="0" copy="0" lock="-1"></select>
        <input group="基本信息" el="105" ek="fbillhead" visible="1150" id="fsubsidyamount_h" fn="fsubsidyamount" pn="fsubsidyamount" cn="补贴总金额" width="130" copy="0" format="0,000.00" apipn="subsidyAmount" notrace="false" sbm="true" uaul="true" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fmembershiptranid" fn="fmembershiptranid" pn="fmembershiptranid" cn="商城交易流水号" copy="0" notrace="false" sbm="true" uaul="true" len="50" />
        <input group="基本信息" el="112" ek="fbillhead" visible="1150" id="fmembershippaydate" fn="fmembershippaydate" pn="fmembershippaydate" cn="商城支付日期" format="yyyy-MM-dd" xlsin="0" copy="0" notrace="false" sbm="true" uaul="true" />
        <input group="基本信息" el="116" ek="fbillhead" visible="1150" id="fiszbrefund" fn="fiszbrefund" pn="fiszbrefund" cn="总部已退款" lock="-1" copy="0" apipn="iszbrefund" />
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fcustomcategory" fn="fcustomcategory" pn="fcustomcategory" cn="焕新定制柜类别" cg="焕新定制柜类别" refid="bd_enum" dfld="fenumitem" notrace="false"></select>
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fsecondorderno" fn="fsecondorderno" pn="fsecondorderno" cn="二级合同编号" copy="0" lock="-1" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="frenewtype" fn="frenewtype" pn="frenewtype" cn="焕新订单类型" refid="ydj_renewtype" copy="0" lix="5" apipn="renewtype" dfld="fisincome,fisstandard,fisv6" canchange="true" />
        <input group="基本信息" el="107" ek="fbillhead" visible="0" id="fisincome" fn="fisincome" pn="fisincome" cn="允许发起收款" dispfk="fisincome" ctlfk="frenewtype" />

        <input group="基本信息" el="116" ek="fbillhead" visible="1150" id="frenewalrectag" fn="frenewalrectag" pn="frenewalrectag" cn="焕新预收标记" lock="0" copy="0" notrace="false" ts="" defval="false" />
        <input group="成本核算" el="105" ek="fbillhead" visible="1150" id="frecdealamount_gb" fn="frecdealamount_gb" pn="frecdealamount_gb" cn="国补预收成交金额" copy="0" lock="-1" notrace="false" />
        <input group="成本核算" el="105" ek="fbillhead" visible="1150" id="frecsumamount_gb" fn="frecsumamount_gb" pn="frecsumamount_gb" cn="国补预收总补贴金额" copy="0" lock="-1" notrace="false" />

        <!--成本核算-->
        <input group="成本核算" el="105" ek="fbillhead" visible="1150" id="fsumamount" fn="fsumamount" pn="fsumamount" cn="订单总额" apipn="sumAmount" copy="0" lock="-1" notrace="false" />
        <input group="成本核算" el="105" ek="fbillhead" visible="32" id="fexpenditure" fn="fexpenditure" pn="fexpenditure" cn="费用支出" format="0,000.00" apipn="expenditure" copy="0" notrace="false" />
        <input group="成本核算" el="105" ek="fbillhead" visible="1150" id="fexpensumamount" fn="fexpensumamount" pn="fexpensumamount" cn="费用收入" format="0,000.00" apipn="fexpensumamount" copy="0" notrace="false" />
        <input group="成本核算" el="105" ek="fbillhead" visible="0" id="fprofit" fn="fprofit" pn="fprofit" cn="毛利" format="0,000.00" copy="0" apipn="profit" />
        <input group="成本核算" el="105" ek="fbillhead" visible="0" id="fsumcost" fn="fsumcost" pn="fsumcost" cn="总成本(加权平均)" format="0,000.00" copy="0" apipn="sumCost" notrace="false" />
        <input group="成本核算" el="105" ek="fbillhead" visible="0" id="fproductcost" fn="fproductcost" pn="fproductcost" cn="商品成本" format="0,000.00" copy="0" apipn="productCost" notrace="false" />
        <select group="成本核算" el="122" ek="fbillhead" visible="0" id="fcostsource" fn="fcostsource" pn="fcostsource" cn="成本来源" cg="成本来源" apipn="costSource" refid="bd_enum" dfld="fenumitem" defval="'costsource_01'" copy="0"></select>

        <input group="后台字段" el="103" ek="fbillhead" visible="0" id="fsumoutqty" fn="fsumoutqty" cn="基本单位总出库数量" lock="-1" copy="0" apipn="sumOutQty" />
        <input group="后台字段" el="103" ek="fbillhead" visible="0" id="fbizsumoutqty" fn="fbizsumoutqty" cn="销售总出库数量" lock="-1" copy="0" apipn="salSumOutQty" />
        <input group="后台字段" el="165" ek="fbillhead" visible="0" id="fcostid" fn="fcostid" cn="成本核算单主键Id" ctlfk="#ydj_costaccounting" lock="-1" copy="0" desc="下推成本核算保存时反写" apipn="costId" />
        <input group="后台字段" el="116" ek="fbillhead" visible="0" id="fiscost" fn="fiscost" pn="fiscost" cn="已核算成本" lock="-1" copy="0" desc="是否已核算成本" apipn="fiscost" />
        <input group="后台字段" el="105" ek="fbillhead" visible="0" id="fverifyamount" fn="fverifyamount" pn="fverifyamount" cn="经销商待对账金额" copy="0" lock="-1" />
        <input group="后台字段" el="105" ek="fbillhead" visible="0" id="fcustomersumamount" fn="fcustomersumamount" pn="fcustomersumamount" cn="客户累计消费金额" copy="0" lock="-1" />

        <!-- 非标合同相关 -->
        <input group="基本信息" el="100" ek="fbillhead" visible="0" id="fordernumber" fn="fordernumber" pn="fordernumber" cn="手工单号" copy="0" />

        <!-- v6定制柜合同 -->
        <!--<input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fdesignerid" fn="fdesignerid" pn="fdesignerid" cn="设计师" refid="ydj_staff" filter="(fbiztype='6' or fbiztype='0')" copy="0" />-->
        <!--<input group="基本信息" el="106" ek="fbillhead" id="fhqdesignerid" fn="fhqdesignerid" pn="fhqdesignerid" cn="总部设计师" visible="1150" lock="-1" refid="ste_hqdesign" copy="0" />-->
        <!-- v6定制柜合同（三维家） -->
        <input group="基本信息" el="100" ek="fbillhead" visible="1086" id="fswjordernumber" fn="fswjordernumber" pn="fswjordernumber" cn="三维家单号" copy="0" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fswjdesignerid" fn="fswjdesignerid" pn="fswjdesignerid" cn="设计师" refid="ydj_staff" copy="0" dfld="fphone" canchange="true" />
        <input group="基本信息" el="116" ek="fbillhead" visible="-1" lock="-1" id="fswjorderstate" fn="fswjorderstate" pn="fswjorderstate" cn="三维家门店审核" copy="0" />
        <input group="基本信息" el="100" ek="fbillhead" visible="0" lock="-1" id="fswjdetailurl" fn="fswjdetailurl" pn="fswjdetailurl" cn="三维家详情页跳转链接" copy="0" len="1000" />
        <input group="基本信息" el="116" ek="fbillhead" visible="1150" id="fomsservice" fn="fomsservice" pn="fomsservice" cn="启用定制OMS" lock="-1" copy="0" width="100" />
        <input group="基本信息" el="106" ek="fbillhead" visible="1150" id="fprojectnumber" fn="fprojectnumber" pn="fprojectnumber" cn="项目编号" refid="ydj_projectinfo" dfld="fcustomerid,factivenumber,forderattr" copy="0" lix="200" />
        <input group="基本信息" el="107" ek="fbillhead" visible="0" id="fprojectcustomerid" fn="fprojectcustomerid" pn="fprojectcustomerid" cn="项目编号客户" lock="-1" ts="" ctlfk="fprojectnumber" dispfk="fcustomerid" lix="500" />
        <input group="基本信息" el="152" ek="fbillhead" visible="1150" id="forderattr" fn="forderattr" pn="forderattr" cn="订单属性" lock="-1" copy="0" width="100" vals="'1':'散单','2':'整家宅配','3':'大家居'" lix="200" />
        <input group="基本信息" el="107" ek="fbillhead" visible="0" id="fprojectorderattr" fn="fprojectorderattr" pn="fprojectorderattr" cn="项目订单属性" lock="-1" ts="" ctlfk="fprojectnumber" dispfk="forderattr" lix="500" />
        <input group="基本信息" el="107" ek="fbillhead" visible="0" id="fprojectactivenumber" fn="fprojectactivenumber" pn="fprojectactivenumber" cn="项目活动方案编码" lock="-1" ts="" ctlfk="fprojectnumber" dispfk="factivenumber" lix="500" />
        <input group="基本信息" el="100" ek="fbillhead" visible="0" id="factivenumber" fn="factivenumber" pn="factivenumber" cn="活动方案编码" lock="-1" ts="" lix="500" />
        <input group="基本信息" el="100" ek="fbillhead" visible="0" id="factivediscount" fn="factivediscount" pn="factivediscount" cn="活动折扣系数" lock="-1" ts="" lix="500" />
        <input group="基本信息" el="100" ek="fbillhead" visible="0" id="fswjversion" fn="fswjversion" pn="fswjversion" cn="三维家数据版本号" lock="-1" copy="0" ts="" lix="500" />

        <!-- 一件代发 -->
        <input group="基本信息" el="116" ek="fbillhead" id="fpiecesendtag" fn="fpiecesendtag" pn="fpiecesendtag" visible="1150" cn="一件代发标记" copy="0" lix="500" notrace="false" ts="" defval="false" canchange="false" lock="0" />
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fordersignstatus" fn="fordersignstatus" pn="fordersignstatus" cn="整单签收状态" refid="bd_enum" cg="整单签收状态" dfld="fenumitem" apipn="ordersignstatus" lock="-1" copy="0" lix="500"></select>

        <input group="基本信息" el="113" type="datetime" id="fsubmithtime" ek="fbillhead" fn="fsubmithtime" pn="fsubmithtime" cn="提交SAP时间" visible="-1" lix="10" width="160" lock="-1" copy="0" />
        <input group="基本信息" el="152" ek="fbillhead" id="fchstatus" fn="fchstatus" pn="fchstatus" cn="协同SAP状态"
               vals="'1':'已锁单','2':'已驳回','3':'已终审','4':'锁单失败'" lock="-1" visible="-1" lix="40" must="0" copy="0" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fheadcontracttype" fn="fheadcontracttype" pn="fheadcontracttype" cn="SAP合同类型" lock="-1" ts="" lix="500" copy="0" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fheadquartno" fn="fheadquartno" pn="fheadquartno" cn="SAP合同号" lock="-1" ts="" lix="500" copy="0" />
        <input group="基本信息" el="113" type="datetime" id="fheadquartfrtime" ek="fbillhead" fn="fheadquartfrtime" pn="fheadquartfrtime" cn="SAP终审时间" visible="-1" lix="10" width="160" lock="-1" copy="0" />
        <input group="基本信息" el="100" ek="fbillhead" id="fheadquartsyncmessage" fn="fheadquartsyncmessage" pn="fheadquartsyncmessage" cn="SAP同步消息" visible="-1" copy="0" lix="40" lock="-1" len="1000" copy="0" />
        <input group="基本信息" el="104" ek="fbillhead" id="fcommissionrate" fn="fcommissionrate" pn="fcommissionrate" cn="佣金比例" width="90" lock="-1" visible="0" format="0,000.000000" dformat="0,000.00" apipn="commissionRate" canchange="true" notrace="false" copy="0" />
        <input group="基本信息" el="116" ek="fbillhead" id="forderbyreturn" fn="forderbyreturn" pn="forderbyreturn" visible="0" cn="销售退货生成关联合同推送" copy="0" lix="500" notrace="false" ts="" defval="false" canchange="false" lock="0" />
        <!--商品明细（明细） adld默认是否允许下载 maxCount是文件上传个数,maxSize是文件上传大小，服务端传值默认为0 那么就默认传五个。-->
        <table id="fentry" el="52" pk="fentryid" tn="t_ydj_orderentry" pn="fentry" cn="商品明细" kfks="fproductid,fqty" apipn="entry">
            <tr>
                <!--<th lix="0" el="116" ek="fentry" id="fisself" fn="fisself" pn="fisself" cn="自备料" visible="1150" lock="-1" apipn="isSelf"></th>-->
                <th lix="200" el="116" ek="fentry" id="fisself" fn="fisself" pn="fisself" cn="自备料" visible="0" lock="-1" apipn="isSelf"></th>
                <th lix="201" el="106" ek="fentry" id="fsuitid" fn="fsuitid" pn="fsuitid" cn="所属套件" refid="ydj_suit" sformid="" visible="0" lock="-1"></th>
                <!--<th lix="3" el="116" ek="fentry" id="fissplit" fn="fissplit" pn="fissplit" cn="不可拆卖" visible="96" lock="-1"></th>-->
                <th lix="202" el="116" ek="fentry" id="fissplit" fn="fissplit" pn="fissplit" cn="不可拆卖" visible="0" lock="-1"></th>
                <th lix="219" el="116" ek="fentry" id="fisgiveaway" fn="fisgiveaway" pn="fisgiveaway" cn="赠品" visible="32" canchange="true"></th>
                <!--<th lix="203" el="116" ek="fentry" id="fisdirectgiveaway" fn="fisdirectgiveaway" pn="fisdirectgiveaway" cn="直营赠品" visible="32" canchange="true"></th>-->
                <th lix="204" el="122" ek="fentry" visible="-1" id="fbiztype" fn="fbiztype" pn="fbiztype" cn="特殊标记" cg="特殊标记" refid="bd_enum" dfld="fenumitem"></th>
                <th lix="205" ek="fentry" el="107" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="-1" cn="商品编码" lock="-1" copy="1" notrace="true" ts="" ctlfk="fproductid" dispfk="fnumber"></th>
                <th lix="207" el="106" ek="fentry" id="fproductid" fn="fproductid" pn="fproductid" cn="商品" refid="ydj_product" multsel="true" dfld="fspecifica,fvolume,fgrossload,fpacksize,fstockunitid,fendpurchase,fguideprice,fbrandid,fseriesid,fsubseriesid,fselcategoryid,fbedpartflag,fcategoryid,fauxseriesid,fispresetprop,fseltypeid,fispartflag,fmainorgid,funitid,fsalunitid" sformid="" width="160" visible="-1" apipn="product" must="1" filter="fispulloff='0'"></th>
                <th lix="209" el="107" ek="fentry" id="fmtrlmodel" fn="fmtrlmodel" pn="fmtrlmodel" visible="-1" cn="规格型号" lock="-1" copy="1" notrace="true" ts="" ctlfk="fproductid" dispfk="fspecifica"></th>
                <th lix="211" el="132" ek="fentry" id="fattrinfo" fn="fattrinfo" pn="fattrinfo" cn="辅助属性" ctlfk="fproductid" nstdfk="funstdtype" pricefk="fprice" width="160" lock="0" visible="1150" apipn="attrInfo"
                    tips="通过辅助属性实现商品选配下单，前提商品勾选允许选配"></th>
                <th el="100" ek="fentry" id="fattrinfo_first" cn="初始辅助属性" fn="fattrinfo_first" pn="fattrinfo_first" visible="1150" len="4000" width="200" lock="-1" />
                <th lix="213" el="100" ek="fentry" len="2000" id="fcustomdes_e" fn="fcustomdes_e" pn="fcustomdes_e" cn="定制说明" width="160" visible="1150" apipn="customDes" xsslv="1"></th>
                <th lix="215" el="109" ek="fentry" id="fbizunitid" fn="fbizunitid" pn="fbizunitid" cn="销售单位" refid="ydj_unit" sformid="" ctlfk="fproductid" width="90" visible="-1" apipn="salUnit" must="1"></th>
                <th lix="217" el="103" ek="fentry" id="fbizqty" fn="fbizqty" pn="fbizqty" cn="销售数量" ctlfk="fbizunitid" format="0,000.00" basqtyfk="fqty" width="80" visible="-1" apipn="salQty" canchange="true" notrace="false"></th>
                <th lix="217" el="103" ek="fentry" id="fbizreserveqty" fn="fbizreserveqty" pn="fbizreserveqty" cn="预留量" ctlfk="fbizunitid" format="0,000.00" basqtyfk="freserveqty" width="80" visible="-1" apipn="bizreserveqty" lock="-1" canchange="false"></th>
                <th lix="219" el="104" ek="fentry" id="fprice" fn="fprice" pn="fprice" cn="零售价" width="70" visible="1150" apipn="price" format="0,000.000000" dformat="0,000.00" notrace="false"></th>
                <th lix="219" el="104" ek="fentry" id="fhqprice" fn="fhqprice" pn="fhqprice" cn="总部零售价" width="100" visible="1150" format="0,000.000000" dformat="0,000.00" notrace="false"></th>
                <th lix="219" el="104" ek="fentry" id="fterprice" fn="fterprice" pn="fterprice" cn="终端零售价" width="70" visible="1150" apipn="fterprice" canchange="true" lock="0" format="0,000.000000" dformat="0,000.00"></th>
                <th lix="221" el="105" ek="fentry" id="fteramount" fn="fteramount" pn="fteramount" cn="终端金额" width="90" visible="1150" apipn="fteramount" canchange="true" lock="0"></th>
                <th lix="221" el="105" ek="fentry" id="famount" fn="famount" pn="famount" cn="金额" width="90" visible="1150" apipn="amount" canchange="true" lock="-1" notrace="false"></th>
                <th lix="223" el="102" ek="fentry" id="fdistrate" fn="fdistrate" pn="fdistrate" cn="抹零后折率" width="60" visible="1150" apipn="distRate" canchange="true" format="0,000.000000" dformat="0,000.00" notrace="false"></th>
                <th lix="223" el="102" ek="fentry" id="fdistrateraw" fn="fdistrateraw" pn="fdistrateraw" cn="折率" width="60" visible="1150" apipn="distRate" canchange="true" lock="-1" format="0,000.000000" dformat="0,000.00" notrace="false"></th>
                <th lix="225" el="105" ek="fentry" id="fdistamount_e" fn="fdistamount" pn="fdistamount" cn="折扣额" width="90" lock="-1" visible="1150" format="0,000.000000" apipn="distAmount" canchange="true" notrace="false"></th>
                <th lix="227" el="104" ek="fentry" id="fdealprice" fn="fdealprice" pn="fdealprice" cn="成交单价" width="90" lock="0" visible="1150" format="0,000.000000" dformat="0,000.00" apipn="dealPrice" canchange="true" notrace="false"></th>
                <th lix="229" el="105" ek="fentry" id="fdealamount_e" fn="fdealamount" pn="fdealamount" cn="成交金额" width="90" lock="0" visible="1150" format="0,000.00" apipn="dealAmount" canchange="true" notrace="false"></th>
                <!--新增字段-->
                <th lix="227" el="104" ek="fentry" id="rounded_fdealprice" fn="rounded_fdealprice" pn="rounded_fdealprice" cn="保留2位小数成交单价" width="90" lock="-1" visible="1150" format="0,000.00" dformat="0,000.00" apipn="rounded_dealPrice"></th>
                <th lix="229" el="105" ek="fentry" id="rounded_fdealamount_e" fn="rounded_fdealamount" pn="rounded_fdealamount" cn="国补尾差" width="90" lock="-1" visible="1150" format="0,000.00" apipn="rounded_dealAmount"></th>

                <th lix="229" el="105" ek="fentry" id="fsubsidyamount" fn="fsubsidyamount" pn="fsubsidyamount" cn="补贴金额" width="90" lock="-1" visible="1150" format="0,000.00" apipn="subsidyAmount" canchange="true" notrace="false"></th>
                <th lix="219" el="116" ek="fentry" id="fisoutspot" fn="fisoutspot" pn="fisoutspot" cn="出现货" visible="1150" apipn="isOutSpot" canchange="true" defval="false"></th>
                <th lix="233" el="152" ek="fentry" id="fdeliverymode" fn="fdeliverymode" pn="fdeliverymode" visible="1150" cn="提货方式" lock="0" copy="0" notrace="false" ts="" vals="'0':'物流配送','1':'立即提货','2':'预约提货'" defval="0"></th>
                <th el="106" ek="fentry" id="fstockstatus" fn="fstockstatus" pn="fstockstatus" visible="1150" cn="库存状态" lix="235" notrace="false" refid="ydj_stockstatus" defVal="'311858936800219137'" dfld="fcolor" lock="-1"></th>
                <th ek="fentry" lix="214" el="161" id="fmtrlimage" fn="fmtrlimage" pn="fmtrlimage" cn="图片" ctlfk="fproductid" width="200" visible="-1" canchange="true" lock="-1"></th>
                <th lix="239" el="107" ek="fentry" id="fbrandid" fn="fbrandid" cn="品牌" visible="1150" dispfk="fbrandid" ctlfk="fproductid" sformid="" width="100" lock="-1"></th>
                <th lix="241" el="107" ek="fentry" id="fseriesid" fn="fseriesid" cn="系列" visible="1150" dispfk="fseriesid" ctlfk="fproductid" sformid="" width="100" lock="-1"></th>
                <th lix="241" el="107" ek="fentry" id="fsubseriesid" fn="fsubseriesid" cn="子系列" visible="1086" dispfk="fsubseriesid" ctlfk="fproductid" sformid="" width="100" lock="-1"></th>
                <th ek="fentry" lix="243" el="107" id="fattribute" f="fattribute" pn="fattribute" visible="0" cn="属性" lock="-1" copy="1" notrace="true" ts="" ctlfk="fproductid" dispfk="fattribute" refvt="0"></th>
                <th lix="245" el="107" ek="fentry" id="fcustom" fn="fcustom" pn="fcustom" visible="0" cn="允许定制" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fproductid" dispfk="fcustom" refvt="116"></th>
                <th lix="247" el="106" ek="fentry" id="fsupplierid" fn="fsupplierid" pn="fsupplierid" cn="供应商" refid="ydj_supplier" dfld="faddress" sformid="" ACL="1" width="120" visible="1150" apipn="supplier"></th>
                <th lix="249" el="106" ek="fentry" id="fdeptid_e" fn="fdeptid" pn="fdeptid" cn="销售部门" refid="ydj_dept" sformid="" width="120" visible="1150" canchange="true"></th>
                <th lix="219" el="106" ek="fentry" id="fstorehouseid" fn="fstorehouseid" cn="仓库" refid="ydj_storehouse" sformid="" visible="1150" width="100" apipn="storehouse" filter="fname!='直发仓库'"></th>
                <th lix="251" el="107" ek="fentry" id="fwarehousetype" fn="fwarehousetype" cn="仓库类型" refid="ydj_storehouse" sformid="" visible="0" width="100" apipn="storehouse" ctlfk="fstorehouseid" dispfk="fwarehousetype"></th>
                <th lix="251" el="107" ek="fentry" id="fnocalculate" fn="fnocalculate" cn="不参与采购计划库存量统计" refid="ydj_storehouse" sformid="" visible="0" width="100" apipn="storehouse" ctlfk="fstorehouseid" dispfk="fnocalculate"></th>
                <th lix="253" el="100" ek="fentry" id="fdescription_e" fn="fdescription" pn="fdescription" cn="非生产备注" width="100" visible="1150" apipn="description" canchange="true" lock="0" uaul="true" len="1000"></th>
                <th lix="255" el="112" ek="fentry" id="fexdeliverydate" fn="fexdeliverydate" pn="fexdeliverydate" cn="预计交货日期" format="yyyy-MM-dd" width="160" visible="1150" canchange="true"></th>
                <th lix="257" el="160" ek="fentry" id="fclosestatus_e" fn="fclosestatus" pn="fclosestatus_e" visible="1150" cn="行关闭状态" defval="'0'" xlsin="0" copy="0" lock="-1"></th>
                <th lix="259" el="106" ek="fentry" id="fstaff_e" fn="fstaffid" pn="fstaffid" cn="销售人员" refid="ydj_staff" defVal="@currentStaffId" width="120" visible="1150" apipn="saleMan" canchange="true"></th>
                <th lix="261" el="100" ek="fentry" id="flinkpro" fn="flinkpro" pn="flinkpro" cn="流程状态" visible="1150" width="80" lock="-1"></th>
                <!--慕思新增字段-->
                <th lix="265" el="107" ek="fentry" visible="1096" id="fissuitflag" fn="fissuitflag" pn="fissuitflag" cn="是否套件" ctlfk="fproductid" dispfk="fsuiteflag" lock="-1" refValueType="116"></th>
                <th lix="270" el="116" ek="fentry" visible="1150" id="funstdtype" fn="funstdtype" pn="funstdtype" cn="是否非标" ctlfk="fproductid" dispfk="funstdtype" width="90" copy="0" lock="0" refValueType="116"></th>
                <th el="106" lix="13" ek="fentry" id="fresultbrandid" fn="fresultbrandid" pn="fresultbrandid" cn="业绩品牌" refid="ydj_series" visible="-1"></th>
                <th lix="275" el="106" ek="fentry" visible="1150" id="fsuitproductid" fn="fsuitproductid" pn="fsuitproductid" cn="套件商品" refid="ydj_product" lock="-1"></th>
                <th lix="280" el="100" ek="fentry" visible="1150" id="fsuitdescription" fn="fsuitdescription" pn="fsuitdescription" cn="套件说明" len="2000" lock="-1"></th>
                <th lix="285" el="100" ek="fentry" visible="1150" id="fsuitcombnumber" fn="fsuitcombnumber" pn="fsuitcombnumber" cn="套件组合号" lock="-1"></th>
                <th lix="290" el="100" ek="fentry" visible="1150" id="fpartscombnumber" fn="fpartscombnumber" pn="fpartscombnumber" cn="配件组合号" lock="-1"></th>
                <th lix="295" el="103" ek="fentry" id="fsubqty" fn="fsubqty" pn="fsubqty" visible="1150" cn="子件数量" width="120" lock="-1" copy="0" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
                <th lix="300" el="107" ek="fentry" visible="1150" id="fselcategoryid" fn="fselcategoryid" pn="fselcategoryid" cn="选配类别" ctlfk="fproductid" dispfk="fselcategoryid" lock="-1"></th>
                <th lix="305" el="106" ek="fentry" visible="1150" id="fforproductid" fn="fforproductid" pn="fforproductid" cn="所属套件" refid="ydj_product" lock="-1"></th>
                <th lix="310" el="106" ek="fentry" visible="1150" id="fforsuiteselectionid" fn="fforsuiteselectionid" pn="fforsuiteselectionid" cn="所属套件选配码" refid="mt_suiteselection" lock="-1"></th>
                <th lix="315" el="100" ek="fentry" visible="1150" id="fdescription_suite" fn="description_suite" pn="description_suite" cn="组合说明" len="2000" lock="-1"></th>
                <th lix="320" el="100" ek="fentry" visible="1150" id="fpackagedescription" fn="packagedescription" pn="packagedescription" cn="套件组合说明" len="2000" lock="-1"></th>
                <th lix="325" el="100" ek="fentry" visible="1150" id="fsofacombnumber" fn="fsofacombnumber" pn="fsofacombnumber" cn="沙发组合号" lock="-1"></th>

                <th lix="340" el="122" ek="fentry" visible="1150" id="fprodrequirement" fn="fprodrequirement" pn="fprodrequirement" refid="bd_enum" dfld="fenumitem" ts="" cg="生产要求" cn="生产要求"></th>
                <th lix="345" el="122" ek="fentry" visible="1150" id="fselsuiterequire" fn="fselsuiterequire" pn="fselsuiterequire" refid="bd_enum" dfld="fenumitem" ts="" cg="家纺套件要求" cn="家纺套件要求"></th>

                <th lix="800" el="103" ek="fentry" id="fbizoutpackqty" fn="fbizoutpackqty" ts="" visible="1150" cn="销售已出库包数" lock="-1" copy="0"></th>
                <th lix="800" el="103" ek="fentry" id="fbizchangepackqty" fn="fbizchangepackqty" ts="" visible="1150" cn="销售已退换包数" lock="-1" copy="0"></th>
                <th lix="800" el="103" ek="fentry" id="fbizpurpackqty" fn="fbizpurpackqty" ts="" visible="1150" cn="已采购包数" lock="-1" copy="0"></th>
                <th lix="800" el="103" ek="fentry" id="ftranspurqty" fn="ftranspurqty" ts="" visible="1150" cn="已转采购数" lock="-1" copy="0"></th>
                <th lix="800" el="103" ek="fentry" id="ftransoutqty" fn="ftransoutqty" ts="" visible="1150" cn="已推出库数" lock="-1" copy="0"></th>

                <!-- 隐藏商品行的附件 -->
                <th lix="8" el="135" ek="fentry" id="fmulfile" fn="fmulfile" pn="fmulfile" adld="true" maxCount="0" cn="附件" width="240" visible="0" canchange="true"></th>

                <!-- <th lix="12" el="107" ek="fentry" id="fcustom" fn="fcustom" pn="fcustom" visible="1150" cn="允许定制" width
            ="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fproductid" dispfk="fcustom" refvt="116"></th>-->
                <!--    <th lix="12" el="107" ek="fentry" id="fispresetprop" fn="fispresetprop" pn="fispresetprop" visible="1150" cn="允许选配" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fproductid" dispfk="fispresetprop" refvt="116"></th>-->
                <th lix="12" el="107" ek="fentry" id="fispresetprop" fn="fispresetprop" pn="fispresetprop" visible="0" cn="允许选配" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fproductid" dispfk="fispresetprop" refvt="116"></th>
                <th lix="21" el="109" ek="fentry" id="funitid" fn="funitid" pn="funitid" cn="基本单位" refid="ydj_unit" sformid="" ctlfk="fproductid" lock="-1" filter="fisbaseunit='1'" width="80" visible="32" apipn="unit" must="1" notrace="false"></th>
                <th lix="22" el="103" ek="fentry" id="fqty" fn="fqty" pn="fqty" cn="基本单位数量" ctlfk="funitid" format="0,000.00" width="100" visible="32" apipn="qty" notrace="false" lock="-1"></th><!--must="1"-->
                <th lix="22" el="103" ek="fentry" id="freserveqty" fn="freserveqty" pn="freserveqty" cn="基本单位预留量" ctlfk="funitid" format="0,000.00" width="100" visible="32" apipn="reserveqty" lock="-1" canchange="false"></th>
                <th lix="22" el="103" ek="fentry" id="fpartqty" fn="fpartqty" pn="fpartqty" cn="配件数量" ctlfk="funitid" format="0,000.00" width="100" visible="0" apipn="qty" canchange="true"></th>
                <th lix="25" el="100" ek="fentry" id="fperfdesc" fn="fperfdesc" pn="fperfdesc" visible="0" cn="性能描述" lock="0" copy="1" notrace="false" ts=""></th>

                <th lix="69" el="106" ek="fentry" id="flogisticscompanyid" fn="flogisticscompanyid" pn="flogisticscompanyid" cn="物流公司" refid="ydj_supplier" visible="1086" filter=" ftype ='suppliertype_03' " apipn="logisticscompany"></>

                <th lix="85" el="116" ek="fentry" id="fisreqiured" fn="fisreqiured" pn="fisreqiured" cn="已协同要货" visible="0" lock="-1" width="80" apipn="isReqiured"></th>
                <th lix="90" el="152" ek="fentry" id="forderstatus" fn="forderstatus" pn="forderstatus" cn="订货状态" vals="'1':'已下单','2':'已收货'" visible="0" lock="-1" apipn="orderStatus"></th>
                <th lix="95" el="116" ek="fentry" id="fisdelivery" fn="fisdelivery" pn="fisdelivery" cn="已协同发货" visible="0" lock="-1" width="80" apipn="isDelivery"></th>
                <th lix="100" el="152" ek="fentry" id="fdeliverystatus" fn="fdeliverystatus" pn="fdeliverystatus" cn="发货状态" vals="'1':'待备货','2':'已备货','3':'已发货'" visible="0" lock="-1" apipn="deliveryStatus"></th>

                <!--基础资料分录字段，控制字段指向仓库，仓库上有个分录标识为fentity的仓位值集，此字段将仓位值集虚拟成普通基础资料-->
                <th lix="108" el="153" ek="fentry" id="fstorelocationid" fn="fstorelocationid" cn="仓位" ctlfk="fstorehouseid" luek="fentity" lunmfk="flocname" lunbfk="flocnumber" sformid="" visible="1086" width="100"></th>
                <th lix="109" el="100" ek="fentry" id="fmtono" fn="fmtono" pn="fmtono" cn="物流跟踪号" width="128" align="center" visible="1086" copy="0" lock="-1" apipn="mtono"></th>
                <th lix="110" el="149" ek="fentry" id="fownertype" fn="fownertype" pn="fownertype" dataviewname="v_bd_ownerdata" cn="货主类型" width="100" visible="32" lock="0">
                    <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
                    <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
                    <dataSourceDesc formId="ydj_dept" filter="" caption="部门"></dataSourceDesc>
                </th>
                <th lix="111" el="150" ek="fentry" id="fownerid" fn="fownerid" pn="fownerid" ctlfk="fownertype" cn="货主" width="100" visible="1086" lock="0"></th>

                <th lix="113" el="116" ek="fentry" id="fstricttrack_e" fn="fstricttrack" pn="fstricttrack" visible="1086" cn="严格跟单"
                    lock="0" copy="1" notrace="false" ts="" defval="false"></th>

                <th lix="114" el="152" ek="fentry" id="foperationmode" fn="foperationmode" pn="foperationmode" cn="运营模式" vals="'1':'总部直营','2':'独立经营'" visible="0" lock="-1" apipn="operationMode" defval="0"></th>



                <th lix="120" el="103" ek="fentry" id="fdeliveryqty" fn="fdeliveryqty" pn="fdeliveryqty" cn="基本单位已发货数" ctlfk="funitid" visible="1086" width="120" lock="-1" copy="0" desc="" apipn="deliveryQty"></th>
                <th lix="125" el="103" ek="fentry" id="foutqty" fn="foutqty" pn="foutqty" cn="基本单位已出库数" ctlfk="funitid" visible="1086" width="120" lock="-1" copy="0" desc="由销售合同的下游出入库记录.业务场景=销售出库单据反写生成" apipn="outQty"></th>
                <th lix="126" el="103" ek="fentry" id="fbizdeliveryqty" fn="fbizdeliveryqty" pn="fbizdeliveryqty" ctlfk="fbizunitid" basqtyfk="fdeliveryqty" cn="销售已发货数" visible="1086" width="100" lock="-1" copy="0" desc="" apipn="salDeliveryQty"></th>
                <th lix="127" el="103" ek="fentry" id="fbizoutqty" fn="fbizoutqty" pn="fbizoutqty" cn="销售已出库数" ctlfk="fbizunitid" basqtyfk="foutqty" visible="1086" width="100" lock="-1" copy="0" desc="由销售合同的下游出入库记录.业务场景=销售出库单据反写生成" apipn="salOutQty"></th>

                <th lix="125" el="103" ek="fentry" id="fpurqty" fn="fpurqty" pn="fpurqty" cn="基本单位已采购数量" ctlfk="funitid" visible="1086" width="135" lock="-1" copy="0" desc="由销售合同的下游采购订单.业务场景=采购订单反写生成" apipn="purQty"></th>
                <th lix="125" el="103" ek="fentry" id="fbizpurqty" fn="fbizpurqty" pn="fbizpurqty" cn="已采购数量" ctlfk="fbizunitid" basqtyfk="fpurqty" visible="1086" width="90" lock="-1" copy="0" desc="由销售合同的下游采购订单.业务场景=采购订单反写生成" apipn="bizPurQty"></th>

                <th lix="219" el="104" ek="fentry" id="fsellprice" fn="fsellprice" pn="fsellprice" cn="经销价" width="80" visible="0" desc="该字段已废弃" format="0,000.00" lock="-1" copy="0" apipn="sellPrice"></th>
                <th lix="230" el="105" ek="fentry" id="fsellamount" fn="fsellamount" pn="fsellamount" cn="经销总价" width="90" visible="0" desc="该字段已废弃" format="0,000.00" lock="-1" copy="0" apipn="sellAmount"></th>

                <th lix="145" el="103" ek="fentry" id="freturnnoticeqty" fn="freturnnoticeqty" pn="freturnnoticeqty" visible="1086" cn="基本单位退货通知数量" width="150"
                    lock="-1" copy="0" notrace="false" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
                <th lix="150" el="103" ek="fentry" id="freturnqty" fn="freturnqty" pn="freturnqty" visible="1086" cn="基本单位已退换数量" width="120"
                    lock="-1" copy="0" notrace="false" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
                <th lix="155" el="103" ek="fentry" id="frefundqty" fn="frefundqty" pn="frefundqty" visible="1086" cn="基本单位退款数量" width="120"
                    lock="-1" copy="0" notrace="false" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>

                <th lix="160" el="103" ek="fentry" id="fbizreturnnoticeqty" fn="fbizreturnnoticeqty" pn="fbizreturnnoticeqty" visible="1086" cn="销售退货通知数量" width="120"
                    lock="-1" copy="0" notrace="false" ts="" ctlfk="fbizunitid" basqtyfk="freturnnoticeqty" roundType="0" format="0,000.00"></th>
                <th lix="165" el="103" ek="fentry" id="fbizreturnqty" fn="fbizreturnqty" pn="fbizreturnqty" visible="1086" cn="销售已退换数量" width="100"
                    lock="-1" copy="0" notrace="false" ts="" ctlfk="fbizunitid" basqtyfk="freturnqty" roundType="0" format="0,000.00"></th>
                <th lix="170" el="103" ek="fentry" id="fbizrefundqty" fn="fbizrefundqty" pn="fbizrefundqty" visible="1086" cn="销售退款数量" width="100"
                    lock="-1" copy="0" notrace="false" ts="" ctlfk="fbizunitid" basqtyfk="frefundqty" roundType="0" format="0,000.00"></th>



                <th el="133" ek="fentry" id="flength" fn="flength" pn="flength" cn="长" visible="0" width="65" apipn="length"></th>
                <th el="133" ek="fentry" id="fwidth" fn="fwidth" pn="fwidth" cn="宽" visible="0" width="65" apipn="width"></th>
                <th el="133" ek="fentry" id="fthick" fn="fthick" pn="fthick" cn="厚" visible="0" width="65" apipn="thick"></th>
                <th el="102" ek="fentry" id="farea" fn="farea" pn="farea" cn="面积m2" width="70" visible="0" apipn="area"></th>
                <th el="116" ek="fentry" id="fisnsc" fn="fisnsc" pn="fisnsc" cn="非标" width="65" visible="0" apipn="isNsc"></th>
                <th el="102" ek="fentry" id="fnsc" fn="fnsc" pn="fnsc" cn="非标系数" width="80" visible="0" apipn="nsc"></th>
                <th el="116" ek="fentry" id="fissub" fn="fissub" pn="fissub" cn="子件信息" visible="0" width="80" apipn="isSub"></th>
                <th el="116" ek="fentry" id="fissize" fn="fissize" pn="fissize" cn="尺寸信息" visible="0" width="80" apipn="isSize"></th>

                <th el="116" ek="fentry" id="fpromotion" fn="fpromotion" pn="fpromotion" cn="活动促销" visible="1086" width="80" lix="180"></th>


                <th el="101" ek="fentry" id="flinkweight" fn="flinkweight" pn="flinkweight" cn="流程权重" visible="0" width="80" lix="180" lock="-1"></th>

                <th el="104" ek="fentry" id="fstandardprice" fn="fstandardprice" pn="fstandardprice" cn="标准售价" width="100" visible="1086" lock="-1" format="0,000.000000" dformat="0,000.00" copy="0" apipn="standardPrice"></th>
                <!--【成本金额】=【成本价】*【销售数量】-->
                <th el="104" ek="fentry" id="fcostprice" fn="fcostprice" pn="fcostprice" cn="单位成本(加权平均)" width="100" visible="-1" lock="-1" format="0,000.000000" dformat="0,000.00" copy="0" apipn="costPrice"></th>
                <th el="105" ek="fentry" id="fcost" fn="fcost" pn="fcost" cn="总成本(加权平均)" width="100" visible="-1" lock="-1" format="0,000.00" copy="0" apipn="cost"></th>

                <!--后台隐藏字段，用于成本核算-->
                <th el="105" ek="fentry" id="fgain" fn="fgain" pn="fgain" cn="盈利" width="100" visible="0" lock="-1" format="0,000.00" copy="0" apipn="gain"></th>
                <!--<th el="100" ek="fentry" id="fsourceentryid" fn="fsourceentryid" pn="fsourceentryid" cn="源单明细内码" visible="0" lock="-1" copy="0" apipn="sourceEntryId"></th>-->
                <th ek="fentry" el="100" id="fsourceentryid_e" fn="fsourceentryid_e" pn="fsourceentryid_e" cn="源单明细ID" visible="0" lock="-1" copy="0" width="120" apipn="sourceEntryId"></th>
                <th el="141" ek="fentry" id="fsourcenumber_e" fn="fsourcenumber_e" pn="fsourcenumber_e" cn="源单编号" visible="1086" copy="0" lock="-1" width="150" lix="100"></th>
                <th el="140" ek="fentry" id="fsourcetype_e" fn="fsourcetype_e" pn="fsourcetype_e" cn="源单类型" visible="1086" copy="0" lock="-1" width="150" lix="95"></th>
                <th el="100" ek="fentry" id="fsuitentryid" fn="fsuitentryid" cn="套件分录ID" visible="0" lock="-1" copy="0"></th>
                <!--初始已排单据数量，隐藏不必显示，只用于排单发货平台对导入合同历史的数据的排除, 排单平台也不得反写此字段-->
                <th el="102" ek="fentry" id="fscheduledqty" fn="fscheduledqty" pn="fscheduledqty" visible="0" cn="排单数量" ctlfk="funitid" lock="-1" copy="0" lix="159" notrace="false" ts="" roundType="0" format="0,000.00"></th>


                <!--<th el="107" ek="fentry" visible="1150" id="funstdtype" fn="funstdtype" pn="funstdtype" cn="是否非标" ctlfk="fproductid" dispfk="funstdtype" lock="-1" refValueType="116"></th>-->



                <th el="107" ek="fentry" visible="1086" id="fcategoryid" fn="fcategoryid" pn="fcategoryid" cn="商品类别" ctlfk="fproductid" dispfk="fcategoryid" lock="-1"></th>
                <th el="107" ek="fentry" visible="0" id="fbedpartflag" fn="fbedpartflag" pn="fbedpartflag" cn="是否可添加配件" ctlfk="fproductid" dispfk="fbedpartflag" lock="-1"></th>
                <th el="100" ek="fentry" visible="1086" id="fparttype" fn="fparttype" pn="fparttype" cn="配件类型" len="100" lock="-1"></th>
                <!--用于区分商品和其带出的配件-->
                <th lix="800" el="116" ek="fentry" visible="-1" id="fiscombmain" fn="fiscombmain" pn="fiscombmain" cn="配件主商品" lock="-1"></th>
                <!--用于区分标准、非标定制 自动带出的配件 和 手动添加的配件商品-->
                <th lix="800" el="116" ek="fentry" visible="-1" id="fisautopartflag" fn="fisautopartflag" pn="fisautopartflag" cn="是否自动带出配件" lock="-1"></th>

                <th lix="800" el="107" ek="fentry" visible="1086" id="fseltypeid" fn="fseltypeid" pn="fseltypeid" cn="型号" ctlfk="fproductid" dispfk="fseltypeid" lock="-1"></th>

                <!--套件相关-->
                <th el="107" ek="fentry" visible="0" id="fissuit" fn="fissuit" pn="fissuit" cn="是否属于套件" lock="-1" ctlfk="fproductid" dispfk="fissuit" refValueType="116"></th>
                <th el="100" ek="fentry" visible="0" id="fselectionnumber" fn="fselectionnumber" pn="fselectionnumber" cn="选配码" lock="-1"></th>

                <th el="122" ek="fentry" visible="1086" id="fspace" fn="fspace" pn="fspace" cn="空间" cg="空间" apipn="sapace" refid="bd_enum" dfld="fenumitem"></th>
                <th el="116" ek="fentry" visible="0" id="fisclearstock" fn="fisclearstock" pn="fisclearstock" cn="清库存" lock="-1"></th>

                <!-- 非标合同相关 -->
                <th el="152" ek="fentry" visible="1086" id="funstdtypestatus" fn="funstdtypestatus" pn="funstdtypestatus" cn="非标审批状态" vals="'01':'新建','02':'待审批','03':'审批通过','04':'驳回','05':'待定价','06':'终审'" defval="" copy="0" lock="-1"></th>
                <th el="100" ek="fentry" visible="1086" id="funstdtypecomment" fn="funstdtypecomment" pn="funstdtypecomment" cn="非标审批意见" width="160" lix="20" len="2000" copy="0" lock="-1"></th>


                <!--转单申请相关-->

                <th el="152" visible="1086" lix="301" ek="fentry" id="ftransferorderstatus" fn="ftransferorderstatus" pn="ftransferorderstatus" cn="转单状态" lock="-1" copy="0" notrace="false" ts="" vals="'1':'审批中','2':'审批通过','3':'审批驳回','4':'已结算'"></th>
                <th el="113" ek="fentry" id="ftransferorderapplytime" fn="ftransferorderapplytime" pn="ftransferorderapplytime" cn="转单申请日期" width="120" lock="-1" copy="0" apipn="transferorderapplytime" visible="1086" lix="301"></th>
                <th el="113" ek="fentry" id="ftransferorderagreetime" fn="ftransferorderagreetime" pn="ftransferorderagreetime" cn="转单通过日期" width="120" lock="-1" copy="0" apipn="transferorderagreetime" visible="1086" lix="301"></th>

                <th el="100" ek="fentry" id="ftargetagentid" fn="ftargetagentid" pn="ftargetagentid" cn="目标经销商ID" lock="-1" visible="0"></th>
                <th el="100" ek="fentry" id="ftargetagentno" fn="ftargetagentno" pn="ftargetagentno" cn="目标经销商编码" lock="-1" visible="0"></th>
                <th el="100" ek="fentry" id="ftargetagentname" fn="ftargetagentname" pn="ftargetagentname" cn="目标经销商" lock="-1" visible="1086"></th>
                <th el="100" ek="fentry" id="fshipperagentid" fn="fshipperagentid" pn="fshipperagentid" cn="发货经销商ID" lock="-1" visible="0"></th>
                <th el="100" ek="fentry" id="fshipperagentno" fn="fshipperagentno" pn="fshipperagentno" cn="发货经销商编码" lock="-1" visible="0"></th>
                <th el="100" ek="fentry" id="fshipperagentname" fn="fshipperagentname" pn="fshipperagentname" cn="发货经销商" lock="-1" visible="1086" lix="301"></th>
                <th el="100" ek="fentry" id="fshipperagentcontactperson" fn="fshipperagentcontactperson" pn="fshipperagentcontactperson" cn="发货经销商联系人" lock="-1" visible="1086" lix="301"></th>
                <th el="100" ek="fentry" id="fshippercontactpersonphone" fn="fshippercontactpersonphone" pn="fshippercontactpersonphone" cn="发货联系人手机号" lock="-1" visible="1086" lix="301"></th>
                <th el="106" ek="fentry" id="fshipperdeliver" fn="fshipperdeliver" pn="fshipperdeliver" cn="送达方" refid="bas_deliver" visible="1086" desc="接方单转单时，作为接单方下推到转单申请单"></th>
                <!--售后维修相关-->
                <th lix="170" el="103" ek="fentry" id="faftersalesrepairqty" fn="faftersalesrepairqty" pn="faftersalesrepairqty" visible="1086" cn="售后维修数量" width="100"
                    lock="-1" format="0,000"></th>
                <th lix="301" el="107" ek="fentry" id="fauxseriesid" fn="fauxseriesid" cn="附属品牌" visible="-1" dispfk="fauxseriesid" ctlfk="fproductid" sformid="" width="100" lock="-1"></th>
                <th lix="302" el="116" ek="fentry" id="furgent" fn="furgent" pn="furgent" cn="加急" visible="1086" notrace="false"></th>
                <th lix="303" el="100" ek="fentry" id="fadditionalcharge" fn="fadditionalcharge" pn="fadditionalcharge" cn="附加费" visible="1086" lock="-1"></th>
                <th lix="304" el="100" ek="fentry" id="fomsbackreason" fn="fomsbackreason" pn="fomsbackreason" cn="总部退回原因" visible="1086" lock="-1" len="1000"></th>
                <!-- 三维家 -->
                <th lix="305" el="100" ek="fentry" id="ffactorybillid" fn="ffactorybillid" pn="ffactorybillid" cn="工厂订单ID" visible="0" lock="-1"></th>
                <th lix="306" el="100" ek="fentry" id="ffactorybillno" fn="ffactorybillno" pn="ffactorybillno" cn="工厂订单号" visible="1086"></th>
                <th lix="306" el="100" ek="fentry" id="fsfactorybillno" fn="fsfactorybillno" pn="fsfactorybillno" cn="原工厂订单号" visible="1086" lock="-1"></th>
                <th lix="306" el="100" ek="fentry" id="fsfactorybillid" fn="fsfactorybillid" pn="fsfactorybillid" cn="原工厂订单ID" visible="1086" lock="-1"></th>
                <th lix="306" el="100" ek="fentry" id="ffactorybillname" fn="ffactorybillname" pn="ffactorybillname" cn="订单名称" visible="-1" lock="-1"></th>
                <th lix="307" el="152" ek="fentry" id="fomsprogress" fn="fomsprogress" pn="fomsprogress" cn="定制订单进度" cg="定制订单进度" lock="-1" visible="1086" xlsin="0"
                    vals="'-1':'单据作废','-2':'已退回','5':'待提交','10':'待接单','20':'待审单','25':'待门店确认','26':'门店退回','27':'门店确认','30':'待拆单','40':'待报价','50':'流程完成'" defval="5"></th>
                <th lix="310" el="152" ek="fentry" id="fsendtarget" fn="fsendtarget" pn="fsendtarget" visible="1150" cn="直发标记" copy="0" notrace="false" ts="" vals="'0':'门店','1':'消费者'"></th>
                <th lix="312" el="100" ek="fentry" id="fomsbillno" fn="fomsbillno" pn="fomsbillno" visible="1150" cn="定制OMS单号" copy="0" lock="-1"></th>
                <th lix="312" el="100" ek="fentry" id="fsomsbillno" fn="fsomsbillno" pn="fsomsbillno" visible="1150" cn="原定制OMS单号" copy="0" lock="-1"></th>
                <th lix="307" el="152" ek="fentry" id="fomsoperate" fn="fomsoperate" pn="fomsoperate" cn="定制操作类型" cg="定制操作类型" lock="-1" visible="0" xlsin="0"
                    vals="'1':'提交审核通过','5':'单号创建成功','11':'接单审核通过','12':'接单审核退回','19':'接单作废','21':'审图审核通过','22':'审图审核退回','31':'拆单审核通过','32':'拆单审核退回','41':'报价审核通过','42':'报价审核退回'"></th>
                <th lix="312" el="100" ek="fentry" id="fomsstorereturnnote" fn="fomsstorereturnnote" pn="fomsstorereturnnote" visible="-1" cn="门店退回原因" copy="0" lock="-1" width="120"></th>
                <th lix="313" el="100" ek="fentry" id="fordertype" fn="fordertype" pn="fordertype" visible="1150" cn="订单品类" copy="0" lock="-1" width="120"></th>
                <th lix="314" el="116" ek="fentry" id="fstonetable" fn="fstonetable" pn="fstonetable" visible="1150" cn="含石材台面" copy="0" notrace="false"></th>
                <th lix="315" el="100" ek="fentry" id="fdesignremark" fn="fdesignremark" pn="fdesignremark" visible="1150" cn="设计方案备注" copy="0"></th>
                <th lix="315" el="100" ek="fentry" id="ffactoryremark" fn="ffactoryremark" pn="ffactoryremark" visible="0" cn="工厂订单备注" copy="0"></th>
                <th lix="314" el="116" ek="fentry" id="funstdtypefactory" fn="funstdtypefactory" pn="funstdtypefactory" visible="1150" cn="工厂单非标" copy="0" notrace="false"></th>
                <th lix="314" el="106" ek="fentry" id="fomsdeliver" fn="fomsdeliver" pn="fomsdeliver" visible="1086" cn="OMS送达方" refid="bas_deliver" copy="0"></th>
                <th lix="315" el="116" ek="fentry" id="fissubmitoms" fn="fissubmitoms" pn="fissubmitoms" visible="0" cn="已提交OMS" copy="0"></th>
                <th lix="315" el="116" ek="fentry" id="fisomscustomer" fn="fisomscustomer" pn="fisomscustomer" visible="0" cn="启用OMS定制说明" copy="0" defval="true"></th>
                <th lix="315" el="152" ek="fentry" id="forderattr_e" fn="forderattr" pn="forderattr_e" visible="1086" cn="订单属性" copy="0" vals="'1':'散单','2':'整家宅配','3':'大家居'" lock="-1"></th>
                <th lix="316" el="122" ek="fentry" visible="1086" id="faftreason" fn="faftreason" pn="faftreason" cn="售后原因" cg="售后原因" apipn="aftreason" refid="bd_enum" dfld="fenumitem"></th>
                <th lix="317" el="100" ek="fentry" id="fomsreason" fn="fomsreason" pn="fomsreason" visible="1150" cn="原因描述" copy="0" len="500"></th>
                <th lix="318" el="100" ek="fentry" id="fomsoptionid" fn="fomsoptionid" pn="fomsoptionid" visible="1150" cn="方案id" copy="0" lock="-1"></th>
                <th lix="319" el="116" ek="fentry" id="faftserviceisfree" fn="faftserviceisfree" pn="faftserviceisfree" visible="1086" cn="是否工厂责" copy="0"></th>
                <th lix="170" el="103" ek="fentry" id="fomsdeliverqty" fn="fomsdeliverqty" pn="fomsdeliverqty" visible="0" cn="符合条件的OMS送达方数量" lock="-1" format="0,000"></th>
                <th lix="317" el="100" ek="fentry" id="fomscategory" fn="fomscategory" pn="fomscategory" visible="1086" cn="商品品类" copy="0" lock="-1"></th>
                <th lix="317" el="135" ek="fentry" id="fomsfiles" fn="fomsfiles" pn="fomsfiles" cn="缩略图" visible="36" adld="true" width="260"></th>
                <th lix="317" el="100" ek="fentry" id="fomstexture" fn="fomstexture" pn="fomstexture" visible="1150" cn="主材质" copy="0"></th>
                <th lix="317" el="100" ek="fentry" id="fomscolor" fn="fomscolor" pn="fomscolor" visible="1150" cn="主颜色" copy="0"></th>
                <th lix="317" el="100" ek="fentry" id="fomsdesignfile" fn="fomsdesignfile" pn="fomsdesignfile" visible="1150" cn="订单设计方案文件" copy="0" lock="-1" len="2000"></th>
                <th lix="317" el="112" ek="fentry" id="fcustomerstanddate" fn="fcustomerstanddate" pn="fcustomerstanddate" cn="客户标准交期" format="yyyy-MM-dd" visible="1150" lock="-1"></th>
                <th lix="317" el="112" ek="fentry" id="fplaninstockdate" fn="fplaninstockdate" pn="fplaninstockdate" cn="计划入库日期" format="yyyy-MM-dd" visible="1150" lock="-1"></th>
                <th lix="317" el="113" ek="fentry" id="fomsorderdate" fn="fomsorderdate" pn="fomsorderdate" cn="提交OMS时间" format="yyyy-MM-dd HH:mm:ss" visible="1150" lock="-1" type="datetime"></th>
                <th lix="317" el="100" ek="fentry" id="fswjordernumber_e" fn="fswjordernumber_e" pn="fswjordernumber_e" visible="1086" cn="三维家单号" copy="0"></th>
                <th lix="317" el="100" ek="fentry" id="fswjdetailurl_e" fn="fswjdetailurl_e" pn="fswjdetailurl_e" visible="0" cn="三维家详情页跳转链接" copy="0" lock="-1" len="1000"></th>


                <th lix="311" el="106" ek="fentry" id="factivityid_e" fn="factivityid_e" pn="factivityid_e" visible="-1" cn="活动" copy="0" notrace="false" ts="" refid="ydj_activity" filter="" reflvt="0" dfld="" canchange="true"></th>

                <th lix="400" el="103" ek="fentry" id="fhqdeliveryqty" fn="fhqdeliveryqty" pn="fhqdeliveryqty" cn="基本单位总部已发货数" ctlfk="funitid" visible="36" width="160" lock="-1" copy="0"></th>
                <th lix="405" el="103" ek="fentry" id="fbizhqdeliveryqty" fn="fbizhqdeliveryqty" pn="fbizhqdeliveryqty" cn="总部已发货数" ctlfk="fbizunitid" basqtyfk="fhqdeliveryqty" visible="36" width="100" lock="-1" copy="0"></th>
                <th lix="410" el="103" ek="fentry" id="fpurinqty" fn="fpurinqty" pn="fpurinqty" cn="基本单位已采购入库数" ctlfk="funitid" visible="36" width="160" lock="-1" copy="0"></th>
                <th lix="415" el="103" ek="fentry" id="fbizpurinqty" fn="fbizpurinqty" pn="fbizpurinqty" cn="已采购入库数" ctlfk="fbizunitid" basqtyfk="fpurinqty" visible="36" width="100" lock="-1" copy="0"></th>
                <th lix="420" el="112" ek="fentry" id="fhqdeliverydate" fn="fhqdeliverydate" pn="fhqdeliverydate" cn="总部预计交货日期" visible="36" width="130" lock="-1" copy="0" desc="由采购订单总部下发接口反写"></th>
                <!--促销活动-->
                <th el="106" ek="fentry" id="fpromotionid" fn="fpromotionid" pn="fpromotionid" lix="200" refid="ydj_promotiondata" cn="促销主题" width="100" visible="1150" lock="-1" sformid=""></th>
                <th el="107" ek="fentry" id="fpromotionno" fn="fpromotionno" pn="fpromotionno" cn="促销编号" visible="1150" dispfk="fnumber" ctlfk="fpromotionid" sformid="" width="100" lock="-1" lix="301"></th>
                <th el="107" ek="fentry" id="fpromotiontype" fn="fpromotiontype" pn="fpromotiontype" cn="促销类型" desc="用于前端判断使用哪种促销活动逻辑" visible="0" dispfk="fpromotionformid" ctlfk="fpromotionid" sformid="" width="100" lock="-1" lix="301"></th>
                <th el="100" ek="fentry" id="fpromotionrule" fn="fpromotionrule" pn="fpromotionrule" cn="促销规则" visible="1150" sformid="" width="100" lock="-1" lix="301"></th>
                <th el="116" ek="fentry" id="fisusepromotion" fn="fisusepromotion" pn="fisusepromotion" cn="已参与促销活动" desc="用于判断商品促销活动范围" visible="0" width="80" lix="180"></th>
                <th el="100" ek="fentry" id="fpromotioncombono" fn="fpromotioncombono" pn="fpromotioncombono" cn="促销活动组合号" desc="用于判断多个商品行为同属一个促销活动" visible="0" width="80" lix="180"></th>
                <th el="103" ek="fentry" id="fbizpromotioncomboqty" fn="fbizpromotioncomboqty" pn="fbizpromotioncomboqty" cn="组合套餐数量" desc="当商品行数量变化时，联动变化套餐内的其他商品行的数量" ctlfk="fbizunitid" basqtyfk="fpromotioncomboqty" visible="0" width="80" lix="180"></th>
                <th el="103" ek="fentry" id="fpromotioncomboqty" fn="fpromotioncomboqty" pn="fpromotioncomboqty" cn="基本单位组合套餐数量" desc="当商品行数量变化时，联动变化套餐内的其他商品行的数量" ctlfk="funitid" visible="0" width="80" lix="180"></th>
                <th el="104" ek="fentry" id="fpromotionlowestprice" fn="fpromotionlowestprice" pn="fpromotionlowestprice" cn="特价最低价" width="100" visible="0" format="0,000.000000" dformat="0,000.00"></th>
                <th el="105" ek="fentry" id="fpromotiongiftamount" fn="fpromotiongiftamount" pn="fpromotiongiftamount" cn="满赠金额" width="100" visible="0" format="0,000.00" dformat="0,000.00"></th>
                <th el="103" ek="fentry" id="fpromotiongiftqty" fn="fpromotiongiftqty" pn="fpromotiongiftqty" cn="满赠数量" ctlfk="funitid" width="100" visible="0"></th>
                <th el="103" ek="fentry" id="fbizpromotiongiftqty" fn="fbizpromotiongiftqty" pn="fbizpromotiongiftqty" cn="基本单位满赠数量" ctlfk="fbizunitid" basqtyfk="fpromotiongiftqty" width="100" visible="0"></th>
                <th el="116" ek="fentry" id="fisgiftdiscount" fn="fisgiftdiscount" pn="fisgiftdiscount" cn="赠品参与折扣计算" width="100" visible="0"></th>

                <!--【采购折前金额】=【采购单价（折前）】*【销售数量】-->
                <th lix="500" el="105" ek="fentry" id="fpurfacprice" fn="fpurfacprice" pn="fpurfacprice" cn="采购单价（折前）" visible="-1" copy="0" notrace="false" uaul="true" canchange="true" roundType="0" format="0,000.00"></th>
                <th lix="501" el="105" ek="fentry" id="fpurfacamount" fn="fpurfacamount" pn="fpurfacamount" cn="采购折前金额" visible="-1" copy="0" notrace="false" uaul="true" canchange="true" roundType="0" format="0,000.00"></th>

                <th el="100" ek="fentry" visible="0" id="faibedorderid" fn="faibedorderid" pn="faibedorderid" cn="床垫选配单id" copy="0" lock="-1" />

                <!--【调拨在途量】-->
                <th lix="502" el="103" ek="fentry" id="findbqty" fn="findbqty" pn="findbqty" cn="调拨在途量" visible="1086" width="100" lock="-1" canchange="false"></th>

                <th lix="503" el="152" ek="fentry" visible="-1" id="fthirdsource" fn="fthirdsource" pn="fthirdsource" cn="第三方来源" cg="第三方来源"
                    vals="'ts_type_01':'三维家','ts_type_02':'CAD图纸'" ,defval=""></th>

                <th lix="800" el="103" ek="fentry" id="funstockoutqty" fn="funstockoutqty" pn="funstockoutqty" ts="" visible="1148" cn="未出库基本数量" lock="-1" copy="0"></th>

                <th lix="800" el="116" ek="fentry" visible="0" id="fisfrominventory" fn="fisfrominventory" pn="fisfrominventory" cn="是否从库存查询选择的商品" defval="false" lock="-1" copy="0"></th>
                <th lix="800" el="116" ek="fentry" visible="32" id="fisfromfirstinventory" fn="fisfromfirstinventory" pn="fisfromfirstinventory" cn="是否一级库存携带" defval="false" lock="-1" copy="0"></th>
                <th lix="810" el="103" ek="fentry" id="fbizreturningqty" fn="fbizreturningqty" pn="fbizreturningqty" ts="" visible="1084" cn="销售退换中数量" lock="-1" copy="0"></th>

                <th lix="815" el="102" ek="fentry" id="fhqdistrate" fn="fhqdistrate" pn="fhqdistrate" cn="总部折扣率" width="60" visible="1086" format="0,000.00" dformat="0,000.00" lock="-1"></th>

                <th lix="810" el="103" ek="fentry" id="fhqpurqty" fn="fhqpurqty" pn="fhqpurqty" ts="" visible="-1" cn="已提交总部或终审采购数量" lock="-1" copy="0"></th>

                <th lix="227" el="104" ek="fentry" id="fsubrecdealamount_gb" fn="fsubrecdealamount_gb" pn="fsubrecdealamount_gb" cn="明细国补预收成交金额" width="90" lock="-1" visible="1150" format="0,000.000000" dformat="0,000.00" canchange="true" notrace="false"></th>
                <th lix="227" el="104" ek="fentry" id="fsubrecsumamount_gb" fn="fsubrecsumamount_gb" pn="fsubrecsumamount_gb" cn="明细国补预收补贴金额" width="90" lock="-1" visible="1150" format="0,000.000000" dformat="0,000.00" canchange="true" notrace="false"></th>
                <th lix="227" ek="fentry" el="101" id="fsubrenewseq" fn="fsubrenewseq" pn="fsubrenewseq" visible="1150" cn="焕新预售单行号" copy="0" width="200" lock="-1" notrace="false"></th>
                <!-- 一件代发 -->
                <th lix="316" el="122" ek="fentry" visible="1086" id="fdeliverytype" fn="fdeliverytype" pn="fdeliverytype" cn="交货方式" cg="一件代发交货方式" apipn="deliverytype" refid="bd_enum" dfld="fenumitem" defval="''" canchange="true"></th>
                <th lix="316" el="122" ek="fentry" visible="1086" id="fentrysignstatus" fn="fentrysignstatus" pn="fentrysignstatus" cn="行签收状态" cg="行签收状态" apipn="entrysignstatus" refid="bd_enum" dfld="fenumitem" lock="-1"></th>
                <!--<th lix="231" el="116" ek="fentry" id="fapportionflag" fn="fapportionflag" pn="fapportionflag" cn="分摊标识" visible="32" apipn="apportionflag" defval="false"></th>
            <th lix="229" el="105" ek="fentry" id="fapportionbefdealamount" fn="fapportionbefdealamount" pn="fapportionbefdealamount" cn="分摊前成交金额" lock="0" visible="1150" format="0,000.00" apipn="dealAmount" canchange="true" notrace="false"></th>
            <th lix="227" el="104" ek="fentry" id="fapportionbefdealprice" fn="fapportionbefdealprice" pn="fapportionbefdealprice" cn="分摊前成交单价" width="90" lock="-1" visible="0" format="0,000.000000" dformat="0,000.00" copy="0" apipn="dealPrice" canchange="true" notrace="false"></th>-->
                <th lix="229" el="105" ek="fentry" id="frounded_fdirectdealamount" fn="frounded_fdirectdealamount" pn="frounded_fdirectdealamount" cn="直营尾差" width="90" lock="-1" visible="32" format="0,000.00" apipn="rounded_dealAmount"></th>
                <th lix="150" ek="fentry" el="101" id="fseq_e" fn="fseq_e" pn="fseq_e" visible="0" cn="顺序" copy="0" width="200" lock="-1" notrace="false"></th>
                <th lix="150" ek="fentry" el="100" id="fsapitemseq" fn="fsapitemseq" pn="fsapitemseq" visible="0" cn="sap行号" copy="0" width="200" lock="-1" notrace="false"></th>
                <th lix="227" el="104" ek="fentry" id="fcommissionamount_e" fn="fcommissionamount_e" pn="fcommissionamount_e" cn="行佣金金额" width="90" lock="0" visible="32" format="0,000.000000" dformat="0,000.00" apipn="dealPrice" canchange="true" notrace="false" copy="0" ></th>
                <th lix="227" el="104" ek="fentry" id="fcommissionrate_e" fn="fcommissionrate_e" pn="fcommissionrate_e" cn="行佣金比例" width="90" lock="0" visible="32" format="0,000.000000" dformat="0,000.00" apipn="dealPrice" canchange="true" notrace="false" copy="0" ></th>
            </tr>
        </table>

        <!--转单信息-->
        <input group="转单信息" el="100" ek="fbillhead" id="freceivercontractnumber" fn="freceivercontractnumber" pn="freceivercontractnumber" cn="接单方合同编号" visible="0" copy="0" lock="0" />
        <input group="转单信息" el="100" ek="fbillhead" id="freceivercontractid" fn="freceivercontractid" pn="freceivercontractid" cn="接单方合同Id" visible="0" copy="0" lock="-1" />
        <input group="转单信息" el="106" ek="fbillhead" id="freceiveragentid" fn="freceiveragentid" pn="freceiveragentid" cn="接单方经销商ID" refid="bas_agent" dfld="fname,fnumber" visible="0" copy="0" lock="-1" />
        <input group="转单信息" el="100" ek="fbillhead" id="freceiveragentno" fn="freceiveragentno" pn="freceiveragentno" cn="接单方经销商编码" visible="1150" copy="0" lock="0" />
        <input group="转单信息" el="100" ek="fbillhead" id="freceiveragent" fn="freceiveragent" pn="freceiveragent" cn="接单方经销商" visible="1150" copy="0" lock="0" />
        <input group="转单信息" el="106" ek="fbillhead" id="freceiverstore" fn="freceiverstore" pn="freceiverstore" cn="接单方门店" refid="ydj_dept" visible="0" copy="0" lock="0" />
        <input group="转单信息" el="100" ek="fbillhead" id="freceiversaler" fn="freceiversaler" pn="freceiversaler" cn="接单方销售员" visible="0" copy="0" lock="0" />
        <input group="转单信息" el="100" ek="fbillhead" id="freceiveragentcontactperson" fn="freceiveragentcontactperson" pn="freceiveragentcontactperson" cn="接单方经销商联系人" visible="0" copy="0" lock="0" />
        <input group="转单信息" el="100" ek="fbillhead" id="freceivercontactpersonphone" fn="freceivercontactpersonphone" pn="freceivercontactpersonphone" cn="接单方联系人手机号" visible="0" copy="0" lock="0" />
        <input group="转单信息" el="112" ek="fbillhead" id="ftransferdate" fn="ftransferdate" pn="ftransferdate" cn="转单日期" visible="0" copy="0" lock="0" format="yyyy-MM-dd" canchange="true" />
        <input group="转单信息" el="105" ek="fbillhead" id="ftargetagentamount" fn="ftargetagentamount" pn="ftargetagentamount" cn="发货结算金额" format="0,000.00" visible="-1" copy="0" lock="0" />
        <input group="转单信息" el="102" ek="fbillhead" id="ftransfercostprice" fn="ftransfercostprice" pn="ftransfercostprice" cn="成本价格" format="0,000.00" visible="-1" copy="0" lock="0" />
        <input group="转单信息" el="102" ek="fbillhead" id="fotherfee" fn="fotherfee" pn="fotherfee" cn="其他费用" format="0,000.00" visible="-1" copy="0" lock="0" />
        <input group="转单信息" el="100" ek="fbillhead" id="ftransferremark" fn="ftransferremark" pn="ftransferremark" cn="转单备注" visible="-1" copy="0" lix="40" lock="0" len="1000" />

        <!--商品子明细（明细）-->
        <table id="fdetail" el="53" pek="fentry" pk="fdetailid" tn="t_ydj_orderdetail" pn="fdetail" cn="商品子明细" apipn="detail">
            <tr>
                <th el="106" ek="fdetail" id="fproductid_sub" fn="fproductid" pn="fproductid" cn="商品" refid="ydj_product" multsel="true" sformid="" width="160" visible="0" apipn="product" filter="fendsale='0'"></th>
                <th el="100" ek="fdetail" id="fproductname_sub" fn="fproductname" pn="fproductname" cn="物料名称" width="160" visible="0" apipn="productName"></th>
                <th el="116" ek="fdetail" id="fisadjust_sub" fn="fisadjust" pn="fisadjust" cn="手工调整" width="90" visible="0" apipn="isAdjust"></th>

                <th el="109" ek="fdetail" id="funitid_sub" fn="funitid" pn="funitid" cn="基本单位" refid="ydj_unit" sformid="" ctlfk="fproductid_sub" lock="-1" filter="fisbaseunit='1'" width="80" visible="0" apipn="unit"></th>
                <th el="103" ek="fdetail" id="fqty_sub" fn="fqty" pn="fqty" cn="基本单位数量" ctlfk="funitid_sub" format="0,000.00" width="100" visible="0" apipn="qty"></th>
                <th el="109" ek="fdetail" id="fbizunitid_sub" fn="fbizunitid" pn="fbizunitid" cn="销售单位" refid="ydj_unit" sformid="" ctlfk="fproductid_sub" width="90" visible="0" apipn="salUnit" notrace="false"></th>
                <th el="103" ek="fdetail" id="fbizqty_sub" fn="fbizqty" pn="fbizqty" cn="销售数量" ctlfk="fbizunitid_sub" format="0,000.00" basqtyfk="fqty_sub" width="80" visible="0" apipn="salQty" notrace="false"></th>

                <th el="132" ek="fdetail" id="fattrinfo_sub" fn="fattrinfo" pn="fattrinfo" cn="辅助属性" ctlfk="fproductid_sub" pricefk="fprice" width="140" visible="0" apipn="attrInfo"></th>
                <th el="133" ek="fdetail" id="flength_sub" fn="flength" pn="flength" cn="长" visible="0" width="65" apipn="length"></th>
                <th el="133" ek="fdetail" id="fwidth_sub" fn="fwidth" pn="fwidth" cn="宽" visible="0" width="65" apipn="width"></th>
                <th el="133" ek="fdetail" id="fthick_sub" fn="fthick" pn="fthick" cn="厚" visible="0" width="65" apipn="thick"></th>

                <th el="104" ek="fdetail" id="fprice_sub" fn="fprice" pn="fprice" cn="单价" width="100" visible="0" format="0,000.000000" dformat="0,000.00" apipn="price"></th>
                <th el="105" ek="fdetail" id="famount_sub" fn="famount" pn="famount" cn="金额" width="100" lock="-1" visible="0" format="0,000.00" apipn="amount"></th>
                <th el="102" ek="fdetail" id="fdistrate_sub" fn="fdistrate" pn="fdistrate" cn="折扣" width="65" visible="0" apipn="distRate"></th>
                <th el="105" ek="fdetail" id="fdistamount_sub" fn="fdistamount" pn="fdistamount" cn="折扣额" width="100" visible="0" format="0,000.00" apipn="distAmount"></th>
                <th el="104" ek="fdetail" id="fdealprice_sub" fn="fdealprice" pn="fdealprice" cn="成交单价" width="100" lock="-1" visible="0" format="0,000.000000" dformat="0,000.00" apipn="dealPrice"></th>
                <th el="105" ek="fdetail" id="fdealamount_sub" fn="fdealamount" pn="fdealamount" cn="成交金额" width="100" lock="-1" visible="0" format="0,000.00" apipn="dealAmount"></th>
                <th el="100" ek="fdetail" id="fdescription_sub" fn="fdescription" pn="fdescription" cn="非生产备注" width="120" visible="0" apipn="description" len="1000"></th>

                <th el="102" ek="fdetail" id="farea_sub" fn="farea" pn="farea" cn="面积m2" width="70" visible="0" apipn="area"></th>
                <th el="116" ek="fdetail" id="fisnsc_sub" fn="fisnsc" pn="fisnsc" cn="非标" width="65" visible="0" apipn="isNsc"></th>
                <th el="102" ek="fdetail" id="fnsc_sub" fn="fnsc" pn="fnsc" cn="非标系数" width="80" visible="0" apipn="nsc"></th>
            </tr>
        </table>

        <table id="fdetail" el="53" pek="fentry" pk="fdetailid" tn="t_ydj_orderdetail" pn="fdetail" cn="商品子明细" apipn="detail">
            <tr>
                <th el="106" ek="fdetail" id="fproductid_sub" fn="fproductid" pn="fproductid" cn="商品" refid="ydj_product" multsel="true" sformid="" width="160" visible="0" apipn="product" filter="fendsale='0'"></th>
                <th el="100" ek="fdetail" id="fproductname_sub" fn="fproductname" pn="fproductname" cn="物料名称" width="160" visible="0" apipn="productName"></th>
                <th el="116" ek="fdetail" id="fisadjust_sub" fn="fisadjust" pn="fisadjust" cn="手工调整" width="90" visible="0" apipn="isAdjust"></th>

                <th el="109" ek="fdetail" id="funitid_sub" fn="funitid" pn="funitid" cn="基本单位" refid="ydj_unit" sformid="" ctlfk="fproductid_sub" lock="-1" filter="fisbaseunit='1'" width="80" visible="0" apipn="unit"></th>
                <th el="103" ek="fdetail" id="fqty_sub" fn="fqty" pn="fqty" cn="基本单位数量" ctlfk="funitid_sub" format="0,000.00" width="100" visible="0" apipn="qty"></th>
                <th el="109" ek="fdetail" id="fbizunitid_sub" fn="fbizunitid" pn="fbizunitid" cn="销售单位" refid="ydj_unit" sformid="" ctlfk="fproductid_sub" width="90" visible="0" apipn="salUnit" notrace="false"></th>
                <th el="103" ek="fdetail" id="fbizqty_sub" fn="fbizqty" pn="fbizqty" cn="销售数量" ctlfk="fbizunitid_sub" format="0,000.00" basqtyfk="fqty_sub" width="80" visible="0" apipn="salQty" notrace="false"></th>

                <th el="132" ek="fdetail" id="fattrinfo_sub" fn="fattrinfo" pn="fattrinfo" cn="辅助属性" ctlfk="fproductid_sub" pricefk="fprice" width="140" visible="0" apipn="attrInfo"></th>
                <th el="133" ek="fdetail" id="flength_sub" fn="flength" pn="flength" cn="长" visible="0" width="65" apipn="length"></th>
                <th el="133" ek="fdetail" id="fwidth_sub" fn="fwidth" pn="fwidth" cn="宽" visible="0" width="65" apipn="width"></th>
                <th el="133" ek="fdetail" id="fthick_sub" fn="fthick" pn="fthick" cn="厚" visible="0" width="65" apipn="thick"></th>

                <th el="104" ek="fdetail" id="fprice_sub" fn="fprice" pn="fprice" cn="单价" width="100" visible="0" format="0,000.000000" dformat="0,000.00" apipn="price"></th>
                <th el="105" ek="fdetail" id="famount_sub" fn="famount" pn="famount" cn="金额" width="100" lock="-1" visible="0" format="0,000.00" apipn="amount"></th>
                <th el="102" ek="fdetail" id="fdistrate_sub" fn="fdistrate" pn="fdistrate" cn="折扣" width="65" visible="0" apipn="distRate"></th>
                <th el="105" ek="fdetail" id="fdistamount_sub" fn="fdistamount" pn="fdistamount" cn="折扣额" width="100" visible="0" format="0,000.00" apipn="distAmount"></th>
                <th el="104" ek="fdetail" id="fdealprice_sub" fn="fdealprice" pn="fdealprice" cn="成交单价" width="100" lock="-1" visible="0" format="0,000.000000" dformat="0,000.00" apipn="dealPrice"></th>
                <th el="105" ek="fdetail" id="fdealamount_sub" fn="fdealamount" pn="fdealamount" cn="成交金额" width="100" lock="-1" visible="0" format="0,000.00" apipn="dealAmount"></th>
                <th el="100" ek="fdetail" id="fdescription_sub" fn="fdescription" pn="fdescription" cn="非生产备注" width="120" visible="0" apipn="description" len="1000"></th>

                <th el="102" ek="fdetail" id="farea_sub" fn="farea" pn="farea" cn="面积m2" width="70" visible="0" apipn="area"></th>
                <th el="116" ek="fdetail" id="fisnsc_sub" fn="fisnsc" pn="fisnsc" cn="非标" width="65" visible="0" apipn="isNsc"></th>
                <th el="102" ek="fdetail" id="fnsc_sub" fn="fnsc" pn="fnsc" cn="非标系数" width="80" visible="0" apipn="nsc"></th>
            </tr>
        </table>

        <!--预留转移记录（商品明细表体）-->
        <table id="freservetransfer" el="53" pek="fentry" pk="freservetransferid" tn="t_ydj_orderreservetransfer" pn="freservetransfer" cn="预留转移记录" apipn="reserveTransfer">
            <tr>
                <th el="152" ek="freservetransfer" id="freservetransfertype" fn="freservetransfertype" pn="freservetransfertype" cn="预留转移类型" copy="0" notrace="false" vals="'1':'转出','2':'转入'" must="1" lock="-1" width="100" visible="1150" lix="800"></th>

                <th el="106" ek="freservetransfer" id="freservetransferorderid" fn="freservetransferorderid" pn="freservetransferorderid" cn="关联合同编码" copy="0" refid="ydj_order" must="1" lock="-1" visible="0" lix="810"></th>

                <th el="107" ek="freservetransfer" id="freservetransferorderno" pn="freservetransferorderno" cn="关联合同编码" copy="0" ctlfk="freservetransferorderid" dispfk="fbillno" must="1" lock="-1" width="200" visible="1150" lix="811"></th>

                <th el="106" ek="freservetransfer" id="freservetransfercustomerid" fn="freservetransfercustomerid" pn="freservetransfercustomerid" cn="客户" notrace="false" refid="ydj_customer" lock="-1" visible="0" lix="820"></th>

                <th el="107" ek="freservetransfer" id="freservetransfercustomername" pn="freservetransfercustomername" cn="客户" notrace="false" ctlfk="freservetransfercustomerid" dispfk="fname" lock="-1" width="100" visible="1150" lix="821"></th>

                <th el="107" ek="freservetransfer" id="freservetransferproductno" pn="freservetransferproductno" cn="商品编码" notrace="false" ctlfk="freservetransferproductid" dispfk="fnumber" visible="1150" lix="830"></th>
                <th el="106" ek="freservetransfer" id="freservetransferproductid" fn="freservetransferproductid" pn="freservetransferproductid" cn="商品" notrace="false" refid="ydj_product" must="1" lock="-1" width="200" visible="1150" lix="840"></th>

                <th el="100" ek="freservetransfer" id="freservetransferorderentryid" fn="freservetransferorderentryid" pn="freservetransferorderentryid" cn="分录行内码" desc="预留转移类型=转入，取被释放的商品明细行id；预留转移类型=转出时，取借货的商品明细行id" copy="0" lock="-1" visible="0" lix="850"></th>

                <th el="109" ek="freservetransfer" id="fbizreservetransferunitid" fn="fbizreservetransferunitid" pn="fbizreservetransferunitid" cn="销售单位" refid="ydj_unit" sformid="" ctlfk="freservetransferproductid" width="90" must="1" lock="-1" visible="1086" lix="861"></th>
                <th el="103" ek="freservetransfer" id="fbizreservetransferinqty" fn="fbizreservetransferinqty" pn="fbizreservetransferinqty" cn="预留转入数量" ctlfk="fbizreservetransferunitid" format="0,000.00" basqtyfk="freservetransferinqty" width="100" canchange="true" lock="-1" visible="1150" lix="862"></th>
                <th el="103" ek="freservetransfer" id="fbizreservetransferoutqty" fn="fbizreservetransferoutqty" pn="fbizreservetransferoutqty" cn="预留转出数量" ctlfk="fbizreservetransferunitid" format="0,000.00" basqtyfk="freservetransferoutqty" width="100" lock="-1" visible="1150" lix="863"></th>

                <th el="109" ek="freservetransfer" id="freservetransferunitid" fn="freservetransferunitid" pn="freservetransferunitid" cn="基本单位" refid="ydj_unit" sformid="" ctlfk="freservetransferproductid" width="80" must="1" lock="-1" visible="1086" lix="870"></th>
                <th el="103" ek="freservetransfer" id="freservetransferinqty" fn="freservetransferinqty" pn="freservetransferinqty" cn="基本单位预留转入数量" ctlfk="freservetransferunitid" format="0,000.00" width="100" lock="-1" visible="1086" lix="871"></th>
                <th el="103" ek="freservetransfer" id="freservetransferoutqty" fn="freservetransferoutqty" pn="freservetransferoutqty" cn="基本单位预留转出数量" ctlfk="freservetransferunitid" format="0,000.00" width="100" lock="-1" visible="1086" lix="872"></th>

                <!--用于记录采购入库转移-->
                <th el="100" ek="freservetransfer" id="freservetransferpoinentryids" fn="freservetransferpoinentryids" pn="freservetransferpoinentryids" cn="采购入库明细行ids" copy="0" lock="-1" visible="0" lix="850" len="1000"></th>
            </tr>
        </table>


        <!--图纸信息-->
        <table id="fdrawentity" el="52" pk="fentryid" tn="t_ydj_orderdrawentry" pn="FDrawEntity" cn="图纸附件" kfks="ffileid">
            <tr>
                <th ek="fdrawentity" el="100" id="ffilename" fn="ffilename" pn="ffilename" cn="文件名" visible="96" width="350" lock="-1" copy="0" apipn="fileName"></th>
                <th ek="fdrawentity" el="110" id="ffileid" fn="ffileid" pn="ffileid" cn="文件id" visible="0" lock="-1" copy="0" apipn="fileId"></th>
                <th el="122" ek="fdrawentity" id="fdrawstatus" fn="fdrawstatus" pn="fdrawstatus" cg="合同图纸状态" cn="状态" refid="bd_enum" dfld="fenumitem" defVal="'draw_status_01'" width="120" visible="1150" lock="-1" canchange="true"></th>
                <th ek="fdrawentity" el="100" id="ffileformat" fn="ffileformat" pn="ffileformat" cn="文件格式" visible="96" width="70" lock="-1" copy="0" apipn="fileFormat"></th>
                <th ek="fdrawentity" el="100" id="ffilesize" fn="ffilesize" pn="ffilesize" cn="文件大小" visible="96" width="70" lock="-1" copy="0" apipn="fileSize"></th>
                <th ek="fdrawentity" el="100" id="fnote_d" fn="fnote" pn="fnote" cn="备注" visible="96" width="260" copy="0" apipn="note"></th>
                <th ek="fdrawentity" el="100" id="fuploader" fn="fuploader" pn="fuploader" cn="最后操作人" visible="96" width="65" lock="-1" copy="0" apipn="uploader"></th>
                <th ek="fdrawentity" el="100" id="fuploaderid" fn="fuploaderid" pn="fuploaderid" cn="最后操作人id" visible="0" width="100" lock="-1" copy="0" apipn="uploaderId"></th>
                <th ek="fdrawentity" el="113" id="fuptime" fn="fuptime" pn="fuptime" cn="最后更新时间" visible="96" width="120" lock="-1" copy="0" apipn="uptime"></th>
                <th ek="fdrawentity" el="100" id="fsourceentryid" fn="fsourceentryid" cn="源单明细ID" visible="0" lock="-1" copy="0" width="120" apipn="sourceEntryId"></th>
                <th ek="fdrawentity" el="100" id="ffilegrouping" fn="ffilegrouping" pn="ffilegrouping" cn="文件分组（OMS相关）" visible="0" lock="-1" align="center" copy="0"></th>
                <th ek="fdrawentity" el="100" id="ftabledrawing" fn="ftabledrawing" pn="ftabledrawing" cn="台面图纸（OMS相关）" visible="0" lock="-1" align="center" copy="0"></th>
                <th ek="fdrawentity" el="100" id="funstdtypedoc" fn="funstdtypedoc" pn="funstdtypedoc" cn="非标凭证（OMS相关）" visible="0" lock="-1" align="center" copy="0"></th>
                <th ek="fdrawentity" el="144" id="foplist" fn="foplist" pn="foplist" cn="操作" btnid="download,delete" width="120" btntxt="下载,删除" visible="96" lock="-1" align="left"></th>
            </tr>
        </table>

        <!--费用信息（费用收入）-->
        <table id="fexpenseentry" el="52" pk="fentryid" tn="t_ydj_orderexpense" pn="fexpenseentry" cn="费用收入" kfks="fexpenseitemid,famount" apipn="expenseEntry">
            <tr>
                <th el="106" ek="fexpenseentry" id="fexpenseitemid" fn="fexpenseitemid" pn="fexpenseitemid" cn="费用项目" apipn="expenseItem" refid="ydj_expenseitem" sformid="" width="170" visible="1124" filter="ftype='expensetype_01'" canchange="true"></th>
                <th el="105" ek="fexpenseentry" id="famount_ee" fn="famount" pn="famount" cn="金额" width="100" visible="1124" format="0,000.00" apipn="amount" canchange="true"></th>
                <th el="100" ek="fexpenseentry" id="fdescription_ee" fn="fdescription" pn="fdescription" cn="备注" width="280" visible="1124" apipn="description" canchange="true"></th>
            </tr>
        </table>


        <!--费用信息（费用支出）-->
        <table id="fdisburseentry" el="52" pk="fentryid" tn="t_ydj_orderdisburse" pn="fdisburseentry" cn="费用支出" kfks="fexpenseitemid,famount" apipn="disburseEntry">
            <tr>
                <th el="106" ek="fdisburseentry" id="fexpenseitem_d" fn="fexpenseitemid" pn="fexpenseitemid" cn="费用项目" refid="ydj_expenseitem" sformid="" apipn="expenseItem" width="170" visible="1124" filter="ftype='expensetype_02'"></th>
                <th el="105" ek="fdisburseentry" id="famount_d" fn="famount" pn="famount" cn="金额" width="80" visible="1124" format="0,000.00" apipn="amount"></th>
                <th el="100" ek="fdisburseentry" id="fdescription_dise" fn="fdescription" pn="fdescription" cn="备注" width="280" visible="1124" apipn="description" canchange="true"></th>
                <th el="100" ek="fdisburseentry" id="fsettle_d" fn="fsettle" pn="fsettle" cn="结算方" width="220" visible="0" format="0,000.00" apipn="settle"></th>
                <th el="116" ek="fdisburseentry" id="fsettlestatus_d" fn="fsettlestatus" pn="fsettlestatus" cn="结算状态" width="80" visible="0" lock="-1" apipn="isSettle"></th>

                <!--按钮字段不存数据库-->
                <th el="144" ek="fdisburseentry" id="fsettleop_d" fn="" pn="fsettleop" cn="结算" width="70" visible="0" btnid="settle" btntxt="结算"></th>
            </tr>
        </table>
    </div>


    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fentry" id="transferorderapply" op="transferorderapply" opn="转单申请" ubl="1"></ul>
        <ul el="10" ek="fbillhead" id="transferorderapplybatch" op="transferorderapplybatch" opn="转单申请" ubl="1"></ul>
        <ul el="10" ek="fbillhead" id="orderclose" op="orderclose" opn="订单关闭" data="" ubl="1">
            <li el="17" sid="2002" cn="自动释放预留数量" data="{
                'message':'订单关闭，自动释放预留数量',
                'preCondition':'',
                'releaseWay':4,
                'releaseType':0}"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="orderunclose" op="orderunclose" opn="订单反关闭" data="" ubl="1">
        </ul>

        <ul el="10" ek="fbillhead" id="push2outstock" op="push" opn="出库" data="{'parameter':{'ruleId':'ydj_order2stk_sostockout'}}" permid="" ubl="1"></ul>
        <ul el="10" ek="fbillhead" id="push2returngood" op="push" opn="退货" data="{'parameter':{'ruleId':'stk_sostockout2stk_sostockreturn'}}" permid="" ubl="1"></ul>
        <ul el="10" ek="fbillhead" id="push2borrowgood" op="push" opn="借货" data="{'parameter':{'ruleId':'ydj_order2stk_inventorytransfer','transferType':'invtransfer_biztype_02'}}" permid="" ubl="1"></ul>
        <ul el="10" ek="fbillhead" id="pushhqder" op="push" opn="总部下单" data="{'parameter':{'ruleId':'ydj_order2ydj_purchaseorder2'}}" permid="" ubl="1"></ul>
        <ul el="10" id="tbCost" op="cost" opn="成本核算" data="" permid="ydj_order_cost" ubl="1"></ul>
        <ul el="10" id="areatotal" op="areatotal" opn="空间小计" data="" permid=""></ul>
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存">

            <li el="11" vid="510" ek="fentry" cn="销售单位和基本单位一致时，要求销售数量和基本单位数量也要一致"
                data="{'expr':'fbizqty==fqty','message':'销售单位和基本单位一致时，要求销售数量和基本单位数量也要一致！'}"
                precon="fbizunitid==funitid"></li>

            <li el="11" vid="510" cn="交货日期不能小于业务日期" data="{'expr':'fdeliverydate >= forderdate','message':'交货日期不能小于业务日期！'}"></li>
            <li el="11" vid="510" ek="fentry" cn="货主字段不能为空!" data="{'expr':'(fownertype=\'\' and fownerid=\'\') or (fownertype!=\'\' and fownerid!=\'\')','message':'货主字段不能为空!'}"></li>
            <li el="11" vid="3007" cn="套件商品明细的套件组合号不能为空" data="{'productFieldKey':'fproductid','suitCombNumberFieldKey':'fsuitcombnumber'}"></li>
            <!--<li el="11" vid="3008" cn="允许选配的商品明细的辅助属性不能为空" data="{'productFieldKey':'fproductid'}"></li>-->
        </ul>

        <ul el="10" ek="fbillhead" id="submitchange" op="submitchange" opn="提交变更">
            <!--变更中校验必须上传附件-->
            <li el="11" vid="512" precon="SYS.GetSystemParam('bas_storesysparam','fhasvoucherwhenorderchange',False)==True AND fchangestatus=='1'" cn="合同变更时，在提交前必须要上传附件！" data="{'filter':'*.*','message':'合同变更时，在提交前必须要上传附件！'}"></li>
            <!--变更生效服务，提交变更时提前检查数据，以免审核时无法审核也无法取消变更,故设置变更选项为仅检查数量选项[onlyCheckQty]-->
            <li el="17" sid="1004" cn="变更生效执行" data="{'changeOption':'onlyCheckQty'}"></li>

        </ul>

        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存">
            <li el="17" sid="1004" cn="变更生效执行"></li>
            <li el="11" vid="517" id="save_valid_sourcebill" cn="保存时校验源单明细行是否存在于源单中"
                data="{'sourceTypeFieldKey':'fsourcetype_e','sourceNoFieldKey':'fsourcenumber_e','sourceEntryIdFieldKey':'fsourceentryid_e'}" precon=""></li>
        </ul>

        <ul el="10" ek="fbillhead" id="unchange" op="unchange" opn="取消变更">

            <li el="11" vid="3011" cn="如果变更状态是“变更中”或“变更已提交”且存在非标审批状态是“待审批”商品明细行时，不允许执行该操作" data=""></li>

            <li el="17" sid="2003" cn="更新预留" data="{
                'preCondition':'fstatus!=\'D\' and fstatus!=\'E\'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fproductid'}
                ]}"></li>

        </ul>

        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核">

            <!--<li el="17" sid="1003" cn="审核时将合同协同到总部" data="{
        'targetFormId':'ydj_order',
        'targetOperationNo':'save',
        'syncFieldKeys':[
            'ftype','fphone','fprovince','fcity','fregion','faddress','fdeptid','fstaffid',
            'fchannel','fstylistid','fdesignscheme','fscalerecord','foldorderno','fismain','fdutyid','fratio',
            'famount_ed','fdescription_ed','fduty','fsubjoincause','fdutyid_ezb','fdeptid_ezb','fratio_ezb',
            'famount_ezb','fdescription_ezb','fiswholedis','fdisopt','fdiscscale','fdiscscale_temp','fisfavor',
            'ffavorway','ffavoropt','ffixedprice','fdistsumamount','fdistsumrate','fdistamount','ffaceamount',
            'fexpense','fcollectamount','fcollectedamount','fsumamount','fexpenditure','fprofit','fsumcost',
            'fproductcost','fcostsource','fsumoutqty','fisself','fproductid','fcustomdes_e','funitid','fattrinfo',
            'fqty','fprice','famount','fdistrate','fdistamount_e','fdealprice','fdealamount_e','fsupplierid',
            'fdeptid_e','fisreqiured','forderstatus','fisdelivery','fdeliverystatus','fisoutspot','foperationmode',
            'fdescription_e','foutqty','fsellprice','flength','fwidth','fthick','farea','fisnsc','fnsc','fissub',
            'fissize','fcostprice','fcost','fgain','fmtono','fproductid_sub','fproductname_sub','fisadjust_sub',
            'funitid_sub','fqty_sub','fattrinfo_sub','flength_sub','fwidth_sub','fthick_sub','fprice_sub',
            'famount_sub','fdistrate_sub','fdistamount_sub','fdealprice_sub','fdealamount_sub','fdescription_sub',
            'farea_sub','fisnsc_sub','fnsc_sub','fexpenseitemid','famount_ee','fdescription_ee','fexpenseitem_d',
            'fsettle_d','famount_d','fsettlestatus_d','fcustomerid','forderdate',
            'fdeliverydate','fbrandid','fcommercebillno','fdealamount_h','freceivable',
            'funreceived','fdescription','flinkstaffid','fbillno','fstatus','ffilename','ffileid','ffileformat',
            'ffilesize','fnote_d','fuploader','fuploaderid','fuptime','fsourceentryid','fbilltype','fsumreceivable',
            'fbizunitid','fbizqty','fstandardprice','flogisticscompanyid','fstorehouseid','fstaff_e','freducedbrokerage'
        ],
        'targetCIdFieldKey':'',
        'targetPIdFieldKey':'',
        'syncAuxPropFieldKeys':[],
        'simpleData':{'syncOpCode':'audit'},
        'preCondition':''}"></li>-->
            <!--变更生效服务-->
            <li el="17" sid="1004" cn="变更生效执行"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <li el="11" vid="511" cn="已经生成了下游单据，不允许反审核" data='{"expr": [
                {"linkFormId":"pur_reqorder", "sourceLinkFieldKey":"fbillno","linkFieldKey":"fsourcenumber"},
                {"linkFormId":"ydj_purchaseorder", "sourceLinkFieldKey":"fbillno","linkFieldKey":"fsourcenumber"},
                {"linkFormId":"sal_deliverynotice", "linkFieldKey":"fsourceinterid"},
                {"linkFormId":"stk_sostockout", "sourceLinkFieldKey":"fbillno","linkFieldKey":"fsourcenumber"},
                {"linkFormId":"stk_inventorytransfer", "sourceLinkFieldKey":"fbillno","linkFieldKey":"fsourcenumber"}],
                "message":"已经生成了下游单据，不允许反审核！"}'></li>
            <li el="11" vid="510" ek="fbillhead" cn="合同已关闭不能反审核" data="{'expr':'fclosestatus !=\'4\' ','message':'合同已关闭不能反审核！'}"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="manualrelease" op="manualrelease" opn="手动释放" permid="ydj_manualrelease" ubl="1">
            <li el="17" sid="2002" cn="手动释放预留数量" data="{'releaseWay':2}"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="reserveborrow" op="reserveborrow" opn="释放借货" permid="fw_reserveborrow" ubl="1">
        </ul>

        <ul el="10" ek="fentry" id="bizclose" op="bizclose" opn="关闭"
            data="{'parameter':{'linkStatusFieldKey':'fclosestatus_e','wholeStatusFieldKey':'fclosestatus'}}" ubl="1">
            <li el="11" ek="fentry" vid="510" cn="检查是否允许关闭" data="{
                'expr':'(fstatus=\'E\' or fchangestatus =\'1\') and fclosestatus_e!=\'3\'',
                'message':'当前单据必须是已审核，且当前行关闭状态不为自动关闭！'
                }"></li>
        </ul>
        <ul el="10" ek="fentry" id="unclose" op="unclose" opn="反关闭"
            data="{'parameter':{'linkStatusFieldKey':'fclosestatus_e','wholeStatusFieldKey':'fclosestatus'}}" ubl="1">
            <li el="11" ek="fentry" vid="510" cn="检查是否允许反关闭" data="{
                'expr':'(fstatus=\'E\' or fchangestatus =\'1\') and fclosestatus_e=\'4\'',
                'message':'当前单据必须是已审核，且行关闭状态必须为手动关闭！'
                }"></li>
        </ul>
        <ul el="10" ek="fentry" id="queryinventory" op="queryinventory" opn="库存查询" permid="fw_queryinventory"
            data="{
                'parameter':{
                    'fieldMaps':{
                        'fmaterialid':'fproductid',
                        'fcustomdesc':'fcustomdes_e',
                        'fusableqty':'fqty'
                    },
                    'filterString':'fmaterialid in(select fid from t_bd_material where fispulloff!=\'1\') and fmtono=\'\''
                }
            }"></ul>
        <ul el="10" ek="fentry" id="queryinventoryfirst" op="queryinventory" opn="查询一级总部停产及自建商品库存" permid=""
            data="{
                'parameter':{
                    'fieldMaps':{
                        'fmaterialid':'fproductid',
                        'fcustomdesc':'fcustomdes_e',
                        'fusableqty':'fqty'
                    }
                    
                }
            }"></ul>

        <ul el="10" ek="fbillhead" id="receipt" op="receipt" opn="收款" data="" permid="ydj_order_receipt"></ul>
        <ul el="10" ek="fbillhead" id="refund" op="refund" opn="退款" data="" permid="ydj_order_refund"></ul>

        <ul el="10" ek="fbillhead" id="cancel" op="cancel" opn="作废" data="" permid="fw_cancel">
            <li el="11" vid="510" ek="fbillhead" cn="合同已收款不能作废" data="{'expr':'freceivable<=0 ','message':'合同已收款不能作废！'}"></li>
            <li el="17" sid="2002" cn="自动释放预留数量" data="{
                'message':'订单作废，自动释放预留数量',
                'preCondition':'',
                'releaseWay':5,
                'releaseType':0}"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="uncancel" op="uncancel" opn="反作废" data="" permid="fw_uncancel">
            <li el="17" sid="2002" cn="取消释放预留数量" data="{
                'message':'订单反作废，自动取消释放预留数量',
                'preCondition':'',
                'releaseWay':5,
                'releaseType':1}"></li>
        </ul>

        <ul el="10" id="followerrecord" op="followerrecord" opn="跟进" data="" permid="followerrecord"></ul>
        <ul el="10" id="mergeregistfee" op="mergeregistfee" opn="合并付佣" data="" permid="mergeregistfee"></ul>
        <!-- <ul el="10" ek="fbillhead" id="batchmodify" op="batchmodify" opn="批改" data="" permid="batchmodify_prem"></ul> -->
        <ul el="10" id="addattachment" op="addattachment" opn="附件上传" data="" permid="ydj_order_addattachment"></ul>
        <ul el="10" id="deleteattachment" op="deleteattachment" opn="删除附件" data="" permid="ydj_order_deleteattachment"></ul>

        <ul el="10" id="submitthe" op="submitthe" opn="提交审批" data="" permid="fw_submitthe"></ul>
        <ul el="10" id="auditthe" op="auditthe" opn="确认图纸" data="" permid="fw_auditthe"></ul>

        <ul el="10" id="unstdtypeaudit" op="unstdtypeaudit" opn="非标审批" data="" permid="" ubl="1"></ul>

        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除">

            <li el="17" sid="2006" cn="删除对应的预留，同时预留转回到上游业务" data="{
                'preCondition':'',
                'qtyFieldKey':'fqty'
                }"></li>

        </ul>
        <!--<ul el="10" id="lockorder" op="lockorder" opn="锁定订单" data="" permid="lockorder"></ul>
    <ul el="10" id="unlockorder" op="unlockorder" opn="解锁订单" data="" permid="unlockorder"></ul>-->
        <ul el="10" id="orderclose" op="orderclose" opn="订单关闭" data="" permid="fw_close" ubl="1"></ul>
        <ul el="10" id="orderunclose" op="orderunclose" opn="订单反关闭" data="" permid="fw_unclose" ubl="1"></ul>
        <ul el="10" id="reservetransferquery" op="reservetransferquery" opn="预留借还查询" data="" permid="fw_reservetransferquery"></ul>
        <ul el="10" id="pushpurorder" op="pushpurorder" opn="采购" data="" permid="" ubl="1" motency="true"></ul>
        <ul el="10" id="pushpurorderswj" op="pushpurorderswj" opn="采购" data="" permid="" ubl="1" motency="true"></ul>
        <ul el="10" id="RenewalPur" op="RenewalPur" opn="一键采购" data="" permid="" ubl="1">
            <li el="11" vid="3008" cn="允许选配的商品明细的辅助属性不能为空" data="{'productFieldKey':'fproductid'}"></li>
        </ul>
        <ul el="10" id="RenewalReceipt" op="RenewalReceipt" opn="发起收款" data="" permid="fw_renewalreceipt" ubl="1">
            <li el="11" vid="3008" cn="允许选配的商品明细的辅助属性不能为空" data="{'productFieldKey':'fproductid'}"></li>
        </ul>


        <ul el="10" id="transfership" op="transfership" opn="已转单发货" data="" permid=""></ul>
        <ul el="10" id="untransfership" op="untransfership" opn="反执行转单发货" data="" permid=""></ul>

        <ul el="10" id="prjconfirm" op="prjconfirm" opn="方案确认" data="" permid="fw_prjconfirm"></ul>

        <ul el="10" ek="fbillhead" id="savesubmit" op="savesubmit" opn="保存并提交" data="" permid="fw_savesubmit"></ul>
        <ul el="10" ek="fbillhead" id="saveaudit" op="saveaudit" opn="保存并审核" data="" permid="fw_saveaudit"></ul>
        <ul el="10" id="quodetails" op="quodetails" opn="报价明细" data="" permid="fw_quodetails"></ul>
        <ul el="10" id="submitomsagain" op="submitomsagain" opn="提交OMS" data="" permid="fw_submitomsagain"></ul>
        <ul el="10" id="copyorder" op="copyorder" opn="融合成品下单" data="" permid="fw_copyorder"></ul>
        <ul el="10" ek="fbillhead" id="reserveinventory" op="reserveinventory" opn="预留" data="" permid="fw_reserveinventory"></ul>
        <ul el="10" ek="fbillhead" id="threeddesign" op="threeddesign" opn="3D设计" data="" permid="fw_threeddesign"></ul>

        <ul el="10" ek="fbillhead" id="alterhqprice" op="alterhqprice" opn="修改总部零售价为0的价格" data="" permid="fw_alterhqprice"></ul>
        <ul el="10" ek="fbillhead" id="partslistquery" op="partslistquery" opn="部件清单查询" data="" permid="fw_partslistquery"></ul>
        <ul el="10" ek="fbillhead" id="viewfactoryorderapprovallog" op="viewfactoryorderapprovallog" opn="查看工厂单审批日志" data="" permid="fw_viewfactoryorderapprovallog"></ul>
        <!--<ul el="10" id="submitaftersale" op="submitaftersale" opn="发起售后" data="" permid=""></ul>-->
        <ul el="10" id="aftservicedesign" op="aftservicedesign" opn="3D售后设计" data="" permid=""></ul>
        <ul el="10" id="submitaftersale" op="push" opn="发起售后" data="{'parameter':{'ruleId':'ydj_order2ydj_order'}}" permid="" ubl="1"></ul>
        <ul el="10" id="dropshipment" op="dropshipment" opn="一件代发" data="" permid="fw_dropshipment"></ul>
        <ul el="10" ek="fbillhead" id="querychangeapply" op="querychangeapply" opn="变更申请记录" permid="fw_querychangeapply"></ul>
        <ul el="10" id="logisticsprogress" op="logisticsprogress" opn="物流进度查询" data="" permid="fw_logisticsprogress"></ul>
        <ul el="10" id="submitheadquart" op="submitheadquart" opn="直营锁单" data="" permid="fw_submitheadquart"></ul>    
    </div>

    <!--表单所涉及的权限项定义-->
    <div id="permList">
        <!--<ul el="12" id="lockorder" cn="锁定订单"></ul>
    <ul el="12" id="unlockorder" cn="解锁订单"></ul>-->
        <ul el="12" id="fw_submitheadquart" cn="直营锁单"></ul>
        <ul el="12" id="ydj_order_cost" cn="成本核算"></ul>
        <ul el="12" id="ydj_manualrelease" cn="手动释放"></ul>
        <ul el="12" id="fw_reserveborrow" cn="释放借货"></ul>
        <ul el="12" id="fw_queryinventory" cn="库存查询"></ul>
        <ul el="12" id="" cn="查询一级总部停产及自建商品库存"></ul>
        <ul el="12" id="ydj_order_receipt" cn="收款"></ul>
        <ul el="12" id="ydj_order_refund" cn="退款"></ul>
        <ul el="12" id="followerrecord" cn="跟进"></ul>
        <ul el="12" id="mergeregistfee" cn="合并付佣"></ul>
        <!--<ul el="12" id="batchmodify_prem" cn="批改"></ul>-->
        <ul el="12" id="ydj_order_addattachment" cn="附件上传"></ul>
        <ul el="12" id="ydj_order_deleteattachment" cn="删除附件"></ul>
        <ul el="12" id="fw_submitthe" cn="提交审批"></ul>
        <ul el="12" id="fw_auditthe" cn="确认图纸"></ul>
        <ul el="12" id="fw_reservetransferquery" cn="预留借还查询"></ul>
        <ul el="12" id="fw_prjconfirm" cn="方案确认"></ul>
        <ul el="12" id="fw_printtrans" cn="打印转单信息"></ul>
        <ul el="12" id="fw_savesubmit" cn="保存并提交"></ul>
        <ul el="12" id="fw_saveaudit" cn="保存并审核"></ul>
        <ul el="12" id="fw_quodetails" cn="报价明细"></ul>
        <ul el="12" id="fw_submitomsagain" cn="提交OMS"></ul>
        <ul el="12" id="fw_copyorder" cn="融合成品下单"></ul>
        <ul el="12" id="fw_reserveinventory" cn="预留" order="20"></ul>

        <ul el="12" id="fw_change" cn="变更" order="16"></ul>
        <ul el="12" id="fw_submitchange" cn="提交变更" order="17"></ul>
        <ul el="12" id="fw_unchange" cn="取消变更" order="17"></ul>
        <ul el="12" id="fw_threeddesign" cn="3D设计"></ul>
        <ul el="12" id="fw_alterhqprice" cn="修改总部零售价为0的价格"></ul>
        <ul el="12" id="fw_partslistquery" cn="部件清单查询"></ul>
        <ul el="12" id="fw_viewfactoryorderapprovallog" cn="查看工厂单审批日志"></ul>
        <ul el="12" id="fw_submitaftersale" cn="发起售后"></ul>

        <ul el="12" id="fw_renewalreceipt" cn="发起收款"></ul>
        <ul el="12" id="fw_dropshipment" cn="一件代发"></ul>

        <ul el="12" id="fw_querychangeapply" cn="变更申请记录"></ul>
        <ul el="12" id="fw_logisticsprogress" cn="物流进度查询"></ul>
    </div>

</body>
</html>