<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ste_goal" basemodel="bill_basetmpl" el="1" cn="经营目标">
    <div id="fbillhead" el="51" pk="fid" tn="t_ste_goal" pn="fbillhead" cn="经营目标">
        <select id="fstatus" el="122" refId="bd_enum" dfld="fenumitem" cg="数据状态" ek="fbillhead" fn="fstatus" pn="fstatus" cn="数据状态" visible="1150" xlsin="0" copy="0" lix="260" align="center"></select>
        <input type="text" id="fbillno" el="108" ek="fbillhead" fn="fbillno" pn="fbillno" apipn="billNo" cn="单据编号" desc="单据编号" visible="1150" copy="0" lix="1" />
        <!--指标设置-->
        <select lix="1" group="指标设置" el="152" ek="fbillhead" visible="-1" id="fyear" fn="fyear" pn="fyear"
                cn="年度" vals="'fy_2017':'2017','fy_2018':'2018','fy_2019':'2019','fy_2020':'2020','fy_2021':'2021','fy_2022':'2022','fy_2023':'2023','fy_2024':'2024','fy_2025':'2025'"></select>
        <input lix="5" group="指标设置" cn="全年指标" el="105" ek="fbillhead" visible="-1" id="frelateman" fn="frelateman" pn="frelateman" />
        <select lix="10" group="指标设置" el="152" ek="fbillhead" visible="-1" id="fassobject" fn="fassobject" pn="fassobject"
                cn="考核对象" vals="'fao_001':'员工','fao_002':'部门'" copy="0" defval="'fao_001'"></select>
        <select lix="15" group="指标设置" el="152" ek="fbillhead" visible="-1" id="fasslatitude" fn="fasslatitude" pn="fasslatitude"
                cn="考核维度" vals="'fal_001':'开单额','fal_002':'意向额','fal_003':'商机数'" copy="0" defval="'fal_001'"></select>
        <input lix="20" group="指标设置" el="106" ek="fbillhead" visible="-1" id="fdeptid" fn="fdeptid" pn="fdeptid" cn="部门" cg="部门" refid="ydj_dept" />
        <input lix="25" group="指标设置" el="106" ek="fbillhead" visible="-1" id="fastaffid" fn="fastaffid" pn="fastaffid" cn="员工" cg="员工" refid="ydj_staff" />
        <input lix="30" group="指标设置" cn="季度一" el="105" ek="fbillhead" visible="-1" id="faquarter" fn="faquarter" pn="faquarter" />
        <input lix="35" group="指标设置" cn="一月" el="105" ek="fbillhead" visible="-1" id="faqjm" fn="faqjm" pn="faqjm" />
        <input lix="40" group="指标设置" cn="二月" el="105" ek="fbillhead" visible="-1" id="faqfm" fn="faqfm" pn="faqfm" />
        <input lix="45" group="指标设置" cn="三月" el="105" ek="fbillhead" visible="-1" id="faqmm" fn="faqmm" pn="faqmm" />

        <input lix="50" group="指标设置" cn="季度二" el="105" ek="fbillhead" visible="-1" id="ftquarter" fn="ftquarter" pn="ftquarter" />
        <input lix="55" group="指标设置" cn="四月" el="105" ek="fbillhead" visible="-1" id="ftqapr" fn="ftqapr" pn="ftqapr" />
        <input lix="60" group="指标设置" cn="五月" el="105" ek="fbillhead" visible="-1" id="ftqmay" fn="ftqmay" pn="ftqmay" />
        <input lix="65" group="指标设置" cn="六月" el="105" ek="fbillhead" visible="-1" id="ftqjune" fn="ftqjune" pn="ftqjune" />

        <input lix="70" group="指标设置" cn="季度三" el="105" ek="fbillhead" visible="-1" id="fthrqarter" fn="fthrqarter" pn="fthrqarter" />
        <input lix="75" group="指标设置" cn="七月" el="105" ek="fbillhead" visible="-1" id="fthjuly" fn="fthjuly" pn="fthjuly" />
        <input lix="80" group="指标设置" cn="八月" el="105" ek="fbillhead" visible="-1" id="fthaug" fn="fthaug" pn="fthaug" />
        <input lix="85" group="指标设置" cn="九月" el="105" ek="fbillhead" visible="-1" id="fthsep" fn="fthsep" pn="fthsep" />

        <input lix="90" group="指标设置" cn="季度四" el="105" ek="fbillhead" visible="-1" id="ffqarter" fn="ffqarter" pn="ffqarter" />
        <input lix="95" group="指标设置" cn="十月" el="105" ek="fbillhead" visible="-1" id="ffoct" fn="ffoct" pn="ffoct" />
        <input lix="100" group="指标设置" cn="十一月" el="105" ek="fbillhead" visible="-1" id="ffnov" fn="ffnov" pn="ffnov" />
        <input lix="105" group="指标设置" cn="十二月" el="105" ek="fbillhead" visible="-1" id="ffdec" fn="ffdec" pn="ffdec" />


        <!--还有一个描述，引用基类模板的字段 <input type="text" id="fdescription" el="100" ek="fbillhead" fn="fdescription" pn="fdescription" cn="备注" visible="-1" lix="500"/>-->
        <!-- <select group="指标设置" el="122" ek="fbillhead" visible="-1" id="fyear" fn="fyear" pn="fyear" cn="年度" cg="年度" refid="bd_enum" dfld="fenumitem" defval="'fy_2017'"></select> -->



    </div>

</body>
</html>