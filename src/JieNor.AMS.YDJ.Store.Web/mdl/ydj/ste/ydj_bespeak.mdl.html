<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ydj_bespeak" basemodel="bill_basetmpl" el="1" cn="预约单" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="ste_ydj_bespeak" pn="fbillhead" cn="预约单">
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fcustomerid" fn="fcustomerid" pn="fcustomerid" cn="客户" refid="ydj_customer" />
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fbespeak" fn="fbespeak" pn="fbespeak" cn="预约类型" cg="预约类型" refid="bd_enum" dfld="fenumitem" defval="'bespeak_type_01'"></select>
        <input group="基本信息" el="113" type="datetime" id="fdate" ek="fbillhead" fn="fdate" cn="预约时间" visible="-1" />
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fprovince" fn="fprovince" pn="fprovince" cn="省" cg="省" refid="bd_enum" dfld="fenumitem"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fcity" fn="fcity" pn="fcity" cn="市" cg="市" refid="bd_enum" dfld="fenumitem"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fregion" fn="fregion" pn="fregion" cn="区" cg="区" refid="bd_enum" dfld="fenumitem"></select>
        <input group="基本信息" el="100" type="text" id="faddress" ek="fbillhead" fn="faddress" pn="faddress" cn="详细地址" visible="-1" />
        <input group="基本信息" el="100" type="text" id="fphone" ek="fbillhead" fn="fphone" pn="fphone" cn="电话" visible="-1" />
        <input group="基本信息" el="100" type="text" id="fcontacts" ek="fbillhead" fn="fcontacts" pn="fcontacts" cn="联系人" visible="-1" />
        
        <input group="基本信息" len="1000" el="100" type="text" id="frequire" ek="fbillhead" fn="frequire" pn="frequire" cn="要求" visible="-1" />     
    </div>
</body>
</html>