{"Id": "ydj_order2ydj_purchaseorder2", "Number": "ydj_order2ydj_purchaseorder2", "Name": "销售合同生成采购订单", "SourceFormId": "ydj_order", "TargetFormId": "ydj_purchaseorder", "ActiveEntityKey": "f<PERSON>head", "FilterString": "fstatus='E'", "Message": "采购失败：\r\n1、销售合同必须是已审核状态！", "Visible": false, "FieldMappings": [{"Id": "fdate", "Name": "订单日期", "MapType": 0, "SrcFieldId": "forderdate", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fneedtransferorder", "Name": "需转单", "MapType": 0, "SrcFieldId": "fneedtransferorder", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fmemberdesc", "Name": "未注册会员原因", "MapType": 0, "SrcFieldId": "fmemberdesc", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fpickdate", "Name": "交货日期", "MapType": 1, "SrcFieldId": "@currentshortdate", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fcustomerid", "Name": "客户", "MapType": 0, "SrcFieldId": "fcustomerid", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fphone", "Name": "手机号", "MapType": 0, "SrcFieldId": "fphone", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fprovince", "Name": "省", "MapType": 0, "SrcFieldId": "fprovince", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fcity", "Name": "市", "MapType": 0, "SrcFieldId": "fcity", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fregion", "Name": "区", "MapType": 0, "SrcFieldId": "fregion", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "faddress", "Name": "详细地址", "MapType": 0, "SrcFieldId": "faddress", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fprovince_gb", "Name": "省", "MapType": 0, "SrcFieldId": "fprovince_gb", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fcity_gb", "Name": "市", "MapType": 0, "SrcFieldId": "fcity_gb", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fregion_gb", "Name": "区", "MapType": 0, "SrcFieldId": "fregion_gb", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "faddress_gb", "Name": "详细地址", "MapType": 0, "SrcFieldId": "faddress_gb", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fdeptid", "Name": "所属门店", "MapType": 0, "SrcFieldId": "fdeptid", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fstaffid", "Name": "导购员", "MapType": 0, "SrcFieldId": "fstaffid", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fstylistid", "Name": "设计师", "MapType": 0, "SrcFieldId": "fstylistid", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fsourcenumber", "Name": "源单编号", "MapType": 0, "SrcFieldId": "f<PERSON><PERSON>", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fsourcetype", "Name": "源单类型", "MapType": 1, "SrcFieldId": "'ydj_order'", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fnote", "Name": "备注", "MapType": 0, "SrcFieldId": "fdescription_e", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fhqderno", "Name": "总部合同号", "MapType": 0, "SrcFieldId": "fhqderno", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fclose<PERSON><PERSON>", "Name": "关闭状态", "MapType": 1, "SrcFieldId": "'0'", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fclosestatus_e", "Name": "行关闭状态", "MapType": 1, "SrcFieldId": "'0'", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fpackagedescription", "Name": "套件组合说明", "MapType": 0, "SrcFieldId": "fpackagedescription", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fpartqty", "Name": "配件数量", "MapType": 0, "SrcFieldId": "fpartqty", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "frenewalflag", "Name": "焕新订单标记", "MapType": 0, "SrcFieldId": "frenewalflag", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fmembershiptranid", "Name": "会员商城交易流水号", "MapType": 0, "SrcFieldId": "fmembershiptranid", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fmembershippaydate", "Name": "会员商城支付日期", "MapType": 0, "SrcFieldId": "fmembershippaydate", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fdeliverydate", "Name": "合同交货日期", "MapType": 0, "SrcFieldId": "fdeliverydate", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fcustomcategory", "Name": "焕新定制柜类别", "MapType": 0, "SrcFieldId": "fcustomcategory", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fsecondorderno", "Name": "二级合同编号", "MapType": 0, "SrcFieldId": "fsecondorderno", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fretaildisprice", "Name": "零售折扣单价", "MapType": 1, "SrcFieldId": "fprice - fdealprice", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fretaildistamount", "Name": "零售折扣额", "MapType": 1, "SrcFieldId": "(fprice - fdealprice) * fbizqty", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fsubsidyamount", "Name": "补贴金额", "MapType": 0, "SrcFieldId": "fsubsidyamount", "MapActionWhenGrouping": 0, "Order": 0, "IgnoreChangeValidation": true}, {"Id": "fretailprice", "Name": "零售价", "MapType": 0, "SrcFieldId": "fprice", "MapActionWhenGrouping": 0, "Order": 23, "IgnoreChangeValidation": true}, {"Id": "fretaildealprice", "Name": "零售成交单价", "MapType": 0, "SrcFieldId": "fdealprice", "MapActionWhenGrouping": 0, "Order": 23, "IgnoreChangeValidation": true}], "BillGroups": [{"Id": "f<PERSON><PERSON>", "Order": 1}], "FieldGroups": []}