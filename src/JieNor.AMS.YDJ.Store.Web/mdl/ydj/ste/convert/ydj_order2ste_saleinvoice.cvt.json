{"Id": "ydj_order2ste_saleinvoice", "Number": "ydj_order2ste_saleinvoice", "Name": "销售合同下推销售发票", "SourceFormId": "ydj_order", "TargetFormId": "ste_saleinvoice", "ActiveEntityKey": "f<PERSON>head", "FilterString": "", "Message": "开票失败", "FieldMappings": [{"Id": "fsourcetype", "Name": "来源单类型", "MapType": 1, "SrcFieldId": "'ydj_order'", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fsourcenumber", "Name": "来源单编号", "MapType": 0, "SrcFieldId": "f<PERSON><PERSON>", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fphone", "Name": "联系电话", "MapType": 0, "SrcFieldId": "fphone", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "faddress", "Name": "地址", "MapType": 0, "SrcFieldId": "faddress", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "ftax<PERSON>unt", "Name": "含税金额", "MapType": 0, "SrcFieldId": "fsumamount", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fcustomerid", "Name": "客户", "MapType": 0, "SrcFieldId": "fcustomerid", "MapActionWhenGrouping": 0, "Order": 0}], "BillGroups": [{"Id": "fcustomerid", "Name": "客户", "Order": 1}], "FieldGroups": [{"Id": "fentry_fentryid", "Order": 1}]}