{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
  "lockRules": [
    ////已审核状态下，则转售后按钮可用
    //{
    //  "id": "lock_pushafterbtn",
    //  "expression": "menu:tbPushAfterManage|fstatus!='E'"
    //},
    //{
    //  "id": "unlock_pushafterbtn",
    //  "expression": "menu:$tbPushAfterManage|fstatus=='E'"
    //},
    ////如果已转总部则允许修改
    //{
    //  "id": "lock_freturnlognum",
    //  "expression": "field:freturnlognum;|fistransfer!=true"
    //},
    //{
    //  "id": "unlock_freturnlognum",
    //  "expression": "field:$freturnlognum;|fistransfer==true"
    //},
    //{
    //  "id": "lock_tbTransfer",
    //  "expression": "menu:$tbTransfer|fistransfer!=true and ffeedstatus=='aft_service_02'"
    //},
    //{
    //  "id": "unlock_tbTransfer",
    //  "expression": "menu:tbTransfer,tbPull|fistransfer==true"
    //},
    //受理
    //{
    //  "id": "lock_accept",
    //  "expression": "menu:tbAccept|ffeedstatus!='aft_service_01'"
    //},
    //{
    //  "id": "unlock_accept",
    //  "expression": "menu:$tbAccept|ffeedstatus=='aft_service_01'"
    //},
    //完成
    //{
    //  "id": "lock_finish",
    //  "expression": "menu:tbFinish|ffeedstatus!='aft_service_02' and ffeedstatus!='aft_service_04'"
    //},
    //{
    //  "id": "unlock_finish",
    //  "expression": "menu:$tbFinish|ffeedstatus=='aft_service_02' or ffeedstatus=='aft_service_04'"
    //},
    //关闭
    //{
    //  "id": "lock_fclose",
    //  "expression": "menu:tbFclose|ffeedstatus!='aft_service_05'"
    //},
    //{
    //  "id": "unlock_fclose",
    //  "expression": "menu:$tbFclose|ffeedstatus=='aft_service_05'"
    //},
    //总部待审核/已关闭后，全部锁定
    //{
    //  "id": "lock_allField",
    //  "expression": "field:*$;|ffeedstatus=='aft_service_03' or ffeedstatus=='aft_service_06'"
    //},
    //{
    //  "id": "lock_tbSaveAndAudit",
    //  "expression": "menu:tbSaveSubmit,tbSaveAudit|fstatus=='E'"
    //},
    //保存并提交锁定锁定&解锁
    //{
    //  "id": "lock_tbSaveSubmit",
    //  "expression": "menu:tbSaveSubmit|fstatus=='D' or fstatus=='E'"
    //},
    //{
    //  "id": "unlock_tbSaveSubmit",
    //  "expression": "menu:$tbSaveSubmit|fstatus!='D' and fstatus!='E'"
    //}

    //"当【售后状态】=“总部待审核“、“总部已审核”时，需锁定表单所有字段禁止编辑。" 返厂申请字段默认可编辑（遵循原逻辑，总部处理结论】=“返厂”时，返厂申请才可见）
    {
      "id": "lock_allField",
      "expression": "field:*$fauthcity,fisreturn,freturndate,fengineerphone,ffeedtype,fengineeraddress,fsapnumber,farrivaldate,fservicedate,fcususetime,fcusreturnadd,freturnlognum,freturnreson;|ffeedstatus=='aft_service_03' or ffeedstatus=='aft_service_04'"
    },
    ////如果【总部处理结论】=“返厂”时，则返厂申请字段可编辑。
    //{
    //  "id": "unlock_returnField",
    //  "expression": "field:$fdeliver,fauthcity,fisreturn,freturndate,fengineerphone,ffeedtype,fengineeraddress,fsapnumber,farrivaldate,fservicedate,fcususetime,fcusreturnadd,freturnlognum,freturnreson;|fhqhandleconclusion=='fqres_type_08'"
    //}
    //当售后状态=总部待审核，则需控制,不允许操作作废、反作废按钮
    {
      "id": "lock_someMenu",
      "expression": "menu:tbCancel,tbUncancel|ffeedstatus=='aft_service_03'"
    },
    {
      "id": "unlock_someMenu",
      "expression": "menu:$tbCancel,tbUncancel|ffeedstatus!='aft_service_03'"
    },
    //当 售后状态=总部待审核或总部已审核，则需控制 不允许操作  附件 按钮。
    {
      "id": "lock_ListAttachment",
      "expression": "menu:tbListAttachment|ffeedstatus=='aft_service_03' or ffeedstatus=='aft_service_04'"
    },
    {
      "id": "unlock_ListAttachment",
      "expression": "menu:$tbListAttachment|ffeedstatus!='aft_service_03' and ffeedstatus!='aft_service_04'"
    },
    {
      "id": "fstatus_",
      "expression": "menu:*$tbNew,tbSave,tbPull,tbSaveSubmit,tbSaveAudit|fstatus==''"
    },
    {
      "id": "unlock_tbPull",
      "expression": "menu:$tbPull|ffeedstatus=='aft_service_02' or ffeedstatus=='aft_service_01'"
    },
    {
      "id": "lock_tbPull",
      "expression": "menu:tbPull|ffeedstatus=='aft_service_03' or ffeedstatus=='aft_service_04'"
    },
    {
      "id": "unlock_tbPurReturn",
      "expression": "menu:$tbPurReturn|fsourcetype=='ydj_purchaseorder' and fhqhandleconclusion=='fqres_type_03'"
    },
    {
      "id": "lock_tbPurReturn",
      "expression": "menu:tbPurReturn|fsourcetype!='ydj_purchaseorder' or fhqhandleconclusion!='fqres_type_03'"
    },
    {
      "id": "unlock_tbSalReturn",
      "expression": "menu:$tbSalReturn|fsourcetype=='ydj_service' and fhqhandleconclusion=='fqres_type_03'"
    },
    {
      "id": "lock_tbSalReturn",
      "expression": "menu:tbSalReturn|fsourcetype!='ydj_service' or fhqhandleconclusion!='fqres_type_03'"
    },
    {
      "id": "unlock_tbReturnDepto",
      "expression": "menu:$tbReturnDepto|fsourcetype=='ydj_purchaseorder' and fhqhandleconclusion=='fqres_type_01'"
    },
    {
      "id": "lock_tbReturnDepto",
      "expression": "menu:tbReturnDepto|fsourcetype!='ydj_purchaseorder' or fhqhandleconclusion!='fqres_type_01'"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [
    //已转总部
    //{
    //  "id": "hide_fistransfer",
    //  "expression": "other:.feedback-fistransfer,.feeback-hq|fistransfer!=true"
    //},
    //{
    //  "id": "show_fistransfer",
    //  "expression": "other:$.feedback-fistransfer,.feeback-hq|fistransfer==true"
    //},
    //选择供应商隐藏规则
    {
      "id": "show_dutysupplier",
      "expression": "other:$.y-dutysupplier| finstitutiontype == 'dutyunit_type_01'"
    },
    {
      "id": "hide_dutysupplier",
      "expression": "other:.y-dutysupplier| finstitutiontype != 'dutyunit_type_01'"
    },
    //选择客户隐藏规则
    {
      "id": "show_dutycustomer",
      "expression": "other:$.y-dutycustomer| finstitutiontype == 'dutyunit_type_02'"
    },
    {
      "id": "hide_dutycustomer",
      "expression": "other:.y-dutycustomer| finstitutiontype != 'dutyunit_type_02'"
    },
    //选择员工隐藏规则
    {
      "id": "show_dutystaff",
      "expression": "other:$.y-dutystaff| finstitutiontype == 'dutyunit_type_03'"
    },
    {
      "id": "hide_dutystaff",
      "expression": "other:.y-dutystaff| finstitutiontype != 'dutyunit_type_03'"
    },
    //选择部门隐藏规则
    {
      "id": "show_dutydept",
      "expression": "other:$.y-dutydept| finstitutiontype == 'dutyunit_type_04'"
    },
    {
      "id": "hide_dutydept",
      "expression": "other:.y-dutydept| finstitutiontype != 'dutyunit_type_04'"
    },
    //返厂申请
    {
      "id": "show_dutydept",
      "expression": "other:$.feedback-body| fhqhandleconclusion == 'fqres_type_08'"
    },
    {
      "id": "hide_dutydept",
      "expression": "other:.feedback-body| fhqhandleconclusion != 'fqres_type_08'"
    }
    //受理按钮
    //{
    //  "id": "show_dutydept",
    //  "expression": "other:$[opcode=accept]| ffeedstatus=='aft_service_01'"
    //},
    //{
    //  "id": "hide_dutydept",
    //  "expression": "other:[opcode=accept]| ffeedstatus!='aft_service_01'"
    //}
  ],

  //定义表单计算规则
  "calcRules": [
    //选择受理人信息，自动带出受理部门
    { "expression": "fdeptid=fstaffid__fdeptid|fstaffid=='' or 1==1" },
    //客户基础资料值变化时，携带客户属性字段到页面指定的字段上面
    { "expression": "flinkstaffid=fcustomerid__fcontacts" },
    { "expression": "flinkmobile=fcustomerid__fphone" },
    { "expression": "flinkaddress=fcustomerid__faddress" },
    { "expression": "fprovince=fcustomerid__fprovince" },
    { "expression": "fcity=fcustomerid__fcity" },
    { "expression": "fregion=fcustomerid__fregion" },
    { "expression": "fphone=fstaffid__fphone" },
    { "expression": "fengineerphone=fengineerid__fphone" },
    { "expression": "fengineeraddress=fengineerid__faddress" },
    //{ "expression": "fcategoryid=fengineerid__fcategoryid" },
    { "expression": "fauthcity=fagentid__fcityid" },
    //{ "expression": "fspecifica=fquestionproduct__fspecifica" },
    //{ "expression": "fseltypename=fquestionproduct__fname" },
    //{ "expression": "funitid=fquestionproduct__funitid|1==1" }

    //产品返厂原因 取【总部处理方式】自动填充显示
    { "expression": "freturnreson=fhqscheme|1==1" }
  ]
}