{
  "Id": "ydj_customer2ydj_service",
  "Number": "ydj_customer2ydj_service",
  "Name": "客户下推服务单",
  "SourceFormId": "ydj_customer",
  "TargetFormId": "ydj_service",
  "ActiveEntityKey": "fbillhead",
  "Visible": false,
  //"FilterString": "fstatus='E'",
  //"Message": "新增服务失败：<br>客户必须是已审核状态！",
  "FieldMappings": [
    //表头字段
    {
      "Id": "fbilltypeid",
      "Name": "单据类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_service_billtype_03'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //{
    //  "Id": "fagentid",
    //  "Name": "招商经销商",
    //  "MapType": 1,
    //  "SrcFieldId": "@currentOrgId",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    {
      "Id": "fservicetype",
      "Name": "服务类型",
      "MapType": 1,
      "SrcFieldId": "'fres_type_02'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fservicedate",
      "Name": "预约时间",
      "MapType": 1,
      "SrcFieldId": "@currentDate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fserstatus",
      "Name": "服务状态",
      "MapType": 1,
      "SrcFieldId": "'sersta01'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomerid",
      "Name": "客户",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcusnature",
      "Name": "客户性质",
      "MapType": 0,
      "SrcFieldId": "fcusnature",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcollectrel",
      "Name": "收货人",
      "MapType": 0,
      "SrcFieldId": "fcontacts",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "fcollectpho",
      "Name": "联系电话",
      "MapType": 0,
      "SrcFieldId": "fphone",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "fcollectpho",
      "Name": "联系电话",
      "MapType": 0,
      "SrcFieldId": "fphone",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "fcollectadd",
      "Name": "详细地址",
      "MapType": 0,
      "SrcFieldId": "faddress",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "fprovince",
      "Name": "省",
      "MapType": 0,
      "SrcFieldId": "fprovince",
      "MapActionWhenGrouping": 0,
      "Order": 2
    },
    {
      "Id": "fcity",
      "Name": "市",
      "MapType": 0,
      "SrcFieldId": "fcity",
      "MapActionWhenGrouping": 0,
      "Order": 3
    },
    {
      "Id": "fregion",
      "Name": "区",
      "MapType": 0,
      "SrcFieldId": "fregion",
      "MapActionWhenGrouping": 0,
      "Order": 4
    },
    //{
    //  "Id": "forderno",
    //  "Name": "合同编号",
    //  "MapType": 0,
    //  "SrcFieldId": "fsourceinterid",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_customer'",
      "MapActionWhenGrouping": 0,
      "Order": 6
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编码",
      "MapType": 0,
      "SrcFieldId": "fnumber",
      "MapActionWhenGrouping": 0,
      "Order": 7
    }
    //{
    //  "Id": "fattention",
    //  "Name": "注意事项",
    //  "MapType": 0,
    //  "SrcFieldId": "fentrynote",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 7
    //},

    //商品信息字段
    //{
    //  "Id": "fmaterialid",
    //  "Name": "商品",
    //  "MapType": 0,
    //  "SrcFieldId": "fmaterialid",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 15
    //},
    //{
    //  "Id": "fattrinfo",
    //  "Name": "辅助属性",
    //  "MapType": 0,
    //  "SrcFieldId": "fattrinfo",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 16
    //},
    //{
    //  "Id": "fcustomdesc",
    //  "Name": "定制说明",
    //  "MapType": 0,
    //  "SrcFieldId": "fcustomdesc",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 17
    //},
    //{
    //  "Id": "funitid_e",
    //  "Name": "基本单位",
    //  "MapType": 0,
    //  "SrcFieldId": "funitid",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 18
    //},
    //{
    //  "Id": "fqty_e",
    //  "Name": "基本单位实发数量",
    //  "MapType": 1,
    //  "SrcFieldId": "fqty-freturnqty",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 19
    //},
    //{
    //  "Id": "fsourceformid",
    //  "Name": "来源单类型",
    //  "MapType": 1,
    //  "SrcFieldId": "'stk_sostockout'",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fsourcebillno",
    //  "Name": "来源单编号",
    //  "MapType": 0,
    //  "SrcFieldId": "fbillno",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fsourceinterid",
    //  "Name": "来源单内码",
    //  "MapType": 0,
    //  "SrcFieldId": "fbillhead.id",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fsourceentryid",
    //  "Name": "来源单分录内码",
    //  "MapType": 0,
    //  "SrcFieldId": "fentity.id",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //}
  ],
  "BillGroups": [
    {
      "Id": "fnumber",
      "Order": 1
    }
  ],
  "FieldGroups": [
  ]
}