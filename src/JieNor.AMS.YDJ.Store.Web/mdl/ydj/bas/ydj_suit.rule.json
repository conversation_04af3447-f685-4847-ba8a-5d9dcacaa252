{
  //规则引擎基类
  "base": "/mdl/bd.rule.json",

  //定义表单锁定规则
  "lockRules": [

  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [

    //选择商品时，携带出单位
    { "expression": "funitid_e=fproductid__funitid|fproductid=='' or 1==1" },
    { "expression": "famount=fqty*fprice" },
    { "expression": "fsumamount=sum(famount)" },
    //选择商品时，携带出默认辅助属性
    { "expression": "fattrinfo=getAuxPropValue(fproductid)" }
  ]
}