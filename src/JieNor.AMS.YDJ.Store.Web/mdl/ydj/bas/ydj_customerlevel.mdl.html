<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="ydj_customerlevel" basemodel="bd_basetmpl" el="3" cn="会员级别" fqfks="fcondition" rac="false" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_customerlevel" pn="fbillhead" cn="会员级别">

        <!-- 重写字段 -->
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fname" fn="fname" pn="fname" cn="等级名称" lix="500" />

        <!--基本信息-->
        <input group="基本信息" el="101" ek="fbillhead" visible="-1" id="fcondition" fn="fcondition" pn="fcondition" cn="晋级条件（累积金额）" lix="4" />

    </div>
    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">

    </div>
    <div id="permList">

    </div>
</body>
</html>