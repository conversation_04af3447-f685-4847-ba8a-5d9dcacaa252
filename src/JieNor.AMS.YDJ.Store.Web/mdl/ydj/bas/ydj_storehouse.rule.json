{
  //规则引擎基类
  "base": "/mdl/bd.rule.json",

  //定义表单锁定规则
  "lockRules": [
    //解锁和锁定供应商与客户字段
    {
      "id": "lock_supplier",
      "expression": "field:fsupplierid|fstorehousetype!='storehouse_type_02'"
    },
    {
      "id": "unlock_supplier",
      "expression": "field:$fsupplierid|fstorehousetype=='storehouse_type_02'"
    },
    {
      "id": "lock_customer",
      "expression": "field:fcustomerid|fstorehousetype!='storehouse_type_03'"
    },
    {
      "id": "unlock_customer",
      "expression": "field:$fcustomerid|fstorehousetype=='storehouse_type_03'"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

    //客户和供应商字段的隐藏与显示
    {
      "id": "hide_supplier",
      "expression": "other:.supplier-mes|fstorehousetype!='storehouse_type_02'"
    },
    {
      "id": "show_supplier",
      "expression": "other:$.supplier-mes|fstorehousetype=='storehouse_type_02'"
    },
    {
      "id": "hide_customer",
      "expression": "other:.customer-mes|fstorehousetype!='storehouse_type_03'"
    },
    {
      "id": "show_customer",
      "expression": "other:$.customer-mes|fstorehousetype=='storehouse_type_03'"
    }
  ],

  //定义表单计算规则
  "calcRules": [
     //选择负责人，自动带出电话
    { "expression": "fphone=fcharge__fphone" }
  ]
}