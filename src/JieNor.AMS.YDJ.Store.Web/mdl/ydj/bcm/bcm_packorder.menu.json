{
  "base": "/mdl/bd.menu.json",
  "common": [
    {
      "id": "tbNew",
      "caption": "新增",
      "visible": "false",
      "disabled": "false"
    },
    {
      "id": "tbCopy",
      "caption": "复制",
      "visible": "false",
      "disabled": "false"
    }
  ],
  "listmenu": [
    {
      "id": "tbExcelImportTmpl",
      "caption": "生成导入模板",
      "visible": "false",
      "disabled": "false"
    },
    {
      "id": "tbExcleTemp",
      "caption": "配置导入模板",
      "visible": "false",
      "disabled": "false"
    },
    {
      "id": "tbImportExcelData",
      "caption": "导入",
      "visible": "false",
      "disabled": "false"
    }
  ],
  "billmenu": [
    {
      "id": "tbCreatePack",
      "caption": "打包",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 109,
      "parent": "",
      "opcode": "createpackage",
      "param": "",
      "entityKey": "fentity"
    },
    {
      "id": "tbDeletePack",
      "caption": "删除",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 115,
      "parent": "",
      "opcode": "deletepackage",
      "param": "",
      "entityKey": "fpackentity"
    }
    //,
    //{
    //  "id": "tbCommitPack",
    //  "caption": "核验收货",
    //  "visible": "true",
    //  "disabled": "false",
    //  "style": "menu",
    //  "order": 120,
    //  "parent": "",
    //  "opcode": "commitpack",
    //  "param": "",
    //  "entityKey": "fpackentity"
    //}
  ]
}
