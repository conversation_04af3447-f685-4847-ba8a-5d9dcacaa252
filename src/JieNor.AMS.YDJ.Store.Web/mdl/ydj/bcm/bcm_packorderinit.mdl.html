<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）

    包装清单
-->
<html lang="en">
<head>
</head>
<body id="bcm_packorderinit" basemodel="bill_basetmpl" el="1" cn="初始包装清单" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_bcm_packorder" pn="fbillhead" cn="初始包装清单">
        <!--重定义基类字段名称-->
        <input group="基本信息" el="112" ek="FBillHead" id="FDate" fn="FDate" pn="FDate" visible="-1" cn="业务日期"
               lock="0" copy="1" lix="0" notrace="true" ts="" defval="@currentDate" />
        <input group="基本信息" el="152" ek="FBillHead" id="fpackbiztype" fn="fpackbiztype" pn="fpackbiztype" visible="-1" cn="业务类型"
               lock="-1" copy="1" lix="10" notrace="true" ts="" vals="'1':'收货包装','2':'在库包装'" defval="'2'" />
        <input group="物流信息" el="100" ek="fbillhead" id="fdeliverybillno" fn="fdeliverybillno" pn="fdeliverybillno" cn="物流单号" visible="0" copy="0" lix="1010" />
    </div>

    <!--待包装明细-->
    <table id="fentity" el="52" pk="fentryid" tn="t_bcm_packsourceentry" pn="fentity" cn="包装来源明细" kfks="fmaterialid" must="1" allowqkfilter="true">
        <tr>
            <th el="107" ek="fentity" id="fmaterialid_fnumber" fn="fmaterialid_fnumber" pn="fmaterialid_fnumber" visible="1150" cn="商品编码"
                lock="-1" copy="1" lix="10" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fnumber"></th>
            <th el="106" ek="fentity" id="fmaterialid" fn="fmaterialid" pn="fmaterialid" visible="1150" cn="商品"
                copy="0" lix="20" notrace="true" ts="" refid="ydj_product" filter="" reflvt="0" sformid="" lock="-1"></th>
            <th el="132" ek="fentity" id="fattrinfo" fn="fattrinfo" cn="辅助属性" lix="30" ctlfk="fmaterialid" pricefk="" width="140" visible="1150" lock="-1"></th>
            <th el="100" ek="fentity" len="2000" id="fcustomdesc" fn="fcustomdesc" cn="定制说明" lix="40" width="140" visible="1150" lock="-1"></th>
            <th el="152" ek="fentity" id="fpacktype" fn="fpacktype" pn="fpacktype" visible="1150" cn="打包类型"
                copy="1" lix="50" notrace="true" ts="" vals="'1':'标准','2':'1件多包','3':'1包多件'" width="120"></th>
            <!--<th el="107" ek="fentity" id="fpacktype" fn="fpacktype" pn="fpacktype" visible="1150" cn="打包类型"
    lock="-1" copy="1" lix="50" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fpackagtype"></th>-->

            <th el="101" ek="fentity" id="fpackcount" fn="fpackcount" pn="fpackcount" visible="1150" cn="包数/件数"
                copy="1" lix="60" notrace="true" ts="" format="0,000"></th>
            <th el="109" ek="fentity" id="fbizunitid" fn="fbizunitid" cn="库存单位" lix="70" ctlfk="fmaterialid" refid="ydj_unit" sformid="" visible="1150" width="80" lock="-1"></th>
            <th el="103" ek="fentity" id="fbizremainqty" fn="fbizremainqty" pn="fbizremainqty" visible="1150" cn="可包装数量"
                copy="0" lix="80" notrace="true" ts="" ctlfk="fbizunitid" basqtyfk="fremainqty" roundType="0" format="0,000.00" lock="-1"></th>
            <th el="101" ek="fentity" id="fbizpackqty" fn="fbizpackqty" pn="fbizpackqty" visible="1150" cn="本次包装数量"
                lock="0" copy="1" lix="90" notrace="true" ts="" ctlfk="fbizunitid" roundType="0" format="0,000.00" width="100"></th>
            <th el="103" ek="fentity" id="fbizpackedqty" fn="fbizpackedqty" pn="fbizpackedqty" visible="1150" cn="已包装数量"
                copy="1" lix="100" notrace="true" ts="" ctlfk="fbizunitid" basqtyfk="fpackedqty" roundType="0" format="0,000.00" lock="-1"></th>
            <th el="103" ek="fentity" id="fbizqty" fn="fbizqty" cn="待包装数量" lix="110" visible="1150" ctlfk="fbizunitid" basqtyfk="fqty" lock="-1" width="100" format="0,000.00"></th>
            <th el="100" ek="fentity" id="flotno" fn="flotno" pn="flotno" cn="批号" lix="120" width="100" visible="1150" lock="-1"></th>
            <th el="100" ek="fentity" id="fmtono" fn="fmtono" pn="fmtono" cn="物流跟踪号" lix="130" width="100" visible="1150" lock="-1"></th>
            <th el="149" ek="fentity" id="fownertype" fn="fownertype" pn="fownertype" lix="140" dataviewname="v_bd_ownerdata" cn="货主类型" width="100" visible="1150" lock="-1">
                <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
                <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
            </th>
            <th el="150" ek="fentity" id="fownerid" fn="fownerid" pn="fownerid" lix="150" ctlfk="fownertype" cn="货主" width="100" visible="1150" lock="-1"></th>
            <th el="107" ek="fentity" id="fmtrlmodel" fn="fmtrlmodel" pn="fmtrlmodel" visible="1150" cn="规格型号"
                lock="-1" copy="1" lix="160" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fspecifica" refvt="0"></th>
            <th el="107" ek="fentity" id="fmaterialid_fbrandid" fn="fmaterialid_fbrandid" pn="fmaterialid_fbrandid" visible="1150" cn="品牌"
                lock="-1" copy="1" lix="170" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fbrandid"></th>
            <th el="107" ek="fentity" id="fmaterialid_fseriesid" fn="fmaterialid_fseriesid" pn="fmaterialid_fseriesid" visible="1150" cn="系列"
                lock="-1" copy="1" lix="180" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fseriesid"></th>
            <th el="107" ek="fentity" id="fmaterialid_fsubseriesid" fn="fmaterialid_fsubseriesid" pn="fmaterialid_fsubseriesid" cn="子系列" ctlfk="fmaterialid" dispfk="fsubseriesid" visible="1086" sformid="" width="100" lock="-1" lix="181"></th>
            <th lix="190" el="107" ek="fentity" id="fcustom" fn="fcustom" pn="fcustom" visible="0" cn="允许定制" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fcustom" refvt="116"></th>
            <th el="152" ek="fentity" id="fsrcpacktype" fn="fsrcpacktype" pn="fsrcpacktype" visible="0" cn="原打包类型"
                lock="-1" copy="1" lix="200" notrace="true" ts="" vals="'1':'标准','2':'1件多包','3':'1包多件'"></th>
            <th el="101" ek="fentity" id="fsrcpackcount" fn="fsrcpackcount" pn="fsrcpackcount" visible="0" cn="原包数/件数"
                lock="-1" copy="1" lix="210" notrace="true" ts="" format="0,000" width="100"></th>
            <th el="100" ek="fentity" id="ftjno" fn="ftjno" pn="ftjno" cn="套件组合号" lix="220" width="100" visible="-1" lock="-1"></th>
            <th el="100" ek="fentity" id="fpjno" fn="fpjno" pn="fpjno" cn="配件组合号" lix="230" width="100" visible="-1" lock="-1"></th>
            <th el="100" ek="fentity" id="fsfno" fn="fsfno" pn="fsfno" cn="沙发组合号" lix="240" width="100" visible="-1" lock="-1"></th>
            <th el="140" ek="fentity" id="fsourceformid" fn="fsourceformid" ts="" cn="来源单据" visible="1148" copy="0" lix="250" lock="-1"></th>
            <th el="141" ek="fentity" id="fsourcebillno" fn="fsourcebillno" ts="" cn="来源单据编号" visible="1148" copy="0" lix="260" lock="-1"></th>
            <th el="100" ek="fentity" id="fsourceinterid" fn="fsourceinterid" ts="" cn="来源单内码" visible="1148" copy="0" lix="270" lock="-1"></th>
            <th el="100" ek="fentity" id="fsourceentryid" fn="fsourceentryid" ts="" cn="来源单行内码" visible="1148" copy="0" lix="280" lock="-1" width="100"></th>
            <th el="100" ek="fentity" id="fsourceseq" fn="fsourceseq" ts="" cn="来源单行号" visible="1148" copy="0" lix="350" lock="-1"></th>

            <!--<th el="107" ek="fentity" id="fmaterialid_fsuiteflag" fn="fmaterialid_fsuiteflag" pn="fmaterialid_fsuiteflag" visible="0" cn="选配套件"
    lock="-1" copy="1" lix="10" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fsuiteflag"></th>-->
            <th el="107" ek="fentity" id="fmaterialid_fbag" fn="fmaterialid_fbag" pn="fmaterialid_fbag" visible="0" cn="包装规则 (包)"
                lock="-1" copy="1" lix="10" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fbag"></th>
            <th el="107" ek="fentity" id="fmaterialid_fpiece" fn="fmaterialid_fpiece" pn="fmaterialid_fpiece" visible="0" cn="包装规则 (件)"
                lock="-1" copy="1" lix="10" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fpiece"></th>
            <th el="107" ek="fentity" id="fmaterialid_fpackagtype" fn="fmaterialid_fpackagtype" pn="fmaterialid_fpackagtype" visible="0" cn="打包类型"
                lock="-1" copy="1" lix="10" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fpackagtype"></th>
            <th el="109" ek="fentity" id="funitid" fn="funitid" cn="基本单位" lix="150" ctlfk="fmaterialid" refid="ydj_unit" sformid="" visible="1150" width="80" lock="-1"></th>
            <th el="103" ek="fentity" id="fpackedqty" fn="fpackedqty" pn="fpackedqty" visible="1150" cn="基本单位已包装数量"
                lock="-1" copy="1" lix="152" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00" width="120"></th>
            <!--以前的字段-->
            <th el="103" ek="fentity" id="fremainqty" fn="fremainqty" pn="fremainqty" visible="0" cn="基本单位可包装数量"
                lock="-1" copy="1" lix="151" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00" width="120"></th>
            <th el="103" ek="fentity" id="fqty" fn="fqty" cn="基本单位待包装数量" lix="153" visible="0" ctlfk="funitid" format="0,000.00" lock="-1" width="120"></th>
            <th el="103" ek="fentity" id="fpackqty" fn="fpackqty" cn="基本单位本次包装数量" lix="153" visible="0" ctlfk="funitid" format="0,000.00" lock="-1" width="140"></th>
            <th el="106" ek="fentity" id="fstorehouseid" fn="fstorehouseid" cn="仓库" lix="259" refid="ydj_storehouse" sformid="" visible="-1" width="100" lock="-1"></th>
            <th el="153" ek="fentity" id="fstorelocationid" fn="fstorelocationid" cn="仓位" lix="261" ctlfk="fstorehouseid" luek="fentity" lunmfk="flocname" lunbfk="flocnumber" sformid="" visible="-1" width="100" lock="-1"></th>
            <th el="106" ek="fentity" id="fstockstatus" fn="fstockstatus" cn="库存状态" lix="263" refid="ydj_stockstatus" defVal="'311858936800219137'" sformid="" visible="0" width="100" lock="-1"></th>

            <th el="100" lix="20" ek="fentity" id="fentrynote" fn="fentrynote" pn="fentrynote" visible="-1" cn="备注" len="4000" />

            <th el="100" ek="fentity" id="freceptionno" fn="freceptionno" pn="freceptionno" cn="收货单号" visible="1148" lix="355" />
            <th el="140" ek="fentity" id="finisrcformid" fn="finisrcformid" pn="finisrcformid" ts="" cn="初始来源单据" visible="1148" copy="0" lix="360" lock="-1"></th>
            <th el="141" ek="fentity" id="finisrcbillno" fn="finisrcbillno" pn="finisrcbillno" ts="" cn="初始来源单据编号" visible="1148" copy="0" lix="365" lock="-1"></th>
            <th el="100" ek="fentity" id="finisrcinterid" fn="finisrcinterid" pn="finisrcinterid" ts="" cn="初始来源单内码" visible="1148" copy="0" lix="370" lock="-1"></th>
            <th el="100" ek="fentity" id="finisrcentryid" fn="finisrcentryid" pn="finisrcentryid" ts="" cn="初始来源单行内码" visible="1148" copy="0" lix="370" lock="-1" width="100"></th>
            <th el="100" ek="fentity" id="finisrcseq" fn="finisrcseq" pn="finisrcseq" ts="" cn="初始来源单行号" visible="1148" copy="0" lix="375" lock="-1"></th>

            <th id="fseltypeid" el="107" ek="fentity" fn="fseltypeid" ctlfk="fmaterialid" dispfk="fseltypeid" ts="" cn="型号" visible="1086" lix="360"></th>
        </tr>
    </table>

    <!--包装明细-->
    <table id="fpackentity" el="52" pk="fentryid" tn="t_bcm_packorderentry" pn="fpackentity" cn="已包装明细" kfks="fmaterialid"  >
        <tr>
            <th el="100" ek="fpackentity" id="fpackgroup_p" fn="fpackgroup" pn="fpackgroup" visible="1150" cn="打包批次"
                lock="-1" copy="0" lix="10" notrace="true" width="150" ts=""></th>
            <th el="152" ek="fpackentity" id="fpacktype_p" fn="fpacktype" pn="fpacktype" visible="1150" cn="打包类型"
                copy="1" lix="20" notrace="true" ts="" vals="'1':'标准','2':'1件多包','3':'1包多件'" width="120" lock="-1"></th>
            <!--<th el="107" ek="fpackentity" id="fpacktype_p" fn="fpacktype" pn="fpacktype" visible="1150" cn="打包类型"
    lock="-1" copy="1" lix="20" notrace="true" ts="" ctlfk="fmaterialid_p" dispfk="fpackagtype"></th>-->
            <th el="100" ek="fpackentity" id="fbarcode_p" fn="fbarcode" pn="fbarcode" visible="1150" cn="条码"
                lock="-1" copy="0" lix="30" notrace="true" ts=""></th>
            <th el="100" ek="fpackentity" id="fserialno_p" fn="fserialno" pn="fserialno" visible="1150" cn="序列号"
                lock="-1" copy="1150" lix="40" notrace="true" ts=""></th>
            <th el="101" ek="fpackentity" id="fallpacknum" fn="fallpacknum" pn="fallpacknum" visible="1150" cn="总包数"
                copy="1" lix="50" notrace="true" ts="" format="0,000"></th>
            <th el="101" ek="fpackentity" id="fpackno" fn="fpackno" pn="fpackno" visible="1150" cn="包序号"
                copy="1" lix="60" notrace="true" ts="" format="0,000"></th>
            <th el="107" ek="fpackentity" id="fmaterialid_p_fnumber" fn="fmaterialid_p_fnumber" pn="fmaterialid_p_fnumber" visible="1150" cn="商品编码"
                lock="-1" copy="1" lix="70" notrace="true" ts="" ctlfk="fmaterialid_p" dispfk="fnumber"></th>
            <th el="106" ek="fpackentity" id="fmaterialid_p" fn="fmaterialid" pn="fmaterialid" visible="1150" cn="商品"
                copy="0" lix="80" notrace="true" ts="" refid="ydj_product" filter="" reflvt="0" dfld="fspecifica" sformid=""></th>
            <th el="132" ek="fpackentity" id="fattrinfo_p" fn="fattrinfo" cn="辅助属性" lix="90" ctlfk="fmaterialid_p" pricefk="" width="140" visible="1150" lock="-1"></th>
            <th el="100" ek="fpackentity" len="2000" id="fcustomdesc_p" fn="fcustomdesc" cn="定制说明" lix="100" width="140" visible="1150" lock="-1"></th>
            <th el="103" ek="fpackentity" id="fstockqty_p" fn="fstockqty" cn="库存单位数量" lix="110" visible="1150" ctlfk="fstockunitid_p" basqtyfk="fqty_p" lock="-1" width="100" format="0,000.00"></th>
            <th el="109" ek="fpackentity" id="fstockunitid_p" fn="fstockunitid" cn="库存单位" lix="120" ctlfk="fmaterialid_p" refid="ydj_unit" sformid="" visible="1150" width="80" lock="-1"></th>
            <th el="100" ek="fpackentity" id="flotno_p" fn="flotno" pn="flotno" cn="批号" lix="130" width="100" visible="1150" lock="-1"></th>
            <th el="100" ek="fpackentity" id="fmtono_p" fn="fmtono" pn="fmtono" cn="物流跟踪号" lix="140" width="100" visible="1150" lock="-1"></th>
            <th el="149" ek="fpackentity" id="fownertype_p" fn="fownertype" pn="fownertype" lix="150" dataviewname="v_bd_ownerdata" cn="货主类型" width="100" visible="1150" lock="-1">
                <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
                <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
            </th>
            <th el="100" ek="fpackentity" id="ftjno_p" fn="ftjno" pn="ftjno" cn="套件组合号" lix="220" width="100" visible="-1" lock="-1"></th>
            <th el="100" ek="fpackentity" id="fpjno_p" fn="fpjno" pn="fpjno" cn="配件组合号" lix="230" width="100" visible="-1" lock="-1"></th>
            <th el="100" ek="fpackentity" id="fsfno_p" fn="fsfno" pn="fsfno" cn="沙发组合号" lix="240" width="100" visible="-1" lock="-1"></th>
            <th el="150" ek="fpackentity" id="fownerid_p" fn="fownerid" pn="fownerid" lix="160" ctlfk="fownertype_p" cn="货主" width="100" visible="1150" lock="-1"></th>
            <th el="140" ek="fpackentity" id="fsourceformid_p" fn="fsourceformid" ts="" cn="来源单据" visible="1148" copy="0" lix="170" lock="-1"></th>
            <th el="141" ek="fpackentity" id="fsourcebillno_p" fn="fsourcebillno" ts="" cn="来源单据编号" visible="1148" copy="0" lix="180" lock="-1" width="100"></th>
            <th el="100" ek="fpackentity" id="fsourceinterid_p" fn="fsourceinterid" ts="" cn="来源单内码" visible="1148" copy="0" lix="190" lock="-1"></th>
            <th el="100" ek="fpackentity" id="fsourceentryid_p" fn="fsourceentryid" ts="" cn="来源单行内码" visible="1148" copy="0" lix="200" lock="-1"></th>
            <th el="100" ek="fpackentity" id="fsourcelinenumber" fn="fsourcelinenumber" pn="fsourcelinenumber" ts="" cn="来源单据行号" visible="1148" copy="0" lock="-1" lix="72"></th>


            <th el="107" ek="fpackentity" id="fmtrlmodel_p" fn="fmtrlmodel" pn="fmtrlmodel" visible="0" cn="规格型号"
                lock="-1" copy="1" lix="265" notrace="true" ts="" ctlfk="fmaterialid_p" dispfk="fspecifica" refvt="0"></th>
            <th lix="109" el="107" ek="fpackentity" id="fcustom" fn="fcustom" pn="fcustom" visible="0" cn="允许定制" width="80" align="center"
                lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid_p" dispfk="fcustom" refvt="116"></th>
            <th el="109" ek="fpackentity" id="funitid_p" fn="funitid" cn="基本单位" lix="275" ctlfk="fmaterialid_p" refid="ydj_unit" sformid="" visible="0" width="80" lock="-1"></th>
            <th el="103" ek="fpackentity" id="fqty_p" fn="fqty" cn="基本单位数量" lix="278" visible="0" ctlfk="funitid_p" width="100" format="0,000.00" lock="-1"></th>
            <th el="106" ek="fpackentity" id="fstorehouseid_p" fn="fstorehouseid" cn="仓库" lix="285" refid="ydj_storehouse" sformid="" visible="-1" width="100" lock="-1"></th>
            <th el="153" ek="fpackentity" id="fstorelocationid_p" fn="fstorelocationid" cn="仓位" lix="286" ctlfk="fstorehouseid_p" luek="fentity" lunmfk="flocname" lunbfk="flocnumber" sformid="" visible="-1" width="100" lock="-1"></th>
            <th el="106" ek="fpackentity" id="fstockstatus_p" fn="fstockstatus" cn="库存状态" lix="287" refid="ydj_stockstatus" defVal="'311858936800219137'" sformid="" visible="0" width="100" lock="-1"></th>

            <th el="100" lix="20" ek="fpackentity" id="fentrynote_p" fn="fentrynote" pn="fentrynote" visible="-1" cn="备注" len="4000" />

            <th el="100" ek="fpackentity" id="freceptionno_p" fn="freceptionno" pn="freceptionno" cn="收货单号" visible="1148" lix="250" />
            <th el="140" ek="fpackentity" id="finisrcformid_p" fn="finisrcformid" pn="finisrcformid" ts="" cn="初始来源单据" visible="1148" copy="0" lix="250" lock="-1"></th>
            <th el="141" ek="fpackentity" id="finisrcbillno_p" fn="finisrcbillno" pn="finisrcbillno" ts="" cn="初始来源单据编号" visible="1148" copy="0" lix="260" lock="-1"></th>
            <th el="100" ek="fpackentity" id="finisrcinterid_p" fn="finisrcinterid" pn="finisrcinterid" ts="" cn="初始来源单内码" visible="1148" copy="0" lix="270" lock="-1"></th>
            <th el="100" ek="fpackentity" id="finisrcentryid_p" fn="finisrcentryid" pn="finisrcentryid" ts="" cn="初始来源单行内码" visible="1148" copy="0" lix="280" lock="-1" width="100"></th>
            <th el="100" ek="fpackentity" id="finisrcseq_p" fn="finisrcseq" pn="finisrcseq" ts="" cn="初始来源单行号" visible="1148" copy="0" lix="350" lock="-1"></th>

            <th id="fseltypeid_p" el="107" ek="fpackentity" fn="fseltypeid" ctlfk="fmaterialid_p" dispfk="fseltypeid" ts="" cn="型号" visible="1086" lix="361"></th>
        </tr>
    </table>

    <!--条码明细，通常用于在库包装时，选择条码进行拆包重包时，记录下所选择的条码范围-->
    <table id="fbarcodeentity" el="52" pk="fentryid" tn="t_bcm_packbarcdeentry" pn="fbarcodeentity" cn="条码明细" kfks="fbarcode">
        <tr>
            <th el="100" ek="fbarcodeentity" id="fbarcode" fn="fbarcode" pn="fbarcode" visible="1150" cn="条码"
                lock="-1" copy="0" lix="310" notrace="true" ts=""></th>

        </tr>
    </table>



    <!--<div id="opList">

    </div>-->
    <!--表单所涉及的权限项定义-->
    <!--<div id="permList">

    </div>-->
</body>
</html>