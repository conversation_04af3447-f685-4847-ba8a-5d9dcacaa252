<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="coo_company" basemodel="bd_basetmpl" el="3" cn="协同企业">
    <div id="fbillhead" el="51" pk="fid" tn="t_coo_company" pn="fbillhead" cn="协同企业">
        <input el="100" id="fdescription" len="1000" />
        <input el="100" ek="fbillhead" type="text" id="fservicetype" fn="fservicetype" pn="fservicetype" cn="业务性质" width="80" visible="-1" lix="1" />
        <input el="100" ek="fbillhead" type="text" id="fname" fn="fname" pn="fname" cn="企业名称" width="180" visible="-1" lix="3" />
        <input el="100" ek="fbillhead" type="text" id="farea" fn="farea" pn="farea" cn="区域" width="160" visible="-1" lix="4" />
        <input el="144" ek="fbillhead" id="fshowinfo" fn="fshowinfo" pn="fshowinfo" cn="企业介绍" width="80" visible="-1" btnid="showinfo" btntxt="详情" lix="5" />
        <input el="100" ek="fbillhead" type="text" id="fcoostatus" fn="fcoostatus" pn="fcoostatus" cn="协同状态" visible="-1" lix="6" />
        <input el="144" ek="fbillhead" id="foperate" fn="foperate" pn="foperate" cn="操作" width="260" visible="-1" btnid="removesyn,waitaccept,accept,synadjust" btntxt="解除协同,等待受理,受理,绑定调整" lix="99" />
        <input el="100" ek="fbillhead" type="text" id="fcompanyId" fn="fcompanyId" pn="fcompanyId" cn="企业Id" visible="32" />
        <input el="100" ek="fbillhead" type="text" id="fmycompanyId" fn="fmycompanyId" pn="fmycompanyId" cn="我的企业Id" visible="32" />
        <input el="100" ek="fbillhead" type="text" id="fcustomerorsupplierId" fn="fcustomerorsupplierId" pn="fcustomerorsupplierId" cn="客户/供应商Id" visible="32" />
        <input el="100" ek="fbillhead" type="text" id="fnumber" fn="fnumber" pn="fnumber" visible="0" />
        <input el="100" ek="fbillhead" type="text" id="fdescription" fn="fdescription" pn="fdescription" visible="0" />
        <input type="text" id="fcreatorid" el="118" ek="fbillhead" refId="Sec_User" dfld="FName" fn="fcreatorid" pn="fcreatorid" visible="0" />
        <input type="datetime" id="fcreatedate" el="119" ek="fbillhead" fn="fcreatedate" pn="fcreatedate" visible="0" />
        <select id="fstatus" el="122" refId="bd_enum" dfld="fenumitem" cg="数据状态" ek="fbillhead" fn="fstatus" pn="fstatus" cn="数据状态" visible="0"></select>
        <!--系统内置逻辑依赖字段-->
        <input type="checkbox" id="fissponsor" el="116" ek="FBillHead" fn="fissponsor" ts="" visible="0" cn="发起方" />
        <!--此字段会因激活操作而随时变化-->
        <input type="checkbox" id="fiswaiting" el="116" ek="FBillHead" fn="fiswaiting" ts="" visible="32" cn="等待处理方" />
        <input el="100" ek="fbillhead" type="text" id="fprovince" fn="fprovince" pn="fprovince" visible="0" cn="省" />
        <input el="100" ek="fbillhead" type="text" id="fcity" fn="fcity" pn="fcity" visible="0" cn="市" />
        <input el="100" ek="fbillhead" type="text" id="fregion" fn="fregion" pn="fregion" visible="0" cn="区" />
        <input el="100" ek="fbillhead" type="text" id="fccrelationid" fn="fccrelationid" pn="fccrelationid" visible="32" cn="云链关联记录id" />
        <input el="100" ek="fbillhead" type="text" id="fproductid" fn="fproductid" pn="fproductid" visible="0" cn="对方产品标识"/>
        <input el="152" ek="fbillhead" visible="-1" id="foperationmode" fn="foperationmode" pn="foperationmode" cn="运营模式" 
               lix="15" vals="'1':'总部直营','2':'独立经营'" />
        <input el="116" ek="fbillhead" id="fisdealer" fn="fisdealer" cn="是否是经销商" visible="0" />
    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">        
        <ul el="10" ek="fbillhead" id="accpetSyn" op="setCompanyRelationStatus" opn="受理" data="" permid="coo_accpetsyn"></ul>
        <ul el="10" ek="fbillhead" id="removeSyn" op="setCompanyRelationStatus" opn="解除协同" data="" permid="coo_removesyn"></ul>
        <ul el="10" ek="fbillhead" id="adjustSyn" op="setCompanyRelationStatus" opn="绑定调整" data="" permid="coo_adjustsyn"></ul>
        <ul el="10" ek="fbillhead" id="activateSyn" op="setCompanyRelationStatus" opn="激活" data="" permid="coo_activatesyn"></ul>
    </div>
    <!--表单所涉及的权限项定义-->
    <div id="permList">
        <ul el="12" id="coo_accpetsyn" cn="受理"></ul>
        <ul el="12" id="coo_removesyn" cn="解除协同"></ul>
        <ul el="12" id="coo_adjustsyn" cn="绑定调整"></ul>
        <ul el="12" id="coo_activatesyn" cn="激活"></ul>
    </div>
</body>
</html>
