<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="sel_suite" el="3" basemodel="bd_basetmpl" cn="套件" isolate="0" >
    <div id="fbillhead" el="51" pk="fid" tn="t_sel_suite" pn="fbillhead" cn="套件">
        <!--基本信息-->
        <input group="基本信息" el="107" ek="fbillhead" visible="-1" id="fproductnumber" fn="fproductnumber" pn="fproductnumber" cn="套件商品编码" ctlfk="fproductid" dispfk="fnumber" lock="-1" width="150" lix="8" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fproductid" fn="fproductid" pn="fproductid" cn="套件商品名称" refid="ydj_product" dfld="fnumber" filter="fsuiteflag='1'" width="200" lix="11" />

        <input type="text" id="fdescription" el="100" ek="fbillhead" fn="fdescription" apipn="description" pn="fdescription" cn="备注" width="280" visible="0" lix="500" />

        <!--子单据体-->
        <table id="fentity" el="52" pk="fentryid" tn="t_sel_suiteentry" pn="fentity" cn="明细信息" kfks="fpartproductid">
            <tr>
                <th el="107" ek="fentity" id="fpartproductnumber" fn="fpartproductnumber" pn="fpartproductnumber" cn="子件商品编码" ctlfk="fpartproductid" dispfk="fnumber" lock="-1" width="150" visible="-1" lix="20"></th>
                <th el="106" ek="fentity" id="fpartproductid" fn="fpartproductid" pn="fpartproductid" cn="子件商品名称" refid="ydj_product" dfld="fnumber" width="300" filter="fsuiteflag!='1'" visible="-1" lix="10" must="1"></th>
                <th el="116" ek="fentity" id="fwhetherdefault" fn="fwhetherdefault" pn="fwhetherdefault" cn="是否默认" visible="-1" lix="30" width="80"></th>
                <th el="103" ek="fentity" id="fqty" fn="fqty" pn="fqty" cn="数量" visible="-1" lix="40" must="1"></th>
                <th el="101" ek="fentity" id="fsequence_d" fn="fsequence" pn="fsequence" cn="生成顺序" visible="-1" lix="45"></th>
                <th el="106" ek="fentity" id="fpartprop" fn="fpartprop" pn="fpartprop" cn="子件描述" visible="1150" lix="55" width="250" mast="1" refid="sel_prop" sbm="true"></th>
            </tr>
        </table>
    </div>

    <div id="opList">
        <ul el="10" id="save" op="save" opn="保存">
            <li el="11" vid="510" ek="fentity" cn="默认子件的数量需要大于0!" data="{'expr':'fwhetherdefault == False or (fwhetherdefault == True and fqty > 0)','message':'默认子件的数量需要大于0!'}"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="MSSaveSync" op="MSSaveSync" opn="慕思协同保存" data="{'syncFieldIds': ['fnumber','fname','fproductid','fdescription','fpartproductid','fwhetherdefault','fqty','fsequence_d','fpartprop']}" permid=""></ul>
    </div>
</body>
</html>