<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="sel_selectionform" basemodel="" el="0" width="1360px" height="670px" cn="套件选配">
    <div id="fbillhead" el="51" pk="fid" tn="" pn="fbillhead" cn="基本信息">
        <input el="107" ek="fbillhead" visible="-1" id="fsuitenumber" fn="fsuitenumber" pn="fsuitenumber" cn="套件商品编码" ctlfk="fsuiteid" dispfk="fnumber" lock="-1" />
        <input el="106" ek="fbillhead" visible="-1" id="fsuiteid" fn="fsuiteid" pn="fsuiteid" cn="套件商品名称" refid="ydj_product" dfld="fnumber" lock="-1" />
        <input el="101" ek="fbillhead" visible="-1" id="fsuitesumqty" fn="fsuitesumqty" pn="fsuitesumqty" cn="套件总件数" lock="-1" />
        <input el="103" ek="fbillhead" visible="-1" id="fsuiteqty" fn="fsuiteqty" pn="fsuiteqty" cn="套件数量" />

        <!--子件信息-->
        <table id="fentity" el="52" pk="fentryid" tn="" pn="fentity" cn="子件信息" height="500px">
            <tr>
                <th el="107" ek="fentity" visible="1150" width="160" id="fcomponentnumber" fn="fcomponentnumber" pn="fcomponentnumber" cn="子件商品编码" ctlfk="fcomponentid" dispfk="fnumber" lock="-1"></th>
                <th el="106" ek="fentity" visible="1150" width="160" id="fcomponentid" fn="fcomponentid" pn="fcomponentid" cn="子件商品名称" refid="ydj_product" dfld="fnumber,fselcategoryid,fcategoryid,fispresetprop" mast="1" sformid=""></th>
                <th el="152" ek="fentity" visible="1150" id="funstdtypestatus" fn="funstdtypestatus" pn="funstdtypestatus" cn="非标审批状态" vals="'01':'新建','02':'待审批','03':'审批通过','04':'驳回','05':'待定价','06':'终审'" defval="" copy="0" lock="-1"></th>
                <th el="102" ek="fentity" visible="1150" id="fqty" fn="fqty" pn="fqty" cn="数量" mast="1" defval="1"></th>
                <th el="116" ek="fentity" visible="1150" width="80" id="funstdtypeflag" fn="funstdtypeflag" pn="funstdtypeflag" cn="是否非标" refid="mt_partsselection"></th>
                <th el="107" ek="fentity" visible="1150" width="160" id="fselcategoryid" fn="fselcategoryid" pn="fselcategoryid" cn="选配类别" refid="sel_category" ctlfk="fcomponentid" dispfk="fselcategoryid" lock="-1"></th>
                <th el="107" ek="fentity" visible="0" width="160" id="fcategoryid" fn="fcategoryid" pn="fcategoryid" cn="商品类别" refid="ydj_category" ctlfk="fcomponentid" dispfk="fcategoryid" lock="-1"></th>
                <th el="107" ek="fentity" visible="0" width="160" id="fispresetprop" fn="fispresetprop" pn="fispresetprop" cn="允许选配" ctlfk="fcomponentid" dispfk="fispresetprop" lock="-1"></th>
            </tr>
        </table>

        <!--属性选配-->
        <table id="fpropentity" el="52" pk="fentryid" tn="" pn="fpropentity" cn="属性选配" height="500px">
            <tr>
                <th el="106" ek="fpropentity" id="fselpropid" pn="fselpropid" cn="属性名" refid="sel_prop" sformid="" width="100" visible="-1" lock="-1" mast="1"></th>
                <th el="152" ek="fpropentity" visible="0" id="fpropvaluesrc" pn="fpropvaluesrc" cn="属性值来源"
                    vals="'basedata':'基础资料','enumdata':'辅助资料','text':'文本'" lock="-1"></th>
                <th el="166" ek="fpropentity" visible="0" id="fpropvaluetype" pn="fpropvaluetype" cn="属性值类型"
                    refid="bd_bizfieldtype" dfld="fsrc" sformid=""></th>
                <th el="167" ek="fpropentity" visible="1150" id="fpropvalue" pn="fpropvalue" cn="属性值"
                    ctlfk="fpropvaluesrc,fpropvaluetype" width="200" sformid="" esp="false" ffks="fnumber,fname"></th>
                <th el="100" ek="fpropentity" id="fdatatype" pn="fdatatype" cn="数据类型" width="90" visible="0" lock="-1"></th>
                <th el="116" ek="fpropentity" id="fiscontrolmust" pn="fiscontrolmust" cn="是否必录" width="90" visible="0" lock="-1"></th>
                <th el="116" ek="fpropentity" visible="1150" id="fcustom" fn="fcustom" pn="fcustom" cn="支持非标" lock="-1"></th>
                <th el="100" ek="fpropentity" visible="1150" id="fcustomvalue" fn="fcustomvalue" pn="fcustomvalue" cn="非标值" width="200" len="200"></th>
                <th el="116" ek="fentry" id="fenableconstraint" pn="fenableconstraint" cn="属性值是否启用选配约束" width="240" visible="0"
                    desc="根据此标记来决定是否要启用选配约束条件逻辑"></th>
            </tr>
        </table>

    </div>

</body>
</html>