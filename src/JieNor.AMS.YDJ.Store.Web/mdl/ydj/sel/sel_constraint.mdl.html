<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="sel_constraint" el="3" basemodel="bd_basetmpl" cn="选配约束条件" isolate="0" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_sel_constraint" pn="fbillhead" cn="选配约束条件">
        <input el="106" ek="fbillhead" visible="-1" id="fselcategoryid" fn="fselcategoryid" pn="fselcategoryid" cn="选配类别" refid="sel_category" lix="10" must="1" width="150" />
        <input type="text" id="fdescription" el="100" ek="fbillhead" fn="fdescription" apipn="description" pn="fdescription" cn="备注" width="280" visible="0" lix="500" />
    </div>

    <table id="fentity" el="52" pk="fentryid" tn="t_sel_constraintentry" pn="fentity" kfks="fconstraintcondition,fconstraintval">
        <tr>
            <th el="100" ek="fentity" id="fconstraintcondition" fn="fconstraintcondition" pn="fconstraintcondition" cn="约束条件" visible="-1" lix="15" width="400" len="2000" xsslv="1" must="1"></th>
            <th el="100" ek="fentity" id="fconstraintval" fn="fconstraintval" pn="fconstraintval" cn="约束值" visible="-1" lix="20" width="500" len="2000" xsslv="1" must="1"></th>
        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="MSSaveSync" op="MSSaveSync" opn="慕思协同保存" data="{'syncFieldIds': ['fname','fnumber','fselcategoryid','fconstraintcondition','fconstraintval']}" permid=""></ul>
    </div>
</body>
</html>