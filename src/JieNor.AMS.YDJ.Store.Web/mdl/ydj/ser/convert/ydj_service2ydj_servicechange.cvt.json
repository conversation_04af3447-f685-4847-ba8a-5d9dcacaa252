//{
//  "Id": "ydj_service2ydj_servicechange",
//  "Number": "ydj_service2ydj_servicechange",
//  "Name": "服务单下推服务变更单",
//  "SourceFormId": "ydj_service",
//  "TargetFormId": "ydj_servicechange",
//  "ActiveEntityKey": "fproductentry",
//  "FilterString": "(fserstatus='sersta02' or fserstatus='sersta04' or fserstatus='sersta05' or fserstatus='sersta06') and fsettlestatus='settle_status01'",
//  "Message": "补价变更失败：服务订单状态必须是【待师傅确认】、【待师傅预约】、【待评价】、【待完工】且结算状态为【未结算】时才可以申请补价！",
//  "FieldMappings": [
//    //表头字段
//    {
//      "Id": "fservicetype",
//      "Name": "服务类型",
//      "MapType": 0,
//      "SrcFieldId": "fservicetype",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fdealerid",
//      "Name": "商户",
//      "MapType": 0,
//      "SrcFieldId": "fdealerid",
//      "MapActionWhenGrouping": 0,
//      "Order": 1
//    },
//    {
//      "Id": "fmerbill",
//      "Name": "商户单号",
//      "MapType": 0,
//      "SrcFieldId": "fmerbill",
//      "MapActionWhenGrouping": 0,
//      "Order": 2
//    },
//    {
//      "Id": "fteamid",
//      "Name": "团队",
//      "MapType": 0,
//      "SrcFieldId": "fteamid",
//      "MapActionWhenGrouping": 0,
//      "Order": 3
//    },
//    {
//      "Id": "fdeptid_link",
//      "Name": "关联部门",
//      "MapType": 0,
//      "SrcFieldId": "fdeptid_link",
//      "MapActionWhenGrouping": 0,
//      "Order": 3
//    },
//    {
//      "Id": "fstaffid_link",
//      "Name": "关联账号",
//      "MapType": 0,
//      "SrcFieldId": "fstaffid_link",
//      "MapActionWhenGrouping": 0,
//      "Order": 3
//    },
//    {
//      "Id": "fcaptainid",
//      "Name": "队长",
//      "MapType": 0,
//      "SrcFieldId": "fcaptainid",
//      "MapActionWhenGrouping": 0,
//      "Order": 4
//    },
//    {
//      "Id": "fmasterid",
//      "Name": "师傅",
//      "MapType": 0,
//      "SrcFieldId": "fmasterid",
//      "MapActionWhenGrouping": 0,
//      "Order": 5
//    },
//    {
//      "Id": "fdealernumber",
//      "Name": "订单号",
//      "MapType": 0,
//      "SrcFieldId": "fdealernumber",
//      "MapActionWhenGrouping": 0,
//      "Order": 6
//    },
//    {
//      "Id": "fcustname",
//      "Name": "客户名称",
//      "MapType": 0,
//      "SrcFieldId": "fname",
//      "MapActionWhenGrouping": 0,
//      "Order": 7
//    },
//    {
//      "Id": "fphone",
//      "Name": "客户电话",
//      "MapType": 0,
//      "SrcFieldId": "fphone",
//      "MapActionWhenGrouping": 0,
//      "Order": 8
//    },
//    {
//      "Id": "fprovince",
//      "Name": "省",
//      "MapType": 0,
//      "SrcFieldId": "fprovince",
//      "MapActionWhenGrouping": 0,
//      "Order": 9
//    },
//    {
//      "Id": "fcity",
//      "Name": "市",
//      "MapType": 0,
//      "SrcFieldId": "fcity",
//      "MapActionWhenGrouping": 0,
//      "Order": 10
//    },
//    {
//      "Id": "fregion",
//      "Name": "区",
//      "MapType": 0,
//      "SrcFieldId": "fregion",
//      "MapActionWhenGrouping": 0,
//      "Order": 11
//    },
//    {
//      "Id": "fsourcetype",
//      "Name": "源单类型",
//      "MapType": 1,
//      "SrcFieldId": "'ydj_service'",
//      "MapActionWhenGrouping": 0,
//      "Order": 13
//    },
//    {
//      "Id": "fsourcenumber",
//      "Name": "源单编号",
//      "MapType": 0,
//      "SrcFieldId": "fbillno",
//      "MapActionWhenGrouping": 0,
//      "Order": 14
//    },

//    //服务项目明细字段
//    {
//      "Id": "fseritemid",
//      "Name": "服务项目",
//      "MapType": 0,
//      "SrcFieldId": "fseritemid",
//      "MapActionWhenGrouping": 0,
//      "Order": 15
//    },
//    {
//      "Id": "funitid",
//      "Name": "单位",
//      "MapType": 0,
//      "SrcFieldId": "funitid",
//      "MapActionWhenGrouping": 0,
//      "Order": 18
//    },
//    {
//      "Id": "fqty",
//      "Name": "数量",
//      "MapType": 1,
//      "SrcFieldId": "fqty",
//      "MapActionWhenGrouping": 0,
//      "Order": 19
//    },
//    {
//      "Id": "fqty_chg",
//      "Name": "数量",
//      "MapType": 1,
//      "SrcFieldId": "fqty",
//      "MapActionWhenGrouping": 0,
//      "Order": 19
//    },
//    {
//      "Id": "fprice",
//      "Name": "单价",
//      "MapType": 0,
//      "SrcFieldId": "fprice",
//      "MapActionWhenGrouping": 0,
//      "Order": 23
//    },
//    {
//      "Id": "famount",
//      "Name": "金额",
//      "MapType": 1,
//      "SrcFieldId": "famount",
//      "MapActionWhenGrouping": 0,
//      "Order": 24
//    },
//    {
//      "Id": "famount_chg",
//      "Name": "金额",
//      "MapType": 1,
//      "SrcFieldId": "famount",
//      "MapActionWhenGrouping": 0,
//      "Order": 24
//    },
//    {
//      "Id": "forderprice",
//      "Name": "商户下单价",
//      "MapType": 0,
//      "SrcFieldId": "forderprice",
//      "MapActionWhenGrouping": 0,
//      "Order": 26
//    },
//    {
//      "Id": "fsourceformid",
//      "Name": "来源单类型",
//      "MapType": 1,
//      "SrcFieldId": "'ydj_service'",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fsourcebillno",
//      "Name": "来源单编号",
//      "MapType": 0,
//      "SrcFieldId": "fbillno",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fsourceinterid",
//      "Name": "来源单内码",
//      "MapType": 0,
//      "SrcFieldId": "fbillhead.id",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fsourceentryid",
//      "Name": "来源单分录内码",
//      "MapType": 0,
//      "SrcFieldId": "fproductentry.id",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fmerchantorderno",
//      "Name": "商户订单编号",
//      "MapType": 0,
//      "SrcFieldId": "fsourcebillno",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fmerchantorderinterid",
//      "Name": "商户订单内码",
//      "MapType": 0,
//      "SrcFieldId": "fsourceinterid",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fmerchantorderentryid",
//      "Name": "商户订单分录内码",
//      "MapType": 0,
//      "SrcFieldId": "fsourceentryid",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fcustomerid",
//      "Name": "客户",
//      "MapType": 0,
//      "SrcFieldId": "fcustomerid",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fdeptid",
//      "Name": "销售部门",
//      "MapType": 0,
//      "SrcFieldId": "fdeptid",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    }
//  ],
//  "BillGroups": [
//    {
//      "Id": "fbillno",
//      "Order": 1
//    }
//  ],
//  "FieldGroups": [
//    {
//      "Id": "fproductentry_fentryid",
//      "Order": 1
//    }
//  ]
//}