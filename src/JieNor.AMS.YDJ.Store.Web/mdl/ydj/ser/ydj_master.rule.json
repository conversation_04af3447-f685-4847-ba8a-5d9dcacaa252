{
  //规则引擎基类
  "base": "/mdl/bd.rule.json",

  //定义表单锁定规则
  "lockRules": [
    {
      "id": "fapprovestatus_auth2",
      "expression": "field:*|fapprovestatus!='auth1'"
    },
    {
      "id": "fapprovestatus_auth1",
      "expression": "field:$*|fapprovestatus=='auth1'"
    }
    ,{
      "id": "fapprovestatus_auth4",
      "expression": "field:$*|fapprovestatus=='auth4'"
    }
  ],
  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [

  ]
}