<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ser_store" basemodel="bd_basetmpl" el="3" cn="门店" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ser_store" pn="fbillhead" cn="门店">

        <!--基本信息-->
        <input group="基本信息" el="106" ek="fbillhead" id="fdealerid" fn="fdealerid" pn="fdealerid" cn="所属商户" refid="ydj_customer" dfld="fname" filter="fcustype='customercate_03'" visible="-1" />
    	<input group="基本信息" type="text" id="fimage" el="111" ek="FBillHead" fn="fimage" pn="fimage" cn="门店图片" visible="-1" lix="4" />
        <input group="意向信息" el="131" type="text" id="fcategoryid" ek="fbillhead" fn="fcategoryid" pn="fcategoryid" refid="ydj_category" ts="" visible="-1" cn="商品类别" lix="5" />
        <input group="基本信息" el="106" id="fbrandid" ek="fbillhead" fn="fbrandid" pn="fbrandid" refid="ydj_brand" dfld="fname" visible="-1" cn="品牌" lix="6"  />
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fprovince" fn="fprovince" pn="fprovince" cn="省" cg="省" refid="bd_enum" dfld="fenumitem" lix="7"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fcity" fn="fcity" pn="fcity" cn="市" cg="市" refid="bd_enum" dfld="fenumitem" lix="8"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fregion" fn="fregion" pn="fregion" cn="区" cg="区" refid="bd_enum" dfld="fenumitem" lix="9"></select>
        <input group="基本信息" el="100" type="text" id="faddress" ek="fbillhead" fn="faddress" ts="" cn="详细地址" visible="-1" lix="10" />
        <input group="基本信息" el="100" type="text" id="fstaffsize" ek="fbillhead" fn="fstaffsize" ts="" cn="人员规模" visible="-1" lix="11" />
        <input group="基本信息" el="100" type="text" id="fcontact" ek="fbillhead" fn="fcontact" ts="" cn="联系人" visible="-1" lix="12" />
        <input group="基本信息" el="100" type="text" id="fphone" ek="fbillhead" fn="fphone" ts="" cn="联系电话" visible="-1" lix="13" />
        <input group="基本信息" el="100" type="text" id="faddress" ek="fbillhead" fn="faddress" ts="" cn="详细地址" visible="-1" lix="14" />
        
    </div>

</body>
</html>