<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="mt_dimensionalmaterial" basemodel="bd_basetmpl" el="3" cn="维度物料">
    <div id="fbillhead" el="51" pk="fid" tn="t_mt_dimensionalmaterial" pn="fbillhead" cn="维度物料">
        <input el="108" type="text" ek="fbillhead" id="fnumber" fn="fnumber" pn="fnumber" cn="编码" width="120" visible="-1" copy="0" lix="1" />
        <input el="100" type="text" ek="fbillhead" id="fname" fn="fname" pn="fname" cn="名称" width="120" visible="-1" lix="2" />
        <input el="100" type="text" ek="fbillhead" id="fmaterialgroup" fn="fmaterialgroup" pn="fmaterialgroup" cn="物料分组" width="120" visible="-1" lix="5" />
        <input el="100" type="text" ek="fbillhead" id="fspecification" fn="fspecification" pn="fspecification" cn="规格型号" width="120" visible="-1" lix="10" />
    </div>

</body>
</html>