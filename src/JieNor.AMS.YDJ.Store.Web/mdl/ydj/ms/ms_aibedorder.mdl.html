<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head><body id="ms_aibedorder" el="1" basemodel="bill_basetmpl" cn="床垫选配单" rac="false">
    <div id="fbillhead" el="51" pk="fid" tn="t_ms_aibedorder" pn="fbillhead" cn="床垫选配单"> 
        
        <!--重写基类模型中的部分字段属性-->
        <input id="fbillno" cn="客户ID" desc="由总部的编码规则生成" />

        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fdeptid" fn="fdeptid" pn="fdeptid" cn="销售部门" refid="ydj_dept" reflvt="1" defVal="@currentDeptId" apipn="saleDept" canchange="true" lix="110" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fstaffid" fn="fstaffid" pn="fstaffid" cn="销售员" refid="ydj_staff" defVal="@currentStaffId" apipn="saleMan"  dfld="fname" canchange="true" lix="120" />
        
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fcustomername" fn="fcustomername" pn="fcustomername" cn="客户名称" width="100" lix="130" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fphone" fn="fphone" pn="fphone" cn="手机号" lix="140" />

        <input group="基本信息" el="107" ek="fbillhead" id="fproductno" cn="商品编码" visible="-1" width="120" lock="-1" copy="1" ctlfk="fproductid" dispfk="fnumber" refvt="0" lix="150" />
        <input group="基本信息" el="106" ek="fbillhead" id="fproductid" fn="fproductid" cn="商品" refid="ydj_product" visible="-1" width="200" copy="-1" filter="" reflvt="0" dfld="fname,fnumber,fselcategoryid" lix="160" must="1" />
        <input group="基本信息" el="104" ek="fbillhead" id="fprice" fn="fprice" cn="销售价" visible="-1" width="80" copy="-1" format="0,000.000000" dformat="0,000.00" lix="170" />

        <input group="基本信息" el="116" ek="fbillhead" id="fispushorder" fn="fispushorder" pn="fispushorder" visible="-1" cn="是否转单"
               lock="-1" copy="1" lix="180" notrace="false" ts="" defval="false" />

        <input group="基本信息" el="100" ek="fbillhead" id="fcustomparam" fn="fcustomparam" pn="fcustomparam" visible="-1" cn="床垫定制参数" lock="-1" visible="-1" len="200" must="1"/>
        <input group="基本信息" el="100" ek="fbillhead" id="fotherparam" fn="fotherparam" pn="fotherparam" visible="-1" cn="其他参数" desc="由小程序控制使用" lock="-1" visible="0" len="2000"/>
    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
    </div>

    <div id="permList">

    </div>
</body>
</html>