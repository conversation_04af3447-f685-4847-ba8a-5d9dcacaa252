<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head><body id="ms_boss" el="3" basemodel="" cn="实控人" desc="用于保存慕思同步的数据" isolate="0" rac="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ms_boss" pn="fbillhead" cn="实控人">
        <input type="text" id="fnumber" el="108" ek="fbillhead" fn="fnumber" pn="fnumber" apipn="number" cn="编码" desc="编码" width="120" visible="-1" copy="0" lix="1" />
        <input type="text" id="fname" el="100" ek="fbillhead" fn="fname" pn="fname" apipn="name" cn="名称" desc="名称" width="120" visible="-1" lix="2" notrace="false"/>
        <input type="text" id="faccount" el="100" ek="fbillhead" fn="faccount" pn="faccount" apipn="account" cn="登录账号" desc="登录账号" width="120" visible="-1" lix="3" />
        <input type="text" id="fsex" el="100" ek="fbillhead" fn="fsex" pn="fsex" apipn="sex" cn="性别编码" desc="性别编码" width="120" visible="-1" lix="4" />
        <input type="text" id="fcontactinfo" el="100" ek="fbillhead" fn="fcontactinfo" pn="fcontactinfo" apipn="contactInfo" cn="联系方式" desc="联系方式" width="120" visible="-1" lix="5" notrace="false"/>
        <input type="text" id="ftencentqq" el="100" ek="fbillhead" fn="ftencentqq" pn="ftencentqq" apipn="tencentQQ" cn="QQ" desc="QQ" width="120" visible="-1" lix="6" />
        <input type="text" id="femail" el="100" ek="fbillhead" fn="femail" pn="femail" apipn="email" cn="电子邮箱" desc="电子邮箱" width="120" visible="-1" lix="7" />
        <input type="text" id="fidcard" el="100" ek="fbillhead" fn="fidcard" pn="fidcard" apipn="idCard" cn="身份证" desc="身份证" width="120" visible="-1" lix="8" />
        <input type="text" id="feducation" el="100" ek="fbillhead" fn="feducation" pn="feducation" apipn="education" cn="学历编码" desc="学历编码" width="120" visible="-1" lix="9" />
        <input type="text" id="fage" el="100" ek="fbillhead" fn="fage" pn="fage" apipn="age" cn="年龄" desc="年龄" width="120" visible="-1" lix="10" />
        <input type="text" id="fmaritalstatus" el="100" ek="fbillhead" fn="fmaritalstatus" pn="fmaritalstatus" apipn="maritalStatus" cn="婚姻状况编码" desc="婚姻状况编码" width="120" visible="-1" lix="11" />
        <input type="text" id="fnativeplace" el="100" ek="fbillhead" fn="fnativeplace" pn="fnativeplace" apipn="nativePlace" cn="籍贯" desc="籍贯" width="120" visible="-1" lix="12" />
        <input type="text" id="frolecode" el="100" ek="fbillhead" fn="frolecode" pn="frolecode" apipn="roleCode" cn="人员类型编码" desc="人员类型编码" width="120" visible="-1" lix="12" />
        <input type="text" id="fchanneltype" el="100" ek="fbillhead" fn="fchanneltype" pn="fchanneltype" apipn="channelType" cn="渠道合作类型" desc="渠道合作类型" width="120" visible="-1" lix="12" />


        <input type="datetime" id="fcreatedate" el="113" ek="fbillhead" fn="fcreatedate" pn="fcreatedate" cn="创建日期" width="130" visible="-1" copy="0" lix="251" xlsin="0" />
        <input type="datetime" id="fmodifydate" el="113" ek="fbillhead" fn="fmodifydate" pn="fmodifydate" cn="更新日期" visible="1124" copy="0" lix="253" xlsin="0" />

        <input type="checkbox" id="fbossstatus" el="152" ek="fbillhead" fn="fbossstatus" pn="fbossstatus" cn="状态" vals="'0':'启用','1':'禁用'" defval="'1'" visible="-1" xlsin="0" copy="0" lix="263" />

        <input type="text" id="ftranid" el="100" ek="FBillHead" fn="ftranid" apipn="tranId" ts="" cn="交易流水号" visible="0" xlsin="0" copy="0" />
        
        <input type="text" id="fjson" el="127" ek="fbillhead" fn="fjson" pn="fjson" cn="json" width="120" visible="0" lix="12" />
    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存" data="" permid="">
            <li el="11" clear></li>
        </ul>
        <ul el="10" ek="fbillhead" id="query" op="query" opn="查看" data="" permid="fw_view"></ul>
        <ul el="10" ek="fbillhead" id="syncfrommusi" op="syncfrommusi" opn="从慕思拉取数据" permid="fw_syncfrommusi"></ul>
    </div>

    <div id="permList">
        <ul el="12" id="fw_view" cn="查看" order="1"></ul>
        <ul el="12" id="fw_syncfrommusi" cn="从慕思拉取数据"></ul>
    </div>
</body>
</html>