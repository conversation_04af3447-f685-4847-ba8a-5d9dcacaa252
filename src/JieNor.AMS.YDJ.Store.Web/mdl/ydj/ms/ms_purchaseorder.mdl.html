<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ms_purchaseorder" el="3" basemodel="" cn="慕思中台采购订单数据" desc="用于慕思中台采购订单数据" isolate="0">
    <div id="fbillhead" el="51" pk="fid" tn="t_ms_purchaseorder" pn="fbillhead" cn="慕思中台采购订单数据">

        <input id="fjson" el="127" ek="fbillhead" fn="fjson" pn="fjson" cn="json" width="120" visible="0" lix="12" />

        <input id="fmd5" el="100" ek="fbillhead" fn="fmd5" pn="fmd5" cn="md5" desc="json的md5值" width="120" visible="0" lix="12" />

        <input id="fdatatype" el="100" ek="fbillhead" fn="fdatatype" pn="fdatatype" cn="数据类型" desc="用于保存外部数据的类型" width="120" visible="-1" lix="12" />
        <input id="ftype" el="100" ek="fbillhead" fn="ftype" pn="ftype" cn="数据类型" desc="用于保存外部数据的类型" width="120" visible="-1" lix="12" />

        <input id="fcreatedate" el="113" ek="fbillhead" fn="fcreatedate" pn="fcreatedate" cn="创建日期" width="130" visible="-1" copy="0" lix="251" xlsin="0" defval="@currentlongdate" />
        <input id="ftranid" el="100" ek="FBillHead" fn="ftranid" cn="交易流水号" desc="用于保存外部id" visible="0" />

        <input id="fbizformid" el="106" ek="fbillhead" fn="fbizformid" pn="fbizformid" cn="业务表单" refid="sys_bizobject" width="120" visible="-1" lix="12" />
        <input el="100" ek="fbillhead" id="fbizobjid" fn="fbizobjid" pn="fbizobjid" cn="业务数据标识" width="125" visible="-1" lix="10" />
        <input el="100" ek="fbillhead" id="fbillno" fn="fbillno" pn="fbillno" cn="业务数据编码" width="120" visible="-1" lix="15" />

        <input el="100" ek="fbillhead" id="fshiptocode" fn="fshiptocode" pn="fshiptocode" cn="送达方编码" width="120" visible="-1" lix="15" />
        <input el="100" ek="fbillhead" id="fsaletocode" fn="fsaletocode" pn="fsaletocode" cn="经销商编码" width="120" visible="-1" lix="15" />
        <select el="152" ek="fbillhead" visible="996" id="fchangestatus" fn="fchangestatus" pn="fchangestatus" cn="变更状态"
                vals="'0':'正常','1':'变更中','2':'变更完成','3':'变更已提交'" xlsin="0" copy="0" lix="266" align="center" lock="-1"></select>


        <input el="100" ek="fbillhead" id="fstatus" fn="fstatus" pn="fstatus" visible="-1" cn="状态" lock="-1" copy="0" lix="0" notrace="true" ts="" width="75"></th>
        <input lix="30" el="152" ek="fbillhead" visible="1150" id="fhqderstatus" fn="fhqderstatus" pn="fhqderstatus" cn="总部合同状态" vals="'01':'新建','02':'提交至总部','03':'已终审','04':'排产中','05':'驳回'" defval="" copy="0" lock="-1" />
        <input el="152" ek="fbillhead" id="fopstatus" fn="fopstatus" pn="fopstatus" visible="-1" cn="操作状态" defval="'0'"
               lock="-1" copy="0" lix="0" notrace="true" ts="" width="75" vals="0:'失败',1:'成功'"></th>

        <input id="fretrydate" el="113" ek="fbillhead" fn="fretrydate" pn="fretrydate" cn="重试时间" width="130" visible="-1" copy="0" lix="251" />
        <input id="fretrytimes" el="101" ek="fbillhead" fn="fretrytimes" pn="fretrytimes" cn="重试次数" width="130" visible="-1" copy="0" lix="251" />
    </div>
    
    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="query" op="query" opn="查看" data="" permid="fw_view"></ul>
    </div>

    <div id="permList">
        <ul el="12" id="fw_view" cn="查看" order="1"></ul>
    </div>
</body>
</html>