{
  "Id": "ydj_order2sal_deliverynotice",
  "Number": "ydj_order2sal_deliverynotice",
  "Name": "销售合同生成发货通知单",
  "SourceFormId": "ydj_order",
  "TargetFormId": "sal_deliverynotice",
  "ControlFieldKey": "fqty",
  "SourceControlFieldKey": "fdeliveryqty",
  "RelationFieldKey": "fsourceentryid",
  "RealtionFormIdFieldKey": "fsourceformid",
  "ActiveEntityKey": "fentry",
  "FilterString": "fqty+freturnqty>fdeliveryqty and fstatus='E' and (fisoutspot!='1' or fisoutspot='1' and fdeliverymode!='1') and fenablenotice='1' and (fclosestatus_e='0' or fclosestatus_e='2'  or fclosestatus_e=' ') ",
  "Message": "发货失败：<br>1、销售合同必须是已审核状态！<br>2、至少要有一行商品明细没有完全发货(数量-发货数量+退货数量>0)！<br>3、至少要有一行商品明细不是出现货且不是自提！4、至少有一行商品明细的关闭状态是正常或部分关闭状态！",
  //"FilterString": "fqty+freturnqty>fdeliveryqty and fstatus='E' and (fisoutspot!='1' or fisoutspot='1' and fdeliverymode!='1') and fenablenotice='1' and (fclosestatus_e='0' or fclosestatus_e='2') and fshipperagent=@currentorgid ",
  //"Message": "发货失败：<br>1、销售合同必须是已审核状态！<br>2、至少要有一行商品明细没有完全发货(数量-发货数量+退货数量>0)！<br>3、至少要有一行商品明细不是出现货且不是自提！4、至少有一行商品明细的关闭状态是正常或部分关闭状态！<br>5、商品明细中不存在发货经销商等于当前销售合同的经销商数据！",
  "FieldMappings": [
    {
      "Id": "fcustomerid",
      "Name": "客户",
      "MapType": 0,
      "SrcFieldId": "fcustomerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbilltype",
      "Name": "单据类型",
      "MapType": 1,
      "SrcFieldId": "'deliverynotice_type_01'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdate",
      "Name": "发货日期",
      "MapType": 1,
      "SrcFieldId": "@currentshortdate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockdeptid",
      "Name": "发货部门",
      "MapType": 1,
      "SrcFieldId": "''",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstaffid",
      "Name": "发货人",
      "MapType": 1,
      "SrcFieldId": "''",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsostaffid",
      "Name": "销售员",
      "MapType": 0,
      "SrcFieldId": "fstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsodeptid",
      "Name": "销售部门",
      "MapType": 0,
      "SrcFieldId": "fdeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkstaffid",
      "Name": "收货人",
      "MapType": 0,
      "SrcFieldId": "flinkstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "flinkmobile",
      "Name": "收货人电话",
      "MapType": 0,
      "SrcFieldId": "fphone",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "flinkaddress",
      "Name": "收货人地址",
      "MapType": 0,
      "SrcFieldId": "faddress",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "fprovince",
      "Name": "省",
      "MapType": 0,
      "SrcFieldId": "fprovince",
      "MapActionWhenGrouping": 0,
      "Order": 2
    },
    {
      "Id": "fcity",
      "Name": "市",
      "MapType": 0,
      "SrcFieldId": "fcity",
      "MapActionWhenGrouping": 0,
      "Order": 3
    },
    {
      "Id": "fregion",
      "Name": "区",
      "MapType": 0,
      "SrcFieldId": "fregion",
      "MapActionWhenGrouping": 0,
      "Order": 4
    },
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_order'",
      "MapActionWhenGrouping": 0,
      "Order": 6
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编码",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 7
    },
    {
      "Id": "fsourcebilltype",
      "Name": "源单业务类型",
      "MapType": 0,
      "SrcFieldId": "fbilltype.fname",
      "MapActionWhenGrouping": 0,
      "Order": 8
    },
    {
      "Id": "fdescription",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "flogisticsitems",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftotalgrossload",
      "Name": "总重量",
      "MapType": 1,
      "SrcFieldId": "0",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftotalcubeqty",
      "Name": "总体积",
      "MapType": 1,
      "SrcFieldId": "0",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftotalpackageqty",
      "Name": "总件数",
      "MapType": 1,
      "SrcFieldId": "0",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fisresellorder",
      "Name": "二级分销合同",
      "MapType": 0,
      "SrcFieldId": "fisresellorder",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //表体商品明细字段映射
    {
      "Id": "fmaterialid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fproductid",
      "MapActionWhenGrouping": 0,
      "Order": 15
    },
    {
      "Id": "fattrinfo",
      "Name": "辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 16
    },
    {
      "Id": "fcustomdesc",
      "Name": "定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdes_e",
      "MapActionWhenGrouping": 0,
      "Order": 17
    },
    {
      "Id": "funitid",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "fplanqty",
      "Name": "基本单位应发数量",
      "MapType": 1,
      "SrcFieldId": "fqty-fdeliveryqty+freturnqty",
      "MapActionWhenGrouping": 0,
      "Order": 19,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "fbizunitid",
      "Name": "销售单位",
      "MapType": 0,
      "SrcFieldId": "fbizunitid",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "fstockunitid",
      "Name": "库存单位",
      "MapType": 1,
      "SrcFieldId": "fproductid.fstockunitid",
      "MapActionWhenGrouping": 0,
      "Order": 21
    },
    {
      "Id": "fqty",
      "Name": "基本单位实发数量",
      "MapType": 1,
      "SrcFieldId": "fqty-fdeliveryqty+freturnqty",
      "MapActionWhenGrouping": 0,
      "Order": 19
    },
    {
      "Id": "fprice",
      "Name": "单价",
      "MapType": 0,
      "SrcFieldId": "fdealprice",
      "MapActionWhenGrouping": 0,
      "Order": 23,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "famount",
      "Name": "金额",
      "MapType": 1,
      "SrcFieldId": "(fqty-fdeliveryqty+freturnqty)*fdealprice",
      "MapActionWhenGrouping": 0,
      "Order": 24,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "fvolumeqty",
      "Name": "体积",
      "MapType": 1,
      "SrcFieldId": "fproductid.fvolume",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fvolume",
      "Name": "总体积",
      "MapType": 1,
      "SrcFieldId": "(fqty-fdeliveryqty+freturnqty)*fproductid.fvolume",
      "MapActionWhenGrouping": 0,
      "Order": 0,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "fgrossqty",
      "Name": "毛重",
      "MapType": 1,
      "SrcFieldId": "fproductid.fgrossload",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fgross",
      "Name": "总重",
      "MapType": 1,
      "SrcFieldId": "(fqty-fdeliveryqty+freturnqty)*fproductid.fgrossload",
      "MapActionWhenGrouping": 0,
      "Order": 0,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "fpacksize",
      "Name": "纸箱尺寸",
      "MapType": 1,
      "SrcFieldId": "fproductid.fpacksize",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fentrynote",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fdescription_e",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstatus",
      "Name": "库存状态",
      "MapType": 0,
      "SrcFieldId": "fstockstatus",
      "MapActionWhenGrouping": 0,
      "Order": 24
    },
    {
      "Id": "fmtono",
      "Name": "订单跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtono",
      "MapActionWhenGrouping": 0,
      "Order": 1000
    },
    {
      "Id": "fownertype",
      "Name": "货主类型",
      "MapType": 0,
      "SrcFieldId": "fownertype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownerid",
      "Name": "货主",
      "MapType": 0,
      "SrcFieldId": "fownerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forderqty",
      "Name": "销售订单数量",
      "MapType": 0,
      "SrcFieldId": "fqty",
      "MapActionWhenGrouping": 0,
      "Order": 25,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "fstorehouseid",
      "Name": "仓库",
      "MapType": 1,
      "SrcFieldId": "''",
      "MapActionWhenGrouping": 0,
      "Order": 26
    },
    {
      "Id": "fsoorderno",
      "Name": "销售订单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 25
    },
    {
      "Id": "fsoorderinterid",
      "Name": "销售订单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 26
    },
    {
      "Id": "fsoorderentryid",
      "Name": "销售订单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentry.id",
      "MapActionWhenGrouping": 0,
      "Order": 27
    },
    {
      "Id": "fsourceformid",
      "Name": "来源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_order'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcebillno",
      "Name": "来源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid_h",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid",
      "Name": "来源单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentry.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forderdate",
      "Name": "订单日期",
      "MapType": 0,
      "SrcFieldId": "forderdate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forderqty",
      "Name": "基本单位订单数量",
      "MapType": 0,
      "SrcFieldId": "fqty",
      "MapActionWhenGrouping": 0,
      "Order": 0,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "fmtrlimage",
      "Name": "图片",
      "MapType": 0,
      "SrcFieldId": "fmtrlimage",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }
  ],
  "BillGroups": [
    {
      "Id": "fbillno",
      "Order": 1
    }
  ],
  "FieldGroups": [
    {
      "Id": "fentry_fentryid",
      "Order": 1
    }
  ]
}