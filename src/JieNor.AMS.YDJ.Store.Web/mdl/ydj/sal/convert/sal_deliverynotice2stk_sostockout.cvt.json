{
  "Id": "sal_deliverynotice2stk_sostockout",
  "Number": "sal_deliverynotice2stk_sostockout",
  "Name": "销售发货通知下推销售出库单",
  "SourceFormId": "sal_deliverynotice",
  "TargetFormId": "stk_sostockout",
  "ActiveEntityKey": "fentity",
  "FilterString": "fqty>foutstockqty and fstatus='E'",
  "Message": "出库失败：<br>1、销售发货通知单必须是已审核状态！<br>2、至少要有一行商品明细没有完全出库(数量-出库数量>0)！",
  "FieldMappings": [

    //表头字段
    {
      "Id": "fcustomerid",
      "Name": "客户",
      "MapType": 0,
      "SrcFieldId": "fcustomerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fconsignee",
      "Name": "收货人",
      "MapType": 0,
      "SrcFieldId": "flinkstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "fsostaffid",
      "Name": "销售员",
      "MapType": 0,
      "SrcFieldId": "fsostaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsodeptid",
      "Name": "销售部门",
      "MapType": 0,
      "SrcFieldId": "fsodeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fphone",
      "Name": "联系电话",
      "MapType": 0,
      "SrcFieldId": "flinkmobile",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "faddress",
      "Name": "收货地址",
      "MapType": 0,
      "SrcFieldId": "flinkaddress",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "fprovince",
      "Name": "省",
      "MapType": 0,
      "SrcFieldId": "fprovince",
      "MapActionWhenGrouping": 0,
      "Order": 2
    },
    {
      "Id": "fcity",
      "Name": "市",
      "MapType": 0,
      "SrcFieldId": "fcity",
      "MapActionWhenGrouping": 0,
      "Order": 3
    },
    {
      "Id": "fregion",
      "Name": "区",
      "MapType": 0,
      "SrcFieldId": "fregion",
      "MapActionWhenGrouping": 0,
      "Order": 4
    },
    {
      "Id": "fdeliverywayid",
      "Name": "货运方式",
      "MapType": 0,
      "SrcFieldId": "fdeliverywayid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcarrierid",
      "Name": "承运公司",
      "MapType": 0,
      "SrcFieldId": "fcarrierid",
      "MapActionWhenGrouping": 0,
      "Order": 4
    },
    {
      "Id": "fshippingbillno",
      "Name": "运输单号",
      "MapType": 0,
      "SrcFieldId": "fshippingbillno",
      "MapActionWhenGrouping": 0,
      "Order": 4
    },
    {
      "Id": "fdescription",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fdescription",
      "MapActionWhenGrouping": 0,
      "Order": 4
    },
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'sal_deliverynotice'",
      "MapActionWhenGrouping": 0,
      "Order": 6
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 7
    },
    {
      "Id": "fdrivername",
      "Name": "司机姓名",
      "MapType": 0,
      "SrcFieldId": "fdrivername",
      "MapActionWhenGrouping": 0,
      "Order": 7
    },
    {
      "Id": "fisresellorder",
      "Name": "二级分销合同",
      "MapType": 0,
      "SrcFieldId": "fisresellorder",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },

    //出库明细字段
    {
      "Id": "fmaterialid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fmaterialid",
      "MapActionWhenGrouping": 0,
      "Order": 15
    },
    {
      "Id": "fattrinfo",
      "Name": "辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 16
    },
    {
      "Id": "fcustomdesc",
      "Name": "定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdesc",
      "MapActionWhenGrouping": 0,
      "Order": 17
    },
    {
      "Id": "funitid",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "fbizunitid",
      "Name": "销售单位",
      "MapType": 0,
      "SrcFieldId": "fbizunitid",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "fplanqty",
      "Name": "基本单位应发数量",
      "MapType": 1,
      "SrcFieldId": "fqty-foutstockqty",
      "MapActionWhenGrouping": 0,
      "Order": 19
    },
    {
      "Id": "fqty",
      "Name": "基本单位实发数量",
      "MapType": 1,
      "SrcFieldId": "fqty-foutstockqty",
      "MapActionWhenGrouping": 0,
      "Order": 19
    },
    {
      "Id": "fstockunitid",
      "Name": "库存单位",
      "MapType": 0,
      "SrcFieldId": "fstockunitid",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "fprice",
      "Name": "单价",
      "MapType": 0,
      "SrcFieldId": "fprice",
      "MapActionWhenGrouping": 0,
      "Order": 23
    },
    {
      "Id": "famount",
      "Name": "金额",
      "MapType": 1,
      "SrcFieldId": "(fqty-foutstockqty)*fprice",
      "MapActionWhenGrouping": 0,
      "Order": 24
    },
    {
      "Id": "fstorehouseid",
      "Name": "仓库",
      "MapType": 0,
      "SrcFieldId": "fstorehouseid",
      "MapActionWhenGrouping": 0,
      "Order": 25
    },
    {
      "Id": "fstorelocationid",
      "Name": "仓位",
      "MapType": 0,
      "SrcFieldId": "fstorelocationid",
      "MapActionWhenGrouping": 0,
      "Order": 26
    },
    {
      "Id": "fstockstatus",
      "Name": "库存状态",
      "MapType": 0,
      "SrcFieldId": "fstockstatus",
      "MapActionWhenGrouping": 0,
      "Order": 27
    },
    {
      "Id": "flotno",
      "Name": "批号",
      "MapType": 0,
      "SrcFieldId": "flotno",
      "MapActionWhenGrouping": 0,
      "Order": 28
    },
    {
      "Id": "fmtono",
      "Name": "物流跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtono",
      "MapActionWhenGrouping": 0,
      "Order": 29
    },
    {
      "Id": "fownertype",
      "Name": "货主类型",
      "MapType": 0,
      "SrcFieldId": "fownertype",
      "MapActionWhenGrouping": 0,
      "Order": 30
    },
    {
      "Id": "fownerid",
      "Name": "货主",
      "MapType": 0,
      "SrcFieldId": "fownerid",
      "MapActionWhenGrouping": 0,
      "Order": 35
    },
    {
      "Id": "fentrynote",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fentrynote",
      "MapActionWhenGrouping": 0,
      "Order": 50
    },
    {
      "Id": "forderqty",
      "Name": "销售订单数量",
      "MapType": 0,
      "SrcFieldId": "forderqty",
      "MapActionWhenGrouping": 0,
      "Order": 60
    },
    {
      "Id": "fsoorderno",
      "Name": "销售订单编号",
      "MapType": 0,
      "SrcFieldId": "fsoorderno",
      "MapActionWhenGrouping": 0,
      "Order": 60
    },
    {
      "Id": "fsoorderinterid",
      "Name": "销售订单内码",
      "MapType": 0,
      "SrcFieldId": "fsoorderinterid",
      "MapActionWhenGrouping": 0,
      "Order": 61
    },
    {
      "Id": "fsoorderentryid",
      "Name": "销售订单分录内码",
      "MapType": 0,
      "SrcFieldId": "fsoorderentryid",
      "MapActionWhenGrouping": 0,
      "Order": 62
    },
    {
      "Id": "fsourceformid",
      "Name": "来源单类型",
      "MapType": 1,
      "SrcFieldId": "'sal_deliverynotice'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcebillno",
      "Name": "来源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid_h",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid",
      "Name": "来源单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentity.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forderdate",
      "Name": "订单日期",
      "MapType": 0,
      "SrcFieldId": "fdate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forderqty",
      "Name": "基本单位订单数量",
      "MapType": 0,
      "SrcFieldId": "forderqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtrlimage",
      "Name": "图片",
      "MapType": 0,
      "SrcFieldId": "fmtrlimage",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }

  ],
  "BillGroups": [
    {
      "Id": "fbillno",
      "Order": 1
    }
  ],
  "FieldGroups": [
    {
      "Id": "fentity_fentryid",
      "Order": 1
    }
  ]
}