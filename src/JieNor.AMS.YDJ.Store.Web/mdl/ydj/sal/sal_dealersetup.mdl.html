<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="sal_dealersetup" el="5" basemodel="param_basetmpl" cn="财务管理参数">
    <div id="fbillhead" el="51" pk="fid" pn="fbillhead" cn="财务管理参数">

        <!--账户设置-->
        <table id="fentry" el="52" pk="fentryid" pn="fentry" cn="资金设置">
            <tr>
                <th el="122" ek="fentry" id="fpurpose" pn="fpurpose" cn="账户用途" cg="结算单账户类型" refid="bd_enum" dfld="fenumitem" width="130" visible="96" lock="-1" align="center"></th>
                <th el="100" ek="fentry" id="falias" pn="falias" cn="账户别名" width="150" visible="0"></th>
                <!--fisnegative字段已废弃，前端不显示即可，不允许删除已有的字段-->
                <th el="116" ek="fentry" id="fisnegative" pn="fisnegative" cn="允许为负" width="150" visible="0" checkmode="checkbox"></th>
                <th el="116" ek="fentry" id="fispayment" pn="fispayment" cn="可用于支付订单" width="120" visible="96" checkmode="checkbox"></th>
                <th el="116" ek="fentry" id="fisrecharge" pn="fisrecharge" cn="可收款" width="70" visible="96" checkmode="checkbox"></th>
                <th el="116" ek="fentry" id="fisbalance" pn="fisbalance" cn="余额结算无需确认" width="130" visible="0" checkmode="checkbox"></th>
                <th el="116" ek="fentry" id="fisbalanceinunconfirm" pn="fisbalanceinunconfirm" cn="账户可用金额考虑待确认金额" width="200" visible="0" checkmode="checkbox"></th>
                <th el="105" ek="fentry" id="fcredit" pn="fcredit" cn="信用额度" width="100" visible="0" lock="-1"></th>
            </tr>
        </table>

        <input el="116" type="checkbox" id="fissetup" ek="fbillhead" cn="允许经销商资料单独设定" checkmode="checkbox" />
        <table id="fcheckoutentry" el="52" pk="fentryid" pn="fcheckoutentry" cn="关账校验配置" kfks="ferrortypedescription">
            <tr>
                <!--<th el="100" ek="fcheckoutentry" id="ferrortypedescription" pn="ferrortypedescription" cn="错误类型描述" width="150" visible="-1"></th>-->
                <th el="122" ek="fcheckoutentry" id="ferrortypedescription" fn="ferrortypedescription" pn="ferrortypedescription" cn="错误类型描述" cg="关账校验规则" refid="bd_enum" dfld="fenumitem" visible="-1" lix="10"></th>
                <th el="116" ek="fcheckoutentry" id="fenable" pn="fenable" cn="启用" width="70" type="checkbox" visible="-1" lix="20" checkmode="checkbox"></th>
            </tr>
        </table>
    </div>
</body>
</html>