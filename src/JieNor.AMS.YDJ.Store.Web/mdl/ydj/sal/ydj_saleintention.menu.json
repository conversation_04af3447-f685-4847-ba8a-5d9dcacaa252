{
  "base": "/mdl/bill.menu.json",
  "common": [

    {
      "id": "tbPushSaleContract",
      "caption": "成单",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 1290,
      "parent": "droplist_more",
      "opcode": "push",
      "group": "standard",
      "param": "ruleId:'ydj_saleintention2ydj_order'"
    }
  ],
  "listmenu": [ ],
  "billmenu": [
    //收款，退款按钮组
    {
      "id": "tbReceipt",
      "caption": "收款",
      "visible": "true",
      "disabled": "false",
      "style": "menugroup",
      "order": 105,
      "parent": "",
      "opcode": "receipt",
      "param": "",
      "visibleext": 2113,
      "icon": "tbReceipt"
    },
    {
      "id": "tbRefund",
      "caption": "退款",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 106,
      "parent": "tbReceipt",
      "opcode": "Refund",
      "visibleext": 2113,
      "icon": "tbRefund"
    },
    {
      "id": "tbIncomeDisburse",
      "caption": "收支明细",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 107,
      "parent": "tbReceipt",
      "opcode": "IncomeDisburse"
    },
    //{
    //  "id": "tbPushSaleContract",
    //  "caption": "成单",
    //  "visible": "true",
    //  "disabled": "false",
    //  "style": "menu",
    //  "order": 111,
    //  "parent": "",
    //  "opcode": "SaleContract",
    //  "group": "standard",
    //  "visibleext": 2113,
    //  "icon": "dbSettle"
    //},
    {
      "id": "tbQueryInventory",
      "caption": "库存查询",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 105,
      "parent": "",
      "opcode": "queryinventory",
      "param": "listMode:'lookup',filterFieldMap:{'fmtono':'fmtono'}",
      "group": "standard",
      "entityKey": "fentity"
    },
    {
      "id": "tbSaleMember",
      "caption": "销售成员",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 130,
      "parent": "droplist_more",
      "opcode": "salemember",
      "group": "standard",
      "param": ""
    },
    {
      "id": "tbfollowerrecord",
      "caption": "跟进",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 140,
      "parent": "",
      "opcode": "followerrecord",
      "param": "",
      "group": "standard",
      "visibleext": 2112,
      "icon": "tbFollower"
    },
    {
      "id": "tbModify",
      "caption": "修改",
      "visible": "false",
      "disabled": "false",
      "style": "menu",
      "order": 150,
      "parent": "",
      "opcode": "modify",
      "group": "standard",
      "param": "",
      "visibleext": 2048,
      "icon": "tbModify"
    },
    {
      "id": "tbDesignScheme",
      "caption": "设计方案",
      "visible": "false",
      "disabled": "false",
      "style": "menu",
      "order": 150,
      "parent": "",
      "opcode": "Assigndesigner",
      "group": "standard",
      "param": "",
      "visibleext": 2048,
      "icon": "tbDesignScheme"
    },
    {
      "id": "tbReserveInventory",
      "caption": "预留",
      "visible": "true",
      "disabled": "false",
      "style": "menugroup",
      "order": 160,
      "parent": "",
      "opcode": "reserveinventory",
      //参数可以在js文件中编写好，再通过在线压缩工具进行JsMin压缩后复制到这里：https://tool.css-js.com/
      "param": "'dataChangeWarn':true,'dialog':{'formId':'stk_reservedialog','formParam':{'activeEntityKey':'fentity','fieldMappings':[{'id':'fmaterialid','name':'商品','mapType':0,'srcFieldId':'fmaterialid'},{'id':'fattrinfo','name':'辅助属性','mapType':0,'srcFieldId':'fattrinfo'},{'id':'fcustomdesc','name':'定制说明','mapType':0,'srcFieldId':'fcustomdes_e'},{'id':'funitid','name':'基本单位','mapType':0,'srcFieldId':'funitid'},{'id':'fbizunitid','name':'库存单位','mapType':0,'srcFieldId':'fbizunitid'},{'id':'fplanqty','name':'需求数量','mapType':1,'srcFieldId':'fqty'},{'id':'fqty','name':'计划预留数量','mapType':1,'srcFieldId':'fqty'},{'id':'fstorehouseid','name':'仓库','mapType':0,'srcFieldId':'fstorehouseid'},{'id':'fstorelocationid','name':'仓位','mapType':0,'srcFieldId':'fstorelocationid'},{'id':'fstockstatus','name':'库存状态','mapType':0,'srcFieldId':'fstockstatus'},{'id':'fmtono','name':'物流跟踪号','mapType':0,'srcFieldId':'fmtono'}]}}"
    },
    {
      "id": "tbManualRelease",
      "caption": "手动释放",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 170,
      "parent": "tbReserveInventory",
      "opcode": "manualrelease",
      "param": "'dataChangeWarn':true,'dialog':{'formId':'stk_reservereleasedialog'}",
      "group": "standard"
    },
    {
      "id": "tbChargeback",
      "caption": "退单",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 180,
      "parent": "",
      "opcode": "chargeback"
    }
  ]
}