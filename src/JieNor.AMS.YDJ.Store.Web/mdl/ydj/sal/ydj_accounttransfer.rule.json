{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
  "lockRules": [

    //业务状态为“已确认”时，所有字段锁定，否则不锁定
    {
      "id": "lock_all",
      "expression": "field:*|fbizstatus=='bizstatus_02'"
    },
    {
      "id": "unlock_all",
      "expression": "field:$*|fbizstatus=='bizstatus_01'"
    },

    //确认按钮的锁定与解锁
    {
      "id": "lock_confirm",
      "expression": "menu:tbConfirm|fbizstatus=='bizstatus_02'"
    },
    {
      "id": "unlock_confirm",
      "expression": "menu:$tbConfirm|id!='' and fbizstatus=='bizstatus_01'"
    },

    //收支明细按钮的锁定与解锁
    {
      "id": "lock_incomedisburse",
      "expression": "menu:tbIncomeDisburse|fbizstatus!='bizstatus_02'"
    },
    {
      "id": "unlock_incomedisburse",
      "expression": "menu:$tbIncomeDisburse|fbizstatus=='bizstatus_02'"
    }

  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [

  ]
}