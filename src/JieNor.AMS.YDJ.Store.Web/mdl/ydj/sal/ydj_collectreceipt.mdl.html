<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="ydj_collectreceipt" el="1" cn="其它应收单" basemodel="bill_basetmpl" ludt="ListTree" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_collectreceipt" pn="fbillhead" cn="其它应收单">
        <!--基本信息-->
        <input group="基本信息" el="108" ek="fbillhead" visible="1150" id="fbillno" fn="fbillno" cn="单据编号" lix="1" width="145" />
        <select lix="1" group="基本信息" el="123" ek="fbillhead" id="fbilltype" fn="fbilltype" pn="fbilltype" cn="单据类型" refid="bd_billtype" apipn="billType" visible="-1" must="1"></select>
        <input group="基本信息" el="100" ek="fbillhead" id="fsourceinterid" fn="fsourceinterid" pn="fsourceinterid" cn="源单Id" lix="1" width="145" visible="0" />
        <input lix="5" group="基本信息" el="112" type="date" id="fregistdate" fn="fregistdate" pn="fregistdate" ek="fbillhead" cn="业务日期" visible="-1" copy="0" defval="@currentshortdate" must="1" />
        <input lix="6" group="基本信息" el="150" ek="fbillhead" id="frelatecusid" fn="frelatecusid" pn="frelatecusid" ctlfk="frelatetype" cn="往来单位名称" width="100" visible="-1" must="1" />
        <select lix="10" group="基本信息" el="149" ek="fbillhead" id="frelatetype" fn="frelatetype" pn="frelatetype" dataviewname="v_bd_relatedata" cn="往来单位类型" visible="-1" must="1">
            <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
            <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
            <dataSourceDesc formId="ste_channel" filter="" caption="合作渠道"></dataSourceDesc>
            <dataSourceDesc formId="ydj_staff" filter="" caption="员工"></dataSourceDesc>
        </select>
        <input lix="6" group="基本信息" el="107" ek="fbillhead" id="frelatecusnumber" fn="frelatecusnumber" pn="frelatecusnumber" cn="往来单位编码" ctlfk="frelatecusid" dispfk="fnumber" width="100" visible="1150" />
        <input lix="14" group="基本信息" el="107" ek="fbillhead" visible="1150" id="fdeptnumber" fn="fdeptnumber" pn="fdeptnumber" cn="部门编码" ctlfk="ftrainingdept" dispfk="fnumber" />
        <input lix="15" group="基本信息" el="106" ek="fbillhead" visible="-1" id="ftrainingdept" fn="ftrainingdept" pn="ftrainingdept" cn="部门" refid="ydj_dept" defVal="@currentDeptId" />
        <input lix="20" group="基本信息" el="106" ek="fbillhead" visible="-1" id="frelatemanid" fn="frelatemanid" pn="frelatemanid" cn="员工" refid="ydj_staff" defVal="@currentStaffId" />
        <select lix="25" group="基本信息" el="122" ek="fbillhead" visible="-1" id="fcurrency" fn="fcurrency" pn="fcurrency" cn="币别" cg="币别" refid="bd_enum" dfld="fenumitem" must="1"></select>
        <input lix="30" group="基本信息" el="105" ek="fbillhead" visible="-1" id="fsumtaxamount" fn="fsumtaxamount" pn="fsumtaxamount" cn="总金额" lock="-1" />
        <input lix="35" group="基本信息" el="105" ek="fbillhead" id="fsettledamount" fn="fsettledamount" cn="已结算金额" visible="-1" copy="0" />
        <input lix="40" group="基本信息" el="105" ek="fbillhead" visible="-1" id="fmoney" fn="fmoney" pn="fmoney" cn="费用金额" />
        <input lix="70" group="基本信息" el="100" ek="fbillhead" visible="-1" id="fdatasource" fn="fdatasource" pn="fdatasource" cn="数据来源" />
        <input lix="500" group="基本信息" el="100" ek="fbillhead" visible="-1" id="fdescription" fn="fdescription" pn="fdescription" cn="备注" len="500" />
        <input lix="65" group="基本信息" el="100" ek="fbillhead" visible="-1" id="fsourcenumber" fn="fsourcenumber" pn="fsourcenumber" cn="数据来源单号" copy="0" />
        <input lix="500" group="基本信息" el="100" ek="fbillhead" visible="-1" id="fsyncreason" fn="fsyncreason" pn="fsyncreason" cn="同步原因" len="500" />

        <input group="基本信息" el="100" ek="fbillhead" visible="1124" id="fwithin" fn="fwithin" pn="fwithin" cn="手工单号" lix="8" canchange="true" copy="0" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fmybankid" fn="fmybankid" cn="银行账号" refid="ydj_banknum" lix="108" dfld="fbankname,fbanknum" copy="0" apipn="myBankId" />
        <input group="基本信息" el="105" ek="fbillhead" id="funsettleamount" fn="funsettleamount" cn="待结算金额" visible="1150" copy="0" />
        <input group="基本信息" el="105" ek="fbillhead" id="funconfirmamount" fn="funconfirmamount" cn="待确认金额" visible="1150" copy="0" />
        <input group="基本信息" el="106" ek="fbillhead" visible="1150" id="forderno" fn="forderno" pn="forderno" cn="关联合同编号" width="150" refid="ydj_order" />
        <input group="基本信息" el="116" ek="fbillhead" id="fisinvoic" fn="fisinvoic" pn="fisinvoic" visible="1150" cn="需开票"
               lock="0" copy="1" lix="0" notrace="true" ts="" defval="false" />
    </div>

    <!--费用项目明细-->
    <table id="fentry" el="52" pk="fentryid" tn="t_collectreceipt_exp" pn="fentry" cn="费用项目明细" kfks="fexpenseitem">
        <tr>
            <th el="106" lix="13" ek="fentry" id="fresultbrandid" fn="fresultbrandid" pn="fresultbrandid" cn="业绩品牌" refid="ydj_series" visible="-1" lock="-1"></th>
            <th lix="30" el="107" ek="fentry" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="1150" cn="商品编码" ctlfk="fmaterialid" dispfk="fnumber" sformid=""></th>
            <th lix="31" el="106" ek="fentry" id="fmaterialid" fn="fmaterialid" pn="fmaterialid" visible="1150" cn="商品" refid="ydj_product" sformid=""></th>
            <th lix="32" el="132" ek="fentry" id="fattrinfo" fn="fattrinfo" pn="fattrinfo" cn="辅助属性" ctlfk="fmaterialid" pricefk="" width="160" visible="1150"></th>
            <th lix="33" el="100" ek="fentry" len="2000" id="fcustomdes" fn="fcustomdes" pn="fcustomdes" cn="定制说明" width="160" visible="1150" xsslv="1"></th>
            <th lix="36" el="107" ek="fentry" id="fexpenseitemno" fn="fexpenseitemno" pn="fexpenseitemno" cn="费用项目编号" ctlfk="fexpenseitem" dispfk="fnumber" visible="-1" sformid="" must="1"></th>
            <th lix="36" el="106" ek="fentry" id="fexpenseitem" fn="fexpenseitem" pn="fexpenseitem" cn="费用项目" refid="ydj_expenseitem" visible="-1" dfld="ftype" sformid="" must="1"></th>
            <th lix="36" el="107" ek="fentry" id="fexpenseitemtype" fn="fexpenseitemtype" pn="fexpenseitemtype" cn="费用项目类型" ctlfk="fexpenseitem" dispfk="ftype" visible="0" sformid=""></th>
            <th lix="37" el="106" ek="fentry" id="fdept" fn="fdept" pn="fdept" cn="费用承担部门" refid="ydj_dept" visible="-1" sformid=""></th>
            <th el="152" ek="fentry" id="fexpensebilltype" fn="fexpensebilltype" pn="fexpensebilltype" cn="发票类型" visible="1150" vals="'0':'普通发票','1':'增值税发票','2':'机票','3':'火车票','4':'其他运输票','5':'其他'"></th>
            <th el="122" ek="fentry" id="ftaxrate" fn="ftaxrate" pn="ftaxrate" cn="税率" cg="税率" refid="bd_enum" dfld="fenumitem" defval="'0.03'" visible="1150"></th>
            <th el="102" ek="fentry" id="fnontaxamount" fn="fnontaxamount" pn="fnontaxamount" cn="不含税金额" format="0,000.00" visible="1150"></th>
            <th el="102" ek="fentry" id="ftaxamount" fn="ftaxamount" pn="ftaxamount" cn="税金额" format="0,000.00" visible="1150"></th>
            <th el="102" ek="fentry" id="totalamount" fn="totalamount" pn="totalamount" cn="金额" format="0,000.00" visible="1150"></th>
            <th el="102" ek="fentry" id="fqty" fn="fqty" pn="fqty" cn="数量" format="0,000.00" width="80" visible="1150" canchange="true" notrace="false"></th>
            <th el="104" ek="fentry" id="fprice" fn="fprice" pn="fprice" cn="单价" width="90" lock="0" format="0,000.00" visible="1150" canchange="true" notrace="false"></th>
            <th el="102" ek="fentry" id="nonloanamount" fn="nonloanamount" pn="nonloanamount" cn="未借款金额" format="0,000.00" visible="1150"></th>
            <th el="102" ek="fentry" id="fnontaxamountcurrency" fn="fnontaxamountcurrency" pn="fnontaxamountcurrency" cn="不含税金额本位币" format="0,000.00" visible="1150"></th>
            <th el="102" ek="fentry" id="ftaxamountcurrency" fn="ftaxamountcurrency" pn="ftaxamountcurrency" cn="税额本位币" format="0,000.00" visible="1150"></th>
            <th el="100" ek="fentry" id="fremark" fn="fremark" pn="fremark" cn="备注" len="500" visible="1150"></th>
            <th el="140" ek="fentry" id="fsourceformid" fn="fsourceformid" ts="" cn="源单类型" visible="1150" copy="0" lock="-1"></th>
            <th lix="69" el="100" ek="fentry" id="fsourcebillno" fn="fsourcebillno" ts="" cn="源单编号" visible="-1" copy="0" lock="-1"></th>
            <th lix="70" el="100" ek="fentry" id="fdockingbillno" fn="fdockingbillno" ts="" cn="对接源单编号" visible="-1"></th>
            <th el="100" ek="fentry" id="finvoiceno" fn="finvoiceno" pn="finvoiceno" cn="发票号" visible="1150"></th>
            <th el="113" ek="fentry" id="fcreatetime" type="datetime" fn="fcreatetime" pn="fcreatetime" cn="发票创建时间" visible="1150"></th>
            <th el="112" ek="fentry" id="finvoicedate" type="date" fn="finvoicedate" pn="finvoicedate" cn="发票过账日期" visible="1150"></th>
            <th el="108" ek="fentry" id="fsoorderinterid" fn="fsoorderinterid" pn="fsoorderinterid" visible="0" cn="销售订单内码"
                lock="-1" copy="0" lix="231" notrace="true" ts=""></th>
            <th el="108" ek="fentry" id="fsoorderentryid" fn="fsoorderentryid" pn="fsoorderentryid" visible="0" cn="销售订单分录内码"
                lock="-1" copy="0" lix="232" notrace="true" ts=""></th>
            <th id="fseltypeid" el="107" ek="fentry" fn="fseltypeid" ctlfk="fmaterialid" dispfk="fseltypeid" ts="" cn="型号" visible="1086" lix="460"></th>
        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存">
            <li el="17" sid="1002" cn="反写转单申请是否已经下推应收" data="{
                'sourceFormId':'ydj_transferorderapply',
                'sourceControlFieldKey':'fisalreadypushreceipt',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceinterid',
                'linkFormFieldKey':'',
                'linkFilterString':'',
                'writebackFieldKey':'fisalreadypushreceipt',
                'expression':'1',
                'writebackMode':3,
                'excessCondition':'',
                'excessMessage':''
                }"></li>
            <li id="financeclosedaccounts" el="11" vid="6000" cn="财务关账逻辑控制" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="submit" op="submit" opn="提交">
            <li id="financeclosedaccounts" el="11" vid="6000" cn="财务关账逻辑控制" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="unsubmit" op="unsubmit" opn="撤销" data="" permid="fw_unsubmit">
            <li id="financeclosedaccounts" el="11" vid="6000" cn="财务关账逻辑控制" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核" data="" permid="fw_audit">
            <li id="financeclosedaccounts" el="11" vid="6000" cn="财务关账逻辑控制" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核" data="" permid="fw_unaudit">
            <li id="financeclosedaccounts" el="11" vid="6000" cn="财务关账逻辑控制" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="cancel" op="cancel" opn="作废">
            <li id="financeclosedaccounts" el="11" vid="6000" cn="财务关账逻辑控制" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="uncancel" op="uncancel" opn="反作废">
            <li id="financeclosedaccounts" el="11" vid="6000" cn="财务关账逻辑控制" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除">
            <li el="17" sid="1002" cn="反写转单申请是否已经下推应收" data="{
                'sourceFormId':'ydj_transferorderapply',
                'sourceControlFieldKey':'fisalreadypushreceipt',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceinterid',
                'linkFormFieldKey':'',
                'linkFilterString':'',
                'writebackFieldKey':'fisalreadypushreceipt',
                'expression':'0',
                'writebackMode':3,
                'excessCondition':'',
                'excessMessage':''
                }"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="receipt" op="receipt" opn="收款" data="" permid="fw_receipt"></ul>
    </div>

    <!--表单所涉及的权限项定义-->
    <div id="permList">
        <ul el="12" id="fw_receipt" cn="收款"></ul>
    </div>
</body>
</html>