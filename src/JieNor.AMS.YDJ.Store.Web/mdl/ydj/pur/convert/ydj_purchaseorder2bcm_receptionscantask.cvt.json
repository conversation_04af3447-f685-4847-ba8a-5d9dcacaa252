{
	"Id": "ydj_purchaseorder2bcm_receptionscantask",
	"Number": "ydj_purchaseorder2bcm_receptionscantask",
	"Name": "采购订单生成收货扫描任务",
	"SourceFormId": "ydj_purchaseorder",
	"TargetFormId": "bcm_receptionscantask",
	"ActiveEntityKey": "fentity",
	"FilterString": "fstatus='E' and fsumpushreceiveqty<fbizqty and fclosestatus_e!='3' and fclosestatus_e!='4'",
	"Message": "生成收货扫描任务失败：<br>1、采购订单必须是已审核状态!<br>2、商品累计下推收货作业数量必须小于采购数量!<br>3、只能下推行关闭状态不为自动关闭 或 手工关闭的行!",
	"VisibleEx": 1,
	"FieldMappings": [
		{
			"Id": "ftask_type",
			"Name": "任务类型",
			"MapType": 1,
			"SrcFieldId": "'stk_postockin'",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "ftaskstatus",
			"Name": "任务状态",
			"MapType": 1,
			"SrcFieldId": "'ftaskstatus_01'",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "ftaskdate",
			"Name": "业务日期",
			"MapType": 1,
			"SrcFieldId": "@currentDate",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fsourcetype",
			"Name": "来源单据",
			"MapType": 1,
			"SrcFieldId": "'ydj_purchaseorder'",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fsourcenumber",
			"Name": "来源单据编号",
			"MapType": 0,
			"SrcFieldId": "fbillno",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		//表体商品明细字段映射
		{
			"Id": "fmaterialid",
			"Name": "商品",
			"MapType": 0,
			"SrcFieldId": "fmaterialid",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fcustomerid",
			"Name": "客户",
			"MapType": 0,
			"SrcFieldId": "fcustomerid",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fsoorderno",
			"Name": "销售合同编号",
			"MapType": 0,
			"SrcFieldId": "fsoorderno",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fhqderno",
			"Name": "总部合同号",
			"MapType": 0,
			"SrcFieldId": "fhqderno",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fcustomdesc",
			"Name": "定制说明",
			"MapType": 0,
			"SrcFieldId": "fcustomdes_e",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fattrinfo",
			"Name": "辅助属性",
			"MapType": 0,
			"SrcFieldId": "fattrinfo",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "funitid",
			"Name": "基本单位",
			"MapType": 0,
			"SrcFieldId": "funitid",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fqty",
			"Name": "基本单位数量",
			"MapType": 1,
			"SrcFieldId": "fbizqty-fsumpushreceiveqty",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fbizunitid",
			"Name": "采购单位",
			"MapType": 0,
			"SrcFieldId": "fbizunitid",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fwaitworkqty",
			"Name": "待作业数量",
			"MapType": 1,
			"SrcFieldId": "fbizqty-fsumpushreceiveqty",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fcurrworkqty",
			"Name": "本次作业数量",
			"MapType": 1,
			"SrcFieldId": "fbizqty-fsumpushreceiveqty",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fmtono",
			"Name": "物流跟踪号",
			"MapType": 0,
			"SrcFieldId": "fmtono",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fownertype",
			"Name": "货主类型",
			"MapType": 0,
			"SrcFieldId": "fownertype",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fownerid",
			"Name": "货主",
			"MapType": 0,
			"SrcFieldId": "fownerid",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fsuitegroupid",
			"Name": "套件组合号",
			"MapType": 0,
			"SrcFieldId": "fsuitcombnumber",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fcategorygroupid",
			"Name": "配件组合号",
			"MapType": 0,
			"SrcFieldId": "fpartscombnumber",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fsafagroupid",
			"Name": "沙发组合号",
			"MapType": 0,
			"SrcFieldId": "fsofacombnumber",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fsourceformid",
			"Name": "来源单据",
			"MapType": 1,
			"SrcFieldId": "'ydj_purchaseorder'",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fsourcebillno",
			"Name": "来源单据编号",
			"MapType": 0,
			"SrcFieldId": "fbillno",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fsourcefid",
			"Name": "来源单内码",
			"MapType": 0,
			"SrcFieldId": "fbillhead.id",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fsourceinterid",
			"Name": "来源单行号",
			"MapType": 0,
			"SrcFieldId": "fentity.FSeq",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fsourceentryid",
			"Name": "来源单行内码",
			"MapType": 0,
			"SrcFieldId": "fentity.id",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fsupplierid",
			"Name": "供应商",
			"MapType": 0,
			"SrcFieldId": "fsupplierid",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "flinkformid",
			"Name": "关联单据",
			"MapType": 1,
			"SrcFieldId": "'ydj_purchaseorder'",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "flinkbillno",
			"Name": "关联单据编号",
			"MapType": 0,
			"SrcFieldId": "fbillno",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "flinkrownumber",
			"Name": "关联单行号",
			"MapType": 0,
			"SrcFieldId": "fentity.FSeq",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "flinkrowinterid",
			"Name": "关联单行内码",
			"MapType": 0,
			"SrcFieldId": "fentity.id",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fprice",
			"Name": "成交单价",
			"MapType": 1,
			"SrcFieldId": "fdealprice",
			"MapActionWhenGrouping": 0,
			"Order": 0,
			"IgnoreChangeValidation": true
		},
		{
			"Id": "famount",
			"Name": "成交金额",
			"MapType": 1,
			"SrcFieldId": "(fqty-finstockqty+freturnqty)*fdealprice",
			"MapActionWhenGrouping": 0,
			"Order": 0,
			"IgnoreChangeValidation": true
		},
		{
			"Id": "fsupplierorderno",
			"Name": "供货方订单号",
			"MapType": 0,
			"SrcFieldId": "fsupplierorderno",
			"MapActionWhenGrouping": 0,
			"Order": 0
		}
	],
	"BillGroups": [],
	"FieldGroups": [
		{
			"Id": "fentity_fentryid",
			"Order": 1
		}
	]
}