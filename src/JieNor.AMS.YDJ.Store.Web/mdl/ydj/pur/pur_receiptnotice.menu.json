{"base": "/mdl/ydj/tpl/ydj_logisticnoticetpl.menu.json", "common": [{"id": "tbBizClose", "caption": "关闭", "visible": "true", "entityKey": "fentity"}, {"id": "tbUnClose", "caption": "反关闭", "visible": "true", "entityKey": "fentity"}], "listmenu": [], "billmenu": [{"id": "tbCreatePackage", "caption": "打码", "visible": "true", "disabled": "false", "style": "menu", "order": 130, "parent": "", "opcode": "push2packorder", "param": ""}, {"id": "tbStockIn", "caption": "入库", "visible": "true", "disabled": "false", "style": "menu", "order": 135, "parent": "", "opcode": "push2postockin", "param": ""}, {"id": "tbRegistFee", "caption": "费用", "visible": "true", "disabled": "false", "style": "menu", "order": 140, "parent": "", "opcode": "push", "group": "standard", "param": "ruleId:'pur_receiptnotice2ste_registfee'"}, {"id": "tbFeeDistribution", "caption": "费用分配", "visible": "true", "disabled": "false", "style": "menu", "order": 10, "parent": "tbRegistFee", "opcode": "feeDistribution", "param": ""}, {"id": "tbProductTagPrintSeting", "caption": "标签打印", "visible": "true", "disabled": "false", "style": "menu", "order": 10, "parent": "", "opcode": "ProductTagPrintSeting", "entityKey": "fentity", "param": "'dataChangeWarn':true"}]}