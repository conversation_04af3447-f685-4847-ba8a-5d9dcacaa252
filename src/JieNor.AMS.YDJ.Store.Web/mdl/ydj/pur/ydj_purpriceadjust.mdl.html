<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ydj_purpriceadjust" basemodel="bill_basetmpl" el="1" cn="采购调价单" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_purpriceadjust" pn="fbillhead" cn="采购调价单">
        <!--基本信息-->
        <select id="fstatus" el="122" refId="bd_enum" dfld="fenumitem" cg="数据状态" ek="fbillhead" fn="fstatus" pn="fstatus" cn="数据状态" visible="0" lix="27"></select>
        <input el="119" ek="fbillhead" id="fcreatedate" fn="fcreatedate" cn="创建日期" width="130" visible="0" copy="0" lix="251" />
        <input el="118" ek="fbillhead" id="fcreatorid" fn="fcreatorid" cn="创建人" width="130" visible="0" copy="0" lix="251" />
        <!--重写基类模型中的部分字段属性-->
        <input id="fbillno" el="108" cn="调价单号" visible="-1" lix="1" />
        <input id="fdescription" el="100" cn="描述" visible="-1" />

        <!--基本信息-->
        <input group="基本信息" el="106" ek="fbillhead" id="fsupplierid" fn="fsupplierid" refid="ydj_supplier" visible="-1" cn="供应商" lix="1" width="300" lock="-1" />
        <input group="基本信息" el="112" type="date" id="fadjustdate" ek="fbillhead" fn="fadjustdate" pn="fadjustdate" cn="调价日期" visible="-1" defval="@currentshortdate" lix="6" />
        <select group="基本信息" el="122" ek="fbillhead" id="fadjustmode" fn="fadjustmode" pn="fadjustmode" cn="调价方式" cg="调价方式" visible="1150" defval="'price_adjust_01'" refid="bd_enum" dfld="fenumitem" lix="4" lock="-1"></select>
        <input group="基本信息" el="100" type="text" id="fadjustreason" ek="fbillhead" fn="fadjustreason" visible="-1" cn="调价原因" lix="5" />
        <input group="基本信息" el="106" ek="fbillhead" id="fstaffid" fn="fstaffid" pn="fstaffid" visible="-1" cn="调价人" lix="9" ts="" refid="ydj_staff" defVal="@currentStaffId" />
        <input group="基本信息" el="106" ek="fbillhead" id="fdeptid" fn="fdeptid" pn="fdeptid" visible="-1" cn="调价部门" lix="9" ts="" refid="ydj_dept" defVal="@currentDeptId" />
        <input group="基本信息" el="112" type="date" id="fstartdate" ek="fbillhead" fn="fstartdate" pn="fstartdate" cn="生效日期" visible="-1" defval="@currentshortdate" lix="6" />
        <input group="基本信息" el="112" type="date" id="fexpiredate" ek="fbillhead" fn="fexpiredate" pn="fexpiredate" cn="失效日期" visible="-1" defval="'2099-01-01'" lix="7" />
        <!-- <select group="基本信息" el="122" ek="fbillhead" id="fauditstatus" fn="fauditstatus" pn="fauditstatus" cn="审核状态" cg="调价单审核状态" ts="" refid="bd_enum" dfld="fenumitem" defval="'price_audit01'" visible="-1" copy="0"></select> -->
        <!-- 调价规则 -->
        <select group="调价规则" el="122" ek="fbillhead" id="froundingrule" fn="froundingrule" pn="froundingrule" cn="取整规则" cg="取整规则" visible="1150" refid="bd_enum" dfld="fenumitem" lix="8"></select>
        <input group="调价规则" el="100" type="text" id="favoidnumber" ek="fbillhead" fn="favoidnumber" visible="0" cn="数字回避" />
        <select group="调价规则" el="122" ek="fbillhead" id="fpurprice_param" fn="fpurprice_param" pn="fpurprice_param" cn="调后采购价(选择对象)" cg="调后采购价(选择对象)" visible="0" refid="bd_enum" dfld="fenumitem" defval="'purprice_01'" lock="-1"></select>
        <input group="调价规则" el="100" ek="fbillhead" type="text" id="fpurprice_percent" fn="fpurprice_percent" visible="0" cn="采购价(调整百分百)" defval="100" />
        <select group="调价规则" el="122" ek="fbillhead" id="fsalprice_param" fn="fsalprice_param" pn="fsalprice_param" cn="调后零售价(选择对象)" cg="调后零售价(选择对象)" visible="0" refid="bd_enum" dfld="fenumitem" defval="'salprice_01'"></select>
        <input group="调价规则" el="100" ek="fbillhead" type="text" id="fsalprice_percent" fn="fsalprice_percent" visible="0" cn="零售价(调整百分百)" defval="100" />
        <select group="调价规则" el="122" ek="fbillhead" id="fdefinedprice_param" fn="fdefinedprice_param" pn="fdefinedprice_param" cn="调后经销价(选择对象)" cg="调后经销价(选择对象)" visible="0" refid="bd_enum" dfld="fenumitem" defval="'definedprice_01'"></select>
        <input group="调价规则" el="100" ek="fbillhead" type="text" id="fdefinedprice_percent" fn="fdefinedprice_percent" visible="0" cn="经销价(调整百分百)" defval="100" />


        <!-- 调价明细 -->
        <table id="fentry" el="52" pk="fentryid" tn="t_ydj_purpriceadjustentry" pn="fentry" cn="调价明细" kfks="fid_e,fproductid,fdefinedprice_adjust">
            <tr>
                <th el="100" ek="fentry" id="fid_e" fn="fid_e" cn="价目ID" visible="0"></th>
                <th el="106" ek="fentry" id="fproductid" fn="fproductid" pn="fproductid" cn="商品" refid="ydj_product" multsel="true" width="150" lock="-1" visible="96"></th>
                <th el="107" ek="fentry" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="-1" cn="商品编码" lix="3"
                    lock="-1" copy="1" notrace="true" ts="" ctlfk="fproductid" dispfk="fnumber" refvt="0"></th>
                <th el="107" ek="fentry" id="fmtrlmodel" fn="fmtrlmodel" visible="1150" cn="规格型号"
                    lock="-1" copy="1" notrace="true" ts="" ctlfk="fproductid" dispfk="fspecifica" refvt="0"></th>
                <th el="109" ek="fentry" id="funitid" fn="funitid" pn="funitid" cn="单位" ctlfk="fproductid" refid="ydj_unit" sformid="" width="65" lock="-1" visible="96"></th>
                <th el="132" ek="fentry" id="fattrinfo" fn="fattrinfo" pn="fattrinfo" cn="辅助属性" ctlfk="fproductid" pricefk="" width="140" visible="96"></th>
                <th el="104" ek="fentry" id="fpurprice" fn="fpurprice" cn="采购价" width="100" visible="96" format="0,000.00" lock="-1"></th>
                <th el="104" ek="fentry" id="fpurprice_adjust" fn="fpurprice_adjust" cn="调后采购价" width="100" visible="96" format="0,000.00"></th>
                <!--<th el="104" ek="fentry" id="fprice" fn="fprice" ts="" cn="含税单价" visible="96" width="130" format="0,000.00" lock="-1"></th>
            <th el="104" ek="fentry" id="fprice_adjust" fn="fprice_adjust" ts="" cn="调后含税单价" visible="96" width="130" format="0,000.00"></th>
            <th el="102" ek="fentry" id="ftaxrate" fn="ftaxrate" cn="税率" visible="96" lock="-1"></th>
            <th el="102" ek="fentry" id="ftaxrate_adjust" fn="ftaxrate_adjust" cn="调后税率" visible="96"></th>-->
                <th el="112" ek="fentry" type="date" id="fstartdate_e" ek="fbillhead" fn="fstartdate_e" pn="fstartdate_e" cn="生效日期" visible="96"></th>
                <th el="112" ek="fentry" type="date" id="fexpiredate_e" ek="fbillhead" fn="fexpiredate_e" pn="fexpiredate_e" cn="失效日期" visible="96"></th>
            </tr>
        </table>
    </div>
</body>
</html>
