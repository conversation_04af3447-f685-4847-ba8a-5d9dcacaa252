//{
//  "Id": "ydj_order2ydj_vist",
//  "Number": "ydj_order2ydj_vist",
//  "Name": "标准客户回访流程",
//  "SourceFormId": "ydj_order",
//  "TargetFormId": "ydj_vist",
//  "ActiveEntityKey": "fbillhead",
//  "FieldMappings": [
//    {
//      "Id": "fsourcetype",
//      "Name": "源单编号",
//      "MapType": 1,
//      "SrcFieldId": "'ydj_order'",
//      "MapActionWhenGrouping": 0,
//      "Order": 1
//    },
//    {
//      "Id": "fsourcenumber",
//      "Name": "源单编号",
//      "MapType": 0,
//      "SrcFieldId": "fbillno",
//      "MapActionWhenGrouping": 0,
//      "Order": 2
//    },
//    {
//      "Id": "fsourcetype",
//      "Name": "源单类型",
//      "MapType": 1,
//      "SrcFieldId": "'ydj_order'",
//      "MapActionWhenGrouping": 0,
//      "Order": 3
//    },
//    {
//      "Id": "fcustomerid",
//      "Name": "客户",
//      "MapType": 0,
//      "SrcFieldId": "fcustomerid",
//      "MapActionWhenGrouping": 0,
//      "Order": 4
//    },
//    {
//      "Id": "fcontacts",
//      "Name": "联系人",
//      "MapType": 0,
//      "SrcFieldId": "flinkstaffid",
//      "MapActionWhenGrouping": 0,
//      "Order": 5
//    },
//    {
//      "Id": "fphone",
//      "Name": "手机号",
//      "MapType": 0,
//      "SrcFieldId": "fphone",
//      "MapActionWhenGrouping": 0,
//      "Order": 6
//    },
//    {
//      "Id": "faddress",
//      "Name": "详细地址",
//      "MapType": 0,
//      "SrcFieldId": "faddress",
//      "MapActionWhenGrouping": 0,
//      "Order": 7
//    }
//  ],
//  "BillGroups": [
//    {
//      "Id": "fbillno",
//      "Order": 1
//    }
//  ],
//  "FieldGroups": [

//  ]
//}
