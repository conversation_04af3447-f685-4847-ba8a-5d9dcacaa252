//历史没用到，现在拿过来给oms的用
{
  "Id": "ydj_order2ydj_order",
  "Number": "ydj_order2ydj_order",
  "Name": "v6定制柜合同下推v6定制柜合同",
  "SourceFormId": "ydj_order",
  "TargetFormId": "ydj_order",
  "ActiveEntityKey": "fentry",
  "FieldMappings": [
    {
      "Id": "fbilltype",
      "Name": "单据类型",
      "MapType": 0,
      "SrcFieldId": "fbilltype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forderdate",
      "Name": "业务日期",
      "MapType": 0,
      "SrcFieldId": "forderdate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomerid",
      "Name": "客户",
      "MapType": 0,
      "SrcFieldId": "fcustomerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomercontactid",
      "Name": "客户联系人",
      "MapType": 0,
      "SrcFieldId": "fcustomercontactid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprovince",
      "Name": "省",
      "MapType": 0,
      "SrcFieldId": "fprovince",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcity",
      "Name": "市",
      "MapType": 0,
      "SrcFieldId": "fcity",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fregion",
      "Name": "区",
      "MapType": 0,
      "SrcFieldId": "fregion",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "faddress",
      "Name": "收货地址",
      "MapType": 0,
      "SrcFieldId": "faddress",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstaffid",
      "Name": "导购员",
      "MapType": 0,
      "SrcFieldId": "fstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdeptid",
      "Name": "销售部门",
      "MapType": 0,
      "SrcFieldId": "fdeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstore",
      "Name": "门店",
      "MapType": 0,
      "SrcFieldId": "fstore",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkstaffid",
      "Name": "收货人",
      "MapType": 0,
      "SrcFieldId": "flinkstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fphone",
      "Name": "手机号",
      "MapType": 0,
      "SrcFieldId": "fphone",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcommercebillno",
      "Name": "电商订单号",
      "MapType": 0,
      "SrcFieldId": "fcommercebillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flogisticsitems",
      "Name": "跟单备注",
      "MapType": 0,
      "SrcFieldId": "flogisticsitems",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmallorderno",
      "Name": "商场合同号",
      "MapType": 0,
      "SrcFieldId": "fmallorderno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_order'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmemberdesc",
      "Name": "未注册会员原因",
      "MapType": 0,
      "SrcFieldId": "fmemberdesc",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbuildingid",
      "Name": "楼盘",
      "MapType": 0,
      "SrcFieldId": "fbuildingid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "factivityid",
      "Name": "活动",
      "MapType": 0,
      "SrcFieldId": "factivityid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkindenttype",
      "Name": "关联订单类型",
      "MapType": 0,
      "SrcFieldId": "findenttype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fswjdesignerid",
      "Name": "设计师",
      "MapType": 0,
      "SrcFieldId": "fswjdesignerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "frelevanceorderno",
      "Name": "关联合同号",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fproductid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fproductid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprice",
      "Name": "零售价",
      "MapType": 1,
      "SrcFieldId": "'999'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "famount",
      "Name": "金额",
      "MapType": 1,
      "SrcFieldId": "'999'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdealprice",
      "Name": "成交单价",
      "MapType": 1,
      "SrcFieldId": "'999'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdealamount_e",
      "Name": "成交金额",
      "MapType": 1,
      "SrcFieldId": "'999'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfo",
      "Name": "辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fqty",
      "Name": "销售数量",
      "MapType": 0,
      "SrcFieldId": "fqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizqty",
      "Name": "销售数量",
      "MapType": 0,
      "SrcFieldId": "fbizqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "funitid",
      "Name": "销售单位",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizunitid",
      "Name": "销售单位",
      "MapType": 0,
      "SrcFieldId": "fbizunitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsfactorybillno",
      "Name": "原工厂订单号",
      "MapType": 0,
      "SrcFieldId": "ffactorybillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsfactorybillid",
      "Name": "原工厂订单ID",
      "MapType": 0,
      "SrcFieldId": "ffactorybillid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fresultbrandid",
      "Name": "业绩品牌",
      "MapType": 0,
      "SrcFieldId": "fresultbrandid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid_e",
      "Name": "源单明细ID",
      "MapType": 0,
      "SrcFieldId": "fentry.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenumber_e",
      "Name": "源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsomsbillno",
      "Name": "原定制OMS单号",
      "MapType": 0,
      "SrcFieldId": "fomsbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcetype_e",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_order'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fclosestatus_e",
      "Name": "行关闭状态",
      "MapType": 1,
      "SrcFieldId": "'0'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }
  ],
  "BillGroups": [
    {
      "Id": "fbillno",
      "Order": 1
    }
  ],
  "FieldGroups": [

  ]
}
