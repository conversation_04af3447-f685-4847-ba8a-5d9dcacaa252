{
  "Id": "pur_reqorder2ydj_purchaseorder",
  "Number": "pur_reqorder2ydj_purchaseorder",
  "Name": "采购申请单生成采购订单",
  "SourceFormId": "pur_reqorder",
  "TargetFormId": "ydj_purchaseorder",
  "ActiveEntityKey": "fentity",
  "FilterString": "fstatus='E' and fqty>(select isnull(sum(pe.fqty),0) from t_ydj_poorderentry pe inner join t_ydj_purchaseorder po on po.fid=pe.fid where po.fcancelstatus='0' and pe.fsourceentryid=t1.fentryid and pe.fsourceformid='pur_reqorder' and po.fmainorgid=t0.fmainorgid)",
  "ControlFieldKey": "fqty",
  "SourceControlFieldKey": "forderqty",
  "RelationFieldKey": "fsourceentryid_e",
  "RealtionFormIdFieldKey": "fsourcetype",
  "Message": "采购失败：\r\n1、采购申请单必须是已审核状态！\r\n2、至少要有一行商品明细商品数量大于已订购数量！",
  "FieldMappings": [
    {
      "Id": "fpostaffid",
      "Name": "采购员",
      "MapType": 0,
      "SrcFieldId": "fpostaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpodeptid",
      "Name": "采购部门",
      "MapType": 0,
      "SrcFieldId": "fpostdeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsupplierid",
      "Name": "供应商",
      "MapType": 0,
      "SrcFieldId": "fsupplierid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsupplieraddr",
      "Name": "供方地址",
      "MapType": 0,
      "SrcFieldId": "fsupplierid.faddress",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'pur_reqorder'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },

    //商品明细
    {
      "Id": "fsourceentryid_e",
      "Name": "来源单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentity.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcebillno_e",
      "Name": "来源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid_e",
      "Name": "来源单分录内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceformid_e",
      "Name": "来源单类型",
      "MapType": 1,
      "SrcFieldId": "'pur_reqorder'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtono",
      "Name": "物流跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtono",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmaterialid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fmaterialid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdistrate",
      "Name": "折扣",
      "MapType": 1,
      "SrcFieldId": "10",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },

    {
      "Id": "fmulfile",
      "Name": "附件",
      "MapType": 0,
      "SrcFieldId": "fmulfile",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },

    {
      "Id": "fcustomdes_e",
      "Name": "定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdesc",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "funitid",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizunitid",
      "Name": "采购单位",
      "MapType": 0,
      "SrcFieldId": "fbizunitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfo",
      "Name": "辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownertype",
      "Name": "货主类型",
      "MapType": 0,
      "SrcFieldId": "fownertype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownerid",
      "Name": "货主",
      "MapType": 0,
      "SrcFieldId": "fownerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsalprice",
      "Name": "销售单价",
      "MapType": 0,
      "SrcFieldId": "fsalprice",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fqty",
      "Name": "基本单位数量",
      "MapType": 1,
      "SrcFieldId": "fqty-forderqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdistrate",
      "Name": "折扣",
      "MapType": 1,
      "SrcFieldId": "10",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fnote",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fnote",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtrlimage",
      "Name": "图片",
      "MapType": 0,
      "SrcFieldId": "fmtrlimage",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdemanddate",
      "Name": "需求日期",
      "MapType": 0,
      "SrcFieldId": "fdemanddate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsoorderno",
      "Name": "销售合同编号",
      "MapType": 0,
      "SrcFieldId": "fsourcebillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsoorderinterid",
      "Name": "销售合同内码",
      "MapType": 0,
      "SrcFieldId": "fsourceinterid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsoorderentryid",
      "Name": "销售合同分录内码",
      "MapType": 0,
      "SrcFieldId": "fsourceentryid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }
  ],
  "BillGroups": [
    {
      "Id": "fsupplierid",
      "Name": "供应商",
      "Order": 0
    }
  ],
  "FieldGroups": [
    {
      "Id": "fentity_fentryid",
      "Order": 1
    }
  ]
}