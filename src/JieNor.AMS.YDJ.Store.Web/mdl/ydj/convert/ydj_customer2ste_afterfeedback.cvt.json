{
  "Id": "ydj_customer2ste_afterfeedback",
  "Number": "ydj_customer2ste_afterfeedback",
  "Name": "客户转售后反馈",
  "SourceFormId": "ydj_customer",
  "TargetFormId": "ste_afterfeedback",
  "ActiveEntityKey": "fbillhead",
  "Visible": false,
  "FieldMappings": [
    //{
    //  "Id": "fagentid",
    //  "Name": "经销商",
    //  "MapType": 1,
    //  "SrcFieldId": "@currentOrgId",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_customer'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编码",
      "MapType": 0,
      "SrcFieldId": "fnumber",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdate",
      "Name": "反馈时间",
      "MapType": 1,
      "SrcFieldId": "@currentDate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomerid",
      "Name": "客户",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprovince",
      "Name": "省",
      "MapType": 0,
      "SrcFieldId": "fprovince",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcity",
      "Name": "市",
      "MapType": 0,
      "SrcFieldId": "fcity",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fregion",
      "Name": "区",
      "MapType": 0,
      "SrcFieldId": "fregion",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkstaffid",
      "Name": "联系人",
      "MapType": 0,
      "SrcFieldId": "fcontacts",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkmobile",
      "Name": "联系电话",
      "MapType": 0,
      "SrcFieldId": "fphone",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkaddress",
      "Name": "联系地址",
      "MapType": 0,
      "SrcFieldId": "faddress",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }
  ],
  "BillGroups": [
    {
      "Id": "fbillno",
      "Order": 1
    }
  ],
  "FieldGroups": [

  ]
}