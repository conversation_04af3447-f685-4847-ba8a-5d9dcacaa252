{
  "Id": "ydj_saleintention2ydj_order",
  "Number": "ydj_saleintention2ydj_order",
  "Name": "销售意向单生成销售合同单流程",
  "SourceFormId": "ydj_saleintention",
  "TargetFormId": "ydj_order",
  "ActiveEntityKey": "fentity",
  //"FilterString": "fstatus='E' and fispushorder='0' and (fcollectamount=0 or fcollectamount>0 and fcollectedamount>0)",
  "FilterString": "fstatus='E' and (fcollectamount=0 or fcollectamount>0 and fcollectedamount>0)",
  "Message": "成单失败：\r\n1、销售意向必须是已审核状态！\r\n2、销售意向必须是未成单！\r\n3、销售意向必须收取订金（当应收订金>0时）！",
  "VisibleEx": 1,
  "FieldMappings": [
    {
      "Id": "ftype",
      "Name": "业务类型",
      "MapType": 0,
      "SrcFieldId": "ftype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_saleintention'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbuildingid",
      "Name": "楼盘",
      "MapType": 0,
      "SrcFieldId": "fbuildingid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdeliverydate",
      "Name": "交货日期",
      "MapType": 0,
      "SrcFieldId": "fpickdate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomerid",
      "Name": "客户",
      "MapType": 0,
      "SrcFieldId": "fcustomerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkstaffid",
      "Name": "收货人",
      "MapType": 0,
      "SrcFieldId": "flinkstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fphone",
      "Name": "手机号",
      "MapType": 0,
      "SrcFieldId": "fphone_e",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprovince",
      "Name": "省",
      "MapType": 0,
      "SrcFieldId": "fprovince",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcity",
      "Name": "市",
      "MapType": 0,
      "SrcFieldId": "fcity",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fregion",
      "Name": "区",
      "MapType": 0,
      "SrcFieldId": "fregion",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "faddress",
      "Name": "详细地址",
      "MapType": 0,
      "SrcFieldId": "faddress_e",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdeptid",
      "Name": "所属门店",
      "MapType": 0,
      "SrcFieldId": "fdeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstaffid",
      "Name": "导购员",
      "MapType": 0,
      "SrcFieldId": "fstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "finnercustomerid",
      "Name": "加盟商",
      "MapType": 0,
      "SrcFieldId": "finnercustomerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstylistid",
      "Name": "设计师",
      "MapType": 0,
      "SrcFieldId": "fstylistid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmallorderno",
      "Name": "商场合同号",
      "MapType": 0,
      "SrcFieldId": "fmallorderno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fproductid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fmaterialid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //{
    //  "Id": "fdoorderstatus",
    //  "Name": "成单状态",
    //  "MapType": 0,
    //  "SrcFieldId": "fdoorderstatus",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    {
      "Id": "fsourcetype_e",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_saleintention'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenumber_e",
      "Name": "源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "funitid",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizunitid",
      "Name": "销售单位",
      "MapType": 0,
      "SrcFieldId": "fbizunitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfo",
      "Name": "辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fqty",
      "Name": "基本单位数量",
      "MapType": 0,
      "SrcFieldId": "fqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizqty",
      "Name": "销售单位数量",
      "MapType": 0,
      "SrcFieldId": "fbizqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprice",
      "Name": "单价",
      "MapType": 0,
      "SrcFieldId": "fprice",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "famount",
      "Name": "金额",
      "MapType": 0,
      "SrcFieldId": "famount",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcollectamount",
      "Name": "应收定金",
      "MapType": 0,
      "SrcFieldId": "fcollectamount",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcollectedamount",
      "Name": "已收定金",
      "MapType": 0,
      "SrcFieldId": "fcollectedamount",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //兼容协同销售 和 非协同销售 下推合同时的字段携带问题
    //因为协同销售用的是“已结算金额”字段，非协同销售用的是“已收定金”字段，这两个字段目前只会有一个字段有值
    //{
    //  "Id": "freceivable",
    //  "Name": "已收",
    //  "MapType": 1,
    //  "SrcFieldId": "freceivedamount+fconfirmedamount",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fsumreceivable",
    //  "Name": "收款金额",
    //  "MapType": 1,
    //  "SrcFieldId": "freceivedamount+fconfirmedamount",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    {
      "Id": "fcustomdes_e",
      "Name": "定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdes_e",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fchannel",
      "Name": "外部带单",
      "MapType": 0,
      "SrcFieldId": "fchannel",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcommercebillno",
      "Name": "电商订单号",
      "MapType": 0,
      "SrcFieldId": "fcommercebillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdesignscheme",
      "Name": "设计方案",
      "MapType": 0,
      "SrcFieldId": "fdesignscheme",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fscalerecord",
      "Name": "量尺记录",
      "MapType": 0,
      "SrcFieldId": "fscalerecord",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdescription_e",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fnote",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fisoutspot",
      "Name": "出现货",
      "MapType": 0,
      "SrcFieldId": "fisoutspot",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdistrate",
      "Name": "折扣",
      "MapType": 0,
      "SrcFieldId": "fdistrate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdistamount_e",
      "Name": "折扣额",
      "MapType": 0,
      "SrcFieldId": "fdistamount_e",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdealprice",
      "Name": "成交单价",
      "MapType": 0,
      "SrcFieldId": "fdealprice",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdealamount_e",
      "Name": "成交金额",
      "MapType": 0,
      "SrcFieldId": "fdealamount_e",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstatus",
      "Name": "库存状态",
      "MapType": 0,
      "SrcFieldId": "fstockstatus",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fspace",
      "Name": "空间",
      "MapType": 0,
      "SrcFieldId": "fspace",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorehouseid",
      "Name": "仓库",
      "MapType": 0,
      "SrcFieldId": "fstorehouseid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorelocationid",
      "Name": "仓位",
      "MapType": 0,
      "SrcFieldId": "fstorelocationid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownertype",
      "Name": "货主类型",
      "MapType": 0,
      "SrcFieldId": "fownertype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownerid",
      "Name": "货主",
      "MapType": 0,
      "SrcFieldId": "fownerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid_e",
      "Name": "源单明细内码",
      "MapType": 0,
      "SrcFieldId": "fentity.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsuitid",
      "Name": "所属套件",
      "MapType": 0,
      "SrcFieldId": "fsuitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fissplit",
      "Name": "不可拆卖",
      "MapType": 0,
      "SrcFieldId": "fissplit",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fisgiveaway",
      "Name": "赠品",
      "MapType": 0,
      "SrcFieldId": "fisgiveaway",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustom",
      "Name": "允许定制",
      "MapType": 0,
      "SrcFieldId": "fcustom",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtrlimage",
      "Name": "图片",
      "MapType": 0,
      "SrcFieldId": "fmtrlimage",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fisclearstock",
      "Name": "清库存",
      "MapType": 0,
      "SrcFieldId": "fisclearstock",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fismain",
      "Name": "主要负责",
      "MapType": 0,
      "SrcFieldId": "fismain",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdutyid",
      "Name": "销售员",
      "MapType": 0,
      "SrcFieldId": "fdutyid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fratio",
      "Name": "比例%",
      "MapType": 0,
      "SrcFieldId": "fratio",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "famount_ed",
      "Name": "销售员金额",
      "MapType": 0,
      "SrcFieldId": "famount_ed",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdescription_ed",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fdescription_ed",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbrandid_e",
      "Name": "品牌",
      "MapType": 0,
      "SrcFieldId": "fbrandid_e",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdept",
      "Name": "门店(协同)",
      "MapType": 0,
      "SrcFieldId": "fdept",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstaff",
      "Name": "导购员(协同)",
      "MapType": 0,
      "SrcFieldId": "fstaff",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstylist",
      "Name": "设计师(协同)",
      "MapType": 0,
      "SrcFieldId": "fstylist",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fterminalcustomer",
      "Name": "终端客户(协同)",
      "MapType": 0,
      "SrcFieldId": "fterminalcustomer",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcoophone",
      "Name": "手机号(协同)",
      "MapType": 0,
      "SrcFieldId": "fphone",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprovincecityregion",
      "Name": "省市区(协同)",
      "MapType": 0,
      "SrcFieldId": "fprovincecityregion",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcooaddress",
      "Name": "详细地址(协同)",
      "MapType": 0,
      "SrcFieldId": "faddress",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdescription",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fdescription",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdeliverymode",
      "Name": "提货方式",
      "MapType": 0,
      "SrcFieldId": "fdeliverymode",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }
  ],
  "BillGroups": [
    {
      "Id": "fcustomerid",
      "Order": 1
    },
    {
      "Id": "fchannel",
      "Order": 2
    },
    {
      "Id": "fdeptid",
      "Order": 3
    },
    {
      "Id": "fstaffid",
      "Order": 4
    }
  ],
  "FieldGroups": [
    {
      "Id": "fentity_fentryid",
      "Order": 1
    }
  ]
}

