<!--盘点单-->
<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="stk_inventoryverify" basemodel="base_stkbilltmpl" el="1" cn="盘点单" ubl="1">

    <div id="fbillhead" el="51" pk="fid" tn="t_stk_invverify" pn="fbillhead" cn="盘点单">
        <input group="基本信息" el="108" id="fbillno" cn="单据编号" visible="-1" width="145" lix="1" />
        <input group="基本信息" el="123" id="fbilltype" cn="单据类型" visible="-1" width="120" lix="3" must="1" />
        <input group="基本信息" el="112" id="fdate" cn="盘点日期" visible="-1" width="105" lix="5" must="1" />
        <input lix="1" group="基本信息" el="112" id="fplandate" fn="fplandate" cn="计划盘点日期" defval="@currentshortdate" visible="-1" uaul="true" />
        <input group="基本信息" el="106" ek="fbillhead" id="finventbase" fn="finventbase" pn="finventbase" cn="盘点方案" refid="stk_inventorybase" visible="-1" lix="4" width="150" lock="-1" />

        <input group="基本信息" el="152" ek="fbillhead" id="fscale" fn="fscale" pn="fscale" cn="扫描状态" visible="1150" lock="-1" width="200" lix="19" vals="'0':'正常','1':'待盘点','2':'盘点完成'" defVal="'0'" copy="0" />

        <input group="基本信息" el="106" id="fstockstaffid" cn="盘点员" visible="-1" width="80" lix="7" must="0" />
        <input group="基本信息" el="106" id="fstockdeptid" cn="盘点部门" visible="-1" width="120" lix="9" must="0" />
        <input group="基本信息" el="100" id="fdescription" cn="备注" visible="-1" width="120" lix="11" />
        <input group="基本信息" el="116" ek="fbillhead" id="fenablebarcode" fn="fenablebarcode" pn="fenablebarcode" visible="1150" cn="启用条码作业" copy="0" lix="70" notrace="true" ts="" />

        <!--隐藏基类字段-->
        <input group="基本信息" el="100" id="fstockstaffphone" cn="仓管员联系方式" must="0" visible="0" />
        <input group="基本信息" el="100" id="fstockaddress" cn="仓管员地址" must="0" visible="0" />
        <input group="基本信息" el="100" id="fdeliverywayid" cn="运货方式" must="0" visible="0" />
        <input group="基本信息" el="140" id="fsourcetype" cn="源单类型" must="0" visible="0" />
        <input group="基本信息" el="141" id="fsourcenumber" cn="源单编号" must="0" visible="0" />

        <input group="基本信息" el="116" ek="fbillhead" id="fispdatask" fn="fispdatask" pn="fispdatask" visible="1150" cn="是否PDA作业" lock="-1" copy="0" lix="70" notrace="true" ts="" />
        <input group="基本信息" el="116" ek="fbillhead" id="fcreatescantask" fn="fcreatescantask" pn="fcreatescantask" visible="1150" cn="已生成PDA扫描任务" lock="-1" copy="0" lix="70" notrace="true" ts="" />

        <input group="基本信息" el="106" ek="fbillhead" visible="1150" id="fstore" fn="fstore" pn="fstore" cn="盘点门店" notrace="false" refid="bas_store" canchange="true" lix="110" defls="true" copy="0" />

        <input lix="120" el="152" ek="fbillhead" id="fzytype" fn="fzytype" pn="fzytype" visible="1150" cn="盘点类型"
               copy="0" notrace="true" ts="" vals="'1':'全盘','2':'部分盘点'" defval="''" lock="-1" />

        <input group="基本信息" el="113" type="datetime" id="fsubmithtime" ek="fbillhead" fn="fsubmithtime" pn="fsubmithtime" cn="提交SAP时间" visible="-1" lix="10" width="160" lock="-1" copy="0" />
        <input group="基本信息" el="152" ek="fbillhead" id="fchstatus" fn="fchstatus" pn="fchstatus" cn="协同SAP状态"
               vals="'1':'已提交总部','2':'已驳回','3':'已终审'" lock="-1" visible="-1" lix="40" must="0" copy="0" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fheadcontracttype" fn="fheadcontracttype" pn="fheadcontracttype" cn="SAP合同类型" lock="-1" ts="" lix="500" copy="0" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fheadquartno" fn="fheadquartno" pn="fheadquartno" cn="SAP合同号" lock="-1" ts="" lix="500" copy="0" />
        <input group="基本信息" el="113" type="datetime" id="fheadquartfrtime" ek="fbillhead" fn="fheadquartfrtime" pn="fheadquartfrtime" cn="SAP终审时间" visible="-1" lix="10" width="160" lock="-1" copy="0" />
        <input group="基本信息" el="100" ek="fbillhead" id="fheadquartsyncmessage" fn="fheadquartsyncmessage" pn="fheadquartsyncmessage" cn="SAP同步消息" visible="-1" copy="0" lix="40" lock="-1" len="1000" copy="0" />
    </div>

    <!--盘点明细-->
    <table id="fentity" el="52" pk="fentryid" tn="t_stk_invverifyentry" pn="fentity" cn="盘点明细" kfks="fmaterialid" allowqkfilter="true">
        <tr>
            <th lix="20" el="106" ek="fentity" id="fmaterialid" cn="商品" visible="1150" lock="0" copy="0" width="120" dfld="fselcategoryid,fispresetprop,fpackagtype"></th>
            <th lix="10" el="107" ek="fentity" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" cn="商品编码" ctlfk="fmaterialid" dispfk="fnumber" visible="1150" lock="0" copy="0" width="100"></th>
            <th lix="150" el="107" ek="fentity" id="fbrandid" fn="fbrandid" cn="品牌" visible="1150" ctlfk="fmaterialid" dispfk="fbrandid" sformid="" width="100" lock="0"></th>
            <th lix="170" el="107" ek="fentity" id="fseriesid" fn="fseriesid" cn="系列" visible="1150" ctlfk="fmaterialid" dispfk="fseriesid" sformid="" width="100" lock="0"></th>
            <th lix="171" el="107" ek="fentity" id="fsubseriesid" fn="fsubseriesid" cn="子系列" visible="1086" ctlfk="fmaterialid" dispfk="fsubseriesid" sformid="" width="100" lock="-1"></th>
            <th lix="30" el="107" ek="fentity" id="fmtrlmodel" cn="规格型号" visible="1150" lock="0" copy="0" width="120"></th>

            <th lix="40" el="132" ek="fentity" id="fattrinfo" cn="辅助属性" nstdfk="funstdtype" visible="1150" lock="0" copy="0" width="120"></th>

            <!--    <th lix="94" el="107" ek="fentity" id="fcustom" fn="fcustom" pn="fcustom" visible="1150" cn="允许定制" width="80" align="center" lock="0" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fcustom" refvt="116"></th>-->
            <th lix="180" el="107" ek="fentity" id="fcustom" fn="fcustom" pn="fcustom" visible="0" cn="允许定制" width="80" align="center" lock="0" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fcustom" refvt="116"></th>

            <th lix="160" el="109" ek="fentity" id="funitid" cn="基本单位" ctlfk="fmaterialid" visible="1150" lock="0" copy="0" width="80" lock="-1"></th>
            <th lix="50" el="109" ek="fentity" id="fstockunitid" cn="库存单位" ctlfk="fmaterialid" visible="1150" lock="0" copy="0" width="80" lock="-1"></th>

            <th lix="190" el="103" ek="fentity" id="fqty" cn="基本单位账存数量" ctlfk="funitid" visible="1150" lock="-1" copy="0" width="130" format="0,000.00"></th>
            <th lix="60" el="103" ek="fentity" id="fstockqty" cn="账存数量" ctlfk="fstockunitid" basqtyfk="fqty" lock="-1" copy="0" visible="1150" width="80" format="0,000.00"></th>

            <th lix="200" el="103" ek="fentity" id="fpdqty" fn="fpdqty" pn="fpdqty" cn="基本单位盘点数量" ctlfk="funitid" datascope="[0,999999999)" visible="1150" lock="-1" copy="0" width="130" format="0,000.00"></th>
            <th lix="70" el="103" ek="fentity" id="fbizpdqty" fn="fbizpdqty" pn="fbizpdqty" cn="盘点数量" ctlfk="fstockunitid" basqtyfk="fpdqty" datascope="[0,999999999)" visible="1150" copy="0" width="80" format="0,000.00"></th>
            <th lix="75" el="103" ek="fentity" id="fbizpackqty" fn="fbizpackqty" pn="fbizpackqty" cn="盘点包数" datascope="[0,999999999)" visible="1150" copy="0" width="80" format="0,000.00"></th>

            <th lix="210" el="103" ek="fentity" id="fpyqty" fn="fpyqty" pn="fpyqty" cn="基本单位盘盈数量" ctlfk="funitid" visible="1150" lock="-1" copy="0" width="130" format="0,000.00"></th>
            <th lix="80" el="103" ek="fentity" id="fbizpyqty" fn="fbizpyqty" pn="fbizpyqty" cn="盘盈数量" ctlfk="fstockunitid" basqtyfk="fpyqty" visible="1150" lock="-1" copy="0" width="80" format="0,000.00" notrace="false"></th>

            <th lix="220" el="103" ek="fentity" id="fpkqty" fn="fpkqty" pn="fpkqty" cn="基本单位盘亏数量" ctlfk="funitid" visible="1150" lock="-1" copy="0" width="130" format="0,000.00"></th>
            <th lix="90" el="103" ek="fentity" id="fbizpkqty" fn="fbizpkqty" pn="fbizpkqty" cn="盘亏数量" ctlfk="fstockunitid" basqtyfk="fpkqty" visible="1150" lock="-1" copy="0" width="80" format="0,000.00" notrace="false"></th>

            <th lix="230" el="102" ek="fentity" id="fpderror" fn="fpderror" pn="fpderror" cn="盘点误差%" visible="1150" lock="0" copy="0" width="90" format="0,000.00"></th>

            <th lix="100" el="106" ek="fentity" id="fstorehouseid" cn="仓库" visible="1150" lock="0" copy="0" width="120" filter="fname!='直发仓库'"></th>
            <th lix="110" el="153" ek="fentity" id="fstorelocationid" cn="仓位" visible="1150" lock="0" copy="0" width="120"></th>
            <th lix="120" el="106" ek="fentity" id="fstockstatus" cn="库存状态" visible="1150" lock="-1" copy="0" width="100"></th>
            <th lix="240" el="100" ek="fentity" id="flotno" cn="批号" visible="1150" lock="0" copy="0" width="100"></th>
            <th lix="241" el="112" ek="fentity" id="fhisentrydate" fn="fhisentrydate" pn="fhisentrydate" cn="历史入库日期" visible="1150" lock="0" copy="0"></th>
            <th lix="150" el="100" ek="fentity" id="fmtono" cn="物流跟踪号" visible="1150" lock="0" copy="0" width="130"></th>
            <th lix="250" el="149" ek="fentity" id="fownertype" cn="货主类型" visible="1150" lock="0" copy="0" width="120"></th>
            <th lix="160" el="150" ek="fentity" id="fownerid" cn="货主" visible="1150" lock="0" copy="0" width="120"></th>
            <th lix="260" el="100" ek="fentity" id="fentrynote" cn="备注" visible="1150" copy="0" width="160"></th>

            <!--慕思新增字段，标准定制非标定制-->
            <th lix="270" el="116" ek="fentity" visible="1150" id="funstdtype" fn="funstdtype" pn="funstdtype" cn="是否非标" ctlfk="fmaterialid" dispfk="funstdtype" width="90" copy="0" lock="0" canchange="true" refValueType="116"></th>
            <th lix="290" el="100" ek="fentity" visible="1150" id="fpartscombnumber" fn="fpartscombnumber" pn="fpartscombnumber" cn="配件组合号" lock="-1"></th>
            <th lix="300" el="107" ek="fentity" visible="1150" id="fselcategoryid" fn="fselcategoryid" pn="fselcategoryid" cn="选配类别" ctlfk="fmaterialid" dispfk="fselcategoryid" lock="-1"></th>
            <th lix="310" el="107" ek="fentity" id="fispresetprop" fn="fispresetprop" pn="fispresetprop" visible="0" cn="允许选配" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fispresetprop" refvt="116"></th>
            <th lix="320" el="116" ek="fentity" id="fdostandard" fn="fdostandard" pn="fdostandard" cn="已转标准品" visible="1150" lock="-1" defval="false"></th>

            <!--隐藏基类字段-->
            <th el="103" ek="fentity" id="fbizplanqty" visible="0"></th>
            <th el="103" ek="fentity" id="fplanqty" visible="0"></th>

            <th el="109" ek="fentity" id="fbizunitid" must="0" cn="业务单位" visible="0"></th>
            <th el="103" ek="fentity" id="fbizqty" must="0" cn="业务数量" visible="0"></th>

            <th el="140" ek="fentity" id="fsourceformid" must="0" cn="来源单类型" visible="0"></th>
            <th el="141" ek="fentity" id="fsourcebillno" must="0" cn="来源单编号" visible="0"></th>


            <th el="104" ek="fentity" id="fbizprice" fn="fbizprice" pn="fbizprice" cn="账存单价" lix="135" width="70" visible="1086" format="0,000.000000" dformat="0,000.00"></th>
            <th el="104" ek="fentity" id="fprice" fn="fprice" pn="fprice" cn="基本单位账存单价" lix="135" width="70" visible="1086" format="0,000.000000" dformat="0,000.00"></th>

            <th el="104" ek="fentity" id="fbizpdprice" fn="fbizpdprice" pn="fbizpdprice" cn="盘点单价" lix="135" width="70" visible="1150" format="0,000.000000" dformat="0,000.00"></th>
            <th el="104" ek="fentity" id="fpdprice" fn="fpdprice" pn="fpdprice" cn="基本单位盘点单价" lix="135" width="70" visible="1150" format="0,000.000000" dformat="0,000.00" uaul="true"></th>

            <th el="105" ek="fentity" id="famount" fn="famount" pn="famount" cn="账存金额" lix="136" width="90" visible="1086" format="0,000.00"></th>
            <th el="105" ek="fentity" id="fpdamount" fn="fpdamount" pn="fpdamount" cn="盘点金额" lix="136" width="90" visible="1086" format="0,000.00"></th>
            <th el="105" ek="fentity" id="fpyamount" fn="fpyamount" pn="fpyamount" cn="盘盈金额" lix="136" width="90" visible="1086" format="0,000.00"></th>
            <th el="105" ek="fentity" id="fpkamount" fn="fpkamount" pn="fpkamount" cn="盘亏金额" lix="136" width="90" visible="1086" format="0,000.00"></th>

            <th lix="137" el="103" ek="fentity" id="freserveqty" fn="freserveqty" pn="freserveqty" cn="基本单位预留量" ctlfk="funitid" visible="1150" lock="-1" copy="0" width="130" format="0,000.00"></th>
            <th lix="137" el="103" ek="fentity" id="fbizreserveqty" fn="fbizreserveqty" pn="fbizreserveqty" cn="预留量" ctlfk="fstockunitid" basqtyfk="freserveqty" visible="1150" lock="-1" copy="0" width="80" format="0,000.00"></th>
            <th el="105" ek="fentity" id="fbugunitprice" fn="fbugunitprice" pn="fbugunitprice" cn="采购单价(折前)" lix="136" width="90" visible="1086" format="0,000.00"></th>
            <th el="105" ek="fentity" id="fpkbuyamount" fn="fpkbuyamount" pn="fpkbuyamount" cn="盈亏采购总额" lix="136" width="90" visible="1086" format="0,000.00"></th>
            <th el="105" ek="fentity" id="funifyamount" fn="funifyamount" pn="funifyamount" cn="统一零售价" lix="136" width="90" visible="1086" format="0,000.00"></th>
            <th el="105" ek="fentity" id="fpksaleamount" fn="fpksaleamount" pn="fpksaleamount" cn="盈亏零售总额" lix="136" width="90" visible="1086" format="0,000.00"></th>

            <th el="105" ek="fentity" id="flamount" fn="flamount" pn="flamount" cn="零售价" lix="136" width="90" visible="0" format="0,000.00"></th>

            <th el="112" ek="fentity" id="findate" fn="findate" pn="findate" cn="入库日期" lix="300" width="90" visible="1086" must="2"></th>

            <!--单位成本，总成本-->
            <th group="盘点明细" el="105" ek="fentity" id="fcostprice" fn="fcostprice" pn="fcostprice" cn="单位成本(加权平均)" />
            <th group="盘点明细" el="105" ek="fentity" id="fcostamt" fn="fcostamt" pn="fcostamt" cn="总成本(加权平均)" />

            <th el="100" lix="299" ek="fentity" id="fvolumeunit" fn="fvolumeunit" pn="fvolumeunit" visible="1150" cn="体积单位" lock="0" must="0" uaul="true"></th>
            <th el="102" lix="300" ek="fentity" id="ftotalvolume" fn="ftotalvolume" pn="ftotalvolume" visible="1150" cn="总体积" lock="0" copy="0" ts="" format="0,000.000" width="150" uaul="true"></th>
            <th el="102" lix="301" ek="fentity" id="fsinglevolume" fn="fsinglevolume" pn="fsinglevolume" visible="1150" cn="单位体积" lock="0" copy="0" ts="" format="0,000.000" width="150" uaul="true"></th>
            <th lix="137" el="103" ek="fentity" id="fzyonwayqty" fn="fzyonwayqty" pn="fzyonwayqty" cn="在途数" visible="1150" lock="-1" copy="0" width="80" format="0,000.00"></th>

        </tr>
    </table>
    <!--条码扫描记录不需要在这里
        <table id="fscanentity" el="52" pk="fentryid" tn="t_stk_invverifyscanentity" pn="fscanentity" cn="条码扫描记录" >
            <tr>
                <th el="100" ek="fscanentity" id="fscanbarcodeid" fn="fscanbarcodeid" pn="fscanbarcodeid" cn="条码" visible="-1" lock="-1" copy="0"></th>
                <th el="100" ek="fscanentity" id="fscanbarcode" fn="fscanbarcode" pn="fscanbarcode" cn="条码(文本)" visible="-1" lock="-1" copy="0"></th>
                <th el="100" ek="fscanentity" id="fscanbilltype" fn="fscanbilltype" pn="fscanbilltype" cn="业务单据" visible="-1" lock="-1" copy="0"></th>
                <th el="100" ek="fscanentity" id="fscanbillno" fn="fscanbillno" pn="fscanbillno" cn="业务单据编码" visible="-1" lock="-1" copy="0"></th>
                <th el="113" ek="fscanentity" id="fscanoptime" fn="fscanoptime" pn="fscanoptime" cn="操作日期" defval="@currentlongdate" format="yyyy-MM-dd HH:mm:ss" visible="-1" lock="-1" copy="0"></th>
                <th el="106" ek="fscanentity" id="fscanopuserid" fn="fscanopuserid" cn="操作员" refid="ydj_staff" visible="-1" lock="-1" copy="0"></th>
                <th el="106" ek="fscanentity" id="fscanstorehouseid" fn="fscanstorehouseid" cn="仓库" refid="ydj_storehouse" visible="-1" lock="-1" copy="0" ></th>
                <th el="153" ek="fscanentity" id="fscanstorelocationid" fn="fscanstorelocationid" cn="仓位" ctlfk="fstorehouseid" luek="fentity" lunmfk="flocname" lunbfk="flocnumber" visible="-1" lock="-1" copy="0"></th>
                <th el="100" ek="fscanentity" id="fscanremark" fn="fscanremark" pn="fscanremark" cn="备注" visible="-1" lock="-1" copy="0"></th>
            </tr>
        </table>
    -->
    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" id="save" op="save" opn="保存">
            <li el="11" vid="510" cn="单据类型、盘点日期、盘点人、盘点部门不能同时为空"
                data="{'expr':'(fbilltype!=\'\' and fbilltype!=\' \') or (fdate!=\'\' and fdate!=\' \') or (fstockstaffid!=\'\' and fstockstaffid!=\' \') or (fstockdeptid!=\'\' and fstockdeptid!=\' \')','message':'单据类型、盘点日期、盘点人、盘点部门不能同时为空！'}"></li>
            <!--<li el="11" vid="3008" cn="允许选配的商品明细的辅助属性不能为空" data="{'productFieldKey':'fmaterialid'}"></li>-->
        </ul>

        <ul el="10" id="submit" op="submit" opn="提交">
            <li el="11" vid="510" cn="单据类型、盘点日期、盘点人、盘点部门不能同时为空"
                data="{'expr':'(fbilltype!=\'\' and fbilltype!=\' \') or (fdate!=\'\' and fdate!=\' \') or (fstockstaffid!=\'\' and fstockstaffid!=\' \') or (fstockdeptid!=\'\' and fstockdeptid!=\' \')','message':'单据类型、盘点日期、盘点人、盘点部门不能同时为空！'}"></li>
        </ul>

        <ul el="10" id="audit" op="audit" opn="审核">
            <li el="11" vid="510" cn="单据类型、盘点日期、盘点人、盘点部门不能同时为空"
                data="{'expr':'(fbilltype!=\'\' and fbilltype!=\' \') or (fdate!=\'\' and fdate!=\' \') or (fstockstaffid!=\'\' and fstockstaffid!=\' \') or (fstockdeptid!=\'\' and fstockdeptid!=\' \')','message':'单据类型、盘点日期、盘点人、盘点部门不能同时为空！'}"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核">
            <!--维度库存库存数量+=维度盘盈数量-->
            <li el="17" sid="2000" cn="审核时增加维度库存" data="{'updInvServiceId':'to','factor':1,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fpyqty',
                'stockQtyFieldKey':'fpyqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'fpyamount',
                'preCondition':'fstatus=\'c\' and fpyqty&gt;0'}"></li>
            <!--维度库存库存数量-=维度盘亏数量-->
            <li el="17" sid="2000" cn="审核时扣减维度库存" data="{'updInvServiceId':'from','factor':-1,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fpkqty',
                'stockQtyFieldKey':'fpkqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'fpkamount',
                'preCondition':'fstatus=\'c\' and fpkqty&gt;0'}"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <!--维度库存还原-->
            <li el="17" sid="2000" cn="反审核时还原维度库存" data="{'updInvServiceId':'to','factor':0,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fpyqty',
                'stockQtyFieldKey':'fpyqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'fpyamount',
                'preCondition':'fstatus=\'c\' and fpyqty&gt;0'}"></li>
            <!--维度库存还原-->
            <li el="17" sid="2000" cn="反审核时还原维度库存" data="{'updInvServiceId':'from','factor':0,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fpkqty',
                'stockQtyFieldKey':'fpkqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'fpkamount',
                'preCondition':'fstatus=\'c\' and fpkqty&gt;0'}"></li>
            <li el="11" vid="3016" cn="反审核增加校验如果对应的《盘点扫描任务》单据头【任务状态】=”已完成”时, 不允许反审核" data=""></li>
        </ul>

        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除">
            <!--维度库存还原-->
            <li el="17" sid="2000" cn="删除时还原维度库存" data="{'updInvServiceId':'to','factor':0,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fpyqty',
                'stockQtyFieldKey':'fpyqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'famount',
                'preCondition':'fstatus=\'c\' and fpyqty&gt;0'}"></li>
            <!--维度库存还原-->
            <li el="17" sid="2000" cn="删除时还原维度库存" data="{'updInvServiceId':'from','factor':0,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fpkqty',
                'stockQtyFieldKey':'fpkqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'famount',
                'preCondition':'fstatus=\'c\' and fpkqty&gt;0'}"></li>
        </ul>


        <ul el="10" id="setinistockbal" op="setinistockbal" opn="更新库存余额" permid="setinistockbal" ubl="1"></ul>
        <ul el="10" id="standardbustombatch" op="standardbustombatch" opn="转标准品" permid="" ubl="1"></ul>
        <ul el="10" id="receiveamount" op="receiveamount" opn="获取成本" permid="receiveamount" ubl="1"></ul>
    </div>

    <div id="permList">
        <ul el="12" id="setinistockbal" cn="更新库存余额"></ul>
        <ul el="12" id="receiveamount" cn="获取成本"></ul>
    </div>

</body>
</html>