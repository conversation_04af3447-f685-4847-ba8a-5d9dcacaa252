{
  //规则引擎基类
  "base": "",

  //定义表单锁定规则
  "lockRules": [

  ],

  //定义表单可见性规则
  "visibleRules": [
    //显示和隐藏首款比例
    {
      "id": "hide_enableownertype",
      "expression": "other:.fenableownertype|fenablepickser==false"
    },
    {
      "id": "show_enableownertype",
      "expression": "other:$.fenableownertype|fenablepickser==true"
    }
  ],

  //定义表单计算规则
  "calcRules": [
    { "expression": "fenableownertype=false|fenablepickser==false" }
  ]
}