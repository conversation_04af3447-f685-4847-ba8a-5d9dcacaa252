{
  "Id": "stk_inventoryverify2bcm_packorder",
  "Number": "stk_inventoryverify2bcm_packorder",
  "Name": "�̵㵥���ɰ�װ�嵥",
  "SourceFormId": "stk_inventoryverify",
  "TargetFormId": "bcm_packorder",
  "ActiveEntityKey": "fentity",
  "FilterString": "",
  "Message": "",
  "FieldMappings": [
    {
      "Id": "fpackbiztype",
      "Name": "ҵ������",
      "MapType": 1,
      "SrcFieldId": "'2'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdate",
      "Name": "ҵ������",
      "MapType": 1,
      "SrcFieldId": "@currentDate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdescription",
      "Name": "��ע",
      "MapType": 0,
      "SrcFieldId": "fdescription",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcetype",
      "Name": "Դ������",
      "MapType": 1,
      "SrcFieldId": "'stk_inventoryverify'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenumber",
      "Name": "Դ�����",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //�������װ��ϸ�ֶ�ӳ��
    {
      "Id": "fmaterialid",
      "Name": "��Ʒ",
      "MapType": 0,
      "SrcFieldId": "fmaterialid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomdesc",
      "Name": "����˵��",
      "MapType": 0,
      "SrcFieldId": "fcustomdesc",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfo",
      "Name": "��������",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "funitid",
      "Name": "������λ",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizunitid",
      "Name": "��װ��λ",
      "MapType": 0,
      "SrcFieldId": "fbizunitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizremainqty",
      "Name": "�ɰ�װ����",
      "MapType": 0,
      "SrcFieldId": "fbizpyqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizpackqty",
      "Name": "���ΰ�װ����",
      "MapType": 0,
      "SrcFieldId": "'0'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizpackedqty",
      "Name": "�Ѱ�װ����",
      "MapType": 0,
      "SrcFieldId": "'0'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizqty",
      "Name": "����װ����",
      "MapType": 0,
      "SrcFieldId": "fbizpdqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fremainqty",
      "Name": "������λ�ɰ�װ����",
      "MapType": 1,
      "SrcFieldId": "fpyqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fqty",
      "Name": "������λ����װ����",
      "MapType": 1,
      "SrcFieldId": "fpyqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorehouseid",
      "Name": "�ֿ�",
      "MapType": 0,
      "SrcFieldId": "fstorehouseid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorelocationid",
      "Name": "��λ",
      "MapType": 0,
      "SrcFieldId": "fstorelocationid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstatus",
      "Name": "���״̬",
      "MapType": 0,
      "SrcFieldId": "fstockstatus",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flotno",
      "Name": "����",
      "MapType": 0,
      "SrcFieldId": "flotno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtono",
      "Name": "�������ٺ�",
      "MapType": 0,
      "SrcFieldId": "fmtono",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownertype",
      "Name": "��������",
      "MapType": 0,
      "SrcFieldId": "fownertype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownerid",
      "Name": "����",
      "MapType": 0,
      "SrcFieldId": "fownerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpacktype",
      "Name": "�������",
      "MapType": 0,
      "SrcFieldId": "fmaterialid.fpackagtype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpjno",
      "Name": "�����Ϻ�",
      "MapType": 0,
      "SrcFieldId": "fpartscombnumber",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpacktype",
      "Name": "�������",
      "MapType": 0,
      "SrcFieldId": "fmaterialid.fpackagtype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceformid",
      "Name": "��Դ������",
      "MapType": 1,
      "SrcFieldId": "'stk_inventoryverify'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcebillno",
      "Name": "��Դ�����",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid",
      "Name": "��Դ������",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid",
      "Name": "��Դ����¼����",
      "MapType": 0,
      "SrcFieldId": "fentity.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceseq",
      "Name": "��Դ���к�",
      "MapType": 0,
      "SrcFieldId": "fentity.FSeq",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }
  ],
  "BillGroups": [
    {
      "Id": "fbillno",
      "Order": 1
    }
  ],
  "FieldGroups": [
    {
      "Id": "fentity_fentryid",
      "Order": 1
    }
  ]
}