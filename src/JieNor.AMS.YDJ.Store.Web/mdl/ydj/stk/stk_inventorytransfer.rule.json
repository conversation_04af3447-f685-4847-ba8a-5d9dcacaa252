{
  //规则引擎基类
  "base": "/mdl/ydj/stk/base_stkbilltmpl.rule.json",

  //定义表单锁定规则
  "lockRules": [
    {
      "id": "fstatus_D",
      "expression": "field:*$fstorelocationidto;menu:tbSubmit,tbPull$*|fstatus=='D'"
    },
    //{
    //  "id": "unlock_storelocationidto",
    //  "expression": "field:$fstorelocationidto,ftype,fdate|fstatus=='E'"
    //},
    //此规则表示：该商品对应的商品商品属性“允许定制”为true时放开，为false时，锁定
    {
      "id": "lock_fcallupcustomdescto",
      "expression": "field:fcallupcustomdescto$|fcustom!=true"
    },
    {
      "id": "unlock_fcallupcustomdescto",
      "expression": "field:$fcallupcustomdescto|fcustom==true"
    },
    //此规则表示：该商品对应的商品商品属性“允许选配”=true时，辅助属性放开，为false时，锁定
    {
      "id": "lock_fsel",
      "expression": "field:fattrinfo,fattrinfoto$|fispresetprop!=true"
    },
    {
      "id": "unlock_fsel",
      "expression": "field:$fattrinfo,fattrinfoto|fispresetprop==true"
    },
    //普通调拨时：
    //锁定 调出定制说明、调入定制说明、调出辅助属性、调出物流跟踪号、调入物流跟踪号。
    //解锁 调出仓库、调入仓库、调出仓位、调入仓位、调出库存状态、调入库存状态。
    {
      "id": "lock_ftype_1",
      "expression": "field:fcustomdesc,fcallupcustomdescto,fattrinfo,fmtono,fmtonoto,fattrinfoto$fstorehouseid,fstorehouseidto,fstorelocationid,fstorelocationidto,fstockstatus,fstockstatusto,fdeliveryman|ftype=='1' and fstatus!='E'"
    },
    //形态转换时：
    //锁定 调入库存状态。
    //解锁 出库存状态、调出定制说明、调入定制说明、调出辅助属性、调入辅助属性、调出仓库、调入仓库、调出仓位、调入仓位、调出物流跟踪号、调入物流跟踪号。
    {
      "id": "lock_ftype_2",
      "expression": "field:fstockstatusto$fstockstatus,fcustomdesc,fcallupcustomdescto,fattrinfo,fattrinfoto,fstorehouseid,fstorehouseidto,fstorelocationid,fstorelocationidto,fmtono,fmtonoto,fdeliveryman|ftype=='2' and fstatus!='E'"
    },
    //货主调整时：
    //锁定 调出仓库、调出仓位、调出辅助属性、调出定制说明、调出物流跟踪号、调入仓库、调入仓位、调入辅助属性、调入定制说明、调入物流跟踪号
    //解锁 调出货主类型、调出货主、调入货主类型、调入货主
    {
      "id": "lock_ftype_3",
      "expression": "field:fstorehouseid,fstorelocationid,fattrinfo,fcustomdesc,fmtono,fstorehouseidto,fstorelocationidto,fattrinfoto,fcallupcustomdescto,fmtonoto$fownertype,fownerid,fownertypeto,fowneridto,fdeliveryman|ftype=='3' and fstatus!='E'"
    },
    //仓位调拨时：
    //解锁 仓位字段
    {
      "id": "lock_ftype_4",
      "expression": "field:$fdeliveryman,fstorelocationidto|ftype=='4' and fstatus!='E'"
    },
    //当不是货主调整时：
    //锁定 调出货主类型、调出货主、调入货主类型、调入货主
    //解锁 调出仓库、调出仓位、调出辅助属性、调出定制说明、调出物流跟踪号、调入仓库、调入仓位、调入辅助属性、调入定制说明、调入物流跟踪号
    //{
    //  "id": "unlock_ftype_!3",
    //  "expression": "field:fownertype,fownerid,fownertypeto,fowneridto$fstorehouseid,fstorelocationid,fattrinfo,fcustomdesc,fmtono,fstorehouseidto,fstorelocationidto,fattrinfoto,fcallupcustomdescto,fmtonoto|ftype!='3'"
    //},
    {
      "id": "lock_fowneridto",
      "expression": "field:fowneridto$|fownertypeto=='' or fstatus=='D' or fstatus=='E'"
    },
    {
      "id": "unlock_fowneridto",
      "expression": "field:$fowneridto|fownertypeto!='' and fstatus!='D' and fstatus!='E'"
    },
    {
      "id": "unlock_tbCreateStockoutTask",
      "expression": "menu:$tbBarcode,tbCreateStockoutTask,tbCreateStockinTask|fstatus=='D' and fcancelstatus==false"
    },
    {
      "id": "lock_tbCreateStockoutTask",
      "expression": "menu:tbCreateStockoutTask,tbCreateStockinTask|fstatus!='D' or fcancelstatus==true"
    },
    {
      "id": "unlock_tbCreateStockinTask",
      "expression": "menu:$tbBarcode|fstatus=='E'"
    },
    //{
    //  "id": "lock_tbCreateStockinTask",
    //  "expression": "menu:tbBarcode|fstatus!='E'"
    //},
    {
      "id": "unlock_tbAllot",
      "expression": "menu:$tbAllot,tbAllotout,tbCancelAllotout|fstatus=='D'"
    },
    {
      "id": "lock_tbAllot",
      "expression": "menu:tbAllot,tbAllotout,tbCancelAllotout|fstatus!='D'"
    },
    {
      "id": "unlock_tbCreateInTransfer",
      "expression": "menu:$tbCreateInTransfer|fstatus=='E' and ftransfertype=='invtransfer_biztype_04'"
    },
    {
      "id": "lock_tbCancelInTransfer",
      "expression": "menu:tbCancelInTransfer|fstatus!='E' or  fintransferbillno==''"
    },
    {
      "id": "unlock_tbCancelInTransfer",
      "expression": "menu:$tbCancelInTransfer|fstatus=='E' and fintransferbillno!=''"
    },
    //1.调出方：不允许操作，按钮置灰
    {
      "id": "lock_tbTransferBack",
      "expression": "menu:tbTransferBack| ftransfertype=='invtransfer_biztype_04'"
    },
    {
      "id": "lock_tbCreateInTransfer",
      "expression": "menu:tbCreateInTransfer|fstatus!='E'"
    },
    {
      "id": "lock_tbPackorder",
      "expression": "menu:tbPackorder|fstatus!='D' or fcancelstatus=='0'"
    },
    {
      "id": "unlock_tbPackorder",
      "expression": "menu:$tbPackorder|fstatus=='D' and fcancelstatus!='0'"
    },
    {
      "id": "lock_tbEntryCopy",
      "expression": "menu:btnEntryCopy$|fstatus=='D' or fstatus=='E' or fcancelstatus==true"
    },
    {
      "id": "unlock_tbEntryCopy",
      "expression": "menu:$btnEntryCopy|fstatus!='D' and fstatus!='E' and fcancelstatus==false"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [
    //选择收货人信息，自动带出部门
    { "expression": "fstockdeptidto=fstockstaffidto__fdeptid|fstockstaffidto=='' or 1==1" },
    { "expression": "fbizunitid=fmaterialid__funitid" },
    { "expression": "fstockstatusto=fstorehouseidto__fstockid" },
    //选择商品时，携带出默认辅助属性
    { "expression": "fattrinfo=getAuxPropValue(fmaterialid)" },
    //形态转换与普通调拨时，处理默认字段的值携带
    { "expression": "fcallupcustomdescto=fcustomdesc|fcallupcustomdescto=='' and fcustomdesc!=''" },
    { "expression": "fmtonoto=fmtono|fmtonoto=='' and fmtono!=''" },
    //{ "expression": "fstorehouseidto=fstorehouseid|fstorehouseidto=='' and fstorehouseid!=''" },
    { "expression": "fstockstatusto=fstockstatus|fstockstatusto=='' and fstockstatus!=''" },
    { "expression": "fownertypeto=fownertype|fownertypeto=='' and fownertype!=''" },
    //{ "expression": "fattrinfoto=fattrinfo|fattrinfoto=='' and fattrinfo!=''" }
    //{ "expression": "fattrinfoto=fattrinfo| ftype!='2'" }
    //总体积
    { "expression": "ftotalvolume=fbizqty*fsinglevolume|fbizqty!='' and fsinglevolume!=''" },
    //单位体积
    { "expression": "fsinglevolume=ftotalvolume/fbizqty|fbizqty!='' and fbizqty!=0 and ftotalvolume!=''" }
  ]
}