{
  //规则引擎基类
  "base": "/mdl/ydj/stk/base_stkbilltmpl.rule.json",

  //定义表单锁定规则
  "lockRules": [
    //锁定调出物流跟踪号、发货仓库、发货仓位、发货仓状态
    {
      "id": "lock_fmtono",
      "expression": "field:fmtono,fstorehouseid,fstorelocationid,fstockstatus,fcustomdesc,fcustomdescto$fmtonoto,fstorehouseidto,fstorelocationidto,fstockstatusto;menu:tbQueryInventory$|fapplytype=='0' or fapplytype=='1'"
    },
    //锁定调入物流跟踪号、收货仓库、收货仓位、收货仓状态
    {
      "id": "lock_fmtonoto",
      "expression": "field:fmtonoto,fstorehouseidto,fstorelocationidto,fstockstatusto,fcustomdesc,fcustomdescto,fcustomdesc,fcustomdescto$fmtono,fstorehouseid,fstorelocationid,fstockstatus;menu:$tbQueryInventory|fapplytype=='3'"
    },
    //解锁调出物流跟踪号、发货仓库、发货仓位、发货仓状态、调入物流跟踪号、收货仓库、收货仓位、收货仓状态
    {
      "id": "unlock_fmtono",
      "expression": "field:$fstorehouseid,fstorehouseidto,fstorelocationid,fstorelocationidto,fstockstatus,fstockstatusto,fcustomdesc,fcustomdescto;menu:$tbQueryInventory|fapplytype=='2' or fapplytype=='4'"
    },
    {
      "id": "lock_tbQueryInventory$",
      "expression": "menu:tbQueryInventory$|fapplytype=='' or fapplytype==' '"
    },
    {
      "id": "lock_fprice",
      "expression": "field:fprice$|fsourcetype!='' and fsourcetype!=' '"
    },
    {
      "id": "unlock_fprice",
      "expression": "field:$fprice|fsourcetype=='' or fsourcetype==' '"
    },
    {
      "id": "lock_push",
      "expression": "field:fsostaffid,fsodeptid|fsourcenumber!='' and fsourcenumber!=' '"
    },
    {
      "id": "lock_fcustomdesc",
      "expression": "field:fcustomdesc,fcustomdescto$|fcustom!=true"
    },
    //规则引擎有bug，改用js处理，保留此处的规则，以覆盖基类的规则
    {
      "id": "unlock_fcustomdesc",
      "expression": "field:$fcustomdesc,fcustomdescto|(fapplytype=='2' or fapplytype=='4') and fcustom==true"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [

    { "expression": "fapplydeptid=fapplystaffid__fdeptid" },
    { "expression": "fstockdeptidto=fstockstaffidto__fdeptid" },
    { "expression": "fstockdeptid=fstockstaffid__fdeptid" },

    { "expression": "fsodeptid=fsostaffid__fdeptid" },

    { "expression": "fcustphone=fcustomerid__fphone" },
    { "expression": "funitid=fmaterialid__funitid|fmaterialid=='' or 1==1" },
    { "expression": "fbizunitid=fmaterialid__funitid|fmaterialid=='' or 1==1" },

    { "expression": "fstockstatusto=fstorehouseidto__fstockid|fstorehouseidto=='' or 1==1" },
    { "expression": "fstockstatusto=fstockstatus|fapplytype=='3'" },
    { "expression": "fstockstatus=fstorehouseid__fstockid|fstorehouseid=='' or 1==1" },
    { "expression": "fstockstatus=fstockstatusto|fapplytype=='0' or fapplytype=='1'" },
    { "expression": "fcustomdescto=fcustomdesc|fcustomdescto==''" },
    { "expression": "fmtonoto=fmtono" },
    { "expression": "faddress=fcustomerid__faddress" },
    //选择商品时，携带出默认辅助属性
    { "expression": "fattrinfo=getAuxPropValue(fmaterialid)" },
    { "expression": "fattrinfoto=getAuxPropValue(fmaterialid)" },
    { "expression": "famount=fprice*fqty" }
  ]
}