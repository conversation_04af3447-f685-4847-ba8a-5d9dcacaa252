<!-- 经销商库存水位配置
 -->
<html lang="en">
<head>
</head>
<body id="si_inventorywaterlevel" el="3" basemodel="bd_basetmpl" cn="经销商库存水位配置表" isolate="0">
    <!-- 4.1.11.4.1.1基本信息-单据头 -->
    <div id="fbillhead" el="51" pk="fid" tn="t_si_inventorywaterlevel" pn="fbillhead" cn="经销商库存水位配置表">
        <input group="基本信息" ek="fbillhead" type="text" el="108" id="fnumber" fn="fnumber" pn="fnumber" cn="编码" width="120" visible="-1" copy="0" lix="1" />
        <input group="基本信息" ek="fbillhead" type="text" el="100" id="fname" fn="fname" pn="fname" cn="名称" width="120" visible="-1" copy="0" lix="2" />
        <select el="152" group="基本信息" ek="fbillhead" visible="1150" id="fcontrollevel" fn="fcontrollevel" pn="fcontrollevel" cn="管控级别" vals="'1':'送达方（优先级1）','2':'售达方（优先级2）','3':'全局（优先级3）'" defval="'0'" notrace="false" width="90" lix="5" />
        <input el="105" group="基本信息" ek="fbillhead" visible="1150" id="fredline" fn="fredline" pn="fredline" cn="红灯线" copy="0" format="0,000.00" width="100" notrace="false" />
        <input el="105" group="基本信息" ek="fbillhead" visible="1150" id="fgreenline" fn="fgreenline" pn="fgreenline" cn="绿灯线" copy="0" format="0,000.00" width="100" notrace="false" />

        <input lix="5" group="基本信息" el="107" ek="fbillhead" visible="-1" id="fagentnumber" fn="fagentnumber" pn="fagentnumber" cn="售达方编码" ctlfk="fagentid" lock="-1" dispfk="fnumber" lix="10" />
        <input lix="10" group="基本信息" el="106" ek="fbillhead" visible="-1" id="fagentid" fn="fagentid" pn="fagentid" refid="bas_agent" cn="售达方名称" lix="15" notrace="false" />
        <input lix="5" group="基本信息" el="107" ek="fbillhead" visible="-1" id="fdelivernumber" fn="fdelivernumber" pn="fdelivernumber" cn="送达方编码" ctlfk="fdeliverid" lock="-1" dispfk="fnumber" lix="20" />
        <input lix="10" group="基本信息" el="106" ek="fbillhead" visible="-1" id="fdeliverid" fn="fdeliverid" pn="fdeliverid" refid="bas_deliver" cn="送达方名称" lix="25" notrace="false" />
        <input group="基本信息" ek="fbillhead" type="text" el="116" id="fcalculateenable" fn="fcalculateenable" dispfk="fcalculateenable" ts="" cn="启用计算"  defval="true" notrace="false"  visible="-1" lock="0" lix="30" width="100" />
        <input group="基本信息" ek="fbillhead" type="text" el="116" id="fvalidateenable" fn="fvalidateenable" dispfk="fvalidateenable" ts="" cn="启用校验"  defval="true" notrace="false"  visible="-1" lock="0" lix="30" width="100" />
    </div>

    <!-- 4.1.11.4.1.3例外型号-单据体 model -->
    <table id="fexceptionmodelsentity" el="52" pk="fexceptionmodelsentity" tn="t_si_exceptionmodels" pn="fexceptionmodelsentity" cn="例外SPU" kfks="fseltypeid">
        <tr>
            <th ek="fexceptionmodelsentity" el="107" id="fseltypeid_fumber" fn="fseltypeid_fumber" ctlfk="fseltypeid" dispfk="fnumber" ts="" cn="型号编码" visible="1150" lix="9" width="140"></th>
            <th ek="fexceptionmodelsentity" el="106" id="fseltypeid" fn="fseltypeid" refid="sel_type" ts="" cn="型号名称" visible="1150" lix="10" lock="0" must="1" width="200" notrace="false" ></th>
            <th el="116" ek="fexceptionmodelsentity" id="fcalculateenable_spu" fn="fcalculateenable_spu" pn="fcalculateenable_spu" width="100" cn="SPU启用计算" defval="true" visible="-1" lix="35" notrace="false" ></th>
            <th el="116" ek="fexceptionmodelsentity" id="fvalidateenable_spu" fn="fvalidateenable_spu" pn="fvalidateenable_spu" width="100" cn="SPU启用校验" defval="true" visible="-1" lix="35" notrace="false" ></th>
            <th el="105" ek="fexceptionmodelsentity" id="fredline_spu" fn="fredline_spu" pn="fredline_spu" width="100" cn="SPU红灯线" format="0,000.00" visible="-1" lix="35" notrace="false" ></th>
            <th el="105" ek="fexceptionmodelsentity" id="fgrennline_spu" fn="fgrennline_spu" pn="fgrennline_spu" width="100" cn="SPU绿灯线" format="0,000.00"  visible="-1" lix="35" notrace="false" ></th>
        </tr>
    </table>

    <div id="opList">
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存" data="" permid="">
            <li el="11" id="save_valid_Controllevel3" cn="保存时全局（优先级3）必须唯一" vid="500" data="fcontrollevel" precon="fcontrollevel=='3'"></li>
            <li el="11" id="save_valid_Controllevel2" cn="保存时售达方（优先级2）必须唯一" vid="500" data="fagentid,fcontrollevel" precon="fcontrollevel=='2'"></li>
            <li el="11" id="save_valid_Controllevel1" cn="保存时送达方（优先级1）必须唯一" vid="500" data="fdeliverid,fcontrollevel" precon="fcontrollevel=='1'"></li>
        </ul>
    </div>
</body>
