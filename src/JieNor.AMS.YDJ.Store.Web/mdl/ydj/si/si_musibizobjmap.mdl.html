<!doctype html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
</head>
<body id="si_musibizobjmap" el="3" basemodel="bd_basetmpl" mn="系统集成" cn="慕思成品中台业务对象映射" IsAsynLstDesc="true">

    <div id="fbillhead" el="51" pk="fid" tn="t_si_musibizobjmap" pn="fbillhead" cn="慕思成品中台业务对象映射">

        <!--重新基类模型字段部分属性-->
        <input id="fdescription" el="100" cn="备注" visible="1150" />

        <input group="基本信息" el="100" ek="fbillhead" visible="-1" lix="10" id="fnumber" fn="fnumber" cn="编码" width="100" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" lix="20" id="fname" fn="fname" cn="外部接口名称" width="180" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" lix="30" id="fapino" fn="fapino" cn="外部接口编码" width="220" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" lix="40" id="fextappid" fn="fextappid" cn="外部应用" refid="sys_externalapp" width="150" filter="fapptype='3'" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" lix="50" id="fmyobjectid" fn="fmyobjectid" cn="我方业务对象" refid="sys_bizobject" filter="(ftype='1' or ftype='3')" width="120" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" lix="60" id="fextobjectid" fn="fextobjectid" cn="外部业务对象" width="120" />
        <input group="基本信息" el="152" ek="fbillhead" visible="1150" lix="70" id="fsynctype" fn="fsynctype" cn="请求方式"
               vals="'0':'Post','1':'Get'" defval="'0'" width="110" />
        <input group="基本信息" el="152" ek="fbillhead" visible="1150" lix="70" id="fsyncdir" fn="fsyncdir" cn="同步方向"
               vals="'musitocurrent':'从慕思中台至当前系统','currenttomusi':'从当前系统至慕思中台'" defval="'currenttomusi'" width="110" />

        <input group="基本信息" el="152" ek="fbillhead" visible="0" lix="80" id="fsyncway" fn="fsyncway" cn="同步方式"
               vals="'pull':'主动拉取','push':'主动推送'" defval="'push'" width="90" lock="-1" />

        <input group="基本信息" el="152" ek="fbillhead" visible="0" lix="95" id="fresulttypein" fn="fresulttypein" cn="输入结果类型" width="150"
               vals="'table':'内表','struct':'结构','string':'字符串','set':'单表'" defval="'struct'" lock="-1" />
        <input group="基本信息" el="100" ek="fbillhead" visible="0" lix="96" id="fresultin" fn="fresultin" cn="输入结果" width="150" lock="-1" />

        <input group="基本信息" el="152" ek="fbillhead" visible="0" lix="98" id="fresulttype" fn="fresulttype" cn="输出结果类型" width="150"
               vals="'table':'内表','struct':'结构','string':'字符串','set':'单表'" defval="'struct'" lock="-1" />
        <input group="基本信息" el="100" ek="fbillhead" visible="0" lix="100" id="fresult" fn="fresult" cn="输出结果" width="150" lock="-1" />

        <input group="基本信息" el="152" ek="fbillhead" visible="0" lix="98" id="fentityoptype" fn="fentityoptype" cn="更新明细类型" width="150"
               vals="'override':'全覆盖','update':'更新'" lock="-1" />

        <input group="基本信息" el="100" ek="fbillhead" visible="1150" lix="110" id="fresultflag" fn="fresultflag" cn="标记位标识" width="150" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" lix="120" id="fresultmsg" fn="fresultmsg" cn="消息标识" width="150" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" lix="120" id="fsuccessflag" fn="fsuccessflag" cn="成功标识" width="150" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" lix="120" id="fmyresultconfig" fn="fmyresultconfig" cn="我方协同结果配置" width="150" />

        <input group="基本信息" el="100" ek="fbillhead" visible="1150" lix="120" id="fsyncdate" fn="fsyncdate" cn="协同日期" width="150" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" lix="120" id="fsyncstatus" fn="fsyncstatus" cn="协同状态" width="150" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" lix="120" id="fsyncstatusvalues" fn="fsyncstatusvalues" cn="协同状态值" width="150" />

        <!--满足条件的数据才参与同步-->
        <input group="基本信息" el="127" ek="fbillhead" visible="1150" lix="130" id="ffilterstring" fn="ffilterstring" cn="过滤条件" xsslv="1" width="200" />

        <!--同步顺序，为0时，系统自动推断，自动插入到某个已排序的对象前面，保留人工干预的可能-->
        <input group="基本信息" el="101" ek="fbillhead" visible="1150" lix="140" id="fsyncorder" fn="fsyncorder" cn="同步顺序" width="75" />

        <input group="基本信息" el="101" ek="fbillhead" visible="-1" lix="145" id="fmaxsize" fn="fmaxsize" cn="最大处理数" width="85" defval="2000" />
        <input group="基本信息" el="101" ek="fbillhead" visible="-1" lix="145" id="fbatchsize" fn="fbatchsize" cn="批处理数" width="75" defval="200" />

        <input group="基本信息" el="116" ek="fbillhead" visible="-1" lix="147" id="fsynctimer" fn="fsynctimer" cn="定时同步" width="80" />
        <input group="基本信息" el="116" ek="fbillhead" visible="-1" lix="147" id="fsyncmanual" fn="fsyncmanual" cn="手工同步" width="80" />
        <input group="基本信息" el="116" ek="fbillhead" visible="-1" lix="150" id="fsyncaftersave" fn="fsyncaftersave" cn="保存时同步" width="90" />
        <input group="基本信息" el="116" ek="fbillhead" visible="-1" lix="175" id="fsyncaftersubmit" fn="fsyncaftersubmit" cn="提交时同步" width="90" />
        <input group="基本信息" el="116" ek="fbillhead" visible="-1" lix="160" id="fsyncafterdelete" fn="fsyncafterdelete" cn="删除时同步" width="90" />
        <input group="基本信息" el="116" ek="fbillhead" visible="-1" lix="170" id="fsyncafteraudit" fn="fsyncafteraudit" cn="审核时同步" width="90" />
        <input group="基本信息" el="116" ek="fbillhead" visible="-1" lix="175" id="fsyncafterunaudit" fn="fsyncafterunaudit" cn="反审核时同步" width="90" />
        <input group="基本信息" el="116" ek="fbillhead" visible="-1" lix="170" id="fsyncafterforbid" fn="fsyncafterforbid" cn="禁用时同步" width="90" />
        <input group="基本信息" el="116" ek="fbillhead" visible="-1" lix="175" id="fsyncafterunforbid" fn="fsyncafterunforbid" cn="反禁用时同步" width="90" />

        <input group="基本信息" el="116" ek="fbillhead" visible="-1" lix="175" id="fisasync" fn="fisasync" pn="fisasync" cn="异步执行" width="90" />

        <input group="基本信息" el="127" ek="fbillhead" visible="1150" lix="180" id="fextendparam" fn="fextendparam" cn="其它同步参数" width="180" />
        <!-- <input group="基本信息" el="100" ek="fbillhead" visible="1150" lix="185" id="fopcode" fn="fopcode" pn="fopcode" cn="操作码" width="150" /> -->

    </div>

    <table id="fparamentry" el="52" pk="fentryid" tn="t_si_musibizobjmapparam" pn="fparamentry" cn="接口参数明细" kfks="fparamid">
        <tr>
            <th el="100" ek="fparamentry" visible="1150" id="fparamid" fn="fparamid" cn="参数标识" width="150"></th>

            <th el="152" ek="fparamentry" visible="1150" id="fparamsrc" fn="fparamsrc" cn="参数值来源" width="150"
                vals="'const':'常量','currbegindate':'当天开始时间','currenddate':'当天结束时间','currbefore':'当天前X天','currafter':'当天后X天','fieldmap':'字段映射','batchsize':'批处理数'" defval="'const'"></th>

            <th el="100" ek="fparamentry" visible="1150" id="fparamvalue" fn="fparamvalue" cn="参数值" width="150" len="500"></th>
            <th el="100" ek="fparamentry" visible="1150" id="fparamname" fn="fparamname" cn="参数说明" width="300"></th>
            <th el="100" ek="fparamentry" visible="1150" id="fparamformat" fn="fparamformat" cn="参数格式" width="300"></th>

            <th el="152" ek="fparamentry" visible="0" id="fparamtype" fn="fparamtype" cn="参数类型" width="150"
                vals="'string':'字符串','table':'内表','struct':'结构','int':'整数','decimal':'十进制小数','double':'双精度小数','float':'单精度小数','datetime':'日期'"
                defval="'string'"></th>
        </tr>
    </table>

    <table id="ffieldentry" el="52" pk="fentryid" tn="t_si_musibizobjmapfield" pn="ffieldentry" cn="字段映射明细" kfks="fmyfieldid,fextfieldid">
        <tr>
            <th el="139" ek="ffieldentry" visible="1150" id="fmyfieldid" fn="fmyfieldid" cn="我方业务字段" ctlfk="fmyobjectid" width="200" liv="1"></th>
            <th el="100" ek="ffieldentry" visible="1150" id="fmyconstval" fn="fmyconstval" cn="我方常量" width="150" liv="2"></th>
            <th el="100" ek="ffieldentry" visible="1150" id="fmyfieldexpression" fn="fmyfieldexpression" cn="我方字段表达式" width="150" liv="3"></th>
            <th el="100" ek="ffieldentry" visible="1150" id="fmyfieldformat" fn="fmyfieldformat" cn="我方字段格式" width="200" liv="4"></th>
            <th el="116" ek="ffieldentry" visible="1150" id="fispk" fn="fispk" cn="是否主键" width="100" liv="5"></th>
            <th el="116" ek="ffieldentry" visible="0" id="fiskey" fn="fiskey" cn="实体唯一性标识" width="100" liv="6"></th>
            <th el="100" ek="ffieldentry" visible="1150" id="fextfieldid" fn="fextfieldid" cn="外部业务字段标识" width="160" liv="7"></th>
            <th el="100" ek="ffieldentry" visible="1150" id="fextfieldname" fn="fextfieldname" cn="外部业务字段名称" width="160" liv="8"></th>
            <th el="100" ek="ffieldentry" visible="1150" id="fextentityid" fn="fextentityid" cn="外部业务实体标识" width="140" liv="9"></th>
            <th el="116" ek="ffieldentry" visible="0" id="fsrcentrykey" fn="fsrcentrykey" cn="来源明细标识" width="110" liv="10"></th>
            <th el="100" ek="ffieldentry" visible="1150" id="fextconstval" fn="fextconstval" cn="外部常量" width="150" liv="11"></th>
            <th el="100" ek="ffieldentry" visible="0" id="fextfieldformat" fn="fextfieldformat" cn="外部字段格式" width="150" liv="12"></th>
            <th el="100" ek="ffieldentry" visible="1150" id="fextfieldexpression" fn="fextfieldexpression" cn="外部字段表达式" width="200" liv="13"></th>
            <th el="116" ek="ffieldentry" visible="0" id="fnotupdate" fn="fnotupdate" cn="是否更新时忽略" width="100" liv="14"></th>
            <th el="100" ek="ffieldentry" visible="1150" id="fmapdesc" fn="fmapdesc" cn="映射说明" width="300" liv="15"></th>
        </tr>
    </table>

</body>
</html>