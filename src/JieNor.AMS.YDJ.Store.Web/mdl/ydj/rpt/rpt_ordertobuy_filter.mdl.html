<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="rpt_ordertobuy_filter" el="0" basemodel="" cn="订单转采购分析过滤">
    <div id="fbillhead" el="51" pk="fid" pn="fbillhead" cn="订单转采购分析过滤">
        <!--合同交货日期-->
        <input group="基本信息" el="112" ek="fbillhead" id="fdatefrom" fn="fdatefrom" pn="fdatefrom" visible="-1" cn="合同交货日期从"
               lock="0" copy="1" lix="0" notrace="true" ts="" format="yyyy-MM-dd" />
        <input group="基本信息" el="112" ek="fbillhead" id="fdateto" fn="fdateto" pn="fdateto" visible="-1" cn="合同交货日期至"
               lock="0" copy="1" lix="0" notrace="true" ts="" format="yyyy-MM-dd" />
        <input group="基本信息" el="131" ek="fbillhead" id="fstorehouseid" fn="fstorehouseid" pn="fstorehouseid" visible="-1" cn="仓库" refid="ydj_storehouse"
               lock="0" copy="0" lix="90" notrace="true" ts="" filter="" reflvt="0" dfld="" />
        <input group="基本信息" el="125" ek="fbillhead" id="fwarehousetype" fn="fwarehousetype" pn="fwarehousetype"
               visible="-1" cn="仓库类型" refId="bd_enum" dfld="fenumitem" cg="仓库类型"
               lock="0" copy="1" lix="0" notrace="true" ts="" />
        <input group="基本信息" el="125" ek="fbillhead" id="forderstatus" fn="forderstatus" pn="forderstatus" visible="-1" cn="订单状态" refId="bd_enum" dfld="fenumitem" cg="数据状态" defval="'E'"
               lock="0" copy="1" lix="0" notrace="true" ts="" />

        <!-- 商品-->
        <!-- 基础资料选择，可以多选按分号隔开-->
        <input group="基本信息" el="131" ek="fbillhead" id="fproductid" fn="fproductid" pn="fproductid" visible="-1" cn="商品" refid="ydj_product"
               lock="0" copy="0" lix="90" notrace="true" ts="" filter="" reflvt="0" dfld="" />

        <input group="基本信息" el="131" ek="fbillhead" id="fcategoryid" fn="fcategoryid" pn="fcategoryid" visible="-1" cn="商品类别" refid="ydj_category"
               lock="0" copy="0" lix="90" notrace="true" ts="" filter="" reflvt="0" dfld="" />

        <select group="基本信息" el="152" ek="fbillhead" visible="-1" id="fisbz" fn="fisbz" pn="fisbz"
                cn="是否非标商品" vals="'1':'是','0':'否'" copy="0" defval="" lix="90"></select>

        <select group="基本信息" el="152" ek="fbillhead" visible="-1" id="fisdz" fn="fisdz" pn="fisdz"
                cn="是否定制商品" vals="'1':'是','0':'否'" copy="0" defval="" lix="90"></select>

        <input group="基本信息" el="116" ek="fbillhead" id="fisshow" fn="fisshow" pn="fisshow" type="checkbox" cn="无建议采购量不显示" visible="-1" lix="70" defval="true"  />
        <input group="基本信息" el="116" ek="fbillhead" id="fstorehouseisnull" fn="fstorehouseisnull" pn="fstorehouseisnull" type="checkbox" cn="合同仓库为空参与计算" visible="-1" lix="70" defval="true" />
        <input group="基本信息" el="116" ek="fbillhead" id="faddqty" fn="faddqty" pn="faddqty" type="checkbox" cn="计算建议采购量考虑【库存锁定数】、【已转采购数】、【未清采购数】" visible="-1" lix="70" defval="true" />
        <!--供应商-->
        <input group="基本信息" el="131" ek="fbillhead" id="fsupplierid" fn="fsupplierid" pn="fsupplierid" visible="-1" cn="供应商" refid="ydj_supplier"
               lock="0" copy="0" lix="90" notrace="true" ts="" filter="" reflvt="0" dfld="" />
    </div>
</body>
</html>