<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="rpt_orderbalance_filter" el="0" basemodel="" cn="销售合同明细报表过滤">
    <div id="fbillhead" el="51" pk="fid" pn="fbillhead" cn="销售合同明细报表过滤">
        <!-- 销售合同号-->
        <!--按编号模糊查询-->
        <input group="基本信息" el="100" ek="fbillhead" id="fbillno" fn="fbillno" pn="fbillno" visible="-1" cn="销售合同编号"
               lock="0" copy="0" lix="90" notrace="true" ts="" />

        <!-- 销售日期-->
        <!-- 获取当前日期 -->
        <input group="基本信息" el="112" ek="fbillhead" id="fdatefrom" fn="fdatefrom" pn="fdatefrom" visible="-1" cn="创建日期从"
               lock="0" copy="1" lix="0" notrace="true" ts="" format="yyyy-MM-dd" />
        <input group="基本信息" el="112" ek="fbillhead" id="fdateto" fn="fdateto" pn="fdateto" visible="-1" cn="创建日期至"
               lock="0" copy="1" lix="0" notrace="true" ts="" format="yyyy-MM-dd" />

        <!-- 客户-->
        <!-- 基础资料选择，可以多选按分号隔开-->
        <input group="基本信息" el="131" ek="fbillhead" id="fcustomerid" fn="fcustomerid" pn="fcustomerid" visible="-1" cn="客户" refid="ydj_customer"
               lock="0" copy="0" lix="90" notrace="true" ts="" filter="" reflvt="0" dfld="" />
        <!-- 商品-->
        <!-- 基础资料选择，可以多选按分号隔开-->
        <input group="基本信息" el="131" ek="fbillhead" id="fproductid" fn="fproductid" pn="fproductid" visible="-1" cn="商品" refid="ydj_product"
               lock="0" copy="0" lix="90" notrace="true" ts="" filter="" reflvt="0" dfld="" />
        <!--创建人-->
        <!-- 基础资料选择，可以多选按分号隔开-->
        <input group="基本信息" el="131" ek="fbillhead" id="fcreatorid" fn="fcreatorid" pn="fcreatorid" visible="-1" cn="创建人" refid="sec_user" />
        <!-- 手工单号-->
        <!--按编号模糊查询-->
        <input group="基本信息" el="100" ek="fbillhead" id="fwithin" fn="fwithin" pn="fwithin" visible="-1" cn="手工单号"
               lock="0" copy="0" lix="90" notrace="true" ts="" />


        <select group="基本信息" el="152" ek="fbillhead" visible="-1" id="flinkpro" fn="flinkpro" pn="flinkpro"
                cn="销售合同状态" vals="'已出库':'已出库','已入库':'已入库','已下采购':'已下采购'" copy="0" defval="" lix="90"></select>

        <!-- 门店-->
        <!-- 基础资料选择，可以多选按分号隔开-->
        <input group="基本信息" el="131" ek="fbillhead" id="fstoreid" fn="fstoreid" pn="fstoreid" visible="-1" cn="门店" refid="bas_store"
               lock="0" copy="0" lix="90" notrace="true" ts="" filter="" reflvt="0" dfld="" />
        <!--销售员-->
        <input group="基本信息" el="131" ek="fbillhead" id="assistant" fn="assistant" pn="assistant" visible="-1" cn="销售员" refid="ydj_staff"
               lock="0" copy="0" lix="90" notrace="true" ts="" filter="" reflvt="0" dfld="" />

        <!--业绩品牌-->
        <input group="基本信息" el="131" ek="fbillhead" id="fresultbrandid" fn="fresultbrandid" pn="fresultbrandid" visible="-1" cn="业绩品牌" refid="ydj_series"
               lock="0" copy="0" lix="90" notrace="true" ts="" filter="" reflvt="0" dfld="" />

        <!-- 合同交货日期 -->
        <input group="基本信息" el="112" ek="fbillhead" id="fdatefrom_delivery" fn="fdatefrom_delivery" pn="fdatefrom_delivery" visible="-1" cn="合同交货日期从"
               lock="0" copy="1" lix="0" notrace="true" ts="" format="yyyy-MM-dd" />
        <input group="基本信息" el="112" ek="fbillhead" id="fdateto_delivery" fn="fdateto_delivery" pn="fdateto_delivery" visible="-1" cn="合同交货日期至"
               lock="0" copy="1" lix="0" notrace="true" ts="" format="yyyy-MM-dd" />
 

    </div>


</body>
</html>