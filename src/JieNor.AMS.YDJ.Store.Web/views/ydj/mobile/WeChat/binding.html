<html>
<head>
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1, user-scalable=no" />
    <title>手机号绑定</title>
    <link href="/fw/css/ydj/mobile/isp/App/ionic.css" rel="stylesheet" />
    <link href="/fw/css/ydj/mobile/isp/App/override.css" rel="stylesheet" />
    <link href="/fw/css/ydj/mobile/WeChat/compound.css" rel="stylesheet" />
</head>
<body class="booking bg-white">
    <div class="list">
        <div class="item item-input relative">
            <span class="input-label">手机号</span>
            <input type="tel" maxlength="11" placeholder="请输入手机号" class="mobilephone" yi-hover="true" name="mobilePhone" yi-name="codelogin" yi-pattern="^[1]{1}[3,4,5,7,8]{1}[0-9]{9}$" yi-error="手机号码格式错误" />
            <i class="icon input-clear ion-android-close hide"></i>
        </div>
        <div class="item item-input">
            <span class="input-label">验证码</span>
            <input type="number" placeholder="验证码" yi-name="codelogin" yi-hover="true" name="code" maxlength="4" yi-pattern="^\d{4}$" yi-error="请输入四位验证码">
            <i class="icon input-code-clear ion-android-close hide"></i>
            <i class="code-right bg-blue-light absolute" onclick="GetCode(this)">获取验证码</i>
        </div>
    </div>
    <div class="text-center submit-wrrap"><button class="bg-blue-light" onclick="bindMobile()">提交信息</button></div>


    <script src="/fw/js/ydj/mobile/isp/App/Comm.js"></script>
    <script> 
        var _input = document.getElementsByTagName('input');
        var openId, isbind = true,flag=true;
        if (_input[0].getAttribute("yi-validate") == false) {
            flag = false;
        }
        GetToken(function () {
            if (arguments[0]) {
                openId = arguments[0];
            } else {
                Message.Alert('无法获取openId')
            }
            if (arguments[1]) {
                isbind = false;
            }
        });

        function bindMobile() {
            if (flag == true && openId) {
                Ajax({
                    url: '/sms/code/validate?MobilePhone=' + _input[0].value + '&AuthCode=' + _input[1].value + "&format=json",
                    callback: function () {
                        var Json = arguments[0];
                        if (String(Json.operationResult.isSuccess).Boolean()) {

                            Ajax({
                                url: "/Wechat/BindMobile?format=json",
                                data: {
                                    mobile: _input[0].value,
                                    openid: openId,
                                    isbind: isbind
                                },
                                callback: function () {
                                    var Json = arguments[0];
                                    if (String(Json.isSuccess).Boolean()) {
                                        WeixinJSBridge.call('closeWindow');
                                    } else {
                                        Message.Alert("此手机号已绑定！")
                                    }
                                }
                            });

                        }
                        else {
                            Message.Alert("验证码错误");
                        }
                    }
                });
            }
        }
        
    </script>
</body>
</html>