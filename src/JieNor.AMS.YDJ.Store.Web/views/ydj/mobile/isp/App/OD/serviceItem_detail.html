<html>
<head>
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1, user-scalable=no" />
    <title>服务项目详情</title>
    <link href="/fw/css/ydj/mobile/isp/App/ionic.css" rel="stylesheet" />
    <link href="/fw/css/ydj/mobile/isp/App/override.css" rel="stylesheet" />
</head>
<body>
    <div class="bar bar-header bar-white">
        <i class="bar-title-left icon ion-ios-arrow-back blue button button-icon" onclick="Andoggy.finishPage()"></i>
        <div class="title">服务项目详情</div>
    </div>
    <div id="Scroll" data-calc="44">
        <div class="content has-header" id="seviceItem-detail">
            <div class="subtitle">服务清单</div>
            <ul class="bg-white" id="service-list">

            </ul>
            <div>
                <div class="col75 bg-blue floatL subtitle text-center">总金额</div>
                <div class="col25 floatL bg-white subtitle assertive text-right" style="padding-right:15px;"><span>¥</span><span id="sum"></span></div>
            </div>
            <div class="subtitle clearB">商品图片</div>
            <div class="bg-white border" id="service-img">
                <div class="w31">
                    <img src="/fw/css/ydj/mobile/isp/App/images/imgupload.png" />
                </div>
            </div>
            <div class="bg-white border record">
                <div class="subtitle" style="padding-left:10px;">订单备注</div>
                <textarea id="order-record" class="textarea" disabled></textarea>
            </div>
        </div>
    </div>

    <script src="/fw/js/ydj/mobile/isp/App/Comm.js"></script>
    <script>
        //获取后端数据填充到页面中
        var fid = GetQueryString('fid');
        if (fid) {
            Ajax({
                url: "/bill/ydj_service?operationno=getservicedetail&format=json",
                data: {
                    fromId: "ydj_service",
                    operationNo: "getservicedetail",
                    simpleData: { id: fid}
                },
                callback: function () {
                    var Json = arguments[0];
                    if (String(Json.operationResult.isSuccess).Boolean() && !String(Json.operationResult.srvData).isNullOrEmpty()) {
                        var result = Json.operationResult.srvData.uidata;
                        if (result) {
                            var datas = result.fproductentry
                            for (var i = 0; i < datas.length; i++) {
                                var Html = '<li>';
                                Html += '<div  class="w40 floatL"><span>' + datas[i].fseritemid.fname + '</span><br /><span>¥</span><span>' + datas[i].fprice + '</span><span>/' + datas[i].funitid.fname + '</span></div>';
                                Html += '<div class="w30 floatL"><span>' + datas[i].fqty + '</span></div>';
                                Html += '<div class="w30 floatR assertive text-right"><span>¥</span><span class="price">' + datas[i].famount + '</span></div>';
                                Html += '</li>';

                                Html.CreateHtml("service-list");

                            }

                            //总金额汇总
                            var elem = DomTag('.price');
                            var sum = 0;
                            for (var i = 0; i < elem.length; i++) {
                                sum += parseInt(elem[i].innerText);
                            }
                            DomTag('sum').innerText = sum;

                            //订单备注
                            if (result.fdescription) {
                                DomTag('order-record').innerText =String(result.fattention).NullReplaceEmpty();
                            }
                            //商品图片
                            var a=GetImages(DomTag('service-img'), result.fproimage.id.split(','));

                        }

                    }
                }
            });
        }

    </script>
</body>
</html>
