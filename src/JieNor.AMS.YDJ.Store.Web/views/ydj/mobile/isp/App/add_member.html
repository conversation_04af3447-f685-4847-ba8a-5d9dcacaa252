<html>
<head>
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1, user-scalable=no" />
    <title>添加队员</title>
    <link href="/fw/css/ydj/mobile/isp/App/ionic.css" rel="stylesheet" />
    <link href="/fw/css/ydj/mobile/isp/App/override.css" rel="stylesheet" />
</head>
<body>
    <div class="bar bar-header bar-white">
        <i class="bar-title-left icon ion-ios-arrow-back blue button button-icon" onclick="Andoggy.finishPage()"></i>
        <div class="title">添加队员</div>
    </div>
    <div class="content has-header marginTop44 ionic-pseudo">
        <div class="list">
            <label class="item item-input" id="search">
                <i class="icon ion-search placeholder-icon balanced"></i>
                <input type="text" placeholder="请输入师傅姓名或手机号码">
                <button class="button button-small button-balanced">查询</button>
            </label>
        </div>
        <div class="list" id="Scroll" data-calc="120">

        </div>
    </div>

    <script src="/fw/include/jquery/jquery-1.11.3.min.js"></script>
    <script src="/fw/js/ydj/mobile/isp/App/iscroll.js"></script>
    <!--Reference the SignalR library.广播通信插件 -->
    <script src="/fw/include/signalr/jquery.signalR-2.2.2.js"></script>
    <!--Reference the autogenerated SignalR hub script. -->
    <script src="/signalr/hubs"></script>
    <script src="/fw/js/ydj/mobile/isp/App/Comm.js"></script>

    <script>
        var Config = JSON.parse(FileConfig);
        //搜索添加成员信息
        DomTag('#search .button')[0].onclick = function () {
            DomTag("Scroll").innerHTML = "";
            Ajax({
                url: "/bill/ydj_master?operationno=AddBySearch&format=json",
                data: {
                    fromId: "ydj_master",
                    operationNo: "AddBySearch",
                    simpleData: { Phone: DomTag('#search input')[0].value }
                },
                callback: function () {
                    var Json = arguments[0];
                    if (String(Json.operationResult.isSuccess).Boolean()) {
                        var result = Json.operationResult.srvData;
                        if (result.length > 0) {
                            for (var i = 0; i < result.length; i++) {
                                var Html = '';
                                if (!String(result[i].tname).isNullOrEmpty()) {

                                    Html = '<div class="item item-avatar">';
                                    if (String(result[i]["fimage"]).isNullOrEmpty()) {
                                        Html += '<img src="/fw/css/ydj/mobile/isp/App/images/App-logo.png">';
                                    } else {
                                        Html += '<img src="' + Config.fsApiUrl + '/FileInfo/File/' + result[i]["fimage"] + '">';
                                    }
                                    Html += '<h2>' + result[i].name + '</h2>';
                                    Html += '<p>' + result[i].phone + '</p>';
                                    Html += '<button class="button button-small code-right bg-grey">' + result[i].tname + '</button>';
                                    Html += '</div>';

                                } else {

                                    Html = '<div class="item item-avatar">';
                                    if (String(result[i]["fimage"]).isNullOrEmpty()) {
                                        Html += '<img src="/fw/css/ydj/mobile/isp/App/images/App-logo.png">';
                                    } else {
                                        Html += '<img src="' + Config.fsApiUrl + '/FileInfo/File/' + result[i]["fimage"] + '">';
                                    }
                                    Html += '<h2>' + result[i].name + '</h2>';
                                    Html += '<p>' + result[i].phone + '</p>';
                                    Html += '<button class="button button-small button-positive code-right" onclick="sendInviteMessage(\'' + result[i].id + '\',this)">邀请</button>';
                                    Html += '</div>';
                                }
                                Html.CreateHtml('Scroll');
                            }
                        } else {
                            Message.Alert('查询不到该师傅信息！')
                        }
                    }
                }
            });
        }

        var loghub = $.connection.log;
        var imhub = $.connection.ydjIMHub;
        //连接消息通道并获取消息
        // Start the connection.
        $.connection.hub.start().done(function (r) {

        })
        .fail(function (e) {
            console.info(e);
        });

        var teamId = GetQueryString('teamid');
        var tName = GetQueryString('tname');
        var that;
        function sendInviteMessage() {
            that = arguments[1];
            RemoveClass(that, 'button-positive');
            AddClass(that, 'btn-bg-grey');
            that.setAttribute('disabled', 'disabled');

            if (!String(arguments[0]).isNullOrEmpty()) {

                imhub.server.inviteMaster(arguments[0], teamId);
            }
        }

        imhub.client.onRecvError = function (errCode, error) {
            if (!error) {
                return;
            }

            setTimeout(function () {
                AddClass(that, 'btn-bg-blue');
                RemoveClass(that, 'btn-bg-grey');
                that.removeAttribute('disabled');
            }, 50000)

            Message.Box(error.Message, null, '确定');
        }

        imhub.client.onRecvSimpleMessage = function (type, messages) {
            if (!messages) {
                return;
            }

            setTimeout(function () {
                AddClass(that, 'btn-bg-blue');
                RemoveClass(that, 'btn-bg-grey');
                that.removeAttribute('disabled');
            }, 50000)

            Message.Box(messages, null, '确定');
        }

    </script>
</body>
</html>