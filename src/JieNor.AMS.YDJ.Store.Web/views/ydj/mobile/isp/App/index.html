<html>
<head>
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1, user-scalable=no" />
    <title>易到家（师傅端）首页</title>
    <link href="/fw/css/ydj/mobile/isp/App/ionic.css" rel="stylesheet" />
    <link href="/fw/css/ydj/mobile/isp/App/override.css" rel="stylesheet" />
</head>
<body>
    <div class="main-content">
        <!--首页-->
        <div class="bar bar-header bar-white">
            <div menu-toggle="left" class="button button-icon icon ion-navicon" id="menu-toggle"></div>
            <div class="title">易到家（师傅端）首页</div>
        </div>
    </div>

    <div class="left-content hide">
        <!--个人主页-->
        <div class="bar bar-header user-center black">
            <a onclick="Redirect('/views/ydj/mobile/isp/App/personalInfo.html')"><img src="/fw/css/ydj/mobile/isp/App/images/App-logo.png" class="user-image" /></a>
            <span class="user-account">131****4113</span>
        </div>
        <div class="content has-header personal-info" id="Scroll">
            <div class="list">
                <label class="item item-input" onclick="Redirect('/views/ydj/mobile/isp/App/my_team.html')">
                    <i class="icon ion-ios-people-outline"></i>我的团队
                </label>
                <label class="item item-input">
                    <i class="icon ion-ios-briefcase-outline"></i>我的钱包
                </label>
                <label class="item item-input">
                    <i class="icon ion-ios-home-outline"></i>师傅学堂
                </label>
                <label class="item item-input">
                    <i class="icon ion-ios-keypad-outline"></i>更多
                </label>
            </div>
        </div>
    </div>

    <script src="/fw/js/ydj/mobile/isp/App/Comm.js"></script>
    <script>
        var flag = false;

        DomTag('menu-toggle').onclick = function () {
            if (flag == false) {
                AddClass(DomTag('.main-content')[0], 'margin-left');
                RemoveClass(DomTag('.left-content')[0], 'hide');
                flag = true;
            } else {
                RemoveClass(DomTag('.main-content')[0], 'margin-left');
                AddClass(DomTag('.left-content')[0], 'hide');
                flag = false;
            }
        }
    </script>
</body>
</html>