<form action="###" class="form-horizontal">
    <div class="portlet box yellow-casablanca">
        <div class="portlet-title">
            <div class="caption">基本信息</div>
            <div class="tools"><a href="###" class="collapse"></a></div>
        </div>
        <div class="portlet-body form">
            <div class="form-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-md-3 control-label">编码</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off"
                                           name="fnumber" placeholder="编码">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label"><span class="required">*</span>外部接口编码</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off" required
                                           name="fapino" placeholder="外部接口编码">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label"><span class="required">*</span>外部接口名称</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off" required
                                           name="fname" placeholder="外部接口名称">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label"><span class="required">*</span>外部应用</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="lookup" class="form-control" autocomplete="off" required
                                           name="fextappid" placeholder="外部应用" maxlength="50" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label"><span class="required">*</span>同步方式</label>
                            <div class="col-md-8">
                                <select class="form-control select2me simple" name="fsynctype"></select>
                            </div>
                        </div>
                        <!-- <div class="form-group">
                            <label class="col-md-3 control-label">操作码</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off"
                                           name="fopcode" placeholder="操作码" maxlength="50" />
                                    <span class="help-block">用于执行此操作码时，定位业务对象映射来同步。</span>
                                </div>
                            </div>
                        </div> -->
                        <!-- <div class="form-group">
                            <label class="col-md-3 control-label">输入结果类型</label>
                            <div class="col-md-8">
                                <select class="form-control select2me simple" name="fresulttypein"></select>
                            </div>
                        </div> -->
                        <!-- <div class="form-group">
                            <label class="col-md-3 control-label">输入结果</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off"
                                           name="fresultin" placeholder="接口结果" maxlength="50" />
                                    <span class="help-block">如果是内表，此处填写内表标识，多个标识之间用逗号“,”隔开</span>
                                </div>
                            </div>
                        </div> -->
                        <!-- <div class="form-group">
                            <label class="col-md-3 control-label">输出结果类型</label>
                            <div class="col-md-8">
                                <select class="form-control select2me simple" name="fresulttype"></select>
                            </div>
                        </div> -->
                        <!-- <div class="form-group">
                            <label class="col-md-3 control-label">输出结果</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off"
                                           name="fresult" placeholder="接口结果" maxlength="50" />
                                    <span class="help-block">如果是内表，此处填写内表标识，多个标识之间用逗号“,”隔开</span>
                                </div>
                            </div>
                        </div> -->
                        <!-- <div class="form-group">
                            <label class="col-md-3 control-label">更新明细类型</label>
                            <div class="col-md-8">
                                <select class="form-control select2me simple" name="fentityoptype"></select>
                            </div>
                        </div> -->
                        <div class="form-group">
                            <label class="col-md-3 control-label">协同顺序</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off" required
                                           name="fsyncorder" placeholder="协同顺序" maxlength="50" />
                                    <span class="help-block">为0时：系统自动推断，对有些对象间互相关联时，保留人工干预的机会，不受系统自动推断的影响。</span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label"></label>
                            <div class="col-md-9">
                                <div class="checkbox-list">
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="fsyncaftersave"> 保存时同步
                                    </label>
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="fsyncaftersubmit"> 提交时同步
                                    </label>
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="fsyncafteraudit"> 审核时同步
                                    </label>
                                </div>

                                <div class="checkbox-list">
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="fisasync"> 异步执行
                                    </label>
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="fsyncafterforbid"> 禁用同步
                                    </label>
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="fsyncafterunforbid"> 反禁用同步
                                    </label>
                                </div>
                                <span class="help-block">异步执行勾选后，将不影响主流程执行。异步执行只作用于：保存时同步、提交时同步和审核时同步</span>

                                <!-- <div class="checkbox-list">
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="fsyncafterunaudit"> 反审核时同步
                                    </label>
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="fsyncafterdelete"> 删除时同步
                                    </label>
                                </div> -->


                                <div class="checkbox-list">
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="fsynctimer"> 定时同步
                                    </label>
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="fsyncmanual"> 手工同步
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- <div class="form-group">
                            <label class="col-md-3 control-label"></label>
                            <div class="col-md-9">
                                <div class="checkbox-list">
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="fisasync"> 异步执行
                                    </label>
                                </div>
                                <span class="help-block">异步执行勾选后，将不影响主流程执行。</span>
                            </div>
                        </div> -->
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-md-3 control-label"><span class="required">*</span>外部业务对象</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off" required
                                           name="fextobjectid" placeholder="外部业务对象" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label"><span class="required">*</span>我方业务对象</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="lookup" class="form-control" autocomplete="off" required
                                           name="fmyobjectid" placeholder="我方业务对象" maxlength="50" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label"><span class="required">*</span>同步方向</label>
                            <div class="col-md-8">
                                <select class="form-control select2me simple" name="fsyncdir"></select>
                            </div>
                        </div>
                        <!--
                        <div class="form-group">
                            <label class="col-md-3 control-label"><span class="required">*</span>同步方式</label>
                            <div class="col-md-8">
                                <select class="form-control select2me simple" name="fsyncway"></select>
                            </div>
                        </div>-->
                        <div class="form-group">
                            <label class="col-md-3 control-label">标记位标识</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off"
                                           name="fresultflag" placeholder="标记位标识" maxlength="50" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label">消息标识</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off"
                                           name="fresultmsg" placeholder="消息标识" maxlength="50" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label">成功标识</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off"
                                           name="fsuccessflag" placeholder="成功标识" maxlength="50" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label">我方协同结果配置</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off"
                                           name="fmyresultconfig" placeholder="我方协同结果配置" maxlength="50" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label">过滤条件</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <textarea class="form-control" autocomplete="off"
                                              name="ffilterstring" placeholder="过滤条件" style="height:129px;"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-md-3 control-label">最大处理数</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off"
                                           name="fmaxsize" placeholder="最大处理数" maxlength="50" />
                                    <span class="help-block">为了性能考虑，下载数据后会有个最大处理数的控制，该参数表示每一次下载后的最大处理记录数。</span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label">批处理数</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off"
                                           name="fbatchsize" placeholder="批处理数" maxlength="50" />
                                    <span class="help-block">数据量较大的情况下会分批次保存下载的数据，该参数表示每一批次要处理的记录数。</span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label">协同日期</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off"
                                           name="fsyncdate" placeholder="协同日期" maxlength="50" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label">协同状态</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off"
                                           name="fsyncstatus" placeholder="协同状态" maxlength="50" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label">有效协同状态值</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off"
                                           name="fsyncstatusvalues" placeholder="有效协同状态值" maxlength="50" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label">其它同步参数</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <textarea class="form-control" autocomplete="off"
                                              name="fextendparam" placeholder="其它同步参数" style="height:105px;"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label">备注</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <textarea class="form-control" autocomplete="off"
                                              name="fdescription" placeholder="备注" style="height:105px;"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="portlet box yellow-casablanca">
        <div class="portlet-title">
            <div class="caption">接口参数明细</div>
            <div class="tools"><a href="###" class="collapse"></a></div>
        </div>
        <div class="portlet-body form">
            <div class="form-body">
                <div class="row">
                    <div class="col-md-12">
                        <table entryid="fparamentry" data-options="allowAdd:true,allowDel:true,allowEdit:true"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="portlet box yellow-casablanca">
        <div class="portlet-title">
            <div class="caption">字段映射明细</div>
            <div class="tools"><a href="###" class="collapse"></a></div>
        </div>
        <div class="portlet-body form">
            <div class="form-body">
                <div class="row">
                    <div class="col-md-12">
                        <table entryid="ffieldentry" data-options="allowAdd:true,allowDel:true,allowEdit:true"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>

</form>
