<style>
    .i-left {
        padding-left: 19px;
        padding-top: 14px;
    }

    .i-right {
        padding-top: 14px;
    }

    .icon-upload {
        background-color: #529DE3;
        color: white;
        padding: 10px;
        width: 100px;
        text-align: center;
        cursor: pointer;
        border-radius: 3px !important;
    }
</style>
<form action="###" class="form-horizontal">

    <!--基本信息-->
    <div class="portlet box yellow-casablanca base-mes">
        <div class="portlet-title">
            <div class="caption">过滤条件</div>
        </div>
        <div class="portlet-body form">
            <div class="form-body">
                <!--合同交货日期从-->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-3 control-label"><span class="required">*</span>合同交货日期</label>
                            <div class="col-md-8 cols">
                                <div class="input-icon right input-group date date-picker">
                                    <input type="text" class="form-control" name="fdatefrom" placeholder="不填写默认本月" required />
                                    <span class="input-group-addon">
                                        <i class="fa fa-calendar"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-3 control-label"><span class="required">*</span>至</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group date date-picker">
                                    <input type="text" class="form-control" name="fdateto" placeholder="不填写默认本月" required />
                                    <span class="input-group-addon">
                                        <i class="fa fa-calendar"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-3 control-label">品牌</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <input type="lookup" class="form-control" autocomplete="off"
                                           name="fbrandid" placeholder="品牌" data-options="multSelect:true" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--数据状态-->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-3 control-label"><span class="required">*</span>订单状态</label>
                            <div class="col-md-8">
                                <select class="form-control select-mult" name="forderstatus"></select>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 基础资料选择，可以多选按分号隔开-->
                <div class="row">
                    <!-- 商品-->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-3 control-label">商品</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <input type="lookup" class="form-control" autocomplete="off"
                                           name="fmaterialid" placeholder="商品" data-options="multSelect:true" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 商品类别-->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-3 control-label">商品类别</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <input type="lookup" class="form-control" autocomplete="off"
                                           name="fcategoryid" placeholder="商品类别" data-options="multSelect:true" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <!-- 是否非标商品-->
                        <div class="form-group">
                            <label class="col-md-3 control-label">是否非标</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <select class="form-control select2me simple"
                                            name="fisbz" placeholder="是否非标商品"></select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <!-- 是否定制商品-->
                        <div class="form-group">
                            <label class="col-md-3 control-label">是否定制</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <select class="form-control select2me simple"
                                            name="fisdz" placeholder="是否定制商品"></select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-3 control-label">&nbsp;</label>
                            <div class="col-md-8">
                                <div class="checkbox-list">
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="fisshow">无建议采购量不显示
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                 
                </div>
            </div>
        </div>
    </div>


</form>