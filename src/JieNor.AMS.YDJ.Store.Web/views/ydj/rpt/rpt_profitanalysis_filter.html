<style>
    .i-left {
        padding-left: 19px;
        padding-top: 14px;
    }

    .i-right {
        padding-top: 14px;
    }

    .icon-upload {
        background-color: #529DE3;
        color: white;
        padding: 10px;
        width: 100px;
        text-align: center;
        cursor: pointer;
        border-radius: 3px !important;
    }
</style>
<form action="###" class="form-horizontal">

    <!--基本信息-->
    <div class="portlet box yellow-casablanca base-mes">
        <div class="portlet-title">
            <div class="caption">过滤条件</div>
        </div>
        <div class="portlet-body form">
            <div class="form-body">
                <!-- 销售日期-->  
                <div class="row">                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-3 control-label"><span class="required">*</span>起始日期</label>
                            <div class="col-md-8 cols">
                                <div class="input-icon right input-group date date-picker">
                                    <input type="text" class="form-control" name="fdatefrom" placeholder="销售日期从" required />
                                    <span class="input-group-addon">
                                        <i class="fa fa-calendar"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-3 control-label"><span class="required">*</span>至</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group date date-picker">
                                    <input type="text" class="form-control" name="fdateto" placeholder="销售日期至" required />
                                    <span class="input-group-addon">
                                        <i class="fa fa-calendar"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 销售部门-->
                <!-- 基础资料选择，可以多选按分号隔开-->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-3 control-label">销售部门</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <input type="lookup" class="form-control" autocomplete="off"
                                           name="fdeptid" placeholder="销售部门" data-options="multSelect:true" />
                                </div>
                            </div>
                        </div>
                    </div>                  
                </div>

                <!-- 销售员-->
                <!-- 基础资料选择，可以多选按分号隔开-->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-3 control-label">销售员</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <input type="lookup" class="form-control" autocomplete="off"
                                           name="fstaffid" placeholder="销售员" data-options="multSelect:true" />
                                </div>
                            </div>
                        </div>
                    </div>                
                </div>

                <!-- 客户-->
                <!-- 基础资料选择，可以多选按分号隔开-->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-3 control-label">客户</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <input type="lookup" class="form-control" autocomplete="off"
                                           name="fcustomerid" placeholder="客户" data-options="multSelect:true" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 合同编号-->
                <!--按编号模糊查询-->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-3 control-label">合同编号</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <input type="text" class="form-control" autocomplete="off" name="fbillno" placeholder="合同编号" />
                                </div>
                            </div>
                        </div>
                    </div>                 
                </div>

                <!-- 成本取值来源-->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-3 control-label">成本取值来源</label>
                            <div class="col-md-8">
                                <select class="form-control select2me simple" name="fsourcecost"> </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
      

        <!--<div class="portlet-title">
            <div class="caption">汇总依据条件</div>
        </div>
        <div class="portlet-body form">           
                <div class="form-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-3 control-label">汇总依据条件</label>
                                <div class="col-md-8">
                                    <select class="form-control select2me dynamic"  name="fcustomeruniqueone"> </select>
                                    <select class="form-control select2me dynamic" name="fcustomeruniquetwo"> </select>
                                    <select class="form-control select2me dynamic" name="fcustomeruniquethree"> </select>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
        </div>-->
    </div>



</form>