<style>
    .viewer-title
    {
        overflow: initial;
    }

    .schel
    {
        width: 100%
    }

    .schel .caption-one
    {
        float: left;
    }

    .schel .caption-two
    {
        float: right;
        font-size: 14px;
        color: #6B8299;
        font-weight: normal;
        width: 72px;
        text-align: right;
        background: url(/fw/images/scheladd.png) no-repeat left top;
        cursor: pointer;
    }

    .tabs.buts ul
    {
        border-top: 1px solid #E1E1E1;
        overflow: hidden;
    }

    .tabs.buts, .tabs.buts-two
    {
        clear: both;
    }

    .tabs.buts li
    {
        float: left;
        width: 50%;
        height: 40px;
        line-height: 56px;
    }


    .tabs.buts li
    {
        position: relative
    }

    .tabs.buts li a
    {
        display: block;
        width: 100%;
        height: 100%;
    }

    .tabs.buts li a div
    {
        display: block;
        width: 32px;
        height: 24px;
        position: absolute;
        left: 42%;
        top: 22%;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        -moz-background-size: 100% 100%;
    }

    .tabs.buts li
    {
        background-color: #FFFFFF;
    }

    .tabs.buts li.active
    {
        background-color: #F1F1F1;
    }

    .tabs.buts li:nth-child(1) a div
    {
        background-image: url(/fw/images/date-default.png);
    }

    .tabs.buts li:nth-child(2) a div
    {
        background-image: url(/fw/images/goodscar-default.png);
    }

    .tabs.buts-two li
    {
        float: left;
        margin-right: 16px;
    }

    .tabs.buts-two ul li a
    {
        font-family: SimSun;
        width: 64px;
        height: 42px;
        line-height: 42px;
        color: #8AADBB;
        text-decoration: none;
    }

    .tabs.buts-two ul li.active
    {
        border-bottom: 3px solid rgba(82,157,227,1);
    }

    .tabs.buts-two ul li.active a
    {
        color: #0589C7;
        font-size: 14px;
    }

    button[opcode=search]
    {
        border-radius: 3px !important;
        float: right
    }

    .page-content .tab-content .sch-style .tab-pane
    {
        min-height: 100px;
    }

    .tab-treecontent
    {
        max-height:500px;
        overflow-x:hidden;
        overflow-y:auto;
    }

    .sch-line
    {
        padding-left: 10px;
    }

    .sch-tree-control{
        position:absolute;
        left:-10px;
        top:50px;
        width:20px;
        height:20px;
        border: 1px solid #d4d4d4;
        border-radius: 50% !important;
        cursor:pointer;
        background-repeat:no-repeat;
        background-position:center;
    }
    .sch-tree-show-control{
        background-image:url(/fw/images/dashboard/prev.png);
    }
    .sch-tree-hide-control{
        background-image:url(/fw/images/dashboard/next.png);
        left:10px;
    }
    .sch-list-max-width{
        padding-left:34px;
    }
</style>
<form action="###" class="form-horizontal">
    <div class="row">
        <div id="scheduleTreeTab" class="col-md-3">
            <div class="portlet box yellow-casablanca" style="min-width:291px">
                <div class="portlet-title">
                    <div class="caption schel">
                        <div class="caption-one">排程计划列表</div>
                        <div class="caption-two" opcode="createsch">创建排单</div>
                    </div>
                    <div class="tabs buts">
                        <ul>
                            <li class="active">
                                <a data-toggle="tab" opcode="change" data-param="data:1">
                                    <div></div>
                                </a>
                            </li>
                            <li>
                                <a data-toggle="tab" opcode="change" data-param="data:2">
                                    <div></div>
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="tabs buts-two ">
                        <ul>
                            <li class="active">
                                <a href="#tab_1" data-toggle="tab" opcode="change" data-param="data:3">
                                    未排定
                                </a>
                            </li>
                            <li>
                                <a href="#tab_2" data-toggle="tab" opcode="change" data-param="data:4">
                                    已排定
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="portlet-body form">
                    <div class="form-body">
                        <div class="tab-content tab-treecontent">
                            <div class="tab-pane active" id="tab_1">
                                <div type="newdatetree" class="ztree"> </div>
                            </div>
                            <div class="tab-pane" id="tab_2">
                                <div class="form-group ">
                                    <label class="col-md-3 control-label">日期区间</label>
                                    <div class="col-md-8 cols">
                                        <div class="input-icon right datectl">
                                            <input type="text" class="form-control daterange" section="30" name="fdatesec"
                                                   placeholder="日期区间">
                                            <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">开始时间</label>
                                    <div class="col-md-8 cols">
                                        <div class="input-group right">
                                            <div class="timepicker col-xs-12">
                                                <input type="timepicker" class="form-control" autocomplete="off"
                                                       name="fstarttime_m" value="" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">结束时间</label>
                                    <div class="col-md-8 cols">
                                        <div class="input-group right">
                                            <div class="timepicker col-xs-12">
                                                <input type="timepicker" class="form-control" autocomplete="off"
                                                       name="fendtime_m" value="" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-md-11 cols">
                                        <button id="" type="button" class="btn btn-sm btn-primary" opcode="search">搜索</button>
                                    </div>
                                </div>
                                <div type="olddatetree" class="ztree"> </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="scheduleList" class="col-md-9">
            <div class="sch-tree-control sch-tree-show-control" data-status="show" opcode="changeTreeVisibility"></div>

            <div class="portlet box yellow-casablanca draw-info">
                <div class="portlet-title">
                    <div class="caption">待排单据</div>
                    <div class="tools"><a href="###" class="collapse"></a></div>
                    <div class="row">
                        <div class="col-md-2">
                            <div class="input-icon right">
                                <input type="text" class="form-control" autocomplete="off" name="fsearchtext" placeholder="搜索框" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="col-md-4 control-label">源单类型</label>
                            <div class="col-md-6">
                                <select class="form-control select2me simple" name="fselectformid"></select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="col-md-4 control-label">收款状态</label>
                            <div class="col-md-6">
                                <select class="form-control select2me dynamic" name="freceiptstatus"></select>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <label class="col-md-7 control-label">齐套</label>
                            <div class="col-md-3">
                                <div class="checkbox-list">
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="fismatchall">
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <span class="btn btn-sm btn-primary" style="cursor: pointer;" opcode="searchwatingplan">搜索</span>
                        </div>
                    </div>
                </div>
                <div class="portlet-body form">
                    <div class="form-body">
                        <table entryid="fbillentity" data-options="allowAdd:false,allowDel:false,allowEdit:true,defRow:0,pager:true"></table>
                    </div>
                </div>
            </div>

            <div class="portlet box yellow-casablanca draw-info">
                <div class="portlet-title">
                    <div class="caption">待排单据明细</div>
                    <div class="tools" opcode="resize" data-param="data:['fdetailentity']"><a href="###" class="collapse"></a></div>
                </div>
                <div class="portlet-body form">
                    <div class="form-body">
                        <table entryid="fdetailentity"
                               data-options="allowAdd:false,allowDel:false,allowEdit:true,defRow:0,retainLastRow:false,caption:'商品'" style="max-height:500px;"></table>
                    </div>
                </div>
            </div>

            <div class="portlet box yellow-casablanca draw-info">
                <div class="portlet-title">
                    <div class="caption">已排单据明细</div>
                    <div class="tools" opcode="resize" data-param="data:['fscheduleentity']"><a href="###" class="collapse"></a></div>
                </div>
                <div class="portlet-body form">
                    <div class="form-body">
                        <table entryid="fscheduleentity"
                               data-options="allowAdd:false,allowDel:true,allowEdit:true,defRow:0,retainLastRow:false,caption:'商品明细'" style="max-height:500px;"></table>
                    </div>
                </div>
            </div>

            <div class="portlet box yellow-casablanca fill-top">
                <div class="portlet-title">
                    <div class="caption">
                        基本信息
                    </div>
                    <div class="tools">
                        <a href="javascript:;" class="collapse"></a>
                    </div>
                </div>
                <div class="portlet-body form">
                    <div class="form-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="col-md-3 control-label"><span class="required">*</span>排程编号</label>
                                    <div class="col-md-8">
                                        <div class="input-icon right">
                                            <input type="text" class="form-control" autocomplete="off" name="fschedulebillno" />
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label"><span class="required">*</span>排程日期</label>
                                    <div class="col-md-8">
                                        <div class="input-icon right input-group date date-picker">
                                            <input type="text" class="form-control" autocomplete="off" required
                                                   name="fdate" placeholder="排程日期" />
                                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label"><span class="required">*</span>开始时间</label>
                                    <div class="col-md-8 cols">
                                        <div class="input-group right">
                                            <div class="timepicker col-xs-12">
                                                <input type="timepicker" class="form-control" required autocomplete="off"
                                                       name="fstarttime" value="" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label"><span class="required">*</span>结束时间</label>
                                    <div class="col-md-8 cols">
                                        <div class="input-group right">
                                            <div class="timepicker col-xs-12">
                                                <input type="timepicker" class="form-control" required autocomplete="off"
                                                       name="fendtime" value="" />
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-md-3 control-label">车辆</label>
                                    <div class="col-md-8">
                                        <div class="input-icon right input-group">
                                            <i class="fa"></i>
                                            <input type="lookup" class="form-control" autocomplete="off"
                                                   name="fcarid" placeholder="车辆" />
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="col-md-3 control-label">收货日期</label>
                                    <div class="col-md-8">
                                        <div class="input-icon right input-group date date-picker">
                                            <input type="text" class="form-control" autocomplete="off"
                                                   name="fstockdateto" placeholder="收货日期" />
                                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">收货部门</label>
                                    <div class="col-md-8">
                                        <div class="input-icon right input-group">
                                            <i class="fa"></i>
                                            <input type="lookup" class="form-control" autocomplete="off"
                                                   name="fstockdeptidto" placeholder="收货部门" />
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">收货人</label>
                                    <div class="col-md-8">
                                        <div class="input-icon right input-group">
                                            <i class="fa"></i>
                                            <input type="lookup" class="form-control" autocomplete="off"
                                                   name="fstockstaffidto" placeholder="收货人" />
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-md-3 control-label">排单员</label>
                                    <div class="col-md-8">
                                        <div class="input-icon right input-group">
                                            <i class="fa"></i>
                                            <input type="lookup" class="form-control" autocomplete="off"
                                                   name="fschedulestaffid" placeholder="排单员" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="col-md-3 control-label">发货日期</label>
                                    <div class="col-md-8">
                                        <div class="input-icon right input-group date date-picker">
                                            <input type="text" class="form-control" autocomplete="off"
                                                   name="fstockdate" placeholder="发货日期" />
                                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">发货部门</label>
                                    <div class="col-md-8">
                                        <div class="input-icon right input-group">
                                            <i class="fa"></i>
                                            <input type="lookup" class="form-control" autocomplete="off"
                                                   name="fstockdeptid" placeholder="发货部门" />
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">发货人</label>
                                    <div class="col-md-8">
                                        <div class="input-icon right input-group">
                                            <i class="fa"></i>
                                            <input type="lookup" class="form-control" autocomplete="off"
                                                   name="fstockstaffid" placeholder="发货人" />
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-md-3 control-label">排单部门</label>
                                    <div class="col-md-8">
                                        <div class="input-icon right input-group">
                                            <i class="fa"></i>
                                            <input type="lookup" class="form-control" autocomplete="off"
                                                   name="fscheduledeptid" placeholder="排单部门" />
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
