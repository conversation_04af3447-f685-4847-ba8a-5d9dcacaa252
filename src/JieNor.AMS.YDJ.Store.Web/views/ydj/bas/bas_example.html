<form action="###" class="form-horizontal">

    <div class="portlet box yellow-casablanca">
        <div class="portlet-title">
            <div class="caption">基本的常用字段</div>
            <div class="tools"><a href="###" class="collapse"></a></div>
        </div>
        <div class="portlet-body form">
            <div class="form-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-md-4 control-label"><span class="required">*</span>编码字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <input type="text" class="form-control" autocomplete="off"
                                           name="fnumber" placeholder="编码字段" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label"><span class="required">*</span>名称字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <input type="text" class="form-control" autocomplete="off"
                                           name="fname" placeholder="名称字段" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label"><span class="required">*</span>文本字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off" required
                                           name="ftext" placeholder="文本字段" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label"><span class="required">*</span>整数字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off" required
                                           name="finteger" placeholder="整数字段" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label"><span class="required">*</span>小数字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off" required
                                           name="fdecimal" placeholder="小数字段" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label"><span class="required">*</span>数量字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off" required
                                           name="fqty" placeholder="数量字段" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label"><span class="required">*</span>单价字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off" required
                                           name="fprice" placeholder="单价字段" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label"><span class="required">*</span>金额字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off" required
                                           name="famount" placeholder="金额字段" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">省市区</label>
                            <div class="col-md-8">
                                <div class="city-border-bottom input-group right">
                                    <div class="col-xs-12">
                                        <input type="city" name="fcity" province="fprovince" city="fcity" region="fregion"
                                               class="form-control" autocomplete="off" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">户型</label>
                            <div class="col-md-8 cols">
                                <div class="housetype-border-bottom input-group right">
                                    <div class="col-xs-12">
                                        <input type="housetype" name="froom" room="froom" hall="fhall" toilet="ftoilet" balcony="fbalcony"
                                               class="form-control" autocomplete="off" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label"><span class="required">*</span>辅助资料字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <select class="form-control select2me dynamic" required
                                            name="fcombo" placeholder="辅助资料字段"></select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">多选辅助资料字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <select class="form-control select-mult"
                                            name="fmulcombo" placeholder="多选辅助资料字段"></select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">简单下拉框字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <select class="form-control select2me simple"
                                            name="fsimpleselect" placeholder="简单下拉框字段"></select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">单据状态字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <select class="form-control select2me dynamic"
                                            name="fbillstatus" placeholder="单据状态字段"></select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">单据类型字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <select class="form-control select2me billtype"
                                            name="fbilltype" placeholder="单据类型字段"></select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">关闭状态字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <select class="form-control select2me simple"
                                            name="fclosestatus" placeholder="关闭状态字段"></select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">标签字段</label>
                            <div class="col-md-8">
                                <input type="hidden" class="form-control select-tags"
                                       name="ftag" max="5" maxlength="5">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">源单类型类型</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <input type="lookup" class="form-control" autocomplete="off"
                                           name="fsourcetype" placeholder="源单类型类型" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">源单编号字段</label>
                            <div class="col-md-8">
                                <input type="text" class="form-control" autocomplete="off"
                                       name="fsourcenumber" placeholder="源单编号字段" />
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-md-4 control-label"><span class="required">*</span>基础资料字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="lookup" class="form-control" autocomplete="off" required
                                           name="fbasedata" placeholder="基础资料字段" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">基础资料属性字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off"
                                           name="fbasedataprop" placeholder="基础资料属性字段" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">计量单位字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="lookup" class="form-control" autocomplete="off"
                                           name="funitid" placeholder="计量单位字段" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label"><span class="required">*</span>日期字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group date date-picker">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control" autocomplete="off" required
                                           name="fdate" placeholder="日期字段" />
                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label"><span class="required">*</span>日期时间字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="text" class="form-control datetime form_datetime" autocomplete="off" required
                                           name="fdatetime" placeholder="日期时间字段" />
                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group ">
                            <label class="col-md-4 control-label">日期区间</label>
                            <div class="col-md-8">
                                <div class="input-icon right datectl">
                                    <input type="text" class="form-control daterange" section="30" name="fdaterange" placeholder="日期区间">
                                    <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">时间字段</label>
                            <div class="col-md-8">
                                <div class="input-group right">
                                    <div class="timepicker col-xs-12">
                                        <input type="timepicker" class="form-control" autocomplete="off"
                                               name="ftime" placeholder="时间字段" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">多选基础资料字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="lookup" class="form-control" autocomplete="off"
                                           name="fmulbasedata" data-options="multSelect:true" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">辅助属性字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="auxprop" class="form-control" autocomplete="off"
                                           name="fauxproperty" placeholder="辅助属性字段" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">表达式字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="expr" class="form-control" autocomplete="off"
                                           name="fexpr" placeholder="表达式字段" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">动态基础资料字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="dynbdselect" class="form-control" autocomplete="off"
                                           name="fdynbasedata" placeholder="动态基础资料字段" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">多选动态基础资料字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="dynbdselect" class="form-control" autocomplete="off"
                                           name="fmuldynbasedata" placeholder="多选动态基础资料字段" data-options="multSelect:true" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">货主类型</label>
                            <div class="col-md-8">
                                <div class="input-icon right">
                                    <select class="form-control select2me mulclasstype"
                                            name="fownertype" placeholder="货主类型"></select>
                                </div>
                                <span class="help-block">多类别基础资料类型字段</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">货主</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="lookup" class="form-control" autocomplete="off"
                                           name="fownerid" placeholder="货主" />
                                </div>
                                <span class="help-block">多类别基础资料字段</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">仓库</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="lookup" class="form-control" autocomplete="off"
                                           name="fhouseid" placeholder="仓库" />
                                </div>
                                <span class="help-block">基础资料字段</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">仓位</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="lookup" class="form-control" autocomplete="off"
                                           name="flocationid" placeholder="仓位" />
                                </div>
                                <span class="help-block">基础资料明细字段</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">&nbsp;</label>
                            <div class="col-md-8">
                                <div class="checkbox-list">
                                    <label class="checkbox-inline">
                                        <input type="checkbox" name="fcheckbox"> 复选框字段
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-md-4 control-label">单文件字段</label>
                            <div class="col-md-8">
                                <div class="uploader-file mul-type" name="ffile" data-opt-show="down:true"
                                     caution="" hasbutton="" limit="1" sizelimit="" allowExt=""></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">多文件字段</label>
                            <div class="col-md-8">
                                <div class="uploader-file mul-type" name="fmulfile" data-opt-show="down:true"
                                     caution="" hasbutton="" limit="10" sizelimit="" allowExt=""></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">单图片字段</label>
                            <div class="col-md-8">
                                <div class="uploader-file" name="fimage" data-opt-show="down:true"
                                     caution="" hasbutton="" limit="1" sizelimit="" allowExt=""></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">多图片字段</label>
                            <div class="col-md-8">
                                <div class="uploader-file mul-type" name="fmulimage" data-opt-show="down:true"
                                     caution="" hasbutton="" limit="10" sizelimit="" allowExt=""></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">商品图片字段</label>
                            <div class="col-md-8">
                                <div class="uploader-file mul-type" name="fmtrlimage" data-opt-show="down:true"
                                     caution="" hasbutton="" limit="10" sizelimit="" allowExt=""></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">二维码</label>
                            <div class="col-md-8">
                                <div class="barcodefield" name="fqrcode" codetype="QR"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">条形码</label>
                            <div class="col-md-8">
                                <div class="barcodefield" name="fbarcode" codetype="BR"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">只读图片字段</label>
                            <div class="col-md-8">
                                <ul class="jsonimage" name="fjsonimage">
                                    该字段很少用，目前只有在协同企业->企业详情中的Logo有用到，具体视图配置请参考：\views\ydj\ste\ydj_companyinfo.html
                                </ul>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">用户签名字段</label>
                            <div class="col-md-8">
                                <div class="ppsign" name="fusersign"></div>
                                <span class="help-block">需要配置外部设备，比如：电子签名设备</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">颜色字段</label>
                            <div class="col-md-8 colorfield">
                                <input class="color-box" name="fcolor" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="portlet box yellow-casablanca">
        <div class="portlet-title">
            <div class="caption">单据体</div>
            <div class="tools"><a href="###" class="collapse"></a></div>
        </div>
        <div class="portlet-body form">
            <div class="form-body">
                <div class="row">
                    <div class="col-md-12">
                        <table entryid="fentity" data-options="allowAdd:true,allowDel:true,allowEdit:true"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="portlet box yellow-casablanca">
        <div class="portlet-title">
            <div class="caption">子单据体</div>
            <div class="tools"><a href="###" class="collapse"></a></div>
        </div>
        <div class="portlet-body form">
            <div class="form-body">
                <div class="row">
                    <div class="col-md-12">
                        <table entryid="fdetail" data-options="allowAdd:true,allowDel:true,allowEdit:true"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="portlet box yellow-casablanca">
        <div class="portlet-title">
            <div class="caption">树形单据体</div>
            <div class="tools"><a href="###" class="collapse"></a></div>
        </div>
        <div class="portlet-body form">
            <div class="form-body">
                <div class="row">
                    <div class="col-md-12">
                        <table entryid="ftreeentity" copydata="true" data-options="allowAdd:true,allowDel:true,allowEdit:true"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="portlet box yellow-casablanca">
        <div class="portlet-title">
            <div class="caption">单据体 - 组合控件示例（业务字段来源、业务字段类型、业务字段值）</div>
            <div class="tools"><a href="###" class="collapse"></a></div>
        </div>
        <div class="portlet-body form">
            <div class="form-body">
                <div class="row">
                    <div class="col-md-12">
                        <table entryid="ftestentity" data-options="allowAdd:true,allowDel:true,allowEdit:true"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="portlet box yellow-casablanca">
        <div class="portlet-title">
            <div class="caption">依赖业务对象的字段</div>
            <div class="tools"><a href="###" class="collapse"></a></div>
        </div>
        <div class="portlet-body form">
            <div class="form-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-md-4 control-label">业务对象字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="lookup" class="form-control" autocomplete="off"
                                           name="fbizobject" placeholder="业务对象字段" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">人员字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="bizperson" class="form-control" autocomplete="off"
                                           name="fperson" placeholder="人员字段" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-md-4 control-label">字段模型字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="dropfield" class="form-control" autocomplete="off"
                                           name="ffieldmodel" placeholder="字段模型字段" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-4 control-label">多选字段模型字段</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="dropfield" class="form-control" autocomplete="off"
                                           name="fmulfieldmodel" placeholder="多选字段模型字段" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-md-4 control-label">表达式面板字段</label>
                            <div class="col-md-8">
                                该字段很少用，且封装的不够完善，目前只有审批流节点页面中有用到，具体视图配置请参考：\views\bpm\bpm_pdstart.html
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="portlet box yellow-casablanca">
        <div class="portlet-title">
            <div class="caption">条件面板字段（依赖业务对象字段）</div>
            <div class="tools"><a href="###" class="collapse"></a></div>
        </div>
        <div class="portlet-body form">
            <div class="form-body">
                <div class="filterpanel" name="ffilterpanel"></div>
            </div>
        </div>
    </div>

    <div class="portlet box yellow-casablanca">
        <div class="portlet-title">
            <div class="caption">富文本字段</div>
            <div class="tools"><a href="###" class="collapse"></a></div>
        </div>
        <div class="portlet-body form">
            <div class="form-body">
                <textarea type="cke" name="frichtext" style="width:100%;height:200px;" data-options="zIndex:2,toolbars:[['undo', 'redo','bold', 'italic','backcolor','justifyleft', 'justifycenter', 'justifyright','fontsize', 'underline','forecolor','insertorderedlist', 'inserttable','insertunorderedlist','insertimage', 'attachment', 'spechars']]"></textarea>
            </div>
        </div>
    </div>

</form>