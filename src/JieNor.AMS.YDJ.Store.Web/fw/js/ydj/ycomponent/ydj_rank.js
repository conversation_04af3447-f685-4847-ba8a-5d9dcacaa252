/*
@ sourceURL=/fw/js/ydj/ycomponent/ydj_rank.js
*/
; (function () {
    var ydj_rank = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        __extends(_child, _super);

        //初始化事件
        _child.prototype.onInitialized = function (args) {
            var that = this;
            that.setPermComboData();
            that.queryData();
        };

        //元素点击事件
        _child.prototype.onElementClick = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id) {
                case 'rank':
                    that.Model.removeClass({ id: '.rank', value: 'active' });
                    that.Model.addClass({ id: '.rank.' + e.param.dtType, value: 'active' });
                    break;
            };
            that.queryData();
        };

        _child.prototype.setPermComboData = function () {
            var that = this;

            var comboData = [{ id: "type_01", name: "所有" }, { id: "type_02", name: "已审核" }, { id: "type_03", name: "未审核" }];
            that.Model.setComboData({ id: 'franktype', data: comboData });
            if (comboData.length > 0) {
                that.Model.setValue({
                    id: 'franktype',
                    value: {
                        id: comboData[0].id,
                        fname: comboData[0].name,
                        fnumber: comboData[0].name
                    }
                });
                that.Model.setVisible({ id: '.permselect', value: true });
            }
        };

        //字段值变化事件
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case "frankselect":
                    that.queryData();
                    break;
                case "franktype":
                    that.queryData();
                    break;
            }
        };

        //查询报表数据
        _child.prototype.queryData = function () {
            var that = this;
            var dtType = that.Model.getText({ id: '.active.rank' });
            var khType = that.Model.getSimpleValue({ id: "frankselect" });
            var statusType = that.Model.getSimpleValue({ id: "franktype" });
            //var dataPermId = that.Model.getSimpleValue({ id: "fpermselect" })//"mydepartment";
            var rptFormId = that.Model.viewModel.formId;

            that.Model.invokeFormOperation({
                id: 'loadrptdata',
                opcode: 'loadrptdata',
                param: {
                    formId: 'rpt_salesrpt',
                    domainType: Consts.domainType.report,
                    dtType: dtType,
                    khType: khType,
                    statusType: statusType,
                    //dataPermId: dataPermId,
                    rptFormId: rptFormId
                }
            });
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'loadrptdata':
                    if (isSuccess) {
                        that.renderChart(srvData);
                    }
                    break;
            }
        };

        //渲染图表
        _child.prototype.renderChart = function (srvData) {
            var that = this;
            if (!srvData) return;
            if (srvData.rptGridDataSource.length === 0) {
                that.Model.setVisible({ id: '.ranking', value: false });
                that.Model.setVisible({ id: '.ranking_nodata', value: true });
            } else {
                if (srvData.chartDataSource) {
                    that.Model.setVisible({ id: '.ranking', value: true });
                    that.Model.setVisible({ id: '.ranking_nodata', value: false });
                    var $chart = that.Model.getEleMent({ id: '.ranking' });
                    srvData.chartDataSource.rpt_salesrpt.option.title.text = "部门间排名";
                    var option = srvData.chartDataSource.rpt_salesrpt.option;
                    option.clear = true;
                    yiChart.init($chart, option);
                }
            }
        };

        return _child;
    })(BasePlugIn);
    window.ydj_rank = window.ydj_rank || ydj_rank;
})();