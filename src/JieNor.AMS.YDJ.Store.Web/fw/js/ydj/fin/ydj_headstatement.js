///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/ent/ydj_headstatement.js
*/
; (function () {
    var ydj_headstatement = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //初始化编辑页面插件
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
 
        };

        //字段值变化时的操作
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this; 
        };
        
        _child.prototype.onAfterDoOperation = function (e) {
            var that = this;

        }

        //表单元素被单击后
        _child.prototype.onElementClick = function (e) {
            var that = this; 
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            debugger;
            switch (e.opcode.toLowerCase()) {
                case 'confirmbill': 
                    e.result = true;

                    var cloneObj = that.Model.clone();
                    var cloneObjs = [];
                    cloneObjs.push(cloneObj);

                    that.Model.invokeFormOperation({
                        id: 'tbConfirmBill',
                        opcode: 'confirmbill',
                        billData: JSON.stringify(cloneObjs)
                    });
                    break;
                case 'unconfirmbill': 
                    e.result = true;
                    
                    var cloneObj = that.Model.clone();
                    var cloneObjs = [];
                    cloneObjs.push(cloneObj);

                    that.Model.invokeFormOperation({
                        id: 'tbUnconfirmBill',
                        opcode: 'unconfirmbill',
                        billData: JSON.stringify(cloneObjs)
                    });
                    break;
            }
        }

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) { 
            }
        };

        return _child;
    })(BillPlugIn);
    window.ydj_headstatement = window.ydj_headstatement || ydj_headstatement;
})();