/**
 * 属性值
 * @ sourceURL=/fw/js/ydj/sel/sel_propvalue.js
 */
; (function () {
    var sel_propvalue = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        }
        __extends(_child, _super);

        //编辑页面初始化事件
        _child.prototype.onBillInitialized = function (e) {
            var that = this;
            //处理【值类型】对应的相关字段信息
            var valueSourceId = that.Model.getSimpleValue({ id: 'fnosuit' });
            that.procValueSource(valueSourceId);
        };

        //页面初始化后触发
        _child.prototype.onInitialized = function (e) {
            var that = this;
            if (Consts.loginCompany.id != Consts.topcompanyid)
            {
                that.Model.setEnable({ id: '#tbNew', value: false });
                that.Model.setEnable({ id: '#tbCopy', value: false });
            } 
        };

        //字段值变化事件
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fnosuit':
                    that.procValueSource(e.value);
                    break;
            }
        };

        //处理【选择非标】对应的相关字段信息
        _child.prototype.procValueSource = function (valueSource) {
            var that = this;
            if (valueSource) {
                that.Model.setVisible({ id: '.y-fseries', value: true });
                that.Model.setVisible({ id: '.y-fcategory', value: true });
                that.Model.setVisible({ id: '.y-fbrand', value: true });
                that.Model.setVisible({ id: '.y-fconstraint', value: true });

            } else {
                that.Model.setVisible({ id: '.y-fseries', value: false });
                that.Model.setVisible({ id: '.y-fcategory', value: false });
                that.Model.setVisible({ id: '.y-fbrand', value: false });
                that.Model.setVisible({ id: '.y-fconstraint', value: false });
            }
        };

        return _child;
    })(BasePlugIn);
    window.sel_propvalue = sel_propvalue;
})();