
(function () {
    var sel_fittingsmap = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
            that.flag = true;
        };
        __extends(_child, _super);

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fcategoryid'://配件类型
                    e.result.filterString = "ffittings=@ffittings";
                    e.result.params = [
                        { fieldId: 'ffittings', pValue: '1' }//默认过滤勾选【配件】的《产品类别》配件
                    ];
                    break;
            }
        };
        //初始化编辑页面插件
        _child.prototype.onBillInitialized = function (e) {
            debugger;
            var that = this;
            that.hideOrShowEntryField();

        };
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fmatchbyproduct':
                    debugger;
                    that.hideOrShowEntryField();
                    break;
            }
        };


        //根据需转单显示或隐藏单据体部分列 
        _child.prototype.hideOrShowEntryField = function () {
            var that = this;
            var visible = that.Model.getSimpleValue({ id: 'fmatchbyproduct' });
            var entry = that.Model.getEntryData({ id: 'ffittingsmapentity' });
            for (var i = 0, j = entry.length; i < j; i++) {
                that.Model.setVisible({ id: 'fmaterialidnew', value: visible });
                that.Model.setVisible({ id: 'fmaterialidnew_fumber', value: visible });
            }
        };

        return _child;
    })(BasePlugIn);
    window.sel_fittingsmap = window.sel_fittingsmap || sel_fittingsmap;
})();