/// <reference path="/fw/js/platform/mvvm/baseplugin.js" />
//@ sourceURL=/fw/js/ydj/sel/sel_promotionform.js
//套件选配
; (function () {
    var sel_promotionform = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        }

        __extends(_child, _super);

        var inited = false;

        var ps = {
            combines: [],
            parentRow: "",
            suite: {}
        };

        //var selsettings = {};

        //平台在调用页面的 resizeForm 方法前触发
        _child.prototype.onBeforeResizeForm = function (args) {
            var that = this;
            var offset = 90; //偏移量
            if (args.dialogResize) {
                offset = 60;
            }
            var pageInfo = that.Model.getPageInfo();
            var baseInfoHeight = that.Model.getEleMent({ id: '.base-info' }).outerHeight(true);
            var entryHeight = pageInfo.height - baseInfoHeight - offset;
            that.Model.setEntryHeight({ id: 'fentity', value: entryHeight });
            that.Model.setEntryHeight({ id: 'fcombineentry', value: entryHeight });
        };

        _child.prototype.onInitialized = function (e) {
            var that = this;
            ps.parentpageid = that.Model.viewModel.cp.parentPageId;
            //兼容多个单据，通过单据里设置
            var parentPage = Index.getPage(ps.parentpageid);
            that.Model.selsettings = parentPage.Model.selsettings;
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            ;
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            var optData = e.result.operationResult.optionData;
            switch (e.opcode) {
                case 'addpromotion':
                    ;
                    if (isSuccess) {
                        ps.combines = JSON.parse(srvData);
                        that.Model.deleteEntryData({ id: "fcombineentry" });
                        if (!ps.combines) return;
                        var rowno;
                        var combineentrys
                        for (var i = 0; i < ps.combines.length; i++) {
                            var item = ps.combines[i];
                            var frow = {
                                fcombineid: { id: item.id, fname: item.fname, fnumber: item.fnumber },
                                fcombinenumber: item.fnumber,
                                fcombinedescription: item.fdescription,
                                fcombinerate: item.fcombinerate,
                                fcombinepriority: item.fcombinepriority
                            };
                            rowno = that.Model.addRow({ id: "fentity", data: frow });
                            //that.Model.setValue({ id: 'fcombineid', row: rowno, value: item.id. });
                            //that.Model.setValue({ id: 'fcombineid', row: rowno, value: item.id, opctx: { ignore: true }});
                            //组合商品明细
                            combineentrys = item.fcombineentry;
                        }
                        //第一次加载的时候只需要把最后一组商品明细加载出来
                        that.initProp(rowno, combineentrys);
                    } else {
                        that.Model.deleteEntryData({ id: "fcombineentry" });
                        that.Model.deleteEntryData({ id: "fentity" });
                        yiDialog.warn('当前送达方下面没有符合条件的套餐数据');
                    }
                    break;
                case 'savecombineinfo':
                    if (isSuccess && srvData && srvData.length > 0) {
                        debugger
                        let delRowCount = 0;
                        var parentPage = Index.getPage(ps.parentpageid);
                        var parentrows = parentPage.Model.getEntryData({ id: "fentity" });
                        for (var i = 0; i < parentrows.length; i++) {
                            var fproduct1 = parentrows[i].fmaterialid;
                            if (!fproduct1 || $.trim(fproduct1.id) == "") {
                                delRowCount++;
                                parentPage.Model.deleteRow({ id: that.Model.selsettings.fentity, row: parentrows[i].id });
                            }
                        }

                        for (var i = 0; i < srvData.length; i++) {
                            for (var j = 0; j < parentrows.length; j++) {
                                if (i == j && srvData[i].fmaterialid == parentrows[j].fmaterialid.id) {
                                    parentPage.Model.setValue({ id: "fbizqty", row: parentrows[j].id, value: srvData[i].fbizqty, opctx: { ignore: true, iscombine: true } });//采购数量
                                    parentPage.Model.setValue({ id: "fpromotion", row: parentrows[j].id, value: srvData[i].fpromotion }); //促销活动名称
                                    parentPage.Model.setValue({ id: "fcombinenumber", row: parentrows[j].id, value: srvData[i].fcombinenumber }); //组合促销编号
                                    parentPage.Model.setValue({ id: 'fcombineremark', row: parentrows[j].id, value: srvData[i].fcombineremark });//组合描述
                                    parentPage.Model.setValue({ id: 'fcombinepriority', row: parentrows[j].id, value: srvData[i].fcombinepriority });//促销活动优先级
                                    parentPage.Model.setValue({ id: 'fcombinerate', row: parentrows[j].id, value: srvData[i].fcombinerate });//折扣率
                                    parentPage.Model.setValue({ id: "fcombineqty", row: parentrows[j].id, value: srvData[i].fcombineqty });//套餐组合基数
                                    parentPage.Model.setValue({ id: "fgroupnumber", row: parentrows[j].id, value: srvData[i].fgroupnumber });//套餐组合基数
                                    break;
                                }
                            }
                        }

                        //for (var i = 0; i < srvData.length; i++) {
                        //    var row = parentrows.filter(c => c.fmaterialid.id == srvData[i].fmaterialid && c.fsoorderno == "" && c.fcombinenumber == "");
                        //    if (row != undefined && row.length > 0) {
                        //        parentPage.Model.setValue({ id: "fbizqty", row: row[0].id, value: srvData[i].fbizqty, opctx: { ignore: true } });//采购数量
                        //        parentPage.Model.setValue({ id: "fpromotion", row: row[0].id, value: srvData[i].fpromotion }); //促销活动名称
                        //        parentPage.Model.setValue({ id: "fcombinenumber", row: row[0].id, value: srvData[i].fcombinenumber }); //组合促销编号
                        //        parentPage.Model.setValue({ id: 'fcombineremark', row: row[0].id, value: srvData[i].fcombineremark });//组合描述
                        //        parentPage.Model.setValue({ id: 'fcombinepriority', row: row[0].id, value: srvData[i].fcombinepriority });//促销活动优先级
                        //        parentPage.Model.setValue({ id: 'fcombinerate', row: row[0].id, value: srvData[i].fcombinerate });//折扣率
                        //        parentPage.Model.setValue({ id: "fcombineqty", row: row[0].id, value: srvData[i].fcombineqty });//套餐组合基数
                        //        parentPage.Model.setValue({ id: "fgroupnumber", row: row[0].id, value: srvData[i].fgroupnumber });//套餐组合基数
                        //    }
                        //}


                        if (srvData.length > parentrows.length) {
                            //拆行
                            for (var i = srvData.length; i > 0; i--) {
                                if (i >= parentrows.length - delRowCount) {
                                    let index = i - 1;
                                    if (srvData[index].fmaterialid && srvData[index].fpartqty == 0) {
                                        var rowno = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                                        parentPage.Model.setValue({ id: "fmaterialid", row: rowno, value: srvData[index].fmaterialid });
                                        parentPage.Model.setValue({ id: 'fresultbrandid', row: rowno, value: srvData[index].fresultbrandid });//业绩品牌
                                        parentPage.Model.setValue({ id: 'fgoodschanneltype', row: rowno, value: srvData[index].fgoodschanneltype });//渠道类型
                                        parentPage.Model.setValue({ id: 'fsoorderno', row: rowno, value: srvData[index].fsoorderno ?? '' });//销售合同编号
                                        parentPage.Model.setValue({ id: 'forderentryid', row: rowno, value: srvData[index].forderentryid ?? '' });
                                        parentPage.Model.setValue({ id: 'fsourceformid', row: rowno, value: "ydj_order" });
                                        parentPage.Model.setValue({ id: 'fsourcebillno', row: rowno, value: srvData[index].fsourcebillno ?? '' });
                                        parentPage.Model.setValue({ id: 'fsourceinterid', row: rowno, value: srvData[index].fsourceinterid ?? '' });
                                        parentPage.Model.setValue({ id: 'fsoorderentryid', row: rowno, value: srvData[index].fsoorderentryid ?? '' });
                                        parentPage.Model.setValue({ id: 'fsoorderinterid', row: rowno, value: srvData[index].fsoorderinterid ?? '' });
                                        parentPage.Model.setValue({ id: 'fbizqty', row: rowno, value: srvData[index].fbizqty });
                                        parentPage.Model.setValue({ id: 'fattrinfo', row: rowno, value: srvData[index].fattrinfo, opctx: { ignore: true } });
                                        parentPage.Model.setValue({ id: 'fsaledeptid', row: rowno, value: srvData[index].fsaledeptid, opctx: { ignore: true } });
                                        parentPage.Model.setValue({ id: 'fcustomer', row: rowno, value: srvData[index].fcustomer, opctx: { ignore: true } });
                                        parentPage.Model.setValue({ id: 'fphone', row: rowno, value: srvData[index].fphone, opctx: { ignore: true } });
                                        parentPage.Model.setValue({ id: 'fentrystaffid', row: rowno, value: srvData[index].fentrystaffid, opctx: { ignore: true } });
                                    }
                                }
                            }
                        }

                        //parentPage.Model.deleteEntryData({ id: that.Model.selsettings.fentity });
                        //for (var i = 0; i < srvData.length; i++) {
                        //    //parentPage.Model.addRow({ id: that.Model.selsettings.fentity, data: srvData[i] });
                        //    var rowno = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                        //    //基础的数据
                        //    //baseArray.push({ product: ds[i].data.fproductid.id, groupno: ds[i].data.fgroupnumber, baseqty: ds[i].data.fbaseqty });
                        //    parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno, value: ds[i].data.fproductid.id });//促销商品
                        //    parentPage.Model.setValue({ id: "fbizqty", row: rowno, value: srvData[i].fbizqty, opctx: { ignore: true } });//采购数量
                        //    parentPage.Model.setValue({ id: "fpromotion", row: rowno, value: srvData[i].fpromotion }); //促销活动名称
                        //    parentPage.Model.setValue({ id: "fcombinenumber", row: rowno, value: srvData[i].fcombinenumber }); //组合促销编号
                        //    parentPage.Model.setValue({ id: 'fcombineremark', row: rowno, value: srvData[i].fcombineremark });//组合描述
                        //    parentPage.Model.setValue({ id: 'fcombinepriority', row: rowno, value: srvData[i].fcombinepriority });//促销活动优先级
                        //    parentPage.Model.setValue({ id: 'fcombinerate', row: rowno, value: srvData[i].fcombinerate });//折扣率
                        //    parentPage.Model.setValue({ id: "fcombineqty", row: rowno, value: srvData[i].fcombineqty });//套餐组合基数
                        //    parentPage.Model.setValue({ id: "fresultbrandid", row: rowno, value: srvData[i].fresultbrandid });//业绩品牌
                        //}
                        //that.Model.addRow({ id: that.fdentity, data: data, opctx: { ignore: true } });

                        //var parentPage = Index.getPage(ps.parentpageid);
                        //parentPage.Model.refreshEntry({ id: that.Model.selsettings.fentity });
                        yiDialog.mt({ msg: '总部促销商品添加成功！', skinseq: 1 });
                        //不自动关闭，自己手动关闭
                        if (that.Model.getPageInfo().height > 0) {
                            that.Model.close();
                        }
                        //that.Model.refresh();
                    }
                    else if (isSuccess) {
                        yiDialog.mt({ msg: '总部促销商品添加成功！', skinseq: 1 });
                        //不自动关闭，自己手动关闭
                        if (that.Model.getPageInfo().height > 0) {
                            that.Model.close();
                        }
                    }
                    break;
            }
        };

        //初始化套餐组合
        _child.prototype.initEntity = function (fpromotionid) {
            ;
            var that = this;
            var parentPage = Index.getPage(that.Model.viewModel.cp.parentPageId);
            var fsupplierid = parentPage.Model.getValue({ id: "fsupplierid" });
            var forgid = parentPage.Model.getValue({ id: "forgid" });
            var fdeliverid = parentPage.Model.getValue({ id: "fdeliverid" });
            var fbilltypeid = parentPage.Model.getValue({ "id": "fbilltypeid" });
            var billtypeNo = fbilltypeid ? fbilltypeid.fnumber : "";
            var billtypeName = fbilltypeid ? fbilltypeid.fname : "";
            var deptId = parentPage.Model.getSimpleValue({ id: 'fpodeptid' });
            //清空
            that.Model.deleteEntryData({ id: "fentity" });
            //每次切换活动后初始化套餐组合 先从后台获取活动的 x个组合 以及组合下的 y个商品 都一起返回
            var param = {
                formId: 'sel_promotionform',
                fpromotionid: fpromotionid,
                deliverid: fdeliverid ? fdeliverid.id : "",
                supplierid: fsupplierid ? fsupplierid.id : "",
                orgid: forgid ? fsupplierid.id : "",
                domainType: 'dynamic'
            };
            param = {
                formId: 'sel_promotionform',
                fpromotionid: fpromotionid,
                billtypeNo: billtypeNo,
                billtypeName: billtypeName,
                deptId: deptId,
                deliverid: fdeliverid ? fdeliverid.id : "",
                deliverno: fdeliverid ? fdeliverid.fnumber : "",
                delivername: fdeliverid ? fdeliverid.fname : "",
                supplierid: fsupplierid ? fsupplierid.id : "",
                supplierNo: fsupplierid ? fsupplierid.id : "",
                supplierName: fsupplierid ? fsupplierid.id : "",
                domainType: 'dynamic'
            };
            that.Model.invokeFormOperation({
                id: 'addpromotion',
                opcode: 'addpromotion',
                param: param
            });
        };

        _child.prototype.getEntryKey = function (fentityrow) {
            return "PROM_" + fentityrow;
        };

        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                //子件商品
                case 'fpromotionid':
                    var parentPage = Index.getPage(that.Model.viewModel.cp.parentPageId);
                    var fbilltypeid = parentPage.Model.getValue({ "id": "fbilltypeid" });
                    var fdeliverid = parentPage.Model.getValue({ "id": "fdeliverid" });
                    var billtypeName = fbilltypeid ? fbilltypeid.fname : "";
                    if (billtypeName == "期初采购订单") {
                        e.result.filterString = " 1=2 ";
                        return;
                    }
                    var date = new Date() // 获取时间
                    //日期筛选
                    e.result.filterString = "  '{0}' >= fbegindate and '{0}' <= fenddate ".format(date.format("yyyy-MM-dd 00:00:00"));
                    e.result.simpleData = { fdeliverid: fdeliverid.id };
                    e.result.params = [
                        { fieldId: 'fdeliverid', pValue: fdeliverid.id }
                    ];
                    break;
            }
        };

        //初始化商品
        _child.prototype.initProp = function (fentityrow, combineentrys) {
            var that = this;

            var entity = that.Model.getEntryRowData({ id: "fentity", row: fentityrow });
            if (!entity) return;

            //清空组合商品行
            that.Model.deleteEntryData({ id: "fcombineentry" });
            //设置组合商品行
            for (var i = 0; i < combineentrys.length; i++) {
                var item = combineentrys[i];
                if (item.fproductid_ref) {
                    var frow = {
                        parentrow: fentityrow,
                        fproductnumber: item.fproductid_ref.fnumber,
                        fproductid: { id: item.fproductid_ref.id, fname: item.fproductid_ref.fname, fnumber: item.fproductid_ref.fnumber },
                        fqty: item.fbaseqty,
                        fbaseqty: item.fbaseqty,
                        funitid: { id: item.funitid_ref ? item.funitid_ref.id : '', fname: item.funitid_ref ? item.funitid_ref.fname : '', fnumber: item.funitid_ref ? item.funitid_ref.fnumber : '' },
                        fcdescription: item.fcdescription,
                        fdistrate: item.fdistrate,
                        fgroupnumber: item.fgroupnumber,
                        fgroupdesc: item.fgroupdesc
                    };
                    var rowno = that.Model.addRow({ id: "fcombineentry", data: frow });
                }
                //that.Model.setValue({ id: 'fproductid', row: rowno, value: item.fproductid_ref.id,opctx: { ignore: true } });
                //that.Model.setValue({ id: 'funitid', row: rowno, value: item.funitid_ref ? item.funitid_ref.id : '', opctx: { ignore: true } });
            }
            that.Model.refreshEntry({ id: 'fcombineentry' });
        };

        //表单菜单点击时触发
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'submitreturn':
                    e.result = true;
                    that.submit();
                    break;
            }
        };




        _child.prototype.submit = function () {
            ;
            var that = this;
            //添加到父页
            var parentPage = Index.getPage(ps.parentpageid);
            var parentrows = parentPage.Model.getEntryData({ id: "fentity" });
            var fcombineentry = that.Model.getEntryData({ id: "fcombineentry" });
            var fpromotionnumber = that.Model.getValue({ id: 'fpromotionid' }).fnumber;
            var fpromotionname = that.Model.getValue({ id: 'fpromotionid' }).fname;
            if (fcombineentry.length == 0) {
                yiDialog.mt({ msg: '请选择促销活动！', skinseq: 2 });
                return;
            }
            var combine = that.Model.getEntryRowData({ id: "fentity", row: fcombineentry[0].parentrow });
            parentrows = parentrows.filter(c => c.fcombinenumber == "");

            var ds = that.Model.getSelectRows({ id: "fcombineentry" });
            var number = [];
            var message = "";
            for (var i = 0; i < ds.length; i++) {
                //套餐组合基数 大于 采购数量
                //if (ds[i].data.fbaseqty > ds[i].data.fqty) return yiDialog.warn('采购数量不能小于套餐组合基数');
                let index = number.indexOf(ds[i].data.fgroupnumber)
                if (index < 0) {
                    number.push(ds[i].data.fgroupnumber);
                }
            }
            var combinenumber = [];
            for (var j = 0; j < fcombineentry.length; j++) {
                let index = combinenumber.indexOf(fcombineentry[j].fgroupnumber)
                if (index < 0) {
                    combinenumber.push(fcombineentry[j].fgroupnumber);
                }
            } if (number.length < combinenumber.length) {
                let diff = combinenumber.filter(e => !number.includes(e));
                message += "套餐明细已选择的商品未包含子组号" + diff.toString() + "，请从子组号" + diff.toString() + "中选择商品凑成组合套餐，享受活动折扣优惠 。</br>";
            }
            if (message.length > 0) {
                return yiDialog.warn(message);
            }
            var nomatch = [];
            for (var i = 0; i < number.length; i++) {
                let qty = 0;
                for (var j = 0; j < ds.length; j++) {
                    if (ds[j].data.fgroupnumber == number[i]) {
                        qty += parseInt(ds[j].data.fqty);
                    }
                }
                var baseqty = ds.filter(a => a.data.fgroupnumber == number[i])[0].data.fbaseqty;
                if (qty < baseqty) {
                    nomatch.push(number[i]);
                }
            }
            message = "";
            if (nomatch.length > 0) {
                message += "套餐明细子组号" + nomatch.toString() + "中的商品采购数量合计，小于套餐组合基数，不符合促销活动规则，请继续凑单。</br>";
            }
            if (message.length > 0) {
                return yiDialog.warn(message);
            }
            //if (number.length < combinenumber.length) return yiDialog.warn('请勾选套餐中其他子组号商品');

            //var intersection = number.filter(function (val) { return combinenumber.indexOf(val) > -1 });
            //if (intersection.length < combinenumber.length) return yiDialog.warn('套餐商品不完整，请选择完整的套餐商品');
            debugger
            for (var i = 0; i < ds.length; i++) {
                var row = parentrows.filter(c => c.fmaterialid.id == ds[i].data.fproductid.id && c.fsoorderno == "");
                if (row != undefined && row.length > 0) {
                    let rownumber = row[0].fbizqty;
                    parentPage.Model.setValue({ id: "fbizqty", row: row[0].id, value: parseInt(rownumber) + parseInt(ds[i].data.fqty), opctx: { ignore: true } });//采购数量
                } else {
                    var rowno = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                    //基础的数据
                    //baseArray.push({ product: ds[i].data.fproductid.id, groupno: ds[i].data.fgroupnumber, baseqty: ds[i].data.fbaseqty });
                    parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno, value: ds[i].data.fproductid.id });//促销商品
                    parentPage.Model.setValue({ id: "fbizqty", row: rowno, value: ds[i].data.fqty });//采购数量
                }

            }
            for (var i = 0; i < parentrows.length; i++) {
                var fproduct1 = parentrows[i].fmaterialid;
                if (!fproduct1 || $.trim(fproduct1.id) == "") {
                    parentPage.Model.deleteRow({ id: that.Model.selsettings.fentity, row: parentrows[i].id });

                }
            }

            setTimeout(function () {
                that.Model.invokeFormOperation({
                    id: 'savecombineinfo',
                    opcode: 'savecombineinfo',
                    formId: 'ydj_purchaseorder',
                    billData: JSON.stringify([parentPage.Model.uiData]),
                    selectedRows: [{ PKValue: parentPage.Model.pkid }],
                    //selectedRows: [{ PKValue: that.Model.pkid }],
                    param: {
                        formId: 'ydj_purchaseorder',
                        combinenumber: combine.fcombinenumber,
                        pkid: parentPage.Model.pkid,
                        parentPageId: that.parentpageid,
                        stype: 'sel_promotionform'
                        //changeReason: v
                    }
                });
            }, 3000)



            ////最小套餐基数
            //for (var i = 0; i < baseArray.length; i++) {
            //    var comp = fcombineentry.find(o => o.fproductid.id == baseArray[i].product);
            //    var rowno = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
            //    parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno, value: comp.fproductid.id, opctx: { ignore: true } });//促销商品
            //    parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno, value: comp.fproductid.id });//促销商品
            //    parentPage.Model.setValue({ id: "fbizqty", row: rowno, value: baseArray[i].baseqty, opctx: { ignore: true } });//采购数量
            //}

        }


        _child.prototype.submit11 = function () {
            ;
            var that = this;
            //添加到父页
            var parentPage = Index.getPage(ps.parentpageid);
            var parentrows = parentPage.Model.getEntryData({ id: "fentity" });
            var fcombineentry = that.Model.getEntryData({ id: "fcombineentry" });
            var fpromotionnumber = that.Model.getValue({ id: 'fpromotionid' }).fnumber;
            var fpromotionname = that.Model.getValue({ id: 'fpromotionid' }).fname;
            var combine = that.Model.getEntryRowData({ id: "fentity", row: fcombineentry[0].parentrow });
            parentrows = parentrows.filter(c => c.fcombinenumber == "");

            var ds = that.Model.getSelectRows({ id: "fcombineentry" });
            var number = [];

            for (var i = 0; i < ds.length; i++) {
                //套餐组合基数 大于 采购数量
                //if (ds[i].data.fbaseqty > ds[i].data.fqty) return yiDialog.warn('采购数量不能小于套餐组合基数');
                let index = number.indexOf(ds[i].data.fgroupnumber)
                if (index < 0) {
                    number.push(ds[i].data.fgroupnumber);
                }
            }
            var combinenumber = [];
            for (var j = 0; j < fcombineentry.length; j++) {
                let index = combinenumber.indexOf(fcombineentry[j].fgroupnumber)
                if (index < 0) {
                    combinenumber.push(fcombineentry[j].fgroupnumber);
                }
            }

            if (number.length < combinenumber.length) return yiDialog.warn('请勾选套餐中其他子组号商品');

            var intersection = number.filter(function (val) { return combinenumber.indexOf(val) > -1 });
            if (intersection.length < combinenumber.length) return yiDialog.warn('套餐商品不完整，请选择完整的套餐商品');

            for (var i = 0; i < parentrows.length; i++) {

                var fproduct1 = parentrows[i].fmaterialid;
                if (!fproduct1 || $.trim(fproduct1.id) == "") {
                    parentPage.Model.deleteRow({ id: that.Model.selsettings.fentity, row: parentrows[i].id });

                }
            }

            var baseArray = [];
            var resiArray = [];
            var oldArray = [];
            var orderArray = [];
            for (var i = 0; i < parentrows.length; i++) {

                var compfind = fcombineentry.find(o => o.fproductid.id == parentrows[i].fmaterialid.id);
                if (compfind != undefined) {
                    var findds = ds.find(c => c.data.fproductid.id == parentrows[i].fmaterialid.id);
                    if (findds != undefined) {
                        parentPage.Model.setValue({ id: "fcombinenumber", row: parentrows[i].id, value: "" });
                        findds.data.fqty = parseInt(findds.data.fqty) + parentrows[i].fbizqty;
                        if (!(parentrows[i].fsoorderno != undefined && parentrows[i].fsoorderno.length > 0)) {
                            oldArray.push(parentrows[i]);
                            parentPage.Model.deleteRow({ id: that.Model.selsettings.fentity, row: parentrows[i].id });
                        } else {
                            orderArray.push(parentrows[i]);
                        }

                    } else {
                        if (parentrows[i].fbizqty >= compfind.fbaseqty) {
                            parentPage.Model.setValue({ id: "fcombinenumber", row: parentrows[i].id, value: "" });
                            compfind.fqty = parentrows[i].fbizqty;
                            if (!(parentrows[i].fsoorderno != undefined && parentrows[i].fsoorderno.length > 0)) {
                                ds.push({ pkid: parentrows[i].fmaterialid.id, data: compfind });
                                oldArray.push(parentrows[i]);
                                parentPage.Model.deleteRow({ id: that.Model.selsettings.fentity, row: parentrows[i].id });
                            } else {
                                orderArray.push(parentrows[i]);
                            }
                        }
                    }
                }
            }

            var integerArray = [];
            for (var i = 0; i < ds.length; i++) {
                integerArray.push(Math.trunc(ds[i].data.fqty / ds[i].data.fbaseqty));
            }
            //先计算整数倍
            var minqty = Math.min(...integerArray);

            for (var i = 0; i < ds.length; i++) {
                //基础的数据
                baseArray.push({ product: ds[i].data.fproductid.id, groupno: ds[i].data.fgroupnumber, baseqty: ds[i].data.fbaseqty });
                //剩余的数据
                if (ds[i].data.fqty - ds[i].data.fbaseqty * minqty > 0) {
                    resiArray.push({ product: ds[i].data.fproductid.id, groupno: ds[i].data.fgroupnumber, baseqty: ds[i].data.fqty - ds[i].data.fbaseqty * minqty });
                }
            }

            //最小套餐基数
            for (var i = 0; i < baseArray.length; i++) {
                var comp = fcombineentry.find(o => o.fproductid.id == baseArray[i].product);
                var rowno = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno, value: comp.fproductid.id, opctx: { ignore: true } });//促销商品
                parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno, value: comp.fproductid.id });//促销商品
                parentPage.Model.setValue({ id: "fpromotion", row: rowno, value: fpromotionname }); //促销活动名称
                parentPage.Model.setValue({ id: "fcombinenumber", row: rowno, value: combine.fcombinenumber }); //组合促销编号
                parentPage.Model.setValue({ id: 'fcombineremark', row: rowno, value: combine.fcombinedescription });//组合描述
                parentPage.Model.setValue({ id: 'fcombinepriority', row: rowno, value: combine.fcombinepriority });//促销活动优先级
                parentPage.Model.setValue({ id: 'fcombinerate', row: rowno, value: comp.fdistrate });//折扣率
                parentPage.Model.setValue({ id: "fcombineqty", row: rowno, value: comp.fbaseqty });//套餐组合基数
                parentPage.Model.setValue({ id: "fbizqty", row: rowno, value: baseArray[i].baseqty * minqty, opctx: { ignore: true } });//采购数量

                if (resiArray.length <= 0) {
                    var rownumber = 0;
                    var row = parentrows.filter(c => c.fmaterialid.id == baseArray[i].product && c.fsoorderno != "");
                    if (row != undefined) {
                        for (var j = 0; j < row.length; j++) {
                            rownumber += row[j].fbizqty;
                        }
                    }

                    parentPage.Model.setValue({ id: "fbizqty", row: rowno, value: baseArray[i].baseqty * minqty - rownumber, opctx: { ignore: true } });//采购数量
                    var oldrow = oldArray.find(o => o.fmaterialid.id == baseArray[i].product);
                    if (oldrow != undefined) {
                        parentPage.Model.setValue({ id: 'fresultbrandid', row: rowno, value: oldrow["fresultbrandid"].id });//业绩品牌
                    }
                    var orderrow = orderArray.filter(o => o.fmaterialid.id == baseArray[i].product);
                    if (orderrow != undefined) {
                        for (var j = 0; j < orderrow.length; j++) {
                            parentPage.Model.setValue({ id: "fpromotion", row: orderrow[j].id, value: fpromotionname }); //促销活动名称
                            parentPage.Model.setValue({ id: "fcombinenumber", row: orderrow[j].id, value: combine.fcombinenumber }); //组合促销编号
                            parentPage.Model.setValue({ id: 'fcombineremark', row: orderrow[j].id, value: combine.fcombinedescription });//组合描述
                            parentPage.Model.setValue({ id: 'fcombinepriority', row: orderrow[j].id, value: combine.fcombinepriority });//促销活动优先级
                            parentPage.Model.setValue({ id: 'fcombinerate', row: orderrow[j].id, value: combine.fcombinerate });//折扣率
                            parentPage.Model.setValue({ id: "fcombineqty", row: orderrow[j].id, value: comp.fbaseqty });//套餐组合基数
                        }
                    }
                }
            }
            //剩余数量包含(套餐多余的数量和原商品数量)
            for (var i = 0; i < resiArray.length; i++) {

                var row = parentrows.filter(c => c.fmaterialid.id == ds[i].data.fproductid.id && c.fsoorderno != "");
                if (row.length > 0) {

                    parentPage.Model.setValue({ id: "fbizqty", row: row[0].id, value: resiArray[i].baseqty, opctx: { ignore: true } });//采购数量

                } else {
                    var comp1 = fcombineentry.find(o => o.fproductid.id == resiArray[i].product);
                    var rowno1 = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                    parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno1, value: comp1.fproductid.id, opctx: { ignore: true } });//促销商品
                    parentPage.Model.setValue({ id: "fbizqty", row: rowno1, value: resiArray[i].baseqty, opctx: { ignore: true } });//采购数量

                    var oldrow1 = oldArray.find(o => o.fmaterialid.id == resiArray[i].product);
                    if (oldrow1 != undefined) {
                        parentPage.Model.setValue({ id: 'fresultbrandid', row: rowno1, value: oldrow1["fresultbrandid"].id });//业绩品牌
                    }

                    //var orderrow1 = orderArray.filter(o => o.fmaterialid.id == resiArray[i].product);
                    //if (orderrow1 != undefined) {
                    //    for (var j = 0; j < orderrow1.length; j++) {
                    //        parentPage.Model.setValue({ id: "fpromotion", row: orderrow1[j].id, value: fpromotionname }); //促销活动名称
                    //        parentPage.Model.setValue({ id: "fcombinenumber", row: orderrow1[j].id, value: combine.fcombinenumber }); //组合促销编号
                    //        parentPage.Model.setValue({ id: 'fcombineremark', row: orderrow1[j].id, value: combine.fcombinedescription });//组合描述
                    //        parentPage.Model.setValue({ id: 'fcombinepriority', row: orderrow1[j].id, value: combine.fcombinepriority });//促销活动优先级
                    //        parentPage.Model.setValue({ id: 'fcombinerate', row: orderrow1[j].id, value: combine.fcombinerate });//折扣率
                    //        parentPage.Model.setValue({ id: "fcombineqty", row: orderrow1[j].id, value: comp1.fbaseqty });//套餐组合基数
                    //    }
                    //}

                }
            }
            //for (var i = 0; i < baseArray.length; i++) {
            //    var comp = fcombineentry.find(o => o.fproductid.id == baseArray[i].product);
            //    var rowno = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
            //    parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno, value: comp.fproductid.id, opctx: { ignore: true } });//促销商品
            //    parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno, value: comp.fproductid.id });//促销商品
            //    parentPage.Model.setValue({ id: "fpromotion", row: rowno, value: fpromotionname }); //促销活动名称
            //    parentPage.Model.setValue({ id: "fcombinenumber", row: rowno, value: combine.fcombinenumber }); //组合促销编号
            //    parentPage.Model.setValue({ id: 'fcombineremark', row: rowno, value: combine.fcombinedescription });//组合描述
            //    parentPage.Model.setValue({ id: 'fcombinepriority', row: rowno, value: combine.fcombinepriority });//促销活动优先级
            //    parentPage.Model.setValue({ id: 'fcombinerate', row: rowno, value: combine.fcombinerate });//折扣率
            //    parentPage.Model.setValue({ id: "fcombineqty", row: rowno, value: comp.fbaseqty });//套餐组合基数
            //    var rownumber = 0;
            //    var row = parentrows.filter(c => c.fmaterialid.id == baseArray[i].product && c.fsoorderno!="");
            //    if (row != undefined) {
            //        for (var j = 0; j < row.length; j++) {
            //            rownumber += row[j].fbizqty;
            //        }

            //        parentPage.Model.setValue({ id: "fbizqty", row: rowno, value: baseArray[i].baseqty * minqty - rownumber, opctx: { ignore: true }});//采购数量
            //    } else {
            //        parentPage.Model.setValue({ id: "fbizqty", row: rowno, value: baseArray[i].baseqty * minqty, opctx: { ignore: true }});//采购数量
            //    }

            //    var oldrow = oldArray.find(o => o.fmaterialid.id == baseArray[i].product);
            //    if (oldrow != undefined) {
            //        parentPage.Model.setValue({ id: 'fresultbrandid', row: rowno, value: oldrow["fresultbrandid"].id});//业绩品牌
            //    }
            //    var orderrow = orderArray.filter(o => o.fmaterialid.id == baseArray[i].product);

            //    if (orderrow != undefined) {
            //        for (var j = 0; j < orderrow.length; j++) {
            //            parentPage.Model.setValue({ id: "fpromotion", row: orderrow[j].id, value: fpromotionname }); //促销活动名称
            //            parentPage.Model.setValue({ id: "fcombinenumber", row: orderrow[j].id, value: combine.fcombinenumber }); //组合促销编号
            //            parentPage.Model.setValue({ id: 'fcombineremark', row: orderrow[j].id, value: combine.fcombinedescription });//组合描述
            //            parentPage.Model.setValue({ id: 'fcombinepriority', row: orderrow[j].id, value: combine.fcombinepriority });//促销活动优先级
            //            parentPage.Model.setValue({ id: 'fcombinerate', row: orderrow[j].id, value: combine.fcombinerate });//折扣率
            //            parentPage.Model.setValue({ id: "fcombineqty", row: orderrow[j].id, value: comp.fbaseqty });//套餐组合基数
            //        }
            //    }
            //}

            //for (var i = 0; i < resiArray.length; i++) {
            //    var rownumber = 0;
            //    var row = parentrows.filter(c => c.fmaterialid.id == ds[i].data.fproductid.id && c.fsoorderno != "");
            //    if (row != undefined) {
            //        for (var j = 0; j < row.length; j++) {
            //            rownumber += row[j].fbizqty;
            //        }

            //        parentPage.Model.setValue({ id: "fbizqty", row: row[0].id, value: resiArray[i].baseqty + rownumber, opctx: { ignore: true } });//采购数量

            //    } else {
            //        var comp1 = fcombineentry.find(o => o.fproductid.id == resiArray[i].product);
            //        var rowno1 = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
            //        parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno1, value: comp1.fproductid.id, opctx: { ignore: true } });//促销商品
            //        parentPage.Model.setValue({ id: "fbizqty", row: rowno1, value: resiArray[i].baseqty, opctx: { ignore: true } });//采购数量

            //        var oldrow1 = oldArray.find(o => o.fmaterialid.id == resiArray[i].product);
            //        if (oldrow1 != undefined) {
            //            parentPage.Model.setValue({ id: 'fresultbrandid', row: rowno1, value: oldrow1["fresultbrandid"].id });//业绩品牌
            //        }

            //        var orderrow1 = orderArray.filter(o => o.fmaterialid.id == resiArray[i].product);
            //        if (orderrow1 != undefined) {
            //            for (var j = 0; j < orderrow1.length; j++) {
            //                parentPage.Model.setValue({ id: "fpromotion", row: orderrow1[j].id, value: fpromotionname }); //促销活动名称
            //                parentPage.Model.setValue({ id: "fcombinenumber", row: orderrow1[j].id, value: combine.fcombinenumber }); //组合促销编号
            //                parentPage.Model.setValue({ id: 'fcombineremark', row: orderrow1[j].id, value: combine.fcombinedescription });//组合描述
            //                parentPage.Model.setValue({ id: 'fcombinepriority', row: orderrow1[j].id, value: combine.fcombinepriority });//促销活动优先级
            //                parentPage.Model.setValue({ id: 'fcombinerate', row: orderrow1[j].id, value: combine.fcombinerate });//折扣率
            //                parentPage.Model.setValue({ id: "fcombineqty", row: orderrow1[j].id, value: comp1.fbaseqty });//套餐组合基数
            //            }
            //        }

            //    }
            //}

            var newrows = parentPage.Model.getEntryData({ id: "fentity" });
            var rows = newrows.filter(c => c.fbizqty == 0 && c.fcombinenumber != "");
            if (rows.length > 0) {
                for (var i = 0; i < rows.length; i++) {
                    parentPage.Model.setValue({ id: "fcombinenumber", row: rows[i].id, value: "" });
                    parentPage.Model.deleteRow({ id: that.Model.selsettings.fentity, row: rows[i].id });
                }
            }

            for (var i = 0; i < newrows.length; i++) {
                var newfproduct = newrows[i].fmaterialid;
                if (!newfproduct || $.trim(newfproduct.id) == "") {
                    parentPage.Model.deleteRow({ id: that.Model.selsettings.fentity, row: newrows[i].id });
                }
            }

            newrows.sort(function (a, b) {
                if (a.fsofacombnumber !== b.fsofacombnumber) {
                    return a.fsofacombnumber.localeCompare(b.fsofacombnumber);
                }
                else if (a.fcombinenumber !== b.fcombinenumber) {

                    if (a.fcombinenumber < b.fcombinenumber) {
                        return 1;
                    }
                    if (a.fcombinenumber > b.fcombinenumber) {
                        return -1;
                    }
                }
                else if (a.fpartscombnumber !== b.fpartscombnumber) {
                    return a.fpartscombnumber.localeCompare(b.fpartscombnumber)
                }
                else if (a.fiscombmain !== b.fiscombmain) {
                    return b.fiscombmain - a.fiscombmain;
                }
                else if (a.fsuitcombnumber !== b.fsuitcombnumber) {
                    return a.fsuitcombnumber.localeCompare(b.fsuitcombnumber)
                }
                else if (a.fissuitflag !== b.fissuitflag) {
                    return b.fissuitflag - a.fissuitflag;
                } else {
                    return 1;
                }
            });


            parentPage.Model.refreshEntry({ id: that.Model.selsettings.fentity });
            yiDialog.mt({ msg: '总部促销商品添加成功！', skinseq: 1 });
            //不自动关闭，自己手动关闭
            that.Model.close();
        }

        _child.prototype.submit1 = function () {
            ;
            var that = this;
            //添加到父页
            var parentPage = Index.getPage(ps.parentpageid);
            var parentrows = parentPage.Model.getEntryData({ id: "fentity" });
            var fcombineentry = that.Model.getEntryData({ id: "fcombineentry" });
            var fpromotionnumber = that.Model.getValue({ id: 'fpromotionid' }).fnumber;
            var fpromotionname = that.Model.getValue({ id: 'fpromotionid' }).fname;
            var combine = that.Model.getEntryRowData({ id: "fentity", row: fcombineentry[0].parentrow });
            //var hasrow = parentrows.find(o => o.fcombinenumber == combine.fcombinenumber);

            var ds = that.Model.getSelectRows({ id: "fcombineentry" });
            var number = [];
            //清空空行
            var emptyIds = [];

            for (var i = 0; i < ds.length; i++) {
                for (var j = 0; j < parentrows.length; j++) {
                    var parentrow = parentrows[j];
                    var fproduct = parentrow.fmaterialid;
                    //找到对应的商品
                    if (ds[i].data.fproductid.id == fproduct.id) {
                        ds[i].data.fqty = parseInt(ds[i].data.fqty) + parentrow.fqty;
                        emptyIds.push(parentrow.id);
                    }
                }

                //套餐组合基数 大于 采购数量
                //if (ds[i].data.fbaseqty > ds[i].data.fqty) return yiDialog.warn('采购数量不能小于套餐组合基数');
                let index = number.indexOf(ds[i].data.fgroupnumber)
                if (index < 0) {
                    number.push(ds[i].data.fgroupnumber);
                }
            }
            var combinenumber = [];
            for (var j = 0; j < fcombineentry.length; j++) {
                let index = combinenumber.indexOf(fcombineentry[j].fgroupnumber)
                if (index < 0) {
                    combinenumber.push(fcombineentry[j].fgroupnumber);
                }
            }

            if (number.length < combinenumber.length) return yiDialog.warn('请勾选套餐中其他子组号商品');

            var intersection = number.filter(function (val) { return combinenumber.indexOf(val) > -1 });
            if (intersection.length < combinenumber.length) return yiDialog.warn('套餐商品不完整，请选择完整的套餐商品');

            //如果采购订单没有添加过此套餐直接新增明细行
            //if (!hasrow)
            //{
            //清空空行


            for (var i = 0; i < parentrows.length; i++) {

                var fproduct1 = parentrows[i].fmaterialid;
                if (!fproduct1 || $.trim(fproduct1.id) == "") {
                    parentPage.Model.deleteRow({ id: that.Model.selsettings.fentity, row: parentrows[i].id });
                    //emptyIds.push(fproduct1.id);
                }
            }

            if (emptyIds && emptyIds.length > 0) {
                for (var i = 0; i < emptyIds.length; i++) {
                    parentPage.Model.setValue({ id: "fcombinenumber", row: emptyIds[i], value: "" });
                    parentPage.Model.deleteRow({ id: that.Model.selsettings.fentity, row: emptyIds[i] });
                }
            }

            //for (var i = 0; i < parentrows.length; i++) {
            //    var compfind = fcombineentry.find(o => o.fproductid.id == parentrows[i].fmaterialid.id);
            //    if (compfind != undefined) {
            //        if (parentrows[i].fqty >= compfind.fbaseqty) {
            //            var dsrow = ds.find(c => c.data.fproductid.id == parentrows[i].fmaterialid.id);
            //            if (dsrow == undefined) {
            //                ds.push({ pkid: parentrows[i].fmaterialid.id, data: dsrow });
            //            } 
            //        }
            //    }
            //}

            //处理不勾选但是符合套餐的数据
            for (var i = 0; i < parentrows.length; i++) {
                var rowfind = ds.find(o => o.data.fproductid.id == parentrows[i].fmaterialid.id);
                var compfind2 = fcombineentry.find(o => o.fproductid.id == parentrows[i].fmaterialid.id);
                if (rowfind == undefined && compfind2 != undefined) {
                    var qty = parentrows[i].fqty;
                    if (parentrows[i].fqty % compfind2.fbaseqty == 0 && parentrows[i].fqty > 0) {
                        parentPage.Model.setValue({ id: "fpromotion", row: parentrows[i].id, value: fpromotionname }); //促销活动名称
                        parentPage.Model.setValue({ id: "fcombinenumber", row: parentrows[i].id, value: combine.fcombinenumber }); //组合促销编号
                        parentPage.Model.setValue({ id: 'fcombineremark', row: parentrows[i].id, value: combine.fcombinedescription });//组合描述
                        parentPage.Model.setValue({ id: 'fcombinepriority', row: parentrows[i].id, value: combine.fcombinepriority });//促销活动优先级
                        parentPage.Model.setValue({ id: 'fcombinerate', row: parentrows[i].id, value: combine.fcombinerate });//折扣率
                        parentPage.Model.setValue({ id: "fcombineqty", row: parentrows[i].id, value: compfind2.fbaseqty });//套餐组合基数
                        parentPage.Model.setValue({ id: "fgroupnumber", row: parentrows[i].id, value: compfind2.fgroupnumber });//套餐组合基数
                    } else if (qty != 1) {
                        var zhengnumber = Math.trunc(qty / compfind2.fbaseqty);
                        parentPage.Model.setValue({ id: "fpromotion", row: parentrows[i].id, value: fpromotionname }); //促销活动名称
                        parentPage.Model.setValue({ id: "fcombinenumber", row: parentrows[i].id, value: combine.fcombinenumber }); //组合促销编号
                        parentPage.Model.setValue({ id: that.Model.selsettings.fbizqty, row: parentrows[i].id, value: combine.fqty, tgChange: false });//数量
                        parentPage.Model.setValue({ id: 'fcombineremark', row: parentrows[i].id, value: combine.fcombinedescription });//组合描述
                        parentPage.Model.setValue({ id: 'fcombinepriority', row: parentrows[i].id, value: combine.fcombinepriority });//促销活动优先级
                        parentPage.Model.setValue({ id: 'fcombinerate', row: parentrows[i].id, value: combine.fcombinerate });//折扣率
                        parentPage.Model.setValue({ id: "fcombineqty", row: parentrows[i].id, value: compfind2.fbaseqty });//套餐组合基数
                        parentPage.Model.setValue({ id: "fbizqty", row: parentrows[i].id, value: compfind2.fbaseqty * zhengnumber });//采购数量

                        var rowno2 = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                        parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno2, value: compfind2.fproductid.id });//促销商品
                        parentPage.Model.setValue({ id: that.Model.selsettings.fbizqty, row: rowno2, value: qty - compfind2.fbaseqty * zhengnumber });//数量
                        parentPage.Model.setValue({ id: "fbizqty", row: rowno2, value: qty - compfind2.fbaseqty * zhengnumber });//采购数量
                    }

                }
            }

            for (var i = 0; i < ds.length; i++) {
                var comp = fcombineentry.find(o => o.fproductid == ds[i].data.fproductid);
                if (ds[i].data.fqty % ds[i].data.fbaseqty == 0) {
                    var rowno = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                    parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno, value: comp.fproductid.id });//促销商品
                    parentPage.Model.setValue({ id: "fpromotion", row: rowno, value: fpromotionname }); //促销活动名称
                    parentPage.Model.setValue({ id: "fcombinenumber", row: rowno, value: combine.fcombinenumber }); //组合促销编号
                    parentPage.Model.setValue({ id: that.Model.selsettings.fbizqty, row: rowno, value: comp.fqty, tgChange: false });//数量
                    parentPage.Model.setValue({ id: 'fcombineremark', row: rowno, value: combine.fcombinedescription });//组合描述
                    parentPage.Model.setValue({ id: 'fcombinepriority', row: rowno, value: combine.fcombinepriority });//促销活动优先级
                    parentPage.Model.setValue({ id: 'fcombinerate', row: rowno, value: combine.fcombinerate });//折扣率
                    parentPage.Model.setValue({ id: "fcombineqty", row: rowno, value: comp.fbaseqty });//套餐组合基数
                    parentPage.Model.setValue({ id: "fgroupnumber", row: rowno, value: comp.fgroupnumber });//套餐组合基数
                    parentPage.Model.setValue({ id: "fbizqty", row: rowno, value: ds[i].data.fqty });//采购数量
                } else {
                    //var zhengnumber = ds[i].data.fqty % ds[i].data.fbaseqty;
                    var zhengnumber = Math.trunc(ds[i].data.fqty / ds[i].data.fbaseqty);

                    if (ds[i].data.fqty - ds[i].data.fbaseqty * zhengnumber != 0) {
                        var yurowno = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                        parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: yurowno, value: comp.fproductid.id });//促销商品
                        parentPage.Model.setValue({ id: "fpromotion", row: yurowno, value: fpromotionname }); //促销活动名称
                        parentPage.Model.setValue({ id: "fcombinenumber", row: yurowno, value: combine.fcombinenumber }); //组合促销编号
                        parentPage.Model.setValue({ id: that.Model.selsettings.fbizqty, row: yurowno, value: comp.fqty, tgChange: false });//数量
                        parentPage.Model.setValue({ id: 'fcombineremark', row: yurowno, value: combine.fcombinedescription });//组合描述
                        parentPage.Model.setValue({ id: 'fcombinepriority', row: yurowno, value: combine.fcombinepriority });//促销活动优先级
                        parentPage.Model.setValue({ id: 'fcombinerate', row: yurowno, value: combine.fcombinerate });//折扣率
                        parentPage.Model.setValue({ id: "fcombineqty", row: yurowno, value: comp.fbaseqty });//套餐组合基数
                        parentPage.Model.setValue({ id: "fgroupnumber", row: yurowno, value: comp.fgroupnumber });//套餐组合基数
                        parentPage.Model.setValue({ id: "fbizqty", row: yurowno, value: ds[i].data.fbaseqty * zhengnumber });//采购数量

                        var rowno1 = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                        parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno1, value: comp.fproductid.id });//促销商品
                        parentPage.Model.setValue({ id: "fbizqty", row: rowno1, value: ds[i].data.fqty - ds[i].data.fbaseqty * zhengnumber });//采购数量
                    }
                }
            }
            //}
            //else
            //{
            //    //如果存在则是累加
            //    for (var i = 0; i < parentrows.length; i++) {
            //        var parentrow = parentrows[i];
            //        var fproduct = parentrow.fmaterialid;
            //        //找到对应的商品
            //        var currentproduct = fcombineentry.find(o => o.fproductid.id == fproduct.id);
            //        if (currentproduct)
            //        {
            //            var sumqty = currentproduct.fqty + parentrow.fbizqty;
            //            parentPage.Model.setValue({ id: that.Model.selsettings.fbizqty, row: parentrow.id, value: sumqty, tgChange: false });//数量
            //        }
            //    }
            //}
            //that.Model.refreshEntry({ id: 'fcombineentry' });
            parentPage.Model.refreshEntry({ id: that.Model.selsettings.fentity });
            yiDialog.mt({ msg: '总部促销商品添加成功！', skinseq: 1 });
            //不自动关闭，自己手动关闭
            //that.Model.close();
        }

        //表格选中行变化时触发
        _child.prototype.onSelectedRowsChanged = function (e) {
            var that = this;
            if (e.id == 'fentity') {
                //切换子件时，先结束右边表格的编辑状态（这样就会触发非标值的字段值变化事件）
                that.Model.endEditEntry({ id: "fcombineentry" });
                /*that.initProp(e.row);*/
                if (e.data.length == 0) return;
                var fcombineid = e.data[0].fcombineid.id;
                var fcombineentry = ps.combines.find(o => o.id == e.data[0].fcombineid.id).fcombineentry;
                that.initProp(e.data[0].id, fcombineentry);

            }
        };
        //设值前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            if (e.opctx && e.opctx.ignore) return;
            switch (e.id.toLowerCase()) {
                case 'fqty':
                    //var rows = that.Model.getEntryData({ id: 'fcombineentry' });
                    var oldqty = that.Model.getValue({ id: 'fqty', row: e.row });
                    //var fbaseqty = that.Model.getValue({ id: 'fbaseqty', row: e.row });
                    //var newqty = e.value;
                    //if (newqty % fbaseqty != 0) {
                    //    e.result = true;
                    //    yiDialog.mt({ msg: "添加的数量必须为套餐组合基数的倍数, 请重新添加数量 !", skinseq: 2 });
                    //    e.value = oldqty;
                    //}
                    if (parseInt(e.value) <= 0) {
                        e.result = true;
                        yiDialog.mt({ msg: "不允许为0与负数", skinseq: 2 });
                        e.value = oldqty;
                    }
                    break;
            }

        }
        _child.prototype.onFieldValueChanged = function (e) {
            if (e.opctx && e.opctx.ignore) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fpromotionid':
                    that.initEntity(e.value.id);
                    break;
                case 'fqty':
                    //that.CaculateQty(e);
                    break;
            }
        };

        //计算组合商品的数量
        _child.prototype.CaculateQty = function (e) {
            var that = this;
            //首先定义一个倍数
            var rows = that.Model.getEntryData({ id: 'fcombineentry' });
            var fbaseqty = that.Model.getValue({ id: 'fbaseqty', row: e.row });
            var newqty = e.value;
            var mul = newqty / fbaseqty;
            for (var i = 0; i < rows.length; i++) {
                var curfbaseqty = that.Model.getValue({ id: 'fbaseqty', row: rows[i].id });
                that.Model.setValue({ id: 'fqty', value: mul * curfbaseqty, row: rows[i].id, opctx: { ignore: true } });
            }
            that.Model.refreshEntry({ id: 'fcombineentry' });
        };

        return _child;
    })(BasePlugIn);
    window.sel_promotionform = window.sel_promotionform || sel_promotionform;
})();