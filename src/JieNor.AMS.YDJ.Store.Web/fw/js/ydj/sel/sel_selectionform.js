/// <reference path="/fw/js/platform/mvvm/baseplugin.js" />
//@ sourceURL=/fw/js/ydj/sel/sel_selectionform.js
//套件选配
; (function () {
    var sel_selectionform = (function (_super) {
        var _child = function (args) {
            var that = this;
            
            that.parentFormId = '';
            that.ignoreProductPromotionQtyOrDealAmountBeforeSetValue = [];

            _super.call(that, args);
        }

        __extends(_child, _super);

        var inited = false;
        var ps = {
            parentRow: "",
            suite: {}
        };
        var propSelection = {
            current: "",
            result: {}
        };
        var props = [];
        var oriSuite = {};
        var propSuiteCount = "";
        var InitEntry = [];

        //var selsettings = {};

        //平台在调用页面的 resizeForm 方法前触发
        _child.prototype.onBeforeResizeForm = function (args) {
            var that = this;
            var offset = 90; //偏移量
            if (args.dialogResize) {
                offset = 60;
            }
            var pageInfo = that.Model.getPageInfo();
            var baseInfoHeight = that.Model.getEleMent({ id: '.base-info' }).outerHeight(true);
            var entryHeight = pageInfo.height - baseInfoHeight - offset;
            that.Model.setEntryHeight({ id: 'fentity', value: entryHeight });
            that.Model.setEntryHeight({ id: 'fpropentity', value: entryHeight });
        };

        _child.prototype.onInitialized = function (e) {
            var that = this;
            inited = false;

            //初始化
            //参数
            that.initParams();
            //单据头
            that.initBillhead(e);
            //子件信息
            that.initEntity();

            inited = true;
        };

        //初始化参数
        _child.prototype.initParams = function () {
            var that = this;
            ps = {};
            propSelection = {
                current: "",
                result: {}
            };
            oriSuite = {
                row: "",
                rowData: {},
                compoments: []
            };
            //selsettings = {};
            ps.parentRow = that.Model.viewModel.cp.rowid;
            ps.parentpageid = that.Model.viewModel.cp.parentpageid;
            ps.canchange = that.Model.viewModel.cp.canchange;
            //套件信息
            if (that.Model.viewModel.cp.suite) {
                ps.suite = eval("(" + that.Model.viewModel.cp.suite + ")");
                //子件根据生成顺序排序
                ps.suite.fentity.sort(function (x, y) { return x.fsequence - y.fsequence; });
            }
            //相关选配类别
            if (that.Model.viewModel.cp.categories) {
                ps.categories = eval("(" + that.Model.viewModel.cp.categories + ")");
                //属性根据生成顺序排序
                ps.categories.map(function (x) {
                    x.fentity.sort(function (x, y) { return x.fdisplayseq - y.fdisplayseq; });
                })
            }
            //商品的选配类别
            if (that.Model.viewModel.cp.productcategories) {
                ps.productcategories = eval("(" + that.Model.viewModel.cp.productcategories + ")");
            }

            if (that.Model.viewModel.cp.products) {
                ps.products = eval("(" + that.Model.viewModel.cp.products + ")");
            }

            if (that.Model.viewModel.cp.props) {
                props = eval("(" + that.Model.viewModel.cp.props + ")");
            }

            //套件总件数属性
            if (that.Model.viewModel.cp.propsuitecount) {
                propSuiteCount = eval("(" + that.Model.viewModel.cp.propsuitecount + ")");
            }

            //兼容多个单据，通过单据里设置
            var parentPage = Index.getPage(ps.parentpageid);
            that.parentFormId = parentPage.formId;
            that.Model.selsettings = parentPage.Model.selsettings;

            var oriSuiteRow = parentPage.Model.getEntryRowData({ id: that.Model.selsettings.fentity, row: ps.parentRow });
            if (oriSuiteRow) {
                var suiteCombNumber = oriSuiteRow[parentPage.Model.selsettings.fsuitcombnumber];
                if (suiteCombNumber) {
                    oriSuite.row = ps.parentRow;
                    oriSuite.rowData = oriSuiteRow;
                    var parentData = parentPage.Model.getEntryData({ id: that.Model.selsettings.fentity });
                    for (var i = 0; i < parentData.length; i++) {
                        if (parentData[i][parentPage.Model.selsettings.fsuitcombnumber] && parentData[i][parentPage.Model.selsettings.fsuitcombnumber] == suiteCombNumber) {
                            if (oriSuiteRow.id != parentData[i].id) oriSuite.compoments.push(parentData[i]);
                        }
                    }
                }
            }
        }

        _child.prototype.initBillhead = function () {
            var that = this;
            //套件商品
            that.Model.setValue({ id: "fsuiteid", value: ps.suite.fproductid });
            //套件数量
            var parentPage = Index.getPage(ps.parentpageid);
            var qty = parentPage.Model.getSimpleValue({ id: that.Model.selsettings.fbizqty, row: ps.parentRow });
            that.Model.setValue({ id: "fsuiteqty", value: qty });
        }

        //初始化子件信息
        _child.prototype.initEntity = function () {
            var that = this;
            //清空
            that.Model.deleteEntryData({ id: "fentity" });
            if (!ps.suite || !ps.suite.fentity || ps.suite.fentity.length == 0) return;

            if (oriSuite && oriSuite.compoments && oriSuite.compoments.length) {
                //加载原来的选配
                for (var i = 0; i < oriSuite.compoments.length; i++) {
                    var item = oriSuite.compoments[i];
                    var cate = ps.productcategories[item[that.Model.selsettings.fmaterialid].id] || {};
                    var frow = {
                        fentityid: item.id,
                        rowid: item[that.Model.selsettings.fmaterialid].id,
                        fqty: item[that.Model.selsettings.fsubqty],
                        funstdtypeflag: item[that.Model.selsettings.funstdtype],
                        funstdtypestatus: item[that.Model.selsettings.funstdtypestatus],
                        fselcategoryid: { id: cate.id, fname: cate.fname, fnumber: cate.fnumber }
                    };

                    var rowno = that.Model.addRow({ id: "fentity", data: frow, opctx: { ignore: true } });

                    var attrs = item.fattrinfo.fentity;
                    that.initProp(rowno, attrs);
                }
            } else {
                for (var i = 0; i < ps.suite.fentity.length; i++) {
                    var item = ps.suite.fentity[i];
                    if (!item.fwhetherdefault) continue; //子件为默认时才默认加上
                    var cate = ps.productcategories[item["fpartproductid"]] || {};
                    var frow = {
                        fentityid: item["Id"],
                        rowid: item["fpartproductid"],
                        //fcomponentid: { id: item.fcomponentid_ref.id, fname: item.fcomponentid_ref.fname, fnumber: item.fcomponentid_ref.fnumber },
                        fqty: item["fqty"],
                        funstdtypeflag: ps.products[item["fpartproductid"]].funstdtype == "True" ? 1 : 0,
                        fselcategoryid: { id: cate.id, fname: cate.fname, fnumber: cate.fnumber }
                    };

                    that.Model.addRow({ id: "fentity", data: frow, opctx: { ignore: true } });
                }
            }

            var entitys = that.Model.getEntryData({ id: "fentity" });
            if (entitys.length) {
                for (var i = 0; i < entitys.length; i++) {
                    that.Model.setValue({ id: "fcomponentid", row: entitys[i].id, value: entitys[i].rowid });
                }
                that.initProp(entitys[0].id);
            } else {
                //如果全部取消默认则保留一空行
                if (entitys.length == 0) {
                    that.Model.addRow({ id: "fentity", opctx: { ignore: true }});
                }
            }

            var rows = that.Model.getEntryData({ id: "fentity" });
            for (var i = 0; i < rows.length; i++) {
                InitEntry.push({ id: rows[i].id, fname: rows[i].fcomponentid.fname, fqty: rows[i].fqty });
            }
        }

        //初始化属性选配
        _child.prototype.initProp = function (fentityrow, attrs) {
            var that = this;

            var entity = that.Model.getEntryRowData({ id: "fentity", row: fentityrow });
            if (!entity) return;
            var fselcategoryid = entity.fselcategoryid.id;
            var funstdtypeflag = !!entity.funstdtypeflag;

            var entryKey = that.getEntryKey(fentityrow);
            //设置非标列是否显示
            that.initUnstdypeFlag(funstdtypeflag);
            //判断是否切换了子件
            var isComponentChanged = that.isComponentChanged(fentityrow);
            //if (!isComponentChanged) {
            //    //当前子件没有变化时，仅设置非标列可见性
            //    return;
            //}

            //切换了子件，保存上次子件的选配内容
            if (propSelection.current) {
                that.savePropValue();
            }
            //标记当前的子件选配
            propSelection.current = entryKey;
            
            //清空属性行
            that.Model.deleteEntryData({ id: "fpropentity" });
            if (!ps.categories || ps.categories.length == 0 || !fselcategoryid) return;
            var category = null;
            for (var i = 0; i < ps.categories.length; i++) {
                if (ps.categories[i].id == fselcategoryid) {
                    category = ps.categories[i];
                    break;
                }
            }
            if (!category) return; //没有选配类别跳走
            //设置属性行
            var es = propSelection.result[entryKey] && propSelection.result[entryKey].props;
            if (es && es.length > 0) {
                es.map(function (x) {
                    that.Model.addRow({ id: "fpropentity", data: x });
                })
            } else {
                for (var i = 0; i < category.fentity.length; i++) {
                    var cp = category.fentity[i];
                    if (!cp.fiswhetherinforce) continue;

                    var fdynamicsrc = this.getPropValueSrc(cp.fpropid_ref);
                    var fdynamictype = this.getPropValueType(cp.fpropid_ref);

                    var propValue = this.getPropValue(fdynamictype, cp, attrs);
                    var frow = {
                        fentityid: cp.Id,
                        fselpropid: {
                            id: cp.fpropid,
                            fname: cp.fpropid_ref.fname,
                            fnumber: cp.fpropid_ref.fnumber
                        },
                        fpropvaluesrc: fdynamicsrc,
                        fpropvaluetype: fdynamictype,
                        fpropvalue: this.getPropValue(fdynamictype, cp, attrs),
                        fdatatype: cp.fpropid_ref.fdatatype,
                        fiscontrolmust: cp.fiscontrolmust,
                        fcustom: cp.fpropid_ref.fallowcustom
                    }

                    if (cp.fpropid_ref.fallowcustom && propValue && propValue.fname && props && props.length) {
                        
                        var fullPropValue = this.loadOrCreateProp(cp.fpropid, propValue.fname);
                        if (fullPropValue && fullPropValue.fnosuitcreate) {
                            frow.fcustomvalue = fullPropValue.fname;
                        }
                    }

                    that.Model.addRow({ id: "fpropentity", data: frow });
                }
            }
        }

        _child.prototype.initUnstdypeFlag = function (unstdtypeflag) {
            var that = this;
            //显示隐藏列
            that.Model.setVisible({
                id: ['fcustom', 'fcustomvalue'],
                value: unstdtypeflag,
                batch: true
            });
        }

        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fcustomvalue':
                    var isCustom = that.Model.getValue({ id: "fcustom", row: e.row });
                    var entityRow = that.getEntityRow();
                    var unstdtypestatus = that.Model.getSimpleValue({ id: 'funstdtypestatus', row: entityRow });
                    e.result.enabled = isCustom == "1" && unstdtypestatus == "";
                    break;
                case 'funstdtypeflag':
                    var fispresetprop = that.Model.getSimpleValue({ id: "fispresetprop", row: e.row });
                    if (!fispresetprop) {
                        e.result.enabled = false;
                    } else {
                        e.result.enabled = true;
                    }
                    break;
                case 'fsuiteqty':
                case 'fqty':
                    var funstdtypestatus = that.Model.getSimpleValue({ id: 'funstdtypestatus', row: e.row });
                    if (['02'].indexOf(funstdtypestatus) === -1) {
                        e.result.enabled = true;
                    } else {
                        e.result.enabled = false;
                    }
                    break;
            }
            //这种情况只能修改套件数量，子件数量不能单独修改
            if (ps.canchange == "true")
            {
                if (e.id.toLowerCase() == "fsuiteqty") {
                    e.result.enabled = true;
                } else {
                    e.result.enabled = false;
                }
            }

            if (that.promotionEditorState(e)) {

            }
        }

        _child.prototype.getEntryKey = function (fentityrow) {
            return "PROP_" + fentityrow;
        }

        _child.prototype.isComponentChanged = function (fentityrow) {
            var that = this;
            var entity = that.Model.getEntryRowData({ id: "fentity", row: fentityrow });
            var current = propSelection.result[propSelection.current];
            if (!current) return true;
            return !current.props || current.props.length == 0 || current.productId != (entity.fcomponentid && entity.fcomponentid.id);
        }

        _child.prototype.savePropValue = function () {
            var that = this;
            var row = propSelection.current.substring(5);
            var entity = that.Model.getEntryRowData({ id: "fentity", row: row });
            if (entity) {
                propSelection.result[propSelection.current] = {
                    row: row,
                    productId: entity.fcomponentid && entity.fcomponentid.id,
                    props: [].concat(that.Model.getEntryData({ id: "fpropentity" }))
                };
            } else {
                propSelection.result[propSelection.current] = {};
            }
        }

        _child.prototype.getPropValueType = function (prop) {
            var valueType = "";
            if (!prop || !prop.fvaluesource) return "";
            switch (prop.fvaluesource) {
                case "basedata":
                    valueType = prop.frefbaseformid;
                    break;
                case "enumdata":
                    valueType = prop.frefenumformid;
                    break;
                default:
                    valueType = "$sys_text$";
                    break;
            }
            return { id: valueType };
        }

        _child.prototype.getPropValueSrc = function (prop) {
            return {
                id: prop.fvaluesource,
                fname: prop.fvaluesource,
                fnumber: prop.fvaluesource
            };
        }

        _child.prototype.getPropValue = function (fdynamictype, cp, attrs) {

            var fpropid = cp.fpropid;
            if (attrs && attrs.length > 0) {
                for (var i = 0; i < attrs.length; i++) {
                    if (attrs[i].fauxpropid.id == fpropid) {
                        //if (fdynamictype == "basedata" || fdynamictype == "enumdata") {
                            return {
                                id: attrs[i].fvalueid,
                                fname: attrs[i].fvaluename,
                                fnumber: attrs[i].fvaluenumber
                            };
                        //} else {
                        //    return attrs[i].fvalueid;
                        //}
                    }
                }
            } else {

                if (!cp.fdefaultpropvalueid_ref) return "";
                return {
                    id: cp.fdefaultpropvalueid,
                    fname: cp.fdefaultpropvalueid_ref.fname,
                    fnumber: cp.fdefaultpropvalueid_ref.fnumber
                };
            }
        }

        //计算总套数
        _child.prototype.calcSuiteSumQty = function () {
            var that = this;
            var entitys = that.Model.getEntryData({ id: "fentity" });
            var sumQty = 0;
            entitys.map(function (x) {
                if (x.fcomponentid && x.fcomponentid.id) sumQty += x.fqty;
            });
            that.Model.setValue({ id: "fsuitesumqty", value: sumQty });
        }

        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                //子件商品
                case 'fcomponentid':
                    //只能选套件里的子件
                    e.result.filterString = "fid in ('{0}') and fendpurchase != 1".format(ps.suite.fentity.map(function (x) { return x.fpartproductid; }).join("','"));
                    var parentPage = Index.getPage(ps.parentpageid);
                    //套件选子件商品时 也需要传送达方信息给插件去过滤商品
                    var fsupplierid = parentPage.Model.getValue({ id: 'fsupplierid' });
                    var deptId = parentPage.Model.getSimpleValue({ id: 'fpodeptid' });
                    var fbilltypeid = parentPage.Model.getValue({ "id": "fbilltypeid" });
                    var fdeliverid = parentPage.Model.getValue({ "id": "fdeliverid" });
                    if (parentPage.formId == "ydj_order") {
                        fbilltypeid = parentPage.Model.getValue({ "id": "fbilltype" });
                        deptId = parentPage.Model.getValue({ "id": "fdeptid" });
                    }
                    var billtypeNo = fbilltypeid ? fbilltypeid.fnumber : "";
                    var billtypeName = fbilltypeid ? fbilltypeid.fname : "";
                    var srcPara = {
                        billtypeNo: billtypeNo,
                        billtypeName: billtypeName,
                        deptId: deptId,
                        deliverid: fdeliverid ? fdeliverid.id : "",
                        deliverno: fdeliverid ? fdeliverid.fnumber : "",
                        delivername: fdeliverid ? fdeliverid.fname : "",
                        supplierid: fsupplierid ? fsupplierid.id : "",
                        supplierNo: fsupplierid ? fsupplierid.id : "",
                        supplierName: fsupplierid ? fsupplierid.id : "",
                    };
                    e.result.simpleData = {
                        srcPara: JSON.stringify(srcPara)
                    };
                    break;
                case 'fpropvalue':
                    that.propValueQueryFilterString(e);
                    break;
            }
        };

        //表格选中行变化时触发
        _child.prototype.onSelectedRowsChanged = function (e) {
            var that = this;
            that.calcSuiteSumQty();
            if (e.id == 'fentity') {
                //切换子件时，先结束右边表格的编辑状态（这样就会触发非标值的字段值变化事件）
                that.Model.endEditEntry({ id: "fpropentity" });
                /*that.initProp(e.row);*/
                if (inited) {
                    setTimeout(function () {
                        //that.Model.deleteEntryData({ id: "fpropentity" });
                        that.initProp(e.data[0].id);
                    }, 200);
                }
            }
        };

        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fcomponentid':
                    that.calcSuiteSumQty();

                    if (inited) {
                        setTimeout(function () {
                            that.Model.deleteEntryData({ id: "fpropentity" });
                            that.initProp(e.row);
                        }, 200);
                    }
                    break;
                case 'fqty':
                    that.calcSuiteSumQty();
                    break;
                case 'funstdtypeflag':
                    that.initUnstdypeFlag(!!e.value);
                    break;
                case 'fpropvalue':
                    that.propValueFieldChanged(e);
                    break;
                case 'fcustomvalue':
                    that.customValueChanged(e);
                    break;
            }
        }

        //处理【属性值】字段变化逻辑
        _child.prototype.propValueFieldChanged = function (e) {
            var that = this;

            //加载【属性】字段相关的其他属性信息
            that.loadRelationProp(e);
        };

        //加载【属性】字段相关的其他属性信息
        _child.prototype.loadRelationProp = function (e) {
            var that = this;

            var entityRow = that.getEntityRow();
            var isNonStandard = that.Model.getValue({ id: 'funstdtypeflag', row: entityRow });
            if (isNonStandard) {
                //切换属性值后同时清空非标值
                that.Model.setValue({ id: 'fcustomvalue', value: '', row: e.row });
                return;
            } 

            //将当前属性启用选配约束
            that.Model.setValue({ id: 'fenableconstraint', value: true, row: e.row });

            if (!$.trim(e.value.fnumber)) return;

            var propObj = that.Model.getValue({ id: 'fselpropid', row: e.row });
            var propNumber = $.trim(propObj && propObj.fnumber);
            if (!propNumber) return;

            //同步加载与当前属性相关的其他属性信息
            var selCategoryId = that.Model.getSimpleValue({ id: 'fselcategoryid', row: entityRow });
            var res = that.Model.invokeFormService({
                formId: 'sel_propselectionsingle',
                opcode: 'loadrelationprop',
                sync: true,
                param: {
                    simpleData: {
                        selCategoryId: selCategoryId,
                        propNumber: propNumber
                    }
                }
            });
            var srvData = res && res.operationResult && res.operationResult.srvData;
            var relationPropNumbers = srvData && srvData.relationPropNumbers;
            if (!relationPropNumbers || relationPropNumbers.length < 1) return;

            var entry = that.Model.getEntryData({ id: 'fpropentity' });
            for (var i = 0; i < relationPropNumbers.length; i++) {
                var relPropNumber = $.trim(relationPropNumbers[i]).toLowerCase();
                for (var j = 0; j < entry.length; j++) {
                    var _propNumber = $.trim(entry[j].fselpropid.fnumber).toLowerCase();
                    if (_propNumber === relPropNumber) {
                        //将相关的属性启用选配约束
                        that.Model.setValue({ id: 'fenableconstraint', value: true, row: entry[j].id });
                        break;
                    }
                }
            }
        };

        //获取【子件信息】明细行号
        _child.prototype.getEntityRow = function () {
            var entityRow = propSelection.current.substring(5);
            return entityRow;
        };

        //处理【属性值】字段的过滤条件
        _child.prototype.propValueQueryFilterString = function (e) {
            var that = this;

            var entityrow = propSelection.current.substring(5);
            //通过 simpleData 传递自定义的参数值，这些参数值可以被后端插件事件获取，用于实现一些复杂的过滤条件
            var productId = that.Model.getSimpleValue({ id: 'fcomponentid', row: entityrow });
            var isNonStandard = !!that.Model.getValue({ id: 'funstdtypeflag', row: entityrow });
            var selCategoryId = that.Model.getSimpleValue({ id: 'fselcategoryid', row: entityrow });
            var selPropId = that.Model.getSimpleValue({ id: 'fselpropid', row: e.row });
            var enableConstraint = that.Model.getSimpleValue({ id: 'fenableconstraint', row: e.row }) === true;

            var propList = [];
            var entry = that.Model.getEntryData({ id: 'fpropentity' });
            for (var i = 0; i < entry.length; i++) {

                //当前编辑的属性被认为是被约束的属性（主要用于匹配约束条件中的约束值公式）
                var isRestricted = entry[i].id === e.row;

                var selProp = entry[i].fselpropid;
                var propValueSrc = entry[i].fpropvaluesrc;
                var dataType = entry[i].fdatatype;
                var _propValue = entry[i].fpropvalue;
                var _propValueId = _propValue;
                var _propValueName = _propValue;
                var _propValueNumber = _propValue;
                if (propValueSrc.id === 'basedata' || propValueSrc.id === 'enumdata') {
                    if (_propValue) {
                        _propValueId = _propValue.id;
                        _propValueName = _propValue.fname;
                        _propValueNumber = _propValue.fnumber;
                    }
                }
                if (_propValueName || isRestricted) {
                    propList.push({
                        propId: selProp.id,
                        propName: selProp.fname,
                        propNumber: selProp.fnumber,
                        valueId: _propValueId,
                        valueName: _propValueName,
                        valueNumber: _propValueNumber,
                        isRestricted: isRestricted,
                        propValueDataType: dataType //属性值数据类型（字符、数值）
                    });
                }
            }

            e.result.simpleData = {
                productId: productId,
                isNonStandard: isNonStandard,
                selCategoryId: selCategoryId,
                selPropId: selPropId,
                enableConstraint: enableConstraint,
                propList: JSON.stringify(propList)
            };
        };

        _child.prototype.onEntryRowClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fentity':
                    //切换子件时，先结束右边表格的编辑状态（这样就会触发非标值的字段值变化事件）
                    that.Model.endEditEntry({ id: "fpropentity"  });
                    that.initProp(e.row);
                    break;
            }
        };

        //处理【非标值】字段变化逻辑
        _child.prototype.customValueChanged = function (e) {
            var that = this;
            var row = that.Model.getSelectRows({ id: "fpropentity" });
            var cur = row.find(o => o.pkid == e.row);
            if (!cur) return;
            var propValueName = $.trim(e.value);
            if (!propValueName) return;

            var selPropId = that.Model.getSimpleValue({ id: 'fselpropid', row: e.row });

            //设置属性值
            var propValue = this.loadOrCreateProp(selPropId, propValueName);
            that.Model.setValue({ id: 'fpropvalue', value: propValue, row: e.row });

            //如果只是触发修改但是值不变就不要清空标准值
            if (row.length==1 && row[0].data.fcustomvalue == e.value) return;
            //清空标准值
            if (propValue && !propValue.fnosuitcreate) {
                setTimeout(function () {
                    that.Model.setValue({ id: 'fcustomvalue', value: '', row: e.row });
                }, 100);
            }
        };

        _child.prototype.loadOrCreateProp = function (selPropId, propValueName) {
            var that = this;
            //同步加载或创建属性值基础资料
            var res = that.Model.invokeFormService({
                formId: 'sel_propvalue',
                opcode: 'loadorcreate',
                sync: true,
                param: {
                    simpleData: {
                        propId: selPropId,
                        propValueName: propValueName
                    }
                }
            });
            var srvData = res && res.operationResult && res.operationResult.srvData;

            //设置属性值propValue
            var propValue = $.extend({ id: '', fname: '', fnumber: '' }, srvData || {});
            return propValue;
        };

        //检查属性
        _child.prototype.checkProp = function (fentityrow, msg, forCheckEntities) {
            var that = this;
            var entryKey = that.getEntryKey(fentityrow);
            var entry = propSelection.result[entryKey];
            if (!entry || !entry.props || entry.props.length == 0) { 
                //没有属性继续提交
                that.submitCheck(forCheckEntities);
                return;
            }

            var err = [];
            for (var i = 0; i < entry.props.length; i++) {
                var selProp = entry.props[i].fselpropid;
                var propValueSrc = entry.props[i].fpropvaluesrc;
                var propValue = entry.props[i].fpropvalue;
                var isControlMust = entry.props[i].fiscontrolmust;
                var selPropName = selProp && selProp.fname || '';
                var propValueId = propValue;
                if (propValueSrc.id === 'basedata' || propValueSrc.id === 'enumdata') {
                    if (propValue) {
                        propValueId = propValue.id;
                        propValueName = propValue.fname;
                        propValueNumber = propValue.fnumber;
                    }
                }
                if (isControlMust && !propValueId) {
                    err.push('第{0}行的【{1}】属性值要求必录，请检查！'.format(i + 1, selPropName));
                }
            }

            if (err.length > 0) {
                yiDialog.warn((msg ? (msg + "<br/>") : "") + err.join('<br/>'));
                return;
            }

            var rowdata = that.Model.getEntryRowData({ id: 'fentity', row: fentityrow });
            var isNonStandard = rowdata.funstdtypeflag;
            var selCategoryId = rowdata.fselcategoryid && rowdata.fselcategoryid.id;
            if (selCategoryId && !isNonStandard) {
                var productId = rowdata.fcomponentid.id;

                var propList = [];
                for (var j = 0; j < entry.props.length; j++) {
                    var pp = entry.props[j];
                    if (pp.fpropvalue && pp.fpropvalue.fname) {
                        var selProp = pp.fselpropid;
                        propList.push({
                            propId: pp.fselpropid.id,
                            propName: pp.fselpropid.fname,
                            propNumber: pp.fselpropid.fnumber,
                            valueId: pp.fpropvalue.id,
                            valueName: pp.fpropvalue.fname,
                            valueNumber: pp.fpropvalue.fnumber,
                            propValueDataType: pp.fdatatype //属性值数据类型（字符、数值）
                        });
                    }
                }
                that.Model.invokeFormService({
                    formId: 'sel_propselectionsingle',
                    opcode: 'checkpropvalue',
                    param: {
                        simpleData: {
                            productId: productId,
                            isNonStandard: isNonStandard,
                            selCategoryId: selCategoryId,
                            propList: JSON.stringify(propList)
                        }
                    },
                    callback: function (r) {
                        var isSuccess = r && r.operationResult && r.operationResult.isSuccess;
                        if (!isSuccess) {
                            if (r && r.operationResult && r.operationResult.complexMessage) {
                                yiDialog.warn((msg ? (msg + "<br/>") : "") + "选配约束条件不满足！" + "<br/>" + r.operationResult.complexMessage.errorMessages.join('<br/>'));
                                //r.operationResult.complexMessage.errorMessages.push(msg + "选配约束条件不满足！");
                            }
                            return;
                        }

                        that.submitCheck(forCheckEntities);
                    }
                });
            } else {
                that.submitCheck(forCheckEntities);
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'loadorcreate':
                    //设置属性值
                    var propValue = $.extend({ id: '', fname: '', fnumber: '' }, srvData || {});
                    that.Model.setValue({ id: 'fpropvalue', value: propValue, row: e.opctx.row });
                    break;
            }
        };

        //表单菜单点击时触发
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'submitreturn':
                    that.submitReturn(e);
                    break;
            }
        };

        _child.prototype.onEntryRowDeleted = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fentity':
                    that.calcSuiteSumQty();
                    break;
            }
        };

        _child.prototype.submitReturn = function (e) {
            var that = this;
            e.result = true;

            
            that.submitInvoke()
        };

        _child.prototype.submitInvoke = function () {
            var that = this;
            var forCheckEntities = [].concat(that.Model.getEntryData({ id: "fentity" })); //克隆
            that.submitCheck(forCheckEntities);
        }

        _child.prototype.submitCheck = function (forCheckEntities) {
            var that = this;
            if (forCheckEntities && forCheckEntities.length) { //如果还有需要检查的继续检查
                var checking = forCheckEntities.shift();
                var fentityrow = checking.id;
                var msg = "子件信息的【{0}】商品：".format(checking.fcomponentid.fname);

                //切换到各子件
                that.initProp(fentityrow);
                ////保存非标属性值
                //that.saveUntypePropValue();
                //保存子件的选配属性值到缓存
                that.savePropValue();
                //检查属性值相关，检查完会回调本方法，当forCheckEntities为空时，执行提交
                that.checkProp(fentityrow, msg, forCheckEntities);
            } else {
                that.submit(); //没有待检查的，可以提交;
            }

            //var entities = that.Model.getEntryData({ id: "fentity" });
            //for (var i = 0; i < entities.length; i++) {
            //    var fentityrow = entities[i].id;
            //    var msg = "子件信息第{0}行的【{1}】商品：".format(i + 1, entities[i].fcomponentid.fname);

            //    //切换到各子件
            //    that.initProp(fentityrow);
            //    ////保存非标属性值
            //    //that.saveUntypePropValue();
            //    //保存子件的选配属性值到缓存
            //    that.savePropValue();
            //    //检查必填项
            //    var checked = that.checkProp(fentityrow, msg);
            //    if (!checked) return;
            //}

        };

        _child.prototype.onEntryRowDeleting = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case "fentity":
                    if (ps.canchange == "true") {
                        e.result = true;
                        yiDialog.mt({ msg: '向总部采购变更, 不允许删除商品行, 如果该商品无需采购时, 请进行关闭!', skinseq: 2 });
                        return;
                    }

                    var unstdtypestatus = that.Model.getSimpleValue({ id: 'funstdtypestatus', row: e.row });
                    if (unstdtypestatus == '02')
                    {
                        e.result = true;
                        yiDialog.mt({ msg: '当前商品行为非标商品，非标审批状态=待审批, 不允许删除!', skinseq: 2 });
                        return;
                    }

                    that.promotionEntryRowDeleting(e);

                    break;
            }
        };

        //希望 手动点击行号“+”时触发 ，但是addrow时 不触发 
        _child.prototype.onEntryRowCreating = function (e) {
            var that = this;
            if (e.opctx && e.opctx.ignore) return;
            switch (e.id.toLowerCase()) {
                case "fentity":
                    if (ps.canchange=="true") {
                        e.result = true;
                        yiDialog.mt({ msg: '向总部采购变更, 不允许新增商品, 如果需采购新商品, 请创建新采购订单向总部采购!', skinseq: 2 });
                        return;
                    }
                    break;
            }
        };

        _child.prototype.submit = function () {
            var that = this;

            var parentPage = Index.getPage(ps.parentpageid);
            var suiteQty = that.Model.getSimpleValue({ id: "fsuiteqty" });
            var suiteUUID = parentPage.Model.getSimpleValue({ id: that.Model.selsettings.fsuitcombnumber, row: ps.parentRow });
            if (!suiteUUID) suiteUUID = yiCommon.uuid(32);

            var fresultbrandid = parentPage.Model.getSimpleValue({ id: 'fresultbrandid', row: ps.parentRow });
            
            parentPage.Model.setValue({ id: that.Model.selsettings.fsuitproductid, row: ps.parentRow, value: ps.suite.fproductid}); //套件商品
            parentPage.Model.setValue({ id: that.Model.selsettings.fsuitcombnumber, row: ps.parentRow, value: suiteUUID }); //套件组合号
            parentPage.Model.setValue({ id: that.Model.selsettings.fbizqty, row: ps.parentRow, value: suiteQty, tgChange: false }); //数量
            parentPage.Model.setValue({ id: that.Model.selsettings.fhqprice, row: ps.parentRow, value: 0, tgChange: false }); //套件总部零售价为0，否则与子件价格重复
            parentPage.Model.setValue({ id: that.Model.selsettings.fprice, row: ps.parentRow, value: 0 }); //套件零售价为0，否则与子件价格重复

            var compoments = that.Model.getEntryData({ id: "fentity" });

            //更新套件的辅助属性
            var suitesumqty = that.Model.getSimpleValue({ id: "fsuitesumqty" });
            var propSuiteCountValue = {
                fauxpropid: {
                    id: propSuiteCount.id,
                    fname: propSuiteCount.fname,
                    fnumber: propSuiteCount.fnumber
                },
                fvalueid: suitesumqty,
                fvaluename: suitesumqty,
                fvaluenumber: suitesumqty
            }
            var groupObj = {}; 
            var prex = "gp_";

            //http://dmp.jienor.com:81/zentao/task-view-33864.html 
            //不根据套件选配商品去匹配，直接遍历套件明细下的子件描述
            var partprop = "";
            if (ps.suite && ps.suite.fentity && ps.suite.fentity.length) {
                for (var i = 0; i < ps.suite.fentity.length; i++) {
                    if (!ps.suite.fentity[i].fpartprop_ref) continue;
                    partprop = ps.suite.fentity[i].fpartprop_ref;

                    if (!partprop) return;
                    var gKey = prex + partprop.fname;
                    if (!groupObj[gKey]) {
                        groupObj[gKey] = {
                            fauxpropid: {
                                id: partprop.id,
                                fname: partprop.fname,
                                fnumber: partprop.fnumber
                            },
                            fvalueid: 0,
                            fvaluename: 0,
                            fvaluenumber: 0
                        }
                    }
                    compoments.map(function (x) {
                        var gValue = groupObj[gKey].fvalueid || 0;
                        if (x.fcomponentid.id == ps.suite.fentity[i].fpartproductid) {
                            gValue = gValue + x.fqty;
                        }
                        groupObj[gKey].fvalueid = gValue;
                        groupObj[gKey].fvaluename = gValue;
                        groupObj[gKey].fvaluenumber = gValue;
                    });
                }
            }

            var suiteAuxEntry = [propSuiteCountValue];
            for (var p in groupObj) {
                if (p.startsWith(prex)) {
                    suiteAuxEntry.push(groupObj[p]);
                }
            }
            parentPage.Model.setValue({ id: that.Model.selsettings.fattrinfo, row: ps.parentRow, value: { fentity: suiteAuxEntry } });

            // 删除不存在的子件
            var parentEntrys = parentPage.Model.getEntryData({ id: that.Model.selsettings.fentity });

            var compomentsEntryIds = [];
            for (var i = compoments.length - 1; i >= 0; i--) {
                var comp = compoments[i];
                if (comp.fentityid) {
                    compomentsEntryIds.push(comp.fentityid);
                }
            }
            
            for (var i = 0; i < parentEntrys.length; i++) {
                var parentEntry = parentEntrys[i];
                var parentEntryId = parentEntry["id"];
                // 套件组合 且 非套件 且 非标审核状态不是02 且 此行不在子件内
                if (parentEntry["fsuitcombnumber"] == suiteUUID 
                    && !parentEntry["fissuitflag"] 
                    && parentEntry["funstdtypestatus"].id != "02" 
                    && compomentsEntryIds.indexOf(parentEntryId) == -1) {
                    
                    // parentPage.Model.setValue({ id: that.Model.selsettings.fsuitcombnumber, row: parentEntryId, value: '' });
                    var rowData = parentPage.Model.getEntryRowData({ id: that.Model.selsettings.fentity, row: parentEntryId });
                    parentPage.Model.deleteRow({ id: that.Model.selsettings.fentity, row: parentEntryId, opctx: { ignore: true } });

                    if (that.parentFormId === 'ydj_order') {
                        parentPage.plugInProxy.invoke('promotionEntryRowDeleted', {
                            delRow: rowData
                        });
                    }
                }
            }

            //添加子件
            for (var i = compoments.length - 1; i >= 0; i--) {
                if (compoments[i].fcomponentnumber == '') continue;
                var comp = compoments[i];
                var rowno = comp.fentityid || parentPage.Model.addRow({ id: that.Model.selsettings.fentity, row: ps.parentRow, opctx: { ignore: true } });

                //因为子件 非标审核状态是02 不会删除，所以这里也不再去创建行,但需要更新数量
                if (compoments[i]["funstdtypestatus"].id == "02") {
                    parentPage.Model.setValue({ id: that.Model.selsettings.fbizqty, row: rowno, value: comp.fqty * suiteQty, tgChange: false });//数量
                    parentPage.Model.setValue({ id: that.Model.selsettings.fsubqty, row: rowno, value: comp.fqty, tgChange: false });//子件数量
                } else {
                    // 判断数量是否影响商品促销
                    var fbizqty = comp.fqty * suiteQty;
                    if (that.parentFormId === 'ydj_order' && comp.fentityid) {
                        var eventArgs = {
                            row: rowno,
                            id: 'fqty',
                            value: fbizqty,
                            result: false,
                            ctx: {
                                ignorePrompt: true
                            }
                        }

                        parentPage.plugInProxy.invoke('productPromotionQtyOrDealAmountBeforeSetValue', eventArgs);

                        if (eventArgs.result) {
                            var fpromotioncombono = parentPage.Model.getValue({ id: 'fpromotioncombono', row: rowno });
                            parentPage.plugInProxy.invoke('clearPromotionInfo', fpromotioncombono, rowno);
                        }
                    }

                    parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno, value: comp.fcomponentid.id });//子件商品
                    parentPage.Model.setValue({ id: that.Model.selsettings.fsuitcombnumber, row: rowno, value: suiteUUID }); //套件组合号
                    parentPage.Model.setValue({ id: that.Model.selsettings.fsuitproductid, row: rowno, value: ps.suite.fproductid }); //套件商品
                    parentPage.Model.setValue({ id: that.Model.selsettings.funstdtype, row: rowno, value: comp.funstdtypeflag }); //套件商品
                    parentPage.Model.setValue({ id: that.Model.selsettings.fbizqty, row: rowno, value: comp.fqty * suiteQty, opctx: { ignore: true } });//数量
                    parentPage.Model.setValue({ id: that.Model.selsettings.fsubqty, row: rowno, value: comp.fqty});//子件数量
                    parentPage.Model.setValue({ id: that.Model.selsettings.funstdtypestatus, row: rowno, value: comp.funstdtypestatus });//子件数量
                    parentPage.Model.setValue({ id: 'fresultbrandid', row: rowno, value: fresultbrandid });//业绩品牌

                    var entityKey = that.getEntryKey(comp.id);

                    var props = propSelection.result[entityKey].props;
                    var auxEntry = [];
                    for (var j = 0; j < props.length; j++) {
                        var selProp = props[j].fselpropid;
                        var propValueSrc = props[j].fpropvaluesrc;
                        var propValue = props[j].fpropvalue;
                        var propValueId = propValue;
                        var propValueName = propValue;
                        var propValueNumber = propValue;
                        if (propValueSrc.id === 'basedata' || propValueSrc.id === 'enumdata') {
                            if (propValue) {
                                propValueId = propValue.id;
                                propValueName = propValue.fname;
                                propValueNumber = propValue.fnumber;
                            }
                        }
                        if (propValueName) {
                            auxEntry.push({
                                fauxpropid: {
                                    id: selProp.id,
                                    fname: selProp.fname,
                                    fnumber: selProp.fnumber
                                },
                                fvalueid: propValueId,
                                fvaluename: propValueName,
                                fvaluenumber: propValueNumber
                            });
                        }
                    }

                    //回填业务单据辅助属性值
                    var auxPropArgs = { id: that.Model.selsettings.fattrinfo, row: rowno, value: { fentity: auxEntry } };
                    //对业务单据辅助属性字段设置
                    parentPage.Model.setValue(auxPropArgs);
                }
            }

            that.Model.close();
        };

        //设值前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'funstdtypeflag':
                    var oriValue = that.Model.getSimpleValue({ id: "funstdtypeflag", row: e.row });
                    if (oriValue) {
                        e.value = true;
                        e.result = true;
                    }
                    break;
                case 'fqty':
                    /*var product = that.Model.getEntryRowData({ id: "fentity", row: e.row });*/
                    var product = InitEntry.find(o => o.id == e.row);
                    //如果 采购变更时控制采购数量只能减少
                    if (ps.canchange == "true") {
                        var oldqty = product.fqty;
                        if (oldqty != e.value && e.value != 0) {
                            e.result = true;
                            yiDialog.mt({ msg: "商品数量只允许修改为0!", skinseq: 2 });
                            e.value = oldqty;
                        }
                        //如果是增加了数量 则要提示
                        //if (oldqty < e.value) {
                        //    e.result = true;
                        //    yiDialog.mt({ msg: "商品" + product.fname + "修改后数量 " + e.value + "大于原采购数量" + oldqty + ", 采购变更时只允许减少数量, 不允许比原采购数量多!", skinseq: 2 });
                        //    e.value = oldqty;
                        //    //return;
                        //}
                    }

                    that.promotionQtyBeforeSetValue(e);
                    break;
                case 'fsuiteqty':
                    if (ps.canchange == "true")
                    {
                        var parentPage = Index.getPage(ps.parentpageid);
                        var product = ps.suite;
                        var qty = parentPage.Model.getSimpleValue({ id: that.Model.selsettings.fbizqty, row: ps.parentRow });
                        if (qty != e.value && e.value != 0) {
                            e.result = true;
                            yiDialog.mt({ msg: "商品数量只允许修改为0!", skinseq: 2 });
                            e.value = oldqty;
                        }
                        //if (qty < e.value) {
                        //    e.result = true;
                        //    yiDialog.mt({ msg: "套件商品" + product.fname + "修改后数量 " + e.value + "大于原采购数量" + qty + ", 采购变更时只允许减少数量, 不允许比原采购数量多!", skinseq: 2 });
                        //    e.value = qty;
                        //}
                    }

                    that.promotionSuiteQtyBeforeSetValue(e);
                    break;
            }
        };
        

        // 合同促销活动的删除前事件
        _child.prototype.promotionEntryRowDeleting = function (e) {
            var that = this;
            if (that.parentFormId != 'ydj_order') {
                return;
            }

            var rowData = that.Model.getEntryRowData({ id: 'fentity', row: e.row });

            var parentRowId = rowData.fentityid;

            var parentPage = Index.getPage(ps.parentpageid);
            var parentRowData = parentPage.Model.getEntryRowData({ id: that.Model.selsettings.fentity, row: parentRowId });
            // 表示新行
            if (!parentRowData) {
                return;
            }

            var eventArgs = {
                rowData: parentRowData,
                row: parentRowId
            };

            parentPage.plugInProxy.invoke('promotionEntryRowDeleting', eventArgs);
            
            e.confirmMessage = eventArgs.confirmMessage;
        }
        

        // 合同促销活动的锁定性
        _child.prototype.promotionEditorState = function (e) {
            var that = this;
            if (that.parentFormId != 'ydj_order') {
                return;
            }

            // 子件明细 且 字段不是商品时，跳出
            if (e.entityKey === 'fentity' && e.id.toLowerCase() !== 'fcomponentid') {
                return;
            }

            var rowId = e.entityKey == 'fpropentity' ? that.getEntityRow(propSelection.current) : e.row;
            var rowData = that.Model.getEntryRowData({ id: 'fentity', row: rowId });

            var parentRowId = rowData.fentityid;

            var parentPage = Index.getPage(ps.parentpageid);
            var parentRowData = parentPage.Model.getEntryRowData({ id: that.Model.selsettings.fentity, row: parentRowId });
            // 表示新行
            if (!parentRowData) {
                return;
            }

            if (parentRowData.fpromotionrule) {
                e.result.enabled = false;
            }

            // var parentFld = 'fattrinfo'; 
            // var eventArgs = {
            //     rowData: parentRowData,
            //     row: parentRowId,
            //     id: parentFld,
            //     result: e.result,
            //     entityKey: that.Model.selsettings.fentity
            // }

            // parentPage.plugInProxy.invoke('promotionEditorState', eventArgs);

            // e.result.enabled = eventArgs.result.enabled;
        }

        _child.prototype.promotionQtyBeforeSetValue = function (e) {
            var that = this;
            if (that.parentFormId != 'ydj_order') {
                return;
            }

            var rowId = e.row;
            if (that.ignoreProductPromotionQtyOrDealAmountBeforeSetValue.indexOf(rowId) != -1) {
                return;
            }

            var rowData = that.Model.getEntryRowData({ id: 'fentity', row: rowId });
            var parentRowId = rowData.fentityid;

            var parentPage = Index.getPage(ps.parentpageid);
            var parentRowData = parentPage.Model.getEntryRowData({ id: that.Model.selsettings.fentity, row: parentRowId });
            // 表示新行
            if (!parentRowData) {
                return;
            }

            var fsuiteqty = yiMath.toNumber(that.Model.getValue({ id: 'fsuiteqty' }));
            var newSumQty = e.value * fsuiteqty;
            
            var eventArgs = { 
                row: parentRowId,
                id: 'fqty',
                value: newSumQty,
                result: false,
                ctx: {
                    ignorePrompt: true
                }
            }
            
            parentPage.plugInProxy.invoke('productPromotionQtyOrDealAmountBeforeSetValue', eventArgs);

            if (eventArgs.result) {
                var newValue = e.value;
                yiDialog.c('此商品参与促销活动，是否减少数量？减少将不享受促销优惠。', function () {
                    that.ignoreProductPromotionQtyOrDealAmountBeforeSetValue.push(rowId);
                    that.Model.setValue({ id: 'fqty', value: newValue, row: rowId });
                });
                e.value = rowData.fqty;
            }
            //else {
            //    e.value = eventArgs.value;
            //}
        }

        _child.prototype.promotionSuiteQtyBeforeSetValue = function (e) {
            var that = this;
            if (that.parentFormId != 'ydj_order') {
                return;
            }

            var oldValue = yiMath.toNumber(that.Model.getValue({ id: 'fsuiteqty' }));
            var newValue = e.value;

            var dialog = false;
            var rowIds = [];

            var parentPage = Index.getPage(ps.parentpageid);            
            var rowDatas = that.Model.getEntryData({ id: 'fentity' });
            for (var i = 0; i < rowDatas.length; i++) {
                var rowData = rowDatas[i];
                var rowId = rowData.id;
                var parentRowId = rowData.fentityid;

                if (that.ignoreProductPromotionQtyOrDealAmountBeforeSetValue.indexOf(rowId) != -1) {
                    return;
                }

                var parentRowData = parentPage.Model.getEntryRowData({ id: that.Model.selsettings.fentity, row: parentRowId });
                // 表示新行
                if (!parentRowData) {
                    continue;
                }

                var fsuiteqty = newValue;
                var fqty = rowData.fqty;
                var newSumQty = fsuiteqty * fqty;
                
                var eventArgs = { 
                    row: parentRowId,
                    id: 'fqty',
                    value: newSumQty,
                    result: false,
                    ctx: {
                        ignorePrompt: true
                    }
                }
                
                parentPage.plugInProxy.invoke('productPromotionQtyOrDealAmountBeforeSetValue', eventArgs);

                if (eventArgs.result) {
                    dialog = true;
                    rowIds.push(rowId);
                }
            }

            if (dialog) {
                yiDialog.c('存在子件商品参与促销活动，是否减少数量？减少将不享受促销优惠。', function () {

                    for (var i = 0; i < rowIds.length; i++) {
                        that.ignoreProductPromotionQtyOrDealAmountBeforeSetValue.push(rowIds[i]);
                    }

                    that.Model.setValue({ id: 'fsuiteqty', value: newValue });
                });
                e.value = oldValue;
            }
        }
        

        return _child;
    })(BasePlugIn);
    window.sel_selectionform = window.sel_selectionform || sel_selectionform;
})();