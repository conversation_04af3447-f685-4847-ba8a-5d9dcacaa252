; (function () {
    var si_msapitaskparam = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };

        //继承 BasePlugIn
        __extends(_child, _super);

        _child.prototype.onInitialized = function (e) {
            var that = this;

            var parentModel = that.Model.getParentModel();
            var paramValString = parentModel.getSimpleValue({ id: "ftaskparameter" });
            if (paramValString) {
                var paramObj = JSON.parse(paramValString);
                if (paramObj && paramObj.freqapi) {
                    that.Model.setValue({ id: "freqapi", value: paramObj.freqapi });
                } 
                if (paramObj && paramObj.fmaxsize) {
                    that.Model.setValue({ id: "fmaxsize", value: paramObj.fmaxsize });
                }
                if (paramObj && paramObj.fbatchsize) {
                    that.Model.setValue({ id: "fbatchsize", value: paramObj.fbatchsize });
                }
                if (paramObj && paramObj.fretrytimes) {
                    that.Model.setValue({ id: "fretrytimes", value: paramObj.fretrytimes });
                }
            }
        };


        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                //退货
                case 'tbconfirm':
                    var data = {}; 
                    data.freqapi = that.Model.getSimpleValue({ id: 'freqapi' });
                    data.fmaxsize = that.Model.getSimpleValue({ id: 'fmaxsize' });
                    data.fbatchsize = that.Model.getSimpleValue({ id: 'fbatchsize' });
                    data.fretrytimes = that.Model.getSimpleValue({ id: 'fretrytimes' });
                     
                    if (data.fbatchsize <= 0) {
                        data.fbatchsize = 10;
                    }
                    var parentModel = that.Model.getParentModel();
                    if (parentModel) {
                        parentModel.setValue({ id: 'ftaskparameter', value: JSON.stringify(data) });
                    }

                    break;
                //取消
                case 'tbcancel':
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.si_msapitaskparam = window.si_msapitaskparam || si_msapitaskparam;
})();
