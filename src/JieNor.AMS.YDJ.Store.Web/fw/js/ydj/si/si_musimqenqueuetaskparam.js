; (function () {
    var si_musimqenqueuetaskparam = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };

        //继承 BasePlugIn
        __extends(_child, _super);

        _child.prototype.onModelDataCreated = function(args) {
            var that = this;

            var parentModel = that.Model.getParentModel();
            var paramValString = parentModel.getSimpleValue({ id: "ftaskparameter" });
            if (paramValString) {
                var paramObj = JSON.parse(paramValString);

                args.data.fbizformid = paramObj.fbizformid;
                args.data.fcondition = paramObj.fcondition;
                args.data.fcomparefieldids = paramObj.fcomparefieldids;
                args.data.fmaxsize = paramObj.fmaxsize;
                // args.data.fbatchsize = paramObj.fbatchsize || 2000;
            }
        }

        _child.prototype.onInitialized = function (e) {
            var that = this;
        };


        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                //退货
                case 'tbconfirm':
                    e.result = true;
                    if (!that.Model.validForm()) {
                        return;
                    }

                    var uiData = that.Model.clone();
                    var data = {
                        fbizformid: uiData.fbizformid,
                        fcondition: uiData.fcondition,
                        fcomparefieldids: uiData.fcomparefieldids,
                        fmaxsize: uiData.fmaxsize,
                        // fbatchsize: uiData.fbatchsize || 2000
                    }; 

                    var parentModel = that.Model.getParentModel();
                    if (parentModel) {
                        parentModel.setValue({ id: 'ftaskparameter', value: JSON.stringify(data) });
                    }

                    //关闭对话框
                    that.Model.close();

                    break;
                //取消
                case 'tbcancel':
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.si_musimqenqueuetaskparam = window.si_musimqenqueuetaskparam || si_musimqenqueuetaskparam;
})();
