/*
    其他入库单插件
    <reference path="/fw/js/basepage.js" />
    @ sourceURL=/fw/js/ydj/stk/stk_otherstockin.js
 */
; (function () {
    var stk_otherstockin = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);

            that.remindnumbers = "";
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.fentity = "fentity";

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                //仓库：按库存参数中控制可选仓库范围
                case 'fstorehouseid':
                    //debugger;                      
                    var deptid = that.Model.getSimpleValue({ id: 'fstockdeptid' });
                    var srcPara = {
                        formid: 'stk_otherstockin',
                        deptid: deptid,
                    };
                    e.result.simpleData = {
                        srcPara: JSON.stringify(srcPara)
                    };
                     
                    break;
                //商品基础资料
                case 'fmaterialid':
                    var fsupplierid = that.Model.getValue({ "id": "fsupplierid" })
                    var fbilltypeid = that.Model.getValue({ "id": "fbilltype" })
                    var billtypeNo = fbilltypeid ? fbilltypeid.fnumber : "";
                    var billtypeName = fbilltypeid ? fbilltypeid.fname : "";
                    var srcPara = {
                        billtypeid: fbilltypeid ? fbilltypeid.id:"",
                        billtypeNo: billtypeNo,
                        billtypeName: billtypeName,
                        supplierid: fsupplierid ? fsupplierid.id : "",
                        supplierNo: fsupplierid ? fsupplierid.id : "",
                        supplierName: fsupplierid ? fsupplierid.id : "",
                    };
                    e.result.simpleData = {
                        srcPara: JSON.stringify(srcPara)
                    };
                    break;
            }
        };

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            switch (e.opcode) {
                //标准定制
                case 'showstandardcustom':
                    e.result = true;
                    that.showstandardcustom();
                    break;
                //非标定制
                case 'showunstandardcustom':
                    e.result = true;
                    that.showunstandardcustom();
                    break;
                case 'barcodequery':
                    that.querybarcode(e);
                    break;
                case 'packorder':
                    debugger
                    that.packorder(e);
                    break;
                case 'save':
                    // 商品档案勾选了<允许选配>或<非标产品>校验合同辅助属性和定制说明,
                    var productEntry = that.Model.getEntryData({ id: that.fentity });
                    var checkEntry = [];
                    for (var i = 0; i < productEntry.length; i++) {
                        var attrinfoEntity = productEntry[i].fattrinfo.fentity;
                        if (attrinfoEntity == null) attrinfoEntity = [];
                        var entryitem = {
                            fproductid: productEntry[i].fmaterialid.id,
                            fseq: productEntry[i].FSeq,
                            fattrinfo: attrinfoEntity.length > 0 ? JSON.stringify(attrinfoEntity) : "",
                            fcustomdesc: productEntry[i].fcustomdesc,
                        };
                        checkEntry.push(entryitem);
                    }
                    var param = {
                        simpleData: {
                            formId: 'stk_otherstockin',
                            entry: JSON.stringify(checkEntry),
                            status: that.Model.getSimpleValue({ id: 'fstatus' }),
                            domainType: 'dynamic'
                        }
                    };
                    yiAjax.p('/bill/stk_otherstockin?operationno=checkproducts', param, function (r) {
                        var res = r.operationResult;
                        e.result = true;
                        var param = e.param;
                        param.formId = "stk_otherstockin";
                        if (res.isSuccess && res.srvData.length > 0) {
                            yiDialog.c(res.srvData, function () {
                                that.Model.invokeFormOperation({
                                    id: 'tbSave',
                                    opcode: 'save',
                                    param: param
                                });
                            });
                        }
                        else {
                            that.Model.invokeFormOperation({
                                id: 'tbSave',
                                opcode: 'save',
                                param: param
                            });
                        }
                    }, null, null, null, { async: false });
                    break;
                case 'saveaudit':
                case 'rejectflow':
                case 'auditflow':
                    var checkorder = that.checkOrder(e.opcode.toLowerCase());
                    if (checkorder && that.remindnumbers) {
                        e.result = true;
                        yiDialog.d({
                            id: 'remindnumbers',
                            type: 1,
                            resize: false,
                            maxmin: false,
                            title: '系统提示',
                            content: that.remindnumbers,
                            area: ['400px', '200px'],
                            btn: ['确定'],
                            yes: function (index, layero) {
                                layer.close(index);
                            }
                        });
                    }
                    break;
            }
        };

        //检查订单是否满足条件
        _child.prototype.checkOrder = function (opname) {
            var that = this;
            var isremind = false;

            var selectedRows;
            if (that.Model.viewModel.domainType == Consts.domainType.bill) {
                selectedRows = [{ pkValue: that.Model.pkid }];
            }
            if (that.Model.viewModel.domainType == Consts.domainType.list) {
                selectedRows = that.Model.getSelectRows();
                if (!selectedRows || selectedRows.length <= 0) {
                    yiDialog.mt({ msg: '请至少选择一条数据。', skinseq: 2 });
                    return;
                }
            }

            var ids = [];
            for (var i = 0; i < selectedRows.length; i++) {
                ids.push(selectedRows[i].pkValue);
            }
            var param = {
                simpleData: {
                    formId: 'stk_otherstockin',
                    Ids: ids.join(","),
                    opname: opname
                }
            };

            yiAjax.p('/bill/stk_otherstockin?operationno=verifyproduct', param, function (r) {
                that.Model.unblockUI({ id: '#page#' });
                var res = r.operationResult;
                var srvData = r.operationResult.srvData;
                if (res.isSuccess && srvData) {
                    isremind = true;
                    that.remindnumbers = srvData;
                }
            }, null, null, null, { async: false });
            return isremind;
        };

        //打码
        _child.prototype.packorder = function (e) {
            var that = this;
            e.result = true;
            var fbillno = $.trim(that.Model.getSimpleValue({ id: 'fbillno' }));
            
                that.Model.invokeFormOperation({
                    id: 'IsExistPackOrder',
                    opcode: 'IsExistPackOrder',
                    param: {
                        'formId': 'bcm_packorder',
                        'fsourcetype': 'stk_otherstockin',
                        'fsourcenumber': fbillno,
                    }
                });
        };

        //条码联查
        _child.prototype.querybarcode = function (e) {
            var that = this;
            e.result = true;

            var selRows = that.Model.getSelectRows({ id: that.fentity });
            if (!selRows || selRows.length < 1) {
                yiDialog.warn('请先选中行再操作!');
                return;
            }
            //if (selRows.length > 1) {
            //    yiDialog.warn('只允许勾选一行进行条码联查!');
            //    return;
            //}
            var fbillno = $.trim(that.Model.getSimpleValue({ id: 'fbillno' }));
            if (fbillno == '' || fbillno == undefined) {
                yiDialog.mt({ msg: '请保存单据', skinseq: 2 });
                return;
            }
            var datas = [];
            for (var i = 0; i < selRows.length; i++) {
                datas.push({ seldata: selRows[i].data.fmaterialid.id });
            }
            //JSON.stringify(datas)
            that.Model.invokeFormOperation({
                id: 'isexsitbarcode',
                opcode: 'isexsitbarcode',
                param: {
                    'formId': 'bcm_barcodemaster',
                    'fsourcetype': 'stk_otherstockin',
                    'fsourcenumber': fbillno,
                    'fmaterialid': JSON.stringify(datas),
                }
            });
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fmaterialid':
                    that.SetDefaultStockInfo(true, e.row)
                    break;
                case 'fstockdeptid':
                    that.SetDefaultStockInfo(false, e.row)
                    break;
            }
        };

        //表格按钮点击
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            if (e.id == that.fentity) {
                switch (e.btnid.toLowerCase()) {
                    case 'g_record': //批录按钮 
                        //仓位禁止批录，需求#35670 
                        if (e.fieldId.toLowerCase() === 'fstorehouseid') {
                            e.copyFields = ["fstockstatus"];
                            e.clearFields = ["fstorelocationid"];
                        }
                        break;
                }
            }
        };

        //标准定制
        _child.prototype.showstandardcustom = function () {
            debugger;
            var that = this;
            //选中行
            var ds = that.Model.getSelectRows({ id: that.fentity });
            if (ds.length == 0) {
                yiDialog.warn('请选择一条数据再进行标准定制！');
                return;
            }
            else if (ds.length > 1) {
                yiDialog.warn('标准定制不支持多选！');
                return;
            };
            if (ds) {
                ////选中行
                //判断物料是否启用 选配类别 
                var fisunstandard = ds[0].data.funstdtype;
                var fissuit = ds[0].data.fissuitflag;
                //是否启用选配
                var Isenableselectioncategory = false;
                if (ds[0].data.fselcategoryid && ds[0].data.fselcategoryid != '') {
                    Isenableselectioncategory = true;
                }
                //当前行产品启用选配且未勾选非标定制
                if (Isenableselectioncategory && !fisunstandard) {
                    //如果当前行 非选配套件，则弹出“标准定制-单件”功能框
                    if (!fissuit) {
                        //弹出“标准定制-单件”功能框 
                        that.Model.propSelection({
                            auxPropFieldKey: 'fattrinfo', //辅助属性字段标识
                            productId: ds[0].data.fmaterialid.id, //商品ID
                            row: ds[0].data.id //辅助属性字段所在的明细行ID
                        });
                    }
                }
                else {
                    yiDialog.warn('当前商品未启用选配或勾选了非标定制，不允许标准定制！');
                    return;
                }
            }
        };

        //非标定制
        _child.prototype.showunstandardcustom = function () {
            debugger;
            var that = this;
            //选中行
            var that = this;
            var ds = that.Model.getSelectRows({ id: that.fentity });
            if (ds.length == 0) {
                yiDialog.warn('请选择一条数据再进行非标定制！');
                return;
            }
            else if (ds.length > 1) {
                yiDialog.warn('非标定制不支持多选！');
                return;
            }
            if (ds) {
                //判断选中行物料是否启用 选配类别
                var fisunstandard = ds[0].data.funstdtype;
                var fissuit = ds[0].data.fissuitflag;
                //允许选配
                var fispresetprop = ds[0].data.fispresetprop;
                //是否启用选配
                var Isenableselectioncategory = false;
                if (ds[0].data.fselcategoryid && ds[0].data.fselcategoryid != '') {
                    Isenableselectioncategory = true;
                }
                //当前行产品启用选配且 勾选非标定制
                if (fispresetprop && fisunstandard) {
                    //如果当前行 非选配套件，则弹出“标准定制-单件”功能框
                    if (!fissuit) {
                        //弹出“标准定制-单件”功能框
                        that.Model.propSelection({
                            auxPropFieldKey: 'fattrinfo', //辅助属性字段标识
                            productId: ds[0].data.fmaterialid.id, //商品ID
                            row: ds[0].data.id //辅助属性字段所在的明细行ID
                        });
                    }
                }
                else {
                    yiDialog.warn('当前商品未启用选配或未勾选非标定制，不允许非标准定制！');
                    return;
                }
            }
        };

        //辅助属性编辑页面字段值改变事件
        _child.prototype.onFlexFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fattrinfo':
                    that.aftersetauxProp(e);
                    break;
            }
        };

        //标准定制、非标定制后根据属性、属性值 匹配带出配件
        _child.prototype.aftersetauxProp = function (e) {
            debugger;
            var that = this;
            if (!e.value.fentity) {
                return;
            }
            var attrinfo = e.value;
            var attrinfoNo = attrinfo.fentity.filter(o => o.fvaluename == "无");
            that.attrinfoNew = [];
            if (attrinfoNo) {
                attrinfoNo.forEach(o => { that.attrinfoNew.push(o.fauxpropid.fname) });
            }
            var rowData = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
            var productId = $.trim(rowData.fmaterialid && rowData.fmaterialid.id);
            if (attrinfo.fentity && attrinfo.fentity.length > 0) {
                var isCheckCustom = false;
                var ds = that.Model.getSelectRows({ id: that.fentity });
                if (ds.length > 0) {
                    var rowid = ds[0].data.id;
                    isCheckCustom = rowid == rowData.id;
                }
                if (isCheckCustom || rowData.fsuitcombnumber) isCheckCustom = true;

                if (isCheckCustom) {
                    that.Model.invokeFormOperation({
                        id: 'doaddparts_custom',
                        opcode: 'doaddparts_custom',
                        param: {
                            formId: 'stk_inventoryverify',
                            rows: JSON.stringify(attrinfo),
                            currentrow: JSON.stringify(rowData),
                            productId: productId,
                            rowId: e.row,
                            domainType: 'dynamic'
                        }
                    });
                }
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'doaddparts_custom':
                    if (isSuccess && srvData) {
                        debugger;
                        //如果匹配到标准品商品id则直接更新商品
                        if (srvData.standardId && srvData.standardId != undefined) {
                            //// 触发获取商品其他信息
                            var row = that.Model.getEntryRowData({ id: that.fentity, row: srvData.rowId });
                            that.Model.setValue({ id: 'fmaterialid', row: srvData.rowId, value: srvData.standardId });
                            that.Model.setValue({ id: 'fdostandard', row: srvData.rowId, value: 1 });
                        }
                    }
                    break;
                case 'isexistpackorder':
                    //已经存在对应的《包装清单》时, 将对应的《包装清单》在弹出的视窗中打开表单
                    if (isSuccess) {
                        var fbillno = $.trim(that.Model.getSimpleValue({ id: 'fbillno' }));
                        var filterString = " fsourcetype ='{0}' and fsourcenumber='{1}' and fmainorgid = '{2}'"
                            .format('stk_otherstockin', fbillno, Consts.loginCompany.id);
                        that.Model.showForm({
                            formId: 'bcm_packorder',
                            domainType: Consts.domainType.list,
                            param: {
                                openStyle: Consts.openStyle.modal,
                                filterstring: filterString
                            }
                        });
                    } else {
                        //不存在对应的《包装清单》时, 系统自动弹出全新的《包装清单》新增界面,
                        that.Model.invokeFormOperation({
                            id: 'push',
                            opcode: 'push',
                            param: {
                                'ruleId': "stk_otherstockin2bcm_packorder"
                            }
                        });
                    }
                    break;
                //case 'isexsitbarcode':
                //    //if (!isSuccess) {
                //    //    yiDialog.warn("当前商品未查询到对应条码信息!");
                //    //    return false;
                //    //}
                //    //弹出《条码主档》列表界面
                //    var filterString = "fmainorgid = '{0}' and fid in ({1})"
                //        .format(Consts.loginCompany.id, srvData.split(','));
                //    that.Model.showForm({
                //        formId: 'bcm_barcodemaster',
                //        domainType: Consts.domainType.list,
                //        param: {
                //            openStyle: Consts.openStyle.modal,
                //            filterstring: filterString
                //        }
                //    });
                //    break;

            }
        };

        ///获取默认的仓库
        _child.prototype.SetDefaultStockInfo = function (isMatChange, rowIndex) {
            var that = this;
            var deptid = that.Model.getSimpleValue({ id: 'fstockdeptid' });
            if (!deptid) {
                //没有设置部门，不需要请求获取数据
                return;
            }
            if (isMatChange) {
                var fstorehouseid = that.Model.getSimpleValue({ id: 'fstorehouseid', row: rowIndex });
                if (fstorehouseid) {
                    //修改物料时，如果已经有仓库，不需要再设置默认的
                    return;
                }
            }

            yiAjax.p('/bill/ydj_storehouse?operationno=getdefaultstockinfo&srcformid=stk_otherstockin&deptid=' + deptid, null,
                function (r) {
                    var data = r.operationResult;                    
                    if (data.isSuccess) {
                        //库存参数中启用了部门仓库控制
                        var stockInfo = data.srvData;
                        if (stockInfo) {
                            //设置默认仓库
                            if (isMatChange) {
                                that.Model.setValue({ id: 'fstorehouseid', row: rowIndex, value: stockInfo.id });
                            }
                            else {
                                var ds = that.Model.getEntryData({ id: 'fentity' });
                                for (var i = 0, j = ds.length; i < j; i++) {
                                    that.Model.setValue({ id: 'fstorehouseid', row: ds[i].id, value: stockInfo.id });
                                }
                            }
                        }
                        else {
                            //清空仓库
                            if (isMatChange) {
                                that.Model.setValue({ id: 'fstorehouseid', row: rowIndex, value: "" });
                            }
                            else {
                                var ds = that.Model.getEntryData({ id: 'fentity' });
                                for (var i = 0, j = ds.length; i < j; i++) {
                                    that.Model.setValue({ id: 'fstorehouseid', row: ds[i].id, value: '' });
                                }
                            }
                        }
                    } else {

                    }
                }, null, null, null, { async: false }
            );

        }


        return _child;
    })(BasePlugIn);
    window.stk_otherstockin = window.stk_otherstockin || stk_otherstockin;
})();