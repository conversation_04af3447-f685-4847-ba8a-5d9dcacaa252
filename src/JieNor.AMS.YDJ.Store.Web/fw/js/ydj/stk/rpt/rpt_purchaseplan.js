///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/stk/rpt/rpt_purchaseplan.js
*/
; (function () {
    var rpt_purchaseplan = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        __extends(_child, _super);

        //在原型上定义所有实例共享成员，以便复用

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************

        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************

        //表格单元格点击事件
        _child.prototype.onEntryCellClick = function (e) {
            var that = this;
            if (e.id === 'list') {
                
                var filter = JSON.parse(that.Model.viewModel.getCustomFilterData());
                if (e.fieldId === 'invqty') {
                    e.result = true;
                    if (!e.data || e.data.invqty <= 0) return;
                    ////根据即时库存Id显示预留单
                    //var stockId = e.data.fbillhead_id;
                    //if (stockId) {
                    //    that.Model.invokeFormOperation({
                    //        id: 'showreserve',
                    //        opcode: 'showreserve',
                    //        param: {
                    //            formId: 'stk_reservebill',
                    //            stockId: stockId
                    //        }
                    //    });
                    //}

                    var filterstring = " fmaterialid='" + e.data.fmaterialid + "') and fcustomdesc=N'" + e.data.fcustomdesc + "' and fmtono='' and fattrinfo_e=N'" + e.data.fattrinfo_e + "' " ;
                    var param = {
                        'formId': 'ydj_storehouse'
                        }
                    //不参与统计的仓库，就不显示在点开的详情中
                    yiAjax.p('/bill/ydj_storehouse?operationno=getcalculate', param, function (r) {
                        var res = r.operationResult
                        if (res.isSuccess & res.srvData != null) {
                            var storehouseids = [];
                            var ids = res.srvData.split(',');
                            for (var i = 0; i < ids.length; i++) {
                                storehouseids.push("'" + ids[i] + "'");
                            }
                            filterstring = filterstring + "and fstorehouseid in ({0})".format(storehouseids.join(','));
                        }
                    }, null, null, null, { async: false });
                    that.Model.showList({
                        formId: 'stk_inventorylist',
                        filterstring: filterstring,
                        openStyle: Consts.openStyle.modal
                    });
                }
                else if (e.fieldId === 'inpurqty') {
                    e.result = true;
                    if (!e.data || e.data.inpurqty <= 0) return;
                    that.Model.showList({
                        formId: 'ydj_purchaseorder',
                        filterstring: " fmaterialid='" + e.data.fmaterialid + "') and fcustomdes_e=N'" + e.data.fcustomdesc + "' and fclosestatus_e in ('0','2') and fcancelstatus='0' and fstatus='E'  and fattrinfo_e=N'" + e.data.fattrinfo_e + "' ",// and (fclosestatus='' or fclosestatus='0')
                        openStyle: Consts.openStyle.modal
                    });
                }
                else if (e.fieldId === 'needqty') {
                    e.result = true;
                    if (!e.data || e.data.needqty <= 0) return;
                    var datefrom = filter.fdatefrom;
                    var dateto = filter.fdateto;
                    if (datefrom=='') {
                        var y = new Date().getFullYear(); //获取年份
                        var m = new Date().getMonth() + 1; //获取月份
                        var d = new Date(y, m, 0).getDate(); //获取当月最后一日
                        m = m < 10 ? '0' + m : m; //月份补 0
                        d = '01'; // 

                        var first_date = [y, m, d].join("-");
                        datefrom = first_date;
                    }
                    if (dateto == '') {
                        dateto = new Date().format('yyyy-MM-dd');
                    }
                    var forderstatus = filter.forderstatus.id.split(',');
                    var status = " and fstatus in ('{0}')".format(forderstatus.join("','"));
                    var filterstring = " fdeliverydate>='" + datefrom + " 00:00:00' and fdeliverydate<'" + dateto + " 23:59:59' and fproductid='" + e.data.fmaterialid + "' and fcustomdes_e=N'" + e.data.fcustomdesc + "' and fclosestatus_e in('0','2') and fcancelstatus='0' " + status
                        + "and (fneedtransferorder='0' or (fneedtransferorder='1' and fisoutspot='1')) and fattrinfo_e=N'" + e.data.fattrinfo_e + "' ";
                    debugger;
                    that.Model.showList({
                        formId: 'ydj_order',
                        filterstring: filterstring,// and (fclosestatus='' or fclosestatus='0')
                        openStyle: Consts.openStyle.modal
                    });
                }
            }
        };

        _child.prototype.onFieldValueFormat = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case 'invqty':
                case 'inpurqty':
                case 'needqty':
                    e.value = '<span style="color:#529DE3;cursor:pointer;">{0}</span>'.format(e.value);
                    e.cancel = true;
                    break;
            }
        }

        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                case 'showordertobuy':
                    //e.result = true;
                    //var selectedRows = that.Model.getSelectRows();
                    //var filter = JSON.parse(that.Model.viewModel.getCustomFilterData());
                    //var fmaterialIds = [];
                    //var fmaterialIds_fname = [];
                    //var fproductIds;
                    //var fproductIds_name;
                    //if (selectedRows.length > 0)
                    //{
                    //    selectedRows.forEach(function (row) {
                    //        fmaterialIds.push(row.data["fmaterialid"]);
                    //        fmaterialIds_fname.push(row.data["fmaterialid_fname"]);
                    //    });
                    //    fproductIds = fmaterialIds.join(',');
                    //    fproductIds_name = fmaterialIds_fname.join(',');
                    //}
                    //else
                    //{
                    //    fproductIds = filter.fmaterialid.id;
                    //    fproductIds_name = filter.fmaterialid.name;
                    //}
                    //var myDate = new Date();
                    //var year = myDate.getFullYear();
                    //var month = myDate.getMonth() + 1;
                    //var lastDate = new Date(year, month, 0).getDate();
                    //month = month < 10 ? '0' + month : month;
                    //that.Model.setValue({ id: 'fdatefrom', value: [year, month, '01'].join("-") });
                    //that.Model.setValue({ id: 'fdateto', value: [year, month, lastDate].join("-") });
                    //var datefrom = filter.fdatefrom;
                    //var dateto = filter.fdateto;
                    //if (datefrom == '') {
                    //    datefrom = [year, month, '01'].join("-");
                    //}
                    //if (dateto == '') {
                    //    dateto = new Date().format('yyyy-MM-dd');
                    //}
                    //debugger;
                    //var param =
                    //{
                    //    fproductIds: fproductIds,
                    //    fproductIds_name: fproductIds_name,
                    //    fdatefrom: datefrom,
                    //    fdateto: dateto,
                    //    fisshow: filter.fisshow,
                    //    fsupplierid: filter.fsupplierid.id,
                    //    fsupplierid_txt: filter.fsupplierid.name,
                    //    fisbz: filter.fisbz.id,
                    //    fisdz: filter.fisdz.id,
                    //    fcategoryid: filter.fcategoryid.id,
                    //    fcategoryid_txt: filter.fcategoryid.name,
                    //    forderstatus: filter.forderstatus.id,
                    //    fwarehousetype: filter.fwarehousetype.id
                    //}
                    //that.Model.showListReport({
                    //    formId: 'rpt_ordertobuy',
                    //    openStyle: 'modal',
                    //    param: param
                    //});
                    break;
            }
        }

        return _child;
    })(ListReportPlugin);
    window.rpt_purchaseplan = window.rpt_purchaseplan || rpt_purchaseplan;
})();