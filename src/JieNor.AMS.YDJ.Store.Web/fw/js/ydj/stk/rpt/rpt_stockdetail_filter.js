///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/stk/rpt/rpt_stockdetail_filter.js
*/
; (function () {
    var rpt_stockdetail_filter = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);




        //初始化页面插件
        _child.prototype.onInitialized = function (e) {
            var that = this;
            var parentViewModel = Index.getPage(that.Model.viewModel.parentPageId);
            debugger
            var myDate = new Date();
            var year = myDate.getFullYear();
            var month = myDate.getMonth() + 1;
            var lastDate = new Date(year, month, 0).getDate();
            month = month < 10 ? '0' + month : month;
            that.Model.setValue({ id: 'fdatefrom', value: [year, month, '01'].join("-") });
            that.Model.setValue({ id: 'fdateto', value: [year, month, lastDate].join("-") });
            if (parentViewModel) {
                //if (parentViewModel.parentId == "rpt_stocksynthesize") {
                //    that.Model.setValue({ id: 'fnotshowwhennotran', value: false });
                //    that.Model.setValue({ id: 'fnotshowwhennoending', value: false });
                //}

                var secondParentViewModel = Index.getPage(parentViewModel.parentPageId);
                if (secondParentViewModel && secondParentViewModel.formId == 'rpt_stocksummary') {
                    var param = JSON.parse(localStorage.getItem("summaryfilterparam"));
                    for (var data in param) {
                        if (data == 'id' || data == 'parentPageId') {
                            continue;
                        }
                        that.Model.setValue({ id: data, value: param[data] });
                    }
                }
            }
        };
        //元素点击事件
        _child.prototype.onElementClick = function (e) {
            var that = this;
            var opcode = e.id.toLowerCase();
            switch (e.id.toLowerCase()) {

                case 'clearall':

                    //自定义搜索全部置空按钮
                    that.Model.setValue({ id: 'fdatefrom', value: new Date().ToString() });
                    that.Model.setValue({ id: 'fdateto', value: new Date().ToString() });
                    var _des = {
                        id: '',
                        fnumber: '',
                        fname: ''
                    }
                    that.Model.setValue({ id: 'fmaterialidfrom', value: _des });//商品编码
                    that.Model.setValue({ id: 'fmaterialidto', value: _des });
                    that.Model.setValue({ id: 'fstorehouseidfrom', value: _des });//仓库
                    that.Model.setValue({ id: 'fstorehouseidto', value: _des });
                    that.Model.setValue({ id: 'fstorelocationidfrom', value: _des });//仓库
                    that.Model.setValue({ id: 'fstorelocationidto', value: _des });

                    that.Model.setValue({ id: 'fmtonofrom', value: '' });
                    that.Model.setValue({ id: 'fmtonoto', value: '' });

                    that.Model.setValue({ id: 'fbillstatus', value: { id: 'E', name: '' } });
                    break;
            };
        };

        return _child;
    })(BasePlugIn);
    window.rpt_stockdetail_filter = window.rpt_stockdetail_filter || rpt_stockdetail_filter;
})();