/// <reference path="/fw/js/platform/mvvm/baseplugin.js" />
//@ sourceURL=/fw/js/ydj/mt/mt_selectionruleform.js
//选配规则弹窗
; (function () {
    var mt_selectionruleform = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        }

        __extends(_child, _super);

        var dimensions = [];
        var dimensionRanges = {};
        var activedField = {};

        var rulemap = {
            condition: {
                dimension: "fcdndimensionid",
                text: "fcdndimensionvaluetext",
                value: "fcdndimensionvalue"
            },
            content: {
                dimension: "fctndimensionid",
                text: "fctndimensionvaluetext",
                value: "fctndimensionvalue",
                deftext: "fctndimensiondefvaluetext",
                defvalue: "fctndimensiondefvalue",
                lock: "fctndimensionlock"
            }
        }

        _child.prototype.onInitialized = function (e) {
            var that = this;

            if (that.Model.viewModel.cp.dimensions) {
                dimensions = eval("(" + that.Model.viewModel.cp.dimensions + ")");
            }
            if (that.Model.viewModel.cp.dimensionRanges) {
                dimensionRanges = eval("(" + that.Model.viewModel.cp.dimensionRanges + ")");
            }
            if (that.Model.viewModel.cp.rule) {
                var rule = eval("(" + that.Model.viewModel.cp.rule + ")");

                //是否无条件
                var uncodition = rule.condition.length == 0;
                that.Model.setValue({ id: "funcondition", value: uncodition });
                $("#conditionpanel").toggle(!uncodition);

                if (!uncodition) {

                    var conditions = rule.condition.map(function (x) {
                        var co = {};
                        for (var p in rulemap.condition) {
                            co[rulemap.condition[p]] = x[p];
                        }
                        return co;
                    });
                    that.Model.uiData.fconditionentity = conditions;
                    that.Model.refreshEntry({ id: "fconditionentity" });

                }

                var contents = rule.content.map(function (x) {
                    var co = {};
                    for (var p in rulemap.content) {
                        co[rulemap.content[p]] = x[p];
                    }
                    return co;
                })
                that.Model.uiData.fcontententity = contents;
                that.Model.refreshEntry({ id: "fcontententity" });
            }

        };

        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode) {
                case 'selectionconfirm':
                    e.result = true;
                    var parentpageid = that.Model.viewModel.cp.parentId;
                    if (!parentpageid) return;
                    var parentPage = Index.getPage(parentpageid);
                    var status = parentPage.Model.getValue({ id: "fstatus" });
                    if (status.id == "D" || status.id == "E") {
                        yiDialog.warn("当前单据状态不允许修改");
                        return;
                    }
                    var rule = {};
                    if (that.Model.uiData.funcondition || that.Model.uiData.fconditionentity.length == 0) {
                        rule.condition = [];
                    } else {
                        rule.condition = that.Model.uiData.fconditionentity.map(function (x) {
                            var co = {};
                            for (var p in rulemap.condition) {
                                co[p] = x[rulemap.condition[p]];
                            }
                            return co;
                        })
                    }

                    if (that.Model.uiData.fcontententity.length == 0) {
                        yiDialog.warn("请设置限制内容。");
                        break;
                    } else {
                        rule.content = that.Model.uiData.fcontententity.map(function (x) {
                            var co = {};
                            for (var p in rulemap.content) {
                                co[p] = x[rulemap.content[p]];
                            }
                            return co;
                        });
                    }

                    parentPage.plugInProxy.invoke("setRule", { rowid: that.Model.viewModel.cp.rowid, rule: rule });
                    that.Model.close();                    
                    break;
            }
        };

        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'funcondition':
                    //触发条件是否需要
                    $("#conditionpanel").toggle(!e.value);
                    break;
                case 'fcdndimensionid':
                    //维度变更后，清空原维度设置的内容
                    that.Model.setValue({ id: "fcdndimensionvaluetext", row: e.row, value: "" });
                    that.Model.setValue({ id: "fcdndimensionvalue", row: e.row, value: "" });
                    break;
                case 'fctndimensionid':
                    //维度变更后，清空原维度设置的内容
                    that.Model.setValue({ id: "fctndimensionvaluetext", row: e.row, value: "" });
                    that.Model.setValue({ id: "fctndimensionvalue", row: e.row, value: "" });
                    that.Model.setValue({ id: "fctndimensiondefvaluetext", row: e.row, value: "" });
                    that.Model.setValue({ id: "fctndimensiondefvalue", row: e.row, value: "" });
                    that.Model.setValue({ id: "fctndimensionlock", row: e.row, value: "false" });
                    break;
            }
        };

        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fcdndimensionid':
                    e.result.filterString = that.getDimensionFilter("fconditionentity", "fcdndimensionid");
                    break;
                case 'fctndimensionid':
                    e.result.filterString = that.getDimensionFilter("fcontententity", "fctndimensionid");
                    break;
            }
        };

        //获取维度弹窗的筛选条件
        _child.prototype.getDimensionFilter = function (entryid, dimensionFieldId) {
            var that = this;
            var filter = "";
            var ds = [];
            //选配范围已限制的维度
            for (var p in dimensionRanges) {
                ds.push(p.substr(1));
            }
            //选配维度为文本
            for (var i = 0; i < dimensions.length; i++) {
                if (dimensions[i]["fvaluetype"] == "text") {
                    ds.push(dimensions[i]["fnumber"]);
                }
            }
            filter = "fnumber in ('" + ds.join("','") + "')";

            var selected = [];
            var data = that.Model.getEntryData({ id: entryid });
            for (var i = 0; i < data.length; i++) {
                if (data[i][dimensionFieldId].id) {
                    selected.push(data[i][dimensionFieldId].id);
                }
            }
            filter += " AND fid not in ('" + selected.join("','") + "')";

            return filter;
        }

        _child.prototype.onEntryCellClick = function (e) {
            var that = this;

            //标记当前的编辑字段
            activedField = {
                row: e.row,
                fentityId: e.id,
                fieldId: e.fieldId
            };

            switch (e.fieldId.toLowerCase()) {
                //触发值
                case 'fcdndimensionvaluetext':
                    if (!e.data.fcdndimensionid.id) {
                        break;
                    }
                    that.showValueForm(e.data.fcdndimensionid.id, true, e);
                    break;
                //可选值
                case 'fctndimensionvaluetext':
                    if (!e.data.fctndimensionid.id) {
                        break;
                    }
                    that.showValueForm(e.data.fctndimensionid.id, true, e);
                    break;
                //默认值
                case 'fctndimensiondefvaluetext':
                    if (!e.data.fctndimensionid.id) {
                        break;
                    }
                    that.showValueForm(e.data.fctndimensionid.id, false, e);
                    break;
            }
        };

        _child.prototype.onAfterSelectFormData = function (e) {
            if (!e || !e.formId) return;
            var that = this;

            var ids = [];
            var names = [];
            for (var i = 0; i < e.data.length; i++) {
                ids.push(e.data[i].fbillhead_id);
                names.push(e.data[i].fname);
            }
            //显示
            that.Model.setValue({ id: activedField.fieldId, row: activedField.row, value: names.join(',') });
            //ID为显示字段少4个字符
            that.Model.setValue({ id: activedField.fieldId.substr(0, activedField.fieldId.length - 4), row: activedField.row, value: ids.join(',') });
        };

        _child.prototype.setFieldData = function (ids, names) {
            var that = this;
            var idValue = $.isArray(ids) ? ids.join(',') : ids;
            var nameValue = $.isArray(names) ? names.join(',') : names;

            //显示
            that.Model.setValue({ id: activedField.fieldId, row: activedField.row, value: nameValue });
            //ID为显示字段少4个字符
            that.Model.setValue({ id: activedField.fieldId.substr(0, activedField.fieldId.length - 4), row: activedField.row, value: idValue });
        }

        _child.prototype.getDimension = function (dimensionId) {
            if (!dimensions || dimensions.length == 0) {
                return null;
            }

            for (var i = 0; i < dimensions.length; i++) {
                if (dimensions[i].fid == dimensionId) {
                    return dimensions[i];
                }
            }

            return null;
        }

        _child.prototype.showValueForm = function (dimensionId, selectMul, e) {
            var that = this;

            //维度
            var dimension = that.getDimension(dimensionId);
            if (!dimension) {
                return;
            }
            //值范围
            var field = 'f' + dimension.fnumber.toLowerCase();
            var range = dimensionRanges[field] || "";

            switch (dimension.fvaluetype) {
                case 'basedata':
                    var param = {
                        formId: dimension.frefbaseformid,
                        selectMul: selectMul
                    };
                    if (!range) {
                        yiDialog.warn("选配范围没有设置可选值。");
                        break;
                    }
                    param.dynamicParam = {
                        filterString: "fid in ('" + range.replaceAll(",", "','") + "')"
                    };
                    that.Model.showSelectForm(param);
                    break;
                case 'enumdata':
                    if (!range) {
                        yiDialog.warn("选配范围没有设置可选值。");
                        break;
                    }
                    var param = {
                        formId: "mt_selectionrule",
                        selectMul: selectMul,
                        enumdataid: dimension.frefenumformid,
                        range: range
                    };
                    that.Model.invokeFormOperation({
                        id: 'showenumdataform',
                        opcode: 'showenumdataform',
                        param: param
                    });
                    break;
                case 'text':
                    var value = e.data[activedField.fieldId];
                    that.Model.showForm({
                        formId: 'mt_selectionruletextform',
                        param: { openStyle: Consts.openStyle.modal },
                        cp: {
                            fvalue: value,
                            selectMul: selectMul,
                            callback: function (result) {
                                if (result.fvalue !== undefined) {
                                    that.setFieldData(result.fvalue, result.fvalue);
                                }
                            }
                        }
                    });

                    break;
            }
        };

        _child.prototype.setEnumDataValue = function (data) {
            var that = this;
            var ids = [];
            var names = [];
            for (var i = 0; i < data.length; i++) {
                ids.push(data[i].fentryid);
                names.push(data[i].fenumitem);
            }
            that.setFieldData(ids, names);
        };

        _child.prototype.getTextValue = function (textField, row) {
            return this.Model.getValue({ id: textField.substr(0, textField.length - 4), row: row });
        }
        return _child;
    })(BasePlugIn);
    window.mt_selectionruleform = window.mt_selectionruleform || mt_selectionruleform;
})();