/// <reference path="/fw/js/platform/mvvm/baseplugin.js" />
//@ sourceURL=/fw/js/ydj/mt/mt_selectionform.js
//辅助属性
; (function () {
    var mt_selectionform = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        }

        __extends(_child, _super);

        //fpartproductid, fqty, fpartsselectionid, fselectioncategoryid
        //子件产品ID，数量，常用选配方案，子件产品的选配类别
        var partsselections = [];
        var materialid = "";
        var issuite = true;
        var issuiteselection = true;
        var isusesuiteselection = true;
        var rule = [];
        var dimensions = {};

        _child.prototype.onInitialized = function (e) {
            var that = this;
            partsselections = [];
            materialid = that.Model.viewModel.cp.materialid;
            if (that.Model.viewModel.cp.partsselections) {
                partsselections = eval("(" + that.Model.viewModel.cp.partsselections + ")");
            }
            //产品是否套件
            issuite = that.Model.viewModel.cp.issuite == "1";
            //是否套件选配
            issuiteselection = that.Model.viewModel.cp.issuiteselection == '1';
            //是否返回套件
            isusesuiteselection = that.Model.viewModel.cp.isusesuiteselection == "1";

            //选配规则 [{condition:XXX,content:YYY}]
            if (that.Model.viewModel.cp.selectionrule) {
                var rs = eval("(" + that.Model.viewModel.cp.selectionrule + ")");
                for (var i = 0; i < rs.length; i++) {
                    rule.push(eval("(" + rs[i].frule + ")"));
                }
            }

            //选配设置
            var parentpageid = that.Model.viewModel.cp.parentpageid;
            var parentPage = Index.getPage(parentpageid);
            that.Model.selectionSettings = parentPage.Model.selectionSettings;

            this.initSelection();
        };

        _child.prototype.initSelection = function () {
            var that = this;
            //根据参数设置当前产品
            that.Model.setValue({ id: "fproductid", value: materialid }); //产品

            //设置套件方案，是套件选配才显示套件方案
            that.Model.setVisible({ id: ".fsuiteselectionid", value: issuiteselection });
            //设置是否返回套件复选框
            var usesuite = $("[name='fusesuite']");
            usesuite.get(0).checked = isusesuiteselection;
            if (!issuiteselection) usesuite.attr("disabled", "disabled");
            else usesuite.removeAttr("disabled");

            //清空选配维度
            that.Model.deleteEntryData({ id: "fdimensionentity" });
            dimensions = {};

            if (issuiteselection) {
                //套件方案
                if (that.Model.viewModel.cp.suiteselectionid) {
                    //有套件方案直接设置，设置套件方案后会显示子件信息。
                    that.Model.setValue({ id: "fsuiteselectionid", value: that.Model.viewModel.cp.suiteselectionid });
                } else {
                    //没有套件方案时，子件设置为产品的套件的默认子件明细
                    var ps = []
                    for (var i = 0; i < partsselections.length; i++) {
                        var d = partsselections[i];
                        if (d.fwhetherdefault != "1") {
                            //非默认子件，不默认添加
                            continue;
                        }
                        ps.push(d);
                    };
                    this.buildPartsSelectionRows(ps);
                }
            } else {
                this.buildPartsSelectionRows(partsselections);
            }
        }

        _child.prototype.buildPartsSelectionRows = function (partsselections) {
            var that = this;
            if (partsselections) {
                that.Model.deleteEntryData({ id: "fentity" });
                for (var i = 0; i < partsselections.length; i++) {
                    var d = partsselections[i];
                    var frow = {
                        fpartproductnumber: d.productnumber,
                        fpartproductid: {
                            id: d.fpartproductid,
                            fname: d.productname,
                            fnumber: d.productnumber
                        },
                        fselectioncategoryid: {
                            id: d.fselectioncategoryid,
                            fname: d.categoryname,
                            fnumber: d.categorynumber
                        }
                    };

                    if (issuiteselection) {
                        frow.fpartsselectionid = {
                            id: d.fpartsselectionid,
                            fname: d.partsname,
                            fnumber: d.partsnumber
                        };
                        frow.fqty = d.fqty || 1;
                    } else {
                        if (d.fpartsselectionid) {
                            frow.fpartsselectionid = {
                                id: d.fpartsselectionid,
                                fname: d.partsname,
                                fnumber: d.partsnumber
                            };
                        }
                        var parentpageid = that.Model.viewModel.cp.parentpageid;
                        var parentPage = Index.getPage(parentpageid);
                        var parentRows = parentPage.Model.getSelectRows({ id: that.Model.selectionSettings.fentity });
                        var fqty = 1;
                        if (parentRows && parentRows.length) {
                            var row = parentRows[0];
                            fqty = row["data"]["fqty"] ?? 1;
                        }
                        frow.fqty = fqty;
                    }

                    that.Model.addRow({ id: "fentity", data: frow });
                }
                var entryDatas = that.Model.getEntryData({ id: "fentity" });
                if (entryDatas.length == 0) {
                    that.Model.addRow({ id: "fentity" });
                }

                //加载第一行数据
                var ds = that.Model.getEntryData({ id: "fentity" });
                if (ds && ds.length > 0) {
                    var dmsRow = ds[0].id;
                    var dmsPartproductid = ds[0].fpartproductid.id;
                    var dmsPartsselectionid = ds[0].fpartsselectionid.id;
                    //if (dmsPartproductid) {
                    this.buildDimensionRows(dmsRow, dmsPartproductid, dmsPartsselectionid, false);
                    //}
                }
            }
        }

        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                //套件方案
                case 'fsuiteselectionid':
                    e.result.filterString = "fproductid=@fproductid";
                    e.result.params = [
                        { fieldId: 'fproductid', pValue: materialid }
                    ];
                    break;
                //子件产品
                case 'fpartproductid':
                    debugger;
                    var fpartproductids = [];
                    for (var i = 0; i < partsselections.length; i++) {
                        fpartproductids.push(partsselections[i]["fpartproductid"]);
                    }
                    e.result.filterString = "fissuit != 1 AND fid IN ('" + fpartproductids.join("','") + "')";
                    break;
                //常用方案
                case 'fpartsselectionid':
                    var fpartproductid = that.Model.getSimpleValue({ id: "fpartproductid", row: e.row });
                    e.result.filterString = "fproductid=@fproductid and fisstasolution = 1";
                    e.result.params = [
                        { fieldId: 'fproductid', pValue: fpartproductid }
                    ];
                    break;
                case 'fbizfieldvalue':
                    var rowdata = that.Model.getEntryRowData({ id: "fdimensionentity", row: e.row });
                    if (!dimensions.selectedrow || !dimensions[dimensions.selectedrow] || !dimensions[dimensions.selectedrow].range) break;
                    var range = dimensions[dimensions.selectedrow].range;
                    var column = "f" + rowdata.fmatchingdimensions.fnumber;
                    var filter = "fid in (select " + column + " from t_mt_srdimensionentry where fid = @fnumber and fdimensionrowauditstatus = 'A' and " + column + " != '')";

                    //选配规则的基础资料值范围限定
                    var rules = dimensions.selectedrow && dimensions[dimensions.selectedrow] && dimensions[dimensions.selectedrow].rules;
                    if (rules && rules.length > 0) {
                        var rowdata = that.Model.getEntryRowData({ id: "fdimensionentity", row: e.row });
                        var dimension = rowdata["fmatchingdimensions"];
                        var rulevalues = [];
                        for (var i = 0; i < rules.length; i++) {
                            var rule = rules[i];
                            //当前字段是否在设置内容列表中
                            var isContent = false;
                            var rvs = [];
                            for (var j = 0; j < rule.content.length; j++) {
                                var cd = rule.content[j].dimension;
                                if (cd.id == dimension.id) {
                                    rvs = rule.content[j].value && rule.content[j].value.split(",") || [];
                                    isContent = true;
                                    break;
                                }
                            }
                            if (!isContent) continue;
                            //所有条件是否都符合条件
                            if (!this.isAllMapping(rule.condition)) continue;

                            rulevalues = rvs;
                        }
                        //选配规则的可选值
                        if (rulevalues && rulevalues.length > 0) {
                            filter = filter + " and fid in ('" + rulevalues.join("','") + "')";
                        }
                    }

                    e.result.filterString = filter;
                    e.result.params = [
                        { fieldId: 'fnumber', pValue: range }
                    ];
                    break;
            }
        };

        _child.prototype.onBeforeSelectBaseData = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fpartproductid':
                    var fpartproductids = [];
                    var entityRows = that.Model.getEntryData({ id: "fentity" });
                    for (var i = 0; i < partsselections.length; i++) {
                        var had = false;
                        for (var j = 0; j < entityRows.length; j++) {
                            if (entityRows[j] && entityRows[j]["fpartproductid"] && entityRows[j]["fpartproductid"]["id"] == partsselections[i]["fpartproductid"]) {
                                had = true;
                            }
                        }
                        if (!had) fpartproductids.push(partsselections[i]["fpartproductid"]);
                    }
                    if (fpartproductids.length == 0) {
                        e.cancel = true;
                        yiDialog.warn("没有可选择的子件产品。");
                    }
                    break;
            }
        }

        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fsuiteselectionid':
                    var fsuiteselectionid = that.Model.getSimpleValue({ id: "fsuiteselectionid" });
                    //that.Model.setEnable({ id: "fpartproductid", value: !!fsuiteselectionid });
                    that.Model.invokeFormOperation({
                        id: 'getpartsselection',
                        opcode: 'getpartsselection',
                        param: {
                            formId: "mt_suiteselection",
                            suiteselectionid: fsuiteselectionid
                        }
                    });
                    break;
                //子件产品编码变更，修改子件的选配类别、默认数量和常用方案
                case 'fpartproductid':
                    var partproduct = this.getPartsSelection(e.value.id);
                    if (partproduct == null) break;
                    that.Model.setValue({ id: "fselectioncategoryid", value: partproduct["fselectioncategoryid"], row: e.row });
                    that.Model.setValue({ id: "fqty", value: partproduct["fqty"], row: e.row });
                    that.Model.setValue({ id: "fpartsselectionid", value: partproduct["fpartsselectionid"], row: e.row });
                    break;
                //选配方案变更时，重新加载维度信息
                case 'fpartsselectionid':

                    var partproductid = that.Model.getSimpleValue({ id: "fpartproductid", row: e.row });
                    //if (partproductid && e.value.id) {
                    this.buildDimensionRows(e.row, partproductid, e.value.id, true);
                    //}
                    break;
                case 'fbizfieldvalue':
                    //选配规则的默认值限定
                    var rules = dimensions.selectedrow && dimensions[dimensions.selectedrow] && dimensions[dimensions.selectedrow].rules;
                    if (!rules || !rules.length) break;
                    var rowdata = that.Model.getEntryRowData({ id: "fdimensionentity", row: e.row });
                    var dimension = rowdata["fmatchingdimensions"];
                    for (var i = 0; i < rules.length; i++) {
                        var rule = rules[i];
                        //当前字段是否在条件中
                        var isCondition = false;
                        for (var j = 0; j < rule.condition.length; j++) {
                            var cd = rule.condition[j].dimension;
                            if (cd.id == dimension.id) { //维度和值相同，触发设置
                                var rulevalue = rule.condition[j].value;
                                //有三种情景
                                if (rowdata["fbizfieldsrc"]["id"] == "text") {
                                    var value = e.value;
                                    var rvs = rulevalue.split(",");//取规则的值
                                    for (var i = 0; i < rvs.length; i++) {
                                        if (rvs[i].indexOf("-") >= 0) { //数值范围
                                            var rs = rvs[i].split("-");
                                            if (rs && rs.length >= 2) {
                                                var start = yiMath.toNumber(rs[0]);
                                                var end = yiMath.toNumber(rs[1]);
                                                if (start <= value && value <= end) {
                                                    isCondition = true;
                                                    break;
                                                }
                                            }
                                        } else { //单个值
                                            if (yiMath.toNumber(rvs[i]) == value) {
                                                isCondition = true;
                                                break;
                                            }
                                        }
                                    }
                                }
                                if (rowdata["fbizfieldsrc"]["id"] == "enumdata") {
                                    var value = e.value.id;
                                    if (rulevalue == value) isCondition = true;
                                    break;
                                }
                                if (rowdata["fbizfieldsrc"]["id"] == "basedata") {
                                    var value = e.value.id;
                                    if (rulevalue == value) isCondition = true;
                                    break;
                                }
                                break;
                            }
                        }
                        if (!isCondition) continue;
                        //所有条件是否都符合
                        if (!this.isAllMapping(rule.condition)) continue;
                        //设置内容
                        var ctnValues = this.getViewValues(rule.content);
                        rule.content.map(function (x) {
                            var row = ctnValues[x.dimension.id].row;
                            if (x.defvalue !== '') {
                                that.Model.setValue({ id: "fbizfieldvalue", row: row, value: x.defvalue });
                            }
                            if (x.lock && x.defvalue === '') {
                                that.Model.setValue({ id: "fbizfieldvalue", row: row, value: "" });
                            }
                        });
                    }
                    that.Model.refreshEntry({ id: "fdimensionentity" });
                    break;
            }
        };

        _child.prototype.onEntryRowClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fentity':
                    if (dimensions.selectedrow == e.row) break;
                    var partproductid = that.Model.getSimpleValue({ id: "fpartproductid", row: e.row });
                    var partsselectionid = that.Model.getSimpleValue({ id: "fpartsselectionid", row: e.row });
                    //if (partproductid) {
                    this.buildDimensionRows(e.row, partproductid, partsselectionid, false);
                    //}
                    break;
            }
        };

        _child.prototype.onAfterDoOperation = function (e) {
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            if (!isSuccess) return;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'productdimension':
                    if (!e.result.operationResult.simpleData.dimensions) break;
                    var ds = eval("(" + e.result.operationResult.simpleData.dimensions + ")");
                    var rowid = e.result.operationResult.simpleData.rowid;
                    //初始化维度信息
                    this.initDimensionRows(rowid, ds);
                    //选配范围
                    dimensions[rowid].range = e.result.operationResult.simpleData.range;
                    //选配辅助资料
                    var bd = dimensions[rowid].enumdata = {};
                    if (e.result.operationResult.simpleData.enumdata) {
                        var enumdatas = eval("(" + e.result.operationResult.simpleData.enumdata + ")");
                        for (var i = 0; i < enumdatas.length; i++) {
                            var name = enumdatas[i].name;
                            if (!bd[name]) {
                                bd[name] = [];
                            }
                            bd[name].push(enumdatas[i].value);
                        }
                    }
                    //选配规则
                    if (e.result.operationResult.simpleData.rules) {
                        var frules = eval("(" + e.result.operationResult.simpleData.rules + ")");
                        var rules = [];
                        frules.map(function (r, i, arr) {
                            var rule = eval("(" + r.frule + ")");
                            rules.push(rule);
                        });
                        dimensions[rowid].rules = rules;
                    }
                    break;
                case 'getpartsselection':
                    var results = srvData;//eval("(" + e.result.operationResult.simpleData.partselections + ")");
                    this.buildPartsSelectionRows(results);
                    break;
                case 'selectionconfirm':
                    var ds = e.result.operationResult.simpleData["data"];
                    if (ds) {
                        var data = eval("(" + ds + ")");
                        this.returnData(data);
                    }
                    if (e.result.operationResult.complexMessage.errorMessages && e.result.operationResult.complexMessage.errorMessages.length > 0) {
                        break;
                    }
                    that.Model.close();
                    break;
            }
        };

        _child.prototype.getPartsSelection = function (fpartproductid) {
            var partproduct = null;
            for (var i = 0; i < partsselections.length; i++) {
                if (partsselections[i]["fpartproductid"] == fpartproductid) {
                    partproduct = partsselections[i];
                }
            }
            return partproduct;
        };

        var dimensions = {
            selectedrow: '',
            //rowid: {
            //    rowid: rowid,
            //    fpartproductid: '',
            //    fpartsselectionid: ''
            //}
        };
        _child.prototype.buildDimensionRows = function (rowid, productid, partsselectionid, isRebuild) {
            var that = this;

            //保存原设置
            //重置前保存原有的值
            if (dimensions.selectedrow && dimensions.selectedrow != rowid) {
                dimensions[dimensions.selectedrow].fentity = [].concat(that.Model.getEntryData({ id: "fdimensionentity" }));
            }
            //清空列表
            that.Model.deleteEntryData({ id: "fdimensionentity" });
            //更改为新的选择行
            dimensions.selectedrow = rowid;
            if (!dimensions[rowid]) dimensions[rowid] = {
                rowid: rowid,
                fproductid: productid
            };

            if (!isRebuild && dimensions[rowid].fentity) {
                that.Model.deleteEntryData({ id: "fdimensionentity" });
                var ds = dimensions[rowid].fentity;
                for (var i = 0; i < ds.length; i++) {
                    that.Model.addRow({ id: "fdimensionentity", row: ds[i].id, data: ds[i] });
                }
            } else {
                if (productid) {
                    that.Model.invokeFormOperation({
                        id: 'productdimension',
                        opcode: 'productdimension',
                        param: {
                            formId: "mt_selectiondimension",
                            rowid: rowid,
                            productid: productid,
                            partsselectionid: partsselectionid
                        }
                    });
                }
            }
        };

        _child.prototype.initDimensionRows = function (rowid, ds) {
            var that = this;
            if (ds && ds.length > 0) {
                for (var i = 0; i < ds.length; i++) {
                    var d = ds[i];
                    var rowData = {
                        fmatchingdimensions: {
                            id: d["fid"],
                            fnumber: d["fnumber"],
                            fname: d["fname"]
                        },
                        fbizfieldsrc: {
                            id: d["fvaluetype"],
                            fnumber: d["fvaluetype"],
                            fname: d["fvaluetype"]
                        },
                        fbizfieldtype: {
                            id: d["fviewtypenumber"],
                            fnumber: d["fviewtypenumber"],
                            fname: d["fviewtypename"]
                        },
                        fdisplayseq: d["fdisplayseq"]
                    };
                    if (d["fvaluetype"] == "text") {
                        rowData["fstdbizfieldvalue"] = d["fstdbizfieldvalue"];
                        rowData["fbizfieldvalue"] = d["fbizfieldvalue"];
                    } else {
                        rowData["fstdbizfieldvalue"] = {
                            id: d["fstdbizfieldvalue"],
                            fnumber: d["fstdbizfieldnumber"],
                            fname: d["fstdbizfieldname"]
                        };
                        rowData["fbizfieldvalue"] = {
                            id: d["fbizfieldvalue"],
                            fnumber: d["fbizfieldnumber"],
                            fname: d["fbizfieldname"]
                        };
                    }
                    that.Model.addRow({ id: "fdimensionentity", row: i, data: rowData });
                }
            }
            dimensions[rowid].fentity = [].concat(that.Model.getEntryData({ id: "fdimensionentity" }));
        };

        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode) {
                case 'selectionconfirm':
                    //e.result = true; //由业务插件接管
                    //保存维度值
                    if (dimensions.selectedrow) {
                        dimensions[dimensions.selectedrow].fentity = [].concat(that.Model.getEntryData({ id: "fdimensionentity" }));
                    }
                    //维度值参数
                    var entity = that.Model.getEntryData({ id: "fentity" });
                    var ds = [];
                    for (var p in dimensions) {
                        for (var i = 0; i < entity.length; i++) {
                            if (entity[i].id && entity[i].id == p && dimensions[p].fproductid) {
                                ds.push(dimensions[p]);
                                break;
                            }
                        }
                    }
                    e.param["dimensions"] = JSON.stringify(ds);
                    //返回的选配类型
                    var isusesuiteselection = $("[name='fusesuite']").get(0).checked;
                    e.param["issuiteselection"] = isusesuiteselection ? 1 : 0;
                    break;
            }
        };

        _child.prototype.returnData = function (data) {
            var that = this;
            var parentpageid = that.Model.viewModel.cp.parentpageid;
            if (!parentpageid) return;
            var parentPage = Index.getPage(parentpageid);
            //商品列表
            var entity = parentPage.Model.getEntryData({ id: that.Model.selectionSettings.fentity });
            var oriIsUseSuiteSelection = !!that.Model.viewModel.cp.isusesuiteselection;
            var oriSuiteSelectionId = that.Model.viewModel.cp.suiteselectionid;
            var isusesuiteselection = $("[name='fusesuite']").get(0).checked;
            var rowid = that.Model.viewModel.cp.rowid;
            var deleteRows = [];
            var addRows = [];

            if (issuite) { //产品是套件
                if (isusesuiteselection) { //返回套件
                    if (oriIsUseSuiteSelection) { //原来是返回套件
                        //返回套件且原数据也是套件，直接修改原商品的字段值
                        //var forsuiteselection = data.fid; //that.Model.getValue({ id: "fsuiteselectionid" });
                        parentPage.Model.setValue({ id: that.Model.selectionSettings.fselectionnumber, value: data.fnumber, row: rowid }); //选配码
                        parentPage.Model.setValue({ id: that.Model.selectionSettings.fdescription, value: data.fdescription, row: rowid }); //组合说明
                        parentPage.Model.setValue({ id: that.Model.selectionSettings.fpackagedescription, value: data.fpackagedescription, row: rowid }); //套件组合说明
                    } else { //原来非返回套件
                        //删除原来的所有子件
                        for (var i = 0; i < entity.length; i++) {
                            if (entity[i][that.Model.selectionSettings.fforsuiteselectionid]
                                && entity[i][that.Model.selectionSettings.fforsuiteselectionid]["id"] == oriSuiteSelectionId) {
                                deleteRows.push(entity[i].id);
                            }
                        }
                        //添加套件
                        addRows.push({
                            fmaterialid: data.fproductid,
                            fselectionnumber: data.fnumber,
                            fforproductid: "",
                            fforsuiteselectionid: "",
                            fdescription: data.fdescription,
                            fpackagedescription: data.fpackagedescription
                        });
                    }
                } else { //返回子件
                    if (oriIsUseSuiteSelection) { //原来是返回套件
                        //删除原商品
                        deleteRows.push(rowid);
                    } else { //原来是返回子件
                        //删除原来的所有子件
                        for (var i = 0; i < entity.length; i++) {
                            var pid = entity[i][that.Model.selectionSettings.fforproductid]["id"] || entity[i][that.Model.selectionSettings.fmaterialid]["id"];//有所属物料以所属物料，否则以当前物料来判断。
                            if (pid == materialid && entity[i][that.Model.selectionSettings.fforsuiteselectionid] && entity[i][that.Model.selectionSettings.fforsuiteselectionid]["id"] == oriSuiteSelectionId) {
                                deleteRows.push(entity[i].id);
                            }
                        }
                    }
                    //添加子件
                    for (var i = 0; i < data.fentity.length; i++) {
                        addRows.push({
                            fmaterialid: data.fentity[i]["fpartproductid"],
                            fselectionnumber: data.fentity[i]["fpartsselectionnumber"],
                            fforproductid: data.fproductid,
                            fforsuiteselectionid: data.fid,
                            fdescription: data.fentity[i]["fdescription"],
                            fqty: data.fentity[i]["fqty"]
                        });
                    }
                }
            } else { //产品是子件
                //子件产品只能返回子件，直接设置内容
                parentPage.Model.setValue({ id: that.Model.selectionSettings.fselectionnumber, value: data.fnumber, row: rowid }); //选配码
                parentPage.Model.setValue({ id: that.Model.selectionSettings.fdescription, value: data.fdescription, row: rowid }); //组合说明
                parentPage.Model.setValue({ id: that.Model.selectionSettings.fbizqty, value: data.fqty, row: rowid });
            }
            //添加选配行
            for (var i = 0; i < addRows.length; i++) {
                var newRowid = parentPage.Model.addRow({ id: that.Model.selectionSettings.fentity });
                parentPage.Model.setValue({ id: that.Model.selectionSettings.fmaterialid, value: addRows[i]["fmaterialid"], row: newRowid });
                parentPage.Model.setValue({ id: that.Model.selectionSettings.fselectionnumber, value: addRows[i]["fselectionnumber"], row: newRowid });
                parentPage.Model.setValue({ id: that.Model.selectionSettings.fforproductid, value: addRows[i]["fforproductid"], row: newRowid });
                parentPage.Model.setValue({ id: that.Model.selectionSettings.fforsuiteselectionid, value: addRows[i]["fforsuiteselectionid"], row: newRowid });
                parentPage.Model.setValue({ id: that.Model.selectionSettings.fdescription, value: addRows[i]["fdescription"], row: newRowid });
                parentPage.Model.setValue({ id: that.Model.selectionSettings.fbizqty, value: addRows[i]["fqty"], row: newRowid });
            }
            //删除行和空行
            for (var i = 0; i < entity.length; i++) {
                if (!entity[i][that.Model.selectionSettings.fmaterialid] || !entity[i][that.Model.selectionSettings.fmaterialid]["id"]) {
                    deleteRows.push(entity[i]["id"]);
                }
            }
            for (var i = 0; i < deleteRows.length; i++) {
                parentPage.Model.deleteRow({ id: that.Model.selectionSettings.fentity, row: deleteRows[i] });
            }
        };

        _child.prototype.onAfterDataSourceLoad = function (e) {
            var that = this;
            switch (e.id) {
                case 'fbizfieldvalue':
                    //对下拉框数据源进行干预
                    var row = that.Model.getEntryRowData({ id: "fdimensionentity", row: e.row });
                    var field = "f" + row.fmatchingdimensions.fnumber;
                    //选配范围的辅助资料可选值
                    var values = dimensions[dimensions.selectedrow].enumdata[field];
                    //选配规则的辅助资料值范围限定
                    var rules = dimensions.selectedrow && dimensions[dimensions.selectedrow] && dimensions[dimensions.selectedrow].rules;
                    if (rules && rules.length > 0) {
                        var rowdata = that.Model.getEntryRowData({ id: "fdimensionentity", row: e.row });
                        var dimension = rowdata["fmatchingdimensions"];
                        var rulevalues = [];
                        for (var i = 0; i < rules.length; i++) {
                            var rule = rules[i];
                            //当前字段是否在设置内容列表中
                            var isContent = false;
                            var rvs = [];
                            for (var j = 0; j < rule.content.length; j++) {
                                var cd = rule.content[j].dimension;
                                if (cd.id == dimension.id) {
                                    rvs = rule.content[j].value && rule.content[j].value.split(",") || [];
                                    isContent = true;
                                    break;
                                }
                            }
                            if (!isContent) continue;
                            //所有条件是否都符合条件
                            if (!this.isAllMapping(rule.condition)) continue;

                            rulevalues = rvs;
                        }
                        //选配规则的可选值
                        if (values && rulevalues && rulevalues.length > 0) {
                            //取交集
                            values = values.filter(function (v) { return rulevalues.indexOf(v) > -1 });
                        }
                    }

                    var data = [];
                    if (values) {
                        e.data.map(function (d) {
                            if (values.indexOf(d.id) >= 0) {
                                data.push(d);
                            }
                        });
                    }
                    e.data = data;

                    break;
            }
        };

        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fbizfieldvalue':
                    //选配规则的文本值范围限定
                    var rules = dimensions.selectedrow && dimensions[dimensions.selectedrow] && dimensions[dimensions.selectedrow].rules;
                    if (!rules || !rules.length) break;
                    var rowdata = that.Model.getEntryRowData({ id: "fdimensionentity", row: e.row });
                    if (rowdata["fbizfieldsrc"]["id"] != "text") break;
                    if (e.value === "") {
                        e.value = null;
                        break;
                    }
                    var value = yiMath.toNumber(e.value);
                    var dimension = rowdata["fmatchingdimensions"];
                    var values = "";
                    for (var i = 0; i < rules.length; i++) {
                        var rule = rules[i];
                        //当前字段是否在设置内容列表中
                        var isContent = false;
                        var val = "";
                        for (var j = 0; j < rule.content.length; j++) {
                            var cd = rule.content[j].dimension;
                            if (cd.id == dimension.id) {
                                val = rule.content[j].value && rule.content[j].value;
                                isContent = true;
                                break;
                            }
                        }
                        if (!isContent) continue;
                        //所有条件是否都符合条件
                        if (!this.isAllMapping(rule.condition)) continue;

                        values = val;
                    }

                    if (values) {
                        var rvs = values.split(",");
                        var ok = false;
                        for (var i = 0; i < rvs.length; i++) {
                            if (rvs[i].indexOf("-") >= 0) { //数值范围
                                var rs = rvs[i].split("-");
                                if (rs && rs.length >= 2) {
                                    var start = yiMath.toNumber(rs[0]);
                                    var end = yiMath.toNumber(rs[1]);
                                    if (start <= value && value <= end) {
                                        ok = true;
                                        break;
                                    }
                                }
                            } else { //单个值
                                if (yiMath.toNumber(rvs[i]) == value) {
                                    ok = true;
                                    break;
                                }
                            }
                        }
                        if (!ok) {
                            e.value = that.Model.getValue({ id: 'fbizfieldvalue', row: e.row });
                            e.result = true;
                            yiDialog.mt({ msg: '选配规则限制值只允许：' + values, skinseq: 2 });
                        }
                    }
                    break;
            }
        };

        _child.prototype.getViewValues = function (conditions) {
            var that = this;
            var cv = {};
            var data = that.Model.getEntryData({ id: "fdimensionentity" });
            for (var i = 0; i < conditions.length; i++) {
                var cd = conditions[i].dimension;
                for (var j = 0; j < data.length; j++) {
                    var dd = data[j]["fmatchingdimensions"];
                    if (cd.id == dd.id) {
                        var val = data[j].fbizfieldvalue;
                        cv[dd.id] = { row: data[j].id, value: data[j].fbizfieldsrc.id == "text" ? val : val.id };
                        continue;
                    }
                }
            }
            return cv;
        }

        _child.prototype.isAllMapping = function (condition) {
            var cdnValues = this.getViewValues(condition);
            for (var j = 0; j < condition.length; j++) {
                var value = cdnValues[condition[j].dimension.id].value;
                //判断是文本
                if (condition[j].value.indexOf(",") >= 0 || condition[j].value.indexOf("-") >= 0) {
                    var rvs = condition[j].value.split(",");//取规则的值
                    for (var i = 0; i < rvs.length; i++) {
                        if (rvs[i].indexOf("-") >= 0) { //数值范围
                            var rs = rvs[i].split("-");
                            if (rs && rs.length >= 2) {
                                var start = yiMath.toNumber(rs[0]);
                                var end = yiMath.toNumber(rs[1]);
                                if (value < start || value > end) {
                                    return false;
                                }
                            }
                        } else { //单个值
                            if (yiMath.toNumber(rvs[i]) != value) {
                                return false;
                            }
                        }
                    }
                } else {
                    if (condition[j].value != value) {
                        return false;
                    }
                }
            }
            return true;
        }

        //获取表格行编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fbizfieldvalue':
                    //e.result.enabled = true;
                    //选配规则的锁定限定
                    var rules = dimensions.selectedrow && dimensions[dimensions.selectedrow] && dimensions[dimensions.selectedrow].rules;
                    if (!rules || !rules.length) break;
                    var rowdata = that.Model.getEntryRowData({ id: "fdimensionentity", row: e.row });
                    var dimension = rowdata["fmatchingdimensions"];
                    var lock = false;
                    for (var i = 0; i < rules.length; i++) {
                        var rule = rules[i];
                        //当前字段是否在设置内容列表中
                        var isContent = false;
                        var isLock = false;
                        for (var j = 0; j < rule.content.length; j++) {
                            var cd = rule.content[j].dimension;
                            if (cd.id == dimension.id) {
                                isLock = rule.content[j].lock;
                                isContent = true;
                                break;
                            }
                        }
                        if (!isContent) continue;
                        if (!this.isAllMapping(rule.condition)) continue;

                        lock = lock || isLock;
                    }

                    e.result.enabled = !lock;
                    break;
            }
        };

        _child.prototype.onEntryRowCreating = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fentity':
                    that.Model.getEntryData({ id: "fentity" }).length
                    if (!issuite && that.Model.getEntryData({ id: "fentity" }).length == 1) {
                        yiDialog.warn("子件选配的子件不允许添加或者删除");
                        e.result = true;
                    }
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.mt_selectionform = window.mt_selectionform || mt_selectionform;
})();