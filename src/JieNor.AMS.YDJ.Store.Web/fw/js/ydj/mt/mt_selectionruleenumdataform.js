/// <reference path="/fw/js/platform/mvvm/baseplugin.js" />
//@ sourceURL=/fw/js/ydj/mt/mt_selectionruleenumdataform.js
//选配规则辅助资料弹窗
; (function () {
    var mt_selectionruleenumdataform = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        }

        __extends(_child, _super);

        _child.prototype.onInitialized = function (e) {
            var that = this;
            var data = [];
            if (that.Model.viewModel.cp.data) {
                data = eval("(" + that.Model.viewModel.cp.data + ")");
            }

            that.Model.deleteEntryData({ id: "fentity" });
            var source = that.Model.getEntryData({ id: "fentity" });
            data.map(function (item) {
                source.push(item);
            });
            that.Model.refreshEntry({ id: "fentity" });
        };

        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode) {
                case 'dataconfirm':
                    e.result = true;

                    var data = [];
                    var source = that.Model.getSelectRows({ id: "fentity" });
                    source.map(function (x) {
                        data.push(x.data);
                    });
                    var parentpageid = that.Model.viewModel.cp.parentId;
                    if (!parentpageid) return;
                    var parentPage = Index.getPage(parentpageid);
                    //parentPage.Model.setEnumDataValue(data);
                    parentPage.plugInProxy.invoke("setEnumDataValue", data);
                    that.Model.close();
                    break;
            }
        };

        //创建明细表格
        _child.prototype.onCreateGrid = function (args) {
            var that = this;
            if (!args.id) return;
            switch (args.id.toLowerCase()) {
                case "fentity":
                    args.result = { multiselect: that.Model.viewModel.cp.selectMul };
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.mt_selectionruleenumdataform = window.mt_selectionruleenumdataform || mt_selectionruleenumdataform;
})();