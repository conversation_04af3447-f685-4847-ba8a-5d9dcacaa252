///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/stk/ydj_productbarcodesetting.js
*/
; (function () {
    var ydj_productbarcodesetting = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);


        //初始化页面插件
        _child.prototype.onInitialized = function (e) {
            var that = this;
            //列表数据对象
            var lookData = that.formContext.cp.lookData;
            var entry = that.Model.getEntryData({ id: 'fentry' });
            if (lookData && lookData.length > 0) {
                entry.length = 0;
                for (var i = 0; i < lookData.length; i++) {
                    entry.push(lookData[i]);
                }
                //渲染数据
                that.Model.refreshEntry({ id: 'fentry' });
            }


            if (!entry || entry.length <= 0) {
                return;
            }

            var pkValue = lookData[0].fproductbarcodeid;

            that.Model.invokeFormOperation({
                id: 'getprinttemplate',
                opcode: 'getprinttemplate',
                selectedRows: [{ PKValue: pkValue }],
                param: {
                    'formId': 'ydj_productbarcode',
                }
            });
        };

        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case "fentry":
                    e.result = { multiselect: false };
                    break;
            }
        };

        //表单元素被单击后
        _child.prototype.onBeforeDoOperation = function (args) {
            var that = this;
            if (!args.opcode) return;
            switch (args.opcode) {
                case 'close':
                    var entry = that.Model.getEntryData({ id: 'fentry' });
                    that.Model.setReturnData({ tempData: entry });
                    break;
            }
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'tbproducttagprint':
                    that.productTagPrint(e);
                    break;
                case 'tbcleardata':
                    e.result = true;
                    //清空备选数据
                    that.Model.deleteEntryData({ id: 'fentry' });
                    break;
            }
        };

        //标签打印
        _child.prototype.productTagPrint = function (e) {
            var that = this;
            var params = [];
            e.result = true;

            var fprinttmpid = $.trim(that.Model.getSimpleValue({ id: "fprinttmpid" }));
            if (!fprinttmpid) {
                yiDialog.warn('请先选择打印模板!');
                return;
            }

            var entry = that.Model.getEntryData({ id: 'fentry' });
            if (!entry || entry.length <= 0) {
                yiDialog.warn('当前打印明细为空无法打印！');
                return;
            }
            for (var i = 0; i < entry.length; i++) {
                if (entry[i].fqty <= 0) {
                    yiDialog.warn('第' + (i + 1) + '行的打印数据小于等于0!');
                    return;
                }
                params.push({ "qty": entry[i].fqty, "productBarCodeId": entry[i].fproductbarcodeid });
            }

            that.Model.invokeFormOperation({
                id: 'producttagprint',
                opcode: 'producttagprint',
                param: {
                    'formId': 'ydj_producttagprintseting',
                    'printInfos': JSON.stringify(params),
                    'printTmpId': fprinttmpid
                }
            });
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode.toLowerCase()) {
                case 'producttagprint':
                    yiAjax.gf('/views/sys/pdf_show.html', {}, function (html) {
                        yiDialog.d({
                            id: 'ydj_taskA',
                            type: 1,
                            resize: false,
                            content: html,
                            title: '商品标签 - pdf预览打印',
                            area: ['100%', '100%'],
                            btn: ['关闭'],
                            yes: function (index, layero) {
                                layer.close(index);
                            },
                            success: function (layero, index) {
                                var $layero = $(layero);
                                yiPdf.set(srvData, '#pdf-show', { height: ($layero.height() - 130) + 'px' });
                            }
                        });
                    });
                    break;
                case "getprinttemplate":
                    if (srvData == null || srvData.length == null || srvData.length <= 0) {
                        break;
                    }
                    var comboData = [];
                    for (var i = 0; i < srvData.length; i++) {
                        comboData.push({
                            id: srvData[i].fid,
                            name: srvData[i].fname
                        });
                    }
                    that.Model.setComboData({ id: 'fprinttmpid', data: comboData });
                    that.Model.setValue({ id: 'fprinttmpid', value: { id: srvData[0].fid, fnumber: srvData[0].fid, fname: srvData[0].fname } });
                    break;
            }
        };


        return _child;
    })(BasePlugIn);
    window.ydj_productbarcodesetting = window.ydj_productbarcodesetting || ydj_productbarcodesetting;
})();