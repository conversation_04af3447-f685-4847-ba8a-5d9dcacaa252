/**
 * 门店转让
 * @ sourceURL=/fw/js/bas/bas_storetransfer.js
 */
; (function () {
    var bas_storetransfer = (function (_super) {
        var _child = function (args) {
            _super.call(this, args);

            //防重复确认标记
            this.confirming = false;
        };
        __extends(_child, _super);

        //页面初始化事件
        _child.prototype.onInitialized = function (e) {
            var that = this;

        };

        //表单操作点击事件
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode) {
                //取消
                case 'pagecancel':
                    e.result = true;
                    that.Model.close();
                    break;
                //确定
                case 'pageconfirm':
                    e.result = true;

                    var storeId = that.Model.getSimpleValue({ id: 'fstoreid' });
                    var agentId = that.Model.getSimpleValue({ id: 'fagentid' });
                    //43554：门店超级转让不允许重复转让给相同目标经销商
                    if (that.Model.viewModel.parentPageId)
                    {
                        var parentPage = Index.getPage(that.Model.viewModel.parentPageId);
                        var ftargetagentid = parentPage.Model.getSimpleValue({ id: 'ftargetagentid' });
                        if (ftargetagentid == agentId)
                        {
                            yiDialog.warn('不允许重复转让相同目标经销商！');
                            return;
                        }
                    }

                    if (!storeId) {
                        yiDialog.warn('请选择门店！');
                        return;
                    }
                    if (!agentId) {
                        yiDialog.warn('请选择二级分销商！');
                        return;
                    }

                    yiDialog.c('门店被转让后将无法撤销，确定继续？', function () {

                        if (that.confirming) return;
                        that.confirming = true;

                        that.Model.invokeFormOperation({
                            id: 'transferconfirm',
                            opcode: 'transferconfirm',
                            param: {
                                formId: 'bas_store',
                                storeId: storeId,
                                agentId: agentId,
                                topstoreid: that.Model.viewModel.cp.topstoreid,
                                istransferadmin: that.Model.viewModel.cp.istransferadmin
                            }
                        });
                    }, null, '转让确认');
                    break;
            }
        };

        //操作后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'transferconfirm':
                    if (isSuccess) {
                        that.Model.refreshParentPage();
                        that.Model.close();
                    }
                    break;
            }
        };

        //操作完成时触发的事件
        _child.prototype.onCompleteDoOperation = function (e) {
            var that = this;
            switch (e.opcode) {
                case 'transferconfirm':
                    that.confirming = false;
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.bas_storetransfer = bas_storetransfer;
})();