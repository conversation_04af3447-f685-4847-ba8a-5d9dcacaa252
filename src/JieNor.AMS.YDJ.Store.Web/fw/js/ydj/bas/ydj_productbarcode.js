// <reference path="/fw/js/consts.js" />
/*
 * description:商品条码业务控制插件
 * author:宋纪强
 * create date:2018-05-22
 * modify by:刘乙平
 * modify date:2019-11-7
 * remark:
 *@ sourceURL=/fw/js/ydj/bas/ydj_productbarcode.js
*/
; (function () {
    var ydj_productbarcode = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //页面初始化事件
        _child.prototype.onBillInitialized = function (args) {
            var that = this;

        };

        //字段值改变后触发的事件
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fproductid':
                    //清空辅助属性字段值
                    var param = [];
                    that.Model.setValue({id:'fattrinfo',value:{fentity:param}});
                    break;
            }
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                //添加备选
                case 'addoptional':
                    e.result = true;
                    //选中行
                    var selRows = that.Model.getSelectRows({});
                    if (selRows.length == 0) {
                        yiDialog.warn('请选择要添加备选的明细行！');
                    } else {
                        var selectedRows = [];
                        for (var i = 0; i < selRows.length; i++) {
                            selectedRows.push({
                                fmtrlname: selRows[i].data.fmtrlname,
                                fproductnumber: selRows[i].data.fproductnumber,
                                fmtrlmodel: selRows[i].data.fmtrlmodel,
                                fqty:'1',
                                fproductbarcodeid: selRows[i].pkValue
                            });
                        }
                        var tempData = that.Model.tempData ? that.Model.tempData : [];
                        var datas = [];
                        for (var i = 0; i < selectedRows.length; i++) {
                            var isSelected = false;
                            for (var j = 0; j < tempData.length; j++) {
                                if (selectedRows[i].fproductbarcodeid == tempData[j].fproductbarcodeid) {
                                    isSelected = true;
                                }
                            }
                            if (!isSelected) {
                                datas.push(selectedRows[i]);
                            }
                        }
                        var entryDatas = tempData.concat(datas);
                        that.Model.tempData = entryDatas;
                        yiDialog.mt({ msg: '添加成功！', skinseq: 1 });
                    }
                    break;
                //查看备选
                case 'lookoptional':
                    e.result = true;
                    that.Model.showForm({
                        formId: 'ydj_productbarcodesetting',
                        param: { openStyle: Consts.openStyle.modal },
                        cp: {
                            lookData:that.Model.tempData ? that.Model.tempData:[],
                            callback: function (result) {
                                if (!$.isEmptyObject(result)) {
                                    that.Model.tempData = result.tempData;
                                }
                            }
                        }
                    });
                    break;
            }
        };
        
        return _child;
    })(BillPlugIn);
    window.ydj_productbarcode = window.ydj_productbarcode || ydj_productbarcode;
})();