(function () {
    var ydj_productauth = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
            that.flag = true;
        };
        __extends(_child, _super);

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            debugger;
            switch (e.id.toLowerCase()) {
                //case 'fserieid':
                ////case 'fproductid_o':
                //    var fbrandid = $.trim(that.Model.getSimpleValue({ id: "fbrandid", row: e.row}));
                //    e.result.filterString = "fbrandid=@fbrandid";
                //    e.result.params = [
                //        { fieldId: 'fbrandid', pValue: fbrandid }
                //    ];
                //    break;
                case 'forgid':
                    e.result.filterString = " forgtype<>'1' ";
                    break;
            }
        };



        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            debugger
            switch (e.opcode.toLowerCase()) {
                case 'batchzgproduct':
                    debugger
                    e.result = true;
                    that.Model.showForm({
                        formId: 'ydj_productzgdialog',
                        param: { openStyle: Consts.openStyle.modal },
                        cp: {
                            fsourceid: that.Model.getSimpleValue({ id: 'id' }),
                            callback: function (result) {

                                debugger
                                if (result.data) {
                                    that.Model.refresh();
                                    //that.Model.setValue({ id: 'fchgremark', value: result.data.fchgremark });
                                    //that.Model.setValue({ id: 'frefchgtype', value: result.data.fchgtype });
                                    //that.Model.invokeFormOperation({
                                    //    id: 'submitchange',
                                    //    opcode: 'submitchange',
                                    //    billData: JSON.stringify([that.Model.uiData]),
                                    //    selectedRows: [{ PKValue: that.Model.pkid }],
                                    //    param: {
                                    //        fchgremark: result.data.fchgremark,
                                    //        fchgtype: result.data.fchgtype
                                    //    }
                                    //});
                                }
                            }
                        }

                    })
                    break;
                //case "tbSave":
                //    if (that.checkIsReap()) {
                //        yiDialog.warn('不允许添加相同的例外商品');
                //        e.result = true;
                //        return;
                //    }
                //    break;

            };
        }

        _child.prototype.checkIsReap = function () {
            var that = this;
            var allRows = that.Model.getEntryData({ id: "fproductauthexclude" });
            var seen = new Set();
            allRows.some(item => {
                if (seen.has(item.fproductid_o.id)) {
                    return true
                }
                seen.add(item.fproductid_o.id)
                return false
            })


        }

        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fproductid_o':
                    //例外商品不允许重复
                    var matObj = e.value;
                    var allRows = that.Model.getEntryData({ id: "fproductauthexclude" });
                    if (allRows.some(a => a.fproductid_o.id == matObj.id)) {
                        //yiDialog.warn('不允许添加相同的例外商品');
                        e.result = true;
                        that.Model.deleteRow({ id: 'fproductauthexclude', row: e.row, opctx: { ignore: true } });
                        that.Model.refreshEntry({ id: "fproductauthexclude" });
                        return;
                    }
                    break;
            }
        }

        //字段值变化事件
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fdeliverid':
                    //清空送达方、城市
                    that.Model.setValue({ id: 'fsaleorgid', value: '', row: e.row });
                    that.Model.setValue({ id: 'fcityid', value: '', row: e.row });
                    break;
                // case 'fproductid_o':
                //     //例外商品不允许重复
                //     debugger
                //     var matObj = that.Model.getValue({ id: 'fproductid_o', row: e.row });
                //     var allRows = that.Model.getEntryData({ id: "fproductauthexclude" });
                //     if (allRows.some(a => a.fproductid_o.id == matObj.id)) {
                //         //yiDialog.warn('不允许添加相同的例外商品');
                //         e.result = true;
                //         that.Model.deleteRow({ id: 'fproductauthexclude', row: e.row, opctx: { ignore: true } });
                //         that.Model.refreshEntry({ id: "fproductauthexclude" });
                //         return;
                //     }
                //     break;
            }
        };


        return _child;
    })(BasePlugIn);
    window.ydj_productauth = window.ydj_productauth || ydj_productauth;
})();