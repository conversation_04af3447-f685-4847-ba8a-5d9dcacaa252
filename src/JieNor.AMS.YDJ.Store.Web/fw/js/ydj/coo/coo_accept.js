; (function () {
    var coo_accept = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);
		
		//初始化编辑页面插件
        _child.prototype.onInitialized  = function (args) {
        	var that = this;
        	var companyName = that.Model.uiData.companyName,
        		serviceType = that.Model.uiData.serviceType,
        		optype = that.Model.uiData.optype,
        		fid = that.Model.uiData.custorsuppId;
        	that.Model.setValue({id:'fname',value:companyName});
        	if(serviceType == '供应商'){
        		that.Model.setVisible({id:'.y-supplier',value:true});
        		that.Model.setVisible({id:'.y-customer',value:false});
        	}else{
        		that.Model.setVisible({id:'.y-supplier',value:false});
        		that.Model.setVisible({id:'.y-customer',value:true});
        	}
        	if(optype == '绑定调整'){
        		that.Model.setVisible({id:'.y-autocreate',value:false});
        	}
        	if(optype == '激活'){
        		if(serviceType == '供应商'){
        			that.Model.setValue({id:'fsupplierid',value: fid});
        		}else{
        			that.Model.setValue({id:'fcustomerid',value: fid});
        		}
        	}
            //显示运营模式
        	if (optype === '激活' || optype === '绑定调整') {
        	    that.Model.setVisible({ id: '.y-foperationmode', value: true });
        	}
        };
		
		//处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            if (!args.opcode) return;
            switch (args.opcode) {
             	case 'cancel':
                    //点击取消关闭协同发布弹窗
                    args.result = true;
                    that.Model.close();
                    break;
                case 'confirm':
                    //保存发布操作
                    args.result = true;
                	that.acceptSyn();
                    break;
            }
        };
        
        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
            	//自动创建
                case 'fisautocreat':
                   	that.autoCreat(e);
                    break;
            }
        };
        
        
        //自动创建
        _child.prototype.autoCreat = function (e) {
        	var that = this;
        	var	serviceType = that.Model.uiData.serviceType;
        	switch(e.value){
        		case true:
        			if(serviceType === '供应商'){
        				that.Model.setVisible({id:'.y-supplier',value:false});
        				that.Model.setValue({id:'fsupplierid',value:''});
        			}
        			else if(serviceType === '客户'){
        				that.Model.setVisible({id:'.y-customer',value:false});
        				that.Model.setValue({id:'fcustomerid',value:''});
        			}
        			break;
        		case false:
        			if(serviceType === '供应商'){
        				that.Model.setVisible({id:'.y-supplier',value:true});
        			}
        			else if(serviceType === '客户'){
        				that.Model.setVisible({id:'.y-customer',value:true});
        			}
        			break;
        	}
        }
        
        
        //受理操作
        _child.prototype.acceptSyn = function (args) {
        	var that = this;
        	
        	var customer = that.Model.getValue({ id: 'fcustomerid' }),
        		supplier = that.Model.getValue({ id: 'fsupplierid' }),
        		autocreat = that.Model.getValue({ id: 'fisautocreat' }),
                opmode = $.trim(that.Model.getSimpleValue({ id: 'foperationmode' })),
        		optype = that.Model.uiData.optype;
        		
        	if (optype === '激活' || optype === '绑定调整') {
        	    if (!opmode) {
        	        yiDialog.warn('请选择运营模式！');
        	        return;
        	    }
        	}
        	if(customer.fname == '' && supplier.fname == '' && autocreat === false){
        		yiDialog.mt({msg:'请选择关联对象！', skinseq: 2});
        	}else{
	        	var	companyId = that.Model.uiData.companyId,
	    			serviceType = that.Model.uiData.serviceType;
	        	var CusorSupId,
	        		CusorSupName;
	        	if(serviceType == '供应商'){
	        		CusorSupId = supplier.id;
	        		CusorSupName = supplier.fname;
	        	}else if(serviceType == '客户'){
	        		CusorSupId = customer.id;
	        		CusorSupName = customer.fname;
	        	}
	        	var param,
	        		message;
	        	if(optype === '受理'){
	        		message = '确定要与该企业建立协同？';
	        		param = {
	        			formId: 'coo_company',
                        domainType:'dynamic',
        	            optype: '0',
        	            id: companyId,
        	            relationid: CusorSupId
	        		};
	        	}
	        	else if(optype === '激活'){
	        		message = '确定要重新向该企业发起协同邀请？';
	        		param = {
	        			formId: 'coo_company',
                        domainType:'dynamic',
        	            optype: '2',
        	            id: companyId,
        	            relationid: CusorSupId
	        		};
	        	}
	        	else if(optype === '绑定调整'){
	        		message = '确定调整绑定与该企业的关联资料？';
	        		param = {
	        			formId: 'coo_company',
                        domainType:'dynamic',
        	            optype: '3',
        	            id: companyId,
        	            relationid: CusorSupId
	        		};
	        	}
	        	param.opmode = opmode;
	        	yiDialog.c(message, function () {
	        	    that.Model.invokeFormOperation({
	        	    	id: 'acceptSyn',
	        	        opcode: 'setCompanyRelationStatus',
	        	        param
	        	    });
        	  	});
        	};
        };
        
        
        //操作成功则关闭弹窗
        _child.prototype.onAfterDoOperation = function (args) {
            var that = this;
            var optype = that.Model.uiData.optype;
            switch (args.id) {
                case 'acceptSyn':
                    if (args.result && args.result.operationResult && args.result.operationResult.isSuccess) {
                        //成功则刷新父页面
                        var newRow = {flag: 'Y'};
	        			that.Model.setReturnData({ newRow: newRow });
	        			//关闭当前弹窗
                        that.Model.close();
                    }
                    break;
            }
        };
		
        return _child;
    })(BasePlugIn);
    window.coo_accept = window.coo_accept || coo_accept;
})();