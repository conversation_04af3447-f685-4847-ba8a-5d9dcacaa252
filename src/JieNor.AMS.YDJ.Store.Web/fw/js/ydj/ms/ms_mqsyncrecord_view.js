/**
 * 慕思业务对象映射
 * @ sourceURL=/fw/js/is/ms_mqsyncrecord_view.js
 */
; (function () {
    var ms_mqsyncrecord_view = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        __extends(_child, _super);

        //编辑页面初始化后触发
        _child.prototype.onBillInitialized = function (e) {
            var that = this;

        };
        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase())
            {
                case 'tosync':
                    debugger;
                    e.result = true;
                    var pkids = "";
                    var bizobjids = "";
                    var rows = that.Model.getAllSelectedRows(); 
                    for (var i = 0; i < rows.length; i++) {
                        pkids += rows[i].pkValue + ","
                        bizobjids += rows[i].data["fbizobjid"] + ","
                    }
                    that.Model.invokeFormOperation({
                        id: 'tosync',
                        opcode: 'tosync',
                        param: {
                            formId: 'ydj_order',
                            pkids: pkids,
                            bizobjids: bizobjids,
                            domainType: 'dynamic'
                        }
                    });
                    break;
                case 'mqsyncrecord': 
                    //that.Model.showPopForm({ popup: 'mqsyncrecord-info' });
                    e.result = true;
                    that.Model.showForm({
                        formId: 'ms_mqsyncrecord_dialog',
                        param: { openStyle: Consts.openStyle.modal },
                        cp: {
                            callback: function (result) {
                                if (result.data) {
                                    //that.Model.refresh();
                                }
                            }
                        }
                    })
                    break;
                case 'batchaddmqsync':
                    e.result = true;
                    that.Model.showForm({
                        formId: 'ms_mqsyncrecord_add',
                        param: { openStyle: Consts.openStyle.modal }
                    })
                    break;
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess
            var srvData = e.result.operationResult.srvData;

            switch (e.opcode) {
                case 'tosync':
                    if (isSuccess) {
                        that.Model.refresh();
                    }
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.ms_mqsyncrecord_view = ms_mqsyncrecord_view;
})();