/// <reference path="/fw/js/basepage.js" />
//@ sourceURL=/fw/js/ydj/ser/ydj_merchantorder.js
; (function () {
    var ydj_merchantorder = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.serviceEntryId = 'fserviceentry';

        //初始化编辑页面插件
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            //根据服务状态显示对应的操作
            var serStatus = that.Model.getSimpleValue({ id: 'fserstatus' }),
            	delivery = that.Model.getSimpleValue({ id: 'fisdelivery' }),
            	billno = that.Model.getValue({ id: 'fservicebillno' }),
                sourcebillnum = that.Model.getValue({ id: 'fsourcebillnum' });
            that.deliveryShow(delivery);
            that.procSerStatusOp(serStatus);
            that.OutServiceShow(billno);
            that.LookSourceShow(sourcebillnum);
            that.onInitSta();
            //派单方式的判断
            that.onInithandbill();
            
        };

		//初始化编辑页面插件
        _child.prototype.onInitSta = function () {
        	var serStatus = this.Model.getSimpleValue({ id: 'fservicetype' });
            switch (serStatus) {
                case 'fres_type_01':
                case 'fres_type_03':
                    this.Model.setVisible({ id: '.collect', value: true });
                    break;
                default:
                    this.Model.setVisible({ id: '.collect', value: false });
                    break;
            }
        }
		
		_child.prototype.onInithandbill = function () {
			//如果派单方式为空，则派单信息不显示
			var status = this.Model.getSimpleValue({ id: 'fdispatchtype' });
			if(!$.trim(status)){
				this.Model.setVisible({ id: '.y-handbill', value: false });
			}
		}
        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            if (!e.opcode) return;
            that.onInithandbill();
            switch (e.opcode) {
                case 'sht_submitbill':
                case 'sht_quoteprice':
                case 'sht_refusebill':
                case 'sht_confirmprice':
                case 'sht_finishbill':
                case 'sht_cancelbill':
                    // that.changeStatus(e);
                    var statusText = that.Model.getText({ id: '[opcode={0}]'.format(e.opcode )});
                    //提示用户
                    e.dialog = {
                        simpleMessage: '确认执行 ' + statusText + ' 操作？',
                    };
                    // e.result = true;
                    break;
                case 'sht_dispatchbill':
                    var cp = {
                        billId: that.Model.pkid,
                        servicetype: that.Model.uiData.fservicetype.id,
                        callback: function (result) {
                            if (!result || !result.newRow) { return; }
                            var newRow = result.newRow;
                            //操作成功则刷新列表
                            if (newRow.flag == 'Y') {
                                that.Model.refresh();
                            }
                        }
                    };
                    var Data;
                    cp = $.extend(true, cp, Data);
                    var statusText = that.Model.getText({ id: '[opcode={0}]'.format(e.opcode) });
                    // 选择团队对话框
                    that.Model.showForm({
                        formId: 'ydj_dispatchbill_team',
                        param: { openStyle: Consts.openStyle.modal },
                        cp: cp
                    });
                    e.result = true;
                    break;
                case 'showservice':
                    var pkids = that.Model.uiData.fserviceid;
                    var pkArray = pkids.split(",");
                    for (var i = 0; i < pkArray.length; i++) {
                        var pkid = pkArray[i];
                        Index.openForm({
                            formId: 'ydj_service',
                            domainType: 'bill',
                            pkids: [pkid]
                        });
                    }
                    e.result = true;
                    break;
                case 'showorder':
                    var pkid = that.Model.uiData.fsourceid;
                    Index.openForm({
                        formId: 'ydj_merchantorder',
                        domainType: 'bill',
                        pkids: [pkid]
                    });
                    e.result = true;
                    break;
                case 'putoutservice':
                    that.Model.invokeFormOperation({ id: 'tbOutService', opcode: 'push', param: { ruleid: "ydj_merchantorder2ydj_service" } });
                    e.result = true;
                    break;
                case 'putcustomerservice':
                    that.Model.invokeFormOperation({ id: 'tbPushCustomerService', opcode: 'push', param: { ruleid: "ydj_merchantorder2ydj_merchantorder" } });
                    e.result = true;
                    break;
                // 投诉
                case 'merchantComplain':
                    that.Model.invokeFormOperation({ id: 'tbSubmitComplaint', opcode: 'push', param: { ruleid: "ydj_merchantorder2ser_complaintrecord" } });
                    e.result = true;
                    break;
                case 'sht_assignagent': //指派服务商
                    var cp = {
                        billId: that.Model.pkid,
                        servicetype: that.Model.uiData.fservicetype.id,
                        callback: function (result) {
                            if (!result || !result.newRow) { return; }
                            var newRow = result.newRow;
                            //操作成功则刷新列表
                            if (newRow.flag == 'Y') {
                                that.Model.refresh();
                            }
                        }
                    };
                    var Data;
                    cp = $.extend(true, cp, Data);
                    // 选择代理商对话框
                    that.Model.showForm({
                        formId: 'ydj_assign_agent',
                        param: { openStyle: Consts.openStyle.modal },
                        cp: cp
                    });
                    e.result = true;
                    break;

                case 'sht_refuseagent': //驳回（已指派服务商）
                    var billId = that.Model.pkid;
                    var statusid = that.Model.uiData.fserstatus.id;
                    yiDialog.c('确定要执行 驳回 操作吗？', function () {
                        that.Model.invokeFormOperation({
                            id: 'ydj_merchantorder',
                            opcode: 'sht_refuseagent',
                            param: {
                                formId: 'ydj_merchantorder',
                                domainType: 'bill',
                                optype: '2',
                                opId: billId,
                                serstatus: statusid
                            },
                            selectedRows: [{ PKValue: billId }]
                        });
                    });
                    e.result = true;
                    break;

                //收支明细
                case 'incomedisburse':
                    e.result = true;
                    that.Model.showForm({
                        formId: 'coo_incomedisburserptsal',
                        domainType: Consts.domainType.report,
                        param: { openStyle: Consts.openStyle.modal },
                        cp: {
                            fsettlemainid: that.Model.getValue({ id: 'fmerchantid' })
                        }
                    });
                    break;
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'sht_submitbill':
                case 'sht_quoteprice':
                case 'sht_refuseagent':
                case 'sht_cancelbill':
                case 'sht_confirmprice':
                case 'sht_dispatchbill':
                case 'sht_confirmcance':
                case 'sht_finishbill':
                case 'sht_acceptbill':
                case 'sht_acceptcancel':
                case 'sht_assignagent':
                    if (isSuccess) {
                        that.Model.refresh();
                    }
                    break;
            }
        };

        //创建明细表格
        _child.prototype.onCreateGrid = function (args) {
            if (!args.id) return;
            switch (args.id.toLowerCase()) {
                case this.serviceEntryId:
                    args.result = { multiselect: false, rownumbers: false };
                    break;
            }
        };


        //勾选是否需送货显示送货日期
        _child.prototype.deliveryShow = function (delivery) {
            var that = this;
            switch (delivery) {
                case false:
                    that.Model.removeAttr({ id: '.fdeliverydate', random: 'required' });
                    that.Model.setVisible({ id: '.y-fdeliverydate', value: false });
                    break;
                case true:
                    that.Model.setAttr({ id: '.fdeliverydate', random: { 'required': 'required' } });
                    that.Model.setVisible({ id: '.y-fdeliverydate', value: true });
                    break;
            }
        };


        //字段值改变时
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fisdelivery':
                    switch (e.value) {
                        case false:
                            that.Model.setVisible({ id: '.y-fdeliverydate', value: false });
                            that.Model.setValue({ id: 'fdeliverydate', value: '' });
                            that.Model.removeAttr({ id: '.fdeliverydate', random: 'required' });
                            break;
                        case true:
                            that.Model.setVisible({ id: '.y-fdeliverydate', value: true });
                            that.Model.setAttr({ id: '.fdeliverydate', random: { 'required': 'required' } });
                            break;
                    }
                    break;
                case 'fqty':
                case 'fprice':
                    //计算
                    that.serviceEntryChange({ rowId: e.row, value: e.value });
                    break;
                case 'fserstatus':
                    var serStatus = that.Model.getSimpleValue({ id: 'fserstatus' })
                    that.procSerStatusOp(serStatus);
                    break;
                case 'fservicetype':
                    that.onInitSta();
                    that.Model.setValue({ id: 'fcarid', value: '' });
                    that.Model.setValue({ id: 'fceper', value: '' });
                    that.Model.setValue({ id: 'fcollectadd', value: '' });
                    that.Model.setValue({ id: 'fcollectrel', value: '' });
                    that.Model.setValue({ id: 'fcollectpho', value: '' });
                    that.Model.setValue({ id: 'fisupstairs', value: false });
                    that.Model.setValue({ id: 'fistransport', value: false });
                    break;
                case "fseritemid":
                    var fmerchant = that.Model.getValue({ "id": "fmerchantid" });
                    if (fmerchant) {
                        //查询商户价格信息
                        that.Model.blockUI({ id: '#page#' });
                        yiAjax.p('/dynamic/ydj_seritemprice?operationno=getList', {
                            simpledata: {
                                "whereInfos": "[{\"id\":\"fseritemid\",\"opCode\":\"=\",\"type\":1,\"value\":\"" + e.value.id + "\"},{ \"id\":\"fdealerid\",\"opCode\":\"=\",\"type\":1,\"value\":\"" + fmerchant.id+"\"}]",
                            },
                        },
                        function (r) {
                            that.Model.unblockUI({ id: '#page#' });
                            if (r.operationResult.isSuccess == true && r.operationResult.srvData.datas.length>0) {
                                var fsellprice = r.operationResult.srvData.datas[0].fsellprice;
                                //更新单价
                                that.Model.setValue({ id: 'fprice', row: e.row, value: yiMath.toDecimal(fsellprice, 2) });
                            }
                        },
                        function (m) {
                            that.Model.unblockUI({ id: '#page#' });
                            yiDialog.m({ msg: '服务处理错误：' + yiCommon.extract(m) });
                        });
                    }
                    break;
                
            }
        };


        //根据服务状态显示对应的操作
        _child.prototype.procSerStatusOp = function (serStatus) {
            var that = this;

            switch (serStatus) {
                case 'sht_serstatus00':
                case 'sht_serstatus02':
                    //待商户发布
                    that.Model.setVisible({ id: '.show-droplist', value: false });
                    that.Model.setVisible({ id: '.y-serop-btn1', value: true });
                    break;
                case 'sht_serstatus03':
                    //待平台核价,待商户确认
                    that.Model.setVisible({ id: '.show-droplist', value: true });
                    that.Model.setVisible({ id: '.y-serop-btn1', value: true });
                    break;
                case 'sht_serstatus04':
                    //待平台核价,待商户确认
                    that.Model.setVisible({ id: '.show-droplist', value: true });
                    that.Model.setVisible({ id: '.y-serop-btn1', value: true });
                case 'sht_serstatus05':
                    //待派单,已商户确认
                    that.Model.setVisible({ id: '.show-droplist', value: true });
                    that.Model.setVisible({ id: '.y-serop-btn1', value: true });
                    break;
                case 'sht_serstatus07':// 已完工
                    that.Model.setVisible({ id: '.show-droplist', value: false });
                    that.Model.setVisible({ id: '.y-serop-btn1', value: true });
                    break;

                case 'sht_serstatus08':// 验收完成
                    that.Model.setVisible({ id: '.show-droplist', value: false });
                    that.Model.setVisible({ id: '.y-serop-btn1', value: true });
                    break;
                case 'sht_serstatus09':// 已指派服务商
                    that.Model.setVisible({ id: '.show-droplist', value: true });
                    that.Model.setVisible({ id: '.y-serop-btn1', value: true });
                    that.Model.setAttr({ id: '.y-serop-btn2', random: { 'opcode': 'sht_refusebill' } });
                    that.Model.setText({ id: '.y-serop-btn2', value: '驳回' });
                    break;

                default:
                    //显示下拉按钮
                    that.Model.setVisible({ id: '.show-droplist', value: false });
                    that.Model.setVisible({ id: '.y-serop-btn1', value: true });
                    that.Model.setAttr({ id: '#y-input-group', random: 'class', value: 'input-icon input-group' });
                    break;
            }

            //服务状态
            switch (serStatus) {
                case 'sht_serstatus01':
                case 'sht_serstatus02':
                    //已拒单
                    that.Model.setAttr({ id: '.y-serop-btn1', random: { 'opcode': 'sht_submitbill' } });
                    that.Model.setText({ id: '.y-serop-btn1', value: '重新提交' });
                    break;
                //case 'sht_serstatus03':
                case 'sht_serstatus00':
                    //草稿
                    that.Model.setAttr({ id: '.y-serop-btn1', random: { 'opcode': 'sht_submitbill' } });
                    that.Model.setText({ id: '.y-serop-btn1', value: '提交' });
                    break;
                case 'sht_serstatus09':
                    //已提交
                    that.Model.setAttr({ id: '.y-serop-btn1', random: { 'opcode': 'sht_quoteprice' } });
                    that.Model.setText({ id: '.y-serop-btn1', value: '平台审核' });
                    that.Model.setAttr({ id: '.y-serop-btn2', random: { 'opcode': 'sht_refuseagent' } });
                    that.Model.setText({ id: '.y-serop-btn2', value: '驳回' });
                    that.Model.setAttr({ id: '.y-serop-btn3', random: { 'opcode': 'sht_cancelbill' } });
                    that.Model.setText({ id: '.y-serop-btn3', value: '取消' });
                    break;
                case 'sht_serstatus04':
                    //平台审核
                    that.Model.setAttr({ id: '.y-serop-btn1', random: { 'opcode': 'sht_confirmprice' } });
                    that.Model.setText({ id: '.y-serop-btn1', value: '商户确认' });
                    that.Model.setAttr({ id: '.y-serop-btn2', random: { 'opcode': 'sht_submitbill' } });
                    that.Model.setText({ id: '.y-serop-btn2', value: '驳回' });
                    that.Model.setAttr({ id: '.y-serop-btn3', random: { 'opcode': 'sht_cancelbill' } });
                    that.Model.setText({ id: '.y-serop-btn3', value: '取消' });
                    break;
                case 'sht_serstatus05':
                    //商户确认
                    that.Model.setAttr({ id: '.y-serop-btn1', random: { 'opcode': 'sht_dispatchbill' } });
                    that.Model.setText({ id: '.y-serop-btn1', value: '派单' });
                    that.Model.setAttr({ id: '.y-serop-btn2', random: { 'opcode': 'sht_confirmcancel' } });
                    that.Model.setText({ id: '.y-serop-btn2', value: '驳回' });
                    that.Model.setAttr({ id: '.y-serop-btn3', random: { 'opcode': 'sht_cancelbill' } });
                    that.Model.setText({ id: '.y-serop-btn3', value: '取消' });
                    break;
                case 'sht_serstatus06':
                    //服务中
                    that.Model.setAttr({ id: '.y-serop-btn1', random: { 'opcode': 'sht_finishbill' } });
                    that.Model.setText({ id: '.y-serop-btn1', value: '完工' });
                    break;
                case 'sht_serstatus07':
                    //服务中
                    that.Model.setAttr({ id: '.y-serop-btn1', random: { 'opcode': 'sht_acceptbill' } });
                    that.Model.setText({ id: '.y-serop-btn1', value: '完成验收' });
                    break;
                case 'sht_serstatus08':
                    //服务中
                    that.Model.setAttr({ id: '.y-serop-btn1', random: { 'opcode': 'sht_acceptcancel' } });
                    that.Model.setText({ id: '.y-serop-btn1', value: '取消验收' });
                    break;
                case 'sht_serstatus03':
                    //已提交
                    that.Model.setAttr({ id: '.y-serop-btn1', random: { 'opcode': 'sht_quoteprice' } });
                    that.Model.setText({ id: '.y-serop-btn1', value: '平台审核' });
                    that.Model.setAttr({ id: '.y-serop-btn2', random: { 'opcode': 'sht_assignagent' } });
                    that.Model.setText({ id: '.y-serop-btn2', value: '指派服务商' });
                    that.Model.setAttr({ id: '.y-serop-btn3', random: { 'opcode': 'sht_refuseagent' } });
                    that.Model.setText({ id: '.y-serop-btn3', value: '驳回' });
                    that.Model.setAttr({ id: '.y-serop-btn4', random: { 'opcode': 'sht_cancelbill' } });
                    that.Model.setText({ id: '.y-serop-btn4', value: '取消' });
                    break;
                default:
                    break;
            }
        };


        //明细改变时计算明细
        _child.prototype.serviceEntryChange = function (opt) {

            var that = this;

            //行对象
            var row = that.Model.getEntryRowData({ id: that.serviceEntryId, row: opt.rowId });

            //数量
            var qty = yiMath.toNumber(row.fqty);

            //单价
            var price = yiMath.toNumber(row.fprice);

            //表体金额 = 表体数量 * 表体单价
            var amount = qty * price;

            //更新金额
            that.Model.setValue({ id: 'famount', row: opt.rowId, value: yiMath.toDecimal(amount, 2) });

            var flag = 0;
            //计算预计结算金额
            var serviceEntryData = that.Model.getEntryData({ id: that.serviceEntryId });

            for (var i = 0, j = serviceEntryData.length; i < j; i++) {
                flag = flag + serviceEntryData[i].famount;
            }

            that.Model.setValue({ id: 'fexpectamount', value: flag });
        };


        //隐藏下推按钮
        _child.prototype.OutServiceShow = function (billno) {
            var that = this;
            if (billno) {
                that.Model.setVisible({ id: '[menu="putOutService"]', value: false });
                that.Model.setVisible({ id: '.y-showservice-btn', value: true });
                that.Model.setAttr({ id: '#y-service-input', random: 'class', value: 'input-group input-icon' });
            } else {
                that.Model.setVisible({ id: '[menu="putOutService"]', value: true });
                that.Model.setVisible({ id: '.y-showservice-btn', value: false });
            }
        };
        //隐藏显示源单查看按钮
        _child.prototype.LookSourceShow = function () {
            var that = this;
            if (arguments[0]) {
                that.Model.setVisible({ id: '.y-showorder-btn', value: true });
                that.Model.setAttr({ id: '#y-order-input', random: 'class', value: 'input-group input-icon' });
            }
            else {
                that.Model.setVisible({ id: '.y-showorder-btn', value: false });
            }
        };


        //改变状态
        _child.prototype.changeStatus = function (e) {
            var that = this;

            //如果不存在主键ID，则说明是新增
            if (!that.Model.pkid) {
                yiDialog.a('单据还未保存，不允许执行该操作！');
                return;
            }

            var statusText = that.Model.getText({ id: '#' + e.id });

            //提示用户
            yiDialog.c('是否执行 ' + statusText + ' 操作？', function () {

                that.Model.blockUI({ id: '#page#' });
                yiAjax.p('/bill/ydj_merchantorder?operationno=' + e.id, {
                    selectedRows: [{ PKValue: that.Model.pkid }],
                    simpledata: $.extend({ opid: e.id }, e.param),
                    operationName: statusText
                },
                function (r) {
                    that.Model.unblockUI({ id: '#page#' });
                    if (r.operationResult.isSuccess == true) {

                        //更新服务状态下拉框值
                        that.Model.setValue({ id: 'fserstatus', value: e.param.serstatus });

                        //根据服务状态显示对应的操作
                        that.procSerStatusOp(e.param.serstatus);
                    }
                },
                function (m) {
                    that.Model.unblockUI({ id: '#page#' });
                    yiDialog.m({ msg: '服务处理错误：' + yiCommon.extract(m) });
                });
            });
        };

        return _child;
    })(BasePlugIn);
    window.ydj_merchantorder = window.ydj_merchantorder || ydj_merchantorder;
})();
