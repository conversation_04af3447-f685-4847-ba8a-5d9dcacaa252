///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/pur/ydj_purchasesettledyn.js
*/
; (function () {
    var ydj_purchasesettledyn = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);

            //所有可用于订单支付的账户信息
            that.accounts = [];
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //在原型上定义所有实例共享成员，以便复用
        _child.prototype.entryId = "faccountentity";

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************

        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************

        //处理表单渲染事件：可以在此方法里，对页面做动态化渲染处理
        _child.prototype.onPageViewRendering = function () {
            var that = this;
        };

        //设置结算账户显示隐藏
        _child.prototype.setCoorInfoVisible = function () {
            var that = this;
            var fway = that.Model.getValue({ id: 'fway' });//账户支付显示
            if (fway.id == 'payway_01') {
                that.Model.setValue({ id: 'famount', value: 0 });//结算金额清0
                that.Model.setEnable({ id: 'famount', value: false });//结算金额锁定
                setTimeout(function () {
                    that.Model.setVisible({ id: '.coo-tools', value: true });
                }, 10);
            } else {
                that.Model.setEnable({ id: 'famount', value: true });//结算金额锁定
                var ds = that.Model.getEntryData({ id: that.entryId });
                for (var i = 0, l = ds.length; i < l; i++) {
                    //更新字段值
                    that.Model.setValue({ id: 'faccount', row: ds[i].id, value: "" });
                    that.Model.setValue({ id: 'faccountbalance', row: ds[i].id, value: 0 });
                    that.Model.setValue({ id: 'favailablecredit', row: ds[i].id, value: 0 });
                    that.Model.setValue({ id: 'fsettlementratio', row: ds[i].id, value: 0 });
                    that.Model.setValue({ id: 'fthisamount', row: ds[i].id, value: 0 });
                }
                setTimeout(function () {
                    that.Model.setVisible({ id: '.coo-tools', value: false });
                }, 10);
            }
        }

        //表单字段标签点击后触发
        _child.prototype.onFieldLabelClick = function (e) {
            switch (e.id.toLowerCase()) {
                case 'fcontactunitid':
                    //辅助资料没有单独的视图文件，不允许点击标签打开，所以这里取消平台标准通用逻辑
                    e.cancel = true;
                    break;
            }
        };
        //初始化页面插件
        _child.prototype.onInitialized = function (e) {
            var that = this;
            var cp = that.formContext.cp;
            if (!cp) return;

            //显示源单信息
            that.Model.setText({ id: '.fsourcenumber', value: cp.fsourcenumber });
            that.Model.setText({ id: '.fsupplierid', value: cp.fsupplierid.fname });
            that.Model.setText({ id: '.funsettleamount', value: yiMath.toDecimal(cp.funsettleamount, 2) });
            that.Model.setText({ id: '.funconfirmamount', value: yiMath.toDecimal(cp.funconfirmamount, 2) });
            that.Model.setText({ id: '.fsettledamount', value: yiMath.toDecimal(cp.fsettledamount, 2) });
            that.Model.setText({ id: '.ffbillamount', value: yiMath.toDecimal(cp.ffbillamount, 2) });
            that.Model.setValue({ id: 'fdeptid', value: cp.fdeptid });

            //初始化账户数据源
            var fallaccounts = cp.fallaccounts
            if (fallaccounts && fallaccounts.length > 0) {
                //下拉框数据源
                var accountData = [];
                for (var i = 0; i < fallaccounts.length; i++) {
                    var account = fallaccounts[i];
                    //是否可用于订单支付
                    if (account.canUseInOrderPay) {
                        //没设置结算比例的默认为100
                        if (account.settlementRatio == 0) {
                            account.settlementRatio = 100
                        }
                        that.accounts.push({
                            accountId: account.accountId,
                            accountName: account.accountName,
                            balance: account.balance,
                            canRecharge: account.canRecharge,
                            availablecredit: account.availableCredit,
                            settlementratio: account.settlementRatio,
                        });
                        accountData.push({
                            id: account.accountId,
                            name: account.accountName
                        });
                    }
                }
                that.Model.setComboData({ id: 'faccount', data: accountData });
                if (accountData.length > 0) {
                    //that.Model.setValue({ "id": "faccount", value: accountData[0] });
                } else {
                    var wayDatas = that.Model.viewModel.uiComboData.fway;
                    if (wayDatas && wayDatas.length > 0) {
                        var cloneDatas = wayDatas.map(function (x) { return { "id": x.id, "name": x.name, number: x.number }; });
                        var payData = cloneDatas.find(function (x) { return "payway_01" == x.id });
                        if (payData) {
                            payData.disable = true;
                        }
                        that.Model.setComboData({ id: 'fway', data: cloneDatas });
                        var payData = cloneDatas.find(function (x) { return "payway_04" == x.id });
                        if (!payData) {
                            payData = cloneDatas[0];
                        }
                        that.Model.setValue({ "id": "fway", "value": payData });
                    }
                }
            }

            //显示对方银行账号
            if (cp.fissyn) {
                if (cp.synBankNum && cp.synBankNum.length > 0) {
                    var bankComboData = [{ id: '', name: '&nbsp;' }];
                    for (var i = 0; i < cp.synBankNum.length; i++) {
                        bankComboData.push({
                            id: cp.synBankNum[i].accountId,
                            name: '{0} - {1} - {2}'.format(cp.synBankNum[i].bankNum, cp.synBankNum[i].accountName, cp.synBankNum[i].bankName)
                        });
                    }
                    //***************** - 宋纪强 - 招商银行
                    that.Model.setComboData({ id: 'fsynbankid', data: bankComboData });
                }
                that.Model.setVisible({ id: '.syn-banknum', value: true });
            }

            //获取门店系统参数本地缓存数据
            var param = JSON.parse(localStorage.getItem("storesysparam"));
            if (!param) {
                //本地缓存不存在则请求
                that.Model.invokeFormOperation({
                    id: 'loadstoresysparam',
                    opcode: 'loadstoresysparam',
                    param: {
                        'formId': 'bas_storesysparam',
                        'domainType': 'parameter'
                    }
                });
            }

            //设置结算账户显示隐藏
            that.setCoorInfoVisible();

            //默认锁定支付方式--线下支付
            if ($.trim(that.Model.uiData.fway.id) === 'payway_04') {
                that.Model.setEnable({ id: 'fmybankid', value: false });
                that.Model.setEnable({ id: 'fsynbankid', value: false });
            }

            $(".receiptno").bind("keyup", function (e) {
                //that.Model.setValue({ id: "freceiptno", value: this.value.replace(/[^0-9A-Za-z]+$/, '') });
                //平台方法会触发值改变事件，放弃使用
                $(".receiptno").val(this.value.replace(/[^0-9A-Za-z]+/g, ''));
            });
        };

        //设值前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            e.id = e.id.toLowerCase();
            switch (e.id.toLowerCase()) {
                case 'famount':
                    var famount = $.trim(e.value).split('.');
                    if (famount && famount.length === 2 && famount[1].length > 2) {
                        e.value = yiMath.toNumber(yiMath.toDecimal(e.value, 2));
                        e.result = true;
                        yiDialog.mt({ msg: '结算金额只能输入两位小数点！', skinseq: 2 });
                    }

                    var unSettleAmount = yiMath.toNumber(that.Model.getValue({ id: 'funsettleamount' }));//待结算金额
                    //var way = that.Model.getSimpleValue({ "id": "fway" });
                    //if (way === "payway_01") {
                    //    var accountId = that.Model.getSimpleValue({ id: 'faccount' });
                    //    var balance = 0;
                    //    var availablecredit = 0;
                    //    var settlementratio = 100;
                    //    if (accountId && that.accounts && that.accounts.length > 0) {
                    //        for (var i = 0; i < that.accounts.length; i++) {
                    //            if (that.accounts[i].accountId === accountId) {
                    //                balance = that.accounts[i].balance;
                    //                availablecredit = that.accounts[i].availablecredit;
                    //                settlementratio = that.accounts[i].settlementratio;
                    //                break;
                    //            }
                    //        }
                    //    }
                    //    //有结算比例按照结算比例的可用额度来
                    //    if (settlementratio != 100) {
                    //        unSettleAmount = unSettleAmount > availablecredit ? availablecredit : unSettleAmount;
                    //    } else {
                    //        unSettleAmount = unSettleAmount > balance ? balance : unSettleAmount;
                    //    }
                    //}

                    e.value = yiMath.toNumber(e.value);
                    if (e.value > unSettleAmount) {
                        e.value = unSettleAmount;
                        e.result = true;
                        yiDialog.mt({ msg: '由于输入的金额不满足当前支付条件已自动更新！', skinseq: 2 });
                    }
                    break;

                case 'fthisamount':
                    debugger
                    var unSettleAmount = yiMath.toNumber(that.Model.getValue({ id: 'funsettleamount' }));//待结算金额
                    var favailablecredit = yiMath.toNumber(that.Model.getValue({ id: 'favailablecredit', row: e.row })); //可结算金额

                    //明细行 本次结算金额 汇总
                    var sumthisamount = 0;//可结算金额
                    var ds = that.Model.getEntryData({ id: that.entryId });
                    for (var i = 0, l = ds.length; i < l; i++) {
                        if (ds[i].id != e.row) {
                            sumthisamount += yiMath.toNumber(that.Model.getValue({ id: 'fthisamount', row: ds[i].id }));//本次结算额度
                        }
                    }
                    //剩余结算金额
                    var surplusAmount = unSettleAmount - sumthisamount;
                    if (e.value > favailablecredit || e.value > surplusAmount) {
                        //当前行 填写金额 > 可结算额度  默认时结算金额
                        favailablecredit = e.value > favailablecredit ? favailablecredit : e.value;
                        //当结算金额 > 总待结算金额  默认是剩余结算金额
                        favailablecredit = favailablecredit > surplusAmount ? surplusAmount : favailablecredit;
                        e.value = yiMath.toNumber(yiMath.toDecimal(favailablecredit, 2));
                        e.result = true;
                        that.Model.setValue({ id: "famount", value: sumthisamount + favailablecredit });
                        yiDialog.mt({ msg: '由于输入的金额不满足当前支付条件已自动更新！', skinseq: 2 });
                    }
                    break;
            }
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            e.id = e.id.toLowerCase();
            //汇总本次计算额
            if (e.id === 'famount') {
                var sumAmount = yiMath.toNumber(that.Model.getValue({ id: 'famount' }));
                sumAmount = yiMath.toDecimal(sumAmount, 2);
                that.Model.setText({ id: '.fsettleamount', value: sumAmount });
                that.Model.setValue({ id: 'fsettleamount', value: sumAmount });
            }
            switch (e.id) {
                case "fway":
                    //设置结算账户显示隐藏
                    that.setCoorInfoVisible();

                    if (e.value.id != "payway_06" &&
                        e.value.id != "payway_07" &&
                        e.value.id != "payway_08" &&
                        e.value.id != "payway_11") {
                        that.Model.setValue({ id: "fmybankid", value: "" });
                        that.Model.setValue({ id: "fsynbankid", value: "" });
                        that.Model.setValue({ id: "fsynbankname", value: "" });
                        that.Model.setValue({ id: "fsynbanknum", value: "" });
                        that.Model.setValue({ id: "fsynaccountname", value: "" });
                    }
                    var wayDatas = that.Model.viewModel.uiComboData.fway;
                    var payData = wayDatas.find(function (x) { return e.value.id == x.id });

                    var fwayitems = ["payway_06", "payway_11", "payway_07", "payway_08"];
                    var hasval = $.inArray(e.value.id, fwayitems);
                    //处理任务：30856 子 【慕思现场】客户充值界面，选择 嘉联平台收款 有赞平台收款 这两个结算方式，银行账号可编辑 / 客户新增的支付方式，需要设置银行账号必填 - PC端
                    if (hasval != -1 || (hasval == -1 && payData && !payData.isPrepare)) {
                        that.Model.setEnable({ id: 'fmybankid', value: true });
                        that.Model.setEnable({ id: 'fsynbankid', value: true });
                    } else {
                        that.Model.setEnable({ id: 'fmybankid', value: false });
                        that.Model.setEnable({ id: 'fsynbankid', value: false });
                    }
                    break;
                    ////账户支付,赋值,可用额度,结算比例
                    //if (e.value.id == "payway_01") {
                    //    var accountId = that.Model.getSimpleValue({ "id": "faccount" });
                    //    var availablecredit = 0;
                    //    var settlementratio = 100;
                    //    if (accountId && that.accounts && that.accounts.length > 0) {
                    //        for (var i = 0; i < that.accounts.length; i++) {
                    //            if (that.accounts[i].accountId === accountId) {
                    //                availablecredit = that.accounts[i].availablecredit;
                    //                settlementratio = that.accounts[i].settlementratio;
                    //                that.Model.setValue({ id: "favailablecredit", value: availablecredit });
                    //                that.Model.setValue({ id: "fsettlementratio", value: settlementratio });
                    //            }
                    //        }
                    //    }
                    //}
                    break;
                case 'faccount':
                    var accountId = $.trim(e.value.id);
                    var balance = 0;
                    var topup = false;
                    var availablecredit = 0;
                    var settlementratio = 100;
                    //删除 or 添加 账户
                    that.deleteSelectAccount(e);
                    if (accountId && that.accounts && that.accounts.length > 0) {
                        for (var i = 0; i < that.accounts.length; i++) {
                            if (that.accounts[i].accountId === accountId) {
                                balance = that.accounts[i].balance;
                                topup = that.accounts[i].canRecharge;
                                //账户支付,赋值,可用额度,结算比例
                                availablecredit = that.accounts[i].availablecredit;
                                settlementratio = that.accounts[i].settlementratio;
                                that.Model.setValue({ id: "favailablecredit", row: e.row, value: availablecredit });
                                that.Model.setValue({ id: "fsettlementratio", row: e.row, value: settlementratio });
                                that.Model.setValue({ id: "faccountbalance", row: e.row, value: yiMath.toDecimal(balance, 2) });//账户余额
                                that.Model.setValue({ id: "fthisamount", row: e.row, value: 0 });
                                break;
                            }
                        }
                    }
                    if (!accountId) {
                        that.Model.setValue({ id: "favailablecredit", row: e.row, value: 0 });
                        that.Model.setValue({ id: "fsettlementratio", row: e.row, value: 0 });
                        that.Model.setValue({ id: "faccountbalance", row: e.row, value: 0 });
                        that.Model.setValue({ id: "fthisamount", row: e.row, value: 0 });
                    }
                    //that.Model.setHtml({ id: '.y-account-balance', value: '余额 ' + yiMath.toDecimal(balance, 2) });
                    //that.Model.setVisible({ id: '.y-account-topup', value: topup });
                    // that.Model.setAttr({ id: '.y-account-topup', random: 'data-param', value: "accountId:'" + accountId + "'" });
                    //var unSettleAmount = yiMath.toNumber(that.Model.getValue({ id: 'funsettleamount' }));
                    ////有结算比例按照结算比例的可用额度来
                    //if (settlementratio != 100) {
                    //    unSettleAmount = unSettleAmount > availablecredit ? availablecredit : unSettleAmount;
                    //    var famount = that.Model.getValue({ id: "famount" });
                    //    if (famount > unSettleAmount) {
                    //        that.Model.setValue({ id: "famount", value: unSettleAmount });
                    //        yiDialog.mt({ msg: '由于输入的金额不满足当前支付条件已自动更新！', skinseq: 2 });
                    //    }
                    //    break;
                    //}
                    //unSettleAmount = unSettleAmount > balance ? balance : unSettleAmount;
                    //var famount = that.Model.getValue({ id: "famount" });
                    //if (famount > unSettleAmount) {
                    //    that.Model.setValue({ id: "famount", value: unSettleAmount });
                    //    yiDialog.mt({ msg: '由于输入的金额不满足当前支付条件已自动更新！', skinseq: 2 });
                    //}
                    break;
                case 'fthisamount':
                    // var favailablecredit = yiMath.toNumber(that.Model.getValue({ id: 'favailablecredit', row: e.row })); //可结算金额
                    var fthisamount = yiMath.toNumber(that.Model.getValue({ id: 'fthisamount', row: e.row })); //本次结算金额
                    //明细行 本次结算金额 汇总
                    var sumthisamount = 0;
                    var ds = that.Model.getEntryData({ id: that.entryId });
                    for (var i = 0, l = ds.length; i < l; i++) {
                        if (ds[i].id != e.row) {
                            sumthisamount += yiMath.toNumber(that.Model.getValue({ id: 'fthisamount', row: ds[i].id }));//本次结算额度
                        }
                    }
                    ////本次结算金额>可结算金额 大于结算比例的 值为  可结算金额
                    //if (fthisamount > favailablecredit) {
                    //    that.Model.setValue({ id: "famount", value: sumthisamount + favailablecredit });
                    //    yiDialog.mt({ msg: '由于输入的金额不满足当前支付条件已自动更新！', skinseq: 2 });
                    //    break;
                    //}
                    that.Model.setValue({ id: "famount", value: sumthisamount + fthisamount });
                    break;
                case 'famount':
                case 'freceiptno':
                    debugger;
                    //that.checkInvoiceNumber();
                    break;
            }
        };

        //删除 or 添加 账户
        _child.prototype.deleteSelectAccount = function (e) {
            var that = this;
            var accountData = [];
            var ds = that.Model.getEntryData({ id: that.entryId });
            for (var j = 0; j < that.accounts.length; j++) {
                var account = that.accounts[j]
                var isData = ds.find(function (x) { return account.accountId == x.faccount.id });
                if (isData) {
                    continue;
                }
                accountData.push({
                    id: account.accountId,
                    name: account.accountName
                });
            }
            that.Model.setComboData({ id: 'faccount', data: accountData });
        };

        //表格行创建后事件
        _child.prototype.onEntryRowCreated = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'faccountentity':
                    that.deleteSelectAccount(e);
                    break;
            }
        };

        //表格行删除后事件
        _child.prototype.onEntryRowDeleted = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'faccountentity':
                    that.deleteSelectAccount(e);

                    //计算结算金额
                    var sumthisamount = 0;
                    var ds = that.Model.getEntryData({ id: that.entryId });
                    for (var i = 0, l = ds.length; i < l; i++) {
                        sumthisamount += yiMath.toNumber(that.Model.getValue({ id: 'fthisamount', row: ds[i].id }));//本次结算额度
                    }
                    that.Model.setValue({ id: "famount", value: sumthisamount });
                    break;
            }
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                //充值
                case 'recharge':
                    e.result = true;
                    that.topUp(e);
                    break;
                //取消
                case 'settlecancel':
                    e.result = true;
                    that.Model.close();
                    break;
                //确定
                case 'settleconfirm':
                    e.result = true;

                    //克隆一份当前页面的数据包
                    var cloneData = that.Model.clone();

                    var way = $.trim(cloneData.fway.id);
                    if (!way) {
                        yiDialog.warn('支付方式不能为空！');
                        return;
                    }
                    var accounts = [];
                    var storeParam = JSON.parse(localStorage.getItem("storesysparam"));
                    var enableMustInputBankId = storeParam ? storeParam.fenablemustinputbankid : true;

                    //处理对方银行账号信息
                    if (way === 'payway_06' ||
                        way === 'payway_07' ||
                        way === 'payway_08' ||
                        (way === 'payway_11' && enableMustInputBankId)) {
                        if (!$.trim(cloneData.fmybankid.id)) {
                            yiDialog.warn('请选择银行账号！');
                            return;
                        }
                        if (cloneData.fissyn) {
                            var synBankId = $.trim(cloneData.fsynbankid.id);
                            if (!synBankId) {
                                yiDialog.warn('请选择对方银行！');
                                return;
                            }
                            var banks = cloneData.synBankNum;
                            if (banks && banks.length > 0) {
                                for (var i = 0; i < banks.length; i++) {
                                    if ($.trim(banks[i].accountId) === synBankId) {
                                        cloneData.fsynbankname = banks[i].bankName;
                                        cloneData.fsynbanknum = banks[i].bankNum;
                                        cloneData.fsynaccountname = banks[i].accountName;
                                        break;
                                    }
                                }
                            }
                        }
                    } else if (way === 'payway_01') {

                        var ds = that.Model.getEntryData({ id: that.entryId });
                        var isAccount = ds.find(function (x) { return x.faccount.id == "" });
                        if (isAccount) {
                            yiDialog.warn('请选择账户！');
                            return;
                        }
                        for (var i = 0; i < ds.length; i++) {
                            accounts.push({
                                accountId: ds[i].faccount.id,
                                accountName: ds[i].faccount.fname,
                            });
                        }

                        //var accountId = that.Model.getSimpleValue({ "id": "faccount" });
                        //if (!accountId || $.trim(accountId) == "") {
                        //    yiDialog.warn('请选择账户！');
                        //    return;
                        //}
                        //if (accountId && that.accounts && that.accounts.length > 0) {
                        //    for (var i = 0; i < that.accounts.length; i++) {
                        //        if (that.accounts[i].accountId === accountId) {
                        //            accounts.push({
                        //                accountId: that.accounts[i].accountId,
                        //                accountName: that.accounts[i].accountName
                        //            });
                        //            break;
                        //        }
                        //    }
                        //}
                    }

                    //本次结算额
                    var settleAmount = yiMath.toNumber(cloneData.fsettleamount);
                    if (settleAmount < 0.01) {
                        yiDialog.mt({ msg: '本次结算额必须大于0！', skinseq: 2 });
                        return;
                    }
                    //待结算金额
                    var unSettleAmount = yiMath.toNumber(cloneData.funsettleamount);
                    if (settleAmount > unSettleAmount) {
                        yiDialog.mt({ msg: '本次结算额不允许大于待结算金额！', skinseq: 2 });
                        return;
                    }
                    var receiptno = that.Model.getValue({ id: 'freceiptno' });
                    if (receiptno) {
                        var money = that.Model.getValue({ id: 'fmoney' });
                        that.Model.invokeFormOperation({
                            id: 'checkinvoicenumber',
                            opcode: 'checkinvoicenumber',
                            param: {
                                'formId': 'coo_incomedisburse',
                                'freceiptno': receiptno,
                                'fmoney': money
                            }
                        });
                    }
                    else {
                        that.Model.invokeFormOperation({
                            id: 'tbSettle',
                            opcode: 'Settle',
                            billData: [cloneData],
                            param: {
                                accounts: JSON.stringify(accounts)
                            }
                        });
                    }
                    break;
            }
        };

        //充值
        _child.prototype.topUp = function (e) {
            var that = this;
            var accountId = e.param && e.param.accountId;
            if (!accountId) {
                yiDialog.warn('请选择要充值的账户！');
                return;
            }
            var supplierId = $.trim(that.Model.getSimpleValue({ id: 'fsupplierid' }));
            if (supplierId) {
                that.Model.showForm({
                    formId: 'coo_inpourdialog',
                    param: { openStyle: Consts.openStyle.modal },
                    cp: {
                        fusagetype: {
                            id: e.param.accountId,
                            fnumber: e.param.accountId,
                            fname: ''
                        },
                        pkid: supplierId,
                        formId: 'ydj_supplier',
                        callback: function (result) {

                        }
                    }
                });
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'settle':
                    if (isSuccess) {
                        //设置对话框的返回数据
                        that.Model.setReturnData({ isSuccess: true });
                        //关闭对话框
                        that.Model.close();
                    }
                    break;
                case 'loadstoresysparam':
                    if (isSuccess && srvData) {
                        //设置本地缓存数据
                        var storesysparam = {};
                        for (var data in srvData) {
                            storesysparam[data] = srvData[data];
                        }
                        localStorage.setItem("storesysparam", JSON.stringify(storesysparam));
                    }
                    break;
                case 'checkinvoicenumber':
                    if (isSuccess && srvData) {
                        that.confirming = false;
                        yiDialog.c("【收款小票号】录入重复，您确定继续吗？", function () {
                            if (that.confirming) return;
                            that.confirming = true;

                            that.checkInvoiceNumberPass();
                        }, null, '温馨提示');
                    }
                    else {
                        that.checkInvoiceNumberPass();
                    }
                    break;
            }
        };

        //检查收款小票号跟金额
        _child.prototype.checkInvoiceNumber = function () {
            var that = this;
            var receiptno = that.Model.getValue({ id: 'freceiptno' });
            if (receiptno) {
                var money = that.Model.getValue({ id: 'famount' });
                that.Model.invokeFormOperation({
                    id: 'checkinvoicenumber',
                    opcode: 'checkinvoicenumber',
                    param: {
                        'formId': 'coo_incomedisburse',
                        'freceiptno': receiptno,
                        'fmoney': money
                    }
                });
            }
        };

        //检查收款小票号跟金额通过
        _child.prototype.checkInvoiceNumberPass = function () {
            var that = this;
            var cloneData = that.Model.clone();
            var accounts = [];
            var way = $.trim(cloneData.fway.id);
            if (way === 'payway_01') {
                var ds = that.Model.getEntryData({ id: that.entryId });
                for (var i = 0; i < ds.length; i++) {
                    accounts.push({
                        accountId: ds[i].faccount.id,
                        accountName: ds[i].faccount.fname,
                    });
                }
            }

            that.Model.invokeFormOperation({
                id: 'tbSettle',
                opcode: 'Settle',
                billData: [cloneData],
                param: {
                    accounts: JSON.stringify(accounts)
                }
            });
        };

        return _child;
    })(BasePlugIn);
    window.ydj_purchasesettledyn = window.ydj_purchasesettledyn || ydj_purchasesettledyn;
})();