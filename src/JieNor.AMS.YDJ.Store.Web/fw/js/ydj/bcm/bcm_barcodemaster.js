/**
 * 条码主档业务插件
 * @ sourceURL=/fw/js/ydj/bcm/bcm_barcodemaster.js
 */
; (function () {
    var bcm_barcodemaster = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //初始化编辑页面插件
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            that.loadTraceEntrys();
        };

        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                case "archiving":
                    var selectedRows;
                    if (that.Model.viewModel.domainType == Consts.domainType.list) {
                        selectedRows = that.Model.getSelectRows();
                        if (selectedRows && selectedRows.length > 0) {
                            yiDialog.mt({ msg: ' 手动归档无需选中数据，后台会自动归档符合条件的数据。', skinseq: 1 });
                            e.result = true;
                            return;
                        }
                    }
            }
        };
        //操作前触发的事件
        _child.prototype.onBeforeDoOperation = function (e) {
            var that = this;
            switch (e.opcode) {
                case 'save':
                    //明细数据
                    var traceEntrys = that.Model.getEntryData({ id: 'ftraceentity' });
                    traceEntrys.length = 0; //保存时清空追溯明细(追溯明细仅展示，传入会影响保存)
                    break;
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;

            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.id) {
                case 'querybybarcode':
                    that.fillTraceEntrys(srvData);
                    break;
            }
        };

        //填充属性明细
        _child.prototype.fillTraceEntrys = function (srvData) {
            var that = this;

            //明细数据
            var traceEntrys = that.Model.getEntryData({ id: 'ftraceentity' });
            traceEntrys.length = 0; //先清空

            if (srvData) {
                var fentity = srvData.data || [];
                if (fentity && fentity.length > 0) {
                    for (var i = 0; i < fentity.length; i++) {
                        fentity[i].fremark = fentity[i].fdescription;
                        fentity[i].fstorehousename = fentity[i].fstorehouseid_fname;
                        fentity[i].fstorelocationname = fentity[i].fstorelocationid_fname;
                        traceEntrys.push(fentity[i]);
                    }
                }
                that.Model.refreshEntry({ id: 'ftraceentity' });
                that.Model.viewModel.Model.dataChanged = false;//追溯明细仅填充展示，因此页面数据修改状态改为false,否则平台js判断会影响其他操作
            }
        };

        //根据条码加载追溯明细
        _child.prototype.loadTraceEntrys = function () {
            var that = this;

            var pkid = that.Model.uiData.id;
            if (!pkid) {
                //清空明细数据
                that.Model.deleteEntryData({ id: 'ftraceentity' });
                return;
            }
            //根据条码请求加载条码扫描记录
            that.Model.invokeFormOperation({
                id: 'querybybarcode',
                opcode: 'querydata',
                //filterString: "fbarcode='" + pkid + "' order by fopdatetime",
                filterString: "fbarcode='" + pkid + "' ",//平台包调整，带排序语句会报错
                param: {
                    formId: 'bcm_scanresult',
                    domainType: Consts.domainType.list,
                }
            });
        };

        return _child;
    })(BasePlugIn);
    window.bcm_barcodemaster = window.bcm_barcodemaster || bcm_barcodemaster;
})();