/**
 * ��������ҵ����
 * @ sourceURL=/fw/js/ydj/bcm/bcm_transferintask.js
 */
; (function () {
    var bcm_transferintask = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //��ʼ���༭ҳ����
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            //that.loadTraceEntrys();
        };

        //�����ɹ��󴥷����¼�
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;

            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.id) {
                case 'querybybarcode':
                    that.fillTraceEntrys(srvData);
                    break;
                case 'tbcompletetask':
                case 'tbcontinuetask':
                    that.Model.refresh();
                    break;
            }
        };

        //���������ϸ
        _child.prototype.fillTraceEntrys = function (srvData) {
            var that = this;
            //��ϸ����
            var traceEntrys = that.Model.getEntryData({ id: 'fscannedentity' });
            traceEntrys.length = 0; //�����
            debugger;
            var fentity = srvData.data || [];

            if (fentity && fentity.length > 0) {
                for (var i = 0; i < fentity.length; i++) {
                    fentity[i]["fnote_d"] = fentity[i]["fdescription"];
                    //fentity[i].["fmaterialid_fname"] = fentity[i].fmaterialName;

                    traceEntrys.push(fentity[i]);
                }
            }
            that.Model.refreshEntry({ id: 'fscannedentity' });
            //  console.log(that.Model.getEntryData({ id: 'fscannedentity' }));

        };

        //�����������׷����ϸ
        _child.prototype.loadTraceEntrys = function () {
            debugger
            var that = this;

            var fbillno = that.Model.getSimpleValue({ id: 'fbillno' });
            if (!fbillno) {
                //�����ϸ����
                that.Model.deleteEntryData({ id: 'fscannedentity' });
                return;
            }

            //�������������������ɨ���¼
            //var filterString = "fsourceformid ='{0}' and fsourcebillno ='{1}' and fmainorgid = '{2}' order by fopdatetime ".format("bcm_transferintask", fbillno, Consts.loginCompany.id);
            //ƽ̨�����������������ᱨ��
            var filterString = "fsourceformid ='{0}' and fsourcebillno ='{1}' and fmainorgid = '{2}' ".format("bcm_transferintask", fbillno, Consts.loginCompany.id);

            that.Model.invokeFormOperation({
                id: 'querybybarcode',
                opcode: 'querydata',
                filterString: filterString,
                param: {
                    formId: 'bcm_scanresult',
                    domainType: Consts.domainType.list,
                    endfilterString : filterString
                }
            });

        };

        return _child;
    })(BasePlugIn);
    window.bcm_transferintask = window.bcm_transferintask || bcm_transferintask;
})();