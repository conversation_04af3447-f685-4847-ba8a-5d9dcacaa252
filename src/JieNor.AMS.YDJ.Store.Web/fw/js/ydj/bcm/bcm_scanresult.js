/////**
//// * 扫描记录业务插件
//// * @ sourceURL=/fw/js/ydj/bcm/bcm_scanresult.js
//// */
////; (function () {
////    var bcm_scanresult = (function (_super) {
////        var _child = function (args) {
////            var that = this;
////            _super.call(that, args);
////        };
////        __extends(_child, _super);

////        //初始化编辑页面插件
////        _child.prototype.onBillInitialized = function (args) {
////            var that = this;
            
////            that.loadEntrys();
////        };

////        //操作前触发的事件
////        _child.prototype.onBeforeDoOperation = function (e) {
////            var that = this;
            
////            switch (e.opcode) {
////                case 'save':
////                    //明细数据
////                    var entrys = that.Model.getEntryData({ id: 'fentity' });
////                    entrys.length = 0; //保存时清空商品明细(商品明细仅展示，传入会影响保存)
////                    break;
////            }
////        };

////        //操作成功后触发的事件
////        _child.prototype.onAfterDoOperation = function (e) {
////            if (!e || !e.opcode) return;
////            var that = this;
            
////            var isSuccess = e.result.operationResult.isSuccess;
////            var srvData = e.result.operationResult.srvData;
////            switch (e.id) {
////                case 'querybybarcode':
////                    that.fillEntrys(srvData);
////                    break;
////            }
////        };

////        //填充属性明细
////        _child.prototype.fillEntrys = function (srvData) {
////            var that = this;
            
////            //明细数据
////            var entrys = that.Model.getEntryData({ id: 'fentity' });
////            entrys.length = 0; //先清空

////            if (srvData) {
                
////                var fentity = srvData.data || [];
////                var entrayids = [];
////                if (fentity && fentity.length > 0) {
////                    for (var i = 0; i < fentity.length; i++) {

////                        var hasExist = false;

////                        if (entrayids.length === 0 && fentity[i].entryid!="") {
////                            entrayids.push({ id: fentity[i].entryid});
////                        }
////                        else{
////                            for (var j = 0; j < entrayids.length; j++) {
////                                if (fentity[i].entryid != "" && fentity[i].entryid == entrayids[j].id) {
////                                    hasExist = true;
////                                    break;
////                                } else {
////                                    entrayids.push({ id: fentity[i].entryid });
////                                }
////                            }
////                        }
                        

////                        if (hasExist === false ) {
////                            that.Model.addRow({ id: 'fentity' });
////                            var len = entrys.length-1;
////                            entrys[len].fmaterialid = fentity[i].materialname;
////                            entrys[len].fmtrlnumber = fentity[i].materialnumber;
////                            entrys[len].fmtrlmodel = fentity[i].materialspecifica;
////                            entrys[len].fseltypeid = fentity[i].materialseltype;
////                            entrys[len].fbrandid = fentity[i].materialbrand;
////                            entrys[len].fseriesid = fentity[i].materialseries;
////                            entrys[len].fattrinfo = fentity[i].materialselfattrinfo;
////                            entrys[len].fcustomdesc = fentity[i].materialcustomdesc;
////                        }
////                    }
////                }
////                that.Model.refreshEntry({ id: 'fentity' });
////                that.Model.viewModel.Model.dataChanged = false;//商品明细仅填充展示，因此页面数据修改状态改为false,否则平台js判断会影响其他操作
////            }
////        };

////        //根据条码加载商品明细
////        _child.prototype.loadEntrys = function () {
////            var that = this;
            
////            //var pkid = that.Model.uiData.id;
////            var pkid = that.Model.getValue({ id: 'fbarcode' }).id;
////            if (!pkid) {
////                //清空明细数据
////                that.Model.deleteEntryData({ id: 'fentity' });
////                return;
////            }
////            //根据条码请求加载条码扫描记录
////            that.Model.invokeFormOperation({
////                id: 'querybybarcode',
////                opcode: 'querydata',
////                //filterString: "fbarcode='" + pkid + "' order by fopdatetime",
////                filterString: "fbarcode='" + pkid + "' ",//平台包调整，带排序语句会报错
////                param: {
////                    formId: 'bcm_scanresult',
////                    domainType: Consts.domainType.list,
////                }
////            });
////        };

////        return _child;
////    })(BasePlugIn);
////    window.bcm_scanresult = window.bcm_scanresult || bcm_scanresult;
////})();