(function () {
    var rpt_stockout_storesaler_filter = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //初始化事件
        _child.prototype.onInitialized = function () {
            var that = this;
            var y = new Date().getFullYear(); //获取年份
            var m = new Date().getMonth() + 1; //获取月份
            var d = new Date(y, m, 0).getDate(); //获取当月最后一日
            m = m < 10 ? '0' + m : m; //月份补 0
            d = '01'; // 

            var first_date = [y, m, d].join("-");
            var curdate = new Date().format('yyyy-MM-dd');
            that.Model.setValue({ id: 'fdatefrom', value: first_date });
            that.Model.setValue({ id: 'fdateto', value: curdate });
        };
        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fdatefrom':
                    if (e.value == '') {
                        var y = new Date().getFullYear(); //获取年份
                        var m = new Date().getMonth() + 1; //获取月份
                        var d = new Date(y, m, 0).getDate(); //获取当月最后一日
                        m = m < 10 ? '0' + m : m; //月份补 0
                        d = '01'; // 

                        var first_date = [y, m, d].join("-");
                        var curdate = new Date().format('yyyy-MM-dd');
                        that.Model.setValue({ id: 'fdatefrom', value: first_date });
                    }
                    break;
                case 'fdateto':
                    if (e.value == '') {
                        that.Model.setValue({ id: 'fdateto', value: curdate });
                    }
                    break;
            }
        };

        return _child;
    })(ReportPlugIn);
    window.rpt_stockout_storesaler_filter = window.rpt_stockout_storesaler_filter || rpt_stockout_storesaler_filter;
})();