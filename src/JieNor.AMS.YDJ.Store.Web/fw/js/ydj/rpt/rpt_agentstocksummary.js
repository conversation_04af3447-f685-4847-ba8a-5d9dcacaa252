///////<reference path="/fw/js/consts.js" />
/////*
////@ sourceURL=/fw/js/ydj/stk/rpt/rpt_agentstocksummary.js
////*/
////; (function () {
////    var rpt_agentstocksummary = (function (_super) {
////        //构造函数
////        var _child = function (args) {
////            var that = this;
////            _super.call(this, args);
////        };
////        __extends(_child, _super);

////        //在原型上定义所有实例共享成员，以便复用

////        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************

////        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************
////        //表格行双击后事件
////        _child.prototype.onEntryRowDblClick = function (e) {
////            var that = this;
////            //如果列表页面行双击，根据商品类别打开对应的商品品类库存明细分析表
////            if (e.id === 'list' && e.listMode.toLowerCase() === 'default') {
////                e.result = true;

////                /*  that.Model.showListReport({ formId: 'rpt_categorystockdetailmary', openStyle: 'modal', param: { fcategoryid: e.data.fcategoryid } });*/
////            }
////        };

////        //自身特有的操作
////        _child.prototype.onMenuItemClick = function (e) {
////            var that = this;
////            switch (e.opcode.toLowerCase()) {
////                case 'updatedata':
////                    e.result = true;
////                    yiDialog.c("当前报表正在加速汇总统计中，请耐心等待几分钟后，再回来查看！", function () {
////                        that.Model.invokeFormOperation({
////                            id: 'updateData',
////                            opcode: 'updateData'
////                        });
////                    }, function () {
////                        e.result = true;
////                    }, '温馨提示');
////                    break;
////            }
////        }
////        return _child;
////    })(ListReportPlugin);
////    window.rpt_agentstocksummary = window.rpt_agentstocksummary || rpt_agentstocksummary;
////})();




///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/stk/rpt/rpt_agentstocksummary.js
*/
; (function () {
    var rpt_agentstocksummary = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        __extends(_child, _super);

        //表格行双击后事件
        _child.prototype.onEntryRowDblClick = function (e) {
            var that = this;

            //如果不是“查找返回模式”就屏蔽双击行逻辑，
            //因为报表不需要双击打开详情页面，而报表本身就没有详情页面，如果不屏蔽，则双击时会提示详情页面不存在的错误信息。
            if (e.listMode.toLowerCase() !== 'lookup') {
                e.result = true;
            }
        };


        ////表格行双击后事件
        //_child.prototype.onEntryRowDblClick = function (e) {
        //    var that = this;

        //    //如果不是“查找返回模式”就屏蔽双击行逻辑，
        //    //因为报表不需要双击打开详情页面，而报表本身就没有详情页面，如果不屏蔽，则双击时会提示详情页面不存在的错误信息。
        //    if (e.listMode.toLowerCase() !== 'lookup') {
        //        e.result = true;
        //    }
        //};

        //_child.prototype.onFieldValueFormat = function (e) {
        //    if (!e.id) return;
        //    switch (e.id.toLowerCase()) {
        //        case "fbizqty":
        //            e.cancel = true;
        //            if (e.value === 0) {
        //                e.value = "0";
        //            } else {
        //                e.value = '<span style="color:#529DE3;cursor:pointer;">{0}</span>'.format(e.value);
        //            }
        //            break;
        //    }
        //}

        ////表格单元格点击事件
        //_child.prototype.onEntryCellClick = function (e) {
        //    var that = this;
        //    // 数量
        //    if (e.id === 'list' && e.fieldId === 'fbizqty') {
        //        e.result = true;
        //        if (!e.data || e.data.fbizqty <= 0) return;
        //        //根据即时库存Id显示预留单
        //        that.Model.showListReport({
        //            formId: 'rpt_stockagedetail',
        //            openStyle: 'modal',
        //            param: {
        //                usableQty: e.data.fbizqty,
        //                date: e.data.finstockdate,
        //                fmaterialid: e.data.fmaterialid,
        //                fmainorgid: e.data.fmainorgid,
        //                fstorehouseid: e.data.fstorehouseid,
        //                fcustomdesc: e.data.fcustomdesc,
        //                fattrinfo: e.data.fattrinfo,
        //                fstockunitid: e.data.fstockunitid
        //            }
        //        });
        //    }
        //};


        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                case 'updatedata':
                    e.result = true;

                    var fromOpts = {
                        formId: 'rpt_agentstocksummaryfilter',
                        //domainType: Consts.domainType.list,
                        param: { openStyle: Consts.openStyle.modal },
                        cp: {
                            //fsourceid: that.Model.getSimpleValue({ id: 'id' }),
                            callback: function (result) {
                                debugger
                                if (result.data) {
                                    //that.Model.refresh();
                                }
                            }
                        }
                    };
                    //if (that.Model.pkid) {
                    //    fromOpts.param.filterstring = "fproductid='{0}'".format(that.Model.pkid);
                    //}
                    that.Model.showForm(fromOpts);


                    //debugger
                    //yiDialog.c("是否立即刷新报表数据？确定刷新则需耐心等待几分钟。", function () {
                    //    that.Model.invokeFormOperation({
                    //        id: 'updateData',
                    //        opcode: 'updateData'
                    //    });
                    //}, function () {
                    //    e.result = true;
                    //}, '提示');
                    break;
            }
        }

        //设置类别
        _child.prototype.setCategory = function () {
            var that = this;
            var fromOpts = {
                formId: 'rpt_agentstocksummaryfilter',
                //domainType: Consts.domainType.list,
                param: { openStyle: Consts.openStyle.modal },
                cp: {
                    //fsourceid: that.Model.getSimpleValue({ id: 'id' }),
                    callback: function (result) {
                        debugger
                        if (result.data) {
                            //that.Model.refresh();
                        }
                    }
                }
            };
            if (that.Model.pkid) {
                fromOpts.param.filterstring = "fproductid='{0}'".format(that.Model.pkid);
            }
            that.Model.showForm(fromOpts);
        };



        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            debugger
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            var optData = e.result.operationResult.optionData;
            switch (e.opcode) {
                case 'updatedata':
                    yiDialog.mt({ msg: '报表正在加速汇总统计中，请耐心等待几分钟后，再回来刷新查看！', skinseq: 1 });
                    break;
            }
        }
        return _child;
    })(ListReportPlugin);
    window.rpt_agentstocksummary = window.rpt_agentstocksummary || rpt_agentstocksummary;
})();

