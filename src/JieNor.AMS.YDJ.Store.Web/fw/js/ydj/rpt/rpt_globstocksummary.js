
///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/stk/rpt/rpt_globstocksummary.js
*/
; (function () {
    var rpt_globstocksummary = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        __extends(_child, _super);

        //表格行双击后事件
        _child.prototype.onEntryRowDblClick = function (e) {
            var that = this;

            //如果不是“查找返回模式”就屏蔽双击行逻辑，
            //因为报表不需要双击打开详情页面，而报表本身就没有详情页面，如果不屏蔽，则双击时会提示详情页面不存在的错误信息。
            if (e.listMode.toLowerCase() !== 'lookup') {
                e.result = true;
            }
        };


        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                case 'updatedata':

                    break;
            }
        }

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            debugger
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            var optData = e.result.operationResult.optionData;
            switch (e.opcode) {
                case 'updatedata':
                    yiDialog.mt({ msg: '报表正在加速汇总统计中，请耐心等待几分钟后，再回来刷新查看！', skinseq: 1 });
                    break;
            }
        }
        return _child;
    })(ListReportPlugin);
    window.rpt_globstocksummary = window.rpt_globstocksummary || rpt_globstocksummary;
})();

