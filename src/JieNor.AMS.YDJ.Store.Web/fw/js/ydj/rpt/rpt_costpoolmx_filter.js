///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/stk/rpt_costpoolmx_filter.js
*/
; (function () {
    var rpt_costpoolmx_filter = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);

            //所有可用于订单支付的账户信息
            that.accounts = [];
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //在原型上定义所有实例共享成员，以便复用

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************

        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************

        //表格行双击后事件
        //初始化事件
        _child.prototype.onInitialized = function () {
            debugger;
            var that = this;
            var y = new Date().getFullYear(); //获取年份
            var m = new Date().getMonth() + 1; //获取月份
            var d = new Date(y, m, 0).getDate(); //获取当月最后一日
            m = m < 10 ? '0' + m : m; //月份补 0
            d = '01'; // 

            var first_date = [y, m, d].join("-");
            var curdate = new Date().format('yyyy-MM-dd');
            that.Model.setValue({ id: 'fdatefrom', value: first_date });
            that.Model.setValue({ id: 'fdateto', value: curdate });

        }

        return _child;
    })(ListReportPlugin);
    window.rpt_costpoolmx_filter = window.rpt_costpoolmx_filter || rpt_costpoolmx_filter;
})();