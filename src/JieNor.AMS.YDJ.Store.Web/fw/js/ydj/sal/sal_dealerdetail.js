///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/stk/sal_dealerdetail.js
*/
; (function () {
    var sal_dealerdetail = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）
        _child.prototype.entryId = 'freportlist';

        //页面视图初始化后事件
        _child.prototype.onViewInitialized = function (args) {
            this.setEntryHeight();
        };

        //浏览器窗口缩放时触发
        _child.prototype.onResize = function (args) {
            this.setEntryHeight();
        };

        //表格高度自适应
        _child.prototype.setEntryHeight = function () {
            var that = this;
            var wh = $(window).height();
            var searchHeight = 0;
            that.Model.getEleMent({ id: '.box-one' }).each(function () {
                searchHeight += $(this).outerHeight();
            });
            var gridHeight = wh - searchHeight - 275;
            if (gridHeight < 150) gridHeight = 150;
            that.Model.setEntryHeight({ id: that.entryId, value: gridHeight });
        };

        //初始化事件
        _child.prototype.onInitialized = function (args) {
            var that = this;
            var cp = that.formContext.cp;
            var customerId = cp.fdealerid;
            if (customerId) {

                //从账户列表点击详情打开
                that.Model.setValue({ id: "fcustomerid", value: customerId });

                //加载账户余额
                that.loadAccountBalance(customerId);
            }

            //加载日期区间值
            that.getDataRange();
        };

        //表格双击事件
        _child.prototype.onEntryRowDblClick = function (e) {
            var that = this;
            that.Model.invokeFormOperation({
                id: 'LoadData',
                opcode: 'LoadData',
                selectedRows: [{ PKValue: e.data.fid }],
                param: {
                    formId: 'coo_incomedisburse',
                    domainType: Consts.domainType.bill
                }
            });
        }

        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.entryId:
                    e.result = { multiselect: false };
                    break;
            }
        };

        //表格单元格格式化事件
        _child.prototype.onEntryCellFormat = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case 'frecharge':
                case 'fcharge':
                    if (yiMath.toNumber(e.value) <= 0) {
                        e.result = '';
                    }
                    break;
            }
        };

        //元素点击事件
        _child.prototype.onElementClick = function (e) {
            var that = this;
            var opcode = e.id.toLowerCase();
            switch (e.id.toLowerCase()) {
                case 'tswk'://点击按钮，增加选中状态
                case 'ltwk':
                case 'tsmh':
                case 'ltmh':
                    that.Model.setAttr({ id: '.time-area [optype]', random: 'timearea', value: false });
                    that.Model.setAttr({ id: '[optype={0}]'.format(opcode), random: 'timearea', value: true });
                    that.Model.removeClass({ id: '.time-area [timearea=false]', value: 'active' });
                    that.Model.addClass({ id: '.time-area [timearea=true]', value: 'active' });
                    //加载日期区间值
                    that.getDataRange();
                    break;
            };
        };

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            if (!e.opcode) return;
            switch (e.opcode) {
                case 'recharge':
                case 'charge':
                    //充值
                    //扣款
                    e.result = true;
                    that.showInpourDialog(e);
                    break;
                case 'accountsearch':
                    //按账户查询
                    e.result = true;
                    that.Model.setAttr({ id: '.button-right [opcode]', random: 'dataarea', value: false });
                    that.Model.setAttr({ id: '[accountid={0}]'.format(e.param.accountId), random: 'dataarea', value: true });
                    that.Model.removeClass({ id: '.button-right [dataarea=false]', value: 'btn-primary' });
                    that.Model.addClass({ id: '.button-right [dataarea=false]', value: 'btn-default' });
                    that.Model.removeClass({ id: '.button-right [dataarea=true]', value: 'btn-default' });
                    that.Model.addClass({ id: '.button-right [dataarea=true]', value: 'btn-primary' });
                    that.Model.refresh({ pageIndex: 1 });
                    break;
                case 'search':
                    //查询
                    e.result = true;
                    that.loadAccountBalance();
                    that.Model.refresh({ pageIndex: 1 });
                    break;
            };
        };

        //显示充值或扣款对话框
        _child.prototype.showInpourDialog = function (e) {
            var that = this;
            var customerId = that.Model.getSimpleValue({ "id": "fcustomerid" });
            if (!customerId) {
                yiDialog.warn('请先选择一个客户！');
                return;
            }
            that.Model.showForm({
                formId: e.opcode.toLowerCase() === 'recharge' ? 'coo_inpourdialog' : 'coo_chargedialog',
                param: { openStyle: Consts.openStyle.modal },
                cp: {
                    fusagetype: {
                        id: e.param.accountId,
                        fnumber: e.param.accountId,
                        fname: ''
                    },
                    pkid: customerId,
                    formId: 'ydj_customer',
                    callback: function (result) {
                        if (result && result.isSuccess && result.refreshParent) {
                            that.loadAccountBalance();
                        }
                    }
                }
            });
        };

        //加载协同账户余额
        _child.prototype.loadAccountBalance = function (customerId) {
            var that = this;
            if (!customerId) {
                customerId = that.Model.getSimpleValue({ "id": "fcustomerid" });
            }
            if (!customerId) return;
            that.Model.invokeFormOperation({
                id: 'LoadAccountBalance',
                opcode: 'LoadAccountBalance',
                selectedRows: [{ PKValue: customerId }],
                param: {
                    formId: 'ydj_customer',
                    domainType: Consts.domainType.bill
                }
            });
        };

        //获取时间范围
        _child.prototype.getDataRange = function (e) {
            var that = this;
            var dtType = that.Model.getText({ id: '[timearea=true]' });
            that.Model.invokeFormOperation({
                id: 'dtSpan',
                opcode: 'dtSpan',
                param: {
                    formId: 'sys_reportshell',
                    domainType: Consts.domainType.dynamic,
                    dtType: dtType
                }
            });
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                //加载账户余额
                case 'loadaccountbalance':
                    that.renderAccountView(srvData);
                    break;
                //加载日期区间值
                case 'dtspan':
                    that.Model.setValue({ id: 'fstartdate', value: srvData[0] });
                    that.Model.setValue({ id: 'fenddate', value: srvData[1].split(' ')[0] });
                    //自动查询一次（如果查询条件有变化时，重置 pageIndex 为第 1 页）
                    that.Model.refresh({ pageIndex: 1 });
                    break;
                //加载收支记录信息（用于收支详情对话框的数据展示）
                case 'loaddata':
                    if (isSuccess && srvData) {
                        var cp = srvData.uidata;
                        cp.callback = function (result) {
                            if (result && result.isSuccess) {

                            }
                        };
                        that.Model.showForm({
                            formId: 'coo_incomedisburserptdetail',
                            param: { openStyle: Consts.openStyle.modal },
                            cp: cp
                        });
                    }
                    break;
            }
        };

        //动态渲染账户信息
        _child.prototype.renderAccountView = function (srvData) {
            var that = this;
            if (srvData && srvData.length > 0) {
                var accountInfoHtml = '', accountSearchHtml = '';

                //是否已经渲染过
                var $accounts = that.Model.getEleMent({ id: '.syn-account-item' }).length;

                for (var i = 0; i < srvData.length; i++) {
                    var account = srvData[i];

                    //如果已经渲染过，则更新余额信息
                    if ($accounts > 0) {
                        that.Model.setText({ id: '.' + account.accountId, value: yiMath.toDecimal(account.balance, 2) });
                    } else {
                        //第一次渲染
                        accountInfoHtml += '\
                            <div class="col-md-2 syn-account-item">\
                                <span>{0}账户</span>\
                                <span class="balance {1}">{2}</span>\
                                <span>元</span></br>\
                                <a href="###" opcode="recharge" data-param="accountId:\'{1}\'">充值</a>\
                                <a href="###" opcode="charge" data-param="accountId:\'{1}\'">扣款</a>\
                            </div>'.format(account.accountName, account.accountId, yiMath.toDecimal(account.balance, 2));

                        if (i == 0) {
                            accountSearchHtml += '<button type="button" opcode="accountsearch" accountid="{1}" class="btn btn-default btn-sm btn-primary" dataarea="true" data-param="accountId:\'{1}\'">{0}</button>'
                                .format(account.accountName, account.accountId);
                        } else {
                            accountSearchHtml += '<button type="button" opcode="accountsearch" accountid="{1}" class="btn btn-default btn-sm" data-param="accountId:\'{1}\'">{0}</button>'
                                .format(account.accountName, account.accountId);
                        }
                    }
                }
                if (accountInfoHtml) {
                    that.Model.setHtml({ id: '.account-info', value: accountInfoHtml });
                }
                if (accountSearchHtml) {
                    that.Model.setHtml({ id: '.account-search', value: accountSearchHtml });

                    //第一次渲染后，自动查询一次
                    that.Model.refresh({ pageIndex: 1 });
                }

                that.setEntryHeight();
            }
        };

        /**
         * @description 在刷新报表前触发：可以在该事件中收集报表查询参数
         * @param {object} args
         */
        _child.prototype.onBeforeRefresh = function (args) {
            var that = this;

            var customerId = that.Model.getSimpleValue({ "id": "fcustomerid" });
            if (!customerId) {
                yiDialog.warn('请先选择一个客户！');
                args.result = true;
                return;
            }
            var account = that.Model.getAttr({ id: '[accountid].btn-primary', random: 'accountid' });
            if (!account) {
                args.result = true;
                return;
            }
            var dataType = that.Model.getAttr({ id: '[datetype].active', random: 'datetype' });
            var startDate = that.Model.getValue({ id: 'fstartdate' });
            var endDate = that.Model.getValue({ id: 'fenddate' });

            args.param = {
                customerId: customerId,
                account: account,
                dataType: dataType,
                startDate: startDate,
                endDate: endDate
            };
        };

        /**
         * @description 在刷新报表后触发
         * @param {object} args
         */
        _child.prototype.onAfterRefresh = function (args) {
            this.setEntryHeight();
        };

        return _child;
    })(BillPlugIn);
    window.sal_dealerdetail = window.sal_dealerdetail || sal_dealerdetail;
})();