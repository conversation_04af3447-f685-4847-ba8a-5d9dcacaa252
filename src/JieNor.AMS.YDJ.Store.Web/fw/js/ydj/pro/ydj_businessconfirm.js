
; (function () {
    var ydj_businessconfirm = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            debugger
            var that = this;
            if (!args.opcode) return;
            switch (args.opcode.toLowerCase()) {
                case 'cancel':
                    args.result = true;
                    that.Model.close();
                    break;
                case 'confirm':
                    args.result = true;
                    var humanNature = that.Model.getValue({ id: 'fhumannature' });
                    var businessConfirmDate = that.Model.getValue({ id: 'fbusinessconfirmdate' });

                    if (humanNature <= 0) {
                        yiDialog.warn('对不起，人天必需大于0！');
                        return;
                    }

                    if (!businessConfirmDate) {
                        yiDialog.warn('对不起，商务确认时间不能为空！');
                        return;
                    }

                    var parentViewModel = Index.getPage(that.formContext.cp.parentPageId);

                    //从运维问题上操作
                    if (parentViewModel.formId == 'ydj_maintenance') {
                        parentViewModel.Model.invokeFormOperation({
                            id: 'businessconfirm',
                            opcode: 'businessconfirm',
                            param: {
                                'formId': 'ydj_maintenance',
                                'humanNature': humanNature,
                                'businessConfirmDate': businessConfirmDate
                            }
                        });

                        that.Model.close();
                    }
                    break;
            }
        };





        return _child;
    })(BasePlugIn);
    window.ydj_businessconfirm = window.ydj_businessconfirm || ydj_businessconfirm;
})();