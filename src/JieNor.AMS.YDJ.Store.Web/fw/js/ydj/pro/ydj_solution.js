
; (function () {
    var ydj_solution = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);
        //初始化动态表单插件
        _child.prototype.onInitialized = function (args) {
            var that = this;
            var cp = that.formContext.cp;
            if (cp.fisrepeatquestion) {
                that.Model.setValue({ id: 'fsolution', value: "solution_03" });
               // that.Model.setValue({ id: 'frepeatquestionno', value: cp.frepeatquestionno });
            }


        };

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            debugger
            var that = this;
            if (!args.opcode) return;
            switch (args.opcode.toLowerCase()) {
                case 'cancel':
                    args.result = true;
                    that.Model.close();
                    break;
                case 'confirm':
                    args.result = true;
                    debugger
                    var solutionid = that.Model.getValue({ id: 'fsolution' }).id;
                    var solutionName = that.Model.getValue({ id: 'fsolution' }).fname;
                    var frepeatquestionnoid = that.Model.getValue({ id: 'frepeatquestionno' }).id;
                    var frepeatquestionnoNo = that.Model.getValue({ id: 'frepeatquestionno' }).name;
                    var parentViewModel = Index.getPage(that.formContext.cp.parentPageId);

                    //从运维问题上操作
                    if (parentViewModel.formId == 'ydj_maintenance') {
                        parentViewModel.Model.invokeFormOperation({
                            id: 'questionclose',
                            opcode: 'questionclose',
                            param: {
                                'formId': 'ydj_maintenance',
                                'solutionid': solutionid,
                                'solutionName': solutionName,
                                'frepeatquestionnoid': frepeatquestionnoid,
                                'frepeatquestionnoNo': frepeatquestionnoNo
                            }
                        });

                        that.Model.close();
                    }
                    break;
            }
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            var fldId = e.id.toLowerCase();
            switch (fldId) {
                case 'fsolution':
                    that.setrepeatquestion();
                    break;
            }
        };
        //重复问题显示以及必录
        _child.prototype.setrepeatquestion = function () {
            var that = this;
            var fsolution = that.Model.getValue({ id: "fsolution" });
            var _visible = false;
            if (fsolution.id === 'solution_03') { _visible = true; }
            that.Model.setVisible({ id: '.frepeatquestionno', value: _visible });
           // that.setFieldMustFlag({ id: "frepeatquestionno", caption: "问题重复号", must: true })
        }
        //设置必录标签
        _child.prototype.setFieldMustFlag = function (e) {
            var that = this;
            debugger
            var elem = that.Model.getEleMent({ id: '[name=' + e.id + ']' });
            if (elem) {
                var $label = elem.parent().parent().siblings('.control-label');
                if ($label) {
                    if (e.must) {
                        that.Model.setAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html('<span class="required">*</span>' + e.caption);
                    } else {
                        that.Model.removeAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html(e.caption);
                    }
                }
            }
        };


        return _child;
    })(BasePlugIn);
    window.ydj_solution = window.ydj_solution || ydj_solution;
})();