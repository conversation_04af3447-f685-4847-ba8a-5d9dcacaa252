/*
 * description:跟进业务控制插件
 * author: ymh.
 * create date: 2019-08-23
 * modify by: 
 * modify date:
 * remark:
 *@ sourceURL=/fw/js/ydj/ste/ydj_commissionrule.js
*/
; (function () {
    var ydj_commissionrule = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);

        };
        __extends(_child, _super);

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fschemeid':
                    var schemeId = that.Model.getSimpleValue({ 'id': 'fschemeid' });
                    if (!schemeId || $.trim(schemeId).length == 0) {
                        that.Model.setValue({ "id": "fdeptid", value: "" });
                        return;
                    }
                    that.Model.invokeFormOperation({
                        id: 'getdeptbyschemeid',
                        opcode: 'getdeptbyschemeid',
                        param: {
                            schemeIds: schemeId
                        }
                    });
                    break;
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'getdeptbyschemeid':
                    if (isSuccess) {
                        var dept = srvData.depts[0];
                        that.Model.setValue({ "id": "fdeptid", value: dept });
                    }
                    break;
            }
        }

        return _child;
    })(BillPlugIn);
    window.ydj_commissionrule = window.ydj_commissionrule || ydj_commissionrule;
})();
