///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/ste/ydj_order_chg.js
*/
; (function () {
    var ydj_order_chg = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;

            //一口价反算标记
            that.backCalcFlag = false;

            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //在原型上定义所有实例共享成员，以便复

        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************

        //初始化编辑页面插件
        _child.prototype.onBillInitialized = function (e) {
            var that = this;

            //设置变更前
            that.Model.setText({ id: '#fdealamount_h1', value: that.Model.getSimpleValue({ id: 'fdealamount_h' }) });
            that.Model.setText({ id: '#ffaceamount1', value: that.Model.getSimpleValue({ id: 'ffaceamount' }) });
            that.Model.setText({ id: '#fdistamount_h1', value: that.Model.getSimpleValue({ id: 'fdistamount' }) });
            that.Model.setText({ id: '#fexpense1', value: that.Model.getSimpleValue({ id: 'fexpense' }) });
            that.Model.setText({ id: '#freceivable1', value: that.Model.getSimpleValue({ id: 'freceivable' }) });
            that.Model.setText({ id: '#funreceived1', value: that.Model.getSimpleValue({ id: 'funreceived' }) });
            that.Model.setText({ id: '#fsumamount1', value: that.Model.getSimpleValue({ id: 'fsumamount' }) });
            

            //设置变更后
            that.Model.setText({ id: '#fdealamount_h2', value: that.Model.getSimpleValue({ id: 'fdealamount_h_chg' }) });
            that.Model.setText({ id: '#ffaceamount2', value: that.Model.getSimpleValue({ id: 'ffaceamount_chg' }) });
            that.Model.setText({ id: '#fdistamount_h2', value: that.Model.getSimpleValue({ id: 'fdistamount_chg' }) });
            that.Model.setText({ id: '#fexpense2', value: that.Model.getSimpleValue({ id: 'fexpense_chg' }) });
            that.Model.setText({ id: '#freceivable2', value: that.Model.getSimpleValue({ id: 'freceivable_chg' }) });
            that.Model.setText({ id: '#funreceived2', value: that.Model.getSimpleValue({ id: 'funreceived_chg' }) });
            that.Model.setText({ id: '#fsumamount2', value: that.Model.getSimpleValue({ id: 'fsumamount_chg' }) });

			
        };

        //表格单元格格式化事件
        _child.prototype.onFieldValueFormat = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case 'fdistrate_chg'://抹零后折扣(新)
                case 'fdistrate'://抹零后折扣
                case 'fdistrateraw_chg'://折扣（新）
                case 'fdistrateraw'://折扣
                    //对折扣字段值进行换算显示，比如：9折显示为0.9
                    if (e.value) {
                        e.value = yiMath.toDecimal(e.value / 10, 2);
                        e.cancel = true;
                    }
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.ydj_order_chg = window.ydj_order_chg || ydj_order_chg;
})();