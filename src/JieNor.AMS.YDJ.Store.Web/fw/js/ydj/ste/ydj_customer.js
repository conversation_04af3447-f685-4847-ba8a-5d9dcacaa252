/*
 * description:客户档案业务控制插件
 * author:
 * create date:
 * modify by: linus.
 * modify date:
 * remark:
 *@ sourceURL=/fw/js/ydj/ste/ydj_customer.js
*/
; (function () {
    var ydj_customer = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）
        _child.prototype.capitalEntryId = 'fentry';
        _child.prototype.contactEntry = 'fcuscontacttry';
        _child.prototype.dutyEntry = 'fdutyentry';
        _child.prototype.invoiceEntryId = 'finvoiceentry';
        //创建明细表格
        //_child.prototype.onCreateGrid = function (e) {
        //    var that = this;
        //    if (!e.id) return;
        //    switch (e.id.toLowerCase()) {
        //        case this.contactEntry:
        //            //var packData = that.Model.getEntryData({ id: "fcuscontacttry" });
        //            //e.result = { rownumbers: false, multiselect: that.Model.viewModel.cp.selectMul };
        //            e.result = { multiselect: false, rownumbers: false, treeGrid: true };
        //            break;
        //    }
        //};
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.capitalEntryId:
                case this.contactEntry:

                    e.result = { rownumbers: false, multiselect: false };
                    break;
            }
        };

        //表单头部菜单渲染前事件
        _child.prototype.beforeCreateTopMenu = function (e) {
            if (!e || !e.menus || e.menus.length <= 0) return;

            var that = this;
            //如果是弹框出来的客户列表则隐藏公海客户菜单按钮
            if (that.formContext.openStyle == 'modal') {
                var commoncusMenu = e.menus.filter(x => x["id"] == 'tbCommonCus');
                if (commoncusMenu && commoncusMenu.length > 0) {
                    commoncusMenu[0].visible = false;
                }
            }
        };

        //初始化编辑页面插件
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            //编辑时处理相关字段的隐藏和显示
            var packData = that.Model.getEntryData({ id: "fcuscontacttry" });
            for (var i = 0; i < packData.length; i++) {
                that.Model.setValue({ id: 'fcarea', value: packData[i].fprovince_a["fname"] + packData[i].fcity_a["fname"] + packData[i].fregion_a["fname"], row: packData[i].id });
            }

            var coocompany = that.Model.getSimpleValue({ id: 'fcoocompany' });
            var coostate = that.Model.getSimpleValue({ id: 'fcoostate' });
            var pkid = that.Model.pkid;
            that.proccooStatusOp(coocompany);
            that.showSynbtn(pkid);
            that.showAccount(coostate);
            that.setRequired();
            //判断是否编辑页面     
            //if (!that.Model.pkid) {//新增与编辑都需要判断《客户唯一性报备》是否启用了【来源门店】2023-04-11
                //获取本地缓存（销售参数）
                //本地缓存有延迟，取消2023-04-04
                //var param = JSON.parse(localStorage.getItem("storesysparam"));
                //if (!param) {
                //本地缓存（销售参数）不存在则请求
                that.Model.invokeFormOperation({
                    id: 'loadstoresysparam',
                    opcode: 'loadstoresysparam',
                    param: {
                        'formId': 'bas_storesysparam',
                        'domainType': 'parameter'
                    }
                });
                //} else {
                    // that.setdutydept();
                //}
            //}
            that.setArea();
            //初始化账号信息
            that.Initfentry();
            that.SetImageInfoVisible();

            that.setBillTypeVal();
            
            if(Consts.isdirectsale) {
                setTimeout(function() {
                    that.setSelectAndInputFieldMustFlag({ id: "fsrcstoreid", caption: "来源门店", must: true });
                }, 500);
            }
            
            that.hideCustomerSapNumber();
        };

        //重新设置客户来源
        _child.prototype.setBillTypeVal = function () {
            var that = this;
            that.Model.setEnable({ id: 'fmemberno', value: false });
            var fworkwxuserid = that.Model.getValue({ 'id': 'fworkwxuserid' });

            if (fworkwxuserid != "") {
                $("#fworkwxuserid").val('已设置');
            }


            //保存下拉值
            var para = [];
            //用于过滤重复数据
            var paraIndex = [];
            var fsourceid = that.Model.getValue({ id: 'fsource' })
            //记录选中值
            var oldfid = that.Model.getValue({ id: 'fsource' }).id;

            var data = $("select[name='fsource']").find("option");
            //过滤总部下发选择
            if (fsourceid.fname != '总部下发') {
                $.each(data, function (i, item) {
                    if (item.text != '总部下发'
                        && paraIndex.indexOf(item.value) == -1
                    ) {
                        para.push({ id: item.value, name: item.text });
                        paraIndex.push(item.value);
                    }
                });

                that.Model.setComboData({ id: 'fsource', data: para });
                //设置回选中值
                that.Model.setValue({ id: 'fsource', value: oldfid });
            } else {
                that.Model.setEnable({ id: 'fsource', value: false });
            }
        }

        //设置图片隐藏属性
        _child.prototype.SetImageInfoVisible = function (e) {
            var that = this;
            var fimage = that.Model.getValue({ id: 'fimage' });
            var hasimage = false;
            if (fimage && fimage.id.length > 0) hasimage = true;
            //如果有图片，则默认展开
            setTimeout(function () {
                that.Model.setAttr({ id: '.y_cus_tools', random: 'class', value: hasimage ? 'y_cus_tools collapse' : 'y_cus_tools expand' });
                that.Model.setAttr({ id: '.y_cus_portlet', random: 'style', value: hasimage ? 'display:block' : 'display:none' });
            }, 10);
        };

        //隐藏其他账户信息，只保存贷款
        _child.prototype.Initfentry = function (e) {

            var that = this;
            var rowDatas = that.Model.getValue({ id: 'fentry' });
            var newRowDatas = [];
            if (rowDatas && rowDatas.length > 0) {
                for (var i = 0; i < rowDatas.length; i++) {
                    var fpurpose = rowDatas[i].fpurpose;
                    if (fpurpose && fpurpose.fname && fpurpose.fname == "货款") {
                        newRowDatas.push(rowDatas[i]);
                        break;
                    }
                }
            }
            if (newRowDatas.length == 1) {
                that.Model.setValue({ id: 'fentry', value: newRowDatas });
                that.Model.refreshEntry({ id: 'fentry' });
            }

        }

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                // //负责人
                // case 'fdutyid':
                //     var deptId = that.Model.getSimpleValue({ id: 'fdeptid' });
                //     if ($.trim(deptId).length == 0) {
                //         return;
                //     }
                //     e.result.filterString = "fdeptid<>'' and fdeptid=@fdeptid or (exists (select 1 from t_bd_staffentry se where se.fdeptid=@fdeptid and se.fid=fid) )";
                //     e.result.params = [
                //         { fieldId: 'fdeptid', pValue: deptId }
                //     ];
                //     break;
            }
        };

        // 商机唯一性报备
        _child.prototype.procValidate = function () {
            var that = this;
            var type = that.Model.getValue({ id: 'ftype' });
            var id = that.Model.getValue({ id: 'id' });
            console.log(type);
            if (type && type.id === "customertype_01") {
                var name = that.Model.getValue({ id: 'fname' });
                if (!name) {
                    return;
                }
                that.Model.invokeFormOperation({
                    id: 'validate',
                    opcode: 'validate',
                    param: {
                        id: id,
                        type: type.id,
                        name: name
                    }
                });
            }
            if (type && type.id === "customertype_00") {
                var phone = that.Model.getValue({ id: 'fphone' });
                var wechat = that.Model.getValue({ id: 'fwechat' });
                if (!phone && !wechat) {
                    return;
                }
                that.Model.invokeFormOperation({
                    id: 'validate',
                    opcode: 'validate',
                    param: {
                        id: id,
                        type: type.id,
                        phone: phone,
                        wechat: wechat
                    }
                });
            }
        };
        ////表格单元格点击事件
        //_child.prototype.onEntryCellClick = function (e) {
        //    var that = this;
        //    switch (e.id.toLowerCase()) {
        //        case 'fcuscontacttry':

        //            break;
        //    }
        //};

        //字段设值前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            var fldId = e.id.toLowerCase();
            switch (fldId) {
                case 'fsource':
                    if (e.value && e.value.id == "164743697441886213") {
                        e.result = true;
                        e.value = { id: e.oldvalue ? e.oldvalue.id : '' };
                        yiDialog.warn('总部下发不允许选择！');
                        return;
                    }
                    break;
            }
        };
        _child.prototype.setCity = function (id, value, row) {
            var that = this;
            //that.Model.setValue({ id: id, value: value });
            //将首行客户联系人反写到客户中。
            that.Model.setValue({ id: "faddress", value: row.faddress_a });
            that.Model.setValue({ id: "fname", value: row.fcontact_a });
            that.Model.setValue({ id: "fphone", value: row.fphone_a });
            that.Model.setValue({ id: "fprovince", value: row.fprovince_a });
            that.Model.setValue({ id: "fcity", value: row.fcity_a });
            that.Model.setValue({ id: "fregion", value: row.fregion_a });
        }

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {

            var that = this;
            var rows = that.Model.getSelectRows({ id: "fcuscontacttry" });
            var packData = that.Model.getEntryData({ id: "fcuscontacttry" });
            switch (e.id.toLowerCase()) {
                // case 'fdeptid':
                //     //门店改变时，清空负责人字段
                //     that.Model.setValue({ id: 'fdutyid', value: '' });
                //     break;
                case 'freferrer':
                    var fnameVal = that.Model.getValue({ id: 'fname' });
                    var freferrerVal = that.Model.getValue({ id: 'freferrer' });
                    if (fnameVal == freferrerVal.fname) {
                        that.Model.setValue({ id: 'freferrer', value: '' });
                        yiDialog.mt({ msg: '推荐人不能选择自己！', skinseq: 2 });
                    }
                    break;
                //设置手机号是否必填
                case 'fcusnature':
                    that.setRequired();
                    break;
                case 'ftype':
                    that.procValidate();
                    break;
                case 'fphone':
                    that.Model.setValue({ id: "foldphone", value: that.Model.uiDataOld.fphone });
                    break;
                case 'fwechat':
                    var type = that.Model.getValue({ id: 'ftype' });
                    if (type && type.id === "customertype_00") {
                        that.procValidate();
                    }
                    break;
                case 'fname':
                    var type = that.Model.getValue({ id: 'ftype' });
                    if (type && type.id === "customertype_01") {
                        that.procValidate();
                    }
                    break;
                case 'fcontact_a':
                    //if (packData.length > 0) {
                    //    that.setCity('fcontacts', packData[0].fcontact_a, packData[0]);
                    //}
                    //if (rows[0].data["fcisfirst"]) {
                    //    that.Model.setValue({ id: 'fcontacts', value: rows[0].data["fcontact_a"] });
                    //}
                    //var currSel = packData.filter(item => item.fisdefault == true);
                    //if (currSel && currSel.length > 0 && currSel[0].id == rows[0].pkid) {
                    //    that.setCity('fcontacts', currSel[0].fcontact_a);
                    //}
                    //else {
                    //    currSel = packData.filter(item => item.fcisfirst == true);
                    //    if (currSel && currSel.length > 0 && currSel[0].id == rows[0].pkid) {
                    //        that.setCity('fcontacts', currSel[0].fcontact_a);
                    //    }
                    //}
                    that.setFcisfirst(currSel, packData);
                    break;
                case 'fphone_a':
                    //if (packData.length > 0) {
                    //    that.setCity('fphone', packData[0].fphone_a, packData[0]);
                    //}
                    //if (rows[0].data["fcisfirst"]) {
                    //    that.Model.setValue({ id: 'fphone', value: rows[0].data["fphone_a"] });
                    //}
                    //var currSel = packData.filter(item => item.fisdefault == true);
                    //if (currSel && currSel.length > 0 && currSel[0].id == rows[0].pkid) {
                    //    that.setCity('fphone', currSel[0].fphone_a);
                    //}
                    //else {
                    //    currSel = packData.filter(item => item.fcisfirst == true);
                    //    if (currSel && currSel.length > 0 && currSel[0].id == rows[0].pkid) {
                    //        that.setCity('fphone', currSel[0].fphone_a);
                    //    }
                    //}
                    that.setFcisfirst(currSel, packData);
                    break;
                case 'fprovince_a':
                    //if (packData.length > 0) {
                    //    that.setCity('fprovince', packData[0].fprovince_a.id, packData[0]);
                    //}
                    //if (rows[0].data["fcisfirst"]) {
                    //    //that.Model.setValue({ id: 'fprovince', value: rows[0].data["fprovince_a"] });
                    //}
                    //var currSel = packData.filter(item => item.fisdefault == true);
                    //if (currSel && currSel.length > 0 && currSel[0].id == rows[0].pkid) {
                    //    that.setCity('fprovince', currSel[0].fprovince_a.id);
                    //}
                    //else {
                    //    currSel = packData.filter(item => item.fcisfirst == true);
                    //    if (currSel && currSel.length > 0 && currSel[0].id == rows[0].pkid) {
                    //        that.setCity('fprovince', currSel[0].fprovince_a.id);
                    //    }
                    //}
                    that.setFcisfirst(currSel, packData);
                    break;
                case 'fcity_a':
                    //if (packData.length > 0) {
                    //    that.setCity('fcity', packData[0].fcity_a.id, packData[0]);
                    //}
                    //if (rows[0].data["fcisfirst"]) {
                    //    //that.Model.setValue({ id: 'fcity', value: rows[0].data["fcity_a"] });
                    //}
                    //var currSel = packData.filter(item => item.fisdefault == true);
                    //if (currSel && currSel.length > 0 && currSel[0].id == rows[0].pkid) {
                    //    that.setCity('fcity', currSel[0].fcity_a.id);
                    //}
                    //else {
                    //    currSel = packData.filter(item => item.fcisfirst == true);
                    //    if (currSel && currSel.length > 0 && currSel[0].id == rows[0].pkid) {
                    //        that.setCity('fcity', currSel[0].fcity_a.id);
                    //    }
                    //}
                    that.setFcisfirst(currSel, packData);
                    break;
                case 'fregion_a':
                    //if (packData.length > 0) {
                    //    that.setCity('fregion', packData[0].fregion_a.id, packData[0]);
                    //}
                    //var currSel = packData.filter(item => item.fisdefault == true);
                    //if (currSel && currSel.length > 0 && currSel[0].id == rows[0].pkid) {
                    //    that.setCity('fregion', currSel[0].fregion_a.id);
                    //}
                    //else {
                    //    currSel = packData.filter(item => item.fcisfirst == true);
                    //    if (currSel && currSel.length > 0 && currSel[0].id == rows[0].pkid) {
                    //        that.setCity('fregion', currSel[0].fregion_a.id);
                    //    }
                    //}
                    //if (rows[0].data["fcisfirst"]) {
                    //    //that.Model.setValue({ id: 'fregion', value: rows[0].data["fregion_a"] });
                    //}
                    that.setFcisfirst(currSel, packData);
                    break;
                case 'faddress_a':
                    //if (rows[0].data["fcisfirst"]) {
                    //    that.Model.setValue({ id: 'faddress', value: rows[0].data["faddress_a"] });
                    //}
                    /*var currSel = packData.filter(item => item.fisdefault == true);*/
                    //if (packData.length > 0) {
                    //    that.setCity('faddress', packData[0].faddress_a, packData[0]);
                    //}
                    //var currSel = packData.filter(item => item.FSeq == 1);
                    //if (currSel && currSel.length > 0) {
                    //    that.setCity('faddress', currSel[0].faddress_a);
                    //}
                    //else {
                    //    currSel = packData.filter(item => item.fcisfirst == true);
                    //    if (currSel && currSel.length > 0 && currSel[0].id == rows[0].pkid) {
                    //        that.setCity('faddress', currSel[0].faddress_a);
                    //    }
                    //}
                    break;
                case 'fisdefault':
                    for (var i = 0; i < packData.length; i++) {
                        if (packData[i].fisdefault == true && packData[i].id != e.row) {
                            that.Model.setValue({ id: 'fisdefault', value: false, row: packData[i].id, tgChange: false });
                        }
                        else if (packData[i].id == e.row) {
                            that.Model.setValue({ id: 'fisdefault', value: true, row: packData[i].id, tgChange: false });
                        }
                    }
                    //if (rows.length > 0) {
                    //    that.Model.setValue({ id: 'fisdefault', value: true, row: rows[0].pkid });
                    //}
                    var currSel = packData.filter(item => item.fisdefault == true);
                    //if (currSel && currSel.length > 0) {
                    //    that.Model.setValue({ id: 'fcountry', value: 'CN' });
                    //    that.Model.setValue({ id: 'fprovince', value: currSel[0]["fprovince_a"] });
                    //    that.Model.setValue({ id: 'fcity', value: currSel[0]["fcity_a"] });
                    //    that.Model.setValue({ id: 'fregion', value: currSel[0]["fregion_a"] });
                    //    that.Model.setValue({ id: 'faddress', value: currSel[0]["faddress_a"] });
                    //    that.Model.setValue({ id: 'fcontacts', value: currSel[0]["fcontact_a"] });
                    //    that.Model.setValue({ id: 'fphone', value: currSel[0]["fphone_a"] });
                    //}
                    //else {
                    //    currSel = packData.filter(item => item.fcisfirst == true);
                    //    that.Model.setValue({ id: 'fcountry', value: 'CN' });
                    //    that.Model.setValue({ id: 'fprovince', value: currSel[0]["fprovince_a"] });
                    //    that.Model.setValue({ id: 'fcity', value: currSel[0]["fcity_a"] });
                    //    that.Model.setValue({ id: 'fregion', value: currSel[0]["fregion_a"] });
                    //    that.Model.setValue({ id: 'faddress', value: currSel[0]["faddress_a"] });
                    //    that.Model.setValue({ id: 'fcontacts', value: currSel[0]["fcontact_a"] });
                    //    that.Model.setValue({ id: 'fphone', value: currSel[0]["fphone_a"] });
                    //}
                    that.setFcisfirst(currSel, packData);
                    break;
                case 'fcardid':
                    var cardvalue = e.value;
                    var birthday = that.getBirthdayFromCard(cardvalue);
                    that.Model.setValue({ id: 'fbirthdate', value: birthday });
                    break;
                case 'finvoicedefault':
                    var invoiceEntry = that.Model.getEntryData({ id: that.invoiceEntryId });
                    for (var i = 0; i < invoiceEntry.length; i++) {
                        if (invoiceEntry[i].finvoicedefault == true && invoiceEntry[i].id != e.row) {
                            that.Model.setValue({ id: 'finvoicedefault', value: false, row: invoiceEntry[i].id, tgChange: false });
                        }
                        else if (invoiceEntry[i].id == e.row) {
                            that.Model.setValue({ id: 'finvoicedefault', value: true, row: invoiceEntry[i].id, tgChange: false });
                        }
                    }
                case 'fbankaccount':
                    that.checkBankAccountIsNumber(e);
                    break;
                case 'finvoiceemail':
                    that.validateInvoiceEmails(e);
                    break;
            }
        };

        _child.prototype.setFcisfirst = function (data, packData) {
            var that = this;
            if (data && data.length > 0) {
                //if (data[0].fprovince_a && data[0]["fprovince_a"].toString().trim() != ''
                //    && data[0].fcity_a && data[0]["fcity_a"].toString().trim() != ''
                //    && data[0].fregion_a && data[0]["fregion_a"].toString().trim() != ''
                //    && data[0].fcontact_a && data[0]["fcontact_a"].toString().trim() != ''
                //    && data[0].fphone_a && data[0]["fphone_a"].toString().trim() != '') {
                for (var i = 0; i < packData.length; i++) {
                    if (packData[i].fcisfirst == true && packData[i].id != data[0].id) {
                        that.Model.setValue({ id: 'fcisfirst', value: false, row: packData[i].id });
                    }
                    else if (packData[i].id == data[0].id && data[0].fisdefault == true) {
                        that.Model.setValue({ id: 'fcisfirst', value: true, row: data[0].id });
                    }
                }
                //}
            }
        };

        _child.prototype.setFDefault = function (e, isTrue) {
            var that = this;
            if (isTrue) {

            }
        };

        //设置手机号是否必填
        _child.prototype.setRequired = function (e) {
            var that = this;
            var type = that.Model.getSimpleValue({ id: 'fcusnature' });
            if (type == 'cusnature_02') {
                that.Model.setVisible({ id: '.requiredShow', value: true });
                that.Model.setAttr({ id: 'fphone', way: 2, random: 'required', value: 'required' });
            }
            else {
                that.Model.setVisible({ id: '.requiredShow', value: false });
                that.Model.removeAttr({ id: 'fphone', way: 2, random: 'required' });
            }

        };

        //根据部门重置省市区
        _child.prototype.setArea = function () {
            var that = this;
            setTimeout(function () {
                var fid = that.Model.pkid;
                //只有新增才设置默认省市区
                if (fid) {
                    return;
                }
                yiAjax.p("/bill/ydj_customer?operationno=getareabymydept", {}, function (r) {
                    var data = r.operationResult;
                    if (!data || !data.srvData) return;
                    var fcountry = data.srvData.fcountry;
                    var fprovince = data.srvData.fprovince;
                    var fcity = data.srvData.fcity;
                    var fregion = data.srvData.fregion;
                    if (fcountry) {
                        that.Model.setValue({ id: "fcountry", value: fcountry });
                    }
                    if (fprovince) {
                        that.Model.setValue({ id: "fprovince", value: fprovince });
                    }
                    if (fcity) {
                        that.Model.setValue({ id: "fcity", value: fcity });
                    }
                    if (fregion) {
                        that.Model.setValue({ id: "fregion", value: fregion });
                    }
                }, null, null, null, { async: false });
            }, 150);
        }

        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fispayment':
                case 'fisbalance':
                case 'fcredit':
                    e.result.enabled = that.Model.getValue({ id: 'fissetup' });
                    break;
            }
        };

        //业务插件内容写在此
        _child.prototype.onCustomEntryCellOperation = function (e) {
            var that = this;
            if (that.formContext.domainType == 'bill') {
                var flag = that.Model.getValue({ id: 'id' });
                if (!flag || flag == '') {
                    return e.result = [{
                        id: 'recharge',
                        text: '充值',
                        disabled: true
                    }, {
                        id: 'debit',
                        text: '扣款',
                        disabled: true
                    }, {
                        id: 'selectarea',
                        text: '选择地区',
                        disabled: true
                    }
                    ];
                }
            }
        };

        _child.prototype.getBirthdayFromCard = function (idCard) {

            var birthday = "";
            if (idCard != null && idCard != "") {
                if (idCard.length == 15) {
                    birthday = "19" + idCard.substr(6, 6);
                } else if (idCard.length == 18) {
                    birthday = idCard.substr(6, 8);
                }

                birthday = birthday.replace(/(.{4})(.{2})/, "$1-$2-");
            }

            return birthday;

        };
        //表格按钮点击
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            switch (e.btnid.toLowerCase()) {
                case 'recharge':
                    that.recharge(e);
                    break;
                case 'debit':
                    that.debit(e);
                    break;
                case 'selectarea':
                    that.selectarea(e);
                    break;
            }
        };

        //如果已经发过协同则显示协同信息
        _child.prototype.proccooStatusOp = function (coocompany) {
            var that = this;
            coocompany = $.trim(coocompany);

            that.Model.setVisible({ id: '.y-cooinfo', value: (coocompany == '' || !coocompany) ? false : true });
            that.Model.setVisible({ id: '[opcode=sendsyn]', value: (coocompany == '' || !coocompany) ? true : false });
        };

        //如果单据未保存则隐藏发起协同按钮
        _child.prototype.showSynbtn = function (pkid) {
            var that = this;
            var company = that.Model.getValue({ id: 'fcoocompany' });
            company = $.trim(company);

            that.Model.setVisible({ id: '[opcode=sendsyn]', value: (pkid === '' || !pkid || company != '') ? false : true });
        };

        //如果已经已协同成功则显示协同账户设置
        _child.prototype.showAccount = function (coostate) {
            var that = this;
            //if(coostate === '已协同'){
            //	that.Model.setVisible({ id: '.y-synaccount', value: true });
            //}else{
            //	that.Model.setVisible({ id: '.y-synaccount', value: false });
            //};
        };
        // //设置负责人和部门的默认值
        // _child.prototype.setdutydept = function () {
        //     var that = this;
        //     //‘创建客户自动设置负责人及所属门店’参数
        //     var storeParam = JSON.parse(localStorage.getItem("storesysparam"));
        //     var fautosetduty = storeParam && storeParam.hasOwnProperty('fautosetduty') ? storeParam.fautosetduty : false;
        //     ///如果没有勾选了选项‘创建客户自动设置负责人及所属门店’
        //     if (!fautosetduty) {
        //         that.Model.setValue({ id: 'fdutyid', value: '' });
        //         that.Model.setValue({ id: 'fdeptid', value: '' });
        //     }
        // }

        //充值
        _child.prototype.recharge = function (e) {
            var that = this;
            var rowData = that.Model.getEntryRowData({ id: 'fentry', row: e.row });

            //客户唯一性报备启用【来源门店】时，校验【来源门店】
            var validStore = that.validSourceStore();
            if (!validStore.rsVal) {
                yiDialog.mt({ msg: '对不起，客户未设置来源门店，禁止收款和退款。', skinseq: 2 });
                return;
            }

            var cp = {
                fusagetype: {
                    id: rowData.fpurpose.id
                },
                pkid: that.Model.pkid,
                formId: 'ydj_customer',
                callback: function (result) {

                },
                validStore: validStore
            };

            //充值信息对话框
            that.Model.showForm({
                formId: 'coo_inpourdialog',
                param: { openStyle: Consts.openStyle.modal },
                cp: cp
            });
        };

        //扣款
        _child.prototype.debit = function (e) {
            var that = this;
            var rowData = that.Model.getEntryRowData({ id: 'fentry', row: e.row });

            //客户唯一性报备启用【来源门店】时，校验【来源门店】
            var validStore = that.validSourceStore();
            if (!validStore.rsVal) {
                yiDialog.mt({ msg: '对不起，客户未设置来源门店，禁止收款和退款。', skinseq: 2 });
                return;
            }

            var cp = {
                fusagetype: {
                    id: rowData.fpurpose.id
                },
                pkid: that.Model.pkid,
                formId: 'ydj_customer',
                callback: function (result) {

                },
                validStore: validStore
            };

            //充值信息对话框
            that.Model.showForm({
                formId: 'coo_chargedialog',
                param: { openStyle: Consts.openStyle.modal },
                cp: cp
            });
        };

        _child.prototype.validSourceStore = function () {
            var that = this;
            var validResult = {
                rsVal: true,
                mustFlag: false,
                storeId: ""
            };
            var storesysparam = JSON.parse(localStorage.getItem("storesysparam"));
            if (storesysparam.hasOwnProperty('fcustomerunique')) {
                var customerunique = storesysparam.fcustomerunique;
                var mustFlag = customerunique.indexOf("store") >= 0;
                var srcstoreid = that.Model.getSimpleValue({ id: "fsrcstoreid" });
                validResult.mustFlag = mustFlag;
                validResult.storeId = srcstoreid;
                if (mustFlag && (!srcstoreid || srcstoreid == "")) {
                    validResult.rsVal = false;
                }
            }
            return validResult;
        };

        //选择地区
        _child.prototype.selectarea = function (e) {
            var that = this;
            var rowData = that.Model.getEntryRowData({ id: 'fcuscontacttry', row: e.row });

            var cp = {
                fusagetype: {
                    id: rowData.id
                },
                pkid: that.Model.pkid,
                formId: 'ydj_customer',
                callback: function (result) {
                    if (!result || !result.newRow) { return; }
                    if (result.oldIded) {
                        that.oldId = result.oldIded;
                    }
                    that.setTableData(result.newRow);
                }
            };

            //选择地区对话框
            that.Model.showForm({
                formId: 'ydj_areadialog',
                param: { openStyle: Consts.openStyle.modal },
                cp: cp
            });
        };

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            var listId = that.Model.getSelectRows({});
            if (!e.opcode) return;
            switch (e.opcode) {
                case 'sendsyn':
                    e.result = true;
                    //点击配置按钮显示企业查询弹窗
                    if (!that.Model.pkid) {
                        yiDialog.mt({ msg: '请先保存单据再发起协同！', skinseq: 2 });
                    } else {
                        that.showCooDialog();
                    }
                    break;
                case 'showcompanyinfo':
                    e.result = true;
                    //点击详情查看企业详情
                    that.showCompanyInfo();
                    break;
                case 'bulksyn':
                    e.result = true;
                    //点击批量协同
                    if (listId.length === 0) {
                        yiDialog.mt({ msg: '请选择客户再执行批量协同邀请操作！', skinseq: 2 });
                    } else {
                        that.bulkSyn();
                    }
                    break;
                //3d设计
                case 'threeddesign':
                //case '3ddesign':
                    e.result = true;
                    var address = that.Model.getValue({ id: "faddress" });
                    var custid = that.Model.getValue({ id: "id" });
                    var saler = "";
                    var salerCode = "";
                    var deptid = "";
                    var dutyentry = that.Model.getEntryData({ id: "fdutyentry" });
                    if (dutyentry.length == 1) {
                        saler = dutyentry[0].fdutyid.fname;
                        salerCode = dutyentry[0].fdutyid.id;
                        deptid = dutyentry[0].fdeptid.id;
                        if (!saler || !salerCode) {
                            yiDialog.mt({ msg: '请先勾选一位负责人！', skinseq: 2 });
                            return;
                        }
                    } else if (dutyentry.length == 0) {
                        yiDialog.mt({ msg: '请先勾选一位负责人！', skinseq: 2 });
                        return;
                    } else {
                        var selectdRows = that.Model.getSelectRows({ id: "fdutyentry" });
                        if (selectdRows.length == 0 || selectdRows.length > 1) {
                            yiDialog.mt({ msg: '请先勾选一位负责人！', skinseq: 2 });
                            return;
                        } else {
                            deptid = selectdRows[0].data.fdeptid.id;
                            saler = selectdRows[0].data.fdutyid.fname;
                            salerCode = selectdRows[0].data.fdutyid.id;
                        }
                    }
                    debugger
                    that.Model.invokeFormOperation({
                        id: 'threeddesign',
                        opcode: 'threeddesign',
                        param: {
                            deptid: deptid
                        }
                    });
                    break;
                //新增负责人
                case 'addduty':
                    e.result = true;
                    var cp = {
                        callback: function (result) {
                            if (!result || !result.data) { return; }
                            var data = result.data;
                            // //操作成功则刷新列表
                            // if (newRow.flag == 'Y') {
                            //     that.Model.refresh();
                            // }

                            // 负责人和部门必填
                            if (data.fdutyid.id === "" || data.fdeptid.id === "") {
                                yiDialog.mt({ msg: '请选择负责人和部门！', skinseq: 2 });
                                return;
                            }
                            //移除没有负责人的数据
                            //var dutyentry = that.Model.getEntryData({ id: 'fdutyentry' });
                            //for (var i = 0; i < dutyentry.length; i++) {
                            //    if (!dutyentry[i].fdutyid_d.id) {
                            //        that.Model.deleteRow({ id: 'fdutyentry', row: dutyentry[i].id })
                            //    }
                            //}
                            // that.Model.addRow({
                            //     id: that.dutyEntry,
                            //     data: { fjointime_d: (new Date()).format("yyyy-MM-dd HH:mm:ss"), fdutyid_d: data.fstaffid, fdeptid_d: data.fdeptid }
                            // });

                            that.Model.invokeFormOperation({
                                id: 'addduty',
                                opcode: 'addduty',
                                param: {
                                    deptid: data.fdeptid.id,
                                    customerid: that.Model.pkid,
                                    dutyid: data.fdutyid.id
                                }
                            });
                        }
                    };
                    that.Model.showForm({
                        formId: 'ydj_staffdialog',
                        param: { openStyle: Consts.openStyle.modal },
                        cp: cp
                    });

                    break;
                //更换负责人
                case 'replaceduty':
                    e.result = true;

                    // 获取选中行
                    var selectdRows = that.Model.getSelectRows({ id: that.dutyEntry });
                    if (selectdRows && selectdRows.length === 0) {
                        yiDialog.mt({ msg: '请在客户负责人上选择行！', skinseq: 2 });
                        return;
                    }

                    // 取第一行
                    var selectdRow = selectdRows[0];

                    var cp = {
                        callback: function (result) {
                            if (!result || !result.data) { return; }
                            var data = result.data;

                            // 负责人和部门必填
                            if (data.fdutyid.id === "" || data.fdeptid.id === "") {
                                yiDialog.mt({ msg: '请选择负责人和部门！', skinseq: 2 });
                                return;
                            }

                            // that.Model.setValue({ id: 'fdutyid_d', value: data.fstaffid, row: selectdRow.pkid });
                            // that.Model.setValue({ id: 'fdeptid_d', value: data.fdeptid, row: selectdRow.pkid });

                            that.Model.invokeFormOperation({
                                id: 'replaceduty',
                                opcode: 'replaceduty',
                                param: {
                                    deptid: data.fdeptid.id,
                                    customerid: that.Model.pkid,
                                    dutyid: data.fdutyid.id,
                                    id: selectdRow.pkid
                                }
                            });
                        }
                    };
                    that.Model.showForm({
                        formId: 'ydj_staffdialog',
                        param: { openStyle: Consts.openStyle.modal },
                        cp: cp
                    });

                    break;
                //移除负责人
                case 'removeduty':
                    e.result = true;

                    // 获取选中行
                    var selectdRows = that.Model.getSelectRows({ id: that.dutyEntry });
                    if (selectdRows && selectdRows.length === 0) {
                        yiDialog.mt({ msg: '请在客户负责人上选择行！', skinseq: 2 });
                        return;
                    }

                    // 取第一行
                    var selectdRow = selectdRows[0];
                    // that.Model.deleteRow({ id: that.dutyEntry, row: selectdRow.pkid });

                    that.Model.invokeFormOperation({
                        id: 'removeduty',
                        opcode: 'removeduty',
                        param: {
                            customerid: that.Model.pkid,
                            id: selectdRow.pkid
                        }
                    });
                    break;
                //新建销售机会
                case 'push':
                    e.result = true;
                    yiDialog.c('确认新建销售机会？', function () {
                        that.Model.invokeFormOperation({
                            'id': 'tbPush',
                            'opcode': 'push',
                            'param': {
                                'id': that.Model.getValue({ id: 'id' }),
                                'ruleid': "ydj_customer2ydj_customerrecord"
                            }
                        });
                    });
                    break;
                //反馈
                case 'pushaftback':
                    e.result = true;
                    that.Model.invokeFormOperation({
                        'id': 'tbPushAftback',
                        'opcode': 'push',
                        'param': {
                            'id': that.Model.getValue({ id: 'id' }),
                            'ruleid': "ydj_customer2ste_afterfeedback"
                        }
                    });
                    break;
                //新增服务
                case 'pushservice':
                    e.result = true;
                    that.Model.invokeFormOperation({
                        'id': 'tbPushService',
                        'opcode': 'push',
                        'param': {
                            'id': that.Model.getValue({ id: 'id' }),
                            'ruleid': "ydj_customer2ydj_service"
                        }
                    });
                    break;
                case 'opencompany':
                    var cooState = that.Model.getSimpleValue({ 'id': 'fcoostate' });
                    if ($.trim(cooState) !== '') {
                        e.result = true;
                        yiDialog.mt({ msg: '当前客户已协同或正在协同中，不允许创建企业！', skinseq: 2 })
                    }
                    break;
                case 'followerrecord':
                    e.result = true;
                    var sourceId = that.Model.getSimpleValue({ "id": "id" });
                    var sourceNumber = that.Model.getSimpleValue({ "id": "fnumber" });
                    var sourceFormId = that.Model.viewModel.formId;
                    var customerId = sourceId;
                    var ruleId = "{0}2ydj_followerrecord".format(sourceFormId);
                    var isHiddenNewBtn = false;
                    var isHiddenDeleteBtn = false;
                    var filterString = "";

                    if (customerId && customerId.length > 0) {
                        filterString = " fcustomerid='{0}' ".format(customerId);
                    } else {
                        filterString = " fsourcetype='{0}' and fsourcenumber='{1}' ".format(sourceFormId, sourceNumber);
                    }

                    that.Model.showForm({
                        formId: 'ydj_followerrecord',
                        domainType: Consts.domainType.list,
                        openStyle: 'Modal',
                        param: {
                            openStyle: Consts.openStyle.modal,
                            filterstring: filterString
                        },
                        cp: {
                            sourceFormId: sourceFormId,
                            sourceId: sourceId,
                            ruleId: ruleId,
                            isHiddenNewBtn: isHiddenNewBtn,
                            isHiddenDeleteBtn: isHiddenDeleteBtn
                        }
                    });
                    break;
                case 'recycle':
                    e.result = true;
                    if (that.formContext.domainType == 'bill') {
                        var selectedRows = [{ PKValue: that.Model.getValue({ id: 'id' }) }];
                    }
                    else {
                        var listId = that.Model.getSelectRows({}),
                            selectedRows = [];
                        for (var i = 0, j = listId.length; i < j; i++) {
                            selectedRows.push({ PKValue: listId[i].pkValue });
                        }
                    }
                    that.Model.invokeFormOperation({
                        id: 'ydj_customer',
                        opcode: 'recycle',
                        selectedRows: selectedRows
                    });
                    break;
                case 'save':
                case 'savesubmit':
                case 'saveaudit':
                    if (!that.Model.validForm()) {
                        e.result = true;
                        return;
                    }
                    //取消 校验客户联系人是否为空
                    ////校验客户联系人是否为空
                    //var packData = that.Model.getEntryData({ id: "fcuscontacttry" });
                    //var data = packData.find(o => o.fcontact_a != '');
                    //if (!data)
                    //{
                    //    yiDialog.error('客户联系人不能为空！');
                    //    e.result = true;
                    //    break;
                    //}

                    if (!that.Model.getValue({ id: 'fphone' })) {
                        break;
                    }
                    e.result = true;
                    var selectedRows;
                    if (that.formContext.domainType == 'bill') {
                        selectedRows = [{ PKValue: that.Model.getValue({ id: 'id' }) }];
                    }
                    else {
                        var listId = that.Model.getSelectRows({}),
                            selectedRows = [];
                        for (var i = 0, j = listId.length; i < j; i++) {
                            selectedRows.push({ PKValue: listId[i].pkValue });
                        }
                    }
                    var custype = that.Model.getValue({ id: 'ftype' });
                    //公司类型的客户不做判断
                    if (custype && custype == "customertype_01") {
                        e.result = false;
                        break;
                    }
                    //var params = {
                    //    id: that.Model.getValue({ id: 'id' }),
                    //    phone: that.Model.getValue({ id: 'fphone' }),
                    //    newid: "",
                    //    oldid: "",
                    //    isMerge: "0",
                    //    billData: "",
                    //    mergeType: "0"
                    //};
                    if(Consts.isdirectsale) {
                        var srcStoreId = that.Model.getValue({id:'fsrcstoreid'});
                        if(!srcStoreId.id){
                            yiDialog.warn('直营模式下，来源门店需要填写！');
                            return;
                        }
                    }
                    yiAjax.p('/bill/ydj_customer?operationno=checkcustomerbyphone', {
                        simpleData: { id: that.Model.getValue({ id: 'id' }), phone: that.Model.getValue({ id: 'fphone' }), isMerge: "0", srcstoreid: that.Model.getValue({ id: 'fsrcstoreid' }).id }
                    }, function (r) {
                        var res = r.operationResult;
                        if (res.isSuccess && res.srvData["code"] == 1) {
                            yiDialog.warn('存在重复客户，无法保存！');
                            return;
                        }

                        yiAjax.p('/bill/ydj_customer?operationno=checkphone', {
                            simpleData: {
                                id: that.Model.getValue({ id: 'id' }),
                                phone: that.Model.getValue({ id: 'fphone' }),
                                type: that.Model.getValue({ id: 'ftype' }).id,
                                wechat: that.Model.getValue({ id: 'fwechat' }),
                                name: that.Model.getValue({ id: 'fname' }),
                                srcstoreid: that.Model.getValue({ id: 'fsrcstoreid' }).id,
                            }
                        }, function (r) {
                            var res = r.operationResult;
                            if (!res.isSuccess) {
                                var deptid = res.srvData.deptid;
                                var customerid = res.srvData.customerid;
                                var dutyid = res.srvData.dutyid;
                                if (res.srvData.code == '0') {
                                    yiDialog.warn(res.srvData.message);
                                }
                                if (res.srvData.code == '10000') {
                                    yiDialog.c(res.srvData.message, function () {
                                        //自动给已存在的客户新增责任人
                                        that.Model.invokeFormOperation({
                                            id: 'addduty',
                                            opcode: 'addduty',
                                            param: {
                                                deptid: deptid,
                                                customerid: customerid,
                                                dutyid: dutyid
                                            }
                                        });
                                        setTimeout(function () {
                                            //重新打开已存在客户的页面  
                                            var url = '/list/ydj_customer?operationno=modify&pageId=' + res.srvData.customerid;
                                            var params = { selectedRows: [{ PKValue: res.srvData.customerid }], simpleData: { openStyle: "default" } };
                                            yiAjax.p(url, params, function (r) { }, null, null, $(that.pageSelector), { async: false });
                                        }, 1300)
                                        that.Model.close();
                                    });
                                }
                                return;
                            }
                            else {
                                debugger
                                var fcuscontacttry = that.Model.getEntryData({ id: "fcuscontacttry" });
                                if(fcuscontacttry!=undefined && fcuscontacttry!=""){
                                    for(var i=0;i<fcuscontacttry.length;i++){
                                        if(fcuscontacttry[i].fphone_a==""){
                                            fcuscontacttry[i].fphone_a= that.Model.getValue({ id: 'fphone' });
                                        }
                                    }
                                }
                                var param = e.param;
                                param.formId = "ydj_customer";
                                param.fgeneratesource = "web save";
                                that.Model.invokeFormOperation({
                                    id: 'tbSave',
                                    opcode: 'save',
                                    param: param
                                });
                            }
                        }, null, null, null, { async: true });
                    }, null, null, null, { async: true });

                    break;
                //列表上的分配
                case 'distributeduty':
                    e.result = true;
                    var rows = that.Model.getAllSelectedRows();
                    if (rows.length <= 0) {
                        yiDialog.warn('请先选中行再操作！');
                        return;
                    }
                    //客户行id
                    rowIds = rows.map(c => {
                        return c.data.fbillhead_id;
                    });
                    var cp = {
                        callback: function (result) {
                            debugger;
                            if (!result || !result.data) { return; }
                            var data = result.data;
                           

                            // 负责人和部门必填
                            if (data.fdutyid.id === "" || data.fdeptid.id === "") {
                                yiDialog.mt({ msg: '请选择负责人和部门！', skinseq: 2 });
                                return;
                            }
                            

                            that.Model.invokeFormOperation({
                                id: 'distributeduty',
                                opcode: 'distributeduty',
                                param: {
                                    deptid: data.fdeptid.id,
                                    customerid: JSON.stringify(rowIds),
                                    dutyid: data.fdutyid.id
                                }
                            });
                        }
                    };
                    that.Model.showForm({
                        formId: 'ydj_staffdialog',
                        param: { openStyle: Consts.openStyle.modal },
                        cp: cp
                    });
                    break
            }
        };
        //渲染联系人信息表格
        _child.prototype.setTableData = function (tableData) {
            //此处给表格赋值
            var that = this;
            that.Model.setValue({ id: 'fcarea', value: tableData.fprovince["fname"] + tableData.fcity["fname"] + tableData.fregion["fname"], row: tableData.fusagetype["id"] });
            that.Model.setValue({ id: 'fprovince_a', value: { id: tableData.fprovince["id"], fnumber: tableData.fprovince["fnumber"], fname: tableData.fprovince["fname"] }, row: tableData.fusagetype["id"] });
            that.Model.setValue({ id: 'fcity_a', value: { id: tableData.fcity["id"], fnumber: tableData.fcity["fnumber"], fname: tableData.fcity["fname"] }, row: tableData.fusagetype["id"] });
            that.Model.setValue({ id: 'fregion_a', value: { id: tableData.fregion["id"], fnumber: tableData.fregion["fnumber"], fname: tableData.fregion["fname"] }, row: tableData.fusagetype["id"] });
        };
        //表格行删除前事件：设置 e.result=true 表示不让删除
        _child.prototype.onEntryRowDeleting = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fcuscontacttry':
                    var rowData = that.Model.getEntryRowData({ id: 'fcuscontacttry', row: e.row });
                    if (rowData.fcisfirst == true) {
                        yiDialog.warn('默认联系人不允许被删除！');
                        e.result = true;
                    }
                    break;
                case that.invoiceEntryId:
                    var rowData = that.Model.getEntryRowData({ id: that.invoiceEntryId, row: e.row });
                    if (rowData.finvoicedefault == true) {
                        yiDialog.warn('默认开票信息的明细行不允许被删除！');
                        e.result = true;
                    }
                    break;
            }
        };

        _child.prototype.onEntryRowCreating = function (e) {
            var that = this;
            if (e.prow && e.prow.ignore) return;

            switch (e.id.toLowerCase()) {
                case 'fcuscontacttry':
                    e.result = true;
                    var lastRowIsEmpty = false;
                    var entryDatas = that.Model.getEntryData({ id: that.contactEntry });
                    var rowCount = entryDatas.length;
                    var matId = entryDatas[rowCount - 1].fcontact_a;
                    if (!matId) {
                        lastRowIsEmpty = true;
                    }
                    if (!lastRowIsEmpty) {
                        that.Model.addRow({ id: that.contactEntry, prow: { ignore: true } });
                    }
                    break;
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'summountrefresh':
                    if (isSuccess) {
                        that.Model.setValue({ id: "fdealamount_h", value: srvData });
                    }
                    break;
                case 'validate':
                    console.log(srvData);
                    break;
                case 'save':
                case 'savesubmit':
                case 'saveaudit':
                    if (isSuccess && srvData) {
                        if (that.Model.pkid && that.Model.uiDataOld.fphone != that.Model.getValue({ id: 'fphone' })) {
                            yiDialog.c('客户手机号更新成功，是否将该客户未关闭的销售订单及其下游单据手机号同步更新？', function () {
                                that.Model.invokeFormOperation({
                                    id: 'phonechangework',
                                    opcode: 'phonechangework',
                                    param: {
                                        formId: 'ydj_customer'
                                    }
                                });
                            }, function () {
                                // 取消
                            }, '保存');
                        }
                        const newValue = that.Model.getValue({ id: 'fsource' });
                        const oldValue = that.Model.uiDataOld.fsource;
                        if (that.Model.pkid && newValue.id != oldValue.id && oldValue.id && oldValue.id != '' && oldValue.id != void 0) {
                            yiDialog.c('客户来源更新成功，是否将该客户未关闭的销售合同客户来源同步更新？', function () {
                                that.Model.invokeFormOperation({
                                    id: 'changecustomerfieldevent',
                                    opcode: 'changecustomerfieldevent',
                                    param: {
                                        formId: 'ydj_customer',
                                        field: "fsource"
                                    }
                                });
                            }, function () {
                                // 取消
                            }, '保存');
                        } else if (that.Model.pkid && (oldValue.id == '' || oldValue.id == void 0)) {
                            that.Model.invokeFormOperation({
                                id: 'changecustomerfieldevent',
                                opcode: 'changecustomerfieldevent',
                                param: {
                                    formId: 'ydj_customer',
                                    field: "fsource",
                                    type:"all"
                                }
                            });
                        }
                    }
                    break;
                case 'loadstoresysparam':
                    if (isSuccess && srvData) {
                        //设置本地缓存数据
                        var storesysparam = {};
                        for (var data in srvData) {
                            storesysparam[data] = srvData[data];
                        }
                        localStorage.setItem("storesysparam", JSON.stringify(storesysparam));

                        that.setSourceStoreMust();
                    }
                    break;
                // that.setdutydept();
                case 'checkcustomerbyphone':

                    break;
                case 'threeddesign':
                    if (isSuccess) {
                        var address = that.Model.getValue({ id: "faddress" });
                        var saler = "";
                        var salerCode = "";
                        var deptid = "";
                        var dutyentry = that.Model.getEntryData({ id: "fdutyentry" });
                        if (dutyentry.length == 1) {
                            saler = dutyentry[0].fdutyid.fname;
                            salerCode = dutyentry[0].fdutyid.id;
                            if (!saler || !salerCode) {
                                yiDialog.mt({ msg: '请先勾选一位负责人！', skinseq: 2 });
                                return;
                            }
                        } else if (dutyentry.length == 0) {
                            yiDialog.mt({ msg: '请先勾选一位负责人！', skinseq: 2 });
                            return;
                        } else {
                            var selectdRows = that.Model.getSelectRows({ id: "fdutyentry" });
                            if (selectdRows.length == 0 || selectdRows.length > 1) {
                                yiDialog.mt({ msg: '请先勾选一位负责人！', skinseq: 2 });
                                return;
                            } else {
                                deptid = selectdRows[0].data.fdeptid.id;
                                saler = selectdRows[0].data.fdutyid.fname;
                                salerCode = selectdRows[0].data.fdutyid.id;
                            }
                        }
                        var redObj = {
                            "ClientName": that.Model.getValue({ id: "fname" }),
                            "ClientModelName": address,
                            "customerId": that.Model.getValue({ id: "fnumber" }),
                            "ClientPhone": that.Model.getValue({ id: "fphone" }),
                            "ClientCity": that.Model.getValue({ id: "fprovince" }).fname + that.Model.getValue({ id: "fcity" }).fname + that.Model.getValue({ id: "fregion" }).fname,
                            "SalesMan": saler,
                            "SalesManID": salerCode,
                            "Source": '金蝶'
                        }
                        var url = '/swjapi/sso',
                            param = {
                                custdata: JSON.stringify(redObj)
                            };
                        //请求企业数据
                        yiAjax.p(url, param, function (r) {
                            if (r.success) {
                                window.open(r.data);
                                //window.location.href = r.data;
                            }
                        });
                    }
                    break;
            }
        };

        _child.prototype.setSourceStoreMust = function () {
            var that = this;
            var storesysparam = JSON.parse(localStorage.getItem("storesysparam"));
            if (storesysparam.hasOwnProperty('fcustomerunique')) {
                var customerunique = storesysparam.fcustomerunique;
                var mustFlag = customerunique.indexOf("store") >= 0
                var mustField = ["fsrcstoreid"];
                var mustFieldCaption = ["来源门店"];
                for (var i = 0; i < mustField.length; i++) {
                    that.setFieldMustFlag({ id: mustField[i], caption: mustFieldCaption[i], must: mustFlag });
                }
            }
        }

        //设置必录标签
        _child.prototype.setFieldMustFlag = function (e) {
            var that = this;
            var elem = that.Model.getEleMent({ id: '[name=' + e.id + ']' });
            if (elem) {
                var $label = elem.parent().parent().siblings('.control-label');
                if ($label) {
                    if (e.must) {
                        that.Model.setAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html('<span class="required">*</span>' + e.caption);
                    } else {
                        that.Model.removeAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html(e.caption);
                    }
                }
            }
        };

        //显示批量协同
        _child.prototype.bulkSyn = function (e) {
            var that = this;
            var sendType = that.formContext.formId;
            var selectData = [];
            var listId = that.Model.getSelectRows({});
            for (var i = 0, j = listId.length; i < j; i++) {
                selectData.push(listId[i].pkValue);
            }
            var cp = {
                sendType,
                selectData
            };
            //企业信息数据
            var companyData;
            cp = $.extend(true, cp, companyData);
            //弹出批量协同
            that.Model.showForm({
                formId: 'coo_bulksyn',
                param: { openStyle: Consts.openStyle.modal },
                cp: cp
            });
        };

        //显示对应的企业详情对话框
        _child.prototype.showCompanyInfo = function (e) {
            var that = this;
            var companyId = that.Model.getValue({ id: 'fcoocompanyid' });
            var url = '/bill/coo_company?operationno=getCompanyDetail',
                param = {
                    simpledata: {
                        id: companyId
                    }
                };
            //请求企业数据
            yiAjax.p(url, param, function (r) {
                var cp = {

                };
                //企业信息数据
                var companyData = r.operationResult.srvData;
                cp = $.extend(true, cp, companyData);
                //弹出企业详情
                that.Model.showForm({
                    formId: 'ydj_companyinfo',
                    param: { openStyle: Consts.openStyle.modal },
                    cp: cp
                });
            });
        };

        //显示对话框
        _child.prototype.showCooDialog = function (e) {
            var that = this;
            var basData = that.Model.uiData;
            var formIddata = that.formContext.formId,
                phone = that.Model.getValue({ id: 'fphone' });
            var cp = {
                formIddata: formIddata,
                phone: phone,
                callback: function (result) {
                    if (result && result.isSuccess) {
                        //刷新当前页面
                        that.Model.refresh();
                    }
                }
            };
            cp = $.extend(true, cp, basData);
            //弹出成本核算对话框
            that.Model.showForm({
                formId: 'ydj_companyquery',
                param: { openStyle: Consts.openStyle.modal },
                cp: cp
            });
        };

        _child.prototype.checkBankAccountIsNumber = function (e) {
            debugger;
            var that = this;
            var changeEntry = that.Model.getEntryRowData({ id: that.invoiceEntryId, row: e.row });
            var bankaccount = changeEntry.fbankaccount;
            if (!bankaccount) {
                return;
            }
            var isNumber = /^\d+$/.test(bankaccount);
            if (!isNumber) {
                var seq = changeEntry.fseq;
                yiDialog.warn('输入的银行账号格式有误，请检查!');
                that.Model.setValue({ id: 'fbankaccount', value: '', row: changeEntry.id, tgChange: false });
                that.Model.setValue({ id: 'fbankaccount', value: '', row: changeEntry.id, tgChange: false });
            }
        }

        //检查开票信息中的邮箱是否有效
        _child.prototype.validateInvoiceEmails = function (e) {
            var that = this;
            
            // 邮箱正则表达式
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

            var changeEntry = that.Model.getEntryRowData({ id: that.invoiceEntryId, row: e.row });

            var invoiceemailStr = changeEntry.finvoiceemail;

            if (!invoiceemailStr) {
                return;
            }

            // 分割字符串为单个邮箱数组
            var emails = invoiceemailStr.split(',');

            var isTrue = true;

            // 验证每个邮箱
            for (let i = 0; i < emails.length; i++) {
                // 去除前后空格
                const email = emails[i].trim();

                // 跳过空字符串（连续逗号或末尾逗号的情况）
                if (email === '') continue;

                // 验证邮箱格式
                if (!emailRegex.test(email)) {
                    isTrue = false;
                    break;
                }
            }
            if (!isTrue) {
                yiDialog.warn('输入的电子邮箱格式有误，格式类似于***********或者多个邮箱格式************,<EMAIL>');
                that.Model.setValue({ id: 'finvoiceemail', value: '', row: changeEntry.id, tgChange: false });
                that.Model.setValue({ id: 'finvoiceemail', value: '', row: changeEntry.id, tgChange: false });
            }


        }

        _child.prototype.setSelectAndInputFieldMustFlag = function (e) {
            var that = this;
            // 修改选择器以同时支持input和select
            var elem = that.Model.getEleMent({ id: '[name=' + e.id + ']' });

            if (elem) {
                var $parent, $label;

                // 处理不同类型的字段结构
                if (elem.hasClass('select2-offscreen')) {
                    // select2的情况
                    $parent = elem.siblings('.select2-container').parent();
                } else if (elem.attr('type') === 'lookup' || elem.parent().hasClass('input-group')) {
                    // lookup类型或input-group的情况
                    $parent = elem.closest('.input-group').parent();
                } else {
                    // 普通input的情况
                    $parent = elem.parent().parent();
                }

                $label = $parent.siblings('.control-label');

                if ($label.length) {
                    if (e.must) {
                        // 添加required属性
                        that.Model.setAttr({ id: e.id, random: 'required', value: 'required', way: 2 });

                        // 如果label中还没有required标记，则添加
                        if (!$label.find('.required').length) {
                            $label.html('<span class="required">*</span>' + e.caption);
                        }
                    } else {
                        // 移除required属性
                        that.Model.removeAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html(e.caption);
                    }
                }
            }
        }
        
        _child.prototype.hideCustomerSapNumber = function () {
            var that = this;
            if(Consts.isdirectsale){
                that.Model.setVisible({ id: '.hide_customersapnumber', value: true });
            }else{
                that.Model.setVisible({ id: '.hide_customersapnumber', value: false });
            }
            
        }

        //业务插件内容写在此

        return _child;
    })(BillPlugIn);
    window.ydj_customer = window.ydj_customer || ydj_customer;
})();