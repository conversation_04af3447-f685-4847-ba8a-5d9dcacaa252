using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.Stock.AppService.Enums;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Stock.AppService.Common
{
    /// <summary>
    /// 订单公共部分
    /// </summary>
    public class OrderCommon
    {
        private const string OrderFormId = "ydj_order";
        private const string SaleTransferOrderBillType = "ydj_saletransferorder_01";
        private UserContext Context;
        public static string HasNotShipperAgentMessage = "尚未转单或已转单且不存在需要自行发货的商品！";
        public OrderCommon(UserContext context)
        {
            this.Context = context;
        }

        /// <summary>
        /// 检查是否转单申请总且商品明细中是否存在发货经销商等于当前登录人【预留】
        /// </summary>
        /// <param name="orders"></param>
        /// <exception cref="BusinessException"></exception>
        public void CheckTransferOrderApprovingAndHasShipperAgentByEntry(DynamicObject[] orders, string[] entryIds)
        {
            //二级分销组织无法点击转单申请按钮，通过提交一级后一级走转单流程
            if (orders == null || orders.Count() == 0 || this.Context.IsSecondOrg)
            {
                return;
            }

            //获取主组织经销商
            var fentrys = orders.SelectMany(t => t["fentry"] as DynamicObjectCollection);
            if (entryIds == null || !entryIds.Any())
            {
                entryIds = fentrys.Where(x => !x["id"].IsNullOrEmptyOrWhiteSpace())?.Select(x => Convert.ToString(x["id"]))?.ToArray();
            }
            var agentIds = fentrys.Select(t => Convert.ToString(t["fshipperagentid"]).Trim()).ToList();
            var mainAgents = GetMainAgentIds(this.Context, agentIds);
            List<string> msg = new List<string>();
            foreach (var order in orders)
            {
                if (IsTransferOrderApproving(order, entryIds, msg))
                {
                    //approvingBillNoList.Add(order["fbillno"].ToString());
                }
                else if (IsNeedOrSaleTransferOrder(order))
                {
                    var entries = order["fentry"] as DynamicObjectCollection;
                    if (entries != null && entries.Count > 0)
                    {
                        if (IsNeedTransferOrder(order))
                        {
                            var rows = 0;
                            foreach (var item in entries)
                            {
                                rows++;
                                if (!entryIds.Contains(Convert.ToString(item["id"])))
                                {
                                    continue;
                                }
                                var pName = JNConvert.ToStringAndTrim((item["fproductid_ref"] as DynamicObject)?["fname"]);
                                var ftransferorderstatus = 0;
                                if (!item["ftransferorderstatus"].IsNullOrEmptyOrWhiteSpace())
                                {
                                    ftransferorderstatus = Convert.ToInt32(item["ftransferorderstatus"]);
                                }

                                if (ftransferorderstatus == (int)TransferOrderStatus.Unspecified)
                                {
                                    msg.Add($"商品明细第{rows}行, 商品{pName} 已勾选需转单但尚末转单！不允许执行后续操作 ！");
                                }
                                else if (ftransferorderstatus == (int)TransferOrderStatus.Reject)//审批驳回
                                {
                                    msg.Add($"商品明细第{rows}行, 商品{pName} 已转单驳回但尚末重新转单,不允许执行后续操作 ！");
                                }
                                if (ftransferorderstatus == (int)TransferOrderStatus.Approved)
                                {
                                    var agentId = Convert.ToString(item["fshipperagentid"]).Trim();
                                    var mainAgentId = "";
                                    mainAgents.TryGetValue(agentId, out mainAgentId);
                                    mainAgentId = mainAgentId.IsNullOrEmptyOrWhiteSpace() ? agentId : mainAgentId;
                                    //找不到对应主子经商时报错
                                    if ((mainAgents[item["fshipperagentid"].ToString().Trim()].IsNullOrEmptyOrWhiteSpace()))
                                    // && !withoutShipperEntryList.Contains(order["fbillno"].ToString()))
                                    {
                                        //withoutShipperEntryList.Add(order["fbillno"].ToString());
                                        msg.Add($"商品明细第{rows}行,商品{pName}已转单且不为需要自行发货的商品！不允许执行后续操作 ！");
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (msg.Count > 0)
            {
                throw new BusinessException(string.Join("</br>", msg));
            }
        }

        /// <summary>
        /// 是否转单审核中
        /// </summary>
        /// <param name="obj"></param>
        /// <exception cref="BusinessException"></exception>
        public bool IsTransferOrderApproving(DynamicObject obj, string[] entryIds, List<string> error)
        {
            if (IsNeedTransferOrder(obj))
            {
                var entries = obj["fentry"] as DynamicObjectCollection;
                if (entries != null && entries.Count > 0)
                {
                    var rows = 0;
                    foreach (var entry in entries)
                    {
                        rows++;
                        if (!entryIds.Contains(Convert.ToString(entry["id"])))
                        {
                            continue;
                        }
                        var status = entry["ftransferorderstatus"].ToString();
                        if (!status.IsNullOrEmptyOrWhiteSpace() && Convert.ToInt32(status) == (int)TransferOrderStatus.Approving)
                        {
                            error.Add($"商品明细第{rows}行, 商品 {JNConvert.ToStringAndTrim((entry["fproductid_ref"] as DynamicObject)?["fname"])} 转单申请单为审批中！不允许执行后续操作 ！");
                        }
                    }

                    if (error.Count > 0) return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 是否需转单
        /// </summary>
        /// <returns></returns>
        private bool IsNeedTransferOrder(DynamicObject dataEntity)
        {
            return Convert.ToBoolean(dataEntity["fneedtransferorder"]);
        }

        /// <summary>
        /// 是否销售转单
        /// </summary>
        /// <returns></returns>
        public bool IsSaleTransferOrder(DynamicObject order)
        {
            //当前销售合同的单据类型是否【销售转单】类型 
            var billTypeObj = this.Context.GetBillTypeByBizObject(OrderFormId, SaleTransferOrderBillType);
            if (billTypeObj != null && billTypeObj.fid == order["fbilltype"].ToString())
            {
                return true;
            }
            //判断是否通过转单申请单审核系统创建的【销售转单】《销售合同》
            return !order["fissaletransferorder"].IsNullOrEmptyOrWhiteSpace() && Convert.ToBoolean(order["fissaletransferorder"]);
        }

        /// <summary>
        /// 是需转单或销售转单
        /// </summary>
        /// <param name="order"></param>
        /// <returns></returns>
        public bool IsNeedOrSaleTransferOrder(DynamicObject order)
        {
            return IsNeedTransferOrder(order) || IsSaleTransferOrder(order);
        }

        public void AlterOrderCloseStatus(DynamicObject[] dataEntitys,string opcode) 
        {
            var entryIds = dataEntitys.SelectMany(x => x["fentity"] as DynamicObjectCollection)
                             .Select(x => Convert.ToString(x["fsourceentryid"]))
                             .ToList();

            //获取调拨明细上游明细对应调拨数量(避免一个明细一对多)
            var Dic_entry = dataEntitys.SelectMany(x => x["fentity"] as DynamicObjectCollection)
                             .GroupBy(x => Convert.ToString(x["fsourceentryid"]))
                             .Select(o => new { id = Convert.ToString(o.Key), qty = o.ToList().Sum(x => Convert.ToDecimal(x["fstockoutqty"])) })
                             .ToDictionary(x=>x.id,y=>y.qty);
            //获取源头单据销售合同信息
            //var fsourcenumbers = dataEntitys.Where(t => !t["fsourcenumber"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(t["fsourcetype"]) == "ydj_order").Select(t => Convert.ToString(t["fsourcenumber"])).Distinct().ToList();
            //var orders = this.Context.LoadBizDataByFilter("ydj_order", " fbillno in ('{0}') ".Fmt(string.Join("','", fsourcenumbers)));
            //根据明细id获取关联合同
            var orders = this.Context.LoadBizDataByFilter("ydj_order", " exists (select 1 from t_ydj_orderentry odmx where odmx.fid = t_ydj_order.fid and odmx.fentryid in ('{0}') ) ".Fmt(string.Join("','", entryIds)));

            var billTypeService = this.Context.Container.GetService<IBillTypeService>();
            var orderForm = this.Context.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "ydj_order");
            //判断是否上下样单据类型 是否勾选参数
            var fautoalterstatus = false;
            var selectRowIds = new List<string>();

            if (orders.Count > 0)
            {
                foreach (var order in orders)
                {
                    var paramSetObj = billTypeService.GetBillTypeParamSet(this.Context, orderForm, Convert.ToString(order["fbilltype"]));
                    if (paramSetObj != null)
                    {
                        bool.TryParse(Convert.ToString(paramSetObj["fautoalterstatus"]), out fautoalterstatus);
                    }
                    //上下样合同勾选合同调拨出库自动更新单据状态时才去更新关闭状态
                    if (fautoalterstatus)
                    {
                        var orderid = Convert.ToString(order["id"]);
                         
                        var entrys = order["fentry"] as DynamicObjectCollection;
                        foreach (var entry in entrys) 
                        {
                            var id = Convert.ToString(entry["id"]);
                            //当前调拨单的数量
                            decimal qty = 0;
                            //历史的已出库数
                            decimal foutqty = Convert.ToDecimal(entry["foutqty"]);
                            //更新销售合同已出库数
                            Dic_entry.TryGetValue(id, out qty);
                            if (qty > 0) 
                            {
                                if (opcode.Equals("audit"))
                                {
                                    //历史的已出库数 + 当前调拨单的数量
                                    entry["foutqty"] = foutqty+ qty;
                                    entry["fbizoutqty"] = foutqty+ qty;
                                }
                                else 
                                {
                                    //历史的已出库数 - 当前调拨单的数量
                                    entry["foutqty"] = foutqty - qty;
                                    entry["fbizoutqty"] = foutqty - qty;
                                }
                                //计算销售合同【未出库基本数量】
                                entry["funstockoutqty"] = Convert.ToDecimal(entry["fbizqty"]) - Convert.ToDecimal(entry["fbizoutqty"]) + Convert.ToDecimal(entry["fbizreturnqty"]);
                            }
                        }
                        //更新销售已出库数为调拨数量 可能是部分调拨
                        List<string> lst = entrys.Select(o => Convert.ToString(o["id"])).ToList<string>();
                        selectRowIds.AddRange(lst);
                        Core.Helpers.DocumentStatusHelper.CalcOrderCloseStatus(order,this.Context);
                        //Core.Helpers.DocumentStatusHelper.CalcOrderCloseStatus_SXY(order, entryIds, opcode);
                    }
                }
                this.Context.SaveBizData("ydj_order", orders);
                //if (opcode.Equals("audit")) 
                //{
                //    //自动释放预留
                //    ReserveReleaseSetting setting = new ReserveReleaseSetting() { SelectEntryRow = selectRowIds, ReleaseType = 0, ReleaseWay = 6 };
                //    var releaseService = this.Context.Container.GetService<IReserveReleaseService>();
                //    var result = releaseService.Release(this.Context, setting, orderForm, orders, null);
                //}
            }
        }



        /// <summary>
        /// 获取主经销商
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="agentIds">子经销商ids</param>
        /// <returns>
        /// {
        ///     { "子经销商", "主经销商" },
        ///     { "子经销商", null }        // 没有时为null
        /// }
        /// </returns>
        public Dictionary<string, string> GetMainAgentIds(UserContext userCtx, IEnumerable<string> agentIds)
        {
            Dictionary<string, string> result = new Dictionary<string, string>();

            if (agentIds.IsNullOrEmpty()) return result;

            var topCtx = userCtx.CreateTopOrgDBContext();

            foreach (var agentId in agentIds)
            {
                result[agentId] = null;
            }

            string sql = $@"
select 
    m.fmainagentid
    ,me.fsubagentid
from t_bas_mac m 
inner join t_bas_macentry me on m.fid=me.fid
where m.fmainorgid='{topCtx.Company}' and m.fforbidstatus='0' and me.fsubagentid in ({string.Join(",", agentIds.Select(s => $"'{s}'"))})
";
            using (var reader = topCtx.Container.GetService<IDBService>().ExecuteReader(topCtx, sql))
            {
                while (reader.Read())
                {
                    var fsubagentid = reader.GetValueToString("fsubagentid");
                    var fmainagentid = reader.GetValueToString("fmainagentid");

                    result[fsubagentid] = fmainagentid;
                }
            }

            return result;
        }
    }
}
