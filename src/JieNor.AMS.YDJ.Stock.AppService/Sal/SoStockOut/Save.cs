using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.DataTransferObject;

namespace JieNor.AMS.YDJ.Stock.AppService.Sal.SoStockOut
{
    /// <summary>
    /// 销售出库单：保存
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("save")]
    public class CustomerSave : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            //出库明细实发数量必须大于0
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //是否存在实发数量小于等于0的明细
                var entry = newData["fentity"] as DynamicObjectCollection;
                if(entry.FirstOrDefault(o => Convert.ToDecimal(o["fqty"]) <= 0) != null)
                {
                    return false;
                }
                return true;
            }).WithMessage("出库明细实发数量必须大于0！"));
        }
    }
}