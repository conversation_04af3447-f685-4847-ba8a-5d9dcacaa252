using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.Core.Interface.StockInit;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface.Stock;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface.Log;
using System.Diagnostics;
using System.Threading;

namespace JieNor.AMS.YDJ.Stock.AppService
{
    /// <summary>
    /// 库存基础服务
    /// </summary>
    [InjectService]
    public class StockBaseService : IStockBaseService
    {
        /// <summary>
        /// 模型服务
        /// </summary>
        [InjectProperty]
        protected IMetaModelService MetaModelService { get; set; }

        /// <summary>
        /// 数据库写服务
        /// </summary>
        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        /// <summary>
        /// 数据库读服务
        /// </summary>
        [InjectProperty]
        protected IDBService DBService { get; set; }

        /// <summary>
        /// 进度条服务
        /// </summary>
        [InjectProperty]
        protected ITaskProgressService TaskProgressService { get; set; }


        /// <summary>
        /// 初始化库存环境
        /// </summary>
        /// <param name="userCtx"></param>
        public void InitInventoryEnvironment(UserContext userCtx)
        {
            var dm = userCtx.Container.GetService<IDataManager>();
            var invLogBillMeta = this.MetaModelService.LoadFormModel(userCtx, "stk_invcomputelog");
            dm.InitDbContext(userCtx, invLogBillMeta.GetDynamicObjectType(userCtx));

            var invBalanceBillMeta = this.MetaModelService.LoadFormModel(userCtx, "stk_inventorybalance");
            dm.InitDbContext(userCtx, invBalanceBillMeta.GetDynamicObjectType(userCtx));

            var invInitBillMeta = this.MetaModelService.LoadFormModel(userCtx, InventoryFlexModel.InitStockBillFormId);
            dm.InitDbContext(userCtx, invInitBillMeta.GetDynamicObjectType(userCtx));

            foreach (var invFormId in InventoryFlexModel.AllStockBillFormIds)
            {
                var formMeta = this.MetaModelService.LoadFormModel(userCtx, invFormId);
                dm.InitDbContext(userCtx, formMeta.GetDynamicObjectType(userCtx));
            }
        }


        /// <summary>
        /// 库存是否已结束初始化
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public bool IsInitedInventory(UserContext userCtx)
        {
            var systemProfileService = userCtx.Container.BeginLifetimeScope(Guid.NewGuid().ToString()).GetService<ISystemProfile>();
            var initStatus = systemProfileService.GetSystemParameter(userCtx, "stk_invcompleteinit", "finitstatus", "");
            return initStatus.EqualsIgnoreCase("invinitstatus_type_02");
        }

        /// <summary>
        /// 获取最近关账日期
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dtRefDate">参考日期</param>
        /// <returns>返回参考日期最近的一次关账日期</returns>
        public DateTime? GetLatestInventoryCloseDate(UserContext userCtx, DateTime? dtRefDate = null)
        {
            var svcBal = userCtx.Container.GetService<Core.Interface.StockUpdate.ICostCalulateService>();
            var dtLatestCloseDate = svcBal.GetNearClosePeriod(userCtx);

            return dtLatestCloseDate;
        }

        /// <summary>
        /// 获取最近关账日期(商品收发明细报表查询使用)
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dtRefDate">参考日期</param>
        /// <returns>返回参考日期最近的一次关账日期</returns>
        public DateTime? GetLatestInventoryCloseDateForReport(UserContext userCtx, DateTime? dtRefDate = null)
        {
            DateTime? dtLatestCloseDate = null;
            var strCheckSql = @"
select max(fclosedate) from t_stk_inventorybalance where fbalancetype='inventorybalance_type_02' and fmainorgid=@currentCompanyId
";

            var lstParams = new List<SqlParam>()
            {
                new SqlParam("currentCompanyId", System.Data.DbType.String, userCtx.Company)
            };

            if (dtRefDate.HasValue)
            {
                strCheckSql += "and fclosedate<=@refDate";
                lstParams.Add(new SqlParam("refDate", System.Data.DbType.DateTime, dtRefDate));
            }


            using (var reader = this.DBService.ExecuteReader(userCtx, strCheckSql, lstParams))
            {
                if (reader.Read())
                {
                    if (!(reader[0] is DBNull))
                    {
                        dtLatestCloseDate = Convert.ToDateTime(reader[0]);
                    }
                }
            }
            return dtLatestCloseDate;
        }

        /// <summary>
        /// 获取库存初始化日期
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns>返回库存初始化日期</returns>
        public DateTime? GetInitedInventoryDate(UserContext userCtx)
        {
            var systemProfileService = userCtx.Container.BeginLifetimeScope(Guid.NewGuid().ToString()).GetService<ISystemProfile>();
            var dynamicObj = systemProfileService.GetSystemParameter(userCtx, "stk_invcompleteinit");
            DateTime? dtStockInitDate = null;
            if (dynamicObj != null && !dynamicObj["finitdate"].IsNullOrEmpty())
            {
                dtStockInitDate = Convert.ToDateTime(dynamicObj["finitdate"]);
            }

            return dtStockInitDate;
        }

        /// <summary>
        /// 获取最小库存单据日期
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="includeInitStockBill"></param>
        /// <returns></returns>
        public DateTime? GetMinimumStockBillDate(UserContext userCtx, bool includeInitStockBill = false)
        {
            DateTime? dtMinimusStockDate = null;

            var sbSql = new StringBuilder();

            var lstAllStockBills = InventoryFlexModel.AllStockBillFormIds.ToList();
            if (includeInitStockBill)
            {
                lstAllStockBills.Add(InventoryFlexModel.InitStockBillFormId);
            }

            foreach (var stockFormId in lstAllStockBills)
            {
                var stockBillMeta = this.MetaModelService.LoadFormModel(userCtx, stockFormId);
                var bizDateField = stockBillMeta.GetField(stockBillMeta.BizDateFldKey);
                if (bizDateField == null) continue;
                if (sbSql.Length == 0)
                {
                    sbSql.AppendLine($"select min({bizDateField.FieldName}) as fbilldate from {stockBillMeta.BillHeadTableName} where fmainorgid=@currentCompanyId");
                }
                else
                {
                    sbSql.AppendLine($"union all");
                    sbSql.AppendLine($"select min({bizDateField.FieldName}) as fbilldate from {stockBillMeta.BillHeadTableName} where fmainorgid=@currentCompanyId");
                }
            }

            var strSql = $@"
select top 1 *
from (
{sbSql.ToString()}
) t
where t.fbilldate is not null 
order by t.fbilldate asc
";
            using (var reader = this.DBService.ExecuteReader(userCtx, strSql, new SqlParam("currentCompanyId", System.Data.DbType.String, userCtx.Company)))
            {
                if (reader.Read())
                {
                    if (!(reader[0] is DBNull))
                    {
                        dtMinimusStockDate = Convert.ToDateTime(reader[0]);
                    }
                }
            }
            return dtMinimusStockDate;
        }

        /// <summary>
        /// 校正库存数据
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        /// <remarks>
        /// 校正逻辑实现如下：
        /// 1、取得最近关账日期，允许为空
        /// 2、取得所有库存类单据（已审核、未作废且（业务日期>最近关账日期 或者 最近关账日期为空））
        /// 3、将所有库存类单据+最近库存余额（关账日期所在期间或初始库存余额）并集后按库存维度分组统计得到库存数据
        /// 4、将相关库存类单据生成库存更新快照
        /// 5、支持异步进度条算法
        /// </remarks>
        public IOperationResult CorrectInventoryData(UserContext userCtx, OperateOption option)
        {
            this.InitInventoryEnvironment(userCtx);

            var result = userCtx.Container.GetService<IOperationResult>();

            var dtCloseDate = this.GetLatestInventoryCloseDate(userCtx);

            //生成本次关账的交易编码
            var computeTranId = userCtx.Container.GetService<ISequenceService>().GetSequence<string>();

            this.TaskProgressService.SetTaskProgressValue(userCtx, option.GetTaskId(), 5);
            int iFlag = 1;

            var lstNeedCorrectStockFormIds = InventoryFlexModel.AllStockBillFormIds.ToList();

            //没有关账记录时，以初始库存单为准
            if (!dtCloseDate.HasValue)
            {
                lstNeedCorrectStockFormIds.Insert(0, InventoryFlexModel.InitStockBillFormId);
            }

            foreach (var stockBillFormId in lstNeedCorrectStockFormIds)
            {
                var stockFormMeta = this.MetaModelService.LoadFormModel(userCtx, stockBillFormId);

                this.TaskProgressService.SetTaskProgressMessage(userCtx, option.GetTaskId(), $"正在获取符合条件的库存数据……{stockFormMeta.Caption}");

                var filter = $"fdate>@closeDate and {stockFormMeta.BizStatusFldKey}='E' and {stockFormMeta.CancelStatusFldKey}='0' ";
                if (stockBillFormId.EqualsIgnoreCase("stk_inventorytransfer"))
                {
                    filter = $" ";//调拨单在回调里面设置
                }
                //生成初始库存查询对象（符合日志记录格式的）
                var initBillQueryObjs = this.BuildStockBillQueryObject(userCtx, stockBillFormId,
                    filter,
                    (updateSetting, sqlPara, lstSqlSelFldList) =>
                    {
                        if (sqlPara.HtmlForm.Id.EqualsIgnoreCase("stk_inventorytransfer") && updateSetting.UpdInvServiceId.EqualsIgnoreCase("from"))
                        {
                            sqlPara.FilterString = sqlPara.FilterString.JoinFilterString($"fdate>@closeDate and ({sqlPara.HtmlForm.BizStatusFldKey}='E' or ({sqlPara.HtmlForm.BizStatusFldKey}='D' and fisstockout='1' ) ) and {stockFormMeta.CancelStatusFldKey}='0' ");
                        }
                        else if (sqlPara.HtmlForm.Id.EqualsIgnoreCase("stk_inventorytransfer") && updateSetting.UpdInvServiceId.EqualsIgnoreCase("to"))
                        {
                            sqlPara.FilterString = sqlPara.FilterString.JoinFilterString($"fdate>@closeDate and {sqlPara.HtmlForm.BizStatusFldKey}='E' and {sqlPara.HtmlForm.CancelStatusFldKey}='0' ");
                        }
                        sqlPara.DynamicParams.Add(new SqlParam("closeDate", System.Data.DbType.DateTime, dtCloseDate.HasValue ? dtCloseDate : new DateTime(1900, 1, 1)));
                    });

                //将查询对象插入日志表，并生成本次计算识别码
                this.CreateOrAppendInvLogData(userCtx, initBillQueryObjs, computeTranId, "5", $"{userCtx.DisplayName ?? userCtx.UserName ?? userCtx.UserId}在{DateTime.Now}执行了库存校正操作！");

                //设置操作进度
                this.TaskProgressService.SetTaskProgressValue(userCtx, option.GetTaskId(), 5m + iFlag / InventoryFlexModel.AllStockBillFormIds.Count * 45);
                iFlag++;
            }



            this.TaskProgressService.SetTaskProgressMessage(userCtx, option.GetTaskId(), $"库存数据读取完成，正在生成即时库存数据……");
            this.TaskProgressService.SetTaskProgressValue(userCtx, option.GetTaskId(), 52);

            //生成即时库存表数据
            this.CreateInventoryData(userCtx, computeTranId, dtCloseDate, option);

            result.SimpleMessage = "库存校正完成！";
            return result;
        }












        /// <summary>
        /// 关账服务
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dtCloseDate"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        public IOperationResult CloseAccount(UserContext userCtx, DateTime dtCloseDate, OperateOption option)
        {
            var result = userCtx.Container.GetService<IOperationResult>();
            if (!this.IsInitedInventory(userCtx))
            {
                throw new BusinessException("库存还未结束初始化，不允许执行库存关账操作！");
            }
            if (dtCloseDate.Year * 100 + dtCloseDate.Month >= DateTime.Now.Year * 100 + DateTime.Now.Month)
            {
                throw new BusinessException("关账月份不能大于等于当前月份！");
            }

            dtCloseDate = (new DateTime(dtCloseDate.Year, dtCloseDate.Month, 1)).AddMonths(1).AddDays(-1);

            //获取最近关账日期
            var dtLatestCloseDate = this.GetLatestInventoryCloseDate(userCtx);
            if (dtLatestCloseDate.HasValue && dtLatestCloseDate.Value > dtCloseDate)
            {
                throw new BusinessException($"关账日期不得小于最近一次关账日期且不得小于系统结束初始化日期：{dtLatestCloseDate.Value}");
            }

            var systemProfileService = userCtx.Container.BeginLifetimeScope(Guid.NewGuid().ToString()).GetService<ISystemProfile>();
            var initDate = systemProfileService.GetSystemParameter<DateTime>(userCtx, "stk_invcompleteinit", "finitdate", DateTime.Now);
            var preCloseDate = dtLatestCloseDate.HasValue ? dtLatestCloseDate.Value : initDate;

            var lstParams = new List<SqlParam>()
            {
                new SqlParam("currentCompanyId", System.Data.DbType.String, userCtx.Company)
            };

            //检查库存单据的合法性
            this.CheckInventoryData(userCtx, InventoryFlexModel.AllStockBillFormIds, dtCloseDate, option);

            //获取库存管理参数：负库存允许库存关账
            var dynamicObj = systemProfileService.GetSystemParameter(userCtx, "stk_stockparam");
            bool allowinventoryclose = false;
            if (dynamicObj != null && !dynamicObj["fallowinventoryclose"].IsNullOrEmpty())
            {
                allowinventoryclose = Convert.ToBoolean(dynamicObj["fallowinventoryclose"]);
            }
            //检查关账期是否存在负库存
            if (!allowinventoryclose && !this.CheckNegativeInventoryData(userCtx, dtCloseDate, dtLatestCloseDate, preCloseDate, option)) return result;

            //生成本次关账的交易编码
            var computeTranId = userCtx.Container.GetService<ISequenceService>().GetSequence<string>();

            List<StockComputeQueryObject> lstAllQueryObjs = new List<StockComputeQueryObject>();

            foreach (var stockBillFormId in InventoryFlexModel.AllStockBillFormIds)
            {
                //生成初始库存查询对象（符合日志记录格式的）
                var initBillQueryObjs = this.BuildStockBillQueryObject(userCtx, stockBillFormId, "fdate<=@closeDateMax and fdate>@closeDateMin", (updateSetting, sqlPara, lstSqlSelFldList) =>
                {
                    sqlPara.DynamicParams.Add(new SqlParam("closeDateMax", System.Data.DbType.DateTime, dtCloseDate));
                    sqlPara.DynamicParams.Add(new SqlParam("closeDateMin", System.Data.DbType.DateTime, dtLatestCloseDate.HasValue ? dtLatestCloseDate : new DateTime(1900, 1, 1)));
                });
                lstAllQueryObjs.AddRange(initBillQueryObjs);
            }

            //将查询对象插入日志表，并生成本次计算识别码
            var opDescription = $"{userCtx.DisplayName ?? userCtx.UserName ?? userCtx.UserId}在{DateTime.Now}执行了库存关账操作！";//操作描述
            this.CreateOrAppendInvLogData(userCtx, lstAllQueryObjs, computeTranId, "10", opDescription);

            //先删除余额数据
            var strSql = $@"
                        delete t0 from t_stk_inventorybalance t0
                        where t0.fmainorgid=@currentCompanyId and t0.fbalancetype='inventorybalance_type_02' 
                            and t0.fclosedate>=@closeDate   
                        ";
            lstParams = new List<SqlParam>()
            {
                new SqlParam("currentCompanyId", System.Data.DbType.String, userCtx.Company),
                new SqlParam("closeDate", System.Data.DbType.DateTime, dtCloseDate),
            };
            var deleteCount = this.DBServiceEx.Execute(userCtx, strSql, lstParams);


            //统计汇总日志数据，进入库存余额表
            var invFlexModel = InventoryFlexModel.GetInstance(userCtx);

            StringBuilder sbBalanceInsert = new StringBuilder();
            sbBalanceInsert.Append("insert into t_stk_inventorybalance (fid,fformid,fclosedate,fbalancetype,fmainorgid,fcreatedate,fdescription");

            StringBuilder currPeriodBal = new StringBuilder();//当前期间的余额数据
            currPeriodBal.Append($@"select newid() as fid, 'stk_inventorybalance' as fformid, '{dtCloseDate.ToString("yyyy-MM-dd")}' as fclosedate, 'inventorybalance_type_02' as fbalancetype,fmainorgid
                                ,'{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}' as fcreatedate,'{opDescription.Replace("'", "''")}' as fdescription");

            StringBuilder prePeriodBal = new StringBuilder();//上期的期末数据
            prePeriodBal.Append($"select fmainorgid");

            StringBuilder currPeriodInOut = new StringBuilder();//本期的出入库数据
            currPeriodInOut.Append($"select fmainorgid");

            StringBuilder sbGroupBy = new StringBuilder();
            sbGroupBy.Append("fmainorgid");
            foreach (var flexItem in invFlexModel.InvFlexFieldKeys)
            {
                sbBalanceInsert.Append($",{flexItem}");
                currPeriodBal.Append($",{flexItem}");

                prePeriodBal.Append($",{flexItem}");
                currPeriodInOut.Append($",{flexItem}");
                sbGroupBy.Append($",{flexItem}");
            }

            sbBalanceInsert.AppendLine($",{invFlexModel.QtyFieldKey}");
            currPeriodBal.AppendLine($",sum({invFlexModel.QtyFieldKey}) as {invFlexModel.QtyFieldKey}");
            currPeriodInOut.AppendLine($",sum({invFlexModel.QtyFieldKey}*fupdatefactor) as {invFlexModel.QtyFieldKey}");
            prePeriodBal.AppendLine($",{invFlexModel.QtyFieldKey}");

            sbBalanceInsert.Append($",{invFlexModel.StockQtyFieldKey}");
            currPeriodBal.Append($",sum({invFlexModel.StockQtyFieldKey}) as {invFlexModel.StockQtyFieldKey}");
            currPeriodInOut.Append($",sum({invFlexModel.StockQtyFieldKey}*fupdatefactor) as {invFlexModel.StockQtyFieldKey}");
            prePeriodBal.Append($",{invFlexModel.StockQtyFieldKey}");

            sbBalanceInsert.Append($",{invFlexModel.AmountFieldKey}");
            currPeriodBal.Append($",sum({invFlexModel.AmountFieldKey}) as {invFlexModel.AmountFieldKey}");
            currPeriodInOut.Append($",sum({invFlexModel.AmountFieldKey}*fupdatefactor) as {invFlexModel.AmountFieldKey}");
            prePeriodBal.Append($",{invFlexModel.AmountFieldKey}");

            //上期的期末数，作为本期的期初数
            sbBalanceInsert.AppendLine($",fbeginqty");
            currPeriodBal.AppendLine($",sum(fbeginqty) as fbeginqty");
            currPeriodInOut.AppendLine($",0 as fbeginqty");
            prePeriodBal.AppendLine($",fqty as fbeginqty");

            sbBalanceInsert.Append($",fbeginstockqty");
            currPeriodBal.Append($",sum(fbeginstockqty) as fbeginstockqty");
            currPeriodInOut.Append($",0 as fbeginstockqty");
            prePeriodBal.Append($",fstockqty as fbeginstockqty");

            //本期入库数
            sbBalanceInsert.Append($",finqty");
            currPeriodBal.Append($",sum(finqty) as finqty");
            currPeriodInOut.Append($",sum(case when fupdatefactor=1 then fqty else 0 end) as finqty");
            prePeriodBal.Append($",0 as finqty");

            sbBalanceInsert.Append($",finstockqty");
            currPeriodBal.Append($",sum(finstockqty) as finstockqty");
            currPeriodInOut.Append($",sum(case when fupdatefactor=1 then fstockqty else 0 end)  as finstockqty");
            prePeriodBal.Append($",0 as finstockqty");

            //本期出库数
            sbBalanceInsert.Append($",foutqty");
            currPeriodBal.Append($",sum(foutqty) as foutqty");
            currPeriodInOut.Append($",sum(case when fupdatefactor=-1 then fqty else 0 end) as foutqty");
            prePeriodBal.Append($",0 as finqty");

            sbBalanceInsert.Append($",foutstockqty");
            currPeriodBal.Append($",sum(foutstockqty) as foutstockqty");
            currPeriodInOut.Append($",sum(case when fupdatefactor=-1 then fstockqty else 0 end)  as foutstockqty");
            prePeriodBal.Append($",0 as finstockqty");

            //上期的期末数，作为本期的期初数
            sbBalanceInsert.Append($",finicostprice");
            currPeriodBal.Append($",sum(finicostprice) as finicostprice");
            currPeriodInOut.Append($",0 as finicostprice");
            prePeriodBal.Append($",fcostprice as finicostprice");

            sbBalanceInsert.Append($",finicostamt");
            currPeriodBal.Append($",sum(finicostamt) as finicostamt");
            currPeriodInOut.Append($",0 as finicostamt");
            prePeriodBal.Append($",fcostamt as finicostamt");


            sbBalanceInsert.AppendLine(")");
            currPeriodInOut.AppendLine()
                .AppendLine($"from t_stk_invcomputelog with(nolock) ")
                .AppendLine($"where ftranid=@tranid")
                .AppendLine($"group by {sbGroupBy.ToString()}");

            prePeriodBal.AppendLine()
                .AppendLine($"from t_stk_inventorybalance with(nolock) ")
                .Append($"where fmainorgid=@currentCompanyId and fclosedate=@preCloseDate ");
            if (dtLatestCloseDate.HasValue)
            {
                //最近一期关账余额
                prePeriodBal.AppendLine($" and fbalancetype='inventorybalance_type_02'");
            }
            else
            {
                //初始库存余额
                prePeriodBal.AppendLine($" and fbalancetype='inventorybalance_type_01'");
            }

            lstParams = new List<SqlParam>()
            {
                new SqlParam("tranid", System.Data.DbType.String, computeTranId),
                new SqlParam("currentCompanyId", System.Data.DbType.String, userCtx.Company),
                new SqlParam("preCloseDate", System.Data.DbType.DateTime, preCloseDate),
            };


            strSql = $@" {sbBalanceInsert}
                        {currPeriodBal.ToString()} 
                        from (
                                {currPeriodInOut.ToString()}
                                union all
                                {prePeriodBal.ToString()}
                                ) u1
                        group by {sbGroupBy.ToString()}";

            var recordCount = this.DBServiceEx.Execute(userCtx, strSql, lstParams);


            //在系统参数表里面记录最新关账期间 
            var svcBal = userCtx.Container.GetService<Core.Interface.StockUpdate.ICostCalulateService>();
            svcBal.AddOrUpdateClosePeriod(userCtx, dtCloseDate);

            result.ComplexMessage.SuccessMessages.Add($"库存关账成功，共生成{recordCount - deleteCount}条库存余额记录！");

            return result;
        }

        /// <summary>
        /// 库存结束初始化服务
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        /// <remarks>
        /// 实现逻辑描述：
        /// 1、删除初始库存余额表数据
        /// 2、将已审核的初始库存单据按库存维度进行分类汇总得到初始库存余额数据并插入库存余额表
        /// 3、不处理即时库存（初始库存审核时，生成是啥样就啥），若结束初始化或者人工调整后，觉得即时库存不准，可以通过库存校正功能统一处理
        /// </remarks>
        public IOperationResult CreateInitInventoryBalance(UserContext userCtx, OperateOption option)
        {
            this.InitInventoryEnvironment(userCtx);

            var result = userCtx.Container.GetService<IOperationResult>();
            //生成初始库存查询对象（符合日志记录格式的）
            var initBillQueryObjs = this.BuildStockBillQueryObject(userCtx, InventoryFlexModel.InitStockBillFormId, "fstatus='E'");

            //将查询对象插入日志表，并生成本次计算识别码
            var computeTranId = userCtx.Container.GetService<ISequenceService>().GetSequence<string>();
            var opDescription = $"{userCtx.DisplayName ?? userCtx.UserName ?? userCtx.UserId}在{DateTime.Now}执行了库存结束初始化操作！";//操作描述
            this.CreateOrAppendInvLogData(userCtx, initBillQueryObjs, computeTranId, "0", opDescription);

            //统计汇总日志数据，进入库存余额表
            var invFlexModel = InventoryFlexModel.GetInstance(userCtx);

            StringBuilder sbBalanceInsert = new StringBuilder();
            sbBalanceInsert.Append("insert into t_stk_inventorybalance (fid,fformid,fmainorgid,fclosedate,fbalancetype,fcreatedate,fdescription");

            //取得最早的初始库存单据日期
            DateTime? dtMinInitStockDate = this.GetMinimumStockBillDate(userCtx, true);
            if (dtMinInitStockDate.HasValue == false)
            {
                dtMinInitStockDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            }
            StringBuilder sbSelectSql = new StringBuilder();
            sbSelectSql.Append($@"select newid() as fid,'stk_inventorybalance' as fformid,fmainorgid, '{dtMinInitStockDate.Value.ToString("yyyy-MM-dd")}' as fclosedate, 'inventorybalance_type_01' as fbalancetype
                                ,'{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}' as fcreatedate,'{opDescription.Replace("'", "''")}' as fdescription");

            StringBuilder sbGroupBy = new StringBuilder();
            sbGroupBy.Append("fmainorgid");
            foreach (var flexItem in invFlexModel.InvFlexFieldKeys)
            {
                sbBalanceInsert.Append($",{flexItem}");
                sbSelectSql.Append($",{flexItem}");
                sbGroupBy.Append($",{flexItem}");
            }

            sbBalanceInsert.Append($",{invFlexModel.QtyFieldKey}");
            sbSelectSql.Append($",sum({invFlexModel.QtyFieldKey}*fupdatefactor) as {invFlexModel.QtyFieldKey}");

            sbBalanceInsert.Append($",{invFlexModel.StockQtyFieldKey}");
            sbSelectSql.Append($",sum({invFlexModel.StockQtyFieldKey}*fupdatefactor) as {invFlexModel.StockQtyFieldKey}");

            sbBalanceInsert.Append($",{invFlexModel.AmountFieldKey}");
            sbSelectSql.Append($",sum({invFlexModel.AmountFieldKey}*fupdatefactor) as {invFlexModel.AmountFieldKey}");

            sbBalanceInsert.AppendLine(")");
            sbSelectSql.AppendLine()
                .AppendLine($"from t_stk_invcomputelog")
                .AppendLine($"where ftranid=@tranid")
                .AppendLine($"group by {sbGroupBy.ToString()}");
            var strSql = $"{sbBalanceInsert.ToString()} \r\n {sbSelectSql.ToString()}";
            var recordCount = this.DBServiceEx.Execute(userCtx, strSql, new SqlParam("tranid", System.Data.DbType.String, computeTranId));
            result.ComplexMessage.SuccessMessages.Add($"库存结束初始化成功，共生成{recordCount}条库存余额记录！");
            result.SrvData = dtMinInitStockDate;
            return result;
        }

        /// <summary>
        /// 删除初始化库存余额
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        public IOperationResult DeleteInitInventoryBalance(UserContext userCtx, OperateOption option)
        {
            var result = userCtx.Container.GetService<IOperationResult>();
            //删除库存余额
            var strSql = $@"
                        delete from t_stk_inventorybalance where fmainorgid=@currentCompanyId
                        ";
            var recordCount = this.DBServiceEx.Execute(userCtx, strSql, new SqlParam("currentCompanyId", System.Data.DbType.String, userCtx.Company));
            result.ComplexMessage.SuccessMessages.Add($"库存重新初始化成功，共删除{recordCount}条库存余额记录！");
            return result;
        }
        /// <summary>
        /// 反关账服务
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        public IOperationResult UncloseAccount(UserContext userCtx, OperateOption option)
        {
            var result = userCtx.Container.GetService<IOperationResult>();
            //只能反关账最近一期的余额数据
            DateTime? dtLatestCloseDate = this.GetLatestInventoryCloseDate(userCtx);

            if (!dtLatestCloseDate.HasValue)
            {
                throw new BusinessException("系统已没有关账记录，无法进行反关账，若要重新初始化系统，请在初始化控制中进行重新初始化！");
            }

            var strSql = $@"
                            delete from t_stk_inventorybalance 
                            where fbalancetype='inventorybalance_type_02' and fmainorgid=@currentCompanyId and fclosedate >= @closeDate
                            ";
            var lstParams = new List<SqlParam>()
            {
                new SqlParam("currentCompanyId", System.Data.DbType.String, userCtx.Company),
                new SqlParam("closeDate", System.Data.DbType.DateTime, dtLatestCloseDate.Value)
            };
            var recordCount = this.DBServiceEx.Execute(userCtx, strSql, lstParams);

            //删除本期数据后，查上次关账日期并记录关账日期
            var svcBal = userCtx.Container.GetService<Core.Interface.StockUpdate.ICostCalulateService>();
            var strCheckSql = @"
                                select max(fclosedate) from t_stk_inventorybalance 
                                where fbalancetype='inventorybalance_type_02' and fmainorgid='{0}' and fclosedate<'{1}'
                                ".Fmt(userCtx.Company, dtLatestCloseDate.Value);
            var balData = this.DBService.ExecuteDynamicObject(userCtx, strCheckSql);
            if (balData != null && balData.Count > 0)
            {
                dtLatestCloseDate = Convert.ToDateTime(balData[0][0]);
                svcBal.AddOrUpdateClosePeriod(userCtx, dtLatestCloseDate);
            }
            else
            {
                svcBal.AddOrUpdateClosePeriod(userCtx, null);
            }
            //svcBal.AddOrUpdateCostCalcPeriod(userCtx, null);

            result.ComplexMessage.SuccessMessages.Add($"库存反关账成功，共删除{recordCount}条库存余额记录！");
            return result;
        }


        /// <summary>
        /// 检查库存单据的数据合法性
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="allStockBillFormIds"></param>
        /// <param name="dtCloseDate"></param>
        /// <param name="option"></param>
        private void CheckInventoryData(UserContext userCtx, List<string> allStockBillFormIds, DateTime dtCloseDate, OperateOption option)
        {
            var result = userCtx.Container.GetService<IOperationResult>();

            StringBuilder sbCheckBillStatusSql = new StringBuilder();
            List<SqlParam> lstPara = new List<SqlParam>()
            {
                new SqlParam("closeDate", System.Data.DbType.DateTime, dtCloseDate)
            };
            var dctFormMetaCache = new Dictionary<string, HtmlForm>();
            foreach (var stockBillFormId in allStockBillFormIds)
            {
                if (sbCheckBillStatusSql.Length > 0)
                {
                    sbCheckBillStatusSql.AppendLine(" union all ");
                }
                var formMeta = this.MetaModelService.LoadFormModel(userCtx, stockBillFormId);
                dctFormMetaCache[formMeta.Id] = formMeta;
                var numberField = formMeta.GetNumberField();
                SqlBuilderParameter sqlCheckPara = new SqlBuilderParameter(userCtx, formMeta);
                sqlCheckPara.SelectedFieldKeys.AddRange(new string[]
                {
                    $"'{stockBillFormId}' as fformid",
                    $"{numberField.Id} as fbillno",
                });
                sqlCheckPara.PageCount = -1;
                sqlCheckPara.PageIndex = -1;
                sqlCheckPara.QueryUserFieldOnly = true;
                sqlCheckPara.NoIsolation = false;
                sqlCheckPara.MergeBillHeadField = false;
                sqlCheckPara.NoColorSetting = true;
                sqlCheckPara.IsDistinct = true;
                sqlCheckPara.FilterString = $"{formMeta.BizStatusFldKey}<>'E' and ({formMeta.CancelStatusFldKey}='0' or {formMeta.CancelStatusFldKey}='') and fdate<=@closeDate";

                QueryObject checkQueryObj = QueryService.BuilQueryObject(sqlCheckPara);
                sbCheckBillStatusSql.AppendLine(checkQueryObj.SqlNoPage);
                foreach (var sqlPara in sqlCheckPara.DynamicParams)
                {
                    var isExistPara = lstPara.FirstOrDefault(o => o.Name.EqualsIgnoreCase(sqlPara.Name)) != null;
                    if (!isExistPara)
                    {
                        lstPara.Add(sqlPara);
                    }
                }
            }

            if (sbCheckBillStatusSql.Length > 0)
            {
                using (var reader = this.DBService.ExecuteReader(userCtx, sbCheckBillStatusSql.ToString().Replace("Order By  t0.fbillno  Desc", ""), lstPara))
                {
                    while (reader.Read())
                    {
                        HtmlForm hForm;

                        var formId = reader["fformid"] as string;
                        var billNo = reader["fbillno"] as string;

                        dctFormMetaCache.TryGetValue(formId, out hForm);

                        result.ComplexMessage.ErrorMessages.Add($"编号为（{billNo}）的【{hForm.Caption}】未审核，不能进行关账操作！");
                    }
                }
            }

            if (result.ComplexMessage.ErrorMessages != null && result.ComplexMessage.ErrorMessages.Count > 0)
            {
                throw new BusinessException("库存关账失败，{0}存在未审核的库存单据，请处理后再进行关账！".Fmt(dtCloseDate.ToString("yyyy年MM月份")));
            }
        }

        /// <summary>
        /// 检查关账期是否存在负库存
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dtCloseDate"></param>
        /// <param name="option"></param>
        /// <param name="dtLatestCloseDate"></param>
        /// <param name="preCloseDate"></param>
        private bool CheckNegativeInventoryData(UserContext userCtx, DateTime dtCloseDate, DateTime? dtLatestCloseDate, DateTime preCloseDate, OperateOption option)
        {
            //统计汇总日志数据，进入库存余额表
            var invFlexModel = InventoryFlexModel.GetInstance(userCtx);
            StringBuilder sbGroupBy = new StringBuilder();
            sbGroupBy.Append("fmainorgid");
            foreach (var flexItem in invFlexModel.InvFlexFieldKeys)
            {
                sbGroupBy.Append($",{flexItem}");
            }

            StringBuilder sbCurrPeriodInOut = new StringBuilder();//本期出入库库存统计
            sbCurrPeriodInOut.AppendLine(@"Select A.{0},SUM(fstockqty) AS fstockqty From v_stk_stockbillinfo AS A WITH(NOLOCK)  
                                           LEFT JOIN t_bd_material AS M WITH(NOLOCK) ON A.fmaterialid=M.fid ".Fmt(sbGroupBy.ToString().Replace(",", ",A.")));
            sbCurrPeriodInOut.AppendLine(@"Where A.fmainorgid=@fmainorgid AND A.fdate<=@closeDateMax AND A.fdate>@closeDateMin AND M.fsuiteflag='0' ");
            sbCurrPeriodInOut.AppendLine(@"Group By A.{0}".Fmt(sbGroupBy.ToString().Replace(",", ",A.")));

            StringBuilder sbPrePeriodBal = new StringBuilder();//上期的期末数据
            sbPrePeriodBal.AppendLine(@"Select A.{0},fstockqty From t_stk_inventorybalance AS A WITH(NOLOCK)  
                                        LEFT JOIN t_bd_material AS M WITH(NOLOCK) ON A.fmaterialid=M.fid ".Fmt(sbGroupBy.ToString().Replace(",", ",A.")));
            sbPrePeriodBal.Append(@"Where A.fmainorgid=@fmainorgid AND A.fclosedate=@preCloseDate ");
            if (dtLatestCloseDate.HasValue)
            {
                //最近一期关账余额
                sbPrePeriodBal.Append(@" AND A.fbalancetype='inventorybalance_type_02'");
            }
            else
            {
                //初始库存余额
                sbPrePeriodBal.Append(@" AND A.fbalancetype='inventorybalance_type_01'");
            }
            sbPrePeriodBal.AppendLine(@" AND M.fsuiteflag='0' ");

            StringBuilder sbNegativeInventorySql = new StringBuilder();
            sbNegativeInventorySql.AppendLine(@"Select {0},SUM(fstockqty) AS fstockqty From ( ".Fmt(sbGroupBy.ToString()));
            sbNegativeInventorySql.Append(sbCurrPeriodInOut.ToString());
            sbNegativeInventorySql.AppendLine(@"Union All ");
            sbNegativeInventorySql.Append(sbPrePeriodBal.ToString());
            sbNegativeInventorySql.AppendLine(@") AS Temp ");
            sbNegativeInventorySql.AppendLine(@"Group By {0} ".Fmt(sbGroupBy.ToString()));
            sbNegativeInventorySql.AppendLine(@"HAVING SUM(fstockqty)<0 ");

            List<SqlParam> lstPara = new List<SqlParam>()
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("closeDateMax", System.Data.DbType.DateTime, dtCloseDate.DayEnd()),
                new SqlParam("closeDateMin", System.Data.DbType.DateTime, dtLatestCloseDate.HasValue ? dtLatestCloseDate.Value.DayEnd() : new DateTime(1900, 1, 1)),
                new SqlParam("preCloseDate", System.Data.DbType.DateTime, preCloseDate)
            };

            bool hasNegativeInventory = false;

            if (sbNegativeInventorySql.Length > 0)
            {
                using (var reader = this.DBService.ExecuteReader(userCtx, sbNegativeInventorySql.ToString(), lstPara))
                {
                    while (reader.Read())
                    {
                        hasNegativeInventory = true;
                        break;

                        //暂时不需要展示明细，有需要时放开下面的代码，商品名称需要关联查询得到
                        //var fmaterialid = reader["fmaterialid"] as string;
                        //var fmaterialname = "商品名";
                        //result.ComplexMessage.ErrorMessages.Add(@"{0}中商品【{1}】库存为负，不能进行关账操作！".Fmt(dtCloseDate.ToString("yyyy年MM月份"), fmaterialname));
                    }
                }
            }

            if (hasNegativeInventory)
            {
                //throw new BusinessException("库存关账失败，存在负库存，请到【即时库存】查看，处理后再进行关账！");//通过异常抛出会回滚之前关闭成功的账期记录
                var result = userCtx.Container.GetService<IOperationResult>();
                result.Title = "负库存提示";
                result.ComplexMessage.SuccessMessages.Clear();//清理之前成功的信息，避免误导用户
                result.SimpleMessage = "{0}库存关账失败，存在负库存，请到【商品收发汇总表】查看，处理后再关账！".Fmt(dtCloseDate.ToString("yyyy年MM月份"));
                result.MsgStyle = (int)Enu_MsgStyle.Dialog;
                result.IsSuccess = false;
                return false;
            }

            return true;
        }

        /// <summary>
        /// 读取某个表单模型中配置的库存更新服务
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formId"></param>
        /// <param name="opCode"></param>
        /// <returns></returns>
        public IEnumerable<StockUpdateSetting> GetStockUpdateSettingByFormId(UserContext userCtx, string formId, string opCode = "audit")
        {
            var formMeta = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, formId);
            if (formMeta == null) return new StockUpdateSetting[] { };

            var htmlOperation = formMeta.FormOperations.FirstOrDefault(o => o.Id.EqualsIgnoreCase(opCode));
            if (htmlOperation == null) return new StockUpdateSetting[] { };

            //库存更新服务通常挂在审核上，所以直接查审核操作上关联的库存服务
            var allUpdateInvService = htmlOperation.OperationServices.Where(o => o.ServiceId == YDJHtmlElementType.HtmlBizService_UpdateInventory);

            List<StockUpdateSetting> lstUpdSettings = new List<StockUpdateSetting>();
            foreach (var updService in allUpdateInvService)
            {
                var stockUpdSetting = updService.Parameter.FromJson<StockUpdateSetting>(true);
                if (stockUpdSetting != null)
                {
                    lstUpdSettings.Add(stockUpdSetting);
                }
            }
            return lstUpdSettings;
        }

        /// <summary>
        /// 创建或追加数据至计算日志表
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="invLogQueryObjs"></param>
        /// <param name="computeTranId"></param>
        /// <param name="dataType">日志数据类型</param>
        /// <param name="dataDesc">日志数据描述</param>
        /// <param name="invLogTable">日志表名称</param>
        /// <returns></returns>
        public string CreateOrAppendInvLogData(UserContext userCtx, IEnumerable<StockComputeQueryObject> invLogQueryObjs, string computeTranId, string dataType, string dataDesc = "", string invLogTable = "")
        {
            if (invLogQueryObjs.Any() == false) return computeTranId;

            if (computeTranId.IsNullOrEmptyOrWhiteSpace())
            {
                computeTranId = userCtx.Container.GetService<ISequenceService>().GetSequence<string>();
            }
            if (invLogTable.IsNullOrEmptyOrWhiteSpace())
            {
                var invLogMeta = this.MetaModelService.LoadFormModel(userCtx, "stk_invcomputelog");
                invLogTable = invLogMeta.BillHeadTableName;
            }
            long lCount = 0;
            foreach (var queryObj in invLogQueryObjs)
            {
                string insertsql = $"insert into {invLogTable} ({string.Join(",", queryObj.SqlSelectFieldList)},ftranid,fcomputetype,fdescription)";
                queryObj.QueryObject.SqlSelect += $",'{computeTranId}' as ftranid";
                queryObj.QueryObject.SqlSelect += $",'{dataType.Replace("'", "''")}' as fcomputetype";
                queryObj.QueryObject.SqlSelect += $",'{dataDesc.Replace("'", "''")}' as fdescription";
                var strInsertSql = $"{insertsql} \r\n {queryObj.QueryObject.SqlNoPage}";
                lCount += this.DBServiceEx.Execute(userCtx, strInsertSql, queryObj.SqlBuilderParameter.DynamicParams);
            }
            return computeTranId;
        }


        /// <summary>
        /// 创建或追加数据至计算日志表
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="invLogQueryObjs"></param>
        /// <param name="computeTranId"></param>
        /// <param name="dataType">日志数据类型</param>
        /// <param name="dataDesc">日志数据描述</param>
        /// <param name="invLogTable">日志表名称</param>
        /// <returns></returns>
        public string CreateOrAppendInvLogData_new1(UserContext userCtx, IEnumerable<StockComputeQueryObject> invLogQueryObjs, string computeTranId, string dataType, string dataDesc = "", string invLogTable = "")
        {
            if (invLogQueryObjs.Any() == false) return computeTranId;

            if (computeTranId.IsNullOrEmptyOrWhiteSpace())
            {
                computeTranId = userCtx.Container.GetService<ISequenceService>().GetSequence<string>();
            }
            if (invLogTable.IsNullOrEmptyOrWhiteSpace())
            {
                var invLogMeta = this.MetaModelService.LoadFormModel(userCtx, "stk_invcomputelog");
                invLogTable = invLogMeta.BillHeadTableName;
            }
            //StringBuilder sbInsertSql = new StringBuilder();
            //sbInsertSql.Append($"insert into {invLogTable} ({string.Join(",", invLogQueryObjs.First().SqlSelectFieldList)}");
            //sbInsertSql.AppendLine(",ftranid,fcomputetype,fdescription)");
            var sqlTasks = new List<Action>();

            var loger = userCtx.Container.GetService<ILogService>();

            StringBuilder sb = new StringBuilder();
            Stopwatch sw = Stopwatch.StartNew();
            sw.Start();
            long lCount = 0;

            var calOp = new ParallelOptions() { MaxDegreeOfParallelism = 2 };
            // 遍历 orgGrp，将 SQL 操作封装为任务并添加到集合中
            foreach (var queryObj in invLogQueryObjs)
            {
                sqlTasks.Add(() =>
                {
                    string insertsql = $"insert into {invLogTable} ({string.Join(",", queryObj.SqlSelectFieldList)},ftranid,fcomputetype,fdescription)";
                    queryObj.QueryObject.SqlSelect += $",'{computeTranId}' as ftranid";
                    queryObj.QueryObject.SqlSelect += $",'{dataType.Replace("'", "''")}' as fcomputetype";
                    queryObj.QueryObject.SqlSelect += $",'{dataDesc.Replace("'", "''")}' as fdescription";
                    var strInsertSql = $"{insertsql} \r\n {queryObj.QueryObject.SqlNoPage}";
                    var dbService = userCtx.Container.GetService<IDBServiceEx>();
                    Thread.Sleep(100);
                    lCount += dbService.Execute(userCtx, strInsertSql, queryObj.SqlBuilderParameter.DynamicParams);
                    sb.AppendLine("SQL:" + strInsertSql + "；ElapsedMilliseconds：" + sw.ElapsedMilliseconds);
                    // 在这里执行与 org 相关的 SQL 操作
                    // 示例：调用 DBServiceEx 执行 SQL
                    //string sql = BuildSqlForOrg(org.Item1, org.Item2); // 假设 BuildSqlForOrg 是生成 SQL 的方法
                    //this.DBServiceEx.ExecuteNonQuery(userCtx, sql);
                });
            }
            int batchSize = 4;
            for (int i = 0; i < sqlTasks.Count; i += batchSize)
            {
                var batch = sqlTasks.Skip(i).Take(batchSize).ToList();


                // 使用 Parallel.ForEach 并行执行 SQL 操作
                Parallel.ForEach(batch, calOp, task =>
                {
                    try
                    {
                        task(); // 执行 SQL 操作
                    }
                    catch (Exception ex)
                    {
                        sb.AppendLine("任务执行失败:" + ex.Message + "；ElapsedMilliseconds：" + sw.ElapsedMilliseconds);
                        // 记录异常日志
                        Console.WriteLine($"任务执行失败：{ex.Message}");
                    }
                });
            }
            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【CreateOrAppendInvLogData_new】，内容:{4}".Fmt(userCtx.UserName,
                   userCtx.UserPhone, userCtx.Company,
                 DateTime.Now.ToString("HH:mm:ss"), sb.ToString()),
                "stockDetail");

            //List<string> sqls = new List<string>();
            //long lCount = 0;
            //foreach (var queryObj in invLogQueryObjs)
            //{
            //    string insertsql = $"insert into {invLogTable} ({string.Join(",", queryObj.SqlSelectFieldList)},ftranid,fcomputetype,fdescription)";
            //    queryObj.QueryObject.SqlSelect += $",'{computeTranId}' as ftranid";
            //    queryObj.QueryObject.SqlSelect += $",'{dataType.Replace("'", "''")}' as fcomputetype";
            //    queryObj.QueryObject.SqlSelect += $",'{dataDesc.Replace("'", "''")}' as fdescription";
            //    var strInsertSql = $"{insertsql} \r\n {queryObj.QueryObject.SqlNoPage}";
            //    lCount += this.DBServiceEx.Execute(userCtx, strInsertSql, queryObj.SqlBuilderParameter.DynamicParams);
            //}

            //List<Tuple<int, List<string>>> orgGrp = new List<Tuple<int, List<string>>>();
            //orgGrp.Add(Tuple.Create(0, sqls));

            //Parallel.ForEach<Tuple<int, List<string>>>(orgGrp, calOp,
            //                            SqlItem =>
            //                            {
            //                            }
            //                        );


            return computeTranId;
        }



        /// <summary>
        /// 创建或追加数据至计算日志表
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="invLogQueryObjs"></param>
        /// <param name="computeTranId"></param>
        /// <param name="dataType">日志数据类型</param>
        /// <param name="dataDesc">日志数据描述</param>
        /// <param name="invLogTable">日志表名称</param>
        /// <returns></returns>
        public string CreateOrAppendInvLogData_new(UserContext userCtx, IEnumerable<StockComputeQueryObject> invLogQueryObjs, string computeTranId, string dataType, string dataDesc = "", string invLogTable = "")
        {
            if (invLogQueryObjs.Any() == false) return computeTranId;

            if (computeTranId.IsNullOrEmptyOrWhiteSpace())
            {
                computeTranId = userCtx.Container.GetService<ISequenceService>().GetSequence<string>();
            }
            if (invLogTable.IsNullOrEmptyOrWhiteSpace())
            {
                var invLogMeta = this.MetaModelService.LoadFormModel(userCtx, "stk_invcomputelog");
                invLogTable = invLogMeta.BillHeadTableName;
            }
            //StringBuilder sbInsertSql = new StringBuilder();
            //sbInsertSql.Append($"insert into {invLogTable} ({string.Join(",", invLogQueryObjs.First().SqlSelectFieldList)}");
            //sbInsertSql.AppendLine(",ftranid,fcomputetype,fdescription)");
            var sqlTasks = new List<Action>();

            var loger = userCtx.Container.GetService<ILogService>();

            StringBuilder sb = new StringBuilder();
            Stopwatch sw = Stopwatch.StartNew();
            sw.Start();
            long lCount = 0;
            List<KeyValuePair<string, IEnumerable<SqlParam>>> keyValuePairs = new List<KeyValuePair<string, IEnumerable<SqlParam>>>();
            var calOp = new ParallelOptions() { MaxDegreeOfParallelism = 2 };
            // 遍历 orgGrp，将 SQL 操作封装为任务并添加到集合中
            foreach (var queryObj in invLogQueryObjs)
            {
                string insertsql = $"insert into {invLogTable} ({string.Join(",", queryObj.SqlSelectFieldList)},ftranid,fcomputetype,fdescription)";
                queryObj.QueryObject.SqlSelect += $",'{computeTranId}' as ftranid";
                queryObj.QueryObject.SqlSelect += $",'{dataType.Replace("'", "''")}' as fcomputetype";
                queryObj.QueryObject.SqlSelect += $",'{dataDesc.Replace("'", "''")}' as fdescription";
                var strInsertSql = $"{insertsql} \r\n {queryObj.QueryObject.SqlNoPage}";
                //keyValues(strInsertSql, queryObj.SqlBuilderParameter.DynamicParams);
                KeyValuePair<string, IEnumerable<SqlParam>> keyValues = new KeyValuePair<string, IEnumerable<SqlParam>>(strInsertSql, queryObj.SqlBuilderParameter.DynamicParams);
                keyValuePairs.Add(keyValues);
                //var dbService = userCtx.Container.GetService<IDBServiceEx>();
                //Thread.Sleep(100);
                //lCount += dbService.Execute(userCtx, strInsertSql, queryObj.SqlBuilderParameter.DynamicParams);
                //sb.AppendLine("SQL:" + strInsertSql + "；ElapsedMilliseconds：" + sw.ElapsedMilliseconds);
                // 在这里执行与 org 相关的 SQL 操作
                // 示例：调用 DBServiceEx 执行 SQL
                //string sql = BuildSqlForOrg(org.Item1, org.Item2); // 假设 BuildSqlForOrg 是生成 SQL 的方法
                //this.DBServiceEx.ExecuteNonQuery(userCtx, sql);
            }
            //foreach (var queryObj in invLogQueryObjs)
            //{
            //    sqlTasks.Add(() =>
            //    {
            //        string insertsql = $"insert into {invLogTable} ({string.Join(",", queryObj.SqlSelectFieldList)},ftranid,fcomputetype,fdescription)";
            //        queryObj.QueryObject.SqlSelect += $",'{computeTranId}' as ftranid";
            //        queryObj.QueryObject.SqlSelect += $",'{dataType.Replace("'", "''")}' as fcomputetype";
            //        queryObj.QueryObject.SqlSelect += $",'{dataDesc.Replace("'", "''")}' as fdescription";
            //        var strInsertSql = $"{insertsql} \r\n {queryObj.QueryObject.SqlNoPage}";
            //        var dbService = userCtx.Container.GetService<IDBServiceEx>();
            //        Thread.Sleep(100);
            //        lCount += dbService.Execute(userCtx, strInsertSql, queryObj.SqlBuilderParameter.DynamicParams);
            //        sb.AppendLine("SQL:" + strInsertSql + "；ElapsedMilliseconds：" + sw.ElapsedMilliseconds);
            //        // 在这里执行与 org 相关的 SQL 操作
            //        // 示例：调用 DBServiceEx 执行 SQL
            //        //string sql = BuildSqlForOrg(org.Item1, org.Item2); // 假设 BuildSqlForOrg 是生成 SQL 的方法
            //        //this.DBServiceEx.ExecuteNonQuery(userCtx, sql);
            //    });
            //}
            //int batchSize = 4;
            //for (int i = 0; i < sqlTasks.Count; i += batchSize)
            //{
            //    var batch = sqlTasks.Skip(i).Take(batchSize).ToList();


            //    // 使用 Parallel.ForEach 并行执行 SQL 操作
            //    Parallel.ForEach(batch, calOp, task =>
            //    {
            //        try
            //        {
            //            task(); // 执行 SQL 操作
            //        }
            //        catch (Exception ex)
            //        {
            //            sb.AppendLine("任务执行失败:" + ex.Message + "；ElapsedMilliseconds：" + sw.ElapsedMilliseconds);
            //            // 记录异常日志
            //            Console.WriteLine($"任务执行失败：{ex.Message}");
            //        }
            //    });
            //}

            //sb.AppendLine("SQL:" + task.Key + "；ElapsedMilliseconds：" + sw.ElapsedMilliseconds);
            //Thread.Sleep(100);
            lCount += this.DBServiceEx.ExecuteBatch(userCtx, keyValuePairs);
            //// 使用 Parallel.ForEach 并行执行 SQL 操作
            //Parallel.ForEach(keyValues, calOp, task =>
            //{
            //    try
            //    {
            //        sb.AppendLine("SQL:" + task.Key + "；ElapsedMilliseconds：" + sw.ElapsedMilliseconds);
            //        Thread.Sleep(100);
            //        lCount += this.DBServiceEx.ExecuteBatch(userCtx, new List<KeyValuePair<string, List<SqlParam>>>() { keyValues });
            //    }
            //    catch (Exception ex)
            //    {
            //        sb.AppendLine("任务执行失败:" + ex.Message + "；ElapsedMilliseconds：" + sw.ElapsedMilliseconds);
            //        // 记录异常日志
            //        Console.WriteLine($"任务执行失败：{ex.Message}");
            //    }
            //});

            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【CreateOrAppendInvLogData_new】，内容:{4}".Fmt(userCtx.UserName,
                   userCtx.UserPhone, userCtx.Company,
                 DateTime.Now.ToString("HH:mm:ss"), sb.ToString()),
                "stockDetail");

            //List<string> sqls = new List<string>();
            //long lCount = 0;
            //foreach (var queryObj in invLogQueryObjs)
            //{
            //    string insertsql = $"insert into {invLogTable} ({string.Join(",", queryObj.SqlSelectFieldList)},ftranid,fcomputetype,fdescription)";
            //    queryObj.QueryObject.SqlSelect += $",'{computeTranId}' as ftranid";
            //    queryObj.QueryObject.SqlSelect += $",'{dataType.Replace("'", "''")}' as fcomputetype";
            //    queryObj.QueryObject.SqlSelect += $",'{dataDesc.Replace("'", "''")}' as fdescription";
            //    var strInsertSql = $"{insertsql} \r\n {queryObj.QueryObject.SqlNoPage}";
            //    lCount += this.DBServiceEx.Execute(userCtx, strInsertSql, queryObj.SqlBuilderParameter.DynamicParams);
            //}

            //List<Tuple<int, List<string>>> orgGrp = new List<Tuple<int, List<string>>>();
            //orgGrp.Add(Tuple.Create(0, sqls));

            //Parallel.ForEach<Tuple<int, List<string>>>(orgGrp, calOp,
            //                            SqlItem =>
            //                            {
            //                            }
            //                        );


            return computeTranId;
        }




        /// <summary>
        /// 构建从库存单据中抽取符合特定表格式的列表查询对象
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formId"></param>
        /// <param name="filterString"></param>
        /// <param name="queryCallback">自定义查询过虑</param>
        /// <returns></returns>
        public IEnumerable<StockComputeQueryObject> BuildStockBillQueryObject(UserContext userCtx, string formId, string filterString, Action<StockUpdateSetting, SqlBuilderParameter, List<string>> queryCallback = null)
        {
            List<StockComputeQueryObject> lstQueryObjs = new List<StockComputeQueryObject>();

            var updateStockSettings = this.GetStockUpdateSettingByFormId(userCtx, formId);
            if (updateStockSettings.IsNullOrEmpty()) return lstQueryObjs;

            var invBillMeta = this.MetaModelService.LoadFormModel(userCtx, formId);
            var mainOrgIdField = invBillMeta.GetField("fmainorgid");
            var numberField = invBillMeta.GetNumberField();

            var invFlexModel = InventoryFlexModel.GetInstance(userCtx);

            foreach (var updStockSetting in updateStockSettings)
            {
                List<string> lstSqlSelFieldList = new List<string>();
                var activeEntry = invBillMeta.GetEntity(updStockSetting.ActiveEntityKey);
                SqlBuilderParameter sqlPara = new SqlBuilderParameter(userCtx, invBillMeta);
                sqlPara.PageCount = -1;
                sqlPara.PageIndex = -1;
                sqlPara.QueryUserFieldOnly = true;
                sqlPara.NoIsolation = false;
                sqlPara.MergeBillHeadField = false;
                sqlPara.NoColorSetting = true;
                sqlPara.IsDistinct = false;

                sqlPara.FilterString = filterString.JoinFilterString($"{updStockSetting.QtyFieldKey}>0");

                var queryEntryPkIdKey = $"{activeEntry.Id}.{activeEntry.PkFieldName}";

                sqlPara.SelectedFieldKeys.AddRange(new string[]
                {
                    $"newid() as fid",
                    $"'stk_invcomputelog' as fformid",
                    $"'{invBillMeta.Id}' as fsourceformid",
                    $"{numberField?.FieldName??"''"} as fsourcebillno",
                    $"fid as fsourceinterid",
                    $"{queryEntryPkIdKey} as fsourceentryid",
                    $"'{activeEntry.Id}' as fsourceentitykey",
                    $"{updStockSetting.Factor} as fupdatefactor",
                    $"'{updStockSetting.UpdInvServiceId}' as fupdserviceid",
                });
                lstSqlSelFieldList.AddRange(new string[]
                {
                    "fid",
                    "fformid",
                    "fsourceformid",
                    "fsourcebillno",
                    "fsourceinterid",
                    "fsourceentryid",
                    "fsourceentitykey",
                    "fupdatefactor",
                    "fupdserviceid"
                });

                queryCallback?.Invoke(updStockSetting, sqlPara, lstSqlSelFieldList);

                foreach (var flexItem in invFlexModel.InvFlexFieldKeys)
                {
                    var billFieldKey = flexItem;
                    if (!updStockSetting.InvFlexFieldSetting.TryGetValue(flexItem, out billFieldKey))
                    {
                        billFieldKey = flexItem;
                    }
                    sqlPara.SelectedFieldKeys.Add($"{billFieldKey} as {flexItem}");
                    lstSqlSelFieldList.Add($"{flexItem}");
                }

                sqlPara.SelectedFieldKeys.Add($"{updStockSetting.QtyFieldKey} as {invFlexModel.QtyFieldKey}");
                sqlPara.SelectedFieldKeys.Add($"{updStockSetting.StockQtyFieldKey} as {invFlexModel.StockQtyFieldKey}");
                sqlPara.SelectedFieldKeys.Add($"{updStockSetting.AmountFieldKey} as {invFlexModel.AmountFieldKey}");
                sqlPara.SelectedFieldKeys.Add($"{mainOrgIdField?.Id ?? "''"} as {invFlexModel.MainOrgIdFieldKey}");

                lstSqlSelFieldList.Add(invFlexModel.QtyFieldKey);
                lstSqlSelFieldList.Add(invFlexModel.StockQtyFieldKey);
                lstSqlSelFieldList.Add(invFlexModel.AmountFieldKey);
                lstSqlSelFieldList.Add(invFlexModel.MainOrgIdFieldKey);

                var queryObj = QueryService.BuilQueryObject(sqlPara);
                lstQueryObjs.Add(new StockComputeQueryObject()
                {
                    QueryObject = queryObj,
                    SqlBuilderParameter = sqlPara,
                    SqlSelectFieldList = lstSqlSelFieldList
                });
            }

            return lstQueryObjs;
        }

        /// <summary>
        /// 根据日志表数据与库存余额数据生成最新的即时库存数据
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="computeTranId"></param>
        /// <param name="dtCloseDate"></param>
        /// <param name="option"></param>
        private void CreateInventoryData(UserContext userCtx, string computeTranId, DateTime? dtCloseDate, OperateOption option)
        {
            //重置即时库存数据
            var strSql = $@"update t_stk_inventorylist set fqty=0,fstockqty=0,famount=0 where fmainorgid=@currentCompanyId";
            List<SqlParam> lstParams = new List<SqlParam>()
            {
                new SqlParam("currentCompanyId", System.Data.DbType.String, userCtx.Company)
            };
            this.DBServiceEx.Execute(userCtx, strSql, lstParams);
            this.TaskProgressService.SetTaskProgressValue(userCtx, option.GetTaskId(), 55);

            //根据库存模型动态拼装sql，实现业务数据汇总并插入临时表
            this.CreateTempInvData(userCtx, computeTranId, dtCloseDate, option);

            //将临时表库存数据提交至正式即时库存表
            this.CommitInventoryData(userCtx, computeTranId, dtCloseDate, option);

            #region 将本次涉及的业务库存单据生成库存更新快照记录
            var dm = userCtx.Container.GetService<IDataManager>();
            var invFlexModel = InventoryFlexModel.GetInstance(userCtx);

            //最后处理库存单据快照
            //先删除本次校正过程中涉及的所有单据快照
            strSql = $@"
delete u1 from t_stk_updinvlog u1
where exists(select 1 from t_stk_invcomputelog where ftranid=@tranId and u1.fbillinterid=fsourceinterid and u1.fbillformid=fsourceformid)
";
            lstParams = new List<SqlParam>()
            {
                new SqlParam("tranId", System.Data.DbType.String,computeTranId),
            };
            this.DBServiceEx.Execute(userCtx, strSql, lstParams);
            this.TaskProgressService.SetTaskProgressValue(userCtx, option.GetTaskId(), 90);

            //重新生成快照记录
            string strFlexFilter = "";
            StringBuilder sbSelFlexFields = new StringBuilder();
            foreach (var flexItem in invFlexModel.InvFlexFieldKeys)
            {
                strFlexFilter += $" and t0.{flexItem}=t1.{flexItem}";

                sbSelFlexFields.AppendLine($@"       ,t1.{flexItem}");
            }

            var iFlag = 1m;
            var lTotalCount = 0L;
            using (var reader = this.DBService.ExecuteReader(userCtx, $@"
select count(1)
from t_stk_invcomputelog t0
inner join t_stk_inventorylist t1 on t0.fmainorgid=t1.fmainorgid {strFlexFilter}
where t0.ftranid=@tranId", lstParams))
            {
                if (reader.Read())
                {
                    lTotalCount = Convert.ToInt64(reader[0]);
                }
            }

            strSql = $@"
select distinct t0.fsourceformid fbillformid
        ,t0.fsourceinterid fbillinterid
        ,t0.fsourceentryid fbillentryid
        ,t0.fmainorgid
        ,t0.fupdserviceid
        ,t0.fqty*t0.fupdatefactor fqty
        ,t0.fstockqty*t0.fupdatefactor fstockqty
        ,t0.famount*t0.fupdatefactor famount
        ,t1.fid finventoryid
        {sbSelFlexFields.ToString()}
from t_stk_invcomputelog t0
inner join t_stk_inventorylist t1 on t0.fmainorgid=t1.fmainorgid {strFlexFilter}
where t0.ftranid=@tranId
";
            List<DynamicObject> lstUpdInvLogObjs = new List<DynamicObject>();
            var invUpdLogMeta = this.MetaModelService.LoadFormModel(userCtx, "stk_updinvlog");
            using (var reader = this.DBService.ExecuteReader(userCtx, strSql, lstParams))
            {
                while (reader.Read())
                {
                    DynamicObject invLogObj = invUpdLogMeta.GetDynamicObjectType(userCtx).CreateInstance() as DynamicObject;
                    invLogObj["fformid"] = "stk_updinvlog";
                    invLogObj["finventoryid"] = reader["finventoryid"];
                    invLogObj["fbillformid"] = reader["fbillformid"];
                    invLogObj["fbillinterid"] = reader["fbillinterid"];
                    invLogObj["fbillentryid"] = reader["fbillentryid"];
                    invLogObj["fmainorgid"] = reader["fmainorgid"];
                    invLogObj["fupdserviceid"] = reader["fupdserviceid"];
                    var stockSnapshot = new StockEntrySnapshot()
                    {
                        Qty = (decimal)reader["fqty"],
                        StockQty = (decimal)reader["fstockqty"],
                        Amount = (decimal)reader["famount"],
                    };
                    //记录整个维度数据进快照
                    foreach (var flexKey in invFlexModel.InvFlexFieldKeys)
                    {
                        stockSnapshot.FlexData[flexKey] = reader[flexKey];
                    }

                    invLogObj["fsnapshot"] = stockSnapshot.ToJson();
                    lstUpdInvLogObjs.Add(invLogObj);

                    this.TaskProgressService.SetTaskProgressValue(userCtx, option.GetTaskId(), 90 + 9 * iFlag / lTotalCount);

                    iFlag++;
                }
            }
            if (lstUpdInvLogObjs.Any())
            {
                dm.InitDbContext(userCtx, invUpdLogMeta.GetDynamicObjectType(userCtx));
                userCtx.Container.GetService<IPrepareSaveDataService>()?.PrepareDataEntity(userCtx, invUpdLogMeta, lstUpdInvLogObjs.ToArray(), option);
                dm.Save(lstUpdInvLogObjs);
            }

            #endregion
        }

        /// <summary>
        /// 创建校正过程中的库存中间临时表
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="computeTranId"></param>
        /// <param name="dtCloseDate"></param>
        /// <param name="option"></param>
        private void CreateTempInvData(UserContext userCtx, string computeTranId, DateTime? dtCloseDate, OperateOption option)
        {
            var invFormMetaType = this.MetaModelService.CloneFormModel(userCtx, "stk_inventorylist", (entity) =>
            {
                switch (entity.Id.ToLower())
                {
                    case "fbillhead":
                        return $"tmpi_{computeTranId}";
                    case "fentity":
                        return $"tmpie_{computeTranId}";
                    default:
                        return $"tmpiec_{computeTranId}";
                }
            });

            if (!invFormMetaType.Properties.ContainsKey("finventoryid"))
            {
                invFormMetaType.RegisterSimpleProperty("finventoryid", typeof(string), "", false, new SimplePropertyAttribute()
                {
                    Alias = "finventoryid"
                });
            }

            var dm = userCtx.Container.GetService<IDataManager>();
            dm.Option.SetAutoUpdateScheme(true);
            dm.InitDbContext(userCtx, invFormMetaType);

            this.DBServiceEx.Execute(userCtx, $"truncate table tmpi_{computeTranId}");

            invFormMetaType = null;

            //统计汇总日志数据，进入库存余额表
            var invFlexModel = InventoryFlexModel.GetInstance(userCtx);

            StringBuilder sbInvTempInsertSql = new StringBuilder();
            sbInvTempInsertSql.Append($"insert into tmpi_{computeTranId} (fid,fformid,fmainorgid");

            StringBuilder sbInvTempSelectSql = new StringBuilder();
            sbInvTempSelectSql.Append($"select newid() as fid,'stk_inventorylist' as fformid, fmainorgid");

            StringBuilder sbInvComputeLogSelectSql = new StringBuilder();
            sbInvComputeLogSelectSql.Append($"select fmainorgid");

            StringBuilder sbInvBalanceSelectSql = new StringBuilder();
            sbInvBalanceSelectSql.Append($"select fmainorgid");

            StringBuilder sbGroupBy = new StringBuilder();
            sbGroupBy.Append("fmainorgid");
            foreach (var flexItem in invFlexModel.InvFlexFieldKeys)
            {
                sbInvTempInsertSql.Append($",{flexItem}");
                sbInvTempSelectSql.Append($",{flexItem}");

                sbInvComputeLogSelectSql.Append($",{flexItem}");
                sbInvBalanceSelectSql.Append($",{flexItem}");
                sbGroupBy.Append($",{flexItem}");
            }

            sbInvTempInsertSql.Append($",{invFlexModel.QtyFieldKey}");
            sbInvTempSelectSql.Append($",sum({invFlexModel.QtyFieldKey}) as {invFlexModel.QtyFieldKey}");
            sbInvComputeLogSelectSql.Append($",sum({invFlexModel.QtyFieldKey}*fupdatefactor) as {invFlexModel.QtyFieldKey}");
            sbInvBalanceSelectSql.Append($",{invFlexModel.QtyFieldKey}");

            sbInvTempInsertSql.Append($",{invFlexModel.StockQtyFieldKey}");
            sbInvTempSelectSql.Append($",sum({invFlexModel.StockQtyFieldKey}) as {invFlexModel.StockQtyFieldKey}");
            sbInvComputeLogSelectSql.Append($",sum({invFlexModel.StockQtyFieldKey}*fupdatefactor) as {invFlexModel.StockQtyFieldKey}");
            sbInvBalanceSelectSql.Append($",{invFlexModel.StockQtyFieldKey}");

            sbInvTempInsertSql.Append($",{invFlexModel.AmountFieldKey}");
            sbInvTempSelectSql.Append($",sum({invFlexModel.AmountFieldKey}) as {invFlexModel.AmountFieldKey}");
            sbInvComputeLogSelectSql.Append($",sum({invFlexModel.AmountFieldKey}*fupdatefactor) as {invFlexModel.AmountFieldKey}");
            sbInvBalanceSelectSql.Append($",{invFlexModel.AmountFieldKey}");

            sbInvTempInsertSql.AppendLine(")");
            sbInvComputeLogSelectSql.AppendLine()
                .AppendLine("from t_stk_invcomputelog")
                .AppendLine("where ftranid=@tranid")
                // 套件的不管理库存 
                .AppendLine("and exists(select 1 from t_bd_material m where t_stk_invcomputelog.fmaterialid=m.fid and m.fsuiteflag='0') ")
                .AppendLine($"group by {sbGroupBy.ToString()}");

            sbInvBalanceSelectSql.AppendLine()
                .AppendLine($"from t_stk_inventorybalance")
                .Append($"where fmainorgid=@currentCompanyId ");

            var lstParams = new List<SqlParam>()
            {
                new SqlParam("tranid", System.Data.DbType.String, computeTranId),
                new SqlParam("currentCompanyId", System.Data.DbType.String, userCtx.Company),
            };

            if (dtCloseDate.HasValue)
            {
                //最近一期关账余额
                sbInvBalanceSelectSql.AppendLine($"and fclosedate=@closeDate and fbalancetype='inventorybalance_type_02'");
                lstParams.Add(new SqlParam("closeDate", System.Data.DbType.DateTime, dtCloseDate));
            }
            else
            {
                //初始库存余额，不考虑初始库存余额，由于针对没有关账记录时以初始库存单为准，达到修正初始库存单的快照
                sbInvBalanceSelectSql.AppendLine($"and fbalancetype='inventorybalance_type_01' and 1<>1 ");
            }

            // 套件的不管理库存 
            sbInvBalanceSelectSql.AppendLine($"and exists(select 1 from t_bd_material m where t_stk_inventorybalance.fmaterialid=m.fid and m.fsuiteflag='0') ");

            var strSql = $@"
{sbInvTempInsertSql.ToString()} 
{sbInvTempSelectSql.ToString()}
from (
{sbInvComputeLogSelectSql.ToString()}
union all
{sbInvBalanceSelectSql.ToString()}
) u1
group by {sbGroupBy.ToString()}";
            var recordCount = this.DBServiceEx.Execute(userCtx, strSql, lstParams);
            this.TaskProgressService.SetTaskProgressValue(userCtx, option.GetTaskId(), 65);

        }

        /// <summary>
        /// 将中间库存临时表的数据更新至正式即时库存表
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="computeTranId"></param>
        /// <param name="dtCloseDate"></param>
        /// <param name="option"></param>
        private void CommitInventoryData(UserContext userCtx, string computeTranId, DateTime? dtCloseDate, OperateOption option)
        {
            var dm = userCtx.Container.GetService<IDataManager>();
            var invFlexModel = InventoryFlexModel.GetInstance(userCtx);
            //关联更新库存id
            var strSql = $@"
update tmpi_{computeTranId} as u1 set (finventoryid)=
    (select u2.fid
     from t_stk_inventorylist u2
     where u1.fmainorgid=u2.fmainorgid
";
            foreach (var flexItem in invFlexModel.InvFlexFieldKeys)
            {
                strSql += $"            and u1.{flexItem}=u2.{flexItem} \r\n";
            }
            strSql += ")";
            this.DBServiceEx.Execute(userCtx, strSql);
            this.TaskProgressService.SetTaskProgressValue(userCtx, option.GetTaskId(), 70);

            //关联更新即时库存数量金额
            strSql = $@"
update t_stk_inventorylist as u1 set (fqty,fstockqty,famount)=
    (select u2.fqty,u2.fstockqty,u2.famount
     from tmpi_{computeTranId} u2
     where u1.fmainorgid=u2.fmainorgid and u1.fid=u2.finventoryid
";
            foreach (var flexItem in invFlexModel.InvFlexFieldKeys)
            {
                strSql += $"            and u1.{flexItem}=u2.{flexItem} \r\n";
            }
            strSql += ")";
            this.DBServiceEx.Execute(userCtx, strSql);
            this.TaskProgressService.SetTaskProgressValue(userCtx, option.GetTaskId(), 75);

            //插入新库存维度的记录  
            var invFormMeta = this.MetaModelService.LoadFormModel(userCtx, "stk_inventorylist");
            List<DynamicObject> lstInvObjs = new List<DynamicObject>();
            var iFlag = 1m;
            var lTotalCount = 0L;
            using (var reader = this.DBService.ExecuteReader(userCtx, $"select count(1) fcount from tmpi_{computeTranId} where finventoryid is null or finventoryid=''"))
            {
                if (reader.Read())
                {
                    lTotalCount = Convert.ToInt64(reader[0]);
                }
            }
            using (var reader = this.DBService.ExecuteReader(userCtx, $"select * from tmpi_{computeTranId} where finventoryid is null or finventoryid=''"))
            {
                while (reader.Read())
                {
                    DynamicObject invDataObj = invFormMeta.GetDynamicObjectType(userCtx).CreateInstance() as DynamicObject;
                    invDataObj["fformid"] = "stk_inventorylist";
                    invDataObj["fmainorgid"] = reader["fmainorgid"];

                    foreach (var flexItem in invFlexModel.InvFlexFieldKeys)
                    {
                        invDataObj[flexItem] = reader[flexItem];
                    }
                    invDataObj[invFlexModel.QtyFieldKey] = reader[invFlexModel.QtyFieldKey];
                    invDataObj[invFlexModel.StockQtyFieldKey] = reader[invFlexModel.StockQtyFieldKey];
                    invDataObj[invFlexModel.AmountFieldKey] = reader[invFlexModel.AmountFieldKey];
                    lstInvObjs.Add(invDataObj);

                    this.TaskProgressService.SetTaskProgressValue(userCtx, option.GetTaskId(), 75 + 10 * iFlag / lTotalCount);
                    iFlag++;
                }
            }
            if (lstInvObjs.Any())
            {
                var AttrInfoIds_ext = lstInvObjs.Select(o => Convert.ToString(o?["fattrinfo_e"])).ToList().Distinct();
                var AttrInfo = DBService.ExecuteDynamicObject(userCtx, $" select MAX(fid) as fattrinfo,fattrinfo_e from t_bd_auxpropvalue with (nolock) where fattrinfo_e in ({AttrInfoIds_ext.JoinEx(",", true)})  GROUP BY fattrinfo_e ");
                foreach (var lstInvObj in lstInvObjs)
                {
                    var fattrinfo = Convert.ToString(lstInvObj?["fattrinfo"]);
                    var fattrinfo_e = Convert.ToString(lstInvObj?["fattrinfo_e"]);
                    if (!fattrinfo_e.IsNullOrEmptyOrWhiteSpace() && fattrinfo.IsNullOrEmptyOrWhiteSpace())
                    {
                        lstInvObj["fattrinfo"] = AttrInfo.Where(o => Convert.ToString(o["fattrinfo_e"]).EqualsIgnoreCase(fattrinfo_e))?.Select(o => Convert.ToString(o["fattrinfo"]))?.FirstOrDefault() ?? "";
                    }
                }

                dm.InitDbContext(userCtx, invFormMeta.GetDynamicObjectType(userCtx));
                userCtx.Container.GetService<IPrepareSaveDataService>()?.PrepareDataEntity(userCtx, invFormMeta, lstInvObjs.ToArray(), option);
                dm.Save(lstInvObjs);
            }
            this.TaskProgressService.SetTaskProgressValue(userCtx, option.GetTaskId(), 88);
            this.TaskProgressService.SetTaskProgressMessage(userCtx, option.GetTaskId(), "正在校正商品资料图片信息……");

            //            //修复商品图片
            //            var strUpdSql = $@"/*dialect*/
            //update t0 set fmtrlimage=isnull(case when (tk.fimage='' or tk.fimage=' ' or tk.fimage is null) and (t0.fattrinfo='' or t0.fattrinfo=' ' or t0.fattrinfo is null) then sp.fimage else tk.fimage end,' ')
            //from t_stk_inventorylist t0  with(nolock) 
            //left join t_ydj_commoditygallery tk  with(nolock) on t0.fmaterialid=tk.fproductid and t0.fattrinfo=tk.fattrinfo
            //left join t_bd_material sp  with(nolock) on t0.fmaterialid=sp.fid";

            //            this.DBServiceEx.Execute(userCtx, strUpdSql);
            this.TaskProgressService.SetTaskProgressValue(userCtx, option.GetTaskId(), 89);

        }
    }
}