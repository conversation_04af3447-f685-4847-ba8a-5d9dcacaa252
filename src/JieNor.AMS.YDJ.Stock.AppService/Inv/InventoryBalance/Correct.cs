using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Inv.InventoryBalance
{
    /// <summary>
    /// 库存余额校正逻辑
    /// </summary>
    [InjectService]
    [FormId("stk_inventorybalance")]
    [OperationNo("correct")]
    public class Correct : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 操作执行后处理
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            var svc = this.Container.GetService<Core.Interface.StockUpdate.ICostCalulateService>();
            svc.UpdateStockBalance(this.Context, this.Option);
        }
    }
}
