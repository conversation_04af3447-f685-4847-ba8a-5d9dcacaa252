using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.Remoting.Messaging;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.DataTransferObject.Reserve;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Stock.AppService.Inv
{
    /// <summary>
    /// 库存帮助类
    /// </summary>
    public class InventoryUtil
    {
        /// <summary>
        /// 根据【预留设置】获取《商品收发明细表》
        /// </summary>
        /// <returns></returns>
        internal static List<StockDetailModel> GetStockDetailData(UserContext userCtx, ReserveSettingInfo reserveSettingData)
        {
            if (!reserveSettingData.fdemandformid.EqualsIgnoreCase("ydj_order"))
            {
                return new List<StockDetailModel>();
            }

            var dicKeys = new Dictionary<string, SimpleStockKey>();

            foreach (var demandInfo in reserveSettingData.DemandEntry)
            {
                dicKeys.Add(demandInfo.fdemandentryid, new SimpleStockKey
                {
                    fattrinfo_e = demandInfo.fattrinfo_e,
                    fmaterialid = demandInfo.fmaterialid,
                    fcustomdesc = demandInfo.fcustomdesc,
                    fmtono = demandInfo.fmtono,
                    funitid = demandInfo.funitid
                });
            }

            var stockDetailDatas = GetStockDetailData(userCtx, dicKeys.Values, true);

            var inventoryList = reserveSettingData.InventoryList as DynamicObjectCollection;
            var allReserveEntrys = reserveSettingData.AllReserveEntrys as DynamicObjectCollection;

            // 再处理可用量
            stockDetailDatas = FilterByUsableQty(stockDetailDatas, inventoryList, allReserveEntrys);

            // 合并相近相同仓库，累加库存量
            stockDetailDatas = InventoryUtil.MergeNearSameStorehouse(stockDetailDatas);

            return stockDetailDatas;
        }

        /// <summary>
        /// 获取《商品收发明细表》数据
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="keys"></param>
        /// <param name="ignoreNotFIFOStoreHouse">忽略不参与自动推荐的仓库</param>
        /// <returns></returns>
        internal static List<StockDetailModel> GetStockDetailData(UserContext userCtx, IEnumerable<SimpleStockKey> keys, bool ignoreNotFIFOStoreHouse = false)
        {
            if (keys == null || keys.Count() == 0)
            {
                return new List<StockDetailModel>();
            }

            using (var tran = userCtx.CreateTransaction())
            {
                string whereString = GetStockDetailWhereString(userCtx, keys);
                if (!whereString.IsNullOrEmptyOrWhiteSpace())
                {
                    whereString = " and " + whereString;
                }

                //用商品编码过滤一遍，能提升查询速度
                string matWhere = $" and t0.fmaterialid in ('{string.Join("','", keys.Select(t => t.fmaterialid))}') ";

                if (ignoreNotFIFOStoreHouse)
                {
                    whereString +=
                        " and exists(select 1 from t_ydj_storehouse sh with(nolock) where sh.fid=t0.fstorehouseid and sh.fisnofifostock='0')";
                }

                string sql = $@"
select fmaterialid, fattrinfo,fattrinfo_e, fcustomdesc, flotno, fmtono, fownertype, fownerid, funitid, fstockunitid, fstorehouseid, fstorelocationid, fstockstatus, fdate, fbaseqty
from v_stk_stockbillinfo t0
where fmainorgid=@fmainorgid and fbaseqty>0    -- 表示入库
{matWhere}
{whereString}
order by fdate
";

                List<SqlParam> sqlParams = new List<SqlParam>();
                sqlParams.Add(new SqlParam("@fmainorgid", DbType.String, userCtx.Company));

                var dynObjs = userCtx.ExecuteDynamicObject(sql, sqlParams);

                tran.Complete();

                List<StockDetailModel> stockDatas = new List<StockDetailModel>();
                foreach (var dynObj in dynObjs)
                {
                    var stockData = new StockDetailModel
                    {
                        fmaterialid = Convert.ToString(dynObj["fmaterialid"]),
                        fattrinfo_e = Convert.ToString(dynObj["fattrinfo_e"]),
                        fcustomdesc = Convert.ToString(dynObj["fcustomdesc"]),
                        funitid = Convert.ToString(dynObj["funitid"]),
                        fstockunitid = Convert.ToString(dynObj["fstockunitid"]),
                        fownertype = Convert.ToString(dynObj["fownertype"]),
                        fownerid = Convert.ToString(dynObj["fownerid"]),
                        flotno = Convert.ToString(dynObj["flotno"]),
                        fmtono = Convert.ToString(dynObj["fmtono"]),
                        fstorehouseid = Convert.ToString(dynObj["fstorehouseid"]),
                        fstorelocationid = Convert.ToString(dynObj["fstorelocationid"]),
                        fstockstatus = Convert.ToString(dynObj["fstockstatus"]),
                        fdate = Convert.ToDateTime(dynObj["fdate"]),
                        fbaseqty = Convert.ToDecimal(dynObj["fbaseqty"])
                    };

                    stockDatas.Add(stockData);
                }

                return stockDatas;

            }
        }

        private static string GetStockDetailWhereString(UserContext userCtx, IEnumerable<SimpleStockKey> keys)
        {
            var whereString = string.Empty;
            if (keys == null || keys.Count() == 0)
            {
                return whereString;
            }

            var dicValueMap = keys.ToJson().FromJson<List<Dictionary<string, string>>>();
            if (dicValueMap == null || dicValueMap.Count == 0)
            {
                return whereString;
            }

            var invFldKeys = dicValueMap[0].Keys;
            var fldWhere = new List<string>();
            DataTable dtTemp = new DataTable();
            foreach (var key in invFldKeys)
            {
                dtTemp.Columns.Add(key, typeof(string));

                if (key.EqualsIgnoreCase("fmtono"))
                {
                    //物流跟踪号要么相等，要么库存表中的物流跟踪号为空
                    fldWhere.Add($"(tmp.{key}=t0.{key} or t0.{key}='')");
                }
                else if (key.EqualsIgnoreCase("fstockstatus"))
                {
                    //库存状态要么相等，要么临时表中的库存状态为空
                    fldWhere.Add($"(tmp.{key}=t0.{key} or tmp.{key}='')");
                }
                else if (key.EqualsIgnoreCase("fattrinfo"))
                {
                    //fldWhere.Add($" ISNULL(att1.fname_e,'')  = ISNULL(att2.fname_e,'') ");
                    fldWhere.Add($"tmp.fattrinfo_e=t0.fattrinfo_e ");
                }
                else
                {
                    fldWhere.Add($"tmp.{key}=t0.{key}");
                }
            }

            //不为空的辅助属性值集数
            var notNullAuxProps = 0;

            dtTemp.BeginLoadData();
            foreach (var item in dicValueMap)
            {
                var auxPropVal = string.Empty;
                item.TryGetValue("fattrinfo", out auxPropVal);
                if (!auxPropVal.IsNullOrEmptyOrWhiteSpace())
                {
                    notNullAuxProps++;
                }

                dtTemp.LoadDataRow(item.Values.ToArray(), true);
            }
            dtTemp.EndLoadData();

            var dbService = userCtx.Container.GetService<IDBService>();
            var tempTable = dbService.CreateTempTableWithDataTable(userCtx, dtTemp, 2000, null, null, false);
            if (!tempTable.IsNullOrEmptyOrWhiteSpace())
            {
                /*
                 * 由于前端是根据用户实时输入的辅助属性进行匹配，所以前端传给后端的是辅助属性组合值编码串，
                 * 比如：433372423283412992:红木,433372489985429510:红色调,433372588081811462:欧式
                 * 所以需要将组合值编码串 替换成 辅助属性组合值主键Id
                 */
                if (notNullAuxProps > 0)
                { 
                    //var updateSql = $@"
                    //    update {tempTable} as tmp 
                    //    set (fattrinfo_e)=(select top 1 attrext.fid from t_bd_auxpropvalue attr with(nolock) left join t_bd_auxpropvalue_ext as attrext with(nolock) on attr.fname_e = attrext.fname_e where ( attr.fmainorgid='{userCtx.Company}' or attr.fmainorgid='{userCtx.TopCompanyId}' ) and attr.fnumber=tmp.fattrinfo and tmp.fattrinfo<>'' and attr.fmaterialid = tmp.fmaterialid),
                    //   (fattrinfo)=(select top 1 t0.fid from t_bd_auxpropvalue t0 with(nolock) where ( t0.fmainorgid='{userCtx.Company}' or t0.fmainorgid='{userCtx.TopCompanyId}' ) and t0.fnumber=tmp.fattrinfo and tmp.fattrinfo<>'' and t0.fmaterialid = tmp.fmaterialid) 
                    //    ";
                    var updateSql = $@"/*dialect*/
                                            update tmp set tmp.fattrinfo_e = attrext.fid, tmp.fattrinfo = attr.fid from {tempTable} TMP 
                                            left join t_bd_auxpropvalue attr with(nolock) ON ( attr.fmainorgid='{userCtx.Company}' or attr.fmainorgid='{userCtx.TopCompanyId}' ) and (attr.fnumber =tmp.fattrinfo or attr.fid =tmp.fattrinfo )  and tmp.fattrinfo<>'' and attr.fmaterialid = tmp.fmaterialid
                                            left join t_bd_auxpropvalue_ext attrext with(nolock) ON attr.fname_e = attrext.fname_e                            
                                            ";
                    var dbServiceExt = userCtx.Container.GetService<IDBServiceEx>();
                    dbServiceExt.Execute(userCtx, updateSql);
                }

                whereString =
                    $"exists (select top 1 1 from {tempTable} tmp with(nolock) where {string.Join(" and ", fldWhere)})";
            }

            return whereString;
        }

        /// <summary>
        /// 默认查询字段
        /// </summary>
        private static List<string> _defSelectFlds = new List<string> { "fmaterialid", "fattrinfo_e", "fcustomdesc", "flotno", "fmtono", "fownerid", "fownertype", "funitid", "fstockunitid" };

        /// <summary>
        /// 获取《库存综合查询》数据
        /// 通过即时库存的【库存量】-未出库的【库存量】（相当于预留量）得到【可用量】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="stockKeys"></param>
        /// <param name="ignoreNotFIFOStoreHouse">忽略不参与自动推荐的仓库</param>
        /// <returns></returns>
        internal static List<StockSynthesizeModel> GetStockDataByOutStockBill(UserContext userCtx, IEnumerable<SimpleStockKey> stockKeys, bool ignoreNotFIFOStoreHouse = false)
        {
            if (stockKeys == null || !stockKeys.Any()) return new List<StockSynthesizeModel>();

            // 获取即时库存
            DataTable dtTemp = new DataTable();

            var dicValueMap = stockKeys.ToJson().FromJson<List<Dictionary<string, string>>>();
            var invFldKeys = dicValueMap[0].Keys;
            foreach (var flexKey in invFldKeys)
            {
                dtTemp.Columns.Add(flexKey, typeof(string));
            }

            dtTemp.BeginLoadData();
            foreach (var item in dicValueMap)
            {
                dtTemp.LoadDataRow(item.Values.ToArray(), true);
            }
            dtTemp.EndLoadData();

            var dbSvc = userCtx.Container.GetService<IDBService>();

            using (var tran = userCtx.CreateTransaction())
            {
                var tempTable = dbSvc.CreateTempTableWithDataTable(userCtx, dtTemp, 1000, null, null, false);
                var invSql = $@"
                                select {_defSelectFlds.JoinEx(",", false)}, fstorehouseid, fstorelocationid, fstockstatus, fqty, fstockqty
                                from t_stk_inventorylist inv with(nolock)
                                where fmainorgid='{userCtx.Company}' and fqty>0 and exists(select 1 from {tempTable} tmp 
                                     where {_defSelectFlds.Select(s => $"inv.{s}=tmp.{s}").JoinEx(" and ", false)}
                                )";
                if (ignoreNotFIFOStoreHouse)
                {
                    invSql +=
                        " and exists(select 1 from t_ydj_storehouse sh with(nolock) where sh.fid=inv.fstorehouseid and sh.fisnofifostock='0')";
                }

                var invObjs = dbSvc.ExecuteDynamicObject(userCtx, invSql);

                // 默认预留查询条件
                string defReserveWhere = $@" and exists(select 1 from {tempTable} tmp where {invFldKeys.Select(s => $"te.{s}=tmp.{s}").JoinEx(" and ", false)})";

                if (ignoreNotFIFOStoreHouse)
                {
                    defReserveWhere +=
                        " and exists(select 1 from t_ydj_storehouse sh with(nolock) where sh.fid=te.fstorehouseid and sh.fisnofifostock='0')";
                }

                defReserveWhere = "";
                var reserveSql = GetOutingSql(userCtx, defReserveWhere);
                var reserveObjs = dbSvc.ExecuteDynamicObject(userCtx, reserveSql);

                tran.Complete();

                var stockObjList = (stockKeys as IEnumerable<StockKey>).Select(t => new
                {
                    fmaterialid = t.fmaterialid,
                    fattrinfo_e = t.fattrinfo_e,
                    fcustomdesc = t.fcustomdesc,
                    flotno = t.flotno,
                    fmtono = t.fmtono,
                    fownerid = t.fownerid,
                    fownertype = t.fownertype,
                    funitid = t.funitid,
                    fstockunitid = t.fstockunitid
                }).ToList();

                var storeSql = $"select fid from t_ydj_storehouse sh with(nolock) where sh.fmainorgid='{userCtx.Company}' and sh.fisnofifostock='0'";
                var storeObjs = dbSvc.ExecuteDynamicObject(userCtx, storeSql);
                var storeObjList = storeObjs.Select(t => new
                {
                    fid = Convert.ToString(t["fid"]).Trim()
                }).ToList();

                var reserveObjslist = reserveObjs.Where(t =>
                                            stockObjList.Exists(s => s.fmaterialid.EqualsIgnoreCase(Convert.ToString(t["fmaterialid"])) && s.fattrinfo_e.EqualsIgnoreCase(Convert.ToString(t["fattrinfo_e"]).Trim())
                                                && s.fcustomdesc.EqualsIgnoreCase(Convert.ToString(t["fcustomdesc"]).Trim()) && s.flotno.EqualsIgnoreCase(Convert.ToString(t["flotno"]).Trim())
                                                && s.fmtono.EqualsIgnoreCase(Convert.ToString(t["fmtono"]).Trim()) && s.fownerid.EqualsIgnoreCase(Convert.ToString(t["fownerid"]).Trim())
                                                && s.fownertype.EqualsIgnoreCase(Convert.ToString(t["fownertype"]).Trim()) && s.funitid.EqualsIgnoreCase(Convert.ToString(t["funitid"]))
                                                && s.fstockunitid.EqualsIgnoreCase(Convert.ToString(t["fstockunitid"]))
                                            )
                                      )
                                      .Where(f =>
                                            storeObjList.Exists(r => r.fid.EqualsIgnoreCase(Convert.ToString(f["fstorehouseid"])))
                                      );

                List<StockSynthesizeModel> stockDatas = new List<StockSynthesizeModel>();

                foreach (var item in invObjs)
                {
                    var fmaterialid = Convert.ToString(item["fmaterialid"]);
                    var fattrinfo_e = Convert.ToString(item["fattrinfo_e"]);
                    var fcustomdesc = Convert.ToString(item["fcustomdesc"]);
                    var funitid = Convert.ToString(item["funitid"]);
                    var fstockunitid = Convert.ToString(item["fstockunitid"]);
                    var fownertype = Convert.ToString(item["fownertype"]);
                    var fownerid = Convert.ToString(item["fownerid"]);
                    var flotno = Convert.ToString(item["flotno"]);
                    var fmtono = Convert.ToString(item["fmtono"]);
                    var fstorehouseid = Convert.ToString(item["fstorehouseid"]);
                    var fstorelocationid = Convert.ToString(item["fstorelocationid"]);
                    var fstockstatus = Convert.ToString(item["fstockstatus"]);
                    var fqty = Convert.ToDecimal(item["fqty"]);

                    var matchReserveEntrys = MatchReserveEntrys(item, reserveObjslist);
                    var freserveqty = matchReserveEntrys.Sum(s => Convert.ToDecimal(s["fqty"]));

                    var fusableqty = fqty < freserveqty ? 0 : fqty - freserveqty;

                    StockSynthesizeModel stockData = new StockSynthesizeModel
                    {
                        fmaterialid = fmaterialid,
                        fattrinfo_e = fattrinfo_e,
                        fcustomdesc = fcustomdesc,
                        funitid = funitid,
                        fstockunitid = fstockunitid,
                        fownertype = fownertype,
                        fownerid = fownerid,
                        flotno = flotno,
                        fmtono = fmtono,
                        fstorehouseid = fstorehouseid,
                        fstorelocationid = fstorelocationid,
                        fstockstatus = fstockstatus,
                        fqty = fqty,
                        freserveqty = freserveqty,
                        fusableqty = fusableqty
                    };

                    stockDatas.Add(stockData);
                }

                dbSvc.DeleteTempTableByName(userCtx, tempTable, true);

                return stockDatas;
            }
        }

        /// <summary>
        /// 获取《库存综合查询》数据，计算可用量
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="stockKeys"></param>
        /// <param name="ignoreNotFIFOStoreHouse">忽略不参与自动推荐的仓库</param>
        /// <returns></returns>
        internal static List<StockSynthesizeModel> GetStockDataByOutStockBillNew(UserContext userCtx, IEnumerable<SimpleStockKey> stockKeys, bool ignoreNotFIFOStoreHouse = false)
        {
            if (stockKeys == null || !stockKeys.Any()) return new List<StockSynthesizeModel>();

            // 获取即时库存
            DataTable dtTemp = new DataTable();

            var dicValueMap = stockKeys.ToJson().FromJson<List<Dictionary<string, string>>>();
            var invFldKeys = dicValueMap[0].Keys;
            foreach (var flexKey in invFldKeys)
            {
                dtTemp.Columns.Add(flexKey, typeof(string));
            }

            dtTemp.BeginLoadData();
            foreach (var item in dicValueMap)
            {
                dtTemp.LoadDataRow(item.Values.ToArray(), true);
            }
            dtTemp.EndLoadData();

            var dbSvc = userCtx.Container.GetService<IDBService>();

            using (var tran = userCtx.CreateTransaction())
            {
                var tempTable = dbSvc.CreateTempTableWithDataTable(userCtx, dtTemp, 1000, null, null, false);

                var invSql = $@"
                                select {_defSelectFlds.JoinEx(",", false)}, fstorehouseid, fstorelocationid, fstockstatus, fqty, fstockqty, fstockreserveqty 
                                from v_stk_inventorylist inv with(nolock)
                                where fmainorgid='{userCtx.Company}' and fqty>0 and exists(select 1 from {tempTable} tmp 
                                    where {_defSelectFlds.Select(s => $"inv.{s}=tmp.{s}").JoinEx(" and ", false)}
                                )";
                if (ignoreNotFIFOStoreHouse)
                {
                    invSql +=
                        " and exists(select 1 from t_ydj_storehouse sh with(nolock) where sh.fid=inv.fstorehouseid and sh.fisnofifostock='0')";
                }

                var invObjs = dbSvc.ExecuteDynamicObject(userCtx, invSql);

                tran.Complete();

                var stockObjList = (stockKeys as IEnumerable<StockKey>).Select(t => new
                {
                    fmaterialid = t.fmaterialid,
                    fattrinfo_e = t.fattrinfo_e,
                    fcustomdesc = t.fcustomdesc,
                    flotno = t.flotno,
                    fmtono = t.fmtono,
                    fownerid = t.fownerid,
                    fownertype = t.fownertype,
                    funitid = t.funitid,
                    fstockunitid = ""
                }).ToList();

                var storeSql = $"select fid from t_ydj_storehouse sh with(nolock) where sh.fmainorgid='{userCtx.Company}' and sh.fisnofifostock='0'";
                var storeObjs = dbSvc.ExecuteDynamicObject(userCtx, storeSql);
                var storeObjList = storeObjs.Select(t => new
                {
                    fid = Convert.ToString(t["fid"]).Trim()
                }).ToList();

                List<StockSynthesizeModel> stockDatas = new List<StockSynthesizeModel>();

                foreach (var item in invObjs)
                {
                    var fmaterialid = Convert.ToString(item["fmaterialid"]);
                    var fattrinfo_e = Convert.ToString(item["fattrinfo_e"]);
                    var fcustomdesc = Convert.ToString(item["fcustomdesc"]);
                    var funitid = Convert.ToString(item["funitid"]);
                    var fstockunitid = Convert.ToString(item["fstockunitid"]);
                    var fownertype = Convert.ToString(item["fownertype"]);
                    var fownerid = Convert.ToString(item["fownerid"]);
                    var flotno = Convert.ToString(item["flotno"]);
                    var fmtono = Convert.ToString(item["fmtono"]);
                    var fstorehouseid = Convert.ToString(item["fstorehouseid"]);
                    var fstorelocationid = Convert.ToString(item["fstorelocationid"]);
                    var fstockstatus = Convert.ToString(item["fstockstatus"]);
                    var fqty = Convert.ToDecimal(item["fqty"]);
                    var freserveqty = Convert.ToDecimal(item["fstockreserveqty"]);

                    var fusableqty = fqty <= freserveqty ? 0 : fqty - freserveqty;

                    StockSynthesizeModel stockData = new StockSynthesizeModel
                    {
                        fmaterialid = fmaterialid,
                        fattrinfo_e = fattrinfo_e,
                        fcustomdesc = fcustomdesc,
                        funitid = funitid,
                        fstockunitid = fstockunitid,
                        fownertype = fownertype,
                        fownerid = fownerid,
                        flotno = flotno,
                        fmtono = fmtono,
                        fstorehouseid = fstorehouseid,
                        fstorelocationid = fstorelocationid,
                        fstockstatus = fstockstatus,
                        fqty = fqty,
                        freserveqty = freserveqty,
                        fusableqty = fusableqty
                    };

                    stockDatas.Add(stockData);
                }

                dbSvc.DeleteTempTableByName(userCtx, tempTable, true);

                return stockDatas;
            }
        }

        /// <summary>
        /// 获取待出库数量
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="whereString"></param>
        /// <returns></returns>
        internal static string GetOutingSql(UserContext userCtx, string whereString)
        {
            var sql = $@"
                        -- 销售出库单
                        select {_defSelectFlds.Select(s => $"te.{s}").JoinEx(",", false)}, te.fstorehouseid, te.fstorelocationid, te.fstockstatus, te.fqty, te.fstockqty
                        from t_stk_sostockout t with(nolock)
                        inner join t_stk_sostockoutentry te with(nolock) on t.fid=te.fid
                        where t.fmainorgid='{userCtx.Company}' and t.fstatus<>'E' and t.fcancelstatus='0' {whereString}
                        union all
                        -- 其它出库单
                        select {_defSelectFlds.Select(s => $"te.{s}").JoinEx(",", false)}, te.fstorehouseid, te.fstorelocationid, te.fstockstatus, te.fqty, te.fstockqty
                        from t_stk_otherstockout t with(nolock)
                        inner join t_stk_otherstockoutentry te with(nolock) on t.fid=te.fid
                        where t.fmainorgid='{userCtx.Company}' and t.fstatus<>'E' and t.fcancelstatus='0' {whereString}
                        union all
                        -- 库存调拨单
                        select {_defSelectFlds.Select(s => $"te.{s}").JoinEx(",", false)}, te.fstorehouseid, te.fstorelocationid, te.fstockstatus, te.fqty, te.fstockqty
                        from t_stk_invtransfer t with(nolock)
                        inner join t_stk_invtransferentry te with(nolock) on t.fid=te.fid
                        where t.fmainorgid='{userCtx.Company}' and (t.fstatus in ('A', 'B', 'C') or (t.fstatus = 'D' and t.fisstockout='0')) and t.fcancelstatus='0' {whereString}
                        union all
                        -- 采购退货单
                        select {_defSelectFlds.Select(s => $"te.{s}").JoinEx(",", false)}, te.fstorehouseid, te.fstorelocationid, te.fstockstatus, te.fqty, te.fstockqty
                        from t_stk_postockreturn t with(nolock)
                        inner join t_stk_postockreturnentry te with(nolock) on t.fid=te.fid
                        where t.fmainorgid='{userCtx.Company}' and t.fstatus<>'E' and t.fcancelstatus='0' {whereString}
                        ";
            return sql;
        }


        private static IEnumerable<DynamicObject> MatchReserveEntrys(DynamicObject invObj, IEnumerable<DynamicObject> reserveObjs)
        {
            var match = reserveObjs.AsEnumerable();
            var flds = new List<string> { "fmaterialid", "fattrinfo_e", "fcustomdesc", "flotno", "fmtono", "fownerid", "fownertype", "funitid", "fstockunitid", "fstorehouseid", "fstorelocationid", "fstockstatus" };

            foreach (var fld in flds)
            {
                match = match.Where(s =>
                    Convert.ToString(s[fld]).Trim().EqualsIgnoreCase(Convert.ToString(invObj[fld]).Trim()));
            }

            return match;
        }

        /// <summary>
        /// 按可用量过滤入库单
        /// </summary>
        /// <param name="stockDetailDatas"></param>
        /// <param name="invDataGroups"></param>
        /// <returns></returns>
        internal static List<StockDetailModel> FilterByUsableQty(List<StockDetailModel> stockDetailDatas, List<IGrouping<StockKey, StockSynthesizeModel>> invDataGroups)
        {
            List<StockDetailModel> datas = new List<StockDetailModel>();

            foreach (var grp in invDataGroups)
            {
                foreach (var model in grp)
                {
                    if (model.fusableqty <= 0)
                    {
                        continue;
                    }

                    var matchStockDetailDatas = stockDetailDatas
                        .Where(s => StockKey.IsEquals(s, model))
                        .Where(s => s.fstorehouseid.EqualsIgnoreCase(model.fstorehouseid))
                        .Where(s => s.fstorelocationid.EqualsIgnoreCase(model.fstorelocationid))
                        .Where(s => s.fstockstatus.EqualsIgnoreCase(model.fstockstatus))
                        // 按最近入库时间排序
                        .OrderByDescending(s => s.fdate)
                        .ToList();

                    var sumUsable = model.fusableqty;
                    foreach (var stockDetailData in matchStockDetailDatas)
                    {
                        var qty = stockDetailData.fbaseqty;
                        if (sumUsable > qty)
                        {
                            datas.Add(stockDetailData);
                            sumUsable -= qty;
                        }
                        else
                        {
                            stockDetailData.fbaseqty = sumUsable;
                            datas.Add(stockDetailData);
                            break;
                        }
                    }
                }
            }

            return datas;
        }

        /// <summary>
        /// 按可用量过滤入库单
        /// </summary>
        /// <param name="stockDetailDatas"></param>
        /// <returns></returns>
        internal static List<StockDetailModel> FilterByUsableQty(List<StockDetailModel> stockDetailDatas, IEnumerable<DynamicObject> invObjs, IEnumerable<DynamicObject> reserveObjs)
        {
            List<StockDetailModel> datas = new List<StockDetailModel>();

            List<SimpleStockSynthesizeModel> stockSynthesizeModels = new List<SimpleStockSynthesizeModel>();

            var invGrps = invObjs.GroupBy(s => new SimpleStockSynthesizeModel
            {
                fmaterialid = Convert.ToString(s["fmaterialid"]),
                fattrinfo_e = Convert.ToString(s["fattrinfo_e"]),
                fcustomdesc = Convert.ToString(s["fcustomdesc"]),
                fmtono = Convert.ToString(s["fmtono"]),
                funitid = Convert.ToString(s["funitid"]),
                fstorehouseid = Convert.ToString(s["fstorehouseid"]),
                fstockstatus = Convert.ToString(s["fstockstatus"])
            }, new SimpleStockSynthesizeModelComparer());

            var reserveGrps = reserveObjs.GroupBy(s => new SimpleStockSynthesizeModel
            {
                fmaterialid = Convert.ToString(s["fmaterialid"]),
                fattrinfo_e = Convert.ToString(s["fattrinfo_e"]),
                fcustomdesc = Convert.ToString(s["fcustomdesc"]),
                fmtono = Convert.ToString(s["fmtono"]),
                funitid = Convert.ToString(s["funitid"]),
                fstorehouseid = Convert.ToString(s["fstorehouseid"]),
                fstockstatus = Convert.ToString(s["fstockstatus"])
            }, new SimpleStockSynthesizeModelComparer());

            foreach (var invGrp in invGrps)
            {
                var model = (SimpleStockSynthesizeModel)invGrp.Key.Clone();

                model.fqty = invGrp.Sum(s => Convert.ToDecimal(s["fqty"]));

                var reserveGrp = reserveGrps.FirstOrDefault(s => SimpleStockSynthesizeModel.IsEquals(invGrp.Key, s.Key));
                if (reserveGrp != null)
                {
                    model.freserveqty = reserveGrp.Sum(s => Convert.ToDecimal(s["fqty"]));
                }

                model.fusableqty = model.fqty < model.freserveqty ? 0 : model.fqty - model.freserveqty;

                stockSynthesizeModels.Add(model);
            }

            foreach (var model in stockSynthesizeModels)
            {
                if (model.fusableqty <= 0)
                {
                    continue;
                }

                var matchStockDetailDatas = stockDetailDatas
                    .Where(s => SimpleStockKey.IsEquals(s, model))
                    .Where(s => s.fstorehouseid.EqualsIgnoreCase(model.fstorehouseid))
                    .Where(s => s.fstockstatus.EqualsIgnoreCase(model.fstockstatus))
                    // 按最近入库时间排序
                    .OrderByDescending(s => s.fdate)
                    .ToList();

                var sumUsable = model.fusableqty;
                foreach (var stockDetailData in matchStockDetailDatas)
                {
                    var qty = stockDetailData.fbaseqty;
                    if (sumUsable > qty)
                    {
                        datas.Add(stockDetailData);
                        sumUsable -= qty;
                    }
                    else
                    {
                        stockDetailData.fbaseqty = sumUsable;
                        datas.Add(stockDetailData);
                        break;
                    }
                }
            }

            return datas;
        }

        /// <summary>
        /// 合并相近相同仓库
        /// </summary>
        /// <param name="stockDetailDatas"></param>
        /// <returns></returns>
        internal static List<StockDetailModel> MergeNearSameStorehouse(List<StockDetailModel> stockDetailDatas)
        {
            if (stockDetailDatas == null || !stockDetailDatas.Any()) return stockDetailDatas;
            var merged = new List<StockDetailModel>();

            var grps = stockDetailDatas.GroupBy(s => (StockKey)s, new StockKeyComparer()).ToList();
            foreach (var grp in grps)
            {
                StockDetailModel curr = null;
                foreach (var item in grp.OrderBy(s => s.fdate))
                {
                    if (curr == null)
                    {
                        curr = item;
                        merged.Add(curr);
                        continue;
                    }

                    if (curr.fstorehouseid.EqualsIgnoreCase(item.fstorehouseid))
                    {
                        curr.fbaseqty += item.fbaseqty;
                    }
                    else
                    {
                        curr = item;
                        merged.Add(curr);
                    }
                }
            }


            grps.Clear();
            grps = null;

            return merged;
        }

        /// <summary>
        /// 获取不推荐仓库
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        internal static List<string> GetNotFIFOStoreHouseIds(UserContext userCtx)
        {
            string sql = $"select fid from t_ydj_storehouse with(nolock) where fmainorgid='{userCtx.Company}' and fisnofifostock='1'";

            var dynObjs = userCtx.ExecuteDynamicObject(sql, new List<SqlParam>());

            return dynObjs.Select(s => Convert.ToString(s["fid"])).ToList();
        }
    }



    #region 中间类


    /// <summary>
    /// 库存键
    /// </summary>
    internal class SimpleStockKey
    {
        private string _fmaterialid = string.Empty;
        public string fmaterialid
        {
            get
            {
                return _fmaterialid;
            }
            set
            {
                _fmaterialid = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
            }
        }

        private string _fattrinfo = string.Empty;
        public string fattrinfo
        {
            get
            {
                return _fattrinfo;
            }
            set
            {
                _fattrinfo = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
            }
        }
        private string _fattrinfo_e = string.Empty;
        public string fattrinfo_e
        {
            get
            {
                return _fattrinfo_e;
            }
            set
            {
                _fattrinfo_e = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
            }
        }


        private string _fcustomdesc = string.Empty;
        public string fcustomdesc
        {
            get
            {
                return _fcustomdesc;
            }
            set
            {
                _fcustomdesc = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
            }
        }

        private string _funitid = string.Empty;
        public string funitid
        {
            get
            {
                return _funitid;
            }
            set
            {
                _funitid = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
            }
        }

        private string _fmtono = string.Empty;
        public string fmtono
        {
            get
            {
                return _fmtono;
            }
            set
            {
                _fmtono = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
            }
        }

        public static bool IsEquals(SimpleStockKey key1, SimpleStockKey key2)
        {
            if (key1 == null || key2 == null) return false;

            if (!key1.fmaterialid.EqualsIgnoreCase(key2.fmaterialid))
            {
                return false;
            }
            if (!key1.fattrinfo_e.EqualsIgnoreCase(key2.fattrinfo_e))
            {
                return false;
            }
            if (!key1.fcustomdesc.EqualsIgnoreCase(key2.fcustomdesc))
            {
                return false;
            }
            if (!key1.funitid.EqualsIgnoreCase(key2.funitid))
            {
                return false;
            }
            //// 物流跟踪号，其中一方为空时表示相等
            //if (!key1.fmtono.IsNullOrEmptyOrWhiteSpace() && !key2.IsNullOrEmptyOrWhiteSpace() && !key1.fmtono.EqualsIgnoreCase(key2.fmtono))
            //{
            //    return false;
            //}
            if (!key1.fmtono.EqualsIgnoreCase(key2.fmtono))
            {
                return false;
            }

            return true;
        }
    }

    /// <summary>
    /// 库存键
    /// </summary>
    internal class StockKey : SimpleStockKey
    {
        //private string _fmaterialid = string.Empty;
        //public string fmaterialid
        //{
        //    get
        //    {
        //        return _fmaterialid;
        //    }
        //    set
        //    {
        //        _fmaterialid = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
        //    }
        //}

        //private string _fattrinfo = string.Empty;
        //public string fattrinfo
        //{
        //    get
        //    {
        //        return _fattrinfo;
        //    }
        //    set
        //    {
        //        _fattrinfo = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
        //    }
        //}

        //private string _fcustomdesc = string.Empty;
        //public string fcustomdesc
        //{
        //    get
        //    {
        //        return _fcustomdesc;
        //    }
        //    set
        //    {
        //        _fcustomdesc = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
        //    }
        //}

        //private string _funitid = string.Empty;
        //public string funitid
        //{
        //    get
        //    {
        //        return _funitid;
        //    }
        //    set
        //    {
        //        _funitid = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
        //    }
        //}

        private string _fstockunitid = string.Empty;
        public string fstockunitid
        {
            get
            {
                return _fstockunitid;
            }
            set
            {
                _fstockunitid = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
            }
        }

        private string _fownertype = string.Empty;
        public string fownertype
        {
            get
            {
                return _fownertype;
            }
            set
            {
                _fownertype = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
            }
        }

        private string _fownerid = string.Empty;
        public string fownerid
        {
            get
            {
                return _fownerid;
            }
            set
            {
                _fownerid = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
            }
        }

        //private string _fmtono = string.Empty;
        //public string fmtono
        //{
        //    get
        //    {
        //        return _fmtono;
        //    }
        //    set
        //    {
        //        _fmtono = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
        //    }
        //}


        private string _flotno = string.Empty;
        public string flotno
        {
            get
            {
                return _flotno;
            }
            set
            {
                _flotno = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
            }
        }

        public static bool IsEquals(StockKey key1, StockKey key2)
        {
            if (key1 == null || key2 == null) return false;

            if (!key1.fmaterialid.EqualsIgnoreCase(key2.fmaterialid))
            {
                return false;
            }
            if (!key1.fattrinfo_e.EqualsIgnoreCase(key2.fattrinfo_e))
            {
                return false;
            }
            if (!key1.fcustomdesc.EqualsIgnoreCase(key2.fcustomdesc))
            {
                return false;
            }
            if (!key1.funitid.EqualsIgnoreCase(key2.funitid))
            {
                return false;
            }
            if (!key1.fstockunitid.EqualsIgnoreCase(key2.fstockunitid))
            {
                return false;
            }
            if (!key1.fownerid.EqualsIgnoreCase(key2.fownerid))
            {
                return false;
            }
            if (!key1.fownertype.EqualsIgnoreCase(key2.fownertype))
            {
                return false;
            }
            //// 物流跟踪号，其中一方为空时表示相等
            //if (!key1.fmtono.IsNullOrEmptyOrWhiteSpace() && !key2.IsNullOrEmptyOrWhiteSpace() && !key1.fmtono.EqualsIgnoreCase(key2.fmtono))
            //{
            //    return false;
            //}
            if (!key1.fmtono.EqualsIgnoreCase(key2.fmtono))
            {
                return false;
            }
            if (!key1.flotno.EqualsIgnoreCase(key2.flotno))
            {
                return false;
            }

            return true;
        }
    }

    internal class SimpleStockSynthesizeModel : SimpleStockKey, ICloneable
    {
        private string _fstorehouseid = string.Empty;
        public string fstorehouseid
        {
            get
            {
                return _fstorehouseid;
            }
            set
            {
                _fstorehouseid = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
            }
        }
        private string _fstockstatus = string.Empty;
        public string fstockstatus
        {
            get
            {
                return _fstockstatus;
            }
            set
            {
                _fstockstatus = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
            }
        }
        public decimal fqty { get; set; }
        public decimal freserveqty { get; set; }
        public decimal fusableqty { get; set; }

        public object Clone()
        {
            var model = new SimpleStockSynthesizeModel
            {
                fattrinfo = this.fattrinfo,
                fattrinfo_e = this.fattrinfo_e,
                fcustomdesc = this.fcustomdesc,
                fmaterialid = this.fmaterialid,
                fmtono = this.fmtono,
                funitid = this.funitid,
                fstorehouseid = this.fstorehouseid,
                fstockstatus = this.fstockstatus
            };

            return model;
        }

        public static bool IsEquals(SimpleStockSynthesizeModel key1, SimpleStockSynthesizeModel key2)
        {
            if (!SimpleStockKey.IsEquals(key1, key2)) return false;

            if (!key1.fstorehouseid.EqualsIgnoreCase(key2.fstorehouseid))
            {
                return false;
            }

            if (!key1.fstockstatus.EqualsIgnoreCase(key2.fstockstatus))
            {
                return false;
            }

            return true;
        }
    }

    /// <summary>
    /// 库存综合查询数据
    /// </summary>
    internal class StockSynthesizeModel : StockKey
    {
        private string _fstorehouseid = string.Empty;
        public string fstorehouseid
        {
            get
            {
                return _fstorehouseid;
            }
            set
            {
                _fstorehouseid = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
            }
        }
        private string _fstorelocationid = string.Empty;
        public string fstorelocationid
        {
            get
            {
                return _fstorelocationid;
            }
            set
            {
                _fstorelocationid = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
            }
        }
        private string _fstockstatus = string.Empty;
        public string fstockstatus
        {
            get
            {
                return _fstockstatus;
            }
            set
            {
                _fstockstatus = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
            }
        }
        public decimal fqty { get; set; }
        public decimal freserveqty { get; set; }
        public decimal fusableqty { get; set; }
        public decimal fintransitqty { get; set; }

        public string fstorehousename { get; set; }

        public string fstorelocationname { get; set; }

        public string fstockstatusname { get; set; }


        public Dictionary<string, string> ToDictionary()
        {
            return new Dictionary<string, string>
            {
                { "fmaterialid", fmaterialid },
                { "fattrinfo_e", fattrinfo_e },
                { "fcustomdesc", fcustomdesc },
                { "fmtono", fmtono },
                { "funitid", funitid },
                { "fstockunitid", fstockunitid },
                { "flotno", flotno },
                { "fownerid", fownerid },
                { "fownertype", fownertype },
                { "fstorehouseid", fstorehouseid },
                { "fstorelocationid", fstorelocationid },
                { "fstockstatus", fstockstatus },
            };
        }
    }

    /// <summary>
    /// 商品收发明细表数据
    /// </summary>
    internal class StockDetailModel : StockKey
    {
        private string id = Guid.NewGuid().ToString("N");

        private string _fstorehouseid = string.Empty;
        public string fstorehouseid
        {
            get
            {
                return _fstorehouseid;
            }
            set
            {
                _fstorehouseid = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
            }
        }
        private string _fstorelocationid = string.Empty;
        public string fstorelocationid
        {
            get
            {
                return _fstorelocationid;
            }
            set
            {
                _fstorelocationid = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
            }
        }
        private string _fstockstatus = string.Empty;
        public string fstockstatus
        {
            get
            {
                return _fstockstatus;
            }
            set
            {
                _fstockstatus = value.IsNullOrEmptyOrWhiteSpace() ? string.Empty : value;
            }
        }
        public DateTime fdate { get; set; }
        public decimal fbaseqty { get; set; }
    }

    #endregion

    internal class StockKeyComparer : IEqualityComparer<StockKey>
    {
        public bool Equals(StockKey x, StockKey y)
        {
            return StockKey.IsEquals(x, y);
        }

        public int GetHashCode(StockKey obj)
        {
            return (obj.fmaterialid + obj.fattrinfo_e + obj.fcustomdesc + obj.fmtono + obj.funitid + obj.fstockunitid + obj.flotno + obj.fownerid + obj.fownertype).GetHashCode();
        }
    }

    internal class SimpleStockSynthesizeModelComparer : IEqualityComparer<SimpleStockSynthesizeModel>
    {
        public bool Equals(SimpleStockSynthesizeModel x, SimpleStockSynthesizeModel y)
        {
            return SimpleStockSynthesizeModel.IsEquals(x, y);
        }

        public int GetHashCode(SimpleStockSynthesizeModel obj)
        {
            return (obj.fmaterialid + obj.fattrinfo_e + obj.fcustomdesc + obj.fmtono + obj.funitid + obj.fstorehouseid + obj.fstockstatus).GetHashCode();
        }
    }
}
