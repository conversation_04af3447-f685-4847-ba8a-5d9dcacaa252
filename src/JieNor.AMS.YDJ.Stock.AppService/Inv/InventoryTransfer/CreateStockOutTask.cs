using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Inv.InventoryTransfer
{
    /// <summary>
    /// 库存调拨单：生成调出任务
    /// </summary>
    [InjectService]
    [FormId("stk_inventorytransfer")]
    [OperationNo("createstockouttask")]
    public class CreateStockOutTask: AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            this.Result.MsgStyle = (int)Enu_MsgStyle.Dialog;  //弹窗提示
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string fbillno = Convert.ToString(newData["fbillno"]);
                var sql = $"select 1 from t_bcm_transfertask where ftask_type = 'transferout' and fsourcenumber = '{fbillno}'";
                var data = this.DBService.ExecuteDynamicObject(this.Context, sql);
                return data == null || !data.Any();
            }).WithMessage("库存调拨单【{0}】已经存在对应的调出扫描任务, 不允许重复生成 !", (newData, oldData) => newData["fbillno"]));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string fbillno = Convert.ToString(newData["fbillno"]);
                string fstatus = Convert.ToString(newData["fstatus"]);
                return fstatus == "D";
            }).WithMessage("库存调拨单【{0}】不是已提交状态, 不允许生成调出扫描任务 !", (newData, oldData) => newData["fbillno"]));

        }

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            //var data = e.DataEntitys;
            //if (data == null)
            //    return;
            if (e.DataEntitys == null || e.DataEntitys.Length < 1) return ;

            foreach (var data in e.DataEntitys)
            {
                #region 生成调出扫描任务
                //var isPdaCreate = Convert.ToBoolean(data["fispdatask"]);//是否PDA生成
                var isPdaCreate = Convert.ToString(data["fdescription"]).Trim().EqualsIgnoreCase("PDA库内扫描调拨生成");//是否PDA生成
                var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "bcm_transfertask");
                var newBill = htmlForm.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;
                newBill["ftask_type"] = "transferout";//单据头. 任务类型	默认为”调拨出库”
                newBill["ftaskstatus"] = isPdaCreate ? "ftaskstatus_04" : "ftaskstatus_01";//单据头. 任务状态	PDA作业的为：已完成，否则为：待作业
                newBill["ftaskdate"] = DateTime.Now;//单据头. 业务日期	默认为当前日期
                newBill["fexistednetherbill"] = "1";//单据头. 已存在下游库存单据	默认勾选
                newBill["ftaskbillno"] = data["fbillno"];//单据头. 调拨单号
                newBill["fsourcenumber"] = data["fbillno"];//单据头. 源单编号
                newBill["fsourcetype"] = "stk_inventorytransfer";//单据头. 源单类型
                newBill["fdescription"] = $"库存调拨单【{Convert.ToString(data["fbillno"])}】生成调出扫描任务";//备注
                var srcEntitys = data["fentity"] as DynamicObjectCollection;
                var newTaskEntitys = newBill["ftaskentity"] as DynamicObjectCollection;
                foreach (var secEn in srcEntitys)
                {
                    var newTaskEntity = newTaskEntitys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                    newTaskEntity["fmaterialid"] = secEn["fmaterialid"];//单据体. 扫描任务明细. 商品名称	《库存调拨单》单据体-商品明细【商品名称】
                    newTaskEntity["fattrinfo"] = secEn["fattrinfo"];//单据体. 扫描任务明细. 辅助属性	《库存调拨单》单据体-商品明细【调出辅助属性】
                    newTaskEntity["fattrinfo_e"] = secEn["fattrinfo_e"];//单据体. 扫描任务明细. 辅助属性	《库存调拨单》单据体-商品明细【调出辅助属性】
                    newTaskEntity["fcustomdesc"] = secEn["fcallupcustomdescto"];//单据体. 扫描任务明细. 定制说明	《库存调拨单》单据体-商品明细【调出定制说明】
                    newTaskEntity["fbizunitid"] = secEn["fbizunitid"];//单据体. 扫描任务明细. 单位	《库存调拨单》单据体-商品明细【调拨单位】
                    newTaskEntity["fwaitworkqty"] = secEn["fbizqty"];//单据体. 扫描任务明细. 待作业数量	《库存调拨单》单据体-商品明细【调拨数量】
                    newTaskEntity["fworkedqty"] = 0;//单据体. 扫描任务明细. 已作业数量	默认为0
                    newTaskEntity["fscannedqty"] = 0;//单据体. 扫描任务明细. 已扫描包数	默认为0
                    newTaskEntity["fstorehouseid"] = secEn["fstorehouseid"];//单据体. 仓库. 《库存调拨单》单据体-商品明细【调出仓库】
                    newTaskEntity["fstorelocationid"] = secEn["fstorelocationid"];//单据体. 《库存调拨单》单据体-商品明细【调出库位】
                    newTaskEntity["flotno"] = secEn["flotno"];//单据体. 扫描任务明细. 批号	《库存调拨单》单据体-商品明细【批号】
                    newTaskEntity["fmtono"] = secEn["fmtono"];//单据体. 扫描任务明细. 物流跟踪号	《库存调拨单》单据体-商品明细【调出物流跟踪号】
                    newTaskEntity["fownertype"] = secEn["fownertype"];//单据体. 扫描任务明细. 货主类型	《库存调拨单》单据体-商品明细【调出货主类型】
                    newTaskEntity["fownerid"] = secEn["fownerid"];//单据体. 扫描任务明细. 货主	《库存调拨单》单据体-商品明细【调出货主】
                    newTaskEntity["fqty"] = secEn["fqty"];//单据体. 扫描任务明细. 基本单位数量	《库存调拨单》单据体-商品明细【基本单位数量】
                    newTaskEntity["funitid"] = secEn["funitid"];//单据体. 扫描任务明细. 基本单位	《库存调拨单》单据体-商品明细【基本单位】
                    newTaskEntity["flinkformid"] = "stk_inventorytransfer";//单据体. 扫描任务明细. 关联单据	默认为”库存调拨单”
                    newTaskEntity["flinkbillno"] = data["fbillno"];//单据体. 扫描任务明细. 关联单据编号	《库存调拨单》表头【单据编号】
                    newTaskEntity["flinkrownumber"] = secEn["fseq"];//单据体. 扫描任务明细. 关联单行号	《库存调拨单》单据体-商品明细 的 行号
                    newTaskEntity["flinkrowinterid"] = secEn["id"];//单据体. 扫描任务明细. 关联单行内码	《库存调拨单》单据体-商品明细 的 行内码
                    newTaskEntity["fsourceformid"] = "stk_inventorytransfer";//单据体. 扫描任务明细. 来源单据	默认为”库存调拨单”
                    newTaskEntity["fsourcebillno"] = data["fbillno"];//单据体. 扫描任务明细. 来源单据编号	《库存调拨单》表头【单据编号】
                    newTaskEntity["fsourceinterid"] = secEn["fseq"];//单据体. 扫描任务明细. 来源单行号	《库存调拨单》单据体-商品明细 的 行号
                    newTaskEntity["fsourceentryid"] = secEn["id"];//单据体. 扫描任务明细. 来源单行内码	《库存调拨单》单据体-商品明细 的 行内码
                    newTaskEntitys.Add(newTaskEntity);
                }
                this.Context.Container.GetService<LoadReferenceObjectManager>().Load(this.Context, newBill.DynamicObjectType, newBill, false);
                var taskEntitys = newBill["ftaskentity"] as DynamicObjectCollection;
                foreach (var taskEn in taskEntitys)
                {
                    var mat = taskEn["fmaterialid_ref"] as DynamicObject;
                    if (mat == null)
                        continue;
                    var fpackagingqty = 0;//包数/件数
                    var fpackagtype = Convert.ToString(mat["fpackagtype"]);//'1':'标准','2':'1件多包','3':'1包多件'
                    switch (fpackagtype)
                    {
                        case "1":
                            fpackagingqty = 1;
                            taskEn["fwaitscanqty"] = Convert.ToInt32(taskEn["fwaitworkqty"]) * fpackagingqty;
                            break;
                        case "2":
                            fpackagingqty = Convert.ToInt32(mat["fbag"]);
                            taskEn["fwaitscanqty"] = Convert.ToInt32(taskEn["fwaitworkqty"]) * fpackagingqty;
                            break;
                        case "3":
                            fpackagingqty = Convert.ToInt32(mat["fpiece"]);
                            taskEn["fwaitscanqty"] = Convert.ToInt32(taskEn["fwaitworkqty"]) / fpackagingqty;
                            break;
                    }
                    taskEn["fpackagingtype"] = fpackagtype;//单据体. 扫描任务明细. 打包类型	如果商品对应《商品》基础资料的单据头-包装信息【打包类型】不为空时, 用该字段赋值【打包类型】, 否则默认为空
                    taskEn["fpackagingqty"] = fpackagingqty;//单据体. 扫描任务明细. 包数/件数	1.	如果商品对应《商品》基础资料的单据头-包装信息【打包类型】=”1件多包”时, 用【包】赋值【包数/件数】 2.如果商品对应《商品》基础资料的单据头 - 包装信息【打包类型】=”1包多件”时, 用【件】赋值【包数 / 件数】 3.如果商品对应《商品》基础资料的单据头 - 包装信息【打包类型】=”标准”时, 默认【包数 / 件数】= 1 4.如果商品对应《商品》基础资料的单据头 - 包装信息【打包类型】为空时,则默认【包数 / 件数】为空
                                                            //taskEn["fwaitscanqty"] = Convert.ToInt32(taskEn["fwaitworkqty"]) * fpackagingqty;//单据体. 扫描任务明细. 待扫描包数	根据《调拨入库任务》的【打包类型】+【包数/件数】进行计算 1.如果【打包类型】=”标准”, 则获取《库存调拨单》单据体 - 商品明细【应退数量】赋值到【待扫描包数】 2.如果【打包类型】=”1件多包”, 则将《库存调拨单》单据体 - 商品明细【应退数量】*【包数 / 件数】赋值到【待扫描包数】 3.如果【打包类型】=”1包多件”, 则将《库存调拨单》单据体 - 商品明细【应退数量】/【包数 / 件数】赋值到【待扫描包数】 4.如果【打包类型】为空, 则默认【包数 / 件数】为0
                }
                var saveRet = this.Gateway.InvokeBillOperation(this.Context, htmlForm.Id, new[] { newBill }, "save", null);
                //var updatenum = this.GetQueryOrSimpleParam("updatenum", "");
                //if (saveRet.IsSuccess && updatenum== "")
                //{
                //    //《库存调拨单》点击<生成调出任务>时, 系统自动生成《调出扫描任务》后, 会清空《库存调拨单》单据体-调拨明细【调出数量】与【调入数量】 (也会同时清空这两个字段的基本单位数量) 
                //    var sql = $"update t_stk_invtransferentry set fstockoutqty = 0,fstockoutbizqty = 0,fstockinqty = 0,fstockinbizqty = 0 where fid = '{Convert.ToString(data["id"])}'";
                //    this.Context.ExecuteDynamicObject(sql, null);
                //}
                this.Result.IsSuccess = saveRet.IsSuccess;
                //this.Result.SimpleMessage = "　　";
                var newsuccessmessage = new List<string>();
                foreach (var item in saveRet.ComplexMessage.SuccessMessages)
                {
                    var message= $"库存调拨单【{Convert.ToString(data["fbillno"])}】{item}";
                    newsuccessmessage.Add(message);
                }
                this.Result.ComplexMessage.SuccessMessages.AddRange(newsuccessmessage);
                this.Result.ComplexMessage.ErrorMessages.AddRange(saveRet.ComplexMessage.ErrorMessages);

                #endregion
            }
           

           
        }
    }
}
