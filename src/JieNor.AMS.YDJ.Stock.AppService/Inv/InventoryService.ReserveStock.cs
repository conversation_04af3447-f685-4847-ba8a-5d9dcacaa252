using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.Stock.AppService.Reserve;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace JieNor.AMS.YDJ.Stock.AppService.Inv
{
    /// <summary>
    /// 库存服务
    /// </summary> 
    public partial class InventoryService
    {
        /// <summary>
        /// 允许填充预留库存的库存单据
        /// </summary>
        static List<string> _allowFillReserveStockInfoFormIds = new List<string>() {
            "stk_sostockout", "stk_inventorytransfer"
        };

        /// <summary>
        /// 填充预留库位+拆行
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formMeta">库存表单</param>
        /// <param name="stockBills">库存单据</param>
        /// <returns></returns>
        public void FillReserveStockInfos(UserContext userCtx, HtmlForm formMeta, IEnumerable<DynamicObject> stockBills)
        {
            if (!_allowFillReserveStockInfoFormIds.Contains(formMeta.Id, StringComparer.OrdinalIgnoreCase))
            {
                return;
            }

            var groupdatas = stockBills.SelectMany(s => s["fentity"] as DynamicObjectCollection)?.Select(x => Convert.ToString(x["fsourceinterid"])).ToList();
            // 找到所有预留单
            var reservesql = $@"select t.fsourcetype,t.fsourcepkid,te.fsourceentryid,td.fstorehouseid,td.fstockstatus,td.fbizqty_d,td.fmaterialid_d,td.fdirection_d
							from t_stk_reservebill t with(nolock)
							inner join t_stk_reservebillentry te with(nolock) on t.fid=te.fid
							inner join t_stk_reservebilldetail td with(nolock) on te.fentryid=td.fentryid
							where t.fmainorgid=@fmainorgid and t.fsourcepkid in ({groupdatas.JoinEx(",", true)}) ";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company)
            };
            var dbService = userCtx.Container.GetService<IDBService>();
            // 预留数据
            var reserveDatas = dbService.ExecuteDynamicObject(userCtx, reservesql, sqlParam);

            foreach (var stockbill in stockBills)
            {
                var fentity = stockbill["fentity"] as DynamicObjectCollection;
                var addEntity = new List<DynamicObject>();
                foreach (var item in fentity)
                {
                    // 按源单类型+源单单据id+源单明细行id匹配到reservedatas（预留跟踪明细）,按仓库+库存状态分组group，计算出仓库+库存状态的预留量
                    var currentGrp = reserveDatas.Where(x => Convert.ToString(x["fsourcetype"]) == Convert.ToString(item["fsourceformid"])
                                        && Convert.ToString(x["fsourcepkid"]) == Convert.ToString(item["fsourceinterid"])
                                        && Convert.ToString(x["fsourceentryid"]) == Convert.ToString(item["fsourceentryid"]))
                                        .GroupBy(x => new
                                        {
                                            fstorehouseid = Convert.ToString(x["fstorehouseid"]),
                                            fstockstatus = Convert.ToString(x["fstockstatus"]),
                                        })
                                        .Where(g => g.Count() > 0)
                                        .Select(group => new
                                        {
                                            fstorehouseid = group.Key.fstorehouseid,
                                            fstockstatus = group.Key.fstockstatus,
                                            QuantitySum = group.Where(x => Convert.ToString(x["fdirection_d"]) == "0").Sum(x => Convert.ToDecimal(x["fbizqty_d"]))//预留增
                                                        - group.Where(x => Convert.ToString(x["fdirection_d"]) == "1").Sum(x => Convert.ToDecimal(x["fbizqty_d"]))//预留减
                                        }).OrderByDescending(x => x.QuantitySum);

                    if (currentGrp != null && currentGrp.Count() > 0)
                    {
                        var fqty = Convert.ToDecimal(item["fqty"]);
                        //查看同仓库是否有预留
                        // 举例：未出库数量是4个，a仓预留1个，b仓预留2个
                        // 拆分行：第1行是a仓库，出库数量是1个，第2行是b仓，出库数量是2个，第3行剩下1个按参数推荐仓库仓位

                        //var quantitySum = currentGrp.Where(x => x.fstorehouseid == Convert.ToString(item["fstorehouseid"]) && x.fstockstatus == Convert.ToString(item["fstockstatus"])).Select(x => x.QuantitySum).FirstOrDefault();
                        currentGrp.ForEach(x =>
                           {
                               if (x.QuantitySum > 0)
                               {
                                   if (fqty > x.QuantitySum)
                                   {
                                       var currentEntryData = item.Clone() as DynamicObject;
                                       currentEntryData["fqty"] = x.QuantitySum;
                                       currentEntryData["famount"] = x.QuantitySum * Convert.ToDecimal(currentEntryData["fprice"]);
                                       currentEntryData["fstorehouseid"] = x.fstorehouseid;
                                       currentEntryData["fstockstatus"] = x.fstockstatus;
                                       currentEntryData["fstorelocationid"] = "";//仓位默认为空
                                       addEntity.Add(currentEntryData);

                                       fqty = fqty - x.QuantitySum;
                                       item["fqty"] = fqty;
                                       item["famount"] = fqty * Convert.ToDecimal(item["fprice"]);
                                       item["fstorehouseid"] = "";//赋值为空
                                       item["fstorelocationid"] = "";//仓位默认为空
                                   }
                                   else
                                   {
                                       item["famount"] = fqty * Convert.ToDecimal(item["fprice"]);
                                       item["fstorehouseid"] = x.fstorehouseid;
                                       item["fstockstatus"] = x.fstockstatus;
                                       item["fstorelocationid"] = "";//仓位默认为空
                                   }
                               }
                           });
                    }
                }

                if (addEntity != null && addEntity.Count() > 0)
                {   //加入拆行
                    addEntity.ForEach(x => { fentity.Add(x); });
                }
            }
        }
    }
}

