using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json.Linq;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.Core.Interface.StockPick;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.DataTransferObject;

namespace JieNor.AMS.YDJ.Stock.AppService.OpService
{
    /// <summary>
    /// 库存拣货服务
    /// </summary>    
    [InjectService("stockpicking")]
    [ServiceMetaAttribute("name", "库存拣货服务")]
    [ServiceMetaAttribute("serviceid", YDJHtmlElementType.HtmlBizService_StockPicking)]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public class BizStockPickService : AbstractBaseService
    {
        /// <summary>
        /// 库存拣货配置
        /// </summary>
        public StockPickSetting Settings { get; private set; }

        /// <summary>
        /// 服务初始化时，检查库存配置参数是否缺失
        /// </summary>
        /// <param name="servicePara"></param>
        protected override void OnServiceInitialized(string servicePara)
        {
            base.OnServiceInitialized(servicePara);

            //服务参数
            if (servicePara.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("库存拣货服务参数未配置！");
            }
            JObject joServicePara = JObject.Parse(servicePara);
            if (joServicePara == null)
            {
                throw new BusinessException("库存拣货服务参数未配置！");
            }
            var stockSetting = joServicePara.GetJsonValue("serConfig", "");
            if (stockSetting.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("库存拣货服务参数未配置！");
            }
            this.Settings = stockSetting.FromJson<StockPickSetting>(true);
            if (this.Settings == null)
            {
                throw new BusinessException("库存拣货服务参数未配置！");
            }
        }

        /// <summary>
        /// 库存拣货服务逻辑实现
        /// </summary>
        /// <param name="dataEntities"></param>
        public override void ExecuteService(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Count() <= 0) return;

            var stockPickService = this.Context.Container.GetService<IStockPickService>();
            var result = stockPickService.Picking(this.Context, this.Settings, this.HtmlForm, dataEntities, this.Option);

            result.ThrowIfHasError(true, "库存拣货出现意外错误！");
        }
    }
}