using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.AMS.YDJ.DataTransferObject.Reserve;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.AMS.YDJ.Stock.AppService.Reserve
{
    /// <summary>
    /// 采购入库预留
    /// </summary>
    public partial class ReserveService
    {



        /// <summary>
        /// 采购入库预留功能
        /// 1、检查系统参数【采购入库审核时触发关联的销售合同自动预留的商品属性】，如果有设置，则需要做预留
        /// 2、找到采购入库单对应的销售合同，看合同明细的商品是否需要自动预留（按1中的参数确定），需要自动预留的，则走预留处理逻辑
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="demandForm"></param>
        /// <param name="billDatas"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        private IOperationResult POStockInReserve(UserContext ctx, HtmlForm demandForm, IEnumerable<DynamicObject> billDatas, OperateOption option)
        {
            if (billDatas != null)
            {
                foreach (var billData in billDatas)
                {
                    DebugUtil.WriteLogToFile($"billData={billData[demandForm.NumberFldKey]} 进入POStockInReserve", "采购入库自动预留");
                }
            }

            IOperationResult result = new OperationResult();
            result.IsSuccess = true;

            var profileService = ctx.Container.GetService<ISystemProfile>();
            var fporeserveprops = profileService.GetSystemParameter(ctx, "bas_storesysparam", "fporeserveprops", "");
            var reserveDay = profileService.GetSystemParameter(ctx, "stk_stockparam", "freserveday", 30);
            if (!billDatas.Any() || fporeserveprops.IsNullOrEmptyOrWhiteSpace())
            {
                foreach (var billData in billDatas)
                {
                    DebugUtil.WriteLogToFile($"billData={billData[demandForm.NumberFldKey]} fporeserveprops={fporeserveprops}", "采购入库自动预留");
                }

                return result;
            }

            var mapDatas = GetLinkDataInfos(ctx, billDatas);
            if (!mapDatas.Any())
            {
                foreach (var billData in billDatas)
                {
                    DebugUtil.WriteLogToFile($"billData={billData[demandForm.NumberFldKey]} 没找到关联合同", "采购入库自动预留");
                }

                return result;
            }

            // 预留转移合同记录：过滤（【预留转入数量】-【预留转出数量】）> 0 
            var soReserveTransferMaps = GetReserveTransferOrderInfos(ctx, mapDatas)
                .Where(s => s.BeTransferQty > 0).ToList();
            var reserveTranserOrderIds = soReserveTransferMaps.Select(s => s.ffromorderid).Distinct().ToList();

            var soIds = mapDatas.Select(f => f.forderid).Distinct().ToList();
            var allSoIds = soIds.Concat(reserveTranserOrderIds).Distinct().ToList();
            var soBillDatas = ctx.LoadBizDataById("ydj_order", allSoIds, true);

            var expirerDatas = soBillDatas.Where(f => Convert.ToDateTime(f["fdeliverydate"]).AddDays(reserveDay) < new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day)).ToList();
            foreach (var item in expirerDatas)
            {
                var soId = Convert.ToString(item["id"]);
                if (soIds.Contains(soId))
                {
                    result.ComplexMessage.WarningMessages.Add("入库单对应的销售合同{0}的【交货日期+预留天数】小于系统当前日期，无法进行预留".Fmt(item["fbillno"]));

                    DebugUtil.WriteLogToFile("入库单对应的销售合同{0}的【交货日期+预留天数】小于系统当前日期，无法进行预留".Fmt(item["fbillno"]), "采购入库自动预留");
                }
                else
                {
                    result.ComplexMessage.WarningMessages.Add("预留转移对应的销售合同{0}的【交货日期+预留天数】小于系统当前日期，无法进行预留".Fmt(item["fbillno"]));

                    DebugUtil.WriteLogToFile("预留转移对应的销售合同{0}的【交货日期+预留天数】小于系统当前日期，无法进行预留".Fmt(item["fbillno"]), "采购入库自动预留");
                }
            }

            soBillDatas = soBillDatas.Except(expirerDatas).ToList();
            if (!soBillDatas.Any())
            {
                return result;
            }

            List<string> beReserveEns = GetReserveEnInfos(fporeserveprops, soBillDatas);
            if (!beReserveEns.Any())
            {
                return result;
            }

            option.SetVariableValue("updateReserve", true);//预留更新标记 
            option.SetVariableValue("manualupdate", true);//手工预留标记 
            option.SetVariableValue("autoReserve", true);//自动预留标记 
            option.SetVariableValue("selectEntryRow", beReserveEns);//要预留的行
            option.SetVariableValue("so_stkin_maps", mapDatas);//采购入库与销售合同行映射关系
            option.SetVariableValue("so_reservetransfer_maps", soReserveTransferMaps);//销售合同行与预留转移映射关系

            var soMeta = HtmlParser.LoadFormMetaFromCache("ydj_order", ctx);

            // 已排序的销售合同：先还货、再入库
            var sortedOrders = soBillDatas
                .OrderByDescending(s => reserveTranserOrderIds.Contains(Convert.ToString(s["id"]))).ToList();

            var reserveSettingInfos = ReserveUtil.GetReserveSettingInfo(ctx, result, soMeta, sortedOrders, option);

            ReserveUtil.DebugInfo(ctx, reserveSettingInfos);

            // 执行预留逻辑
            var resultX = SetOrUpdateReserve(ctx, soMeta, reserveSettingInfos, option);
            result.MergeResult(resultX);

            // 判断是否有采购入库预留（因为超预留的情况，实际上并没有发生预留，需要排除这部分的数据，避免生成有问题的预留转移记录）
            CalculateReserveTransferQty(reserveSettingInfos, mapDatas, soReserveTransferMaps);

            // 更新《预留转移记录》
            UpdateReserveTransfer(ctx, soReserveTransferMaps);

            return result;
        }

        /// <summary>
        /// 计算最终预留转移的数量
        /// </summary>
        /// <param name="reserveSettingInfos"></param>
        /// <param name="soReserveTransferMaps"></param>
        private void CalculateReserveTransferQty(List<ReserveSettingInfo> reserveSettingInfos, List<SO_StkIn_Map> soStkInMaps, List<SO_ReserveTransfer_Map> soReserveTransferMaps)
        {
            if (soReserveTransferMaps == null || !soReserveTransferMaps.Any())
            {
                return;
            }

            var allDeamndInfos = reserveSettingInfos
                .SelectMany(s => s.DemandEntry);
            var matchTraceInfos = allDeamndInfos
                .SelectMany(s => s.TraceEntry)
                .Where(s =>
                    s.ffromformid.EqualsIgnoreCase("stk_postockin")
                    && s.freservetrancepkid.IsNullOrEmptyOrWhiteSpace()
                );

            foreach (var soReserveTransferMap in soReserveTransferMaps)
            {
                var demandInfo = allDeamndInfos.FirstOrDefault(s =>
                    s.fdemandentryid.EqualsIgnoreCase(soReserveTransferMap.ffromorderentryid));
                // 没有需求明细行，不执行预留转移
                if (demandInfo == null)
                {
                    soReserveTransferMap.RealReserveQty = 0;
                    continue;
                }

                var traceInfos = matchTraceInfos.Where(s => demandInfo.TraceEntry.Contains(s));
                //var matchSoStkInMaps = soStkInMaps.Where(s => traceInfos.Any(ss => ss.ffrombillpkid.EqualsIgnoreCase(s.fstkinenid)));

                var realQty_all = traceInfos.Sum(s => s.fqty_d);
                var oldQty_all = soReserveTransferMap.RealReserveQty;

                if (realQty_all < oldQty_all)
                {
                    // 初始化
                    soReserveTransferMap.PoInEnIds.Clear();
                    soReserveTransferMap.RealReserveQty = 0;

                    foreach (var traceInfo in traceInfos)
                    {
                        var soStkInMap = soStkInMaps.First(s => s.Trances.ContainsKey(traceInfo));

                        var realQty = traceInfo.fqty_d;
                        var oldQty = soStkInMap.Trances[traceInfo];
                        if (realQty < oldQty)
                        {
                            soReserveTransferMap.PoInEnIds.Add(soStkInMap.fstkinenid);
                            soReserveTransferMap.RealReserveQty += realQty;

                            soStkInMap.Trances[traceInfo] = realQty;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 更新《预留转移记录》
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="soReserveTransferMaps"></param>
        private void UpdateReserveTransfer(UserContext ctx, List<SO_ReserveTransfer_Map> soReserveTransferMaps)
        {
            List<string> sqls = new List<string>();
            foreach (var item in soReserveTransferMaps)
            {
                string freservetransferpoinentryids = item.PoInEnIds.JoinEx(",", false);

                // 转入方（借入方，即B），更新转出值
                string sqlB =
                    $@"/*dialect*/update t_ydj_orderreservetransfer set freservetransferoutqty+={item.RealReserveQty}, fbizreservetransferoutqty+={item.RealReserveQty}, freservetransferpoinentryids='{freservetransferpoinentryids}' where fentryid='{item.ftoorderentryid}' and freservetransferorderentryid='{item.ffromorderentryid}';";
                sqls.Add(sqlB);

                // 转出方（借出方，即A），更新转入值
                string sqlA =
                    $@"/*dialect*/update t_ydj_orderreservetransfer set freservetransferinqty+={item.RealReserveQty}, fbizreservetransferinqty+={item.RealReserveQty}, freservetransferpoinentryids='{freservetransferpoinentryids}' where fentryid='{item.ffromorderentryid}' and freservetransferorderentryid='{item.ftoorderentryid}';";
                sqls.Add(sqlA);
            }

            if (sqls.Any())
            {
                var dbServiceEx = ctx.Container.GetService<IDBServiceEx>();
                dbServiceEx.ExecuteBatch(ctx, sqls);
            }
        }


        /// <summary>
        /// 采购入库单反审核时，更新关联的销售合同的预留信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="billForm"></param>
        /// <param name="billDatas"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        public IOperationResult UpdatePOInStockReserve(UserContext ctx, HtmlForm billForm, IEnumerable<DynamicObject> billDatas, OperateOption option)
        {
            IOperationResult result = new OperationResult();
            result.IsSuccess = true;

            var mapDatas = GetLinkDataInfos(ctx, billDatas);
            if (!mapDatas.Any())
            {
                return result;
            }

            var reserveBillDatas = GetLinkReserveBills(ctx, billDatas);
            if (reserveBillDatas == null || reserveBillDatas.Count == 0)
            {
                return result;
            }

            // 获取【预留转移类型】=转入的《预留转移记录》
            var soReserveTransferMaps = GetReserveTransferOrderInfos(ctx, mapDatas);

            // 采购入库明细行id
            var poInEnIds = billDatas
                .SelectMany(billData => (DynamicObjectCollection)billData["fentity"])
                .Select(s => Convert.ToString(s["id"]))
                .ToList();

            // 获取预留单源单
            var orderMeta = HtmlParser.LoadFormMetaFromCache("ydj_order", ctx);
            var orders = ReserveUtil.GetDemandBills(ctx, orderMeta, reserveBillDatas);
            var orderEntrys = orders.SelectMany(s => (DynamicObjectCollection)s["fentry"]);

            // 获取下游预留单
            var nextReserveIds = reserveBillDatas
                .SelectMany(s =>
                    ((DynamicObjectCollection)s["fentity"])
                    .SelectMany(ss => (DynamicObjectCollection)ss["fdetail"])
                ).Select(s => Convert.ToString(s["fnextreservepkid"]))
                .Where(s => !s.IsNullOrEmptyOrWhiteSpace())
                .Distinct()
                .ToList();
            var nextReserveBills = ctx.LoadBizBillHeadDataById("stk_reservebill", nextReserveIds, "fsourcetype,fsourcenumber");

            #region 删除采购入库记录
            var beUpdate = new List<DynamicObject>();
            foreach (var reserve in reserveBillDatas)
            {
                var updateFlag = false;
                var reserveEntrys = reserve["fentity"] as DynamicObjectCollection;
                foreach (var reserveEnRow in reserveEntrys)
                {
                    var fsourcetype = reserve["fsourcetype"]?.ToString();
                    if (!fsourcetype.EqualsIgnoreCase("ydj_order"))
                    {
                        continue;
                    }

                    var fsourceentryid = reserveEnRow["fsourceentryid"]?.ToString(); //对应的来源单据的需求明细行id

                    // 判断对应的来源单据的需求明细行的行关闭状态
                    var orderEntry =
                        orderEntrys.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(fsourceentryid));
                    if (orderEntry == null)
                    {
                        continue;
                    }

                    var fclosestatus = Convert.ToString(orderEntry["fclosestatus_e"]);
                    if (fclosestatus == "3" || fclosestatus == "4")
                    {
                        //已经关闭的行，不处理
                        continue;
                    }

                    var tranceRows = (DynamicObjectCollection)reserveEnRow["fdetail"];

                    // 判断是否存在采购入库预留记录
                    var poInRows = tranceRows.Where(s =>
                        Convert.ToString(s["ffromformid"]).EqualsIgnoreCase("stk_postockin")
                        && poInEnIds.Contains(Convert.ToString(s["ffrombillpkid"]))
                        && Convert.ToString(s["fnextreservepkid"]).IsNullOrEmptyOrWhiteSpace()).ToList();
                    if (!poInRows.Any())
                    {
                        continue;
                    }

                    // 删除采购入库预留记录
                    foreach (var item in poInRows)
                    {
                        tranceRows.Remove(item);
                    }

                    DecreaseReserveTransfer(ctx, tranceRows, poInRows, nextReserveBills);

                    // 重算需求明细行的预留数量
                    CalculateReserveQty(reserveEnRow, tranceRows);

                    CheckReserveQty(reserveEnRow, tranceRows);

                    // 判断需求明细行是否存在转出（借出，即A）记录
                    foreach (var poIn in poInRows)
                    {
                        var poInEnId = Convert.ToString(poIn["ffrombillpkid"]);
                        var qty = Convert.ToDecimal(poIn["fqty_d"]);
                        var map = soReserveTransferMaps.FirstOrDefault(s => s.ffromorderentryid == fsourceentryid && s.PoInEnIds.Contains(poInEnId));
                        if (map != null)
                        {
                            map.PoInEnIds.Remove(poInEnId);
                            map.RealReserveQty -= qty;
                        }
                    }

                    updateFlag = true;
                }

                if (updateFlag)
                {
                    //删除预留量为零的预留跟踪信息
                    DeleteZeroDemandRow(reserve, reserveEntrys);

                    beUpdate.Add(reserve);
                }
            }

            var beDelete = beUpdate.Where(f => f["fentity"] == null || (f["fentity"] as DynamicObjectCollection).Count == 0)
                .ToList();
            beUpdate = beUpdate.Except(beDelete).ToList();
            if (beUpdate.Count > 0)
            {
                var para = new Dictionary<string, object>();
                para.Add("IgnoreCheckPermssion", true);
                para.Add("TopOrperationNo", "ExcelImport");
                result = ctx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(
                    ctx,
                    "stk_reservebill",
                    beUpdate,
                    "draft",
                    para);

                ReserveUtil.UpdateOrderReserveQty(ctx, beUpdate);
            }

            if (beDelete.Count > 0)
            {
                DeleteReserveBill(ctx, beDelete);
            }
            #endregion

            // 更新《预留转移记录》
            UpdateReserveTransfer(ctx, soReserveTransferMaps);

            return result;
        }

        /// <summary>
        /// 因采购入库单反审核删除增加预留记录，按需减少预留转移
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="tranceRows"></param>
        /// <param name="poInRows"></param>
        /// <param name="nextReserveBills"></param>
        private void DecreaseReserveTransfer(UserContext ctx, DynamicObjectCollection tranceRows, List<DynamicObject> poInRows,
            DynamicObjectCollection nextReserveBills)
        {
            // 【预留操作】=预留转移的记录
            var transferRows = tranceRows
                .Where(s => Convert.ToString(s["fopdesc"]).EqualsIgnoreCase("3"))
                .ToList();

            // 记录每个预留转移记录的预留量，用于后续的计算
            Dictionary<string, decimal> dicTransferQty = new Dictionary<string, decimal>();
            foreach (var transferRow in transferRows)
            {
                dicTransferQty[transferRow["id"].ToString()] = Convert.ToDecimal(transferRow["fqty_d"]);
            }

            // 遍历采购入库预留记录，检查同库存维度的预留量是否<0
            foreach (var poInRow in poInRows)
            {
                string fstorehouseid = Convert.ToString(poInRow["fstorehouseid"]).Trim();
                string fstockstatus = Convert.ToString(poInRow["fstockstatus"]).Trim();

                var rows = tranceRows.Where(s =>
                    Convert.ToString(s["fstorehouseid"]).Trim().EqualsIgnoreCase(fstorehouseid)
                    && Convert.ToString(s["fstockstatus"]).Trim().EqualsIgnoreCase(fstockstatus)
                ).ToList();

                var qty = SumReserveQty(rows).Item1;

                if (qty >= 0)
                {
                    continue;
                }

                var beAddQty = Convert.ToDecimal(poInRow["fqty_d"]);
                var poInBillNo = Convert.ToString(poInRow["ffrombillno"]);

                // 遍历预留转移记录
                foreach (var transferRow in transferRows)
                {
                    string transferRowId = transferRow["id"].ToString();

                    var transferQty = dicTransferQty[transferRowId];
                    // 增加的预留量
                    var addQty = transferQty > beAddQty ? beAddQty : transferQty;
                    if (addQty <= 0)
                    {
                        continue;
                    }

                    // 下游预留单及其源单
                    var nextReservePkId = Convert.ToString(transferRow["fnextreservepkid"]);
                    var nextReserveBill =
                        nextReserveBills.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(nextReservePkId));
                    var sourceType = Convert.ToString(nextReserveBill?["fsourcetype"]);
                    var sourceNumber = Convert.ToString(nextReserveBill?["fsourcenumber"]);
                    var transferBillForm = this.MetaModelService.LoadFormModel(ctx, sourceType);

                    // 克隆采购入库预留，保持预留平衡
                    var tranceRow = poInRow.Clone() as DynamicObject;
                    tranceRow["foptime"] = DateTime.Now;
                    tranceRow["fopuserid"] = ctx.UserId;
                    tranceRow["fqty_d"] = addQty;
                    tranceRow["fbizqty_d"] = addQty;
                    tranceRow["freservenote"] =
                        "关联的采购入库单{0}反审核，减少转移到下游单据【{1} {2}】的预留".Fmt(poInBillNo, transferBillForm?.Caption, sourceNumber);
                    tranceRow["fopdesc"] = "0"; //增加预留
                    tranceRow["fdirection_d"] = "0"; //减少预留转移
                    tranceRow["fnextreservepkid"] = tranceRow["fnextreservepkid"]; // 与此预留转移关联

                    tranceRows.Add(tranceRow);

                    beAddQty -= addQty;
                    transferQty -= addQty;
                    dicTransferQty[transferRowId] = transferQty;
                }
            }
        }


        /// <summary>
        /// 获取需要进行自动预留的合同明细行信息
        /// </summary>
        /// <param name="reserveSetting"></param>
        /// <param name="soBillDatas"></param>
        /// <returns>合同明细行id</returns>
        private List<string> GetReserveEnInfos(string reserveSetting, List<DynamicObject> soBillDatas)
        {
            var beReserveEns = new List<string>();
            var allEns = soBillDatas.SelectMany(x => x["fentry"] as DynamicObjectCollection).ToList();
            foreach (var item in allEns)
            {
                if (Convert.ToBoolean(item.GetRoot()["fcancelstatus"]))//单据已经作废，不更新预留
                {
                    DebugUtil.WriteLogToFile($"fentryid={item["id"]} fcancelstatus=1", "采购入库自动预留");
                    continue;
                }

                var fclosestatus_e = Convert.ToString(item["fclosestatus_e"]);
                if ("3" == fclosestatus_e || "4" == fclosestatus_e)//单据行已经关闭的，不更新预留
                {
                    DebugUtil.WriteLogToFile($"fentryid={item["id"]} fclosestatus_e={fclosestatus_e}", "采购入库自动预留");
                    continue;
                }

                var matObj = item["fproductid_ref"] as DynamicObject;
                if (matObj == null)
                {
                    DebugUtil.WriteLogToFile($"fentryid={item["id"]} fproductid_ref=null", "采购入库自动预留");
                    continue;
                }

                // 是否非标
                var funstdtype = Convert.ToBoolean(item["funstdtype"]);
                // 允许选配
                var fispresetprop = Convert.ToBoolean(matObj["fispresetprop"]);

                //标准定制：【允许选配】= 是 且 销售合同明细行的【是否非标】= 否
                if (reserveSetting.IndexOf("fcustom", StringComparison.OrdinalIgnoreCase) > -1 && fispresetprop && funstdtype == false)
                {
                    beReserveEns.Add(Convert.ToString(item["Id"]));
                    continue;
                }

                //非标产品：【允许选配】= 是 且 销售合同明细行的【是否非标】= 是
                if (reserveSetting.IndexOf("funstdtype", StringComparison.OrdinalIgnoreCase) > -1 && fispresetprop && funstdtype)
                {
                    beReserveEns.Add(Convert.ToString(item["Id"]));
                    continue;
                }

                // 标准品：【允许选配】= 否 
                if (reserveSetting.IndexOf("fstandard", StringComparison.OrdinalIgnoreCase) > -1 && fispresetprop == false)
                {
                    beReserveEns.Add(Convert.ToString(item["Id"]));
                    continue;
                }

                DebugUtil.WriteLogToFile($"fentryid={item["id"]} reserveSetting={reserveSetting} fispresetprop={fispresetprop} funstdtype={funstdtype}", "采购入库自动预留");
            }

            return beReserveEns;
        }

        /// <summary>
        /// 依据采购入库单信息，获取关联的销售合同信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="billDatas"></param>
        /// <returns></returns>
        private List<SO_StkIn_Map> GetLinkDataInfos(UserContext ctx, IEnumerable<DynamicObject> billDatas)
        {
            IDBService dbService = ctx.Container.GetService<IDBService>();

            var ids = billDatas.Select(f => Convert.ToString(f["Id"])).ToList();
            var sql = @"/*销售合同----采购订单----采购入库*/
select distinct a.fid as forderid,a.fbillno as forderno,b.fentryid as forderenid,b.fqty as fqty_so
    ,b.fbizqty as fbizqty_so,x.fid as fstkinid,x.fbillno as fstkinno,y.fentryid as fstkinenid
    ,y.fqty as fqty_in,y.fbizqty as fbizqty_in,y.fstorehouseid,y.fstorelocationid,y.fstockstatus,b.fclosestatus,a.fdeliverydate
from t_ydj_order a
inner join t_ydj_orderentry b on a.fid=b.fid  
inner join t_ydj_poorderentry d on  d.fsourceformid='ydj_order' 
    and d.fsourceinterid =a.fid 
    and d.fsourceentryid=b.fentryid 
inner join t_ydj_purchaseorder c on c.fid=d.fid
inner join t_stk_postockinentry y on y.fsourceformid='ydj_purchaseorder' 
    and y.fsourcebillno =c.fbillno 
    and y.fsourceentryid=d.fentryid 
inner join t_stk_postockin x on x.fid=y.fid --and x.fsourcetype ='ydj_purchaseorder' 
where x.fid in ({0})

union all

/*销售合同----采购订单----收货通知单----采购入库*/
select distinct a.fid as forderid,a.fbillno as forderno,b.fentryid as forderenid,b.fqty as fqty_so
    ,b.fbizqty as fbizqty_so,x.fid as fstkinid,x.fbillno as fstkinno,y.fentryid as fstkinenid
    ,y.fqty as fqty_in,y.fbizqty as fbizqty_in ,y.fstorehouseid,y.fstorelocationid,y.fstockstatus,b.fclosestatus,a.fdeliverydate
from t_ydj_order a
inner join t_ydj_orderentry b on a.fid=b.fid  
inner join t_ydj_poorderentry d on d.fsourceformid='ydj_order' 
    and d.fsourceinterid =a.fid 
    and d.fsourceentryid=b.fentryid 
inner join t_ydj_purchaseorder c on c.fid=d.fid
inner join t_pur_receiptnoticeentry j on   j.fsourceformid='ydj_purchaseorder' 
    and j.fsourcebillno =c.fbillno 
    and j.fsourceentryid=d.fentryid 
inner join t_pur_receiptnotice k on   k.fid=j.fid 
inner join t_stk_postockinentry y on y.fsourceformid='pur_receiptnotice' 
    and y.fsourcebillno =k.fbillno 
    and y.fsourceentryid=j.fentryid 
inner join t_stk_postockin x on x.fid=y.fid --and x.fsourcetype ='pur_receiptnotice'
where x.fid in ({0})  

".Fmt(ids.JoinEx(",", true));
            var mapDatas = dbService.ExecuteDynamicObject(ctx, sql);

            var maps = new List<SO_StkIn_Map>();
            foreach (var item in mapDatas)
            {
                var map = new SO_StkIn_Map
                {
                    forderid = Convert.ToString(item["forderid"]),
                    forderno = Convert.ToString(item["forderno"]),
                    forderenid = Convert.ToString(item["forderenid"]),
                    fstkinid = Convert.ToString(item["fstkinid"]),
                    fstkinno = Convert.ToString(item["fstkinno"]),
                    fstkinenid = Convert.ToString(item["fstkinenid"]),
                    fstorehouseid = Convert.ToString(item["fstorehouseid"]),
                    fstorelocationid = Convert.ToString(item["fstorelocationid"]),
                    fstockstatus = Convert.ToString(item["fstockstatus"]),
                    fdeliverydate = Convert.ToDateTime(item["fdeliverydate"]),
                    fclosestatus = Convert.ToString(item["fclosestatus"]),
                    fqty_in = Convert.ToDecimal(item["fqty_in"]),
                    fbizqty_in = Convert.ToDecimal(item["fbizqty_in"]),
                    fqty_so = Convert.ToDecimal(item["fqty_so"]),
                    fbizqty_so = Convert.ToDecimal(item["fbizqty_so"]),
                };

                maps.Add(map);
            }

            mapDatas.Clear();
            mapDatas = null;

            return maps;
        }

        /// <summary>
        /// 根据采购入库明细行关联查找并判断其上游销售合同商品明细行的预留转移记录行
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="mapDatas"></param>
        /// <returns></returns>
        private List<SO_ReserveTransfer_Map> GetReserveTransferOrderInfos(UserContext ctx, IEnumerable<SO_StkIn_Map> mapDatas)
        {
            List<string> orderEnIds = mapDatas.Select(s => s.forderenid).Distinct().ToList();

            IDBService dbService = ctx.Container.GetService<IDBService>();

            /*
             * 要根据采购入库明细行关联查找并判断其上游销售合同商品明细行是否关联存在“同一商品编码 且【预留转移类型】为“转入””的预留转移记录行
             */
            string sql = $@"
select t2.fid as ftoorderid, t0.fentryid as ftoorderentryid, t0.freservetransferorderid as ffromorderid, t0.freservetransferorderentryid as ffromorderentryid, t0.fbizreservetransferinqty, t0.fbizreservetransferoutqty, t0.freservetransferinqty, t0.freservetransferoutqty, t0.freservetransferpoinentryids
from t_ydj_orderreservetransfer t0
inner join t_ydj_orderentry t1 on t0.fentryid = t1.fentryid and t0.freservetransferproductid=t1.fproductid
inner join t_ydj_order t2 on t1.fid = t2.fid
where t0.freservetransfertype='2' 
and t2.fcancelstatus = '0' and t0.fentryid in ({orderEnIds.JoinEx(",", true)})";

            var data = dbService.ExecuteDynamicObject(ctx, sql);

            var maps = new List<SO_ReserveTransfer_Map>();
            foreach (var item in data)
            {
                var freservetransferpoinentryids = Convert.ToString(item["freservetransferpoinentryids"]);

                var poInEnIds = new HashSet<string>();
                foreach (var poStockInEnId in freservetransferpoinentryids.SplitKey(","))
                {
                    if (!poStockInEnId.IsNullOrEmptyOrWhiteSpace())
                    {
                        poInEnIds.Add(poStockInEnId);
                    }
                }

                var map = new SO_ReserveTransfer_Map
                {
                    ftoorderid = Convert.ToString(item["ftoorderid"]),
                    ftoorderentryid = Convert.ToString(item["ftoorderentryid"]),
                    ffromorderid = Convert.ToString(item["ffromorderid"]),
                    ffromorderentryid = Convert.ToString(item["ffromorderentryid"]),
                    fbizreservetransferinqty = Convert.ToDecimal(item["fbizreservetransferinqty"]),
                    fbizreservetransferoutqty = Convert.ToDecimal(item["fbizreservetransferoutqty"]),
                    freservetransferinqty = Convert.ToDecimal(item["freservetransferinqty"]),
                    freservetransferoutqty = Convert.ToDecimal(item["freservetransferoutqty"]),
                    PoInEnIds = poInEnIds
                };
                maps.Add(map);
            }

            return maps;
        }


        /// <summary>
        /// 获取采购入库单关联的预留单
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="poStockInBills"></param>
        /// <returns></returns>
        private List<DynamicObject> GetLinkReserveBills(UserContext ctx, IEnumerable<DynamicObject> poStockInBills)
        {
            var reserveMeta = this.MetaModelService.LoadFormModel(ctx, "stk_reservebill");

            var enIds = poStockInBills.SelectMany(s => (DynamicObjectCollection)s["fentity"])
                .Select(s => Convert.ToString(s["id"])).ToList();

            string sql = $@"
select distinct r.fid from t_stk_reservebilldetail rb 
inner join t_stk_reservebillentry re on rb.fentryid=re.fentryid
inner join t_stk_reservebill r on re.fid=r.fid
where r.fmainorgid='{ctx.Company}' and r.fcancelstatus='0' and rb.fopdesc='0' and rb.ffromformid='stk_postockin' and ffrombillpkid in ({enIds.JoinEx(",", true)})";

            var reader = ctx.ExecuteReader(sql, new List<SqlParam>());

            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, reserveMeta.GetDynamicObjectType(ctx));
            var reserveBills = dm.SelectBy(reader).OfType<DynamicObject>().ToList();

            return reserveBills;
        }
    }






}