using System;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Stock.AppService.Reserve.ReserveBorrowDialog
{
    /// <summary>
    /// 预留借货：动态列基础资料字段弹窗查询操作
    /// </summary>
    [InjectService]
    [FormId("stk_reserveborrowdialog")]
    [OperationNo("QuerySelector")]
    public class QuerySelector : AbstractQueryDyn
    {

        ///// <summary>
        ///// 准备操作选项时触发的事件
        ///// </summary>
        ///// <param name="e"></param>
        //public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        //{
        //    base.OnPrepareOperationOption(e);

        //    e.OpCtlParam.IgnoreOpMessage = true;
        //}

        //        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        //        {
        //            base.BeginOperationTransaction(e);


        //            /*
        //             * 只获取符合以下条件的销售合同明细行：【数据状态】=已审核 且 【关闭状态】=正常 且 【行关闭状态】=正常 或 部分关闭 且 {（销售合同明细行的【销售数量】- 销售合同明细行的【预留量】- 销售合同明细行关联的销售出库明细行的【实发数量】累加）的差额 > 0 } 且 同一需求商品维度（商品编码+定制说明+物流跟踪号+规格型号+辅助属性+销售单位）且 销售合同明细行关联的任一预留借还货记录的【被借货合同分录内码】为空
        //             */

        //            string fmaterialid = this.GetQueryOrSimpleParam<string>("fmaterialid");
        //            string fcustomdesc = this.GetQueryOrSimpleParam<string>("fcustomdesc");
        //            string fmtono = this.GetQueryOrSimpleParam<string>("fmtono");
        //            string fattrinfo = this.GetQueryOrSimpleParam<string>("fattrinfo");
        //            string funitid = this.GetQueryOrSimpleParam<string>("funitid");

        //            var filterString = $@" fmainorgid='{this.Context.Company}'
        //and fstatus='E' and fclosestatus='{(int)CloseStatus.Default}' 
        //and  fclosestatus_e in ('{(int)CloseStatus.Default}','{(int)CloseStatus.Part}')
        //and (fqty-freserveqty-foutqty)>0
        //and fproductid='{fmaterialid}' and fcustomdes_e='{fcustomdesc}' and fmtono='{fmtono}' and fattrinfo='{fattrinfo}' and funitid='{funitid}'

        //"; ;


        //            this.SimpleData["filterString"] = filterString;
        //        }

        ///// <summary>
        ///// 服务端自定义事件
        ///// </summary>
        ///// <param name="e"></param>
        //public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        //{
        //    //事件名称
        //    switch (e.EventName)
        //    {
        //        case "onAfterParseFilterString":
        //            this.OnAfterParseFilterString(e);
        //            break;
        //        default:
        //            break;
        //    }
        //}

        ///// <summary>
        ///// 处理基础资料字段过滤条件解析后事件逻辑
        ///// </summary>
        ///// <param name="e"></param>
        //private void OnAfterParseFilterString(OnCustomServiceEventArgs e)
        //{
            
        //}
    }
}
