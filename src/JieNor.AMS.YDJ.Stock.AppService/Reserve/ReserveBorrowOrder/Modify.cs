using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Stock.AppService.Reserve.ReserveBorrowOrder
{
    /// <summary>
    /// 借货合同：修改
    /// </summary>
    [InjectService]
    [FormId("stk_reserveborroworder")]
    [OperationNo("Modify")]
    public class Modify : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            e.CancelOperation = true;

            throw new BusinessException("不允许查看借货合同详情！");
        }
    }
}
