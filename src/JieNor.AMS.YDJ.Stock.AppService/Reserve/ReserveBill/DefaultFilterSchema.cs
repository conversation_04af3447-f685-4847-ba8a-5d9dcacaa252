using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.Enums;

namespace JieNor.AMS.YDJ.Stock.AppService.Reserve.ReserveBill
{




    /// <summary>
    /// 预留单列表：预设的过滤方案
    /// </summary>
    [InjectService]
    [FormId("stk_reservebill")]
    public class DefaultFilterSchema : IDynamicFilterSchemeService
    {
        public List<FilterSchemeObject> BuildDynamicFilterScheme(UserContext ctx, HtmlForm htmlForm)
        {
            List<FilterSchemeObject> filterSchemes = new List<FilterSchemeObject>();
             
            var schemeObj = PoAutoReserveAndNoOut(htmlForm);
            filterSchemes.Add(schemeObj);

            schemeObj = ManReserveAndNotOut(htmlForm);
            filterSchemes.Add(schemeObj);


            schemeObj = AutoReleaseAndNotOut(htmlForm);
            filterSchemes.Add(schemeObj);

            schemeObj = ManReleaseAndNotOut(htmlForm);
            filterSchemes.Add(schemeObj);

            return filterSchemes;
        }


        private static FilterSchemeObject ManReleaseAndNotOut(HtmlForm htmlForm)
        {
            var schemeObj = new FilterSchemeObject()
            {
                Id = "releasX_notout",
                Name = "手工释放预留但未出库",
                BillFormId = htmlForm.Id,
                IsPreset = true
            };
            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "fsourcetype.fname",
                Operator = "=",
                Value = "销售合同",
                LeftBracket = "",
                RightBracket = "",
                Logic = "",
                RowIndex = 0,
            });

            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "fbizqty",
                Operator = "=",
                Value = "0",
                LeftBracket = "",
                RightBracket = "",
                Logic = "and",
                RowIndex = 0,
            });


            // var sql = @"  select distinct tx.fentryid as FPKId
            //             from t_stk_reservebilldetail tx  
            //             where tx.fopdesc in ('2','4','5') and ( freservenote not like '销售出库单%' or freservenote like '预留转移到下游单据【销售出库单%'  )";

            var sql = @"  select distinct tx.fentryid as FPKId
                        from t_stk_reservebilldetail tx  
                        where tx.fopdesc in ('2','4','5') and ( freservenote not like '销售出库单%' or freservenote like '预留转移到下游单据【销售出库单%'  )
                              and fentryid not in (select te.fentryid
                                   from t_stk_reservebillentry te with (nolock)
                                            inner join t_ydj_orderentry oe with (nolock)
                                                       on te.fsourceformid = 'ydj_order' and te.fsourceentryid = oe.fentryid and
                                                          oe.fclosestatus in (3, 4))";

            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "fentryid",
                Operator = "exists",
                Value = sql,
                Logic = "and",
            });

            return schemeObj;

            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "fopdesc_txt",
                Operator = "=##2",
                Value = "",
                LeftBracket = "(",
                RightBracket = "",
                Logic = "and",
                RowIndex = 0,
            });

            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "fopdesc_txt",
                Operator = "=##4",
                Value = "",
                LeftBracket = "",
                RightBracket = "",
                Logic = "or",
                RowIndex = 0,
            });

            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "fopdesc_txt",
                Operator = "=##5",
                Value = "",
                LeftBracket = "",
                RightBracket = ")",
                Logic = "or",
                RowIndex = 0,
            });

            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "freservenote",
                Operator = "notlike",
                Value = "销售出库单",
                LeftBracket = "(",
                RightBracket = "",
                Logic = "and",
                RowIndex = 0,
            });


            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "freservenote",
                Operator = "like",
                Value = "预留转移到下游单据【销售出库单",
                LeftBracket = "",
                RightBracket = ")",
                Logic = "or",
                RowIndex = 0,
            });

            return schemeObj ;
        }


        private static FilterSchemeObject AutoReleaseAndNotOut(HtmlForm htmlForm)
        {
            var schemeObj = new FilterSchemeObject()
            {
                Id = "releas_notout",
                Name = "自动释放预留但未出库",
                BillFormId = htmlForm.Id,
                IsPreset = true
            };
            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "fsourcetype.fname",
                Operator = "=",
                Value = "销售合同",
                LeftBracket = "",
                RightBracket = "",
                Logic = "",
                RowIndex = 0,
            });
            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "fbizqty",
                Operator = "=",
                Value = "0",
                LeftBracket = "",
                RightBracket = "",
                Logic = "and",
                RowIndex = 0,
            });


            // var sql = @"  select distinct tx.fentryid as FPKId
            //             from t_stk_reservebilldetail tx  
            //             where tx.fopdesc = '6' and ( freservenote not like '销售出库单%' or freservenote like '预留转移到下游单据【销售出库单%'  )";
            var sql = $@"select distinct tx.fentryid as FPKId
                                from t_stk_reservebilldetail tx with(nolock )
                                where tx.fopdesc = '6'
                                  and (freservenote not like '销售出库单%' or freservenote like '预留转移到下游单据【销售出库单%')
                                  and fentryid not in (select te.fentryid
                                                       from t_stk_reservebillentry te with (nolock)
                                                                inner join t_ydj_orderentry oe with (nolock)
                                                                           on te.fsourceformid = 'ydj_order' and te.fsourceentryid = oe.fentryid and
                                                                              oe.fclosestatus in (3, 4))";

            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "fentryid",
                Operator = "exists",
                Value = sql,
                Logic = "and",
            });
             
            return schemeObj;

            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "fopdesc_txt",
                Operator = "=##6",
                Value = "",
                LeftBracket = "",
                RightBracket = "",
                Logic = "and",
                RowIndex = 0,
            });

            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "freservenote",
                Operator = "notlike",
                Value = "销售出库单",
                LeftBracket = "(",
                RightBracket = "",
                Logic = "and",
                RowIndex = 0,
            });

            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "freservenote",
                Operator = "like",
                Value = "预留转移到下游单据【销售出库单",
                LeftBracket = "",
                RightBracket = ")",
                Logic = "or",
                RowIndex = 0,
            });

            return schemeObj;
        }


        private static FilterSchemeObject ManReserveAndNotOut(HtmlForm htmlForm)
        {
           var schemeObj = new FilterSchemeObject()
            {
                Id = "ma_notout",
                Name = "手工自动预留但未出库",
                BillFormId = htmlForm.Id,
                IsPreset = true
            };
            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "fsourcetype.fname",
                Operator = "=",
                Value = "销售合同",
                LeftBracket = "",
                RightBracket = "",
                Logic = "",
                RowIndex = 0,
            });
            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "fbizqty",
                Operator = ">=",
                Value = "1",
                LeftBracket = "",
                RightBracket = "",
                Logic = "",
                RowIndex = 0,
            });

            /*var sql = @"  select distinct tx.fentryid as FPKId
                        from t_stk_reservebilldetail tx  
                        where freservenote not like '%采购入库单%' ";*/
            var sql = @"
                                select distinct tx.fentryid as FPKId
                                    from t_stk_reservebilldetail tx  
                                        where freservenote not like '%采购入库单%'
                                            and fentryid not in (select te.fentryid
                                               from t_stk_reservebillentry te with (nolock)
                                                        inner join t_ydj_orderentry oe with (nolock)
                                                                   on te.fsourceformid = 'ydj_order' and te.fsourceentryid = oe.fentryid and
                                                                      oe.fclosestatus in (3, 4))
                             ";

            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "fentryid",
                Operator = "exists",
                Value = sql,
                Logic = "and",
            });

            return schemeObj;


            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "freservenote",
                Operator = "notlike",
                Value = "采购入库单",
                LeftBracket = "",
                RightBracket = "",
                Logic = "",
                RowIndex = 0,
            });
             
            return schemeObj;
        }


        private static FilterSchemeObject PoAutoReserveAndNoOut(HtmlForm htmlForm)
        {
            var schemeObj = new FilterSchemeObject()
            {
                Id = "po_notout",
                Name = "采购入库自动预留但未出库",
                BillFormId = htmlForm.Id,
                IsPreset = true,
                Order = 1,
            };
            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "fsourcetype.fname",
                Operator = "=",
                Value = "销售合同",
                LeftBracket = "",
                RightBracket = "",
                Logic = "",
                RowIndex = 0,
            });
            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "fbizqty",
                Operator = ">=",
                Value = "1",
                LeftBracket = "",
                RightBracket = "",
                Logic = "",
                RowIndex = 0,
            });

            // var sql = @"  select distinct tx.fentryid as FPKId
            //             from t_stk_reservebilldetail tx  
            //             where freservenote like '%采购入库单%' ";
            var sql = @"
                                select distinct tx.fentryid as FPKId
                         from t_stk_reservebilldetail tx  
                             where freservenote like '%采购入库单%'
                                    and fentryid not in (select te.fentryid
                                                   from t_stk_reservebillentry te with (nolock)
                                                            inner join t_ydj_orderentry oe with (nolock)
                                                                       on te.fsourceformid = 'ydj_order' and te.fsourceentryid = oe.fentryid and
                                                                          oe.fclosestatus in (3, 4))
                             ";

            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "fentryid",
                Operator = "exists",
                Value = sql,
                Logic = "and",
            });

            return schemeObj;


            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "freservenote",
                Operator = "like",
                Value = "采购入库单",
                LeftBracket = "",
                RightBracket = "",
                Logic = "",
                RowIndex = 0,
            }); 

            return schemeObj;
        }


    }





}
