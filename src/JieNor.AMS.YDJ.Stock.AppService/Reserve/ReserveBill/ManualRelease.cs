using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.Serialization;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Stock.AppService.Reserve.ReserveBill
{
    /// <summary>
    /// 预留单：手动释放
    /// </summary>
    [InjectService]
    [FormId("stk_reservebill|ydj_order|ydj_saleintention")]
    [OperationNo("manualrelease")]
    public class ManualRelease : AbstractOperationServicePlugIn
    {
        public override void CreateObjectIdemotency(CreateObjectIdemotencyEventArgs e)
        {
            base.CreateObjectIdemotency(e);

            var settingData = GetManualReleaseSettingData();// this.GetQueryOrSimpleParam<string>("manualReleaseData");
            if (settingData != null)
            {
                var fentry = settingData["fentry"] as DynamicObjectCollection;
                foreach (var entry in fentry)
                {
                    var freservepkid = Convert.ToString(entry["freservepkid"]);
                    if (!e.IdemotencyIds.Contains(freservepkid))
                    {
                        e.IdemotencyIds.Add(freservepkid);
                    }
                }
            }
            else
            {
                if (e.DataEntitys == null || e.DataEntitys.Length == 0)
                {
                    return;
                }

                foreach (var dataEntity in e.DataEntitys)
                {
                    e.IdemotencyIds.Add($"{this.HtmlForm.Id}_{this.OperationNo}_{dataEntity["id"]}");
                }
            }
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            this.AddRefreshPageAction();

            this.Result.IsSuccess = true;
        }

        /// <summary>
        /// 获取手动释放设置数据
        /// </summary>
        /// <returns></returns>
        private DynamicObject GetManualReleaseSettingData()
        {
            var settingData = this.GetQueryOrSimpleParam<string>("manualReleaseData");
            if (settingData.IsNullOrEmptyOrWhiteSpace())
            {
                return null;
            }

            if (!settingData.Trim().StartsWith("["))
            {
                settingData = "[{0}]".Fmt(settingData);
            }

            var formMeta = this.MetaModelService.LoadFormModel(this.Context, "stk_reservereleasedialog");
            var dt = formMeta.GetDynamicObjectType(this.Context);
            var billItems = JArray.Parse(settingData);
            List<DynamicObject> lstDataEntities = new List<DynamicObject>();

            var dcSerializer = this.Container.GetService<IDynamicSerializer>();
            dcSerializer.Sync(dt, lstDataEntities, billItems, (propKey) =>
                {
                    var el = formMeta?.GetElement(propKey);
                    if (el is HtmlField) return (el as HtmlField).DynamicProperty;
                    if (el is HtmlEntryEntity) return (el as HtmlEntryEntity).DynamicProperty;

                    return null;
                },
                null,
                null,
                null);

            return lstDataEntities.FirstOrDefault();
        }
    }
}