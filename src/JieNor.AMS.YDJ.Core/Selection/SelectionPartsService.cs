using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data.SqlClient;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.CustomException;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.DataTransferObject.Poco;

namespace JieNor.AMS.YDJ.Core.Selection
{
    /// <summary>
    /// 选配配件服务
    /// </summary>
    [InjectService]
    public class SelectionPartsService : ISelectionPartsService
    {
        /// <summary>
        /// 数据库服务
        /// </summary>
        [InjectProperty]
        private IDBService DBService { get; set; }

        /// <summary>
        /// 自动匹配加载选配配件映射中的配件映射明细列表，注意：该方法的逻辑是针对自动匹配配件的应用场景。
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="propList">属性实体列表</param>
        /// <returns>返回匹配到的配件映射明细列表</returns>
        public List<DynamicObject> LoadPartsMapingEntryList(UserContext userCtx, string productId, List<PropEntity> propList, string allProductView)
        {
            var partsEntrys = new List<DynamicObject>();

            //如果属性中不存在配件属性，则不用处理
            var propIds = propList
                .Where(o => (o.ValueName ?? "").Trim().EqualsIgnoreCase("有"))
                .Select(o => o.PropId)
                .ToList();
            //获得配件标记，如果商品打上配件标记走铁架床逻辑
            var productObjs = userCtx.LoadBizBillHeadDataById("ydj_product", productId, "fname,fispartflag");
            var fispartflag = Convert.ToString(productObjs["fispartflag"]);

            var partsPropIds = this.MatchPartsPropIds(userCtx, propIds);
            if (!partsPropIds.Any() && !fispartflag.EqualsIgnoreCase("1")) return partsEntrys;

            //配件类型名称
            var partsTypeNames = new List<string>();
            foreach (var partsPropId in partsPropIds)
            {
                foreach (var prop in propList)
                {
                    if (partsPropId.EqualsIgnoreCase(prop.PropId))
                    {
                        prop.IsParts = true; //标记为配件属性
                        partsTypeNames.Add(prop.PropName);
                        break;
                    }
                }
            }
            //如果是铁架床 这里也要支持带出配件
            if (fispartflag.EqualsIgnoreCase("1")) 
            {
                partsTypeNames.Add("铁架床");
            }

            partsEntrys = this.LoadPartsMapingEntryListByPartsTypeNames(userCtx, productId, propList, partsTypeNames, allProductView);

            return partsEntrys;
        }

        /// <summary>
        /// 匹配加载指定配件类型的配件映射明细列表
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="propList">属性实体列表</param>
        /// <param name="partsTypeNames">配件类型名称集合，比如：支撑杆、床箱底板</param>
        /// <returns>返回匹配到的配件映射明细列表</returns>
        public List<DynamicObject> LoadPartsMapingEntryListByPartsTypeNames(
            UserContext userCtx, 
            string productId, 
            List<PropEntity> propList, 
            List<string> partsTypeNames, string allProductView)
        {
            var partsEntrys = new List<DynamicObject>();

            //加载配件映射明细列表
            var partsMapingEntrys = this.LoadPartsMapingEntrys(userCtx, productId, partsTypeNames, allProductView);
            if (partsMapingEntrys == null || !partsMapingEntrys.Any()) return partsEntrys;
            
            //获得配件标记，如果商品打上配件标记走铁架床逻辑
            var productObjs = userCtx.LoadBizBillHeadDataById("ydj_product", productId, "fname,fispartflag");
            var fispartflag = Convert.ToString(productObjs["fispartflag"]);

            //解析条件公式中的属性
            var formulas = new List<string>();
            foreach (var item in partsMapingEntrys)
            {
                var condition = Convert.ToString(item?["fconditions"]);
                formulas.Add(condition);
            }
            var formulaService = userCtx.Container.GetService<IFormulaService>();
            var propDatas = formulaService.ParseFormulaPropDatas(userCtx, formulas);

            //执行条件公式
            foreach (var item in partsMapingEntrys)
            {
                //如果是铁架床，选配映射明细要根据当前商品做过滤
                if (fispartflag.EqualsIgnoreCase("1"))
                {
                    //如果商品名称不满足条件则直接跳过，不必匹配公式。
                    if (!Convert.ToString(item?["fmaterialidnew"]).EqualsIgnoreCase(productId))
                    {
                        continue;
                    }
                }
                var mapingName = Convert.ToString(item?["fname"]);
                var condition = Convert.ToString(item?["fconditions"]);
                if (condition.IsNullOrEmptyOrWhiteSpace()) continue;

                //调用公式服务：判断传入的【属性】和【属性值】是否满足条件
                var errorMessage = "";
                var isOk = formulaService.TryParseAndExecutePropFormula<bool>(userCtx, condition, propList, out errorMessage, propDatas);
                if (!isOk)
                {
                    if (!errorMessage.IsNullOrEmptyOrWhiteSpace())
                    {
                        throw new BusinessException($"选配配件映射【{mapingName}】条件公式【{condition}】解析失败：{errorMessage}");
                    }
                    //条件不满足，不处理
                    continue;
                }

                //去重 根据配件类别+配件+主商品的维度去重
                if (partsEntrys.Any(o => Convert.ToString(o["fmaterialid"]).EqualsIgnoreCase(Convert.ToString(item["fmaterialid"]))
                 && Convert.ToString(o["fmaterialidnew"]).EqualsIgnoreCase(Convert.ToString(item["fmaterialidnew"]))
                 && Convert.ToString(o["fcategoryid"]).EqualsIgnoreCase(Convert.ToString(item["fcategoryid"]))
                )) continue;

                //将满足条件的配件明细返回
                partsEntrys.Add(item);
            }

            return partsEntrys;
        }

        /// <summary>
        /// 加载配件映射明细列表
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="partsTypeNames">配件类型名称集合</param>
        /// <returns>返回配件映射明细列表</returns>
        public DynamicObjectCollection LoadPartsMapingEntrys(UserContext userCtx, string productId, List<string> partsTypeNames, string allProductView)
        {
            if (productId.IsNullOrEmptyOrWhiteSpace()
                || partsTypeNames == null
                || !partsTypeNames.Any()) return null;

            var aclFilter = DataRowACLHelper.GetDataRowACLFilter(userCtx, "fm.");

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fproductid", System.Data.DbType.String, productId)
            };

            var nameWhere = "";
            if (partsTypeNames.Count == 1)
            {
                nameWhere = " and c.fname=@fname";
                sqlParam.Add(new SqlParam("@fname", System.Data.DbType.String, partsTypeNames[0]));
            }
            else
            {
                var paramNames = new List<string>();
                for (int i = 0; i < partsTypeNames.Count; i++)
                {
                    paramNames.Add($"@fname{i}");
                    sqlParam.Add(new SqlParam($"@fname{i}", System.Data.DbType.String, partsTypeNames[i]));
                }
                nameWhere = $" and c.fname in({string.Join(",", paramNames)})";
            }
            //铁架床不会匹配商品类别, 匹配到商品已经是最细维度了
            var TGCWhere = "";
            if (partsTypeNames.Any(o => o.EqualsIgnoreCase("铁架床"))) 
            {
                TGCWhere = "";
            }

            //商品类别过滤条件
            var productService = userCtx.Container.GetService<IProductService>();
            var categoryIdFilter = productService.BuildProductParentCategoryIdFilter(userCtx, productId, "fm.fcategoryid_d");

            var sqlText = $@"
            select t.fid,t.fname,fme.fconditions,fme.fpropid fattrinfo,fme.fqty,
                p.fid as fmaterialid,p.fname as fmaterialname,p.fnumber as fmaterialnumber,p.funstdtype,
                t.fcategoryid,t.fcategoryname,t.fcategorynumber,fme.fmaterialidnew
            from 
            (
	            select fm.fid,fm.fname,fm.fcategoryid,c.fname fcategoryname,c.fnumber fcategorynumber from t_sel_fittingsmap fm with(nolock) 
	            inner join ser_ydj_category c with(nolock) on c.fid=fm.fcategoryid 
	            inner join t_bd_material m with(nolock) on m.fid=@fproductid 
	            where fm.fforbidstatus='0' and m.fforbidstatus ='0' and fmatchbymodel='0'{categoryIdFilter}{nameWhere}{aclFilter} 
	            union
	            select fm.fid,fm.fname,fm.fcategoryid,c.fname fcategoryname,c.fnumber fcategorynumber from t_sel_fittingsmap fm with(nolock) 
	            inner join ser_ydj_category c with(nolock) on c.fid=fm.fcategoryid 
	            inner join t_bd_material m with(nolock) on m.fid=@fproductid 
	            inner join t_sel_dimensionmodelentry dme with(nolock) on dme.fid=fm.fid and dme.fseltypeid=m.fseltypeid
	            where fm.fforbidstatus='0' and m.fforbidstatus ='0' and fmatchbymodel='1'{categoryIdFilter}{nameWhere}{aclFilter} 
                union
	            select fm.fid,fm.fname,fm.fcategoryid,c.fname fcategoryname,c.fnumber fcategorynumber from t_sel_fittingsmap fm with(nolock) 
	            inner join ser_ydj_category c with(nolock) on c.fid=fm.fcategoryid 
	            inner join t_bd_material m with(nolock) on m.fid=@fproductid 
	            where fm.fforbidstatus='0' and m.fforbidstatus ='0' and fmatchbyproduct = '1'{TGCWhere}{nameWhere}{aclFilter} 
            ) t 
            inner join t_sel_fittingsmapentry fme with(nolock) on fme.fid=t.fid and fme.fdisable != 1
            inner join t_bd_material p with(nolock) on fme.fmaterialid = p.fid  AND p.fforbidstatus = '0' 
            where exists (select FPKId from ({allProductView})acl where p.fid=FPKId)
            ";

            var partsMapEntrys = this.DBService.ExecuteDynamicObject(userCtx, sqlText, sqlParam);

            return partsMapEntrys;
        }

        /// <summary>
        /// 检查匹配到的配件映射明细
        /// 如果属性中包含配件属性，但是又没有匹配到配件时，则直接提示错误（每个配件属性都要检查是否有匹配到配件）
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="propList">属性实体列表</param>
        /// <param name="partsMapingEntrys">匹配到的配件映射明细</param>
        /// <returns>返回检查结果</returns>
        public IOperationResult CheckPartsMapingEntrys(UserContext userCtx, List<PropEntity> propList, List<DynamicObject> partsMapingEntrys)
        {
            var result = new OperationResult();
            result.IsSuccess = true;

            //如果属性中包含配件属性，但是又没有匹配到配件时，则直接提示错误（每个配件属性都要检查是否有匹配到配件）
            var partsProps = propList.Where(o => o.IsParts).ToList();
            if (partsProps.Any())
            {
                //没有匹配到配件的属性名称
                var noMatchPartsPropNames = new List<string>();
                foreach (var partsProp in partsProps)
                {
                    var existsParts = partsMapingEntrys.FirstOrDefault(o =>
                        Convert.ToString(o["fcategoryname"]).Trim().EqualsIgnoreCase(partsProp.PropName?.Trim() ?? ""));
                    if (existsParts == null)
                    {
                        noMatchPartsPropNames.Add(partsProp.PropName);
                    }
                }
                if (noMatchPartsPropNames.Any())
                {
                    result.SimpleMessage = $"当前商品的配件属性【{string.Join("，", noMatchPartsPropNames)}】没有可添加的配件！";
                    result.IsSuccess = false;
                    return result;
                }
            }

            return result;
        }

        /// <summary>
        /// 匹配配件属性ID集合
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="propIds">属性ID集合</param>
        /// <returns>返回配件属性ID集合</returns>
        private List<string> MatchPartsPropIds(UserContext userCtx, List<string> propIds)
        {
            var partsPropIds = new List<string>();

            if (!propIds.Any()) return partsPropIds;

            var sqlParam = new List<SqlParam>();

            var aclFilter = DataRowACLHelper.GetDataRowACLFilter(userCtx);
            var sqlText = $@"select fid from t_sel_prop with(nolock) where fisparts='1'{aclFilter}";

            if (propIds.Count == 1)
            {
                sqlText += " and fid=@fid";
                sqlParam.Add(new SqlParam("@fid", System.Data.DbType.String, propIds[0]));
            }
            else
            {
                var paramNames = new List<string>();
                for (int i = 0; i < propIds.Count; i++)
                {
                    paramNames.Add($"@fid{i}");
                    sqlParam.Add(new SqlParam($"@fid{i}", System.Data.DbType.String, propIds[i]));
                }
                sqlText += $" and fid in({string.Join(",", paramNames)})";
            }

            using (var reader = this.DBService.ExecuteReader(userCtx, sqlText, sqlParam))
            {
                while (reader.Read())
                {
                    var partsPropId = reader.GetValueToString("fid");
                    partsPropIds.Add(partsPropId);
                }
            }

            return partsPropIds;
        }
    }
}
