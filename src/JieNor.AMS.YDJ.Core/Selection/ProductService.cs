using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data.SqlClient;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.CustomException;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Core
{
    /// <summary>
    /// 商品服务
    /// </summary>
    [InjectService]
    public class ProductService : IProductService
    {
        /// <summary>
        /// 数据库服务
        /// </summary>
        [InjectProperty]
        private IDBService DBService { get; set; }

        /// <summary>
        /// 构建指定商品的商品类别ID以及所有上级类别ID的过滤条件
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="fieldName">数据库字段名，比如：pf.fcategoryid</param>
        /// <param name="separator">分隔符，比如：and or</param>
        /// <returns>返回商品类别ID以及所有上级类别ID的过滤条件</returns>
        public string BuildProductParentCategoryIdFilter(UserContext userCtx, string productId, string fieldName = "", string separator = "and")
        {
            var categoryIds = this.LoadProductParentCategoryIds(userCtx, productId);
            if (!categoryIds.Any()) return $" {separator} {fieldName}=''";

            if (categoryIds.Count == 1)
            {
                return $" {separator} {fieldName}='{categoryIds[0]}'";
            }

            return $" {separator} {fieldName} in ('{string.Join("','", categoryIds)}')";
        }

        /// <summary>
        /// 递归获取指定商品的商品类别ID以及所有上级类别ID
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <returns>返回商品类别ID以及所有上级类别ID</returns>
        public List<string> LoadProductParentCategoryIds(UserContext userCtx, string productId)
        {
            var categoryIds = new List<string>();

            if (productId.IsNullOrEmptyOrWhiteSpace())
            {
                return categoryIds;
            }

            //向上递归查找某商品产品类别的所有上级
            var sqlText = $@"/*dialect*/
            with temp (fid,fparentid)
            as
            (
	            select c.fid,c.fparentid from ser_ydj_category c with(nolock) 
	            inner join t_bd_material m with(nolock) on m.fcategoryid=c.fid 
	            where m.fid=@fproductid
	            union all
	            select a.fid,a.fparentid from ser_ydj_category a with(nolock)
	            inner join temp on a.fid = temp.fparentid
            )
            select fid,fparentid from temp with(nolock)";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fproductid", System.Data.DbType.String, productId)
            };

            var dynObjs = this.DBService.ExecuteDynamicObject(userCtx, sqlText, sqlParam);
            if (dynObjs != null)
            {
                categoryIds = dynObjs.Select(o => Convert.ToString(o["fid"])).ToList();
            }

            return categoryIds;
        }

        /// <summary>
        /// 递归获取指定商品的商品类别ID以及所有上级类别ID
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productIds">商品ID</param>
        /// <returns>返回商品类别ID以及所有上级类别ID，商品ID为 Key，商品类别ID集合为 Value</returns>
        public Dictionary<string, List<string>> LoadProductParentCategoryIds(UserContext userCtx, IEnumerable<string> productIds)
        {
            var productCategoryIdKv = new Dictionary<string, List<string>>();

            if (productIds == null || !productIds.Any())
            {
                return productCategoryIdKv;
            }

            var _productIds = productIds.ToList();
            var sqlParam = new List<SqlParam>();

            //暂时不考虑商品超过2000个的情况，超过2000个只能用临时表了
            var where = "";
            var productIdCount = productIds.Count();
            if (productIdCount == 1)
            {
                where = "where m.fid=@fproductid";
                sqlParam.Add(new SqlParam("@fproductid", System.Data.DbType.String, _productIds[0]));
            }
            else
            {
                var paramNames = new List<string>();
                for (int i = 0; i < productIdCount; i++)
                {
                    sqlParam.Add(new SqlParam($"@fid{i}", System.Data.DbType.String, _productIds[i]));
                    paramNames.Add($"@fid{i}");
                }
                where = $" where m.fid in({string.Join(",", paramNames)})";
            }

            //向上递归查找某商品产品类别的所有上级
            var sqlText = $@"/*dialect*/
            with temp (fproductid,fid,fparentid)
            as
            (
	            select m.fid fproductid,c.fid,c.fparentid from ser_ydj_category c with(nolock)
	            inner join t_bd_material m with(nolock) on m.fcategoryid=c.fid 
	            {where}
	            union all
	            select temp.fproductid,a.fid,a.fparentid from ser_ydj_category a with(nolock)
	            inner join temp on a.fid = temp.fparentid
            )
            select fproductid,fid,fparentid from temp with(nolock) 
            order by fproductid";

            var dynObjs = this.DBService.ExecuteDynamicObject(userCtx, sqlText, sqlParam);
            if (dynObjs != null)
            {
                foreach (var dynObj in dynObjs)
                {
                    var productId = Convert.ToString(dynObj["fproductid"]);
                    var categoryId = Convert.ToString(dynObj["fid"]);

                    List<string> categoryIds = null;
                    productCategoryIdKv.TryGetValue(productId, out categoryIds);
                    if (categoryIds == null)
                    {
                        categoryIds = new List<string>();
                        productCategoryIdKv[productId] = categoryIds;
                    }
                    categoryIds.Add(categoryId);
                }
            }

            return productCategoryIdKv;
        }

        /// <summary>
        /// 获取指定商品类别上级类别集合
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="categoryId">商品类别</param>
        /// <returns></returns>
        public List<string> LoadProductParentCategoryIdsByID(UserContext userCtx, string categoryId)
        {
            var categoryIds = new List<string>();

            if (categoryId.IsNullOrEmptyOrWhiteSpace())
            {
                return categoryIds;
            }

            //向上递归查找某商品产品类别的所有上级
            var sqlText = $@"/*dialect*/
            with temp (fid,fparentid)
            as
            (
	            select c.fid,c.fparentid from ser_ydj_category c with(nolock) 
	            where c.fid = @fcategoryid
	            union all
	            select a.fid,a.fparentid from ser_ydj_category a with(nolock)
	            inner join temp on a.fid = temp.fparentid
            )
            select fid,fparentid from temp with(nolock)";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fcategoryid", System.Data.DbType.String, categoryId)
            };

            var dynObjs = this.DBService.ExecuteDynamicObject(userCtx, sqlText, sqlParam);
            if (dynObjs != null)
            {
                categoryIds = dynObjs.Select(o => Convert.ToString(o["fid"])).ToList();
            }
            return categoryIds;
        }

        /// <summary>
        /// 获取指定商品类别上级类别集合
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="categoryIds">商品类别</param>
        /// <returns></returns>
        public Dictionary<string, List<string>> LoadProductParentCategoryIdsByID(UserContext userCtx, IEnumerable<string> categoryIds)
        {
            var categoryIdKv = new Dictionary<string, List<string>>();

            if (categoryIds == null || !categoryIds.Any())
            {
                return categoryIdKv;
            }

            foreach (var categoryId in categoryIds)
            {
                categoryIdKv[categoryId] = LoadProductParentCategoryIdsByID(userCtx, categoryId);
            }

            //var sqls = new List<string>();

            //foreach (var categoryId in categoryIds)
            //{
            //    //向上递归查找某商品产品类别的所有上级
            //    var sqlText = $@"/*dialect*/
            //    with temp (fid,fparentid)
            //    as
            //    (
            //     select c.fid,c.fparentid from ser_ydj_category c with(nolock) 
            //     where c.fid = '{categoryId}'
            //     union all
            //     select a.fid,a.fparentid from ser_ydj_category a with(nolock)
            //     inner join temp on a.fid = temp.fparentid
            //    )
            //    select '{categoryId}' as fcategoryid, fid, fparentid from temp with(nolock)";

            //    sqls.Add(sqlText);
            //}
            //var dynObjs = this.DBService.ExecuteDynamicObject(userCtx, sqls.JoinEx(" union all ", false));
            //if (dynObjs != null)
            //{
            //    var grps = dynObjs.GroupBy(s => Convert.ToString(s["fcategoryid"]));
            //    foreach (var grp in grps)
            //    {
            //        categoryIdKv[grp.Key]?.AddRange(grp.Select(s => Convert.ToString(s["fid"])));
            //    }
            //}

            return categoryIdKv;
        }

        /// <summary>
        /// 加载指定商品的型号信息
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <returns>返回商品对应的型号信息</returns>
        public DynamicObject LoadProductSelType(UserContext userCtx, string productId)
        {
            var dynObjs = this.LoadProductSelType(userCtx, new List<string> { productId });
            return dynObjs?.FirstOrDefault();
        }

        /// <summary>
        /// 加载指定商品的型号信息
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productIds">商品ID集合</param>
        /// <returns>返回商品对应的型号信息</returns>
        public DynamicObjectCollection LoadProductSelType(UserContext userCtx, List<string> productIds)
        {
            if (productIds == null || !productIds.Any()) return null;

            var sqlText = $@"
            select m.fid fmaterialid,fstandardmaxheight,fstandardminheight,fgasbedmaxheight,fgasbedminheight,
            felectricmaxheight,felectricminheight from t_sel_type t with(nolock)
            inner join t_bd_material m with(nolock) on m.fseltypeid=t.fid";

            var sqlParam = new List<SqlParam>();

            productIds = productIds.Distinct().ToList();
            if (productIds.Count == 1)
            {
                sqlParam.Add(new SqlParam("@fid", System.Data.DbType.String, productIds[0]));
                sqlText += $" where m.fid=@fid";
            }
            else
            {
                var paramNames = new List<string>();
                for (int i = 0; i < productIds.Count; i++)
                {
                    sqlParam.Add(new SqlParam($"@fid{i}", System.Data.DbType.String, productIds[i]));
                    paramNames.Add($"@fid{i}");
                }
                sqlText += $" where m.fid in({string.Join(",", paramNames)})";
            }

            var dbService = userCtx.Container.GetService<IDBService>();
            var dynObjs = dbService.ExecuteDynamicObject(userCtx, sqlText, sqlParam);

            return dynObjs;
        }
    }
}
