using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core
{
    /// <summary>
    /// 属性实体类：用于属性选配时的公式匹配
    /// </summary>
    public class PropEntity
    {
        /// <summary>
        /// 属性ID
        /// </summary>
        public string PropId { get; set; } = string.Empty;

        /// <summary>
        /// 属性名称
        /// </summary>
        public string PropName { get; set; } = string.Empty;

        /// <summary>
        /// 属性编码
        /// </summary>
        public string PropNumber { get; set; } = string.Empty;

        /// <summary>
        /// 属性值ID
        /// </summary>
        public string ValueId { get; set; } = string.Empty;

        /// <summary>
        /// 属性值名称
        /// </summary>
        public string ValueName { get; set; } = string.Empty;

        /// <summary>
        /// 属性值编码
        /// </summary>
        public string ValueNumber { get; set; } = string.Empty;

        /// <summary>
        /// 属性值数据类型
        /// </summary>
        public PropValueDataTypeEnum PropValueDataType { get; set; } = PropValueDataTypeEnum.Char;

        /// <summary>
        /// 该属性是否是被约束的属性（主要是在【PC端】交互场景下用于处理匹配约束条件中的约束值公式）
        /// </summary>
        public bool IsRestricted { get; set; }

        /// <summary>
        /// 该属性是否是配件属性（在自动匹配配件时会给该属性赋值，目前主要用于选配确定后的检查）
        /// </summary>
        public bool IsParts { get; set; }

        /// <summary>
        /// 属性值是否是由非标录入时生成的
        /// </summary>
        public bool IsNosuitCreate { get; set; }
        /// <summary>
        /// 属性是否影响价格
        /// </summary>
        public bool IsControlPrice { get; set; }
    }

    /// <summary>
    /// 属性值数据类型枚举
    /// </summary>
    public enum PropValueDataTypeEnum
    {
        /// <summary>
        /// 无
        /// </summary>
        None = 0,

        /// <summary>
        /// 字符
        /// </summary>
        Char = 1,

        /// <summary>
        /// 数值
        /// </summary>
        Numeric = 2
    }
}
