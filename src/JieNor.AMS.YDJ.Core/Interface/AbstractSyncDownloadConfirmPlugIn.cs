using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using JieNor.Framework.DataTransferObject.Integration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 下载插件基类
    /// </summary>
    public class AbstractSyncDownloadConfirmPlugIn : AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);
            switch (e.EventName.ToLower())
            {
                case "savedataentities":
                    saveDataEntities(e);
                    break;
                case "dealdataentities":
                    dealDataEntities(e);
                    break;
                case "getformlinkchaindata":
                    getFormLinkChainData(e);
                    break;
                case "loadsqls":
                    loadSqls(e);
                    break;
                case "parsechaindatainfos":
                    parseChainDataInfos(e);
                    break;
                case "dealdatacontents":
                    dealDataContents(e);
                    break;
                case "undistribute_pulloff":
                    unDistribute(e, UnDistributeDownload);
                    break;
                case "undistribute_forbidauxset":
                    unDistribute(e, UnDistributeForbidAuxSet);
                    break;
                default:
                    return;
            }
        }

        #region 子类可重写事件

        /// <summary>
        /// 保存数据事件
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="htmlForm"></param>
        /// <param name="dataEntities"></param>
        /// <param name="rollbackEntities"></param>
        /// <param name="billDatas"></param>
        /// <param name="cooOperationModes"></param>
        /// <param name="chainDataIdToLocalDataIdMaps"></param>
        /// <returns></returns>
        protected virtual bool SaveDataEntities(UserContext userContext,
                                                HtmlForm htmlForm,
                                                List<DynamicObject> dataEntities,
                                                List<DynamicObject> rollbackEntities,
                                                List<Dictionary<string, object>> billDatas,
                                                List<Dictionary<string, string>> cooOperationModes,
                                                List<ChainDataIdToLocalDataIdMapsSet> chainDataIdToLocalDataIdMaps)
        {
            return false;
        }

        /// <summary>
        /// 处理非表单数据事件，例如商品中包含辅助属性的数据，要通过此事件将辅助属性的数据解析出来
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="htmlForm"></param>
        /// <param name="billDatas"></param>
        /// <param name="cooOperationModes"></param>
        protected virtual void DealDataEntities(UserContext userContext,
                                                HtmlForm htmlForm,
                                                List<Dictionary<string, object>> billDatas,
                                                List<Dictionary<string, string>> cooOperationModes)
        {

        }

        /// <summary>
        /// 获取表单的依赖数据，例如商品包含辅助属性的信息，那么商品发布包的LinkChainData也将包含辅助属性的依赖数据，所以要将商品依赖数据解析出来
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="htmlForm"></param>
        /// <param name="dataContent"></param>
        /// <param name="linkChainData"></param>
        /// <returns></returns>
        protected virtual Dictionary<string, string> GetFormLinkChainData(UserContext userContext,
                                                                         HtmlForm htmlForm,
                                                                         JObject dataContent,
                                                                         Dictionary<string, string> linkChainData)
        {
            return linkChainData;
        }

        /// <summary>
        /// 批量加载本地云链数据的sql语句
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="htmlForm"></param>
        /// <param name="tableName"></param>
        /// <param name="sqls"></param>
        /// <returns></returns>
        protected virtual bool LoadSqls(UserContext userContext, HtmlForm htmlForm, string tableName,string isneedloadentity, out List<string> sqls)
        {
            sqls = new List<string>();
            return false;
        }

        /// <summary>
        /// 分析出所有需要查询的已下载云链数据
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="htmlForm"></param>
        /// <param name="linkChainData"></param>
        /// <param name="chainDataInfos"></param>
        /// <returns></returns>
        protected virtual bool ParseChainDataInfos(UserContext userContext,
                                                   HtmlForm htmlForm,
                                                   Dictionary<string, string> linkChainData,
                                                   out List<Dictionary<string, object>> chainDataInfos)
        {
            chainDataInfos = new List<Dictionary<string, object>>();
            return false;
        }

        /// <summary>
        /// 是否取消分析LinkChainData的数据
        /// </summary>
        /// <returns></returns>
        protected virtual bool IsCancelParseLinkChainData()
        {
            return false;
        }

        /// <summary>
        /// 下架或禁用事件
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="htmlForm"></param>
        /// <param name="billDatas"></param>
        /// <param name="chainDataIdToLocalDataIdMaps"></param>
        /// <returns></returns>
        protected virtual List<Dictionary<string, string>> UnDistributeDownload(UserContext userContext,
                                                                               HtmlForm htmlForm,
                                                                               List<Dictionary<string, object>> billDatas,
                                                                               List<ChainDataIdToLocalDataIdMapsSet> chainDataIdToLocalDataIdMaps)
        {
            return new List<Dictionary<string, string>>();
        }

        /// <summary>
        /// 禁用辅助属性事件
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="htmlForm"></param>
        /// <param name="billDatas"></param>
        /// <param name="chainDataIdToLocalDataIdMaps"></param>
        /// <returns></returns>
        protected virtual List<Dictionary<string, string>> UnDistributeForbidAuxSet(UserContext userContext,
                                                                                    HtmlForm htmlForm,
                                                                                    List<Dictionary<string, object>> billDatas,
                                                                                    List<ChainDataIdToLocalDataIdMapsSet> chainDataIdToLocalDataIdMaps)
        {
            return new List<Dictionary<string, string>>();
        }

        /// <summary>
        /// 处理发布的原始数据包
        /// </summary>
        /// <param name="e"></param>
        protected virtual void DealDataContents(UserContext userContext, HtmlForm htmlForm, List<JObject> dataContents)
        {
        }

        #endregion

        #region 解析自定义事件的参数和返回值
        /// <summary>
        /// 处理发布的原始数据包
        /// </summary>
        /// <param name="e"></param>
        private void dealDataContents(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Dictionary<string, object>;
            if (eventData == null || eventData.Count <= 0)
            {
                return;
            }

            var htmlForm = eventData["htmlForm"] as HtmlForm;
            var userContext = eventData["userContext"] as UserContext;
            var dataContents = eventData["dataContents"] as List<JObject>;

            if (htmlForm == null ||
                userContext == null ||
                dataContents == null ||
                dataContents.Count <= 0)
            {
                return;
            }

            DealDataContents(userContext, htmlForm, dataContents);
        }

        /// <summary>
        /// 取消发布事件
        /// </summary>
        /// <param name="e"></param>
        /// <param name="action"></param>
        private void unDistribute(OnCustomServiceEventArgs e,
                                  Func<UserContext,
                                       HtmlForm,
                                       List<Dictionary<string, object>>,
                                       List<ChainDataIdToLocalDataIdMapsSet>,
                                       List<Dictionary<string, string>>> action)
        {
            var eventData = e.EventData as Dictionary<string, object>;
            if (eventData == null || eventData.Count <= 0 || action == null)
            {
                return;
            }

            var userContext = eventData["userContext"] as UserContext;
            var chainDataIdToLocalDataIdMaps = eventData["chainDataIdToLocalDataIdMaps"] as List<ChainDataIdToLocalDataIdMapsSet>;
            var htmlForm = eventData["htmlForm"] as HtmlForm;
            var billDatas = eventData["billDatas"] as List<Dictionary<string, object>>;

            if (userContext == null ||
                chainDataIdToLocalDataIdMaps == null ||
                chainDataIdToLocalDataIdMaps.Count <= 0 ||
                htmlForm == null ||
                billDatas == null ||
                billDatas.Count <= 0)
            {
                return;
            }

            var result = action(userContext, htmlForm, billDatas, chainDataIdToLocalDataIdMaps);
            e.Result = result;
        }

        /// <summary>
        /// 分析出所有需要查询的已下载云链数据
        /// </summary>
        /// <param name="e"></param>
        private void parseChainDataInfos(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Dictionary<string, object>;
            if (eventData == null || eventData.Count <= 0)
            {
                return;
            }

            var htmlForm = eventData["htmlForm"] as HtmlForm;
            var userContext = eventData["userContext"] as UserContext;
            var linkChainData = eventData["linkChainData"] as Dictionary<string, string>;

            if (htmlForm == null ||
                userContext == null ||
                linkChainData == null ||
                linkChainData.Count <= 0)
            {
                return;
            }

            List<Dictionary<string, object>> chainDataInfos = null;
            var result = ParseChainDataInfos(userContext, htmlForm, linkChainData, out chainDataInfos);
            e.Result = new Dictionary<string, object>
            {
                { "isSuccess",result},
                { "chainDataInfos", chainDataInfos}
            };
            e.Cancel = IsCancelParseLinkChainData();
        }

        /// <summary>
        /// 批量加载本地云链数据的sql语句
        /// </summary>
        /// <param name="e"></param>
        private void loadSqls(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Dictionary<string, object>;
            if (eventData == null || eventData.Count <= 0)
            {
                return;
            }

            var htmlForm = eventData["htmlForm"] as HtmlForm;
            var userContext = eventData["userContext"] as UserContext;
            var tableName = eventData["tableName"] as string;
            var isneedloadentity = eventData["isneedloadentity"] as string ;
            if (htmlForm == null ||
                userContext == null ||
                string.IsNullOrWhiteSpace(tableName))
            {
                return;
            }

            List<string> sqls = null;
            var result = LoadSqls(userContext, htmlForm, tableName, isneedloadentity, out sqls);
            e.Result = new Dictionary<string, object>
            {
                { "sqls", sqls}
            };
            e.Cancel = result;
        }

        /// <summary>
        /// 获取表单的依赖数据，例如商品包含辅助属性的信息，那么商品发布包的LinkChainData也将包含辅助属性的依赖数据，所以要将商品依赖数据解析出来
        /// </summary>
        /// <param name="e"></param>
        private void getFormLinkChainData(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Dictionary<string, object>;
            if (eventData == null || eventData.Count <= 0)
            {
                return;
            }

            var htmlForm = eventData["htmlForm"] as HtmlForm;
            var userContext = eventData["userContext"] as UserContext;
            var dataContent = eventData["dataContent"] as JObject;
            var linkChainData = eventData["linkChainData"] as Dictionary<string, string>;

            if (htmlForm == null ||
                userContext == null ||
                dataContent == null ||
                linkChainData == null ||
                linkChainData.Count <= 0)
            {
                return;
            }

            var result = GetFormLinkChainData(userContext, htmlForm, dataContent, linkChainData);
            e.Result = result;
        }

        /// <summary>
        /// 处理非表单数据事件，例如商品中包含辅助属性的数据，要通过此事件将辅助属性的数据解析出来
        /// </summary>
        /// <param name="e"></param>
        private void dealDataEntities(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Dictionary<string, object>;
            if (eventData == null || eventData.Count <= 0)
            {
                return;
            }
            var htmlForm = eventData["htmlForm"] as HtmlForm;
            var userContext = eventData["userContext"] as UserContext;
            var billDatas = eventData["billDatas"] as List<Dictionary<string, object>>;
            var cooOperationModes = eventData["cooOperationModes"] as List<Dictionary<string, string>>;

            if (htmlForm == null ||
                userContext == null ||
                billDatas == null ||
                billDatas.Count <= 0 ||
                cooOperationModes == null)
            {
                return;
            }

            DealDataEntities(userContext, htmlForm, billDatas, cooOperationModes);
        }

        /// <summary>
        /// 保存数据事件
        /// </summary>
        /// <param name="e"></param>
        private void saveDataEntities(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Dictionary<string, object>;
            if (eventData == null || eventData.Count <= 0)
            {
                return;
            }
            var htmlForm = eventData["htmlForm"] as HtmlForm;
            var userContext = eventData["userContext"] as UserContext;
            var dataEntities = eventData["dataEntities"] as List<DynamicObject>;
            var rollbackEntities = eventData["rollbackEntities"] as List<DynamicObject>;
            var billDatas = eventData["billDatas"] as List<Dictionary<string, object>>;
            var cooOperationModes = eventData["cooOperationModes"] as List<Dictionary<string, string>>;
            var chainDataIdToLocalDataIdMaps = eventData["chainDataIdToLocalDataIdMaps"] as List<ChainDataIdToLocalDataIdMapsSet>;

            if (htmlForm == null ||
                userContext == null ||
                dataEntities == null ||
                dataEntities.Count <= 0 ||
                rollbackEntities == null ||
                billDatas == null ||
                billDatas.Count <= 0 ||
                cooOperationModes == null ||
                chainDataIdToLocalDataIdMaps == null)
            {
                return;
            }

            var result = SaveDataEntities(userContext, htmlForm, dataEntities, rollbackEntities, billDatas, cooOperationModes, chainDataIdToLocalDataIdMaps);

            if (result)
            {
                e.Result = "OK";
            }
        }

        #endregion
    }
}
