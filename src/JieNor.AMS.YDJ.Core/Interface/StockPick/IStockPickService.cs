using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.AMS.YDJ.Core.Interface.StockPick
{
    /// <summary>
    /// 库存拣货服务接口定义
    /// </summary>
    public interface IStockPickService
    {
        /// <summary>
        /// 库存拣货
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="setting"></param>
        /// <param name="billForm"></param>
        /// <param name="billData"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        IOperationResult Picking(UserContext userCtx, StockPickSetting setting, HtmlForm billForm, IEnumerable<DynamicObject> billData, OperateOption option);
    }
}