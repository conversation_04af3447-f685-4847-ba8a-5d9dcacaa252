using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.DataTransferObject.Reserve;

namespace JieNor.AMS.YDJ.Core.Interface.StockReserve
{
    /// <summary>
    /// 预留借货服务接口定义
    /// </summary>
    public interface IReserveBorrowService
    {
        /// <summary>
        /// 预留借货
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="demandForm"></param>
        /// <param name="reserveBorrowSettingInfos"></param>
        /// <param name="option"></param>
        IOperationResult Borrow(UserContext userCtx, HtmlForm demandForm, List<ReserveBorrowSettingInfo> reserveBorrowSettingInfos, OperateOption option);
         


    }
}