using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.AMS.YDJ.Core.Interface.StockReserve
{
    /// <summary>
    /// 预留释放服务接口定义
    /// </summary>
    public interface IReserveReleaseService
    {

        /// <summary>
        /// 预留释放
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="setting"></param>
        /// <param name="billForm"></param>
        /// <param name="billDatas"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        IOperationResult Release(UserContext userCtx, ReserveReleaseSetting setting, HtmlForm billForm, IEnumerable<DynamicObject> billDatas, OperateOption option);


        /// <summary>
        /// 获取包含有过期预留信息的预留单Id（所有经销商销售商下的）
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        List<DynamicObject> GetExpirerReserveBills(UserContext ctx);

        /// <summary>
        /// 释放过期的预留单
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        IOperationResult ReleaseExpirer(UserContext userCtx, OperateOption option = null);


        /// <summary>
        /// 释放过期的预留单
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        IOperationResult ReleaseExpirer(UserContext userCtx,List <DynamicObject> reserveBillIds, OperateOption option = null);

        /// <summary>
        /// 预留更新后检查库存情况
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="billForm"></param>
        /// <param name="lstSelRows"></param>
        /// <param name="setting"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        IOperationResult CheckInventory(UserContext userCtx, HtmlForm billForm, IEnumerable<SelectedRow> lstSelRows, ReserveReleaseSetting setting, OperateOption option);




    }
}