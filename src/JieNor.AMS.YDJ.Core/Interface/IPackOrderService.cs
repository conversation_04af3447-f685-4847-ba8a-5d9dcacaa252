using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 包装清单服务接口
    /// </summary>
    public interface IPackOrderService
    {
        /// <summary>
        /// 打包服务
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="packData"></param>
        /// <param name="errorMsg"></param>
        /// <returns></returns>
        Dictionary<string, object> CreatePackage(UserContext userContext, List<Dictionary<string, object>> packData, List<string> errorMsg);

        /// <summary>
        /// 根据扫描记录将源单下推到相应下游单据
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="scanResultEntities"></param>
        /// <param name="errorMsg"></param>
        void PushBySanResult(UserContext userContext, List<DynamicObject> scanResultEntities, List<string> errorMsg);
    }
}
