using JieNor.AMS.YDJ.Core.DataEntity.Barcode;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.Interface.BarcodeMgr
{
    /// <summary>
    /// 条码相关的管理服务类
    /// </summary>
    public interface IBarcodeMgrService
    {
        /// <summary>
        /// 解析条码数据，返回按库存维度进行汇总的数据信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="barcodeList"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        Dictionary<string, ResolvedBarcodeView> ResolveBarcode(UserContext userCtx, IEnumerable<string> barcodeList, OperateOption option);

        /// <summary>
        /// 将条码解析的信息反向归集到关联的库存单据数据对象行上（主要是更新实收数量）
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="dataEntities"></param>
        /// <param name="matchOption"></param>
        /// <returns>
        /// |--Item1:所有传入条码解析结果（是否匹配，是否消耗等）
        /// |--Item2:具体匹配的明细情况
        ///     |--Array:0
        ///         |--Item1:消耗的条码对象
        ///         |--Item2:条码匹配的分录对象
        ///         |--Item3:条码匹配的单据对象
        /// </returns>
        Tuple<Dictionary<string, ResolvedBarcodeView>, Dictionary<DynamicObject, Dictionary<ExtendedDataEntity, List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>>>> MatchInvBillRowQty(UserContext userCtx, HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntities, MatchBarcodeOption matchOption);

        /// <summary>
        /// 获取业务通知单据关联的条码信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="billPkIds"></param>
        /// <returns></returns>
        Dictionary<string, IEnumerable<string>> GetLinkBarcode(UserContext userCtx, HtmlForm htmlForm, IEnumerable<object> billPkIds);

        /// <summary>
        /// 此方法通常在单据转换插件中发起调用：针对下推实际出入库单据时，根据来源单信息关联的包装清单或条码记录信息自动拆分源单明细，以达到生成匹配下游出入库单据分录的目的。
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm">源单据模型</param>
        /// <param name="sourceBillRows">源单据数据集合</param>
        /// <param name="matchOption">拆分过程中匹配选项参数</param>
        IEnumerable<Dictionary<string, object>> SplitInoutStockRowByBarcode(UserContext userCtx, HtmlForm htmlForm, IEnumerable<Dictionary<string,object>> sourceBillRows, MatchBarcodeOption matchOption);

        /// <summary>
        /// 获取表单关联的扫描记录对象
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="dataEntity"></param>
        /// <param name="linkOption"></param>
        /// <returns></returns>
        IEnumerable<DynamicObject> GetBillLinkScanResultData(UserContext userCtx, HtmlForm htmlForm, DynamicObject dataEntity, ScanResultLinkOption linkOption);

        /// <summary>
        /// 更新条码状态
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="barcodeTraceInfo"></param>
        /// <param name="barcodeScanObjs"></param>
        /// <param name="status"></param>
        void UpdateBarcodeStatus(UserContext userCtx, UpdateBarcodeTraceBill barcodeTraceInfo, IEnumerable<DynamicObject> barcodeScanObjs, Enu_BarcodeStatus status);
    }
}
