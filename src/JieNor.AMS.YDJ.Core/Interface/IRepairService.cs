using JieNor.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 修正服务
    /// </summary>
    public interface IRepairService
    {
        /// <summary>
        /// 修正选单时保存后，对应的表头源单号无有效对应明细行的情况数据
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="dataEntitys">实体</param>
        /// <param name="billHeadSourceNumField">表头源单据编号字段fn</param>
        /// <param name="billHeadSourceIdField">表头源单据id字段fn，无则不会更新</param>
        /// <param name="activeEntityKey">活动实体明细entry form id</param>
        /// <param name="activeSourceNumField">活动实体明细entry 源单号 fn</param>
        /// <param name="activeSourceIdField">活动实体明细entry 源单id fn</param>
        void RepairBillHeadSourceNum(UserContext userCtx,
            DynamicObject[] dataEntitys,
            string billHeadSourceNumField="fsourcenumber",
            string billHeadSourceIdField = "fsourceinterid",
            string activeEntityKey="fentity",
            string activeSourceNumField="fsourcebillno",
            string activeSourceIdField = "fsourceinterid");
    }
}
