using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 慕思AI云接口定义
    /// </summary>
    public interface IMuSiAIService
    {
        /// <summary>
        /// 获取问卷
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        JObject GetQuestion(UserContext userCtx);

        /// <summary>
        /// 同步到慕思AI云
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="purchaseOrders"></param>
        void SyncToAI(UserContext userCtx, HtmlForm formMeta, IEnumerable<DynamicObject> purchaseOrders, OperateOption option);

        /// <summary>
        /// 获取未同步的AI床垫的明细行
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="purchaseOrders"></param>
        /// <returns></returns>
        List<DynamicObject> GetUnSyncAIBedEntry(UserContext userCtx, IEnumerable<DynamicObject> purchaseOrders);

        /// <summary>
        /// 检查AI床垫辅助属性必录
        /// </summary>
        /// <returns></returns>
        bool CheckAIBedAttrInfoMust(UserContext userCtx, DynamicObject auxPropValSet);

        /// <summary>
        /// 加载商品选配类别的默认属性值集
        /// </summary>
        Dictionary<string, List<Dictionary<string, string>>> LoadProductAuxPropVals(UserContext userCtx, Dictionary<string, Dictionary<string, string>> aiBedOrders);

        /// <summary>
        /// 获取AI床垫属性值
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="aiBedOrders"></param>
        /// <returns></returns>
        Dictionary<string, List<Dictionary<string, string>>> GetAuxPropVals(UserContext userCtx, List<DynamicObject> aiBedOrders, Dictionary<string, string> cids);

        /// <summary>
        /// 是否此属性
        /// </summary>
        /// <param name="fpropid"></param>
        /// <returns></returns>
        bool IsProp(UserContext userCtx, string fpropid, Tuple<string, string> prop);

        Tuple<string, string> GetProp_CID();

        Tuple<string, string> GetProp_CustomParam();

        /// <summary>
        /// 获取客户ID
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="aiBedOrders"></param>
        /// <returns></returns>
        Dictionary<string, string> GetCIDs(UserContext userCtx, List<DynamicObject> aiBedOrders);

        /// <summary>
        /// 获取授权组织（为空表示全部）
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        List<string> GetAuthorizedOrgIds(UserContext userCtx);
    }
}
