using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 可销控制
    /// </summary>
    public interface ISalesControl
    {
        //void ClearCache(UserContext ctx);



        /// <summary>
        /// 找出有权限的仓库：从销售员可销控制中查找----范围是仓库 可销对象是销售员 控制类型是允销的权限控制表
        /// </summary>
        /// <returns>item1--销售员id，item2---有权限的仓库id,item3 -- 受控的业务模型标识</returns>
       List<Tuple<string, string, string>> GetCanSalHousePerm(UserContext ctx);

    }
}