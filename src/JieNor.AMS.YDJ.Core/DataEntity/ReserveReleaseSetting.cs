using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.DataEntity
{
    /// <summary>
    /// 预留释放设置参数
    /// </summary>
    [Serializable]
    public class ReserveReleaseSetting
    {
        /// <summary>
        /// 预留释放时的前置条件
        /// </summary>
        public string PreCondition { get; set; }

        /// <summary>
        /// 当前预留服务作用的实体标识
        /// </summary>
        public string ActiveEntityKey { get; set; }

        /// <summary>
        /// 预留释放方式 <see cref="Enu_ReleaseWay"/> 2--手工释放，4--单据关闭释放 ，5---单据作废释放，6---自动释放
        /// </summary>
        public int ReleaseWay { get; set; }

        /// <summary>
        /// 预留释放类型 <see cref="Enu_ReleaseType"/> 0表示释放，1表示取消释放
        /// </summary>
        public int ReleaseType { get; set; }

        /// <summary>
        /// 是否释放没有源单的预留单（手工新增的预留单）
        /// </summary>
        public bool IsReleaseNoSource { get; set; } = true;

        /// <summary>
        /// 源单类型字段标识
        /// </summary>
        public string SourceTypeFieldKey { get; set; }

        /// <summary>
        /// 源单编号字段标识
        /// </summary>
        public string SourceNumberFieldKey { get; set; }

        /// <summary>
        /// 客户字段标识
        /// </summary>
        public string CustomerFieldKey { get; set; }

        /// <summary>
        /// 部门字段标识
        /// </summary>
        public string DeptFieldKey { get; set; }

        /// <summary>
        /// 员工字段标识
        /// </summary>
        public string StaffFieldKey { get; set; }

        /// <summary>
        /// 数量字段标识
        /// </summary>
        public string QtyFieldKey { get; set; }

        /// <summary>
        /// 预留维度字段映射表
        /// </summary>
        public List<ReserveFlexMap> FlexMaps { get; set; } = new List<ReserveFlexMap>();

        /// <summary>
        /// 选中的行id（主要是销售合同上的行关闭及行反关闭操作时用到）
        /// </summary>
        public List<string> SelectEntryRow { get; set; } = new List<string>();

        /// <summary>
        /// 手动释放项目
        /// </summary>
        public List<ReserveManualReleaseItem> ManualReleaseItems { get; set; } = new List<ReserveManualReleaseItem>();

        /// <summary>
        /// 预留操作提示信息
        /// </summary>
        public string Message { get; set; }

    }

    /// <summary>
    /// 预留维度字段映射对象
    /// </summary>
    [Serializable]
    public class ReserveFlexMap
    {
        /// <summary>
        /// 预留单字段标识
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 来源单字段标识
        /// </summary>
        public string SrcFieldId { get; set; }

        /// <summary>
        /// 是否严格匹配
        /// </summary>
        public bool IsStrictMatch { get; set; }
    }

    /// <summary>
    /// 预留手动释放项目
    /// </summary>
    [Serializable]
    public class ReserveManualReleaseItem
    {
        /// <summary>
        /// 预留单id
        /// </summary>
        public string ReserveId { get; set; }

        /// <summary>
        /// 预留明细行id
        /// </summary>
        public string ReserveEntryId { get; set; }

        /// <summary>
        /// 来源单类型
        /// </summary>
        public string SourceFormId { get; set; }

        /// <summary>
        /// 来源单内码
        /// </summary>
        public string SourceInterId { get; set; }

        /// <summary>
        /// 来源单分录内码
        /// </summary>
        public string SourceEntryId { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        public string StoreHouseId { get; set; }

        /// <summary>
        /// 库存状态
        /// </summary>
        public string StockStatusId { get; set; }

        /// <summary>
        /// 释放数量
        /// </summary>
        public decimal ReleaseQty { get; set; }

        /// <summary>
        /// 自定义预留操作说明
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 最大释放数量（剩余可释放数量）
        /// </summary>
        public decimal MaxReleaseQty { get; set; }
    }

    /// <summary>
    /// 预留释放方式：2--手工释放，4--单据关闭释放 ，5---单据作废释放，6---自动释放
    /// </summary>
    public enum Enu_ReleaseWay
    {
        /// <summary>
        /// 自动释放
        /// </summary>
        Auto = 6,
        /// <summary>
        /// 手动释放
        /// </summary>
        Manual = 2,

        /// <summary>
        /// 单据关闭释放
        /// </summary>
        Close = 4,

        /// <summary>
        /// 单据作废释放
        /// </summary>
        Cancel = 5,
    }

    /// <summary>
    /// 预留释放类型
    /// </summary>
    public enum Enu_ReleaseType
    {
        /// <summary>
        /// 释放
        /// </summary>
        Release = 0,
        /// <summary>
        /// 取消释放
        /// </summary>
        CancelRelease = 1
    }
}