using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.DataEntity.Inventory
{
    /// <summary>
    /// 库存维度模型
    /// </summary>
    [Serializable]
    public class InventoryFlexModel
    {
        /// <summary>
        /// 目前系统所有库存单据
        /// </summary>
        public static List<string> AllStockBillFormIds { get; set; } = new List<string>()
        {
            "stk_postockin",
            "stk_postockreturn",
            "stk_sostockout",
            "stk_sostockreturn",
            "stk_otherstockin",
            "stk_otherstockout",
            "stk_inventorytransfer",
            "stk_inventoryverify"
        };

        /// <summary>
        /// 初始库存单据标识
        /// </summary>
        public static string InitStockBillFormId = "stk_initstockbill";

        
        /// <summary>
        /// 即时库存中不作为库存维度的字段
        /// </summary>
        private static List<string> IgnoreInvFlexFieldKeys = new List<string>(){
                    "fqty",
                    "famount",
                    "fstockqty",
                    "fmainorgid",
                    "ftranid",
                    "fcostprice",
                    "fcostamt",
                    "fvolumeunit",
                    "ftotalvolume",
                    "fsinglevolume",
                    //改成通过fattrinfo_e判断库存，所以fattrinfo从库存维度拿掉
                    "fattrinfo",
                    "fname_e",
                };

        private static InventoryFlexModel Instance = null;
        private static object lockObj = new object();

        private InventoryFlexModel()
        {
            this.QtyFieldKey = "fqty";
            this.StockQtyFieldKey = "fstockqty";
            this.AmountFieldKey = "famount";
            this.MainOrgIdFieldKey = "fmainorgid";

            this.InvFlexFieldKeys = new ReadOnlyCollection<string>(invFlexFieldKeys);
        }

        private List<string> invFlexFieldKeys = new List<string>();

        /// <summary>
        /// 获取库存维度模型实例（单例实现）
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public static InventoryFlexModel GetInstance(UserContext userCtx)
        {
            if (Instance == null)
            {
                lock (lockObj)
                {
                    var invFormMeta = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, "stk_inventorylist");
                    if (invFormMeta == null)
                    {
                        throw new BusinessException("系统出现意外错误：请检查stk_inventorylist表单模型是否正确！");
                    }
                    if (Instance == null)
                    {
                        Instance = new InventoryFlexModel();

                        var allInvFields = invFormMeta.GetFieldList();
                        foreach (var field in allInvFields)
                        {
                            if (IgnoreInvFlexFieldKeys.Contains(field.Id)
                                || field is HtmlBasePropertyField
                                || field is HtmlButtonField
                                || !(field.Entity is HtmlHeadEntity)
                                || field is HtmlFileField
                                || field is HtmlMulFileField) continue;
                            Instance.invFlexFieldKeys.Add(field.Id);
                        }
                    }
                }
            }
            return Instance;
        }


        /// <summary>
        /// 库存维度字段标识集合
        /// </summary>
        public ReadOnlyCollection<string> InvFlexFieldKeys { get; private set; } 

        /// <summary>
        /// 数量字段标识
        /// </summary>
        public string QtyFieldKey { get; set; }
        /// <summary>
        /// 辅单位数量字段标识
        /// </summary>
        public string StockQtyFieldKey { get; set; }
        /// <summary>
        /// 金额字段标识
        /// </summary>
        public string AmountFieldKey { get; set; }

        /// <summary>
        /// 企业标识字段
        /// </summary>
        public string MainOrgIdFieldKey { get; set; }
    }
}
