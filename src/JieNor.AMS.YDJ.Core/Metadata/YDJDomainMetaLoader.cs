using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.Metadata
{
    [InjectService]
    public class YDJDomainMetaLoader : IDomainMetaTypeLoader
    {
        public Assembly[] LoadMetaAssembly()
        {
            return new Assembly[]
            {
                Assembly.GetExecutingAssembly()
            };
        }
    }
}
