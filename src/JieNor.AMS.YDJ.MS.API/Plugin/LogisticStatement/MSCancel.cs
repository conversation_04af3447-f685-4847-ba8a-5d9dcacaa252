using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MS.API.Model;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using JieNor.Framework.MetaCore.FormOp;

namespace JieNor.AMS.YDJ.MS.API.Plugin.LogisticStatement
{
    /// <summary>
    /// 总部物流对账单：作废
    /// </summary>
    [InjectService]
    [FormId("ydj_logisticsstatement")]
    [OperationNo("MSCancel")]
    public class MSCancel : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 重复提交编码
        /// </summary>
        private List<string> repeatedNumbers = new List<string>();

        /// <summary>
        /// 编码字段
        /// </summary>
        protected HtmlField NumberField { get; set; }
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
        }

        /// <summary>
        /// 初始化服务插件上下文时触发的事件
        /// </summary>
        /// <param name="operCtx"></param>
        /// <param name="serviceList"></param>
        public new void InitializeOperationContext(OperationContext operCtx, params object[] serviceList)
        {
            // 启用幂等性检查
            var serCtrlOpt = serviceList.FirstOrDefault(o => o is ServiceControlOption) as ServiceControlOption;
            serCtrlOpt.SupportIdemotency = false;//不做锁定校验
            ServiceControlOption.EnableDistributedLock = false;//不做锁定校验

            base.InitializeOperationContext(operCtx, serviceList);
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var dataStr = this.GetQueryOrSimpleParam("data", "");
            JArray billNos = null;
            try
            {
                billNos = JArray.Parse(dataStr);
            }
            catch (Exception ex)
            {
                throw new BusinessException("参数格式不正确！");
            }

            IOperationResult result = null;
            if (billNos.Any())
            {
                var bills = this.Context.LoadBizDataByNo(this.HtmlForm.Id, "fbillno", billNos.Select(s => s.ToString()));
                if (bills.Any())
                {
                    MuSiData resp = new MuSiData();
                    result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, bills, "Cancel", this.Option.ToDictionary());
                    HandleResult(result, bills, resp);
                    this.Result.SrvData = resp;
                }
            }
            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "操作成功！";
        }


        /// <summary>
        /// 处理操作结果
        /// </summary>
        /// <param name="result"></param>
        private void HandleResult(IOperationResult result, IEnumerable<DynamicObject> dataEntities, MuSiData resp)
        {
            //MuSiData resp = new MuSiData();
            // 重复提交=成功
            resp.SucceedNumbers.AddRange(this.repeatedNumbers);

            if (result.IsSuccess && !result.ComplexMessage.ErrorMessages.Any())
            {
                this.OperationContext.Result.IsSuccess = true;
                this.OperationContext.Result.SimpleMessage = $"【{this.HtmlForm.Caption}】操作成功！";

                resp.SucceedNumbers.AddRange(dataEntities.Select(s => Convert.ToString(s["fbillno"])));
            }
            else
            {
                this.OperationContext.Result.IsSuccess = false;
                this.OperationContext.Result.SimpleMessage = $"【{this.HtmlForm.Caption}】操作失败！";

                IEnumerable<DynamicObject> failedDataEntities = null;

                result.SimpleData.TryGetValue("pkids", out var pkidsStr);

                // 过滤失败的，然后移除幂等标志
                if (pkidsStr.IsNullOrEmptyOrWhiteSpace())
                {
                    failedDataEntities = dataEntities;

                    resp.FailedNumbers.AddRange(dataEntities.Select(s => Convert.ToString(s["fbillno"])));
                }
                else
                {
                    var pkids = JsonConvert.DeserializeObject<List<string>>(pkidsStr);
                    List<DynamicObject> temp = new List<DynamicObject>();
                    foreach (var dataEntity in dataEntities)
                    {
                        var id = Convert.ToString(dataEntity["Id"]);
                        var number = Convert.ToString(dataEntity["fbillno"]);
                        if (!pkids.Contains(id))
                        {
                            temp.Add(dataEntity);
                            resp.FailedNumbers.Add(number);
                        }
                        else
                        {
                            resp.SucceedNumbers.Add(number);
                        }
                    }

                    failedDataEntities = temp;
                }

                //RemoveRepeatedSubmitFlag(failedDataEntities);

                resp.ErrorMsgs.AddRange(result.ComplexMessage.ErrorMessages);
            }

            // 全部成功
            if (resp.SucceedNumbers.Any() && !resp.FailedNumbers.Any()) resp.Flag = MuSiFlag.SUCCESS.ToString();
            // 全部失败
            if (!resp.SucceedNumbers.Any() && resp.FailedNumbers.Any()) resp.Flag = MuSiFlag.FAIL.ToString();
            // 部分成功
            if (resp.SucceedNumbers.Any() && resp.FailedNumbers.Any()) resp.Flag = MuSiFlag.PARTSUCCESS.ToString();

            this.OperationContext.Result.SrvData = resp;
        }

    }
}
