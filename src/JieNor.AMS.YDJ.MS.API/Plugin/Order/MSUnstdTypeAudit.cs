using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using JieNor.AMS.YDJ.Core.Utils;

namespace JieNor.AMS.YDJ.MS.API.Plugin.Order
{
    /// <summary>
    /// 销售合同：非标审核
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("MSUnstdTypeAudit")]
    public class MSUnstdTypeAudit : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            string orderNo = this.GetQueryOrSimpleParam("orderNo", "");
            if (orderNo.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("参数orderNo不能为空！");

            var entryDataStr = this.GetQueryOrSimpleParam("entryData", "");
            if (entryDataStr.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("参数entryData不能为空！");

            JArray entryData = null;
            try
            {
                entryData = JArray.Parse(entryDataStr);
            }
            catch (Exception ex)
            {
                throw new BusinessException("参数entryData格式不正确！");
            }

            if (entryData == null || entryData.Count == 0)
            {
                throw new BusinessException("参数entryData不为空！");
            }

            var order = this.Context.LoadBizDataByNo(this.HtmlForm.Id, "fbillno", new string[] { orderNo })
                ?.FirstOrDefault();
            if (order == null)
            {
                throw new WarnException("销售合同不存在，请检查！");
            }

            LogDealAmountDiff(new List<DynamicObject>() { order }, "BeginOperationTransaction-beforeCalculate");

            var priceService = this.Container.GetService<IPriceService>();

            var entrys = order["fentry"] as DynamicObjectCollection;

            ////合同是否是二级分销提交上来生成的
            //bool isSecondOrder= !order["fsourceid"].IsNullOrEmptyOrWhiteSpace()&& Convert.ToString(order["fsourcetype"]).EqualsIgnoreCase("ydj_purchaseorder");

            bool needSave = false;

            // 启用了这个参数的经销商, 在《销售合同》非标接口返回价格时, 就不会返回到【零售价】而是只返回到【总部零售价】
            var enableSellPrice = this.Context.IsEnableSellPrice();
            var priceInfos = this.LoadProductAgentSalPrice(priceService, enableSellPrice, entryData, order);

            var specialPriceInfos = this.LoadProductSpecialPrice(priceService, entryData, order);

            foreach (var data in entryData)
            {
                var tranId = data.GetJsonValue("ftranid", "");

                var entry = entrys.FirstOrDefault(s => Convert.ToString(s["ftranid"]).EqualsIgnoreCase(tranId));
                if (entry == null) continue;

                var entryId = Convert.ToString(entry["id"]);

                needSave = true;

                entry["funstdtypecomment"] = data.GetJsonValue("funstdtypecomment", "");
                entry["funstdtypestatus"] = data.GetJsonValue("funstdtypestatus", "");

                decimal qty = Convert.ToDecimal(entry["fbizqty"]);
                decimal hqPrice = decimal.Round(data.GetJsonValue("fprice", 0M), 2);

                // 零售价默认等于总部零售价
                decimal price = hqPrice;

                if (enableSellPrice)
                {
                    // 零售价尝试取经销商自己的价格（没有取到自己的价格时，则直接用慕思中台返回的价格，再结合经销商自己的定价百分比计算出最终的零售价）
                    var priceInfo = priceInfos?.FirstOrDefault(o => Convert.ToString(o["clientId"]).EqualsIgnoreCase(entryId));
                    var salPrice = Convert.ToDecimal(priceInfo?["salPrice"] ?? 0);
                    //该经销商在《销售管理参数》勾选上 【无经销价商品默认价格显示为0（前提是当前经销商启用经销价）】, 所带出来的价格就默认为0, 不会在取总部零售价了
                    price = salPrice;
                }

                //if (isSecondOrder)
                //{
                //    var agentid = this.Context.LoadBizBillHeadDataById("ydj_purchaseorder", Convert.ToString(order["fsourceid"]), "fmainorgid")?["fmainorgid"];
                //    if (!agentid.IsNullOrEmptyOrWhiteSpace()) {
                //        var fresellratio = this.Context.LoadBizBillHeadDataById("bas_agent", Convert.ToString(agentid), "fresellratio");
                //        price = hqprice * Convert.ToDecimal(fresellratio);
                //    }
                //}

                // 使用当前的折率
                decimal distrate = Convert.ToDecimal(entry["fdistrate"]);
                decimal amount = qty * price;

                // 成交单价=零售价*折率/10
                decimal dealprice = decimal.Round(price * distrate / 10, 6);
                // 成交金额=成交单价*数量
                decimal dealamount = dealprice * qty;
                // 折扣额=金额-成交金额
                decimal distamount = amount - dealamount;

                // 有特价，取特价
                var specliaPriceInfo = specialPriceInfos?.FirstOrDefault(o => Convert.ToString(o["clientId"]).EqualsIgnoreCase(entryId));
                if (specliaPriceInfo != null)
                {
                    dealprice = specliaPriceInfo.GetJsonValue<decimal>("promotionSalPrice");
                    dealamount = dealprice * qty;
                    distamount = amount - dealamount;

                    entry["fpromotionid"] = specliaPriceInfo.GetJsonValue<string>("promotionId", "");
                    entry["fpromotionrule"] = specliaPriceInfo.GetJsonValue<string>("promotionRule", "");
                    entry["fpromotionlowestprice"] = specliaPriceInfo.GetJsonValue<decimal>("promotionLowestPrice");
                    entry["fisusepromotion"] = true;
                }

                entry["fhqprice"] = hqPrice;
                entry["fprice"] = price;
                entry["famount"] = amount;
                entry["fdistamount"] = distamount;
                entry["fdealamount"] = dealamount;
                entry["fdealprice"] = dealprice;
            }

            if (!needSave)
            {
                throw new WarnException("非标审批的商品行不存在，请检查！");
            }

            var orderService = this.Container.GetService<IOrderService>();
            orderService.CalculateSettlement(this.Context, order, this.HtmlForm);
            orderService.CalculateUnreceived(this.Context, new[] { order });
            orderService.CalculateReceiptStatus(this.Context, new[] { order });

            LogDealAmountDiff(new List<DynamicObject>() { order }, "BeginOperationTransaction-afterCalculate");
            // 直接保存数据库
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(order);

            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "操作成功！";
        }


        private List<JObject> LoadProductSpecialPrice(IPriceService priceService, JArray entryData, DynamicObject orderData)
        {
            var specialPriceInfos = new List<JObject>();

            var orderDate = Convert.ToDateTime(orderData["forderdate"]);
            var customerId = Convert.ToString(orderData["fcustomerid"]);
            var entrys = orderData["fentry"] as DynamicObjectCollection;

            // 取价参数
            var productInfos = new JArray();

            foreach (var data in entryData)
            {
                var tranId = data.GetJsonValue("ftranid", "");
                var entry = entrys.FirstOrDefault(s => Convert.ToString(s["ftranid"]).EqualsIgnoreCase(tranId));
                if (entry == null) continue;

                productInfos.Add(new JObject
                {
                    ["clientId"] = entry["id"] as string,
                    ["productId"] = entry["fproductid"] as string,
                    ["bizDate"] = orderDate,
                    ["customerId"] = customerId,
                    ["attrInfo"] = new JObject
                    {
                        ["id"] = entry["fattrinfo"] as string,
                        ["entities"] = new JArray()
                    }
                });
            }

            specialPriceInfos = priceService.GetSpecialPrices(this.Context, productInfos, orderDate);

            return specialPriceInfos;
        }

        /// <summary>
        /// 批量加载经销商定义的商品统一零售价
        /// </summary>
        /// <param name="enableSellPrice">经销商是否启用经销价</param>
        /// <param name="entryData">慕思中台返回的商品明细</param>
        /// <param name="enableSellPrice">销售合同商品明细</param>
        /// <returns>商品统一零售价信息</returns>
        private List<JToken> LoadProductAgentSalPrice(IPriceService priceService, bool enableSellPrice, JArray entryData, DynamicObject orderData)
        {
            var priceInfos = new List<JToken>();

            if (!enableSellPrice) return priceInfos;

            var orderDate = Convert.ToDateTime(orderData["forderdate"]);
            var customerId = Convert.ToString(orderData["fcustomerid"]);
            var entrys = orderData["fentry"] as DynamicObjectCollection;

            // 取价参数
            var productInfos = new JArray();

            foreach (var data in entryData)
            {
                var tranId = data.GetJsonValue("ftranid", "");
                var entry = entrys.FirstOrDefault(s => Convert.ToString(s["ftranid"]).EqualsIgnoreCase(tranId));
                if (entry == null) continue;

                // 慕思中台返回的总部零售价
                decimal hqPrice = decimal.Round(data.GetJsonValue("fprice", 0M), 2);

                productInfos.Add(new JObject
                {
                    ["clientId"] = entry["id"] as string,
                    ["productId"] = entry["fproductid"] as string,
                    ["bizDate"] = orderDate,
                    ["customerId"] = customerId,
                    ["attrInfo"] = new JObject
                    {
                        ["id"] = entry["fattrinfo"] as string,
                        ["entities"] = new JArray()
                    },
                    ["hqPrice"] = hqPrice
                });
            }

            priceInfos = priceService.GetUnstdTypePrice(this.Context, productInfos);

            return priceInfos;
        }


        /// <summary>
        /// 表头与表体成交金额不一致日志
        /// </summary>
        /// <param name="content"></param>
        private void LogDealAmountDiff(IEnumerable<DynamicObject> dataEntities, string content)
        {
            if (dataEntities.IsNullOrEmpty()) return;

            ThreadWorker.QuequeTask(() =>
            {
                List<DynamicObject> diffOrders = new List<DynamicObject>();

                foreach (var dataEntity in dataEntities)
                {
                    var fdealamount_h = Convert.ToDecimal(dataEntity["fdealamount"]);

                    var fdealamount_e = (dataEntity["fentry"] as DynamicObjectCollection)?.Sum(s => Convert.ToDecimal(s["fdealamount"])) ?? 0M;


                    if (fdealamount_h != fdealamount_e)
                    {
                        diffOrders.Add(dataEntity);
                    }
                }

                if (!diffOrders.Any()) return;

                StringBuilder builder = new StringBuilder();

                builder.AppendLine(content);

                // 记录请求数据
                builder.AppendLine("非标审批请求:");

                //var requestDto = (CommonBillDTO)this.Context.CurrentRequestObject;
                //builder.AppendLine(requestDto.ToJson(false));

                // 记录当前数据包
                foreach (var diffOrder in diffOrders)
                {
                    var entrys = (diffOrder["fentry"] as DynamicObjectCollection);

                    var fdealamount_h = Convert.ToDecimal(diffOrder["fdealamount"]);
                    var fdealamount_e = entrys?.Sum(s => Convert.ToDecimal(s["fdealamount"])) ?? 0M;

                    builder.AppendLine($"金额差异：{fdealamount_h} - {fdealamount_e} = {fdealamount_h - fdealamount_e}");

                    builder.AppendLine("单据头：");

                    builder.AppendLine(new Dictionary<string, object>
                        {
                            { "id", diffOrder["id"] },
                            { "fbillno", diffOrder["fbillno"] },
                            { "fdealamount", diffOrder["fdealamount"] },
                            { "freceivable", diffOrder["freceivable"] },
                            { "funreceived", diffOrder["funreceived"] },
                            { "freceiptstatus", diffOrder["freceiptstatus"] },
                            { "fdontreflect", diffOrder["fdontreflect"] },
                            { "fdistsumamount", diffOrder["fdistsumamount"] },
                            { "fdistsumrate", diffOrder["fdistsumrate"] },
                            { "fdistamount", diffOrder["fdistamount"] },
                            { "ffaceamount", diffOrder["ffaceamount"] },
                            { "freceivabletobeconfirmed", diffOrder["freceivabletobeconfirmed"] },
                            { "fexpense", diffOrder["fexpense"] },
                            { "frefundamount", diffOrder["frefundamount"] },
                            { "factrefundamount", diffOrder["factrefundamount"] },
                            { "fsumamount", diffOrder["fsumamount"] },
                        }.ToJson(false));

                    builder.AppendLine("单据体：");

                    builder.AppendLine(entrys?
                        .Select(s => new Dictionary<string, object>
                        {
                                { "id", s["id"] },
                                { "fseq", s["fseq"] },
                                { "fproductid", s["fproductid"] },
                                { "fprice", s["fprice"] },
                                { "famount", s["famount"] },
                                { "fbizqty", s["fbizqty"] },
                                { "fdistrate", s["fdistrate"] },
                                { "fdistrateraw", s["fdistrateraw"] },
                                { "fdistamount", s["fdistamount"] },
                                { "fdealprice", s["fdealprice"] },
                                { "fdealamount", s["fdealamount"] },
                                { "fisoutspot", s["fisoutspot"] },
                        }).ToJson(false));
                }

                DebugUtil.WriteLogToFile(builder.ToString(), "OrderDealAmountDiff");
            },
                (asynResult) =>
                {
                    asynResult.HandleError();
                });
        }



    }
}
