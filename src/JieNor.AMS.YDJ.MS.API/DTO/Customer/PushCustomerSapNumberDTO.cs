using JieNor.AMS.YDJ.MS.API.Filter;
using ServiceStack;

namespace JieNor.AMS.YDJ.MS.API.DTO.Customer
{
    [Authenticate]
    [Api("直营客户回传sap客户编码接口")]
    [Route("/msapi/direct/customer/pushsapnumber")]
    [OperationLogFilter("ydj_customer")]
    public class PushCustomerSapNumberDTO : BaseDTO
    {
        /// <summary>
        /// 客户编码
        /// </summary>
        public string CustomerNo { set; get; }
        
        /// <summary>
        /// 经销商编码
        /// </summary>
        public string AgentNo { set; get; }
        
        /// <summary>
        /// 客户sap编码
        /// </summary>
        public string CustomerSapNumber { set; get; }
    }
}