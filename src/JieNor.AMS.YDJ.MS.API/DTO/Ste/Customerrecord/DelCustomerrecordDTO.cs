using Newtonsoft.Json.Linq;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.DTO.Ste.Customerrecord
{
    /// <summary>
    /// 删除商机接口
    /// </summary>
    [Api("删除商机接口")]
    [Route("/msapi/customerrecord/del")]
    [Authenticate]
    public class DelCustomerrecordDTO
    {
        public string Billno { get; set; }
    }
}
