using JieNor.AMS.YDJ.MS.API.Filter;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.DTO.Supplier
{
    /// <summary>
    /// 创建供应商DTO
    /// </summary>
    [Api("创建供应商接口")]
    [Route("/msapi/supplier/create")]
    [Authenticate]
    [OperationLogFilter("ydj_supplier")]
    public class CreateSupplierDto : BaseDTO
    {
        /// <summary>
        /// 直营编码
        /// </summary>
        public string SaletoCode { get; set; }
        
        public string BizAgentId { get; set; }

        /// <summary>
        /// 商场编码
        /// </summary>
        public string ShopCode { get; set; }

        /// <summary>
        /// 商场名称
        /// </summary>
        public string ShopName { get; set; }

        /// <summary>
        /// 详细地址
        /// </summary>
        public string Address { get; set; }
    }
}
