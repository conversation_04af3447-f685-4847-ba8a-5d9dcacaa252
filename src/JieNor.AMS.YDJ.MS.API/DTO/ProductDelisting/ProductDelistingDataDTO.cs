using JieNor.AMS.YDJ.MS.API.Filter;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.DTO.ProductDelisting
{
    /// <summary>
    /// 下市商品数据获取接口
    /// </summary>
    [Api("下市商品数据获取接口")]
    [Route("/msapi/ProductDelisting/DelistData")]
    [Authenticate]
    [OperationLogFilter("ydj_productdelisting")]
    public class ProductDelistingDataDTO 
    {
        public List<ProductDelistingBase> Data { get; set; }
    }
}
