using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.AMS.YDJ.MS.API.DTO.ProductDelisting;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Controller.ProductDelisting
{
    public class ProductDelistingQueryController : BaseController<ProductDelistingQueryDTO>
    {
        public string FormId
        {
            get { return "ydj_productdelisting"; }
        }

        protected HtmlForm HtmlForm { get; set; }

        public override object Execute(ProductDelistingQueryDTO dto)
        {
            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);

            List<DynamicObject> delistObjs = new List<DynamicObject>();
            if (dto.Data != null && dto.Data.Any())
            {
                var matchPkIds = ProductDelistingHelper.GetDelistingProductByHqId(this.Context, dto.Data);
                if (matchPkIds != null && matchPkIds.Any())
                {
                    //匹配到了，加载历史数据
                    delistObjs.AddRange(this.Context.LoadBizDataById(this.FormId, matchPkIds, true));
                }
            }
            else
            {
                delistObjs = this.Context.LoadBizDataByFilter(this.FormId, "1=1", true);
            }

            List<ProductDelistingQueryResponse> result = new List<ProductDelistingQueryResponse>();
            var currPageData = delistObjs.Skip((dto.PageIndex - 1) * dto.PageSize).Take(dto.PageSize);//取分页数据
            foreach (var item in currPageData)
            {
                ProductDelistingQueryResponse response = new ProductDelistingQueryResponse();
                response.Id = Convert.ToString(item["fhqid"]);
                response.ProductNo = "";
                response.ProductName = "";
                var fmaterialid = Convert.ToString(item["fmaterialid"]);
                if (!fmaterialid.IsNullOrEmptyOrWhiteSpace())
                {
                    var matObj = item["fmaterialid_ref"] as DynamicObject;
                    response.ProductNo = Convert.ToString(matObj["fnumber"]);
                    response.ProductName = Convert.ToString(matObj["fname"]);
                }

                response.SelType = "";
                var fseltypeid = Convert.ToString(item["fseltypeid"]);
                if (!fseltypeid.IsNullOrEmptyOrWhiteSpace())
                {
                    var selTypeObj= item["fseltypeid_ref"] as DynamicObject;
                    response.SelType = Convert.ToString(selTypeObj["fnumber"]);
                }

                response.Color = "";
                var propEntrys = item["fpropentry"] as DynamicObjectCollection;
                if (propEntrys != null && propEntrys.Any())
                {
                    var propValObj = propEntrys.FirstOrDefault()["fpropvalueid_ref"] as DynamicObject;
                    response.Color = Convert.ToString(propValObj["fnumber"]);
                }

                response.EstimateQty = 0;
                response.WarnQty = 0;
                response.SalCanPlaceOrderQty = 0;
                response.SumSalOrderQty = 0;
                response.PurCanPlaceOrderQty = 0;
                response.SumPurOrderQty = 0;
                var entrys = item["fentity"] as DynamicObjectCollection;
                if (entrys != null && entrys.Any())
                {
                    var bestNewItem = entrys.OrderByDescending(t => Convert.ToDateTime(t["fhqpushtime"])).FirstOrDefault();
                    response.EstimateQty = Convert.ToDecimal(bestNewItem["festimateqty"]);
                    response.WarnQty = Convert.ToDecimal(bestNewItem["fwarnqty"]);
                    response.SalCanPlaceOrderQty = Convert.ToDecimal(bestNewItem["fsalcanplaceorderqty"]);
                    response.SumSalOrderQty = Convert.ToDecimal(bestNewItem["fsumsalorderqtyreal"]);
                    response.PurCanPlaceOrderQty = Convert.ToDecimal(bestNewItem["fpurcanplaceorderqty"]);
                    response.SumPurOrderQty = Convert.ToDecimal(bestNewItem["fsumpurorderqty"]);
                }

                result.Add(response);
            }

            var resp = new BaseResponse<BaseListPageData<ProductDelistingQueryResponse>>
            {
                Data = new BaseListPageData<ProductDelistingQueryResponse>(dto.PageSize)
            };

            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Data.TotalRecord = result.Count;
            resp.Data.List = result;

            return resp;
        }

        /// <summary>
        /// 将慕思传递的数据转换成系统对应的内码
        /// </summary>
        /// <param name="dto"></param>
        //private List<ProductDelistingSystem> ConvertToSystemData(ProductDelistingQueryDTO dto)
        //{
        //    //获取商品编码与内码ID的关系
        //    var allProductNos = dto.Data.Where(t => !t.ProductNo.IsNullOrEmptyOrWhiteSpace()).Select(d => d.ProductNo).Distinct();
        //    Dictionary<string, string> dicProduct = new Dictionary<string, string>();
        //    if (allProductNos != null && allProductNos.Any())
        //    {
        //        var productForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
        //        var productNumberField = productForm.GetNumberField();

        //        string sql = $@"select {productForm.BillPKFldName}, {productNumberField.FieldName}  
        //                        from {productForm.BillHeadTableName} with(nolock)
        //                        where {productNumberField.FieldName} in ({allProductNos.JoinEx(",", true)})
        //                        and fmainorgid='{this.Context.Company}'";
        //        using (var reader = this.DBService.ExecuteReader(this.Context, sql))
        //        {
        //            while (reader.Read())
        //            {
        //                string id = reader.GetValueToString(productForm.BillPKFldName);
        //                string number = reader.GetValueToString(productNumberField.FieldName);
        //                dicProduct[number] = id;
        //            }
        //        }
        //    }

        //    //获取型号编码与内码ID的关系
        //    var allSeltypeNos = dto.Data.Where(t => !t.SelType.IsNullOrEmptyOrWhiteSpace()).Select(d => d.SelType).Distinct();
        //    Dictionary<string, string> dicSeltype = new Dictionary<string, string>();
        //    if (allSeltypeNos != null && allSeltypeNos.Any())
        //    {
        //        var seltypeForm = this.MetaModelService.LoadFormModel(this.Context, "sel_type");
        //        var seltypeNumberField = seltypeForm.GetNumberField();

        //        string sql = $@"select {seltypeForm.BillPKFldName}, {seltypeNumberField.FieldName}  
        //                        from {seltypeForm.BillHeadTableName} with(nolock)
        //                        where {seltypeNumberField.FieldName} in ({allSeltypeNos.JoinEx(",", true)})
        //                        and fmainorgid='{this.Context.Company}'";
        //        using (var reader = this.DBService.ExecuteReader(this.Context, sql))
        //        {
        //            while (reader.Read())
        //            {
        //                string id = reader.GetValueToString(seltypeForm.BillPKFldName);
        //                string number = reader.GetValueToString(seltypeNumberField.FieldName);
        //                dicSeltype[number] = id;
        //            }
        //        }
        //    }

        //    //获取属性编码与内码ID的关系
        //    //var allPropNos = dto.Data.Where(t => t.Color != null).Select(d => d.Color)
        //    //                .Where(c => !c.PropNo.IsNullOrEmptyOrWhiteSpace()).Select(f => f.PropNo).Distinct();
        //    //Dictionary<string, string> dicProp = new Dictionary<string, string>();
        //    //if (allPropNos != null && allPropNos.Any())
        //    //{
        //    //    var propForm = this.MetaModelService.LoadFormModel(this.Context, "sel_prop");
        //    //    var propNumberField = propForm.GetNumberField();

        //    //    string sql = $@"select {propForm.BillPKFldName}, {propNumberField.FieldName}  
        //    //                    from {propForm.BillHeadTableName} with(nolock)
        //    //                    where {propNumberField.FieldName} in ({allPropNos.JoinEx(",", true)})
        //    //                    and fmainorgid='{this.Context.Company}'";
        //    //    using (var reader = this.DBService.ExecuteReader(this.Context, sql))
        //    //    {
        //    //        while (reader.Read())
        //    //        {
        //    //            string id = reader.GetValueToString(propForm.BillPKFldName);
        //    //            string number = reader.GetValueToString(propNumberField.FieldName);
        //    //            dicProp[number] = id;
        //    //        }
        //    //    }
        //    //}

        //    //获取属性值编码与内码ID的关系
        //    var allPropvalNos = dto.Data.Where(t => !t.Color.IsNullOrEmptyOrWhiteSpace()).Select(d => d.Color).Distinct().ToList();
        //    //.Where(c => !c.PropValNo.IsNullOrEmptyOrWhiteSpace()).Select(f => f.PropValNo).Distinct();
        //    List<Tuple<string, string>> lstPropval = new List<Tuple<string, string>>();
        //    if (allPropvalNos != null && allPropvalNos.Any())
        //    {
        //        var propvalForm = this.MetaModelService.LoadFormModel(this.Context, "sel_propvalue");
        //        var propvalNumberField = propvalForm.GetNumberField();

        //        string sql = $@"select {propvalForm.BillPKFldName}, {propvalNumberField.FieldName}  
        //                        from {propvalForm.BillHeadTableName} with(nolock)
        //                        where {propvalNumberField.FieldName} in ({allPropvalNos.JoinEx(",", true)})
        //                        and fmainorgid='{this.Context.Company}'";
        //        using (var reader = this.DBService.ExecuteReader(this.Context, sql))
        //        {
        //            while (reader.Read())
        //            {
        //                string id = reader.GetValueToString(propvalForm.BillPKFldName);
        //                string number = reader.GetValueToString(propvalNumberField.FieldName);
        //                lstPropval.Add(new Tuple<string, string>(number, id));
        //            }
        //        }
        //    }
        //    var dicPropval = lstPropval.GroupBy(t => t.Item1);

        //    //将入参中的编码换成内码
        //    List<ProductDelistingSystem> result = new List<ProductDelistingSystem>();
        //    foreach (var data in dto.Data)
        //    {
        //        ProductDelistingSystem newData = new ProductDelistingSystem(data);
        //        if (!data.ProductNo.IsNullOrEmptyOrWhiteSpace() && dicProduct.ContainsKey(data.ProductNo))
        //        {
        //            newData.ProductId = dicProduct[data.ProductNo];
        //        }

        //        if (!data.SelType.IsNullOrEmptyOrWhiteSpace() && dicSeltype.ContainsKey(data.SelType))
        //        {
        //            newData.SelTypeId = dicSeltype[data.SelType];
        //        }

        //        if (data.Color != null)
        //        {
        //            //if (!data.Color.PropNo.IsNullOrEmptyOrWhiteSpace() && dicProp.ContainsKey(data.Color.PropNo))
        //            //{
        //            //    data.Color.PropNo = dicProp[data.Color.PropNo];
        //            //}

        //            if (!data.Color.IsNullOrEmptyOrWhiteSpace() && dicPropval.Any(t => t.Key.EqualsIgnoreCase(data.Color)))
        //            {
        //                newData.PropValueIds = dicPropval.FirstOrDefault(t => t.Key.EqualsIgnoreCase(data.Color)).Select(t => t.Item2).ToList();
        //            }
        //        }
        //        result.Add(newData);
        //    }
        //    return result;
        //}
    }
}
