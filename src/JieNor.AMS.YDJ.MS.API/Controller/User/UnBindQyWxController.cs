using JieNor.AMS.YDJ.MS.API.DTO.User;
using JieNor.AMS.YDJ.MS.API.Model;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq; 
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.MS.API.Controller.User
{
    /// <summary>
    /// 认证站点解绑企业微信
    /// </summary>
    public class UnBindQyWxController : BaseAuthController<UnBindQyWxDto>
    {
        protected HtmlForm HtmlForm { get; set; }

        protected string FormId
        {
            get { return "sec_user"; }
        }

        protected override bool IsAsync => false;

        protected override string UniquePrimaryKey => "account";

        protected override string BizObjectFormId => this.FormId;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(UnBindQyWxDto dto)
        {  
            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId); 

            var resp = new BaseResponse<MuSiData>();

            var sql = $@"select fid,fnumber,fname,fmainorgid,fismodifypwd from t_sec_user with(nolock) where fnumber in ({dto.Data.Select(x => x.account).JoinEx(",", true)})";
            var userList = this.DBService.ExecuteDynamicObject(this.Context, sql);

            if (!Valid(dto, resp, userList?.ToList())) return resp;

            resp.Code = 200;
            resp.Success = true;
            resp.Message = "操作成功！";
            resp.Data.Flag = MuSiFlag.SUCCESS.ToString();

            foreach (var data in dto.Data)
            {
                try
                {   
                    //此处根据用户的账号解绑
                    //向认证站点发送请求
                    var gateway = this.Container.GetService<IHttpServiceInvoker>();
                    var acResponse = gateway.Invoke(null, TargetSEP.AuthService,
                        new CommonFormDTO()
                        {
                            FormId = "auth_user",
                            OperationNo = "UnBindQyWxUser",
                            SimpleData = new Dictionary<string, string>
                            {
                        { "userName", data.account }
                            }
                        }) as DynamicDTOResponse;
                    var acResult = acResponse.OperationResult;

                    if (acResult.IsSuccess)
                    {
                        resp.Data.SucceedNumbers.Add(data.account);
                        resp.Data.SucceedNumbers_Log.Add(data.account);
                    }
                    else
                    {
                        resp.Success = false;
                        resp.Data.FailedNumbers.Add(data.account);
                        resp.Data.FailedNumbers_Log.Add(data.account);
                        resp.Data.ErrorMsgs.AddRange(acResult.ToString()?.Split(new string[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries));
                    }
                }
                catch (Exception e)
                {
                    this.LogService.Error(e);
                    resp.Success = false;
                    resp.Data.ErrorMsgs.Add(e.Message);
                }
            } 

            if (!resp.Success)
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", resp.Data.ErrorMsgs);
                resp.Data.Flag = resp.Data.SucceedNumbers.Any()
                    ? MuSiFlag.PARTSUCCESS.ToString()
                    : MuSiFlag.FAIL.ToString();
            }

            return resp;
        }
         

        private bool Valid(UnBindQyWxDto dto, BaseResponse<MuSiData> resp, List<DynamicObject> users)
        { 
            if (dto.Data == null || dto.Data.Count == 0)
            {
                resp.Code = 400;
                resp.Message = "参数data不能为空！";
                resp.Success = false;
                resp.Data.ErrorMsgs.Add(resp.Message);
                return false;
            }
                List<string> errorMsgs = new List<string>();

                foreach (var data in dto.Data)
                {
                if (data.account.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add("参数account不能为空！");
                }
                var user = users?.FirstOrDefault(x => Convert.ToString(x["fnumber"]) == data.account);
                if (user == null)
                {
                    errorMsgs.Add(data.account + "修改用户不存在！");
                }  
            }

            if (errorMsgs.Any())
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", errorMsgs);
                resp.Success = false;

                resp.Data.ErrorMsgs = errorMsgs;
                resp.Data.Flag = MuSiFlag.FAIL.ToString();

                return false;
            }

            return true;
        }
    }
}
