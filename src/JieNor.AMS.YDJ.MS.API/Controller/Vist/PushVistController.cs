using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MS.API.DTO;
using JieNor.AMS.YDJ.MS.API.DTO.Vist;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace JieNor.AMS.YDJ.MS.API.Controller.Vist
{
    public class PushVistController : BaseController<PushVistDTO>
    {
        public string FormId
        {
            get { return "ydj_vist"; }
        }



        protected HtmlForm HtmlForm { get; set; }

        /// <summary>
        /// 经销商用户上下文
        /// </summary>
        protected UserContext AgentContext { get; set; }

        protected override bool IsAsync => true;

        protected override string UniquePrimaryKey => "fbillno";

        protected override string BizObjectFormId => this.FormId;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(PushVistDTO dto)
        {
            var resp = new BaseResponse<object>();
            //if (dto.AgentNo.IsNullOrEmptyOrWhiteSpace())
            //{
            //    resp.Code = 400;
            //    resp.Message = $"经销商编码【{dto.AgentNo}】不存在！";
            //    resp.Success = false;
            //    return resp;
            //}
            //if (dto.AuthCityNo.IsNullOrEmptyOrWhiteSpace())
            //{
            //    resp.Code = 400;
            //    resp.Message = $"城市编码不能为空！";
            //    resp.Success = false;
            //    return resp;
            //}
            //var bizAgentId = this.Container.GetService<IAgentService>()
            //        .GetBizAgentIdByNo(this.Context, dto.AgentNo);
            //if (bizAgentId.IsNullOrEmptyOrWhiteSpace())
            //{
            //    resp.Code = 400;
            //    resp.Message = $"经销商编码【{dto.AgentNo}】不存在！";
            //    resp.Success = false;
            //    return resp;
            //}
            
            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);
            var dt = this.HtmlForm.GetDynamicObjectType(this.Context);


            List<string> sourceSerNos = new List<string>();
            List<string> sourceFeedNos = new List<string>();
            StringBuilder sqlWhere = null;
            List<SqlParam> parm = null;
            foreach (var detail in dto.Data)
            {
                JObject item = (JObject)detail;
                switch (Convert.ToString(item["fservicetype"]))
                {
                    case "增值":
                        item.Add("fservicescore", item["zzdata"]["fservicescore"]);
                        item.Add("fisrecommend", Convert.ToBoolean(item["zzdata"]?["fisrecommend"])?"1":"0");
                        break;
                    case "送装":
                        item.Add("fscore", item["szdata"]["fscore"]);
                        item.Add("fsalescore", item["szdata"]["fsalescore"]);
                        item.Add("finstallscore", item["szdata"]["finstallscore"]);
                        item.Add("fisrecommend", Convert.ToBoolean(item["zzdata"]?["fisrecommend"]) ? "1" : "0");
                        break;
                    case "售后":
                        item.Add("fintime", Convert.ToBoolean(item["shdata"]?["fintime"]) ? "1" : "0");
                        item.Add("fissatisfy", Convert.ToBoolean(item["shdata"]?["fissatisfy"]) ? "1" : "0");
                        break;
                    default:
                        break;
                }
                //item["fagentid"] = dto.AgentNo;
                //item["fauthcity"] = dto.AuthCityNo;
                var fsourcetype = Convert.ToString(item["fsourcetype"]).Trim();
                var fsourcenumber = Convert.ToString(item["fsourcenumber"]).Trim();
                if (fsourcetype.IsNullOrEmptyOrWhiteSpace() || fsourcenumber.IsNullOrEmptyOrWhiteSpace())
                {
                    item["fsourcetype"] = string.Empty;
                    item["fsourcenumber"] = string.Empty;
                }
                if (fsourcetype == "服务单")
                {
                    item["fsourcetype"] = "ydj_service";
                    sourceSerNos.Add(fsourcenumber);
                }
                else if (fsourcetype == "售后反馈单")
                {
                    item["fsourcetype"] = "ste_afterfeedback";
                    sourceFeedNos.Add(fsourcenumber);
                }
                if (item.Property("fstatus") != null)
                {
                    item.Remove("fstatus");
                }
                if (item.Property("zzdata") != null)
                {
                    item.Remove("zzdata");
                }
                if (item.Property("szdata") != null)
                {
                    item.Remove("szdata");
                }
                if (item.Property("shdata") != null)
                {
                    item.Remove("shdata");
                }
            }
            var dataEntities = ConvertToDyns(dt, dto.Data/*,ref AgentNo*/);

            if (dataEntities.IsNullOrEmpty() || dataEntities.Any(x => (x?["fagentid"]).IsNullOrEmptyOrWhiteSpace()))
            {
                resp.Code = 400;
                resp.Message = $"存在【招商经销商】为空的数据，请补齐参数使【招商经销商】必录！";
                resp.Success = false;
                return resp;
            }
            else if (dataEntities.Select(x => Convert.ToString(x["fagentid"])).Distinct().Count() > 1)
            {
                resp.Code = 400;
                resp.Message = $"请保持传过来的数据所有招商经销商数据一致！";
                resp.Success = false;
                return resp;
            }
            var agentDic = this.Container.GetService<ICrmDistributorService>().GetAgentObjByCrmAgent(this.Context, dataEntities.Select(x => Convert.ToString(x["fagentid"]))?.ToList());
            if (agentDic == null || !agentDic.Any() || agentDic.First().Value.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = $"【招商经销商{agentDic.First().Key}】匹配不到售达方，请检查！";
                resp.Success = false;
                return resp;
            }


            //兼顾招商经销商存在多个售达方下经销商匹配不正确的情况
            string agentDicNumber = agentDic.First().Value;
            if (agentDic.Count > 1 && dataEntities.Count() > 0)
            {
                foreach (var item in dataEntities)
                {

                    var customerid = Convert.ToString(item["fcustomerid"]).Trim();

                    var sql = $@"select t0.fnumber 
                                    from T_BAS_ORGANIZATION t0 
                                    inner join t_ydj_customer t1 on t1.fmainorgid = t0.fid and t1.fnumber = '{customerid}'";
                    using (var reader = this.DBService.ExecuteReader(this.Context, sql))
                    {
                        while (reader.Read())
                        {
                            var agentno = reader.GetString("fnumber").Trim();
                            foreach (var itemm in agentDic)
                            {
                                if (itemm.Value.Equals(agentno))
                                    agentDicNumber = itemm.Value;
                            }
                        }
                    }

                    break; //根据第一个表单上的客户来处理(如有多个表单)
                }
            }

            this.AgentContext = this.Context.CreateAgentDBContextByNo(agentDicNumber);


            List<DynamicObject> allSerBills = null;
            if (sourceSerNos.Any())
            {
                sourceSerNos.Distinct();
                sqlWhere = new StringBuilder();
                parm = new List<SqlParam>();
                sqlWhere.Append(string.Join(",", sourceSerNos.Select((x, i) => $"@fbillno{i}")));
                parm.AddRange(sourceSerNos.Select((x, i) => new SqlParam($"@fbillno{i}", System.Data.DbType.String, x)));
                allSerBills = this.AgentContext.LoadBizDataByFilter("ydj_service", $"fbillno in ({sqlWhere})",true, parm);
                var allSerBillNos = allSerBills?.Select(x => x["fbillno"].ToString()).ToList();
                allSerBillNos = allSerBillNos == null ? new List<string>() : allSerBillNos;
                var noSyncNos = sourceSerNos.Where(x => !allSerBillNos.Contains(x));
                if (noSyncNos.Any())
                {
                    resp.Code = 400;
                    resp.Message = $"【{string.Join("、", noSyncNos)}】对应源服务单未同步至金蝶！";
                    resp.Success = false;
                    return resp;
                }
                if (allSerBills.Any())//将上游服务单的【服务状态】设置为“已关闭”，并刷新【修改时间】。
                {
                    var serviceForm = this.MetaModelService.LoadFormModel(this.AgentContext, "ydj_service");
                    foreach (var serBillNo in allSerBills)
                    {
                        serBillNo["fserstatus"] = "sersta05";//已关闭
                    }
                    this.AgentContext.SaveBizData(serviceForm.Id, allSerBills);
                }

            }
            List<DynamicObject> allFeedBills = null;
            if (sourceFeedNos.Any())
            {
                sourceFeedNos.Distinct();
                sqlWhere = new StringBuilder();
                parm = new List<SqlParam>();
                sqlWhere.Append(string.Join(",", sourceFeedNos.Select((x, i) => $"@fbillno{i}")));
                parm.AddRange(sourceFeedNos.Select((x, i) => new SqlParam($"@fbillno{i}", System.Data.DbType.String, x)));
                allFeedBills = this.AgentContext.LoadBizDataByFilter("ste_afterfeedback", $"fbillno in ({sqlWhere})", true, parm);
                var allFeedBillNos = allFeedBills?.Select(x => x["fbillno"].ToString()).ToList();
                allFeedBillNos = allFeedBillNos == null ? new List<string>() : allFeedBillNos;
                var noSyncNos = sourceFeedNos.Where(x => !allFeedBillNos.Contains(x));
                if (noSyncNos.Any())
                {
                    resp.Code = 400;
                    resp.Message = $"【{string.Join("、", noSyncNos)}】对应源售后反馈单未同步至金蝶！";
                    resp.Success = false;
                    return resp;
                }
            }

            var allBillNos = dataEntities.Where(x => !x["fbillno"].IsNullOrEmptyOrWhiteSpace()).Select(x => x["fbillno"].ToString()).ToList();
            List<DynamicObject> allBills = null;
            if (allBillNos != null && allBillNos.Any())
            {
                allBillNos.Distinct();
                sqlWhere = new StringBuilder();
                parm = new List<SqlParam>();
                sqlWhere.Append(string.Join(",", allBillNos.Select((x, i) => $"@fbillno{i}")));
                parm.AddRange(allBillNos.Select((x, i) => new SqlParam($"@fbillno{i}", System.Data.DbType.String, x)));
                allBills = this.AgentContext.LoadBizBillHeadDataByACLFilter(this.FormId, $"fbillno in ({sqlWhere})", "fbillno,fsourcecancl,fprovince,fcity,fregion,faddress", parm);
            }
            foreach (var item in dataEntities)
            {
                var bill = allBills?.FirstOrDefault(x => x["fbillno"].Equals(item["fbillno"]));
                var sourcenum = Convert.ToString(item["fsourcenumber"]).Trim();
                if (Convert.ToString(item["fsourcetype"])== "ydj_service"&& !sourcenum.IsNullOrEmpty())
                {
                    var sourceBill = allSerBills?.FirstOrDefault(x => x["fbillno"].Equals(item["fsourcenumber"]));
                    if (sourceBill != null)
                    {
                        item["fcustomerid"] = item["fcustomerid"]??(sourceBill["fcustomerid_ref"] as DynamicObject)["fnumber"];
                        item["fcontacts"] = item["fcontacts"] ?? sourceBill["fcollectrel"];
                        item["fphone"] = item["fphone"] ?? sourceBill["fcollectpho"];
                        if (item["fprovince"].IsNullOrEmptyOrWhiteSpace())
                        {
                            item["fprovince"] = sourceBill["fprovince"];
                            item["fcity"] = sourceBill["fcity"];
                            item["fregion"] = sourceBill["fregion"];
                            item["faddress"] = sourceBill["fcollectadd"];
                            item["fzbaddress"] = sourceBill["fzbcollectadd"];
                        }
                    }
                }
                else if(Convert.ToString(item["fsourcetype"]) == "ste_afterfeedback" && !sourcenum.IsNullOrEmpty())
                {
                    var sourceBill = allFeedBills?.FirstOrDefault(x => x["fbillno"].Equals(item["fsourcenumber"]));
                    if (sourceBill != null)
                    {
                        item["fcustomerid"] = item["fcustomerid"] ?? (sourceBill["fcustomerid_ref"] as DynamicObject)["fnumber"];
                        item["fcontacts"] = item["fcontacts"] ?? sourceBill["flinkstaffid"];
                        item["fphone"] = item["fphone"] ?? sourceBill["flinkmobile"];
                        if (item["fprovince"].IsNullOrEmptyOrWhiteSpace())
                        {
                            item["fprovince"] = sourceBill["fprovince"];
                            item["fcity"] = sourceBill["fcity"];
                            item["fregion"] = sourceBill["fregion"];
                            item["faddress"] = sourceBill["flinkaddress"];
                            item["fzbaddress"] = sourceBill["fzblinkaddress"];
                        }
                    }
                }
                else
                {
                    item["fzbaddress"] = Convert.ToString(item["fprovince"]).Trim() +
                                        Convert.ToString(item["fcity"]).Trim() +
                                        Convert.ToString(item["fregion"]).Trim() +
                                        Convert.ToString(item["faddress"]).Trim();
                }

                //在回访单保存插件中有处理
                //item["fsourcecancl"] = "0";
                //item["fvistmode"] = "vist_canal_07";
                if (bill != null)
                {
                    item["fsourcecancl"] = bill["fsourcecancl"];
                    //item["fprovince"] = bill["fprovince"];
                    //item["fcity"] = bill["fcity"];
                    //item["fregion"] = bill["fregion"];
                    //item["faddress"] = bill["faddress"];
                }
                else
                {
                    //item["fprovince"] = string.Empty;
                    //item["fcity"] = string.Empty;
                    //item["fregion"] = string.Empty;
                    item["faddress"] = item["fzbaddress"];
                }
            }
            var simplePara = new Dictionary<string, string>();
            simplePara.Add("RequestRawUrl", this.Request.RawUrl);
            simplePara.Add("__RawData__", dto.ToJson());
            var response = this.HttpGateway.InvokeLocal<CommonBillDTOResponse>(this.AgentContext, new CommonBillDTO()
            {
                FormId = this.FormId,
                OperationNo = "MSSaveSync",
                BillData = dataEntities.ToJson(),
                SimpleData = simplePara
            });

            resp = response?.OperationResult.ToResponseModel<object>();
            var srvData = response?.OperationResult?.SrvData?.ToJson() ?? "{}";
            resp.Data = JToken.Parse(srvData);

            return resp;
        }

        private IEnumerable<DynamicObject> ConvertToDyns(DynamicObjectType dt, JArray data/*,ref string AgentNo*/)
        {
            if (data.IsNullOrEmpty()) return new List<DynamicObject>();

            List<DynamicObject> dyns = new List<DynamicObject>();

            foreach (JObject item in data)
            {
                dyns.Add(ConvertToDyn(dt, item/*, ref AgentNo*/));
            }

            return dyns;
        }

        private DynamicObject ConvertToDyn(DynamicObjectType dt, JObject data/*, ref string AgentNo*/)
        {
            var dyn = dt.CreateInstance() as DynamicObject;

            foreach (var property in data.Properties())
            {
                //if(property.Name== "fagentid")
                //{
                //    AgentNo = property.Value.ToString().Split(',').FirstOrDefault();
                //}
                if (property.Value is JArray)
                {
                    var entryEntity = this.HtmlForm.GetEntryEntity(property.Name);
                    if (entryEntity == null) continue;

                    var entrys = dyn[entryEntity.PropertyName] as DynamicObjectCollection;

                    var dyns = ConvertToDyns(entryEntity.DynamicObjectType, (JArray)property.Value/*,ref AgentNo*/);

                    foreach (var item in dyns)
                    {
                        entrys.Add(item);
                    }

                    continue;
                }

                if (property.Value is JObject)
                {
                    continue;
                }

                var field = this.HtmlForm.GetField(property.Name);
                if (field == null) continue;

                dyn[field.PropertyName] = property.Value;
            }

            return dyn;
        }

        protected override Dictionary<string, string> CreateDistributedLocks(PushVistDTO dto)
        {
            var hash = dto.ToJson().HashString();
            return new Dictionary<string, string>
            {
                { $"DistributedLock:{this.FormId}:{hash}", $"回访单 {hash} 正在锁定中，请稍后再操作！" }
            };
        }
    }

}
