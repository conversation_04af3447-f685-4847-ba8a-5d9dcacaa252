using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MS.API.DTO.PurchaseOrder;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.MS.API.Controller.PurchaseOrder
{
    /// <summary>
    /// 采购订单变更单：保存接口
    /// </summary>
    public class PurchaseOrderChgSaveController : BaseController<PurchaseOrderChgSaveDTO>
    {
        public string FormId
        {
            get { return "ydj_purchaseorder_chg"; }
        }

        protected HtmlForm HtmlForm { get; set; }

        protected override bool IsAsync => true;

        protected override string UniquePrimaryKey => "billNo";

        protected override string BizObjectFormId => this.FormId;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(PurchaseOrderChgSaveDTO dto)
        {
            var resp = new BaseResponse<object>();

            if (!Valid(dto, resp)) return resp;

            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);

            var agentCtx = this.Context.CreateAgentDBContext(dto.BizAgentId);

            // 增加单据编号
            dto.Data["fbillno"] = dto.BillNo;

            var response = this.HttpGateway.InvokeLocal<CommonBillDTOResponse>(agentCtx, new CommonBillDTO()
            {
                FormId = this.FormId,
                OperationNo = "MSSave",
                PageId = Guid.NewGuid().ToString("N"),
                SimpleData = new Dictionary<string, string>
                {
                    { "data", dto.Data.ToJson() }
                }
            });

            var result = response?.OperationResult;
            return result.ToResponseModel<object>();
        }

        /// <summary>
        /// 校验
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        /// <returns></returns>
        private bool Valid(PurchaseOrderChgSaveDTO dto, BaseResponse<object> resp)
        {
            string billNo = dto.BillNo;
            if (billNo.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = "参数billNo不能为空！";
                resp.Success = false;

                return false;
            }

            if (dto.AgentNo.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = "参数agentNo不能为空！";
                resp.Success = false;

                return false;
            }

            dto.BizAgentId = this.Context.GetBizAgentId(dto.AgentNo);
            if (dto.BizAgentId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = $"经销商【{dto.AgentNo}】不存在！";
                resp.Success = false;

                return false;
            }

            return true;
        }

        /// <summary>
        /// 设置编码
        /// </summary>
        /// <returns></returns>
        protected override void SetNumbers()
        {
            this.Request.SetBillNo(MSKey.BillNo, (this.Request.Dto as PurchaseOrderChgSaveDTO)?.BillNo);
        }

        protected override Dictionary<string, string> CreateDistributedLocks(PurchaseOrderChgSaveDTO dto)
        {
            return new Dictionary<string, string>
            {
                { $"DistributedLock:{this.FormId}:{dto.BillNo}", $"采购订单变更单 {dto.BillNo} 正在锁定中，请稍后再操作！" }
            };
        }
    }
}