using JieNor.AMS.YDJ.MS.API.DTO.Ste.Customerrecord;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;


namespace JieNor.AMS.YDJ.MS.API.Controller.Ste.Customerrecord
{

    public class RecycleCustomerrecordController : BaseController<RecycleCustomerrecordDTO>
    {
        public string FormId
        {
            get { return "ydj_customerrecord"; }
        }
        protected HtmlForm HtmlForm { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(RecycleCustomerrecordDTO dto)
        {
            var resp = new BaseResponse<object>();

            string strSql = "update t_ydj_customerrecord set fchancestatus='chance_status_04' where fbillno='{0}'".Fmt(dto.Billno);

            var data = this.Context.ExecuteDynamicObject(strSql, new List<SqlParam> { });
            if (data != null)
            {
                resp.Success = true;
                resp.Message = "成功";
            }
            else
            {
                resp.Message = "未找到数据！";
            }

            resp.Code = 200;
            return resp;

        }

        protected override Dictionary<string, string> CreateDistributedLocks(RecycleCustomerrecordDTO dto)
        {
            return new Dictionary<string, string>
            {
                { $"DistributedLock:{this.FormId}:{dto.Billno}", $"商机 {dto.Billno} 正在锁定中，请稍后再操作！" }
            };
        }
    }
}
