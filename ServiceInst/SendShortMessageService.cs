using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

using Newtonsoft.Json.Linq;

using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.DataTransferObject.Message;
using JieNor.Framework.MetaCore;
using JieNor.Framework.DataTransferObject;

namespace JieNor.Framework.AppService.ServiceInst
{
    /// <summary>
    /// 发短信操作服务
    /// </summary>
    [InjectService("sendsms")]
    [ServiceMetaAttribute("name", "发短信服务")]
    [ServiceMetaAttribute("serviceid", HtmlElementType.HtmlBizService_SendShortMessage)]
    public class SendShortMessageService : AbstractBaseService
    {
        /// <summary>
        /// 参数配置页面
        /// </summary>
        public override string ParamFormId
        {
            get
            {
                return "sys_smssereditor";
            }
        }

        /// <summary>
        /// 服务参数
        /// </summary>
        public string ServicePara { get; set; }

        /// <summary>
        /// 服务初始化通知
        /// </summary>
        /// <param name="servicePara">服务参数</param>
        protected override void OnServiceInitialized(string servicePara)
        {
            this.ServicePara = servicePara;
        }

        /// <summary>
        /// 服务执行逻辑
        /// </summary>
        /// <param name="dataEntities">数据包</param>
        public override void ExecuteService(ref DynamicObject[] dataEntities)
        {
            //数据包是否有数据
            if (dataEntities == null || !dataEntities.Any()) return;

            //服务参数
            if (this.ServicePara.IsNullOrEmptyOrWhiteSpace()) return;
            JObject joServicePara = JObject.Parse(this.ServicePara);
            if (joServicePara == null) return;

            //服务配置
            string jsonSerConfig = Convert.ToString(joServicePara["serConfig"]);
            if (jsonSerConfig.IsNullOrEmptyOrWhiteSpace()) return;
            JObject joConfig = JObject.Parse(jsonSerConfig);
            if (joConfig == null) return;
            string tplPkid = joConfig["ftemplate"]?["id"]?.ToString();
            string bizFormId = joConfig["fbizobject"]?["id"]?.ToString();
            if (tplPkid.IsNullOrEmptyOrWhiteSpace() || bizFormId.IsNullOrEmptyOrWhiteSpace()) return;

            //服务前置条件
            string strCondition = Convert.ToString(joServicePara["condition"]);
            
            //业务对象模型
            var bizHtmlForm = this.MetaModelService.LoadFormModel(this.Context, bizFormId);
            if (bizHtmlForm == null) return;

            //短信模板
            DynamicObject tplObj = this.GetSMSTemplateById(tplPkid);
            if (tplObj == null) return;
            string signName = tplObj["fsignname"] as string;
            string smsCode = tplObj["fcode"] as string;
            string smsContent = tplObj["fcontent"] as string;
            if (signName.IsNullOrEmptyOrWhiteSpace() 
                || smsCode.IsNullOrEmptyOrWhiteSpace() 
                || smsContent.IsNullOrEmptyOrWhiteSpace()) return;

            var bizExpCtx = this.Container.GetService<IBizExpressionContext>();
            bizExpCtx.HtmlForm = this.HtmlForm;
            bizExpCtx.Context = this.Context;
            var exprFactory = this.Container.GetService<IBizExpressionEvaluator>();

            //循环业务对象数据包发送短信（有可能是批量操作......）
            foreach (var dataEntitie in dataEntities)
            {
                //如果存在前置条件
                if (!strCondition.IsNullOrEmptyOrWhiteSpace())
                {
                    //执行表达式条件，如果条件不成立，则不执行后续操作
                    bizExpCtx.BizData = dataEntitie;
                    BizDynamicDataRow dcRow = new BizDynamicDataRow(null);
                    dcRow.ActiveDataObject = dataEntitie;
                    bizExpCtx.BindSetField(new TrySetValueHandler(dcRow.TrySetMember));
                    bizExpCtx.BindGetField(new TryGetValueHandler(dcRow.TryGetMember));
                    var checkResult = exprFactory.CheckCondition(strCondition, bizExpCtx);
                    if (!checkResult)
                    {
                        continue;
                    }
                }

                //接收短信的手机号
                var phone = this.GetPhone(joConfig, bizHtmlForm, dataEntitie);
                if (phone.IsNullOrEmptyOrWhiteSpace()) continue;

                //发送短信所需的参数
                SendShortMessageParam param = new SendShortMessageParam()
                {
                    MobilePhone = phone,
                    SmsSignName = signName,
                    SmsTplCode = smsCode
                };

                //解析短信模板中占位符的参数值
                List<string> incognizantFields = this.ParseSmsTemplateParam(bizHtmlForm, dataEntitie, smsContent, param.MessageParams);

                //如果存在未识别的占位符，那么这些未识别的占位符参数值将由业务插件自己提供
                if (incognizantFields.Any())
                {
                    //短信模板中的占位符参数，由业务插件自己提供，因为平台不知道这些参数具体从哪里获取
                    OnCustomServiceEventArgs e = new OnCustomServiceEventArgs()
                    {
                        EventName = "onLoadSmsParam",
                        EventData = Tuple.Create(bizFormId, this.HtmlOperation.Id, dataEntitie, incognizantFields),
                    };
                    this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", e);
                    //合并由业务插件提供的占位符参数
                    if (e.Cancel && e.Result != null)
                    {
                        var bizParams = e.Result as Dictionary<string, string>;
                        if (bizParams != null && bizParams.Count > 0)
                        {
                            foreach (var key in bizParams.Keys)
                            {
                                //防止插件提供的参数名称和平台自动解析的名称重复
                                if (!param.MessageParams.ContainsKey(key))
                                {
                                    param.MessageParams.Add(key, bizParams[key]);
                                }
                            }
                        }
                    }
                }

                //短信发送服务
                var mnsService = this.Container.GetService<IMNSService>();
                mnsService.SendShortMessage(this.Context, param);
            }
        }

        Regex _regex = new Regex(@"\$\{(.+?)\}");
        /// <summary>
        /// 解析短信模板中占位符的参数值
        /// </summary>
        /// <param name="bizForm"></param>
        /// <param name="dataEntitie"></param>
        /// <param name="smsContent"></param>
        /// <param name="msgParams"></param>
        /// <returns>未识别的占位符列表</returns>
        private List<string> ParseSmsTemplateParam(HtmlForm bizForm, DynamicObject dataEntitie, string smsContent, Dictionary<string, string> msgParams)
        {
            //短信模板内容示例：
            //您好，订单${fbillno}师傅已确认服务时间${fdate}上门前将与您联系。如有疑问，请拨打${fphone}

            //匹配短信模板中所有的占位符 ${} 
            MatchCollection matches = _regex.Matches(smsContent);

            //平台识别不了的字段
            List<string> incognizantFields = new List<string>();

            //如果短信模板中有占位符
            if (matches.Count > 0)
            {
                //加载引用数据
                this.Context.Container.GetService<LoadReferenceObjectManager>()?.Load(this.Context, 
                    bizForm.GetDynamicObjectType(this.Context), 
                    dataEntitie, 
                    false);

                foreach (Match match in matches)
                {
                    if (match.Groups.Count == 2)
                    {
                        //取到的是：${fbillno}
                        //string value1 = match.Groups[0].Value;

                        //取到的是：fbillno，对应业务对象模型文件中的字段ID
                        string fieldId = match.Groups[1].Value;
                        if (!fieldId.IsNullOrEmptyOrWhiteSpace())
                        {
                            var field = bizForm.GetField(fieldId);
                            
                            //只识别表头字段
                            if (field != null && field.IsBillHeadField)
                            {
                                if (!msgParams.ContainsKey(fieldId))
                                {
                                    msgParams.Add(fieldId, field.GetDisplayValue(this.Context, bizForm, dataEntitie, this.Option));
                                }
                            }
                            else
                            {
                                incognizantFields.Add(fieldId);
                            }
                        }
                    }
                }
            }
            return incognizantFields;
        }

        /// <summary>
        /// 获取接收短信的手机号
        /// </summary>
        /// <param name="bizForm"></param>
        /// <param name="joConfig"></param>
        /// <param name="dataEntitie"></param>
        /// <returns></returns>
        private string GetPhone(JObject joConfig, HtmlForm bizForm, DynamicObject dataEntitie)
        {
            //接收短信的手机号字段
            string phoneSource = joConfig["fphonesource"]?["id"]?.ToString();
            string phoneFieldId = joConfig["ffield"]?["id"]?.ToString();
            string receiveFieldId = joConfig["fbdfield"]?["id"]?.ToString();
            string phone = "";

            if (phoneSource.IsNullOrEmptyOrWhiteSpace()) phoneSource = "1";
            switch (phoneSource)
            {
                case "1":
                    //如果设置了手机号字段，则取手机号字段对应的值
                    if (!phoneFieldId.IsNullOrEmptyOrWhiteSpace())
                    {
                        var phoneField = bizForm.GetField(phoneFieldId);
                        if (phoneField != null && phoneField is HtmlTextField)
                        {
                            //获取业务数据对象中手机号字段值
                            phone = dataEntitie[phoneFieldId] as string;
                        }
                    }
                    break;
                case "2":
                    //否则取短信接收人字段对应的值
                    if (!receiveFieldId.IsNullOrEmptyOrWhiteSpace())
                    {
                        var receiveField = bizForm.GetField(receiveFieldId);
                        if (receiveField != null)
                        {
                            //如果字段是用户基础资料字段，则手机号已经明确是用户上面的哪个字段了，所以可以直接在用户上面取
                            if (receiveField is HtmlBaseDataField
                                && (receiveField as HtmlBaseDataField).RefFormId.EqualsIgnoreCase("sec_user"))
                            {
                                var userId = receiveField.DynamicProperty.GetValue(dataEntitie) as string;
                                phone = this.GetUserPhoneById(userId);
                            }
                            else
                            {
                                //由业务插件自己提供
                                OnCustomServiceEventArgs e = new OnCustomServiceEventArgs()
                                {
                                    EventName = "onLoadSmsPhone",
                                    EventData = Tuple.Create(bizForm.Id, receiveFieldId, dataEntitie),
                                };
                                this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", e);
                                if (e.Cancel && e.Result != null)
                                {
                                    phone = e.Result.ToString();
                                }
                            }
                        }
                    }
                    break;
                case "3":
                    phone = joConfig["fphone"]?.ToString();
                    break;
                default:
                    break;
            }
            return phone;
        }

        /// <summary>
        /// 根据短信模板主键ID获取短信模板对象
        /// </summary>
        /// <param name="pkid">短信模板主键ID</param>
        /// <returns>短信模板对象</returns>
        private DynamicObject GetSMSTemplateById(string pkid)
        {
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "sys_smstemplate");
            if (htmlForm == null) return null;

            var dm = this.Context.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            return dm.Select(pkid) as DynamicObject;
        }

        /// <summary>
        /// 根据用户ID获取用户手机号
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户手机号</returns>
        private string GetUserPhoneById(string userId)
        {
            if (userId.IsNullOrEmptyOrWhiteSpace()) return "";

            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "sec_user");
            if (htmlForm == null) return "";

            var phoneField = htmlForm.GetField("fphone");
            if (phoneField == null) return "";

            var dm = this.Context.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            var userObj = dm.Select(userId) as DynamicObject;
            if (userObj != null)
            {
                return phoneField.DynamicProperty.GetValue(userObj) as string;
            }
            return "";
        }
    }
}