using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.DataEntity.Sync;
using static JieNor.Framework.MetaCore.FormOp.OperateOptionExtentions;
using JieNor.Framework.Interface.CloudChain;
using System.Text.RegularExpressions;

namespace JieNor.Framework.AppService.ServiceInst
{
    /// <summary>
    /// 数据协同交换服务
    /// </summary>
    [InjectService("datasync")]
    [ServiceMetaAttribute("name", "数据协同交换服务")]
    [ServiceMetaAttribute("serviceid", HtmlElementType.HtmlBizService_DataSyncSwopService)]
    public class DataSyncSwopService : AbstractBaseService
    {
        /// <summary>
        /// 数据协同转换参数配置
        /// </summary>
        public DataSyncSwopSetting Setting { get; private set; }

        /// <summary>
        /// 取消父级事务
        /// </summary>
        //public override int TransactionMode => 1;

        /// <summary>
        /// 服务初始化
        /// </summary>
        /// <param name="servicePara">服务参数</param>
        protected override void OnServiceInitialized(string servicePara)
        {
            base.OnServiceInitialized(servicePara);

            var joServicePara = JObject.Parse(servicePara);
            string jsonSerConfig = Convert.ToString(joServicePara["serConfig"]);

            this.Setting = jsonSerConfig.FromJson<DataSyncSwopSetting>(true);

            if (this.Setting == null)
            {
                throw new BusinessException("数据协同转换参数必须配置！");
            }

            if (this.Setting.TargetFormId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"数据协同转换参数{nameof(this.Setting.TargetFormId)}必须配置！");
            }

            if (this.Setting.TargetOperationNo.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"数据协同转换参数{nameof(this.Setting.TargetOperationNo)}必须配置！");
            }

            if (this.Setting.SyncFieldKeys == null || this.Setting.SyncFieldKeys.Count <= 0)
            {
                throw new BusinessException($"数据协同转换参数{nameof(this.Setting.SyncFieldKeys)}必须配置！");
            }
        }

        /// <summary>
        /// 服务执行
        /// </summary>
        /// <param name="dataEntities">数据包</param>
        public override void ExecuteService(ref DynamicObject[] dataEntities)
        {
            if (dataEntities == null || !dataEntities.Any()) return;

            var exprObj = this.Container.GetService<IBizExpressionEvaluator>();

            var bizExpCtx = this.Container.GetService<IBizExpressionContext>();
            bizExpCtx.HtmlForm = this.HtmlForm;
            bizExpCtx.Context = this.Context;

            BizDynamicDataRow dcRow = new BizDynamicDataRow(null);
            bizExpCtx.BindSetField(new TrySetValueHandler(dcRow.TrySetMember));
            bizExpCtx.BindGetField(new TryGetValueHandler(dcRow.TryGetMember));

            foreach (var dataEntitie in dataEntities)
            {
                //执行表达式条件，如果条件不成立，则不执行后续操作
                if (!this.Setting.PreCondition.IsNullOrEmptyOrWhiteSpace())
                {
                    bizExpCtx.BizData = dataEntitie;
                    dcRow.ActiveDataObject = dataEntitie;
                    var checkResult = exprObj.CheckCondition(this.Setting.PreCondition, bizExpCtx);
                    if (!checkResult) continue;
                }

                //向对方系统发送协同请求
                this.SendSync(dataEntitie);
            }
        }

        /// <summary>
        /// 向对方系统发送协同请求
        /// </summary>
        /// <param name="dataEntitie"></param>
        private void SendSync(DynamicObject dataEntitie)
        {
            //加载引用数据
            this.Context.Container.GetService<LoadReferenceObjectManager>()
                ?.Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), dataEntitie, false);

            //加载协同请求信息，由业务插件自己提供
            Dictionary<TargetSEP, DynamicObject> syncData = new Dictionary<TargetSEP, DynamicObject>();
            Dictionary<string, string> simpleData = this.Setting.SimpleData ?? new Dictionary<string, string>();
            Dictionary<string, List<string>> notPackEntityPkId = null;

            //先根据配置的字段标识在当前数据包中查找协同目标地址
            this.GetTrySyncTarget(dataEntitie, syncData);

            //委托给业务插件处理
            OnCustomServiceEventArgs e = new OnCustomServiceEventArgs()
            {
                EventName = "onLoadSyncInfo",
                EventData = Tuple.Create(this.HtmlOperation.OperationNo, dataEntitie)
            };
            this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", e);
            if (e.Cancel && e.Result != null)
            {
                var bizParams = e.Result as Dictionary<string, object>;
                if (bizParams != null)
                {
                    if (bizParams.ContainsKey("syncData") && bizParams["syncData"] != null)
                    {
                        syncData = bizParams["syncData"] as Dictionary<TargetSEP, DynamicObject>;
                    }
                    if (bizParams.ContainsKey("simpleData") && bizParams["simpleData"] != null)
                    {
                        var tempSimpleData = bizParams["simpleData"] as Dictionary<string, string>;
                        if (tempSimpleData != null)
                        {
                            simpleData.Merge(tempSimpleData);
                        }
                    }
                    if (bizParams.ContainsKey("notPackEntityPkId") && bizParams["notPackEntityPkId"] != null)
                    {
                        notPackEntityPkId = bizParams["notPackEntityPkId"] as Dictionary<string, List<string>>;
                    }
                }
            }
            if (syncData == null || syncData.Count <= 0) return;

            var chainDataSyncService = this.Container.GetService<IChainDataSyncService>();

            foreach (var target in syncData.Keys)
            {
                if (target == null || target.CompanyId.IsNullOrEmptyOrWhiteSpace() || target.ProductId.IsNullOrEmptyOrWhiteSpace()) continue;
                var syncDataObj = syncData[target];

                //协同前先将单据所引用的基础数据发布到云链，以便对方能自动下载到相关的基础数据
                chainDataSyncService.PublishDataToChain(this.Context, this.HtmlForm, new DynamicObject[] { syncDataObj }, target, this.Setting.SyncFieldKeys);

                //数据打包选项
                var packOption = OperateOption.Create();
                packOption.SetOptionFlag(Enu_OpFlags.TPSRequest);
                packOption.SetSyncTargetSEP(target);
                packOption.SetSyncFieldKeys(this.Setting.SyncFieldKeys);
                packOption.SetAuxPropSyncFieldKeys(this.Setting.SyncAuxPropFieldKeys);
                packOption.SetNotPackEntityPkId(notPackEntityPkId);

                //需要协同的数据包
                var uiConverter = this.Context.Container.GetService<IUiDataConverter>();
                JObject joSyncData = uiConverter.CreateUIDataObject(this.Context, this.HtmlForm, syncDataObj, packOption);
                JArray jaSyncData = new JArray();
                jaSyncData.Add(joSyncData.GetJsonValue<JObject>("uidata"));

                //禁用对方事务
                simpleData.Add("disableTransaction", this.GetQueryOrSimpleParam<string>("disableTransaction", "false"));

                //向对方发送协同请求
                var response = this.Gateway.Invoke(
                    this.Context,
                    target,
                    new CommonBillDTO()
                    {
                        FormId = this.Setting.TargetFormId,
                        OperationNo = this.Setting.TargetOperationNo,
                        BillData = jaSyncData.ToJson(),
                        ExecInAsync = false,
                        AsyncMode = (int)Enu_AsyncMode.Background,
                        SimpleData = simpleData
                    }.SetOptionFlag((long)Enu_OpFlags.TPSRequest)
                ) as CommonBillDTOResponse;

                //协同发送完成后，通知业务插件
                var args = new OnCustomServiceEventArgs()
                {
                    EventName = "onSyncSendComplete",
                    EventData = Tuple.Create(this.HtmlOperation.OperationNo, dataEntitie, response, target)
                };
                this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", args);

                //将操作结果转化为异常抛出
                response?.OperationResult?.ThrowIfHasError(true, $"发送协同失败，对方系统未返回任何响应！");
            }
        }


        static Regex _regexConst = new Regex("^#[a-zA-Z]*#$");

        /// <summary>
        /// 尝试从当前上下文数据包中获取协同目标地址
        /// </summary>
        /// <remarks>
        /// 1.TargetCIdFieldKey 可以是企业字段，比如：fpubilshcid（此时将忽略配置中的 TargetPIdFieldKey 参数）
        /// 2.TargetCIdFieldKey 可以是文本类的企业Id字段 和 产品Id字段，比如：fcoocompanyid，fcooproductid（此时需要略配置 TargetPIdFieldKey 参数）
        /// 3.TargetCIdFieldKey 可以是基础资料下面对应的企业字段，比如：fdeptid.fpubilshcid（此时将忽略配置中的 TargetPIdFieldKey 参数）
        /// 4.TargetCIdFieldKey 可以是基础资料下面对应文本类的企业Id字段 和 产品Id字段，比如：fdeptid.fcoocompanyid，fdeptid.fcooproductid（此时需要略配置 TargetPIdFieldKey 参数）
        /// 5.TargetCIdFieldKey 可以是一个常量值，比如：194805616018067456#197356339884855296（此时将忽略配置中的 TargetPIdFieldKey 参数）
        /// </remarks>
        /// <param name="dataEntitie"></param>
        /// <param name="syncData"></param>
        private void GetTrySyncTarget(DynamicObject dataEntitie, Dictionary<TargetSEP, DynamicObject> syncData)
        {
            var companyFieldKey = this.Setting.TargetCIdFieldKey;
            var productFieldKey = this.Setting.TargetPIdFieldKey;
            if (!companyFieldKey.IsNullOrEmptyOrWhiteSpace())
            {
                var companyFieldKeys = companyFieldKey?.Split('.');
                HtmlField companyRefField = null;
                if (companyFieldKeys.Length > 0)
                {
                    companyRefField = this.HtmlForm.GetField(companyFieldKeys[0]);
                }
                var productFieldKeys = productFieldKey?.Split('.');
                HtmlField productRefField = null;
                if (productFieldKeys.Length > 0)
                {
                    productRefField = this.HtmlForm.GetField(productFieldKeys[0]);
                }

                //如果企业字段不存在，则当成常量处理
                if (companyRefField == null && _regexConst.IsMatch ( companyFieldKey.Trim() ))
                {
                    syncData.Add(new TargetSEP(companyFieldKey), dataEntitie);
                    return;
                }
                if (companyRefField == null) return;

                if (companyFieldKeys.Length == 1)
                {
                    if (companyRefField is HtmlCompanyField)
                    {
                        var publishField = companyRefField as HtmlCompanyField;
                        var publishcid = publishField?.DynamicProperty?.GetValue<string>(dataEntitie);
                        var publishpid = publishField?.ProductIdDynamicProperty?.GetValue<string>(dataEntitie);
                        if (!publishcid.IsNullOrEmptyOrWhiteSpace() && !publishpid.IsNullOrEmptyOrWhiteSpace())
                        {
                            syncData.Add(new TargetSEP(publishcid, publishpid), dataEntitie);
                        }
                    }
                    else
                    {
                        if (productRefField != null
                            && companyRefField.ElementType == HtmlElementType.HtmlField_TextField
                            && productRefField.ElementType == HtmlElementType.HtmlField_TextField)
                        {
                            var companyId = companyRefField?.DynamicProperty?.GetValue<string>(dataEntitie);
                            var productId = productRefField?.DynamicProperty?.GetValue<string>(dataEntitie);
                            syncData.Add(new TargetSEP(companyId, productId), dataEntitie);
                        }
                    }
                }
                else if (companyFieldKeys.Length == 2)
                {
                    if (companyRefField is HtmlBaseDataField)
                    {
                        var _baseDataField = companyRefField as HtmlBaseDataField;
                        var _refObj = _baseDataField?.RefDynamicProperty?.GetValue<DynamicObject>(dataEntitie);
                        if (_refObj != null)
                        {
                            var _refForm = _baseDataField?.RefHtmlForm(this.Context);
                            var _companyField = _refForm?.GetField(companyFieldKeys[1]);
                            HtmlField _productField = null;
                            if (productFieldKeys.Length == 2)
                            {
                                _productField = _refForm?.GetField(productFieldKeys[1]);
                            }

                            if (_companyField is HtmlCompanyField)
                            {
                                var publishField = _companyField as HtmlCompanyField;
                                var publishcid = publishField?.DynamicProperty?.GetValue<string>(_refObj);
                                var publishpid = publishField?.ProductIdDynamicProperty?.GetValue<string>(_refObj);
                                if (!publishcid.IsNullOrEmptyOrWhiteSpace() && !publishpid.IsNullOrEmptyOrWhiteSpace())
                                {
                                    syncData.Add(new TargetSEP(publishcid, publishpid), dataEntitie);
                                }
                            }
                            else
                            {
                                if (_productField != null
                                    && _companyField.ElementType == HtmlElementType.HtmlField_TextField
                                    && _productField.ElementType == HtmlElementType.HtmlField_TextField)
                                {
                                    var companyId = _companyField?.DynamicProperty?.GetValue<string>(_refObj);
                                    var productId = _productField?.DynamicProperty?.GetValue<string>(_refObj);
                                    syncData.Add(new TargetSEP(companyId, productId), dataEntitie);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}