using ServiceStack.Web;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{

    /// <summary>
    /// 热身服务，应用场景如：预加载系统相关模型模板文件
    /// 1、在站点启动时会自动异步执行
    /// 2、注意这时候还未登录，无上下文信息 
    /// </summary>
    public interface IWarmUpService
    {

        void Execute(UserContext ctx);


    }

    /// <summary>
    /// 一次热身服务：只在总部下执行
    /// </summary>
    public interface IOnceWarmUpService: IWarmUpService
    {

    }
}
