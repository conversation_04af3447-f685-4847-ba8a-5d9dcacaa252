using JieNor.Framework.DataTransferObject;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface
{


    /// <summary>
    /// 验证码服务
    /// </summary>
    public interface  IRandomCode
    {

        /// <summary>
        /// 动态生成随机验证码
        /// 应用场景：用户注册，企业注册页面等需要输入验证码的地方
        /// 应用说明：如用户注册页面，打开时申请获取5张验证码存到当前的页面，
        ///          当这几张验证码用完后再重新请求获取
        /// </summary>
        /// <param name="param">验证码构建参数</param> 
        /// <returns></returns>
        List<string> CreateRandomCode(RandomCodeImgParam param);



        /// <summary>
        /// 动态生成随机验证码图片
        /// 应用场景：用户注册，企业注册页面等需要输入验证码的地方
        /// 应用说明：如用户注册页面，打开时申请获取5张验证码存到当前的页面，
        ///          当这几张验证码用完后再重新请求获取
        /// </summary>
        /// <param name="param">验证码构建参数</param> 
        /// <returns></returns>
        List<RandomCodeInfo> CreateRandomCodeImg(RandomCodeImgParam param);



        /// <summary>
        /// 验证码图片校验
        /// </summary>
        /// <param name="imgId">验证码图片id</param>
        /// <param name="randomCode">验证码</param>
        /// <returns></returns>
        bool ValidateRandomCodeImg(string imgId, string randomCode);








    }














}
