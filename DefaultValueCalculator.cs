using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System.Linq;
using System;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.Interface.QueryBuilder;
using System.Collections.Generic;

namespace JieNor.Framework.AppService
{
    /// <summary>
    /// 表单默认值计算器的实现
    /// </summary>
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public class DefaultValueCalculator : IDefaultValueCalculator, IExpressionParser
    {
        /// <summary>
        /// 表达式计算
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="expression"></param>
        /// <param name="dcRowObj"></param>
        /// <returns></returns>
        public object Evaluate(UserContext userCtx, string expression, BizDynamicDataRow dcRowObj)
        {
            var exprObj = userCtx.Container.GetService<IExpressionFactory>();
            if (exprObj == null) return null;

            IBizExpressionContext exprCtx = userCtx.Container.GetService<IBizExpressionContext>();
            exprCtx.Context = userCtx;
            exprCtx.BindSetField(dcRowObj.TrySetMember);
            exprCtx.BindGetField(dcRowObj.TryGetMember);

            IBizExpression exprItem = userCtx.Container.GetService<IBizExpression>();
            exprItem.ExpressionText = expression;


            return exprObj.Eval(exprItem, exprCtx);
        }

        /// <summary>
        /// 单纯的计算表达式的值，不提供数据上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="hForm"></param>
        /// <param name="expression"></param>
        /// <param name="activeEntityKey"></param>
        /// <returns></returns>
        public object Evaluate(UserContext userCtx, HtmlForm hForm, string expression, string activeEntityKey = "")
        {
            if (expression.IsNullOrEmptyOrWhiteSpace()) return null;

            var exprObj = userCtx.Container.GetService<IExpressionFactory>();
            if (exprObj == null) return null;

            IBizExpressionContext exprCtx = userCtx.Container.GetService<IBizExpressionContext>();
            exprCtx.Context = userCtx;
            BizDynamicDataRow dcRowObj = new BizDynamicDataRow(hForm);
            exprCtx.BindSetField(dcRowObj.TrySetMember);
            exprCtx.BindGetField(dcRowObj.TryGetMember);

            var billDataEntity = hForm.GetDynamicObjectType(userCtx).CreateInstance() as DynamicObject;
            var entity = hForm.GetEntryEntity(activeEntityKey);
            if (activeEntityKey.IsNullOrEmptyOrWhiteSpace()
                || entity == null)
            {
                dcRowObj.ActiveDataObject = billDataEntity;
            }
            else if (entity is HtmlSubEntryEntity)
            {
                var parentEntity = (entity as HtmlSubEntryEntity).ParentEntity;
                var parentObjs = parentEntity.DynamicProperty.GetValue<DynamicObjectCollection>(billDataEntity);
                var parentObj = parentObjs.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                var subEntryObjs = entity.DynamicProperty.GetValue<DynamicObjectCollection>(parentObj);
                dcRowObj.ActiveDataObject = subEntryObjs.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
            }
            else if (entity is HtmlEntryEntity)
            {
                var entryObjs = entity.DynamicProperty.GetValue<DynamicObjectCollection>(billDataEntity);
                dcRowObj.ActiveDataObject = entryObjs.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
            }

            IBizExpression exprItem = userCtx.Container.GetService<IBizExpression>();
            exprItem.ExpressionText = expression;

            return exprObj.Eval(exprItem, exprCtx);
        }

        /// <summary>
        /// 针对指定数据包进行默认值计算，注意数据包对象要与对应实体匹配
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="hForm"></param>
        /// <param name="dataEntities"></param>
        /// <param name="activeEntityKey"></param>
        /// <param name="fieldDefValKv">字段默认值键值对，key 为字段标识，value 为字段默认值</param>
        public void Execute(UserContext ctx, HtmlForm hForm, DynamicObject[] dataEntities, string activeEntityKey = null, Dictionary<string, object> fieldDefValKv = null)
        {
            var exprObj = ctx.Container.GetService<IExpressionFactory>();
            if (exprObj == null) return;


            if (activeEntityKey.IsNullOrEmptyOrWhiteSpace()) activeEntityKey = hForm.HeadEntity.Id;
            var activeEntity = hForm.GetEntity(activeEntityKey);
            if (activeEntity == null) return;


            IBizExpressionContext exprCtx = ctx.Container.GetService<IBizExpressionContext>();
            exprCtx.Context = ctx;

            IBizExpression exprItem = ctx.Container.GetService<IBizExpression>();

            BizDynamicDataRow dcRow = new BizDynamicDataRow(hForm);
            exprCtx.BindSetField(dcRow.TrySetMember);
            exprCtx.BindGetField(dcRow.TryGetMember);

            var billTypeService = ctx.Container.GetService<IBillTypeService>();

            foreach (var fld in hForm.GetFieldList().Where(f => f.EntityKey.EqualsIgnoreCase(activeEntity.Id)))
            {
                var fieldDefValue = fld.DefaultValue;

                if (fld is HtmlBillTypeField && fieldDefValue.IsNullOrEmptyOrWhiteSpace())
                {
                    var defBillTypeId = billTypeService.GetDefaultBillTypeId(ctx, hForm);
                    if (!defBillTypeId.IsNullOrEmptyOrWhiteSpace())
                    {
                        fieldDefValue = $"'{defBillTypeId}'";
                    }
                }

                // 以指定的默认值为最优先
                // 优先级：单据类型中勾选了自动记忆的字段值 > 主控菜单参数配置的默认值 > 表单模型字段中配置的默认值
                var fieldDefVal = fieldDefValKv?.GetValue(fld.Id);
                if (!fieldDefVal.IsNullOrEmptyOrWhiteSpace())
                {
                    fieldDefValue = Convert.ToString(fieldDefVal);
                }

                if (fieldDefValue.IsNullOrEmptyOrWhiteSpace()) continue;

                var expr = $"{fld.PropertyName}={fieldDefValue}";
                exprItem.ExpressionText = expr;

                foreach (var dataEntity in dataEntities)
                {
                    //如果字段本身就有值，则不需要设置默认值
                    var fldValue = fld.DynamicProperty?.GetValue(dataEntity);
                    if (fldValue.IsEmptyPrimaryKey() == false)
                    {
                        continue;
                    }

                    dcRow.ActiveDataObject = dataEntity;
                    exprObj.Eval(exprItem, exprCtx);
                }
            }
        }

        /// <summary>
        /// 设置默认单据类型的自动记忆字段的默认值
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="hForm"></param>
        /// <param name="dataEntities"></param>
        public void SetFieldMemoryValueByBillType(UserContext userCtx, HtmlForm hForm, DynamicObject[] dataEntities)
        {
            var billTypeField = hForm.GetBillTypeField();
            if (billTypeField == null) return;

            var createDateField = hForm.GetField(hForm.CreateDateFldKey);
            if (createDateField == null) return;

            var createUserField = hForm.GetField("fcreatorid");
            if (createUserField == null) return;

            var mainOrgWhere = "";
            var mainOrgField = hForm.GetField("fmainorgid");
            if (mainOrgField != null)
            {
                mainOrgWhere = $" and {mainOrgField.FieldName}='{userCtx.Company}'";
            }

            var dbService = userCtx.Container.GetService<IDBService>();
            var billTypeService = userCtx.Container.GetService<IBillTypeService>();
            var dataGroups = dataEntities.GroupBy(o => billTypeField.DynamicProperty.GetValue<string>(o) ?? "");
            foreach (var dataGroup in dataGroups)
            {
                if (dataGroup.Key.IsNullOrEmptyOrWhiteSpace()) continue;
                var billTypeObj = billTypeService.GetBillTypeInfor(userCtx, dataGroup.Key);

                //取出所有的记忆字段，并构建查询
                var allAutoMemoryFieldKeys = billTypeObj.FieldCtrlInfo.Where (f=>f.fautomemory)?.ToList ()?.Select(o => o.ffieldid)?.ToList ();
                if (allAutoMemoryFieldKeys.Count == 0) continue;

                SqlBuilderParameter sqlPara = new SqlBuilderParameter(userCtx, hForm);
                sqlPara.AddSelectField(allAutoMemoryFieldKeys.JoinEx(",", false));
                sqlPara.TopCount = 1;
                sqlPara.OrderByString = $"{createDateField.Id} desc";
                sqlPara.FilterString = $"{createUserField.Id}=@currentUserId" + mainOrgWhere;
                sqlPara.NoIsolation = true;
                sqlPara.ReadDirty = true;
                sqlPara.AddParameter(new SqlParam("currentUserId", System.Data.DbType.String, userCtx.UserId));

                var queryObj = QueryService.BuilQueryObject(sqlPara);
                // SqlNoPage 已经加上了排序，这里无须再排序
                var sql = queryObj.SqlNoPage; //$"{queryObj.SqlNoPage} {queryObj.SqlOrderBy}";
                using (var reader = dbService.ExecuteReader(userCtx, sql, sqlPara.DynamicParams))
                {
                    if (reader.Read())
                    {
                        var dctFieldLatestValues = new Dictionary<string, object>();
                        for (int i = 0; i < reader.FieldCount; i++)
                        {
                            dctFieldLatestValues[reader.GetName(i)] = reader[i];
                        }

                        var allAutoMemoryFields = allAutoMemoryFieldKeys.Select(o => hForm.GetField(o))
                            .Where(o => o is HtmlField)
                            .GroupBy(o => o.EntityKey);
                        var ds = new ExtendedDataEntitySet();
                        ds.Parse(userCtx, dataGroup, hForm);
                        foreach (var autoMemoryFieldGroup in allAutoMemoryFields)
                        {
                            var entryObjs = ds.FindByEntityKey(autoMemoryFieldGroup.Key);
                            foreach (var entryObj in entryObjs)
                            {
                                foreach (var field in autoMemoryFieldGroup)
                                {
                                    object fieldValue = field.DynamicProperty.GetValue(entryObj.DataEntity);
                                    //如果当前值为null，则设置值
                                    if (fieldValue == null)
                                    {
                                        dctFieldLatestValues.TryGetValue(field.Id, out fieldValue);
                                        field.DynamicProperty.SetValue(entryObj.DataEntity, fieldValue);
                                        continue;
                                    }

                                    //如果是字符串，并且是空格，则表示没有值需要设置默认值
                                    var strValue = fieldValue as string;
                                    if (strValue != null && string.IsNullOrWhiteSpace(strValue))
                                    {
                                        dctFieldLatestValues.TryGetValue(field.Id, out fieldValue);
                                        field.DynamicProperty.SetValue(entryObj.DataEntity, fieldValue);
                                        continue;
                                    }

                                    //如果是数字0则表示没有值，需要设置默认值
                                    var numberValue = -1m;
                                    if (fieldValue is ValueType && decimal.TryParse(fieldValue.ToString(), out numberValue) && numberValue == 0)
                                    {
                                        dctFieldLatestValues.TryGetValue(field.Id, out fieldValue);
                                        field.DynamicProperty.SetValue(entryObj.DataEntity, fieldValue);
                                    }
                                }
                            }
                        }
                    }
                }




            }
        }
    }
}
