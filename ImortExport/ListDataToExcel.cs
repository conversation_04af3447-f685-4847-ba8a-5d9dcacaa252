using JieNor.Framework.AppService.QueryBuilder;
using JieNor.Framework.AppService.ReportService;
using JieNor.Framework.Consts;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.DataTransferObject.QueryFilter;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.AppService.ImortExport
{
    /// <summary>
    ///列表数据导出到Excel:
    ///前端调用需要传递过滤条件 ：filter  （对应的实体定义： FilterSchemeObject）
    ///返回：对应的文件名（全路径）
    /// </summary>
    [InjectService("ListDataToExcel")]
    public class ListDataToExcel : OfficeBizListDataService
    {
        protected override bool IsOffice
        {
            get
            {
                return true;
            }
        }

        protected override FilterSchemeObject GetFilterObject()
        {
            var filter = new FilterSchemeObject();
            filter.BillFormId = this.OperationContext.HtmlForm.Id;
            string imortColumn = this.GetQueryOrSimpleParam<string>("colModel");
            if (!imortColumn.IsNullOrEmptyOrWhiteSpace())
            {
                List<string> ColumnLists = JsonConvert.DeserializeObject<List<string>>(imortColumn);
                Dictionary<string, HtmlField> FieldList = this.OperationContext.HtmlForm.FieldList;
                foreach (var item in ColumnLists)
                {
                    string fldKey = item.Split('.')[0].Trim();
                    if (fldKey.EndsWithIgnoreCase("_txt")) fldKey = fldKey.Substring(0, fldKey.Length - 4);

                    var Column = FieldList.FirstOrDefault(s => s.Value.Id.EqualsIgnoreCase(fldKey));

                    if (Column.IsNullOrEmptyOrWhiteSpace()) continue;
                    if (Column.Value == null) continue;
                    if (Column.Value is HtmlButtonField) continue;
                    if (Column.Value.CanExport == 0) continue;

                    filter.SelectedFieldKeys.Add(Column.Value.Id);

                    filter.AddColVisible(Column.Value.Id, QueryColVisibleEnum.Visible);
                }
            }
            return filter;
        }

        /// <summary>
        /// 返回查询数据 
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            try
            {
                filter = GetFilterObject();

                if (!dataEntities.IsNullOrEmptyOrWhiteSpace() && dataEntities.Count() > 0)
                {
                    List<string> ids = new List<string>();
                    foreach (DynamicObject data in dataEntities)
                    {
                        ids.Add("'" + data["Id"].ToString() + "'");
                    }
                    filter.FilterString = $"{this.QueryHtmlForm.BillPKFldName} in (" + string.Join(",", ids) + ")";
                }

                HtmlForm lookupForm = this.OperationContext?.HtmlForm;
                var lookUpPara = this.GetPageSessionValue<Tuple<string, HtmlForm>>("LookupParam", null, lookupForm.Id, this.GetParentPageId());

                //增加 禁用状态，作废状态 过滤条件
                if (lookUpPara != null)
                {
                    HtmlField forbidStatusFld = null;
                    switch (lookupForm.ElementType)
                    {
                        case HtmlElementType.HtmlForm_BaseForm:
                            forbidStatusFld = lookupForm.GetField(lookupForm.ForbidStatusFldKey);
                            break;
                        case HtmlElementType.HtmlForm_BillForm:
                            forbidStatusFld = lookupForm.GetField(lookupForm.CancelStatusFldKey);
                            break;
                        default:
                            break;
                    }
                    if (forbidStatusFld != null)
                    {
                        filter.FilterData.Add(new FilterRowObject(forbidStatusFld.Id, "=", "false"));
                    }
                }

                //列表操作上下文
                if (this.OperationContext is ListOperationContext)
                {
                    var listOperationContext = this.OperationContext as ListOperationContext;

                    //过滤方案
                    if (listOperationContext.WhereString != null)
                    {
                        filter.FilterData.AddRange(listOperationContext.WhereString);
                    }

                    //简单快捷过滤字符串
                    filter.FilterString = filter.FilterString.JoinFilterString(listOperationContext.FilterString);

                    //排序
                    var orderBys = listOperationContext.OrderByString?.Split(',');
                    if (orderBys != null && orderBys.Length > 0)
                    {
                        foreach (var item in orderBys)
                        {
                            var items = item?.Split(' ');
                            if (items != null && items.Length > 0)
                            {
                                if (!items[0].IsNullOrEmptyOrWhiteSpace())
                                {
                                    var asc = true;
                                    if (items.Length == 2 && !items[1].IsNullOrEmptyOrWhiteSpace())
                                    {
                                        asc = items[1].EqualsIgnoreCase("asc");
                                    }
                                    filter.AddOrderBy(items[0].Trim(), asc);
                                }
                            }
                        }
                    }

                    if (this.OperationContext is ListReportOperationContext)
                    {
                        if (filter.OrderByString.IsNullOrEmptyOrWhiteSpace())
                        {
                            filter.AddOrderBy("fjnidentityid", true);
                        }
                    }
                }

                //处理主控台菜单上配置的过滤参数
                var sessionFilter = this.GetSessionValue<string>("filter");
                if (!sessionFilter.IsNullOrEmptyOrWhiteSpace())
                {
                    filter.FilterString += $"{(filter.FilterString.IsNullOrEmptyOrWhiteSpace() ? "" : " AND ")} {sessionFilter} ";
                }

                //允许插件在before或begin操作中设置额外过滤条件。
                var plugInFilterString = this.GetQueryOrSimpleParam<string>("filterString");
                filter.FilterString = filter.FilterString.JoinFilterString(plugInFilterString ?? "");

                //快速过滤
                var quickFilter = this.GetQueryOrSimpleParam<string>("quickfilter");
                if (!quickFilter.IsNullOrEmptyOrWhiteSpace())
                {
                    var category = this.DomainType.ToString().ToLower();
                    var svc = this.Container.GetService<IListQuryProfile>();
                    var qf = svc.GetListQuickFilterObject(this.OperationContext.UserContext, this.OperationContext.HtmlForm.Id, quickFilter, category);
                    filter.FilterData.AddRange(qf);
                }

                //解析动态参数
                this.ParseDynamicParam(filter);

                if (lookupForm.Id.EqualsIgnoreCase("bd_recordlist"))
                {
                    var permiSvc = this.Container.GetService<Interface.UsrMgr.IPermissionService>();
                    var isDevOpsUser = permiSvc.IsDevOpsUser(this.UserCtx, new MetaCore.PermData.PermAuth(this.UserCtx));
                    if (!isDevOpsUser)
                    {
                        filter.FilterString = filter.FilterString.JoinFilterString(" fisdevops='0' ");
                    }
                }

                var xlsSvc = this.Container.GetService<IExportService>();
                string file = "";
                string includeBill = this.GetQueryOrSimpleParam<string>("includeBill", "");
                if (!includeBill.EqualsIgnoreCase("true"))
                {
                    var queryDataService = this.Container.GetService<IQueryDataService>();
                    var datas = queryDataService.QueryData(this.OperationContext.UserContext, filter, true);

                    //允许插件干预列表数据
                    var ae = new OnCustomServiceEventArgs()
                    {
                        EventName = "afterListData",
                        EventData = datas,
                    };
                    this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", ae);
                    datas = ae.EventData as QueryDataInfo;

                    file = xlsSvc.ToExcel(this.UserCtx, datas);
                    this.OperationContext.SimpleData.Add("rows", datas.DatasDesc?.Rows.ToString());
                    this.OperationContext.SimpleData.Add("filters", filter.FilterString);
                }
                else
                {
                    file = xlsSvc.ToExcel(this.UserCtx, dataEntities, this.OperationContext.HtmlForm);
                }

                this.OperationContext.SimpleData.Add("filename", file);
                this.OperationContext.Result.SrvData = file;
                this.OperationContext.Result.IsSuccess = true;
                this.OperationContext.Result.IsShowMessage = false;
            }
            catch (Exception ex)
            {
                this.OperationContext.Result.IsSuccess = false;
                this.OperationContext.Result.IsShowMessage = true;
                this.OperationContext.Result.ComplexMessage.ErrorMessages.Add(ex.Message + Environment.NewLine + ex.StackTrace);
            }
        }

        /// <summary>
        /// 解析动态参数
        /// </summary>
        /// <param name="filter"></param>
        private void ParseDynamicParam(FilterSchemeObject filter)
        {
            //列表操作上下文
            if (!(this.OperationContext is ListOperationContext)) return;

            string jsonDynamicParam = (this.OperationContext as ListOperationContext).DynamicParam;
            if (jsonDynamicParam.IsNullOrEmptyOrWhiteSpace()) return;

            //将 Json 字符串转成对象
            DynamicQueryParam dynamicParam = JsonConvert.DeserializeObject<DynamicQueryParam>(jsonDynamicParam);
            if (dynamicParam != null && !dynamicParam.FilterString.IsNullOrEmptyOrWhiteSpace())
            {
                //拼接在 SqlBuilderParameter 的 FilterString 后面
                if (filter.FilterString.IsNullOrEmptyOrWhiteSpace())
                {
                    filter.FilterString = " ( {0} ) ".Fmt(dynamicParam.FilterString);
                }
                else
                {
                    filter.FilterString = filter.FilterString.JoinFilterString(dynamicParam.FilterString);
                }

                //如果有传递参数
                if (dynamicParam.Params != null)
                {
                    //基础资料表单（比如：客户 ydj_customer）
                    HtmlForm fieldKeyForm = this.QueryHtmlForm;
                    //基础资料字段所在的表单（比如：订货单 ydj_order）
                    HtmlForm parentHtmlForm = this.MetaModelService.LoadFormModel(this.UserCtx, dynamicParam.FormId);
                    if (parentHtmlForm == null || fieldKeyForm == null) return;

                    foreach (var param in dynamicParam.Params)
                    {
                        //先在基础资料自己的表单中查找（比如：客户 ydj_customer）
                        HtmlField field = fieldKeyForm.GetField(param.FieldId);
                        if (field == null)
                        {
                            //如果上面没有找到，再去基础资料字段所在的表单中查找（比如：订货单 ydj_order）
                            field = parentHtmlForm.GetField(param.FieldId);
                            if (field == null)
                            {
                                throw new BusinessException($"参数：@{param.FieldId} 对应的字段标识在{fieldKeyForm.Id}或{parentHtmlForm.Id}模型中不存在！");
                            }
                        }

                        //添加参数
                        filter.DynamicParams.Add(new SqlParam("@" + param.FieldId, field.ElementType.ToDbType(), param.PValue));
                    }
                }
            }
        }
    }
}