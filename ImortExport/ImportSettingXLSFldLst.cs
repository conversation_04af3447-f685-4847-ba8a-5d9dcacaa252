using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System.Data;
using Newtonsoft.Json.Linq;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.DataTransferObject.Report;

namespace JieNor.Framework.AppService.ImortExport
{
    /// <summary>
    /// 获取excel表的字段列表
    /// </summary>
    [InjectService("ImportSettingXlsFld")]
    public class ImportSettingXLSFldLst : AbstractOperationService
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName
        {
            get
            {
                return "";
            }
        }
        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem
        {
            get
            {
                return "fw_excletemp";
            }
        }

        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            
        }





        protected override void AfterExecute(ref DynamicObject[] dataEntities)
        {
            base.AfterExecute(ref dataEntities);
            var metaSetting = HtmlParser.LoadFormMetaFromCache("importsetting", this.UserCtx, null);
            var xlsSvc = this.Container.GetService<IExcleImportService>(); 
            string xlsFile = this.GetQueryOrSimpleParam<string>("url");

            if (!xlsFile.IsNullOrEmptyOrWhiteSpace())
            {
                xlsFile = xlsFile.GetSignedFileUrl(false);
            }

            var err = "";
            var stream = xlsSvc.ReadStreamFromService(xlsFile, ref err);
            var setting = xlsSvc.GetExcelColNames (this.UserCtx, stream);

            this.OperationContext.Result.SrvData = setting;
        }



    }
}
