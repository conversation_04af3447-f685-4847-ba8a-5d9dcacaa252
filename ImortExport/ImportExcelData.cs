using JieNor.Framework.AppService.QueryBuilder;
using JieNor.Framework.AppService.ReportService;
using JieNor.Framework.Consts;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;


namespace JieNor.Framework.AppService.ImortExport
{

    /// <summary>
    /// Excel数据导入
    /// </summary>
    [InjectService("ImportExcelData")]
    public class ImportExcelData : AbstractOperationService
    {
        protected override string OperationName
        {
            get
            {
                return "Excel数据导入";
            }
        }

        protected override string PermItem
        {
            get
            {
               return   PermConst.PermssionItem_New;
            }
        }

        protected override bool IgnoreOpMessage
        {
            get
            {
                return true;
            }
        }

        protected override void BeforeExecute(ref DynamicObject[] dataEntities)
        {
            base.BeforeExecute(ref dataEntities);
            var fileId = this.GetQueryOrSimpleParam<string>("fileId", "");
            var fileName = this.GetQueryOrSimpleParam<string>("fileName", "");
            var isCoverExists = this.GetQueryOrSimpleParam<string>("radioChecked", ""); 
            if (fileId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("请先上传要导入的文件！");
            }
            var fileUrl = fileId.GetSignedFileUrl(false);
            if (fileUrl.IsNullOrEmptyOrWhiteSpace() || fileName.IsNullOrEmptyOrWhiteSpace())
            {
                this.OperationContext.Result.IsSuccess = false;
                this.OperationContext.Result.IsShowMessage = true;
                this.OperationContext.Result.SimpleMessage = "请选择要导入的Excel文件并上传到服务器！";
                return;
            }

            try
            {
                var seqSvc = this.Container.GetService<ISequenceService>();
                ExcelImportParam tmpPara = BuildExcelImportParam(fileUrl, fileName, isCoverExists.EqualsIgnoreCase("cover"));
                                 
                ScheduleTaskObject taskInfo = new ScheduleTaskObject(seqSvc.GetSequence<string>(),
                    "excel.import.list",
                    "从excel导入 {0} 数据".Fmt(this.OperationContext.HtmlForm.Caption),
                    "import",
                    this.OperationContext.HtmlForm.Id,
                    tmpPara
                    );
                var jobScheduler = this.Container.TryGetService<IJobScheduleService>();
                jobScheduler.ScheduleJob(this.UserCtx, taskInfo, "");
                var progressAction = this.UserCtx.ShowProgressForm(taskInfo.Identity, null);
                this.OperationContext.Result.IsSuccess = true;
                //this.OperationContext.Result.SimpleMessage = "导入任务正在启动，请等待处理……";
                this.OperationContext.Result.HtmlActions.Add(progressAction);
            }
            catch (Exception ex)
            {
                this.OperationContext.Result.IsSuccess = false;
                this.OperationContext.Result.IsShowMessage = true;
                this.OperationContext.Result.SimpleMessage = ex.Message + Environment.NewLine + ex.StackTrace;
            }
        }

        private ExcelImportParam BuildExcelImportParam(string fileUrl, string fileName, bool IsCoverExists)
        {
            var param = new ExcelImportParam()
            {
                FileName = fileName,
                FileUrl = fileUrl,
                HtmlForm = this.OperationContext.HtmlForm,
                IsCoverExists = IsCoverExists,
            };
            var specialpara = this.GetQueryOrSimpleParam<string>("specialpara", "");
            param.ExtendPara.Add("specialpara", specialpara);
            if (specialpara.EqualsIgnoreCase( "rolefunctmpl"))//导入角色权限模板
            { 
                return param;
            }
            else if (specialpara.EqualsIgnoreCase("roleusertmpl")) //导入角色用户模板
            { 
                return param;
            }

            var setting = this.OperationContext.UserContext.LoadBizDataByFilter("importsetting", "fbizformid='{0}'".Fmt(this.OperationContext.HtmlForm.Id)).FirstOrDefault();
            if (setting == null)
            {
                throw new JieNor.Framework.CustomException.BusinessException("未设置excel导入字段映射关系，无法导入");
            }
            var findMaps = setting["ffindentity"] as DynamicObjectCollection;
            foreach (var item in findMaps)
            {
                if(item["ffindkey"]==null )
                {
                    continue;
                }
                var mapKeys =new  List<string>();
                for (int i = 1; i <= 8; i++)
                {
                    if (item["ffieldkey{0}".Fmt (i)].IsNullOrEmptyOrWhiteSpace ())
                    {
                        continue;
                    }
                    mapKeys.Add(item["ffieldkey{0}".Fmt(i)].ToString());
                }
                param.FindMaps.TryAdd(item["ffindkey"].ToString(), mapKeys);
            }

            findMaps = setting["ffldmapentity"] as DynamicObjectCollection;
            foreach (var item in findMaps)
            {
                if (item["ffldkey"].IsNullOrEmptyOrWhiteSpace () || item["fxlsfldCaption"].IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                var mapKeys = new ExcelFieldMapInfo()
                {
                    ExcelFldCaption = item["fxlsfldCaption"].ToString().Trim(),
                    FldKey = item["ffldkey"].ToString().Trim(),
                    FldCaption = item["ffldkey_txt"]?.ToString().Trim(),
                    IsImport = (bool)item["fisimport"],
                };
                 
                param.FieldMaps.TryAdd(mapKeys.FldKey, mapKeys);
            }

            return param;
        }

        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            
        }



    }









}
