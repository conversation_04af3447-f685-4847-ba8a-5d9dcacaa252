using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.DBSchema
{

    /// <summary>
    /// 表结构更新
    /// </summary>
    [InjectService]
    public class DBSchemaService : IDBSchemaService
    {
        /// <summary>
        /// 缓存模型的更新标记
        /// </summary>
        static ConcurrentDictionary<string, bool> mdlUpdateFlag = new ConcurrentDictionary<string, bool>();

        static ConcurrentDictionary<string, HashSet<string>> schemaInfo = new ConcurrentDictionary<string, HashSet<string>>(StringComparer.OrdinalIgnoreCase);

        /// <summary>
        /// 更新所有模型的库表结构
        /// </summary>
        /// <param name="ctx"></param>
        public IOperationResult UpdateMdlMetaData(UserContext ctx)
        {
            IOperationResult result = new OperationResult();
            result.ComplexMessage = new HtmlComplexMessage();

            Stopwatch stopWatch = new Stopwatch();
            stopWatch.Start();
            var mdlSql = GetUpdateMdlMetaDataSql(ctx);
            result.ComplexMessage.ErrorMessages.AddRange(mdlSql.Content);
            stopWatch.Stop();
            result.ComplexMessage.SuccessMessages.Add("分析模型，获取表结构差异sql耗时 {0}".Fmt(stopWatch.Elapsed));

            ExecuteMdlUpdateSql(ctx, result, stopWatch, mdlSql);

            if (result.ComplexMessage.ErrorMessages.Count > 0)
            {
                var log = ctx.Container.GetService<ILogServiceEx>();
                log.Error(string.Join(Environment.NewLine, result.ComplexMessage.ErrorMessages));
            }
            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formMeta"></param>
        /// <param name="rebuild"></param>
        /// <returns></returns>
        public IOperationResult UpdateMdlMetaData(UserContext ctx, HtmlForm formMeta, bool rebuild = false)
        {
            IOperationResult result = new OperationResult();
            result.ComplexMessage = new HtmlComplexMessage();

            Stopwatch stopWatch = new Stopwatch();
            stopWatch.Start(); 
            var mdlSql = GetModleSchemaSql(ctx, formMeta, rebuild);
            result.ComplexMessage.ErrorMessages.AddRange(mdlSql.Content);
            var refFlds = formMeta.GetFieldList().Where(x => x is HtmlBaseDataField);
            foreach (HtmlBaseDataField fld in refFlds)
            {
                if (!fld.RefFormId.IsNullOrEmptyOrWhiteSpace() && fld.RefHtmlForm(ctx) != null)
                {
                    //引用对象自动重建表结构
                    var refBD = GetModleSchemaSql(ctx, fld.RefHtmlForm(ctx), rebuild);
                    MergMdlSql(mdlSql, refBD);
                }
            }
            stopWatch.Stop();
            result.ComplexMessage.SuccessMessages.Add("分析模型，获取表结构差异sql耗时 {0}".Fmt(stopWatch.Elapsed));

            ExecuteMdlUpdateSql(ctx, result, stopWatch, mdlSql);
            if (result.ComplexMessage.ErrorMessages.Count > 0)
            {
                var log = ctx.Container.GetService<ILogServiceEx>();
                log.Error (string.Join(Environment.NewLine, result.ComplexMessage.ErrorMessages));
            }
            return result;
        }

        /// <summary>
        /// 更新某个模型的库表结构
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <param name="rebuild">是否强制重建</param>
        public IOperationResult UpdateMdlMetaData(UserContext ctx, string formId, bool rebuild = false)
        { 
            var metaModel = HtmlParser.LoadFormMetaFromCache(formId, ctx);
            var result = UpdateMdlMetaData(ctx, metaModel, rebuild);
             
            return result;
        }


        private static void ExecuteMdlUpdateSql(UserContext ctx, IOperationResult result, Stopwatch stopWatch, MdlSchemaSql mdlSql)
        {
            var sqlSvc = ctx.Container.GetService<IDBServiceEx>();

            stopWatch.Start();
            ExecuteSql(ctx, result, sqlSvc, mdlSql.TableSql);
            stopWatch.Stop();
            result.ComplexMessage.SuccessMessages.Add("执行表构建Sql耗时 {0}".Fmt(stopWatch.Elapsed));

            stopWatch.Start();
            ExecuteSql(ctx, result, sqlSvc, mdlSql.PKFieldSql);
            stopWatch.Stop();
            result.ComplexMessage.SuccessMessages.Add("执行主键构建Sql耗时 {0}".Fmt(stopWatch.Elapsed));

            stopWatch.Start();
            ExecuteSql(ctx, result, sqlSvc, mdlSql.IndexSql);
            stopWatch.Stop();
            result.ComplexMessage.SuccessMessages.Add("执行索引构建Sql耗时 {0}".Fmt(stopWatch.Elapsed));

            stopWatch.Start();
            if (mdlSql.ViewSql.Count > 0)
            {
                foreach (var sql in mdlSql.ViewSql)
                {
                    ExecuteSql(ctx, result, sqlSvc, new List<string>() { sql });
                }
            }
            stopWatch.Stop();
            result.ComplexMessage.SuccessMessages.Add("执行视图构建Sql耗时 {0}".Fmt(stopWatch.Elapsed));
        }


        /// <summary>
        /// 获取更新库表结构的sql脚本
        /// </summary>
        /// <param name="ctx"></param>
        public MdlSchemaSql GetUpdateMdlMetaDataSql(UserContext ctx)
        {
            Stopwatch stopWatch = new Stopwatch();
            MdlSchemaSql mdlSql = new MdlSchemaSql();

            stopWatch.Start();
            var mdls = HtmlParser.LoadBizObjectList();
            Dictionary<string, HashSet<string>> tableInfor = GetAllTableInfo(ctx, false);
            stopWatch.Stop();
            mdlSql.Content.Add("获取所有现有表相关信息耗时{0}".Fmt(stopWatch.Elapsed));

            stopWatch.Start();
            foreach (var mdl in mdls)
            {
                try
                {
                    var metaModel = HtmlParser.LoadFormMetaFromCache(mdl.Key, ctx);
                    var schema = GetModleSchemaSql(ctx, metaModel);
                    MergMdlSql(mdlSql, schema);
                }
                catch (Exception ex)
                {
                    mdlSql.Content.Add("分析模型：{0} {1} 时出现错误，错误信息： {2} ==》 {3} ".Fmt(mdl.Key, mdl.Value, ex.Message, ex.StackTrace));
                }
            }

            stopWatch.Stop();
            mdlSql.Content.Add("获取所有表结构差异Sql息耗时{0}".Fmt(stopWatch.Elapsed));

            var logSvc = ctx.Container.GetService<ILogServiceEx>();
            logSvc.Info(mdlSql.Content);
            logSvc.Info(mdlSql.TableSql);
            logSvc.Info(mdlSql.ViewSql);
            logSvc.Info(mdlSql.PKFieldSql);
            logSvc.Info(mdlSql.IndexSql);

            return mdlSql;
        }


        private static MdlSchemaSql GetModleSchemaSql(UserContext ctx, HtmlForm metaModel, bool rebuild = false)
        {
            MdlSchemaSql mdlSql = new MdlSchemaSql();
            try
            {
                if (metaModel.HeadEntity.TableName.IsNullOrEmptyOrWhiteSpace())
                {
                    return mdlSql;
                }

                string cacheKey = "{0}.{1}".Fmt(ctx.Company, metaModel.Id);
                if (!rebuild && mdlUpdateFlag.ContainsKey(cacheKey))
                {
                    return mdlSql;
                }

                var dm = ctx.Container.GetService<IDataManager>();
                dm.InitDbContext(ctx, metaModel.GetDynamicObjectType(ctx,true));
                Dictionary<string, HashSet<string>> tableInfor = GetAllTableInfo(ctx, rebuild);
                Dictionary<string, List<string>> ret = dm.GetUpdateMetadataSql(tableInfor);
                MergMdlSql(mdlSql, ret);

                foreach (var item in ret.Values)
                {
                    item?.Clear();
                }

                //审计日志表
                var auditMdl = HtmlParser.LoadFormMetaFromCache("bd_record", ctx);
                BuildAuditLogTblSql(ctx, mdlSql, auditMdl, metaModel, dm, tableInfor);

                //系统操作日志表
                var opLogMdl = HtmlParser.LoadFormMetaFromCache("sys_logdata", ctx);
                SysOpLogRecord(ctx, mdlSql, opLogMdl, dm, tableInfor);

                if (!mdlUpdateFlag.ContainsKey(cacheKey))
                {
                    mdlUpdateFlag.TryAdd(cacheKey, true);
                }
            }
            catch (Exception ex)
            {
                mdlSql.Content.Add("分析模型：{0} {1} 时出现错误，错误信息： {2} ==》 {3} ".Fmt(metaModel.Id, metaModel.Caption, ex.Message, ex.StackTrace));
            }

            return mdlSql;
        }


        private static void MergMdlSql(MdlSchemaSql mdlSql, MdlSchemaSql mdlSqlX)
        {
            mdlSql.IndexSql.AddRange(mdlSqlX.IndexSql);
            mdlSql.TableSql.AddRange(mdlSqlX.TableSql);
            mdlSql.PKFieldSql.AddRange(mdlSqlX.PKFieldSql);
            mdlSql.ViewSql.AddRange(mdlSqlX.ViewSql);
        }
        private static void MergMdlSql(MdlSchemaSql mdlSql, Dictionary<string, List<string>> sqlX)
        {
            mdlSql.IndexSql.AddRange(sqlX["IndexSql"]);
            mdlSql.TableSql.AddRange(sqlX["TableSql"]);
            mdlSql.PKFieldSql.AddRange(sqlX["PKFieldSql"]);
            mdlSql.ViewSql.AddRange(sqlX["ViewSql"]);
        }


        private static void ExecuteSql(UserContext ctx, IOperationResult result, IDBServiceEx sqlSvc, List<string> sql)
        {
            try
            {
                sqlSvc.ExecuteBatch(ctx, sql, 100, 20000);
            }
            catch (Exception ex)
            {
                result.ComplexMessage.ErrorMessages.Add(ex.Message + Environment.NewLine + ex.StackTrace);
            }
        }


        /// <summary>
        /// 系统操作日志表(建未来两年的日志表)
        /// </summary>
        /// <param name="mdlSql"></param>
        /// <param name="ctx"></param>
        /// <param name="opLogMdl"></param>
        /// <param name="tableInfor"></param>
        /// <param name="dm"></param>
        /// <returns></returns>
        private static void SysOpLogRecord(UserContext ctx, MdlSchemaSql mdlSql, HtmlForm opLogMdl, IDataManager dm,
                                         Dictionary<string, HashSet<string>> tableInfor)
        {
            for (int i = 0; i <= 24; i++)
            {
                var tmpMdl = opLogMdl.ToJson().FromJson<HtmlForm>();
                var yearmoth = DateTime.Now.AddMonths(i).ToString("yyyyMM");
                tmpMdl.Id = "{0}_{1}".Fmt(opLogMdl.Id, yearmoth);
                tmpMdl.HeadEntity.TableName = "{0}_{1}".Fmt(opLogMdl.HeadEntity.TableName.Trim(), yearmoth);

                dm.InitDbContext(ctx, tmpMdl.GetDynamicObjectType(ctx));
                var ret = dm.GetUpdateMetadataSql(tableInfor);
                MergMdlSql(mdlSql, ret);

                foreach (var item in ret.Values)
                {
                    item?.Clear();
                }
            }
        }


        /// <summary>
        /// 审计日志表结构
        /// </summary>
        /// <param name="mdlSql"></param>
        /// <param name="ctx"></param>
        /// <param name="logModel"></param>
        /// <param name="metaModel"></param>
        /// <param name="dm"></param>
        /// <returns></returns>
        private static void BuildAuditLogTblSql(UserContext ctx, MdlSchemaSql mdlSql, HtmlForm logModel, HtmlForm metaModel,
                                        IDataManager dm, Dictionary<string, HashSet<string>> tableInfor)
        {
            if (metaModel.Id.EqualsIgnoreCase("bd_record") || metaModel.HeadEntity.TableName.IsNullOrEmptyOrWhiteSpace()
                ||metaModel.ElementType == HtmlElementType.HtmlForm_ListReportForm || metaModel.ElementType == HtmlElementType.HtmlForm_ReportForm)
            {
                return;
            }

            var billLogMdl = logModel.ToJson().FromJson<HtmlForm>();
            billLogMdl.Id = "{0}_lg".Fmt(metaModel.Id);
            billLogMdl.HeadEntity.TableName = metaModel.HeadEntity.TableName.Trim() + "_lg";
            billLogMdl.EntryList[0].TableName = metaModel.HeadEntity.TableName.Trim() + "_lge";
            dm.InitDbContext(ctx, billLogMdl.GetDynamicObjectType(ctx));

            var ret = dm.GetUpdateMetadataSql(tableInfor);
            MergMdlSql(mdlSql, ret);

            foreach (var item in ret.Values)
            {
                item?.Clear();
            }
        }


        private static Dictionary<string, HashSet<string>> GetAllTableInfo(UserContext ctx, bool reBuild)
        {
            var sqlSvc = ctx.Container.GetService<IDBService>();
            if (reBuild || !schemaInfo.ContainsKey("{0}.TableFields".Fmt(ctx.Company)))
            {
                var schema = GetAllTableFields(ctx, sqlSvc);
                schemaInfo.AddOrUpdate("{0}.TableFields".Fmt(ctx.Company), schema, (k, v) => schema);
            }

            if (reBuild || !schemaInfo.ContainsKey("{0}.TablePKs".Fmt(ctx.Company)))
            {
                var schema = GetAllTablePKIndex(ctx, sqlSvc);
                schemaInfo.AddOrUpdate("{0}.TablePKs".Fmt(ctx.Company), schema, (k, v) => schema);
            }
            if (reBuild || !schemaInfo.ContainsKey("{0}.TableIndexs".Fmt(ctx.Company)))
            {
                var schema = GetAllTableIndex(ctx, sqlSvc);
                schemaInfo.AddOrUpdate("{0}.TableIndexs".Fmt(ctx.Company), schema, (k, v) => schema);
            }

            var ret = new Dictionary<string, HashSet<string>>();
            foreach (var item in schemaInfo)
            {
                if (item.Key.StartsWith(ctx.Company + "."))
                {
                    ret.Add(item.Key.Replace("{0}.".Fmt(ctx.Company), ""), item.Value);
                }
            }
            return ret;
        }


        private static HashSet<string> GetAllTableFields(UserContext ctx, IDBService sqlSvc)
        {
            var ret = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
            var sql = @"/*dialect*/select a.name as ftablename,b.name as fcolname from sysobjects a inner join syscolumns b on  a.id=b.id   where a.xtype ='U' ";
            using (var reader = sqlSvc.ExecuteReader(ctx, sql))
            {
                while (reader.Read())
                {
                    ret.Add("{0}.{1}".Fmt(reader["ftablename"], reader["fcolname"]).ToUpperInvariant () );
                }
            }

            return ret;
        }



        private static HashSet<string> GetAllTablePKIndex(UserContext ctx, IDBService sqlSvc)
        {
            var ret = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
            var sql = @"/*dialect*/SELECT object_name(parent_obj) as ftablename, [name] as FpkIndexName FROM sysobjects WHERE   xtype='PK' ";
            using (var reader = sqlSvc.ExecuteReader(ctx, sql))
            {
                while (reader.Read())
                {
                    if (!ret.Contains(reader["FpkIndexName"].ToString().ToUpperInvariant()))
                    {
                        ret.Add(reader["FpkIndexName"].ToString().ToUpperInvariant() );
                    }
                }
            }
            return ret;
        }



        private static HashSet<string> GetAllTableIndex(UserContext ctx, IDBService sqlSvc)
        {
            var ret = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
            var sql = @"/*dialect*/select object_name(id) as ftablename, [name] as  FpkIndexName 
                        from sysindexes  WHERE   [name] like 'idx_%' ";
            using (var reader = sqlSvc.ExecuteReader(ctx, sql))
            {
                while (reader.Read())
                {
                    if (!ret.Contains(reader["FpkIndexName"].ToString().ToUpperInvariant()))
                    {
                        ret.Add(reader["FpkIndexName"].ToString().ToUpperInvariant() );
                    }
                }
            }

            return ret;
        }

    }


}
