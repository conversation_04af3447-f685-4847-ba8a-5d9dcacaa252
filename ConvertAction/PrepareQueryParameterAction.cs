using JieNor.Framework.AppService.QueryBuilder;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.BLA;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace JieNor.Framework.AppService.ConvertAction
{
    [InjectService]
    public class PrepareQueryParameterAction : BaseConvertAction
    {
        [InputProperty]
        public ConvertRule ConvertRule { get; set; }

        [OutputProperty]
        public IEnumerable<ConvertSourceQueryObject> SourceQueryObjects { get; set; }

        /// <summary>
        /// 源单模型
        /// </summary>
        [InputProperty]
        public HtmlForm SourceHtmlForm { get; set; }

        [OutputProperty]
        public Dictionary<string, HtmlField> SourceFieldList { get; set; }

        /// <summary>
        /// 目标单模型
        /// </summary>
        [InputProperty]
        public HtmlForm TargetHtmlForm { get; set; }

        [InjectProperty]
        protected IBizExpressionEvaluator ExpressionEngine { get; set; }

        [InjectProperty]
        protected ISqlStringParser SqlParser { get; set; }

        public override void Execute()
        {
            List<ConvertSourceQueryObject> lstQueryObjs = new List<ConvertSourceQueryObject>();
            List<HtmlEntity> lstQueryPath = new List<HtmlEntity>();
            this.ParseConvertRule(out lstQueryPath);

            var dm = this.UserContext.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserContext, this.SourceHtmlForm.GetDynamicObjectType(this.UserContext));
            //分析查询线路：必须满足：FBillHead，FBillHead-FEntity，FBillHead-FEntity-FSubEntity;
            foreach (var queryPath in lstQueryPath)
            {
                var lstSelFldKeys = this.SourceFieldList.Where(o => IsInQueryPath(queryPath, o.Value))
                    .Select(o => o.Key)
                    .ToList();

                var sourceQueryObject = new ConvertSourceQueryObject();
                sourceQueryObject.ActiveQueryEntity = queryPath;

                //获取关联查询表格数据
                sourceQueryObject.SelectedRows = this.GetActiveQuerySelectedRows(queryPath);

                if (sourceQueryObject.SelectedRows == null || sourceQueryObject.SelectedRows.Any() == false)
                {
                    throw new BusinessException("单据下推必须提供来源数据：当前操作没有选中行数据！");
                }

                List<DynamicObject> billDatas = new List<DynamicObject>();
                QueryDataWithSelectedRows(sourceQueryObject, ref billDatas, dm, queryPath);
                string srcFldKey = "";    //关联的字段
                HtmlField relationFld = null;
                DataTable dt = new DataTable();
                //定义固定命名的业务已关联发生量
                DataColumn dcWillLinkQty = new DataColumn("__linkQty", typeof(decimal));
                if (!this.ConvertRule.RelationFieldKey.IsNullOrEmptyOrWhiteSpace())
                {
                    CreateDataTable(ref dt, dcWillLinkQty, ref relationFld, ref srcFldKey, sourceQueryObject, billDatas, queryPath);
                }

                string tempTableName = "";
                if (dt.Columns.Count > 0)
                {
                    tempTableName = this.DBService.CreateTempTableWithDataTable(this.UserContext, dt, 2000,null,null,false);
                }
                else
                {
                    tempTableName = this.DBService.CreateTempTableWithSelectedRows(this.UserContext, sourceQueryObject.SelectedRows, new DataColumn[] { dcWillLinkQty },false);
                }
                sourceQueryObject.TempTableName = tempTableName;
                sourceQueryObject.TempTableNameAs = $"_sr_{queryPath.Id}_";
                var controlFld = this.TargetHtmlForm.GetField(this.ConvertRule.ControlFieldKey);        //目标单控制字段

                var sqlPara = new SqlBuilderParameter(this.UserContext, this.SourceHtmlForm.Id);
                sqlPara.PageCount = -1;
                //解析本次查询路径下所有需要查询的字段
                var sourceBillNoField = this.SourceHtmlForm.GetNumberField();
                if (sourceBillNoField != null)
                {
                    lstSelFldKeys.Add(sourceBillNoField.Id);
                }
                var sourceControlField = this.SourceHtmlForm.GetField(this.ConvertRule.SourceControlFieldKey ?? "");
                if (sourceControlField != null)
                {
                    lstSelFldKeys.Add(sourceControlField.Id);
                }
                sqlPara.SelectedFieldKeys.AddRange(lstSelFldKeys);

                //多选字段下推时需要将 _txt 字段一并下推
                foreach (var fldKey in lstSelFldKeys)
                {
                    var field = this.SourceHtmlForm.GetField(fldKey);
                    if (field is IMultTextField)
                    {
                        sqlPara.SelectedFieldKeys.Add($"{field.FieldName}_txt");
                    }
                }

                //委托插件对查询参数进行自定义
                var willLinkEventArgs = new OnCalcWillLinkQtyEventArgs(sourceQueryObject.TempTableName, sourceQueryObject.TempTableNameAs, new DataColumn[] { dcWillLinkQty });
                this.PlugInProxy.InvokePlugInMethod("OnCalcWillLinkQty", willLinkEventArgs);
                if (!willLinkEventArgs.Cancel && !srcFldKey.IsNullOrEmptyOrWhiteSpace() && !relationFld.IsNullOrEmptyOrWhiteSpace()
                    && !controlFld.IsNullOrEmptyOrWhiteSpace() && !sourceControlField.IsNullOrEmptyOrWhiteSpace())
                {
                    //todo:按规则中配置控制字段计算未发生的业务量，关联更新至当前这个临时表里
                    //todo:改造下面的queryObject的SqlSelect属性，与sqlwhere属性（自动追加这个关联量<源单控制字段）
                    UpdateTempQty(controlFld, relationFld, sourceQueryObject, dcWillLinkQty, srcFldKey);
                }

                //委托插件对查询参数进行自定义
                var queryParaEvent = new OnPrepareQueryBuilderParameterEventArgs(queryPath, sqlPara);
                this.PlugInProxy.InvokePlugInMethod("OnPrepareQueryBuilderParameter", queryParaEvent);

                var queryObject = QueryService.BuilQueryObject(sqlPara);

                var headInfo = QueryService.GetEntityTable(this.UserContext, this.BillConvertContext.SourceFormId, "fbillhead");
                var entityInfo = QueryService.GetEntityTable(this.UserContext, this.BillConvertContext.SourceFormId, queryPath.Id);

                queryObject.SqlFrom += $"{Environment.NewLine} inner join {sourceQueryObject.TempTableName} {sourceQueryObject.TempTableNameAs} ";
                queryObject.SqlFrom += $"      on {sourceQueryObject.TempTableNameAs}.fid={headInfo.TableNameAs}.{headInfo.PkFldName} ";

                if (entityInfo != null && queryPath is HtmlEntryEntity)
                {
                    queryObject.SqlFrom += $" and ({sourceQueryObject.TempTableNameAs}.fentryid={entityInfo.TableNameAs}.{entityInfo.PkFldName} ";
                    queryObject.SqlFrom += $"      and {sourceQueryObject.TempTableNameAs}.fentitykey='{queryPath.Id}' ";
                    queryObject.SqlFrom += $"      or {sourceQueryObject.TempTableNameAs}.fentryid is null ";
                    queryObject.SqlFrom += $"      or {sourceQueryObject.TempTableNameAs}.fentryid ='' ";
                    queryObject.SqlFrom += $"      or {sourceQueryObject.TempTableNameAs}.fentryid =' ' ) ";

                    //排序：
                    if ((queryPath as HtmlEntryEntity).SeqDynamicProperty != null)
                    {
                        queryObject.SqlOrderBy = "Order By  t0.{2},{0}.fseq,{0}.{1} ".Fmt(entityInfo.TableNameAs, entityInfo.PkFldName, this.SourceHtmlForm.GetNumberField()?.FieldName ?? "fbillno");
                    }
                }

                var strExFilter = "";
                var whereKeyFlds = new List<string>();
                if (!this.ConvertRule.FilterString.IsNullOrEmptyOrWhiteSpace())
                {
                    if (ConvertRule.FilterString.Contains ("@currentorgid"))
                    {
                        sqlPara.AddParameter(new SqlParam("@currentorgid", System.Data.DbType.String, UserContext.Company));
                    }
                    strExFilter = $" and ({this.SqlParser.GetRealSqlFilter(this.UserContext, this.SourceHtmlForm, this.ConvertRule.FilterString, out whereKeyFlds)})";
                }

                queryObject.SqlWhere += strExFilter;
                //根据下游单据的余量，改造sqlselect和sqlwhere
                UpdateSqlSelectAndWhere(sourceControlField, relationFld, headInfo, sourceQueryObject, dcWillLinkQty, queryObject, entityInfo);

                sourceQueryObject.QueryObject = queryObject;
                sourceQueryObject.ParamList.AddRange(sqlPara.DynamicParams);
                 
                lstQueryObjs.Add(sourceQueryObject);
            }

            var queryEventArgs = new OnPrepareQuerySourceObjectEventArgs(lstQueryObjs);
            this.PlugInProxy.InvokePlugInMethod("OnPrepareQuerySourceObject", queryEventArgs);

            this.SourceQueryObjects = queryEventArgs.SourceQueryObjects;
        }

        private List<SelectedRow> GetActiveQuerySelectedRows(HtmlEntity activeEntity)
        {
            List<SelectedRow> lstSelRows = new List<SelectedRow>();
            foreach (var selRow in this.BillConvertContext.SelectedRows)
            {
                SelectedRow selRow2;

                List<string> lstEntryPkIds = new List<string>();
                if (selRow.EntryPkIds.TryGetValue(activeEntity.Id, out lstEntryPkIds))
                {
                    foreach (var entryPkValue in lstEntryPkIds)
                    {
                        selRow2 = new SelectedRow()
                        {
                            PkValue = selRow.PkValue,
                            BillNo = selRow.BillNo,
                            EntityKey = activeEntity.Id,
                            EntryPkValue = entryPkValue
                        };
                        lstSelRows.Add(selRow2);
                    }

                }
                else
                {
                    selRow2 = new SelectedRow()
                    {
                        PkValue = selRow.PkValue,
                        BillNo = selRow.BillNo,
                    };
                    lstSelRows.Add(selRow2);
                }
            }

            return lstSelRows;
        }

        private bool IsInQueryPath(HtmlEntity queryEntity, HtmlField queryField)
        {
            if (queryField == null) return false;
            if (queryEntity == null) return false;
            if (queryField.Entity is HtmlHeadEntity) return true;

            if (queryEntity.Equals(queryField.Entity)) return true;

            if (queryEntity is HtmlSubEntryEntity)
            {
                var parentEntity = (queryEntity as HtmlSubEntryEntity).ParentEntity;
                if (parentEntity.Equals(queryField.Entity)) return true;
            }
            return false;
        }

        /// <summary>
        /// 获取源单单据类型字段标识
        /// </summary>
        /// <returns></returns>
        private string GetSourceBillTypeFieldKey()
        {
            var billTypeMapObjs = this.ConvertRule?.BillTypeMappings?.Where(o =>
                !o.SourceBillTypeId.IsNullOrEmptyOrWhiteSpace()
                    && !o.TargetBillTypeId.IsNullOrEmptyOrWhiteSpace());
            if (billTypeMapObjs == null || billTypeMapObjs.Count() <= 0) return null;

            var sourceBillTypeField = this.SourceHtmlForm.GetFieldList().FirstOrDefault(o => o is HtmlBillTypeField);
            return sourceBillTypeField?.Id ?? "";
        }

        private void ParseConvertRule(out List<HtmlEntity> lstActiveQueryPath)
        {
            lstActiveQueryPath = new List<HtmlEntity>();
            this.SourceFieldList = new Dictionary<string, HtmlField>(StringComparer.OrdinalIgnoreCase);

            //TODO:根据单据转换字段映射关系，识别需要查询的数据字段，初始化进sqlPara参数里
            this.TryAddSelectField(this.SourceHtmlForm,
                this.ConvertRule.FieldMappings
                    .Where(o => o.MapType == (int)Enu_FieldMapType.Default)
                    .Select(o => o.SrcFieldId)
                    .Distinct());
            this.TryParseSourceFieldKeys(this.SourceHtmlForm,
                this.ConvertRule.FieldMappings
                    .Where(o => o.MapType == (int)Enu_FieldMapType.Expression)
                    .Select(o => o.SrcFieldId).Distinct());
            this.TryAddSelectField(this.SourceHtmlForm, this.ConvertRule.BillGroups.Select(o => o.Id).Distinct());
            this.TryAddSelectField(this.SourceHtmlForm, this.ConvertRule.FieldGroups.Select(o => o.Id).Distinct());

            //源单单据类型字段标识
            var sourceBillTypeFieldKey = this.GetSourceBillTypeFieldKey();
            if (!sourceBillTypeFieldKey.IsNullOrEmptyOrWhiteSpace())
            {
                this.TryAddSelectField(this.SourceHtmlForm, new List<string>() { sourceBillTypeFieldKey });
            }

            //分析查询路径数
            List<string> lstQueryPath = new List<string>();
            var allEntitys = this.SourceFieldList.Values.Select(o => o.Entity).Distinct();
            foreach (var entity in allEntitys)
            {
                string queryPath = "";
                if (entity is HtmlHeadEntity) continue;
                HtmlEntity activeEntity = null;
                if (entity is HtmlSubEntryEntity)
                {
                    activeEntity = (entity as HtmlSubEntryEntity).ParentEntity;

                    queryPath = $"fbillhead.{activeEntity.Id}.{entity.Id}";
                }
                else if (entity is HtmlEntryEntity)
                {
                    activeEntity = entity;
                    queryPath = $"fbillhead.{activeEntity.Id}";
                }

                if (!lstQueryPath.Contains(queryPath, StringComparer.OrdinalIgnoreCase))
                {
                    lstQueryPath.Add(queryPath);
                    lstActiveQueryPath.Add(entity);
                }
            }

            if (lstQueryPath.Any() == false)
            {
                //如果来源单没有单据体，则只有一条查询路径
                lstQueryPath.Add("fbillhead");
                lstActiveQueryPath.Add(this.SourceHtmlForm.HeadEntity);
            }
        }

        private void TryAddSelectField(HtmlForm sourceForm, IEnumerable<string> lstAddKeys)
        {
            if (lstAddKeys == null) return;

            foreach (var key in lstAddKeys)
            {
                string fldKey = key;
                if (key.Contains("."))
                {
                    fldKey = key.Substring(0, key.IndexOf("."));
                }
                var fld = sourceForm.GetField(fldKey);
                if (fld != null)
                    this.SourceFieldList[key] = fld;
            }
        }

        private void TryParseSourceFieldKeys(HtmlForm sourceForm, IEnumerable<string> lstExprItems)
        {
            if (lstExprItems == null) return;
            var bizExpr = this.UserContext.Container.GetService<IBizExpression>();

            foreach (var expr in lstExprItems)
            {
                bizExpr.ExpressionText = expr;
                var keys = this.ExpressionEngine.GetNameExpression(bizExpr);
                if (keys == null) continue;

                TryAddSelectField(sourceForm, keys);
            }
        }
        /// <summary>
        /// 根据前端传递过来的选择行，获取数据（没有传递明细行id的，自动拆分）
        /// </summary>
        /// <param name="sourceQueryObject"></param>
        /// <param name="billDatas"></param>
        /// <param name="dm"></param>
        /// <param name="queryPath"></param>
        private void QueryDataWithSelectedRows(ConvertSourceQueryObject sourceQueryObject, ref List<DynamicObject> billDatas, IDataManager dm, HtmlEntity queryPath)
        {
            if (queryPath is HtmlHeadEntity)
            {
                return;
            }
            List<string> pkIds = new List<string>();
            foreach (var sRow in sourceQueryObject.SelectedRows)
            {
                if (sRow.PkValue.IsNullOrEmptyOrWhiteSpace() || pkIds.Contains(sRow.PkValue, StringComparer.OrdinalIgnoreCase))
                {
                    continue;
                }
                pkIds.Add(sRow.PkValue);
            }

            billDatas = dm.Select(pkIds).OfType<DynamicObject>().ToList();
            if (billDatas.Count == 0)
            {
                throw new Exception("当前单据主键查询失败！");
            }
            List<SelectedRow> entitySelectRows = new List<SelectedRow>();
            foreach (var sRow in sourceQueryObject.SelectedRows)
            {
                DynamicObject billData = billDatas.FirstOrDefault(f => (f["Id"]?.ToString()).EqualsIgnoreCase(sRow.PkValue));
                if (billData.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }
                DynamicObjectCollection entitys = billData[queryPath.Id] as DynamicObjectCollection;
                if (entitys.Count > 0)
                {
                    if (!sRow.EntryPkValue.IsNullOrEmptyOrWhiteSpace())
                    {
                        DynamicObject entity = entitys.FirstOrDefault(f => (f["Id"]?.ToString()).EqualsIgnoreCase(sRow.EntryPkValue));
                        if (!entity.IsNullOrEmptyOrWhiteSpace())
                        {
                            entitySelectRows.Add(sRow);
                        }
                    }
                    else
                    {
                        foreach (DynamicObject entity in entitys)
                        {
                            SelectedRow entitySRow = new SelectedRow
                            {
                                PkValue = sRow.PkValue,
                                EntryPkValue = entity["Id"]?.ToString(),
                                BillNo = sRow.BillNo,
                                EntityKey = queryPath.Id
                            };
                            entitySelectRows.Add(entitySRow);
                        }
                    }
                }
                else
                {
                    entitySelectRows.Add(sRow);
                }
            }
            sourceQueryObject.SelectedRows = entitySelectRows;
        }
        /// <summary>
        /// 创建datatable并绑定数据
        /// </summary>
        /// <param name="dt"></param>
        /// <param name="dcWillLinkQty"></param>
        /// <param name="relationFld"></param>
        /// <param name="srcFldKey"></param>
        /// <param name="sourceQueryObject"></param>
        /// <param name="billDatas"></param>
        /// <param name="queryPath"></param>
        private void CreateDataTable(ref DataTable dt, DataColumn dcWillLinkQty, ref HtmlField relationFld, ref string srcFldKey,
            ConvertSourceQueryObject sourceQueryObject, List<DynamicObject> billDatas, HtmlEntity queryPath)
        {
            dt.Columns.Add("FID");
            dt.Columns.Add("FENTRYID");
            dt.Columns.Add("FENTITYKEY");
            dt.Columns.Add(dcWillLinkQty);
            var fldMap = this.ConvertRule.FieldMappings.FirstOrDefault(f => f.Id.EqualsIgnoreCase(this.ConvertRule.RelationFieldKey));
            if (fldMap.IsNullOrEmptyOrWhiteSpace() || fldMap.MapType == (int)Enu_FieldMapType.Expression)
            {
                throw new Exception("单据转换FieldMappings配置的RelationFieldKey有误！");
            }
            relationFld = this.TargetHtmlForm.GetField(this.ConvertRule.RelationFieldKey);
            srcFldKey = fldMap.SrcFieldId;
            var srcFldAry = srcFldKey.Split(new char[] { '.' }, StringSplitOptions.RemoveEmptyEntries);
            if (srcFldAry.Length > 1)
            {
                if (srcFldAry[0].EqualsIgnoreCase(this.SourceHtmlForm.HeadEntity.Id))
                {
                    if (srcFldAry[1].EqualsIgnoreCase("id"))
                    {
                        //表头考虑类似“fbillhead.id”的形式
                        srcFldKey = "headId";
                        dt.Columns.Add("headId");
                    }
                    else
                    {
                        //暂时不考虑“fbillhead.xxx”的形式
                    }
                }
                else
                {
                    foreach (var entry in this.SourceHtmlForm.EntryList)
                    {
                        if (!srcFldAry[0].EqualsIgnoreCase(entry.Id))
                        {
                            continue;
                        }
                        if (srcFldAry[1].EqualsIgnoreCase("id"))
                        {
                            //表体考虑类似“fentity.id”的形式
                            srcFldKey = "entryId";
                            dt.Columns.Add("entryId");
                        }
                        else if (srcFldAry[1].EqualsIgnoreCase("fseq"))
                        {
                            //表体考虑类似“fentity.fseq”的形式
                            srcFldKey = "seq";
                            dt.Columns.Add(new DataColumn()
                            {
                                ColumnName = "seq",
                                DefaultValue = 0,
                                DataType = typeof(int)
                            });
                        }
                        else
                        {
                            //暂时不考虑“fentity.xxx”字段的形式
                        }
                        break;
                    }
                }
            }
            else
            {
                var dtFld = this.TargetHtmlForm.GetField(srcFldKey);
                DataColumn newCol = new DataColumn();
                newCol.ColumnName = dtFld.Id;
                newCol.DataType = dtFld.DynamicProperty.PropertyType;
                //防止绑定数据过程出错
                if (dtFld is HtmlDecimalField)
                {
                    newCol.DefaultValue = 0;
                }
                else if (dtFld is HtmlDateTimeField)
                {
                    newCol.DefaultValue = DateTime.MinValue;
                }

                dt.Columns.Add(dtFld.Id, dtFld.DynamicProperty.PropertyType);
            }
            dt.BeginLoadData();
            foreach (var sRow in sourceQueryObject.SelectedRows)
            {
                List<object> dtDatas = new List<object>();
                DynamicObject billData = billDatas.FirstOrDefault(f => (f["Id"]?.ToString()).EqualsIgnoreCase(sRow.PkValue));
                if (billData.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }
                DynamicObjectCollection entitys = billData[queryPath.Id] as DynamicObjectCollection;
                if (entitys.Count > 0)
                {
                    if (!sRow.EntryPkValue.IsNullOrEmptyOrWhiteSpace())
                    {
                        DynamicObject entity = entitys.FirstOrDefault(f => (f["Id"]?.ToString()).EqualsIgnoreCase(sRow.EntryPkValue));
                        if (!entity.IsNullOrEmptyOrWhiteSpace())
                        {
                            LoadDataObjs(ref dtDatas, dt, billData, entity, queryPath);
                        }
                    }
                    else
                    {
                        foreach (DynamicObject entity in entitys)
                        {
                            LoadDataObjs(ref dtDatas, dt, billData, entity, queryPath);
                        }
                    }
                }
                else
                {
                    LoadDataObjs(ref dtDatas, dt, billData, null, queryPath);
                }
                dt.LoadDataRow(dtDatas.ToArray(), true);
            }
            dt.EndLoadData();
        }
        /// <summary>
        /// 获取datatable需要绑定的数据
        /// </summary>
        /// <param name="dtDatas"></param>
        /// <param name="dt"></param>
        /// <param name="billData"></param>
        /// <param name="entity"></param>
        /// <param name="queryPath"></param>
        private void LoadDataObjs(ref List<object> dtDatas, DataTable dt, DynamicObject billData, DynamicObject entity, HtmlEntity queryPath)
        {
            foreach (DataColumn dc in dt.Columns)
            {
                if (entity != null && entity.DynamicObjectType.Properties.ContainsKey(dc.ColumnName))
                {
                    dtDatas.Add(entity[dc.ColumnName]);
                }
                else if (billData.DynamicObjectType.Properties.ContainsKey(dc.ColumnName))
                {
                    dtDatas.Add(billData[dc.ColumnName]);
                }
                else if (dc.ColumnName.EqualsIgnoreCase("fid") || dc.ColumnName.EqualsIgnoreCase("headId"))
                {
                    dtDatas.Add(billData["Id"]);
                }
                else if (dc.ColumnName.EqualsIgnoreCase("fentryid") || dc.ColumnName.EqualsIgnoreCase("entryId"))
                {
                    if (entity != null)
                    {
                        dtDatas.Add(entity["Id"]);
                    }
                    else
                    {
                        dtDatas.Add("");
                    }
                }
                else if (dc.ColumnName.EqualsIgnoreCase("fentitykey"))
                {
                    dtDatas.Add(queryPath.Id);
                }
                else if (dc.ColumnName.EqualsIgnoreCase("seq"))
                {
                    if (entity != null)
                    {
                        dtDatas.Add(entity["fseq"]);
                    }
                    else
                    {
                        dtDatas.Add(0);
                    }
                }
                else if (dc.ColumnName.EqualsIgnoreCase("__linkQty"))
                {
                    dtDatas.Add(0);
                }
            }
        }

        /// <summary>
        /// 根据下游单据数据，更新临时表可用量数据
        /// </summary>
        /// <param name="controlFld"></param>
        /// <param name="relationFld"></param>
        /// <param name="sourceQueryObject"></param>
        /// <param name="dcWillLinkQty"></param>
        /// <param name="srcFldKey"></param>
        private void UpdateTempQty(HtmlField controlFld, HtmlField relationFld, ConvertSourceQueryObject sourceQueryObject,
            DataColumn dcWillLinkQty, string srcFldKey)
        {
            string sql = "select sum({1}.{2}) as ftotalqty,{3}.{4} from {0} ".Fmt(this.TargetHtmlForm.BillHeadTableName, controlFld.Entity.TableName,
                        controlFld.FieldName, relationFld.Entity.TableName, relationFld.FieldName);
            if (controlFld.Entity is HtmlSubEntryEntity)
            {
                var subEntry = controlFld.Entity as HtmlSubEntryEntity;
                sql += " inner join {0} on {0}.{1} = {2}.{1} ".Fmt(subEntry.ParentEntity.TableName, this.TargetHtmlForm.BillPKFldName,
                    this.TargetHtmlForm.BillHeadTableName);
                sql += " inner join {0} on {0}.{1} = {2}.{1} ".Fmt(subEntry.TableName, subEntry.ParentEntity.PkFieldName,
                    subEntry.ParentEntity.TableName);
            }
            else if (controlFld.Entity is HtmlEntryEntity)
            {
                var entry = controlFld.Entity as HtmlEntryEntity;
                sql += " inner join {0} on {0}.{1} = {2}.{1} ".Fmt(entry.TableName, this.TargetHtmlForm.BillPKFldName,
                    this.TargetHtmlForm.BillHeadTableName);
            }
            sql += " where fcancelstatus!='1' "; //只统一未作废的下游单据
            if (this.TargetHtmlForm.Isolate == "1")
            {
                sql += " and fmainorgid='{0}'".Fmt(this.UserContext.Company);
            }
            if (!this.ConvertRule.RealtionFormIdFieldKey.IsNullOrEmptyOrWhiteSpace())
            {
                var relationFormIdFld = this.TargetHtmlForm.GetField(this.ConvertRule.RealtionFormIdFieldKey);
                if (!relationFormIdFld.IsNullOrEmptyOrWhiteSpace())
                {
                    sql += " and {0}.{1}='{2}'".Fmt(relationFormIdFld.Entity.TableName, relationFormIdFld.FieldName, this.SourceHtmlForm.Id);
                }
            }
            sql += " group by {0}.{1} ".Fmt(relationFld.Entity.TableName, relationFld.FieldName);
            string updateSql = $@"/*dialect*/update {sourceQueryObject.TempTableName} set {dcWillLinkQty.ColumnName}= B.ftotalqty
                                       from {sourceQueryObject.TempTableName}
                                       inner join (
                                                 select isnull(ftotalqty,0) as ftotalqty,{relationFld.FieldName}
                                                 from ({sql}) as A
                                                  ) as B on B.{relationFld.FieldName} = {sourceQueryObject.TempTableName}.{srcFldKey}";
            string selectSql = $@" select * from {sourceQueryObject.TempTableName}
                                           inner join (
                                                 select isnull(ftotalqty,0) as ftotalqty,{relationFld.FieldName}
                                                 from ({sql}) as A
                                                  ) as B on B.{relationFld.FieldName} = {sourceQueryObject.TempTableName}.{srcFldKey}";

            var dbSer = this.UserContext.Container.GetService<IDBService>();
            var selectDatas = dbSer.ExecuteDynamicObject(this.UserContext, selectSql);
            if (selectDatas.Count > 0)
            {
                var dbSerEx = this.UserContext.Container.GetService<IDBServiceEx>();
                var updateStatus = dbSerEx.Execute(this.UserContext, updateSql);
            }
        }

        private void UpdateSqlSelectAndWhere(HtmlField sourceControlField, HtmlField relationFld, EntityTable headInfo, ConvertSourceQueryObject sourceQueryObject,
            DataColumn dcWillLinkQty, MetaCore.QueryObject queryObject, EntityTable entityInfo)
        {
            if (relationFld.IsNullOrEmptyOrWhiteSpace() || sourceControlField.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            if (sourceControlField.EntityKey.EqualsIgnoreCase(headInfo.IndexKey))
            {
                string oldStr = $"{headInfo.TableNameAs}.{sourceControlField.FieldName}";
                string newStr = $"{sourceQueryObject.TempTableNameAs}.{dcWillLinkQty.ColumnName}";
                string selectStr = queryObject.SqlSelect;
                string whereStr = queryObject.SqlWhere;
                if (selectStr.Contains(oldStr))
                {
                    queryObject.SqlSelect = selectStr.Replace(oldStr, newStr);
                }
                if (whereStr.Contains(oldStr))
                {
                    queryObject.SqlWhere = whereStr.Replace(oldStr, newStr);
                }
            }
            else if (sourceControlField.EntityKey.EqualsIgnoreCase(entityInfo.IndexKey))
            {
                string oldStr = $"{entityInfo.TableNameAs}.{sourceControlField.FieldName}";
                string newStr = $"{sourceQueryObject.TempTableNameAs}.{dcWillLinkQty.ColumnName}";
                string selectStr = queryObject.SqlSelect;
                string whereStr = queryObject.SqlWhere;
                if (selectStr.Contains(oldStr))
                {
                    queryObject.SqlSelect = selectStr.Replace(oldStr, newStr);
                }
                if (whereStr.Contains(oldStr))
                {
                    queryObject.SqlWhere = whereStr.Replace(oldStr, newStr);
                }
            }
        }
    }
}
